{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/companies", "regex": "^/companies(?:/)?$", "routeKeys": {}, "namedRegex": "^/companies(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/global-view", "regex": "^/global\\-view(?:/)?$", "routeKeys": {}, "namedRegex": "^/global\\-view(?:/)?$"}, {"page": "/job-seekers", "regex": "^/job\\-seekers(?:/)?$", "routeKeys": {}, "namedRegex": "^/job\\-seekers(?:/)?$"}, {"page": "/matches", "regex": "^/matches(?:/)?$", "routeKeys": {}, "namedRegex": "^/matches(?:/)?$"}, {"page": "/positions", "regex": "^/positions(?:/)?$", "routeKeys": {}, "namedRegex": "^/positions(?:/)?$"}, {"page": "/skills", "regex": "^/skills(?:/)?$", "routeKeys": {}, "namedRegex": "^/skills(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}