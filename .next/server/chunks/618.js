"use strict";exports.id=618,exports.ids=[618],exports.modules={5:(e,n,r)=>{r.d(n,{A:()=>a});var o=r(8732),t=r(9918),c=r.n(t),i=r(4233),f=r(2015);function u(){let e=(0,i.useRouter)(),[n,r]=(0,f.useState)(!1),t=[{name:"Dashboard",path:"/",icon:"\uD83C\uDFE0"},{name:"Job Matches",path:"/matches",icon:"\uD83C\uDFAF"},{name:"Job Seekers",path:"/job-seekers",icon:"\uD83D\uDC65"},{name:"Companies",path:"/companies",icon:"\uD83C\uDFE2"},{name:"Positions",path:"/positions",icon:"\uD83D\uDCCB"},{name:"Skills",path:"/skills",icon:"\uD83D\uDEE0️"},{name:"Global View",path:"/global-view",icon:"\uD83C\uDF10"}];return(0,o.jsx)("nav",{className:"bg-white shadow-soft border-b border-candid-gray-200",children:(0,o.jsxs)("div",{className:"container-app",children:[(0,o.jsxs)("div",{className:"flex justify-between items-center h-20",children:[(0,o.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,o.jsxs)(c(),{href:"/",className:"flex items-center space-x-3 group",children:[(0,o.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-primary-500 to-accent-500 rounded-xl flex items-center justify-center shadow-soft group-hover:shadow-medium transition-all duration-200",children:(0,o.jsx)("span",{className:"text-white font-bold text-lg",children:"C"})}),(0,o.jsxs)("div",{className:"hidden sm:block",children:[(0,o.jsx)("h1",{className:"text-xl font-bold text-candid-navy-900 group-hover:text-primary-600 transition-colors duration-200",children:"Candid Connections"}),(0,o.jsx)("p",{className:"text-sm text-candid-gray-600 -mt-1",children:"Katra Platform"})]})]})}),(0,o.jsx)("div",{className:"hidden lg:block",children:(0,o.jsx)("div",{className:"flex items-center space-x-1",children:t.map(n=>(0,o.jsxs)(c(),{href:n.path,className:`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${e.pathname===n.path?"nav-link-active":"nav-link"}`,children:[(0,o.jsx)("span",{className:"text-base",children:n.icon}),(0,o.jsx)("span",{children:n.name})]},n.path))})}),(0,o.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[(0,o.jsx)(c(),{href:"/global-view",className:"btn-outline text-sm py-2 px-4",children:"\uD83C\uDF10 Network View"}),(0,o.jsx)(c(),{href:"https://portal.candid-connections.com/user/login",target:"_blank",rel:"noopener noreferrer",className:"btn-primary text-sm py-2 px-4",children:"Portal Login"})]}),(0,o.jsx)("div",{className:"lg:hidden",children:(0,o.jsx)("button",{onClick:()=>r(!n),className:"p-2 rounded-lg text-candid-navy-600 hover:text-primary-600 hover:bg-primary-50 transition-colors duration-200","aria-label":"Toggle mobile menu",children:(0,o.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:n?(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})})]}),n&&(0,o.jsx)("div",{className:"lg:hidden border-t border-candid-gray-200 py-4",children:(0,o.jsxs)("div",{className:"space-y-2",children:[t.map(n=>(0,o.jsxs)(c(),{href:n.path,onClick:()=>r(!1),className:`flex items-center space-x-3 px-4 py-3 rounded-lg text-base font-medium transition-all duration-200 ${e.pathname===n.path?"nav-link-active":"nav-link"}`,children:[(0,o.jsx)("span",{className:"text-lg",children:n.icon}),(0,o.jsx)("span",{children:n.name})]},n.path)),(0,o.jsxs)("div",{className:"pt-4 space-y-2",children:[(0,o.jsx)(c(),{href:"/global-view",onClick:()=>r(!1),className:"block w-full btn-outline text-center",children:"\uD83C\uDF10 Network View"}),(0,o.jsx)(c(),{href:"https://portal.candid-connections.com/user/login",target:"_blank",rel:"noopener noreferrer",className:"block w-full btn-primary text-center",children:"Portal Login"})]})]})})]})})}function a({children:e}){return(0,o.jsxs)("div",{className:"min-h-screen bg-candid-gray-50",children:[(0,o.jsx)(u,{}),(0,o.jsx)("main",{className:"container-app section-padding",children:e})]})}},7605:(e,n,r)=>{r.a(e,async(e,o)=>{try{var t=r(1915),c=r(7127),i=r(8238),f=r(6148),u=r(7929),a=r(1016),d=r(2423),l=r(300),s=r(204),y=r(8095),m=r(276),g=r(246),k=r(7141),h=r(4933),M=r(2355),C=r(437),L=r(7089),S=r(8945),B=r(7440),p=r(2533),x=r(1225),j=r(8180),b=r(2727),v=r(832),N=r(9275),w=r(39),$=r(2441),E=r(3351),A=r(6285),D=r(6501),P=e([t,c,i,f,u,a,d,l,s,y,m,g,k,h,M,C,L,S,B,p,x,j,b,v,N,w,$,E,A,D]);[t,c,i,f,u,a,d,l,s,y,m,g,k,h,M,C,L,S,B,p,x,j,b,v,N,w,$,E,A,D]=P.then?(await P)():P,r.o(t,"drag")&&r.d(n,{drag:function(){return t.drag}}),r.o(t,"forceCenter")&&r.d(n,{forceCenter:function(){return t.forceCenter}}),r.o(t,"forceLink")&&r.d(n,{forceLink:function(){return t.forceLink}}),r.o(t,"forceManyBody")&&r.d(n,{forceManyBody:function(){return t.forceManyBody}}),r.o(t,"forceSimulation")&&r.d(n,{forceSimulation:function(){return t.forceSimulation}}),r.o(t,"select")&&r.d(n,{select:function(){return t.select}}),r.o(c,"drag")&&r.d(n,{drag:function(){return c.drag}}),r.o(c,"forceCenter")&&r.d(n,{forceCenter:function(){return c.forceCenter}}),r.o(c,"forceLink")&&r.d(n,{forceLink:function(){return c.forceLink}}),r.o(c,"forceManyBody")&&r.d(n,{forceManyBody:function(){return c.forceManyBody}}),r.o(c,"forceSimulation")&&r.d(n,{forceSimulation:function(){return c.forceSimulation}}),r.o(c,"select")&&r.d(n,{select:function(){return c.select}}),r.o(i,"drag")&&r.d(n,{drag:function(){return i.drag}}),r.o(i,"forceCenter")&&r.d(n,{forceCenter:function(){return i.forceCenter}}),r.o(i,"forceLink")&&r.d(n,{forceLink:function(){return i.forceLink}}),r.o(i,"forceManyBody")&&r.d(n,{forceManyBody:function(){return i.forceManyBody}}),r.o(i,"forceSimulation")&&r.d(n,{forceSimulation:function(){return i.forceSimulation}}),r.o(i,"select")&&r.d(n,{select:function(){return i.select}}),r.o(f,"drag")&&r.d(n,{drag:function(){return f.drag}}),r.o(f,"forceCenter")&&r.d(n,{forceCenter:function(){return f.forceCenter}}),r.o(f,"forceLink")&&r.d(n,{forceLink:function(){return f.forceLink}}),r.o(f,"forceManyBody")&&r.d(n,{forceManyBody:function(){return f.forceManyBody}}),r.o(f,"forceSimulation")&&r.d(n,{forceSimulation:function(){return f.forceSimulation}}),r.o(f,"select")&&r.d(n,{select:function(){return f.select}}),r.o(u,"drag")&&r.d(n,{drag:function(){return u.drag}}),r.o(u,"forceCenter")&&r.d(n,{forceCenter:function(){return u.forceCenter}}),r.o(u,"forceLink")&&r.d(n,{forceLink:function(){return u.forceLink}}),r.o(u,"forceManyBody")&&r.d(n,{forceManyBody:function(){return u.forceManyBody}}),r.o(u,"forceSimulation")&&r.d(n,{forceSimulation:function(){return u.forceSimulation}}),r.o(u,"select")&&r.d(n,{select:function(){return u.select}}),r.o(a,"drag")&&r.d(n,{drag:function(){return a.drag}}),r.o(a,"forceCenter")&&r.d(n,{forceCenter:function(){return a.forceCenter}}),r.o(a,"forceLink")&&r.d(n,{forceLink:function(){return a.forceLink}}),r.o(a,"forceManyBody")&&r.d(n,{forceManyBody:function(){return a.forceManyBody}}),r.o(a,"forceSimulation")&&r.d(n,{forceSimulation:function(){return a.forceSimulation}}),r.o(a,"select")&&r.d(n,{select:function(){return a.select}}),r.o(d,"drag")&&r.d(n,{drag:function(){return d.drag}}),r.o(d,"forceCenter")&&r.d(n,{forceCenter:function(){return d.forceCenter}}),r.o(d,"forceLink")&&r.d(n,{forceLink:function(){return d.forceLink}}),r.o(d,"forceManyBody")&&r.d(n,{forceManyBody:function(){return d.forceManyBody}}),r.o(d,"forceSimulation")&&r.d(n,{forceSimulation:function(){return d.forceSimulation}}),r.o(d,"select")&&r.d(n,{select:function(){return d.select}}),r.o(l,"drag")&&r.d(n,{drag:function(){return l.drag}}),r.o(l,"forceCenter")&&r.d(n,{forceCenter:function(){return l.forceCenter}}),r.o(l,"forceLink")&&r.d(n,{forceLink:function(){return l.forceLink}}),r.o(l,"forceManyBody")&&r.d(n,{forceManyBody:function(){return l.forceManyBody}}),r.o(l,"forceSimulation")&&r.d(n,{forceSimulation:function(){return l.forceSimulation}}),r.o(l,"select")&&r.d(n,{select:function(){return l.select}}),r.o(s,"drag")&&r.d(n,{drag:function(){return s.drag}}),r.o(s,"forceCenter")&&r.d(n,{forceCenter:function(){return s.forceCenter}}),r.o(s,"forceLink")&&r.d(n,{forceLink:function(){return s.forceLink}}),r.o(s,"forceManyBody")&&r.d(n,{forceManyBody:function(){return s.forceManyBody}}),r.o(s,"forceSimulation")&&r.d(n,{forceSimulation:function(){return s.forceSimulation}}),r.o(s,"select")&&r.d(n,{select:function(){return s.select}}),r.o(y,"drag")&&r.d(n,{drag:function(){return y.drag}}),r.o(y,"forceCenter")&&r.d(n,{forceCenter:function(){return y.forceCenter}}),r.o(y,"forceLink")&&r.d(n,{forceLink:function(){return y.forceLink}}),r.o(y,"forceManyBody")&&r.d(n,{forceManyBody:function(){return y.forceManyBody}}),r.o(y,"forceSimulation")&&r.d(n,{forceSimulation:function(){return y.forceSimulation}}),r.o(y,"select")&&r.d(n,{select:function(){return y.select}}),r.o(m,"drag")&&r.d(n,{drag:function(){return m.drag}}),r.o(m,"forceCenter")&&r.d(n,{forceCenter:function(){return m.forceCenter}}),r.o(m,"forceLink")&&r.d(n,{forceLink:function(){return m.forceLink}}),r.o(m,"forceManyBody")&&r.d(n,{forceManyBody:function(){return m.forceManyBody}}),r.o(m,"forceSimulation")&&r.d(n,{forceSimulation:function(){return m.forceSimulation}}),r.o(m,"select")&&r.d(n,{select:function(){return m.select}}),r.o(g,"drag")&&r.d(n,{drag:function(){return g.drag}}),r.o(g,"forceCenter")&&r.d(n,{forceCenter:function(){return g.forceCenter}}),r.o(g,"forceLink")&&r.d(n,{forceLink:function(){return g.forceLink}}),r.o(g,"forceManyBody")&&r.d(n,{forceManyBody:function(){return g.forceManyBody}}),r.o(g,"forceSimulation")&&r.d(n,{forceSimulation:function(){return g.forceSimulation}}),r.o(g,"select")&&r.d(n,{select:function(){return g.select}}),r.o(k,"drag")&&r.d(n,{drag:function(){return k.drag}}),r.o(k,"forceCenter")&&r.d(n,{forceCenter:function(){return k.forceCenter}}),r.o(k,"forceLink")&&r.d(n,{forceLink:function(){return k.forceLink}}),r.o(k,"forceManyBody")&&r.d(n,{forceManyBody:function(){return k.forceManyBody}}),r.o(k,"forceSimulation")&&r.d(n,{forceSimulation:function(){return k.forceSimulation}}),r.o(k,"select")&&r.d(n,{select:function(){return k.select}}),r.o(h,"drag")&&r.d(n,{drag:function(){return h.drag}}),r.o(h,"forceCenter")&&r.d(n,{forceCenter:function(){return h.forceCenter}}),r.o(h,"forceLink")&&r.d(n,{forceLink:function(){return h.forceLink}}),r.o(h,"forceManyBody")&&r.d(n,{forceManyBody:function(){return h.forceManyBody}}),r.o(h,"forceSimulation")&&r.d(n,{forceSimulation:function(){return h.forceSimulation}}),r.o(h,"select")&&r.d(n,{select:function(){return h.select}}),r.o(M,"drag")&&r.d(n,{drag:function(){return M.drag}}),r.o(M,"forceCenter")&&r.d(n,{forceCenter:function(){return M.forceCenter}}),r.o(M,"forceLink")&&r.d(n,{forceLink:function(){return M.forceLink}}),r.o(M,"forceManyBody")&&r.d(n,{forceManyBody:function(){return M.forceManyBody}}),r.o(M,"forceSimulation")&&r.d(n,{forceSimulation:function(){return M.forceSimulation}}),r.o(M,"select")&&r.d(n,{select:function(){return M.select}}),r.o(C,"drag")&&r.d(n,{drag:function(){return C.drag}}),r.o(C,"forceCenter")&&r.d(n,{forceCenter:function(){return C.forceCenter}}),r.o(C,"forceLink")&&r.d(n,{forceLink:function(){return C.forceLink}}),r.o(C,"forceManyBody")&&r.d(n,{forceManyBody:function(){return C.forceManyBody}}),r.o(C,"forceSimulation")&&r.d(n,{forceSimulation:function(){return C.forceSimulation}}),r.o(C,"select")&&r.d(n,{select:function(){return C.select}}),r.o(L,"drag")&&r.d(n,{drag:function(){return L.drag}}),r.o(L,"forceCenter")&&r.d(n,{forceCenter:function(){return L.forceCenter}}),r.o(L,"forceLink")&&r.d(n,{forceLink:function(){return L.forceLink}}),r.o(L,"forceManyBody")&&r.d(n,{forceManyBody:function(){return L.forceManyBody}}),r.o(L,"forceSimulation")&&r.d(n,{forceSimulation:function(){return L.forceSimulation}}),r.o(L,"select")&&r.d(n,{select:function(){return L.select}}),r.o(S,"drag")&&r.d(n,{drag:function(){return S.drag}}),r.o(S,"forceCenter")&&r.d(n,{forceCenter:function(){return S.forceCenter}}),r.o(S,"forceLink")&&r.d(n,{forceLink:function(){return S.forceLink}}),r.o(S,"forceManyBody")&&r.d(n,{forceManyBody:function(){return S.forceManyBody}}),r.o(S,"forceSimulation")&&r.d(n,{forceSimulation:function(){return S.forceSimulation}}),r.o(S,"select")&&r.d(n,{select:function(){return S.select}}),r.o(B,"drag")&&r.d(n,{drag:function(){return B.drag}}),r.o(B,"forceCenter")&&r.d(n,{forceCenter:function(){return B.forceCenter}}),r.o(B,"forceLink")&&r.d(n,{forceLink:function(){return B.forceLink}}),r.o(B,"forceManyBody")&&r.d(n,{forceManyBody:function(){return B.forceManyBody}}),r.o(B,"forceSimulation")&&r.d(n,{forceSimulation:function(){return B.forceSimulation}}),r.o(B,"select")&&r.d(n,{select:function(){return B.select}}),r.o(p,"drag")&&r.d(n,{drag:function(){return p.drag}}),r.o(p,"forceCenter")&&r.d(n,{forceCenter:function(){return p.forceCenter}}),r.o(p,"forceLink")&&r.d(n,{forceLink:function(){return p.forceLink}}),r.o(p,"forceManyBody")&&r.d(n,{forceManyBody:function(){return p.forceManyBody}}),r.o(p,"forceSimulation")&&r.d(n,{forceSimulation:function(){return p.forceSimulation}}),r.o(p,"select")&&r.d(n,{select:function(){return p.select}}),r.o(x,"drag")&&r.d(n,{drag:function(){return x.drag}}),r.o(x,"forceCenter")&&r.d(n,{forceCenter:function(){return x.forceCenter}}),r.o(x,"forceLink")&&r.d(n,{forceLink:function(){return x.forceLink}}),r.o(x,"forceManyBody")&&r.d(n,{forceManyBody:function(){return x.forceManyBody}}),r.o(x,"forceSimulation")&&r.d(n,{forceSimulation:function(){return x.forceSimulation}}),r.o(x,"select")&&r.d(n,{select:function(){return x.select}}),r.o(j,"drag")&&r.d(n,{drag:function(){return j.drag}}),r.o(j,"forceCenter")&&r.d(n,{forceCenter:function(){return j.forceCenter}}),r.o(j,"forceLink")&&r.d(n,{forceLink:function(){return j.forceLink}}),r.o(j,"forceManyBody")&&r.d(n,{forceManyBody:function(){return j.forceManyBody}}),r.o(j,"forceSimulation")&&r.d(n,{forceSimulation:function(){return j.forceSimulation}}),r.o(j,"select")&&r.d(n,{select:function(){return j.select}}),r.o(b,"drag")&&r.d(n,{drag:function(){return b.drag}}),r.o(b,"forceCenter")&&r.d(n,{forceCenter:function(){return b.forceCenter}}),r.o(b,"forceLink")&&r.d(n,{forceLink:function(){return b.forceLink}}),r.o(b,"forceManyBody")&&r.d(n,{forceManyBody:function(){return b.forceManyBody}}),r.o(b,"forceSimulation")&&r.d(n,{forceSimulation:function(){return b.forceSimulation}}),r.o(b,"select")&&r.d(n,{select:function(){return b.select}}),r.o(v,"drag")&&r.d(n,{drag:function(){return v.drag}}),r.o(v,"forceCenter")&&r.d(n,{forceCenter:function(){return v.forceCenter}}),r.o(v,"forceLink")&&r.d(n,{forceLink:function(){return v.forceLink}}),r.o(v,"forceManyBody")&&r.d(n,{forceManyBody:function(){return v.forceManyBody}}),r.o(v,"forceSimulation")&&r.d(n,{forceSimulation:function(){return v.forceSimulation}}),r.o(v,"select")&&r.d(n,{select:function(){return v.select}}),r.o(N,"drag")&&r.d(n,{drag:function(){return N.drag}}),r.o(N,"forceCenter")&&r.d(n,{forceCenter:function(){return N.forceCenter}}),r.o(N,"forceLink")&&r.d(n,{forceLink:function(){return N.forceLink}}),r.o(N,"forceManyBody")&&r.d(n,{forceManyBody:function(){return N.forceManyBody}}),r.o(N,"forceSimulation")&&r.d(n,{forceSimulation:function(){return N.forceSimulation}}),r.o(N,"select")&&r.d(n,{select:function(){return N.select}}),r.o(w,"drag")&&r.d(n,{drag:function(){return w.drag}}),r.o(w,"forceCenter")&&r.d(n,{forceCenter:function(){return w.forceCenter}}),r.o(w,"forceLink")&&r.d(n,{forceLink:function(){return w.forceLink}}),r.o(w,"forceManyBody")&&r.d(n,{forceManyBody:function(){return w.forceManyBody}}),r.o(w,"forceSimulation")&&r.d(n,{forceSimulation:function(){return w.forceSimulation}}),r.o(w,"select")&&r.d(n,{select:function(){return w.select}}),r.o($,"drag")&&r.d(n,{drag:function(){return $.drag}}),r.o($,"forceCenter")&&r.d(n,{forceCenter:function(){return $.forceCenter}}),r.o($,"forceLink")&&r.d(n,{forceLink:function(){return $.forceLink}}),r.o($,"forceManyBody")&&r.d(n,{forceManyBody:function(){return $.forceManyBody}}),r.o($,"forceSimulation")&&r.d(n,{forceSimulation:function(){return $.forceSimulation}}),r.o($,"select")&&r.d(n,{select:function(){return $.select}}),r.o(E,"drag")&&r.d(n,{drag:function(){return E.drag}}),r.o(E,"forceCenter")&&r.d(n,{forceCenter:function(){return E.forceCenter}}),r.o(E,"forceLink")&&r.d(n,{forceLink:function(){return E.forceLink}}),r.o(E,"forceManyBody")&&r.d(n,{forceManyBody:function(){return E.forceManyBody}}),r.o(E,"forceSimulation")&&r.d(n,{forceSimulation:function(){return E.forceSimulation}}),r.o(E,"select")&&r.d(n,{select:function(){return E.select}}),r.o(A,"drag")&&r.d(n,{drag:function(){return A.drag}}),r.o(A,"forceCenter")&&r.d(n,{forceCenter:function(){return A.forceCenter}}),r.o(A,"forceLink")&&r.d(n,{forceLink:function(){return A.forceLink}}),r.o(A,"forceManyBody")&&r.d(n,{forceManyBody:function(){return A.forceManyBody}}),r.o(A,"forceSimulation")&&r.d(n,{forceSimulation:function(){return A.forceSimulation}}),r.o(A,"select")&&r.d(n,{select:function(){return A.select}}),r.o(D,"drag")&&r.d(n,{drag:function(){return D.drag}}),r.o(D,"forceCenter")&&r.d(n,{forceCenter:function(){return D.forceCenter}}),r.o(D,"forceLink")&&r.d(n,{forceLink:function(){return D.forceLink}}),r.o(D,"forceManyBody")&&r.d(n,{forceManyBody:function(){return D.forceManyBody}}),r.o(D,"forceSimulation")&&r.d(n,{forceSimulation:function(){return D.forceSimulation}}),r.o(D,"select")&&r.d(n,{select:function(){return D.select}}),o()}catch(e){o(e)}})},8587:(e,n,r)=>{r.d(n,{_:()=>o});let o=()=>{let e=Array.from({length:5},(e,n)=>({id:`jobSeekers/${n}`,name:`Job Seeker ${n+1}`,type:"jobSeeker",size:8})),n=Array.from({length:3},(e,n)=>({id:`companies/${n}`,name:`Company ${n+1}`,type:"company",size:10})),r=Array.from({length:4},(e,n)=>({id:`positions/${n}`,name:`Position ${n+1}`,type:"position",size:9})),o=Array.from({length:8},(e,n)=>({id:`skills/${n}`,name:`Skill ${n+1}`,type:"skill",size:6})),t=[...e,...n,...r,...o],c=[];return e.forEach((e,r)=>{c.push({source:e.id,target:n[r%n.length].id,type:"works_for",value:1})}),n.forEach((e,n)=>{r.forEach((r,o)=>{(n+o)%2==0&&c.push({source:e.id,target:r.id,type:"posts",value:1})})}),e.forEach(e=>{let n=2+Math.floor(3*Math.random());[...o].sort(()=>.5-Math.random()).slice(0,n).forEach(n=>{c.push({source:e.id,target:n.id,type:"has_skill",value:1})})}),r.forEach(e=>{let n=2+Math.floor(2*Math.random());[...o].sort(()=>.5-Math.random()).slice(0,n).forEach(n=>{c.push({source:e.id,target:n.id,type:"requires",value:1})})}),{nodes:t,links:c}}},9662:(e,n,r)=>{r.a(e,async(e,o)=>{try{r.d(n,{A:()=>u});var t=r(8732),c=r(2015),i=r(7605),f=e([i]);function u({data:e}){let n=(0,c.useRef)(null);return(0,t.jsx)("div",{className:"border rounded-lg shadow-sm bg-white p-4",children:(0,t.jsx)("svg",{ref:n,className:"w-full h-full"})})}i=(f.then?(await f)():f)[0],o()}catch(e){o(e)}})}};