exports.id=625,exports.ids=[625],exports.modules={5:(o,e,r)=>{"use strict";r.d(e,{A:()=>u});var n=r(8732),t=r(9918),c=r.n(t),i=r(4233),f=r(2015);function d(){let o=(0,i.useRouter)(),[e,r]=(0,f.useState)(!1),t=[{name:"Dashboard",path:"/",icon:"\uD83C\uDFE0"},{name:"Authority Matches",path:"/matches",icon:"\uD83C\uDFAF"},{name:"Job Seekers",path:"/job-seekers",icon:"\uD83D\uDC65"},{name:"Hiring Authorities",path:"/hiring-authorities",icon:"\uD83D\uDC54"},{name:"Companies",path:"/companies",icon:"\uD83C\uDFE2"},{name:"Positions",path:"/positions",icon:"\uD83D\uDCCB"},{name:"Skills",path:"/skills",icon:"\uD83D\uDEE0️"},{name:"Visualizations",path:"/visualizations",icon:"\uD83D\uDCCA"},{name:"Network View",path:"/global-view",icon:"\uD83C\uDF10"}];return(0,n.jsx)("nav",{className:"bg-white shadow-soft border-b border-candid-gray-200",children:(0,n.jsxs)("div",{className:"container-app",children:[(0,n.jsxs)("div",{className:"flex justify-between items-center h-20",children:[(0,n.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,n.jsxs)(c(),{href:"/",className:"flex items-center space-x-3 group",children:[(0,n.jsx)("div",{className:"w-12 h-12 relative group-hover:scale-105 transition-transform duration-200",children:(0,n.jsxs)("svg",{viewBox:"0 0 48 48",className:"w-full h-full",children:[(0,n.jsx)("circle",{cx:"24",cy:"24",r:"22",fill:"none",stroke:"#1e3a8a",strokeWidth:"2"}),(0,n.jsx)("circle",{cx:"24",cy:"12",r:"3",fill:"#00d4ff"}),(0,n.jsx)("circle",{cx:"36",cy:"24",r:"3",fill:"#00d4ff"}),(0,n.jsx)("circle",{cx:"24",cy:"36",r:"3",fill:"#00d4ff"}),(0,n.jsx)("circle",{cx:"12",cy:"24",r:"3",fill:"#00d4ff"}),(0,n.jsx)("circle",{cx:"24",cy:"24",r:"4",fill:"#1e3a8a"}),(0,n.jsx)("line",{x1:"24",y1:"15",x2:"24",y2:"20",stroke:"#00d4ff",strokeWidth:"2"}),(0,n.jsx)("line",{x1:"33",y1:"24",x2:"28",y2:"24",stroke:"#00d4ff",strokeWidth:"2"}),(0,n.jsx)("line",{x1:"24",y1:"33",x2:"24",y2:"28",stroke:"#00d4ff",strokeWidth:"2"}),(0,n.jsx)("line",{x1:"15",y1:"24",x2:"20",y2:"24",stroke:"#00d4ff",strokeWidth:"2"})]})}),(0,n.jsxs)("div",{className:"hidden sm:block",children:[(0,n.jsx)("h1",{className:"text-xl font-bold text-secondary-800 group-hover:text-primary-500 transition-colors duration-200",children:"Candid Connections"}),(0,n.jsx)("p",{className:"text-sm text-candid-gray-600 -mt-1",children:"Katra Platform"})]})]})}),(0,n.jsx)("div",{className:"hidden lg:block",children:(0,n.jsx)("div",{className:"flex items-center space-x-1",children:t.map(e=>(0,n.jsxs)(c(),{href:e.path,className:`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${o.pathname===e.path?"nav-link-active":"nav-link"}`,children:[(0,n.jsx)("span",{className:"text-base",children:e.icon}),(0,n.jsx)("span",{children:e.name})]},e.path))})}),(0,n.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[(0,n.jsx)(c(),{href:"/admin",className:"btn-outline text-sm py-2 px-4",children:"⚙️ Admin"}),(0,n.jsx)(c(),{href:"/global-view",className:"btn-outline text-sm py-2 px-4",children:"\uD83C\uDF10 Network View"}),(0,n.jsx)(c(),{href:"https://portal.candid-connections.com/user/login",target:"_blank",rel:"noopener noreferrer",className:"btn-primary text-sm py-2 px-4",children:"Portal Login"})]}),(0,n.jsx)("div",{className:"lg:hidden",children:(0,n.jsx)("button",{onClick:()=>r(!e),className:"p-2 rounded-lg text-candid-navy-600 hover:text-primary-600 hover:bg-primary-50 transition-colors duration-200","aria-label":"Toggle mobile menu",children:(0,n.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e?(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})})]}),e&&(0,n.jsx)("div",{className:"lg:hidden border-t border-candid-gray-200 py-4",children:(0,n.jsxs)("div",{className:"space-y-2",children:[t.map(e=>(0,n.jsxs)(c(),{href:e.path,onClick:()=>r(!1),className:`flex items-center space-x-3 px-4 py-3 rounded-lg text-base font-medium transition-all duration-200 ${o.pathname===e.path?"nav-link-active":"nav-link"}`,children:[(0,n.jsx)("span",{className:"text-lg",children:e.icon}),(0,n.jsx)("span",{children:e.name})]},e.path)),(0,n.jsxs)("div",{className:"pt-4 space-y-2",children:[(0,n.jsx)(c(),{href:"/global-view",onClick:()=>r(!1),className:"block w-full btn-outline text-center",children:"\uD83C\uDF10 Network View"}),(0,n.jsx)(c(),{href:"https://portal.candid-connections.com/user/login",target:"_blank",rel:"noopener noreferrer",className:"block w-full btn-primary text-center",children:"Portal Login"})]})]})})]})})}function u({children:o}){return(0,n.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,n.jsx)(d,{}),(0,n.jsx)("main",{className:"container-app section-padding",children:o})]})}},2768:()=>{},7522:(o,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>t});var n=r(8732);function t({Component:o,pageProps:e}){return(0,n.jsx)(o,{...e})}r(2768)},7605:(o,e,r)=>{"use strict";r.a(o,async(o,n)=>{try{var t=r(1915),c=r(7127),i=r(8238),f=r(6148),d=r(7929),u=r(1016),l=r(2423),a=r(300),s=r(204),m=r(8095),y=r(276),C=r(246),k=r(7141),g=r(4933),h=r(2355),x=r(437),L=r(7089),M=r(8945),S=r(7440),z=r(2533),B=r(1225),p=r(8180),j=r(2727),v=r(832),N=r(9275),b=r(39),w=r(2441),W=r(3351),A=r(6285),D=r(6501),P=o([t,c,i,f,d,u,l,a,s,m,y,C,k,g,h,x,L,M,S,z,B,p,j,v,N,b,w,W,A,D]);[t,c,i,f,d,u,l,a,s,m,y,C,k,g,h,x,L,M,S,z,B,p,j,v,N,b,w,W,A,D]=P.then?(await P)():P,r.o(t,"drag")&&r.d(e,{drag:function(){return t.drag}}),r.o(t,"forceCenter")&&r.d(e,{forceCenter:function(){return t.forceCenter}}),r.o(t,"forceCollide")&&r.d(e,{forceCollide:function(){return t.forceCollide}}),r.o(t,"forceLink")&&r.d(e,{forceLink:function(){return t.forceLink}}),r.o(t,"forceManyBody")&&r.d(e,{forceManyBody:function(){return t.forceManyBody}}),r.o(t,"forceSimulation")&&r.d(e,{forceSimulation:function(){return t.forceSimulation}}),r.o(t,"select")&&r.d(e,{select:function(){return t.select}}),r.o(t,"zoom")&&r.d(e,{zoom:function(){return t.zoom}}),r.o(c,"drag")&&r.d(e,{drag:function(){return c.drag}}),r.o(c,"forceCenter")&&r.d(e,{forceCenter:function(){return c.forceCenter}}),r.o(c,"forceCollide")&&r.d(e,{forceCollide:function(){return c.forceCollide}}),r.o(c,"forceLink")&&r.d(e,{forceLink:function(){return c.forceLink}}),r.o(c,"forceManyBody")&&r.d(e,{forceManyBody:function(){return c.forceManyBody}}),r.o(c,"forceSimulation")&&r.d(e,{forceSimulation:function(){return c.forceSimulation}}),r.o(c,"select")&&r.d(e,{select:function(){return c.select}}),r.o(c,"zoom")&&r.d(e,{zoom:function(){return c.zoom}}),r.o(i,"drag")&&r.d(e,{drag:function(){return i.drag}}),r.o(i,"forceCenter")&&r.d(e,{forceCenter:function(){return i.forceCenter}}),r.o(i,"forceCollide")&&r.d(e,{forceCollide:function(){return i.forceCollide}}),r.o(i,"forceLink")&&r.d(e,{forceLink:function(){return i.forceLink}}),r.o(i,"forceManyBody")&&r.d(e,{forceManyBody:function(){return i.forceManyBody}}),r.o(i,"forceSimulation")&&r.d(e,{forceSimulation:function(){return i.forceSimulation}}),r.o(i,"select")&&r.d(e,{select:function(){return i.select}}),r.o(i,"zoom")&&r.d(e,{zoom:function(){return i.zoom}}),r.o(f,"drag")&&r.d(e,{drag:function(){return f.drag}}),r.o(f,"forceCenter")&&r.d(e,{forceCenter:function(){return f.forceCenter}}),r.o(f,"forceCollide")&&r.d(e,{forceCollide:function(){return f.forceCollide}}),r.o(f,"forceLink")&&r.d(e,{forceLink:function(){return f.forceLink}}),r.o(f,"forceManyBody")&&r.d(e,{forceManyBody:function(){return f.forceManyBody}}),r.o(f,"forceSimulation")&&r.d(e,{forceSimulation:function(){return f.forceSimulation}}),r.o(f,"select")&&r.d(e,{select:function(){return f.select}}),r.o(f,"zoom")&&r.d(e,{zoom:function(){return f.zoom}}),r.o(d,"drag")&&r.d(e,{drag:function(){return d.drag}}),r.o(d,"forceCenter")&&r.d(e,{forceCenter:function(){return d.forceCenter}}),r.o(d,"forceCollide")&&r.d(e,{forceCollide:function(){return d.forceCollide}}),r.o(d,"forceLink")&&r.d(e,{forceLink:function(){return d.forceLink}}),r.o(d,"forceManyBody")&&r.d(e,{forceManyBody:function(){return d.forceManyBody}}),r.o(d,"forceSimulation")&&r.d(e,{forceSimulation:function(){return d.forceSimulation}}),r.o(d,"select")&&r.d(e,{select:function(){return d.select}}),r.o(d,"zoom")&&r.d(e,{zoom:function(){return d.zoom}}),r.o(u,"drag")&&r.d(e,{drag:function(){return u.drag}}),r.o(u,"forceCenter")&&r.d(e,{forceCenter:function(){return u.forceCenter}}),r.o(u,"forceCollide")&&r.d(e,{forceCollide:function(){return u.forceCollide}}),r.o(u,"forceLink")&&r.d(e,{forceLink:function(){return u.forceLink}}),r.o(u,"forceManyBody")&&r.d(e,{forceManyBody:function(){return u.forceManyBody}}),r.o(u,"forceSimulation")&&r.d(e,{forceSimulation:function(){return u.forceSimulation}}),r.o(u,"select")&&r.d(e,{select:function(){return u.select}}),r.o(u,"zoom")&&r.d(e,{zoom:function(){return u.zoom}}),r.o(l,"drag")&&r.d(e,{drag:function(){return l.drag}}),r.o(l,"forceCenter")&&r.d(e,{forceCenter:function(){return l.forceCenter}}),r.o(l,"forceCollide")&&r.d(e,{forceCollide:function(){return l.forceCollide}}),r.o(l,"forceLink")&&r.d(e,{forceLink:function(){return l.forceLink}}),r.o(l,"forceManyBody")&&r.d(e,{forceManyBody:function(){return l.forceManyBody}}),r.o(l,"forceSimulation")&&r.d(e,{forceSimulation:function(){return l.forceSimulation}}),r.o(l,"select")&&r.d(e,{select:function(){return l.select}}),r.o(l,"zoom")&&r.d(e,{zoom:function(){return l.zoom}}),r.o(a,"drag")&&r.d(e,{drag:function(){return a.drag}}),r.o(a,"forceCenter")&&r.d(e,{forceCenter:function(){return a.forceCenter}}),r.o(a,"forceCollide")&&r.d(e,{forceCollide:function(){return a.forceCollide}}),r.o(a,"forceLink")&&r.d(e,{forceLink:function(){return a.forceLink}}),r.o(a,"forceManyBody")&&r.d(e,{forceManyBody:function(){return a.forceManyBody}}),r.o(a,"forceSimulation")&&r.d(e,{forceSimulation:function(){return a.forceSimulation}}),r.o(a,"select")&&r.d(e,{select:function(){return a.select}}),r.o(a,"zoom")&&r.d(e,{zoom:function(){return a.zoom}}),r.o(s,"drag")&&r.d(e,{drag:function(){return s.drag}}),r.o(s,"forceCenter")&&r.d(e,{forceCenter:function(){return s.forceCenter}}),r.o(s,"forceCollide")&&r.d(e,{forceCollide:function(){return s.forceCollide}}),r.o(s,"forceLink")&&r.d(e,{forceLink:function(){return s.forceLink}}),r.o(s,"forceManyBody")&&r.d(e,{forceManyBody:function(){return s.forceManyBody}}),r.o(s,"forceSimulation")&&r.d(e,{forceSimulation:function(){return s.forceSimulation}}),r.o(s,"select")&&r.d(e,{select:function(){return s.select}}),r.o(s,"zoom")&&r.d(e,{zoom:function(){return s.zoom}}),r.o(m,"drag")&&r.d(e,{drag:function(){return m.drag}}),r.o(m,"forceCenter")&&r.d(e,{forceCenter:function(){return m.forceCenter}}),r.o(m,"forceCollide")&&r.d(e,{forceCollide:function(){return m.forceCollide}}),r.o(m,"forceLink")&&r.d(e,{forceLink:function(){return m.forceLink}}),r.o(m,"forceManyBody")&&r.d(e,{forceManyBody:function(){return m.forceManyBody}}),r.o(m,"forceSimulation")&&r.d(e,{forceSimulation:function(){return m.forceSimulation}}),r.o(m,"select")&&r.d(e,{select:function(){return m.select}}),r.o(m,"zoom")&&r.d(e,{zoom:function(){return m.zoom}}),r.o(y,"drag")&&r.d(e,{drag:function(){return y.drag}}),r.o(y,"forceCenter")&&r.d(e,{forceCenter:function(){return y.forceCenter}}),r.o(y,"forceCollide")&&r.d(e,{forceCollide:function(){return y.forceCollide}}),r.o(y,"forceLink")&&r.d(e,{forceLink:function(){return y.forceLink}}),r.o(y,"forceManyBody")&&r.d(e,{forceManyBody:function(){return y.forceManyBody}}),r.o(y,"forceSimulation")&&r.d(e,{forceSimulation:function(){return y.forceSimulation}}),r.o(y,"select")&&r.d(e,{select:function(){return y.select}}),r.o(y,"zoom")&&r.d(e,{zoom:function(){return y.zoom}}),r.o(C,"drag")&&r.d(e,{drag:function(){return C.drag}}),r.o(C,"forceCenter")&&r.d(e,{forceCenter:function(){return C.forceCenter}}),r.o(C,"forceCollide")&&r.d(e,{forceCollide:function(){return C.forceCollide}}),r.o(C,"forceLink")&&r.d(e,{forceLink:function(){return C.forceLink}}),r.o(C,"forceManyBody")&&r.d(e,{forceManyBody:function(){return C.forceManyBody}}),r.o(C,"forceSimulation")&&r.d(e,{forceSimulation:function(){return C.forceSimulation}}),r.o(C,"select")&&r.d(e,{select:function(){return C.select}}),r.o(C,"zoom")&&r.d(e,{zoom:function(){return C.zoom}}),r.o(k,"drag")&&r.d(e,{drag:function(){return k.drag}}),r.o(k,"forceCenter")&&r.d(e,{forceCenter:function(){return k.forceCenter}}),r.o(k,"forceCollide")&&r.d(e,{forceCollide:function(){return k.forceCollide}}),r.o(k,"forceLink")&&r.d(e,{forceLink:function(){return k.forceLink}}),r.o(k,"forceManyBody")&&r.d(e,{forceManyBody:function(){return k.forceManyBody}}),r.o(k,"forceSimulation")&&r.d(e,{forceSimulation:function(){return k.forceSimulation}}),r.o(k,"select")&&r.d(e,{select:function(){return k.select}}),r.o(k,"zoom")&&r.d(e,{zoom:function(){return k.zoom}}),r.o(g,"drag")&&r.d(e,{drag:function(){return g.drag}}),r.o(g,"forceCenter")&&r.d(e,{forceCenter:function(){return g.forceCenter}}),r.o(g,"forceCollide")&&r.d(e,{forceCollide:function(){return g.forceCollide}}),r.o(g,"forceLink")&&r.d(e,{forceLink:function(){return g.forceLink}}),r.o(g,"forceManyBody")&&r.d(e,{forceManyBody:function(){return g.forceManyBody}}),r.o(g,"forceSimulation")&&r.d(e,{forceSimulation:function(){return g.forceSimulation}}),r.o(g,"select")&&r.d(e,{select:function(){return g.select}}),r.o(g,"zoom")&&r.d(e,{zoom:function(){return g.zoom}}),r.o(h,"drag")&&r.d(e,{drag:function(){return h.drag}}),r.o(h,"forceCenter")&&r.d(e,{forceCenter:function(){return h.forceCenter}}),r.o(h,"forceCollide")&&r.d(e,{forceCollide:function(){return h.forceCollide}}),r.o(h,"forceLink")&&r.d(e,{forceLink:function(){return h.forceLink}}),r.o(h,"forceManyBody")&&r.d(e,{forceManyBody:function(){return h.forceManyBody}}),r.o(h,"forceSimulation")&&r.d(e,{forceSimulation:function(){return h.forceSimulation}}),r.o(h,"select")&&r.d(e,{select:function(){return h.select}}),r.o(h,"zoom")&&r.d(e,{zoom:function(){return h.zoom}}),r.o(x,"drag")&&r.d(e,{drag:function(){return x.drag}}),r.o(x,"forceCenter")&&r.d(e,{forceCenter:function(){return x.forceCenter}}),r.o(x,"forceCollide")&&r.d(e,{forceCollide:function(){return x.forceCollide}}),r.o(x,"forceLink")&&r.d(e,{forceLink:function(){return x.forceLink}}),r.o(x,"forceManyBody")&&r.d(e,{forceManyBody:function(){return x.forceManyBody}}),r.o(x,"forceSimulation")&&r.d(e,{forceSimulation:function(){return x.forceSimulation}}),r.o(x,"select")&&r.d(e,{select:function(){return x.select}}),r.o(x,"zoom")&&r.d(e,{zoom:function(){return x.zoom}}),r.o(L,"drag")&&r.d(e,{drag:function(){return L.drag}}),r.o(L,"forceCenter")&&r.d(e,{forceCenter:function(){return L.forceCenter}}),r.o(L,"forceCollide")&&r.d(e,{forceCollide:function(){return L.forceCollide}}),r.o(L,"forceLink")&&r.d(e,{forceLink:function(){return L.forceLink}}),r.o(L,"forceManyBody")&&r.d(e,{forceManyBody:function(){return L.forceManyBody}}),r.o(L,"forceSimulation")&&r.d(e,{forceSimulation:function(){return L.forceSimulation}}),r.o(L,"select")&&r.d(e,{select:function(){return L.select}}),r.o(L,"zoom")&&r.d(e,{zoom:function(){return L.zoom}}),r.o(M,"drag")&&r.d(e,{drag:function(){return M.drag}}),r.o(M,"forceCenter")&&r.d(e,{forceCenter:function(){return M.forceCenter}}),r.o(M,"forceCollide")&&r.d(e,{forceCollide:function(){return M.forceCollide}}),r.o(M,"forceLink")&&r.d(e,{forceLink:function(){return M.forceLink}}),r.o(M,"forceManyBody")&&r.d(e,{forceManyBody:function(){return M.forceManyBody}}),r.o(M,"forceSimulation")&&r.d(e,{forceSimulation:function(){return M.forceSimulation}}),r.o(M,"select")&&r.d(e,{select:function(){return M.select}}),r.o(M,"zoom")&&r.d(e,{zoom:function(){return M.zoom}}),r.o(S,"drag")&&r.d(e,{drag:function(){return S.drag}}),r.o(S,"forceCenter")&&r.d(e,{forceCenter:function(){return S.forceCenter}}),r.o(S,"forceCollide")&&r.d(e,{forceCollide:function(){return S.forceCollide}}),r.o(S,"forceLink")&&r.d(e,{forceLink:function(){return S.forceLink}}),r.o(S,"forceManyBody")&&r.d(e,{forceManyBody:function(){return S.forceManyBody}}),r.o(S,"forceSimulation")&&r.d(e,{forceSimulation:function(){return S.forceSimulation}}),r.o(S,"select")&&r.d(e,{select:function(){return S.select}}),r.o(S,"zoom")&&r.d(e,{zoom:function(){return S.zoom}}),r.o(z,"drag")&&r.d(e,{drag:function(){return z.drag}}),r.o(z,"forceCenter")&&r.d(e,{forceCenter:function(){return z.forceCenter}}),r.o(z,"forceCollide")&&r.d(e,{forceCollide:function(){return z.forceCollide}}),r.o(z,"forceLink")&&r.d(e,{forceLink:function(){return z.forceLink}}),r.o(z,"forceManyBody")&&r.d(e,{forceManyBody:function(){return z.forceManyBody}}),r.o(z,"forceSimulation")&&r.d(e,{forceSimulation:function(){return z.forceSimulation}}),r.o(z,"select")&&r.d(e,{select:function(){return z.select}}),r.o(z,"zoom")&&r.d(e,{zoom:function(){return z.zoom}}),r.o(B,"drag")&&r.d(e,{drag:function(){return B.drag}}),r.o(B,"forceCenter")&&r.d(e,{forceCenter:function(){return B.forceCenter}}),r.o(B,"forceCollide")&&r.d(e,{forceCollide:function(){return B.forceCollide}}),r.o(B,"forceLink")&&r.d(e,{forceLink:function(){return B.forceLink}}),r.o(B,"forceManyBody")&&r.d(e,{forceManyBody:function(){return B.forceManyBody}}),r.o(B,"forceSimulation")&&r.d(e,{forceSimulation:function(){return B.forceSimulation}}),r.o(B,"select")&&r.d(e,{select:function(){return B.select}}),r.o(B,"zoom")&&r.d(e,{zoom:function(){return B.zoom}}),r.o(p,"drag")&&r.d(e,{drag:function(){return p.drag}}),r.o(p,"forceCenter")&&r.d(e,{forceCenter:function(){return p.forceCenter}}),r.o(p,"forceCollide")&&r.d(e,{forceCollide:function(){return p.forceCollide}}),r.o(p,"forceLink")&&r.d(e,{forceLink:function(){return p.forceLink}}),r.o(p,"forceManyBody")&&r.d(e,{forceManyBody:function(){return p.forceManyBody}}),r.o(p,"forceSimulation")&&r.d(e,{forceSimulation:function(){return p.forceSimulation}}),r.o(p,"select")&&r.d(e,{select:function(){return p.select}}),r.o(p,"zoom")&&r.d(e,{zoom:function(){return p.zoom}}),r.o(j,"drag")&&r.d(e,{drag:function(){return j.drag}}),r.o(j,"forceCenter")&&r.d(e,{forceCenter:function(){return j.forceCenter}}),r.o(j,"forceCollide")&&r.d(e,{forceCollide:function(){return j.forceCollide}}),r.o(j,"forceLink")&&r.d(e,{forceLink:function(){return j.forceLink}}),r.o(j,"forceManyBody")&&r.d(e,{forceManyBody:function(){return j.forceManyBody}}),r.o(j,"forceSimulation")&&r.d(e,{forceSimulation:function(){return j.forceSimulation}}),r.o(j,"select")&&r.d(e,{select:function(){return j.select}}),r.o(j,"zoom")&&r.d(e,{zoom:function(){return j.zoom}}),r.o(v,"drag")&&r.d(e,{drag:function(){return v.drag}}),r.o(v,"forceCenter")&&r.d(e,{forceCenter:function(){return v.forceCenter}}),r.o(v,"forceCollide")&&r.d(e,{forceCollide:function(){return v.forceCollide}}),r.o(v,"forceLink")&&r.d(e,{forceLink:function(){return v.forceLink}}),r.o(v,"forceManyBody")&&r.d(e,{forceManyBody:function(){return v.forceManyBody}}),r.o(v,"forceSimulation")&&r.d(e,{forceSimulation:function(){return v.forceSimulation}}),r.o(v,"select")&&r.d(e,{select:function(){return v.select}}),r.o(v,"zoom")&&r.d(e,{zoom:function(){return v.zoom}}),r.o(N,"drag")&&r.d(e,{drag:function(){return N.drag}}),r.o(N,"forceCenter")&&r.d(e,{forceCenter:function(){return N.forceCenter}}),r.o(N,"forceCollide")&&r.d(e,{forceCollide:function(){return N.forceCollide}}),r.o(N,"forceLink")&&r.d(e,{forceLink:function(){return N.forceLink}}),r.o(N,"forceManyBody")&&r.d(e,{forceManyBody:function(){return N.forceManyBody}}),r.o(N,"forceSimulation")&&r.d(e,{forceSimulation:function(){return N.forceSimulation}}),r.o(N,"select")&&r.d(e,{select:function(){return N.select}}),r.o(N,"zoom")&&r.d(e,{zoom:function(){return N.zoom}}),r.o(b,"drag")&&r.d(e,{drag:function(){return b.drag}}),r.o(b,"forceCenter")&&r.d(e,{forceCenter:function(){return b.forceCenter}}),r.o(b,"forceCollide")&&r.d(e,{forceCollide:function(){return b.forceCollide}}),r.o(b,"forceLink")&&r.d(e,{forceLink:function(){return b.forceLink}}),r.o(b,"forceManyBody")&&r.d(e,{forceManyBody:function(){return b.forceManyBody}}),r.o(b,"forceSimulation")&&r.d(e,{forceSimulation:function(){return b.forceSimulation}}),r.o(b,"select")&&r.d(e,{select:function(){return b.select}}),r.o(b,"zoom")&&r.d(e,{zoom:function(){return b.zoom}}),r.o(w,"drag")&&r.d(e,{drag:function(){return w.drag}}),r.o(w,"forceCenter")&&r.d(e,{forceCenter:function(){return w.forceCenter}}),r.o(w,"forceCollide")&&r.d(e,{forceCollide:function(){return w.forceCollide}}),r.o(w,"forceLink")&&r.d(e,{forceLink:function(){return w.forceLink}}),r.o(w,"forceManyBody")&&r.d(e,{forceManyBody:function(){return w.forceManyBody}}),r.o(w,"forceSimulation")&&r.d(e,{forceSimulation:function(){return w.forceSimulation}}),r.o(w,"select")&&r.d(e,{select:function(){return w.select}}),r.o(w,"zoom")&&r.d(e,{zoom:function(){return w.zoom}}),r.o(W,"drag")&&r.d(e,{drag:function(){return W.drag}}),r.o(W,"forceCenter")&&r.d(e,{forceCenter:function(){return W.forceCenter}}),r.o(W,"forceCollide")&&r.d(e,{forceCollide:function(){return W.forceCollide}}),r.o(W,"forceLink")&&r.d(e,{forceLink:function(){return W.forceLink}}),r.o(W,"forceManyBody")&&r.d(e,{forceManyBody:function(){return W.forceManyBody}}),r.o(W,"forceSimulation")&&r.d(e,{forceSimulation:function(){return W.forceSimulation}}),r.o(W,"select")&&r.d(e,{select:function(){return W.select}}),r.o(W,"zoom")&&r.d(e,{zoom:function(){return W.zoom}}),r.o(A,"drag")&&r.d(e,{drag:function(){return A.drag}}),r.o(A,"forceCenter")&&r.d(e,{forceCenter:function(){return A.forceCenter}}),r.o(A,"forceCollide")&&r.d(e,{forceCollide:function(){return A.forceCollide}}),r.o(A,"forceLink")&&r.d(e,{forceLink:function(){return A.forceLink}}),r.o(A,"forceManyBody")&&r.d(e,{forceManyBody:function(){return A.forceManyBody}}),r.o(A,"forceSimulation")&&r.d(e,{forceSimulation:function(){return A.forceSimulation}}),r.o(A,"select")&&r.d(e,{select:function(){return A.select}}),r.o(A,"zoom")&&r.d(e,{zoom:function(){return A.zoom}}),r.o(D,"drag")&&r.d(e,{drag:function(){return D.drag}}),r.o(D,"forceCenter")&&r.d(e,{forceCenter:function(){return D.forceCenter}}),r.o(D,"forceCollide")&&r.d(e,{forceCollide:function(){return D.forceCollide}}),r.o(D,"forceLink")&&r.d(e,{forceLink:function(){return D.forceLink}}),r.o(D,"forceManyBody")&&r.d(e,{forceManyBody:function(){return D.forceManyBody}}),r.o(D,"forceSimulation")&&r.d(e,{forceSimulation:function(){return D.forceSimulation}}),r.o(D,"select")&&r.d(e,{select:function(){return D.select}}),r.o(D,"zoom")&&r.d(e,{zoom:function(){return D.zoom}}),n()}catch(o){n(o)}})},9662:(o,e,r)=>{"use strict";r.a(o,async(o,n)=>{try{r.d(e,{A:()=>d});var t=r(8732),c=r(2015),i=r(7605),f=o([i]);function d({data:o}){let e=(0,c.useRef)(null);return(0,t.jsx)("div",{className:"border rounded-lg shadow-sm bg-white p-4",children:(0,t.jsx)("svg",{ref:e,className:"w-full h-full"})})}i=(f.then?(await f)():f)[0],n()}catch(o){n(o)}})}};