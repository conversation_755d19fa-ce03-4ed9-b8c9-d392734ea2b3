exports.id=897,exports.ids=[897],exports.modules={5:(e,r,n)=>{"use strict";n.d(r,{A:()=>a});var o=n(8732),t=n(9918),c=n.n(t),i=n(4233),f=n(2015);function u(){let e=(0,i.useRouter)(),[r,n]=(0,f.useState)(!1),t=[{name:"Dashboard",path:"/",icon:"\uD83C\uDFE0"},{name:"Job Matches",path:"/matches",icon:"\uD83C\uDFAF"},{name:"Job Seekers",path:"/job-seekers",icon:"\uD83D\uDC65"},{name:"Companies",path:"/companies",icon:"\uD83C\uDFE2"},{name:"Positions",path:"/positions",icon:"\uD83D\uDCCB"},{name:"Skills",path:"/skills",icon:"\uD83D\uDEE0️"},{name:"Global View",path:"/global-view",icon:"\uD83C\uDF10"}];return(0,o.jsx)("nav",{className:"bg-white shadow-soft border-b border-candid-gray-200",children:(0,o.jsxs)("div",{className:"container-app",children:[(0,o.jsxs)("div",{className:"flex justify-between items-center h-20",children:[(0,o.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,o.jsxs)(c(),{href:"/",className:"flex items-center space-x-3 group",children:[(0,o.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-primary-500 to-accent-500 rounded-xl flex items-center justify-center shadow-soft group-hover:shadow-medium transition-all duration-200",children:(0,o.jsx)("span",{className:"text-white font-bold text-lg",children:"C"})}),(0,o.jsxs)("div",{className:"hidden sm:block",children:[(0,o.jsx)("h1",{className:"text-xl font-bold text-candid-navy-900 group-hover:text-primary-600 transition-colors duration-200",children:"Candid Connections"}),(0,o.jsx)("p",{className:"text-sm text-candid-gray-600 -mt-1",children:"Katra Platform"})]})]})}),(0,o.jsx)("div",{className:"hidden lg:block",children:(0,o.jsx)("div",{className:"flex items-center space-x-1",children:t.map(r=>(0,o.jsxs)(c(),{href:r.path,className:`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${e.pathname===r.path?"nav-link-active":"nav-link"}`,children:[(0,o.jsx)("span",{className:"text-base",children:r.icon}),(0,o.jsx)("span",{children:r.name})]},r.path))})}),(0,o.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[(0,o.jsx)(c(),{href:"/global-view",className:"btn-outline text-sm py-2 px-4",children:"\uD83C\uDF10 Network View"}),(0,o.jsx)(c(),{href:"https://portal.candid-connections.com/user/login",target:"_blank",rel:"noopener noreferrer",className:"btn-primary text-sm py-2 px-4",children:"Portal Login"})]}),(0,o.jsx)("div",{className:"lg:hidden",children:(0,o.jsx)("button",{onClick:()=>n(!r),className:"p-2 rounded-lg text-candid-navy-600 hover:text-primary-600 hover:bg-primary-50 transition-colors duration-200","aria-label":"Toggle mobile menu",children:(0,o.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r?(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})})]}),r&&(0,o.jsx)("div",{className:"lg:hidden border-t border-candid-gray-200 py-4",children:(0,o.jsxs)("div",{className:"space-y-2",children:[t.map(r=>(0,o.jsxs)(c(),{href:r.path,onClick:()=>n(!1),className:`flex items-center space-x-3 px-4 py-3 rounded-lg text-base font-medium transition-all duration-200 ${e.pathname===r.path?"nav-link-active":"nav-link"}`,children:[(0,o.jsx)("span",{className:"text-lg",children:r.icon}),(0,o.jsx)("span",{children:r.name})]},r.path)),(0,o.jsxs)("div",{className:"pt-4 space-y-2",children:[(0,o.jsx)(c(),{href:"/global-view",onClick:()=>n(!1),className:"block w-full btn-outline text-center",children:"\uD83C\uDF10 Network View"}),(0,o.jsx)(c(),{href:"https://portal.candid-connections.com/user/login",target:"_blank",rel:"noopener noreferrer",className:"block w-full btn-primary text-center",children:"Portal Login"})]})]})})]})})}function a({children:e}){return(0,o.jsxs)("div",{className:"min-h-screen bg-candid-gray-50",children:[(0,o.jsx)(u,{}),(0,o.jsx)("main",{className:"container-app section-padding",children:e})]})}},2768:()=>{},7522:(e,r,n)=>{"use strict";n.r(r),n.d(r,{default:()=>t});var o=n(8732);function t({Component:e,pageProps:r}){return(0,o.jsx)(e,{...r})}n(2768)},7605:(e,r,n)=>{"use strict";n.a(e,async(e,o)=>{try{var t=n(1915),c=n(7127),i=n(8238),f=n(6148),u=n(7929),a=n(1016),d=n(2423),l=n(300),s=n(204),y=n(8095),m=n(276),g=n(246),k=n(7141),h=n(4933),M=n(2355),C=n(437),L=n(7089),S=n(8945),B=n(7440),p=n(2533),x=n(1225),j=n(8180),b=n(2727),v=n(832),N=n(9275),w=n(39),$=n(2441),E=n(3351),A=n(6285),D=n(6501),P=e([t,c,i,f,u,a,d,l,s,y,m,g,k,h,M,C,L,S,B,p,x,j,b,v,N,w,$,E,A,D]);[t,c,i,f,u,a,d,l,s,y,m,g,k,h,M,C,L,S,B,p,x,j,b,v,N,w,$,E,A,D]=P.then?(await P)():P,n.o(t,"drag")&&n.d(r,{drag:function(){return t.drag}}),n.o(t,"forceCenter")&&n.d(r,{forceCenter:function(){return t.forceCenter}}),n.o(t,"forceLink")&&n.d(r,{forceLink:function(){return t.forceLink}}),n.o(t,"forceManyBody")&&n.d(r,{forceManyBody:function(){return t.forceManyBody}}),n.o(t,"forceSimulation")&&n.d(r,{forceSimulation:function(){return t.forceSimulation}}),n.o(t,"select")&&n.d(r,{select:function(){return t.select}}),n.o(c,"drag")&&n.d(r,{drag:function(){return c.drag}}),n.o(c,"forceCenter")&&n.d(r,{forceCenter:function(){return c.forceCenter}}),n.o(c,"forceLink")&&n.d(r,{forceLink:function(){return c.forceLink}}),n.o(c,"forceManyBody")&&n.d(r,{forceManyBody:function(){return c.forceManyBody}}),n.o(c,"forceSimulation")&&n.d(r,{forceSimulation:function(){return c.forceSimulation}}),n.o(c,"select")&&n.d(r,{select:function(){return c.select}}),n.o(i,"drag")&&n.d(r,{drag:function(){return i.drag}}),n.o(i,"forceCenter")&&n.d(r,{forceCenter:function(){return i.forceCenter}}),n.o(i,"forceLink")&&n.d(r,{forceLink:function(){return i.forceLink}}),n.o(i,"forceManyBody")&&n.d(r,{forceManyBody:function(){return i.forceManyBody}}),n.o(i,"forceSimulation")&&n.d(r,{forceSimulation:function(){return i.forceSimulation}}),n.o(i,"select")&&n.d(r,{select:function(){return i.select}}),n.o(f,"drag")&&n.d(r,{drag:function(){return f.drag}}),n.o(f,"forceCenter")&&n.d(r,{forceCenter:function(){return f.forceCenter}}),n.o(f,"forceLink")&&n.d(r,{forceLink:function(){return f.forceLink}}),n.o(f,"forceManyBody")&&n.d(r,{forceManyBody:function(){return f.forceManyBody}}),n.o(f,"forceSimulation")&&n.d(r,{forceSimulation:function(){return f.forceSimulation}}),n.o(f,"select")&&n.d(r,{select:function(){return f.select}}),n.o(u,"drag")&&n.d(r,{drag:function(){return u.drag}}),n.o(u,"forceCenter")&&n.d(r,{forceCenter:function(){return u.forceCenter}}),n.o(u,"forceLink")&&n.d(r,{forceLink:function(){return u.forceLink}}),n.o(u,"forceManyBody")&&n.d(r,{forceManyBody:function(){return u.forceManyBody}}),n.o(u,"forceSimulation")&&n.d(r,{forceSimulation:function(){return u.forceSimulation}}),n.o(u,"select")&&n.d(r,{select:function(){return u.select}}),n.o(a,"drag")&&n.d(r,{drag:function(){return a.drag}}),n.o(a,"forceCenter")&&n.d(r,{forceCenter:function(){return a.forceCenter}}),n.o(a,"forceLink")&&n.d(r,{forceLink:function(){return a.forceLink}}),n.o(a,"forceManyBody")&&n.d(r,{forceManyBody:function(){return a.forceManyBody}}),n.o(a,"forceSimulation")&&n.d(r,{forceSimulation:function(){return a.forceSimulation}}),n.o(a,"select")&&n.d(r,{select:function(){return a.select}}),n.o(d,"drag")&&n.d(r,{drag:function(){return d.drag}}),n.o(d,"forceCenter")&&n.d(r,{forceCenter:function(){return d.forceCenter}}),n.o(d,"forceLink")&&n.d(r,{forceLink:function(){return d.forceLink}}),n.o(d,"forceManyBody")&&n.d(r,{forceManyBody:function(){return d.forceManyBody}}),n.o(d,"forceSimulation")&&n.d(r,{forceSimulation:function(){return d.forceSimulation}}),n.o(d,"select")&&n.d(r,{select:function(){return d.select}}),n.o(l,"drag")&&n.d(r,{drag:function(){return l.drag}}),n.o(l,"forceCenter")&&n.d(r,{forceCenter:function(){return l.forceCenter}}),n.o(l,"forceLink")&&n.d(r,{forceLink:function(){return l.forceLink}}),n.o(l,"forceManyBody")&&n.d(r,{forceManyBody:function(){return l.forceManyBody}}),n.o(l,"forceSimulation")&&n.d(r,{forceSimulation:function(){return l.forceSimulation}}),n.o(l,"select")&&n.d(r,{select:function(){return l.select}}),n.o(s,"drag")&&n.d(r,{drag:function(){return s.drag}}),n.o(s,"forceCenter")&&n.d(r,{forceCenter:function(){return s.forceCenter}}),n.o(s,"forceLink")&&n.d(r,{forceLink:function(){return s.forceLink}}),n.o(s,"forceManyBody")&&n.d(r,{forceManyBody:function(){return s.forceManyBody}}),n.o(s,"forceSimulation")&&n.d(r,{forceSimulation:function(){return s.forceSimulation}}),n.o(s,"select")&&n.d(r,{select:function(){return s.select}}),n.o(y,"drag")&&n.d(r,{drag:function(){return y.drag}}),n.o(y,"forceCenter")&&n.d(r,{forceCenter:function(){return y.forceCenter}}),n.o(y,"forceLink")&&n.d(r,{forceLink:function(){return y.forceLink}}),n.o(y,"forceManyBody")&&n.d(r,{forceManyBody:function(){return y.forceManyBody}}),n.o(y,"forceSimulation")&&n.d(r,{forceSimulation:function(){return y.forceSimulation}}),n.o(y,"select")&&n.d(r,{select:function(){return y.select}}),n.o(m,"drag")&&n.d(r,{drag:function(){return m.drag}}),n.o(m,"forceCenter")&&n.d(r,{forceCenter:function(){return m.forceCenter}}),n.o(m,"forceLink")&&n.d(r,{forceLink:function(){return m.forceLink}}),n.o(m,"forceManyBody")&&n.d(r,{forceManyBody:function(){return m.forceManyBody}}),n.o(m,"forceSimulation")&&n.d(r,{forceSimulation:function(){return m.forceSimulation}}),n.o(m,"select")&&n.d(r,{select:function(){return m.select}}),n.o(g,"drag")&&n.d(r,{drag:function(){return g.drag}}),n.o(g,"forceCenter")&&n.d(r,{forceCenter:function(){return g.forceCenter}}),n.o(g,"forceLink")&&n.d(r,{forceLink:function(){return g.forceLink}}),n.o(g,"forceManyBody")&&n.d(r,{forceManyBody:function(){return g.forceManyBody}}),n.o(g,"forceSimulation")&&n.d(r,{forceSimulation:function(){return g.forceSimulation}}),n.o(g,"select")&&n.d(r,{select:function(){return g.select}}),n.o(k,"drag")&&n.d(r,{drag:function(){return k.drag}}),n.o(k,"forceCenter")&&n.d(r,{forceCenter:function(){return k.forceCenter}}),n.o(k,"forceLink")&&n.d(r,{forceLink:function(){return k.forceLink}}),n.o(k,"forceManyBody")&&n.d(r,{forceManyBody:function(){return k.forceManyBody}}),n.o(k,"forceSimulation")&&n.d(r,{forceSimulation:function(){return k.forceSimulation}}),n.o(k,"select")&&n.d(r,{select:function(){return k.select}}),n.o(h,"drag")&&n.d(r,{drag:function(){return h.drag}}),n.o(h,"forceCenter")&&n.d(r,{forceCenter:function(){return h.forceCenter}}),n.o(h,"forceLink")&&n.d(r,{forceLink:function(){return h.forceLink}}),n.o(h,"forceManyBody")&&n.d(r,{forceManyBody:function(){return h.forceManyBody}}),n.o(h,"forceSimulation")&&n.d(r,{forceSimulation:function(){return h.forceSimulation}}),n.o(h,"select")&&n.d(r,{select:function(){return h.select}}),n.o(M,"drag")&&n.d(r,{drag:function(){return M.drag}}),n.o(M,"forceCenter")&&n.d(r,{forceCenter:function(){return M.forceCenter}}),n.o(M,"forceLink")&&n.d(r,{forceLink:function(){return M.forceLink}}),n.o(M,"forceManyBody")&&n.d(r,{forceManyBody:function(){return M.forceManyBody}}),n.o(M,"forceSimulation")&&n.d(r,{forceSimulation:function(){return M.forceSimulation}}),n.o(M,"select")&&n.d(r,{select:function(){return M.select}}),n.o(C,"drag")&&n.d(r,{drag:function(){return C.drag}}),n.o(C,"forceCenter")&&n.d(r,{forceCenter:function(){return C.forceCenter}}),n.o(C,"forceLink")&&n.d(r,{forceLink:function(){return C.forceLink}}),n.o(C,"forceManyBody")&&n.d(r,{forceManyBody:function(){return C.forceManyBody}}),n.o(C,"forceSimulation")&&n.d(r,{forceSimulation:function(){return C.forceSimulation}}),n.o(C,"select")&&n.d(r,{select:function(){return C.select}}),n.o(L,"drag")&&n.d(r,{drag:function(){return L.drag}}),n.o(L,"forceCenter")&&n.d(r,{forceCenter:function(){return L.forceCenter}}),n.o(L,"forceLink")&&n.d(r,{forceLink:function(){return L.forceLink}}),n.o(L,"forceManyBody")&&n.d(r,{forceManyBody:function(){return L.forceManyBody}}),n.o(L,"forceSimulation")&&n.d(r,{forceSimulation:function(){return L.forceSimulation}}),n.o(L,"select")&&n.d(r,{select:function(){return L.select}}),n.o(S,"drag")&&n.d(r,{drag:function(){return S.drag}}),n.o(S,"forceCenter")&&n.d(r,{forceCenter:function(){return S.forceCenter}}),n.o(S,"forceLink")&&n.d(r,{forceLink:function(){return S.forceLink}}),n.o(S,"forceManyBody")&&n.d(r,{forceManyBody:function(){return S.forceManyBody}}),n.o(S,"forceSimulation")&&n.d(r,{forceSimulation:function(){return S.forceSimulation}}),n.o(S,"select")&&n.d(r,{select:function(){return S.select}}),n.o(B,"drag")&&n.d(r,{drag:function(){return B.drag}}),n.o(B,"forceCenter")&&n.d(r,{forceCenter:function(){return B.forceCenter}}),n.o(B,"forceLink")&&n.d(r,{forceLink:function(){return B.forceLink}}),n.o(B,"forceManyBody")&&n.d(r,{forceManyBody:function(){return B.forceManyBody}}),n.o(B,"forceSimulation")&&n.d(r,{forceSimulation:function(){return B.forceSimulation}}),n.o(B,"select")&&n.d(r,{select:function(){return B.select}}),n.o(p,"drag")&&n.d(r,{drag:function(){return p.drag}}),n.o(p,"forceCenter")&&n.d(r,{forceCenter:function(){return p.forceCenter}}),n.o(p,"forceLink")&&n.d(r,{forceLink:function(){return p.forceLink}}),n.o(p,"forceManyBody")&&n.d(r,{forceManyBody:function(){return p.forceManyBody}}),n.o(p,"forceSimulation")&&n.d(r,{forceSimulation:function(){return p.forceSimulation}}),n.o(p,"select")&&n.d(r,{select:function(){return p.select}}),n.o(x,"drag")&&n.d(r,{drag:function(){return x.drag}}),n.o(x,"forceCenter")&&n.d(r,{forceCenter:function(){return x.forceCenter}}),n.o(x,"forceLink")&&n.d(r,{forceLink:function(){return x.forceLink}}),n.o(x,"forceManyBody")&&n.d(r,{forceManyBody:function(){return x.forceManyBody}}),n.o(x,"forceSimulation")&&n.d(r,{forceSimulation:function(){return x.forceSimulation}}),n.o(x,"select")&&n.d(r,{select:function(){return x.select}}),n.o(j,"drag")&&n.d(r,{drag:function(){return j.drag}}),n.o(j,"forceCenter")&&n.d(r,{forceCenter:function(){return j.forceCenter}}),n.o(j,"forceLink")&&n.d(r,{forceLink:function(){return j.forceLink}}),n.o(j,"forceManyBody")&&n.d(r,{forceManyBody:function(){return j.forceManyBody}}),n.o(j,"forceSimulation")&&n.d(r,{forceSimulation:function(){return j.forceSimulation}}),n.o(j,"select")&&n.d(r,{select:function(){return j.select}}),n.o(b,"drag")&&n.d(r,{drag:function(){return b.drag}}),n.o(b,"forceCenter")&&n.d(r,{forceCenter:function(){return b.forceCenter}}),n.o(b,"forceLink")&&n.d(r,{forceLink:function(){return b.forceLink}}),n.o(b,"forceManyBody")&&n.d(r,{forceManyBody:function(){return b.forceManyBody}}),n.o(b,"forceSimulation")&&n.d(r,{forceSimulation:function(){return b.forceSimulation}}),n.o(b,"select")&&n.d(r,{select:function(){return b.select}}),n.o(v,"drag")&&n.d(r,{drag:function(){return v.drag}}),n.o(v,"forceCenter")&&n.d(r,{forceCenter:function(){return v.forceCenter}}),n.o(v,"forceLink")&&n.d(r,{forceLink:function(){return v.forceLink}}),n.o(v,"forceManyBody")&&n.d(r,{forceManyBody:function(){return v.forceManyBody}}),n.o(v,"forceSimulation")&&n.d(r,{forceSimulation:function(){return v.forceSimulation}}),n.o(v,"select")&&n.d(r,{select:function(){return v.select}}),n.o(N,"drag")&&n.d(r,{drag:function(){return N.drag}}),n.o(N,"forceCenter")&&n.d(r,{forceCenter:function(){return N.forceCenter}}),n.o(N,"forceLink")&&n.d(r,{forceLink:function(){return N.forceLink}}),n.o(N,"forceManyBody")&&n.d(r,{forceManyBody:function(){return N.forceManyBody}}),n.o(N,"forceSimulation")&&n.d(r,{forceSimulation:function(){return N.forceSimulation}}),n.o(N,"select")&&n.d(r,{select:function(){return N.select}}),n.o(w,"drag")&&n.d(r,{drag:function(){return w.drag}}),n.o(w,"forceCenter")&&n.d(r,{forceCenter:function(){return w.forceCenter}}),n.o(w,"forceLink")&&n.d(r,{forceLink:function(){return w.forceLink}}),n.o(w,"forceManyBody")&&n.d(r,{forceManyBody:function(){return w.forceManyBody}}),n.o(w,"forceSimulation")&&n.d(r,{forceSimulation:function(){return w.forceSimulation}}),n.o(w,"select")&&n.d(r,{select:function(){return w.select}}),n.o($,"drag")&&n.d(r,{drag:function(){return $.drag}}),n.o($,"forceCenter")&&n.d(r,{forceCenter:function(){return $.forceCenter}}),n.o($,"forceLink")&&n.d(r,{forceLink:function(){return $.forceLink}}),n.o($,"forceManyBody")&&n.d(r,{forceManyBody:function(){return $.forceManyBody}}),n.o($,"forceSimulation")&&n.d(r,{forceSimulation:function(){return $.forceSimulation}}),n.o($,"select")&&n.d(r,{select:function(){return $.select}}),n.o(E,"drag")&&n.d(r,{drag:function(){return E.drag}}),n.o(E,"forceCenter")&&n.d(r,{forceCenter:function(){return E.forceCenter}}),n.o(E,"forceLink")&&n.d(r,{forceLink:function(){return E.forceLink}}),n.o(E,"forceManyBody")&&n.d(r,{forceManyBody:function(){return E.forceManyBody}}),n.o(E,"forceSimulation")&&n.d(r,{forceSimulation:function(){return E.forceSimulation}}),n.o(E,"select")&&n.d(r,{select:function(){return E.select}}),n.o(A,"drag")&&n.d(r,{drag:function(){return A.drag}}),n.o(A,"forceCenter")&&n.d(r,{forceCenter:function(){return A.forceCenter}}),n.o(A,"forceLink")&&n.d(r,{forceLink:function(){return A.forceLink}}),n.o(A,"forceManyBody")&&n.d(r,{forceManyBody:function(){return A.forceManyBody}}),n.o(A,"forceSimulation")&&n.d(r,{forceSimulation:function(){return A.forceSimulation}}),n.o(A,"select")&&n.d(r,{select:function(){return A.select}}),n.o(D,"drag")&&n.d(r,{drag:function(){return D.drag}}),n.o(D,"forceCenter")&&n.d(r,{forceCenter:function(){return D.forceCenter}}),n.o(D,"forceLink")&&n.d(r,{forceLink:function(){return D.forceLink}}),n.o(D,"forceManyBody")&&n.d(r,{forceManyBody:function(){return D.forceManyBody}}),n.o(D,"forceSimulation")&&n.d(r,{forceSimulation:function(){return D.forceSimulation}}),n.o(D,"select")&&n.d(r,{select:function(){return D.select}}),o()}catch(e){o(e)}})},8587:(e,r,n)=>{"use strict";n.d(r,{_:()=>o});let o=()=>{let e=Array.from({length:5},(e,r)=>({id:`jobSeekers/${r}`,name:`Job Seeker ${r+1}`,type:"jobSeeker",size:8})),r=Array.from({length:3},(e,r)=>({id:`companies/${r}`,name:`Company ${r+1}`,type:"company",size:10})),n=Array.from({length:4},(e,r)=>({id:`positions/${r}`,name:`Position ${r+1}`,type:"position",size:9})),o=Array.from({length:8},(e,r)=>({id:`skills/${r}`,name:`Skill ${r+1}`,type:"skill",size:6})),t=[...e,...r,...n,...o],c=[];return e.forEach((e,n)=>{c.push({source:e.id,target:r[n%r.length].id,type:"works_for",value:1})}),r.forEach((e,r)=>{n.forEach((n,o)=>{(r+o)%2==0&&c.push({source:e.id,target:n.id,type:"posts",value:1})})}),e.forEach(e=>{let r=2+Math.floor(3*Math.random());[...o].sort(()=>.5-Math.random()).slice(0,r).forEach(r=>{c.push({source:e.id,target:r.id,type:"has_skill",value:1})})}),n.forEach(e=>{let r=2+Math.floor(2*Math.random());[...o].sort(()=>.5-Math.random()).slice(0,r).forEach(r=>{c.push({source:e.id,target:r.id,type:"requires",value:1})})}),{nodes:t,links:c}}},9662:(e,r,n)=>{"use strict";n.a(e,async(e,o)=>{try{n.d(r,{A:()=>u});var t=n(8732),c=n(2015),i=n(7605),f=e([i]);function u({data:e}){let r=(0,c.useRef)(null);return(0,t.jsx)("div",{className:"border rounded-lg shadow-sm bg-white p-4",children:(0,t.jsx)("svg",{ref:r,className:"w-full h-full"})})}i=(f.then?(await f)():f)[0],o()}catch(e){o(e)}})}};