"use strict";(()=>{var e={};e.id=267,e.ids=[267],e.modules={3306:(e,t,i)=>{i.a(e,async(e,n)=>{try{i.r(t),i.d(t,{config:()=>u,default:()=>c,routeModule:()=>h});var r=i(3480),a=i(8667),o=i(6435),s=i(4627),l=e([s]);s=(l.then?(await l)():l)[0];let c=(0,o.M)(s,"default"),u=(0,o.M)(s,"config"),h=new r.PagesAPIRouteModule({definition:{kind:a.A.PAGES_API,page:"/api/ai/describe",pathname:"/api/ai/describe",bundlePath:"",filename:""},userland:s});n()}catch(e){n(e)}})},3480:(e,t,i)=>{e.exports=i(5600)},4627:(e,t,i)=>{i.a(e,async(e,n)=>{try{i.r(t),i.d(t,{default:()=>o});var r=i(7984),a=e([r]);let s=new(r=(a.then?(await a)():a)[0]).default({apiKey:process.env.OPENAI_API_KEY});async function o(e,t){if("POST"!==e.method)return t.status(405).json({error:"Method not allowed"});let{entity:i,entityType:n}=e.body;if(!i||!n)return t.status(400).json({error:"Entity and entityType are required"});try{let e=function(e,t){switch(t){case"skill":return`Analyze the skill "${e.name}" in the context of hiring and talent acquisition. Consider its market demand (${e.demand}), category (${e.category}), and current industry trends. Provide insights on why this skill is valuable for hiring authorities and what types of roles typically require it.`;case"position":return`Analyze the position "${e.title}" with level "${e.level}" and type "${e.type}". Consider the typical responsibilities, required skills (${e.requirements?.join(", ")||"various"}), and what hiring authorities should look for when filling this role.`;case"company":return`Analyze the company "${e.name}" in the ${e.industry} industry with ${e.employeeCount} employees. Consider their likely hiring needs, organizational structure, and what makes them attractive to job seekers. Focus on their hiring authority structure and talent acquisition approach.`;case"authority":return`Analyze the hiring authority "${e.name}" with role "${e.role}" at level "${e.level}" with ${e.hiringPower} hiring power. Consider their decision-making influence, typical hiring responsibilities, and what job seekers should know when connecting with this authority level.`;default:return`Provide a professional analysis of this ${t} in the context of talent acquisition and hiring.`}}(i,n),r=await s.chat.completions.create({model:"gpt-3.5-turbo",messages:[{role:"system",content:"You are an expert talent acquisition and HR analytics assistant. Provide concise, professional insights about skills, positions, companies, and hiring authorities in the context of talent matching and recruitment. Keep responses to 2-3 sentences and focus on practical hiring insights."},{role:"user",content:e}],max_tokens:150,temperature:.7}),a=r.choices[0]?.message?.content?.trim();return t.status(200).json({description:a||"Unable to generate description."})}catch(r){console.error("OpenAI API error:",r);let e=function(e,t){switch(t){case"skill":return`${e.name} is a ${e.demand?.toLowerCase()||"medium"} demand skill in the ${e.category||"technology"} category. This skill is valuable for organizations looking to build technical capabilities and competitive advantage in their industry.`;case"position":return`${e.title} is a ${e.level||"mid-level"} position that typically requires specialized skills and experience. This role is important for organizations looking to fill key operational or strategic functions.`;case"company":return`${e.name} is a ${e.size||"growing"} company in the ${e.industry||"technology"} industry with ${e.employeeCount||"multiple"} employees. They likely have diverse hiring needs across various departments and skill levels.`;case"authority":return`${e.name} serves as ${e.role||"a key decision maker"} with ${e.hiringPower?.toLowerCase()||"significant"} hiring authority. They play a crucial role in talent acquisition and organizational growth decisions.`;default:return`This ${t} plays an important role in the talent acquisition and hiring process within the organization.`}}(i,n);return t.status(200).json({description:e})}}n()}catch(e){n(e)}})},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6435:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,i){return i in t?t[i]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,i)):"function"==typeof t&&"default"===i?t:void 0}}})},7984:e=>{e.exports=import("openai")},8667:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return i}});var i=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})}};var t=require("../../../webpack-api-runtime.js");t.C(e);var i=t(t.s=3306);module.exports=i})();