"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/companies";
exports.ids = ["pages/api/companies"];
exports.modules = {

/***/ "(api-node)/./lib/db.js":
/*!*******************!*\
  !*** ./lib/db.js ***!
  \*******************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   initDb: () => (/* binding */ initDb)\n/* harmony export */ });\n/* harmony import */ var arangojs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! arangojs */ \"arangojs\");\n/* harmony import */ var arangojs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(arangojs__WEBPACK_IMPORTED_MODULE_0__);\n\n// Get collections\nconst getCollections = async (db)=>{\n    return {\n        jobSeekers: db.collection('jobSeekers'),\n        companies: db.collection('companies'),\n        hiringAuthorities: db.collection('hiringAuthorities'),\n        positions: db.collection('positions'),\n        skills: db.collection('skills'),\n        matches: db.collection('matches'),\n        // Edge collections - using regular collection method for now\n        works_for: db.collection('works_for'),\n        employs: db.collection('employs'),\n        posts: db.collection('posts'),\n        requires: db.collection('requires'),\n        has_skill: db.collection('has_skill'),\n        matched_to: db.collection('matched_to'),\n        reports_to: db.collection('reports_to')\n    };\n};\n// Initialize database and collections\nconst initDb = async ()=>{\n    const dbName = process.env.ARANGODB_DB_NAME || 'candid_connections';\n    // First connect to _system database to create our database\n    const systemDb = new arangojs__WEBPACK_IMPORTED_MODULE_0__.Database({\n        url: process.env.ARANGODB_URL || 'http://localhost:8529',\n        databaseName: '_system',\n        auth: {\n            username: process.env.ARANGODB_USERNAME || 'root',\n            password: process.env.ARANGODB_PASSWORD || ''\n        }\n    });\n    // Ensure our database exists\n    try {\n        await systemDb.createDatabase(dbName);\n        console.log(`✅ Created database: ${dbName}`);\n    } catch (error) {\n        if (error.errorNum === 1207) {\n            console.log(`✅ Database ${dbName} already exists`);\n        } else {\n            console.log('Database creation note:', error.message);\n        }\n    }\n    // Now connect to our specific database\n    const db = new arangojs__WEBPACK_IMPORTED_MODULE_0__.Database({\n        url: process.env.ARANGODB_URL || 'http://localhost:8529',\n        databaseName: dbName,\n        auth: {\n            username: process.env.ARANGODB_USERNAME || 'root',\n            password: process.env.ARANGODB_PASSWORD || ''\n        }\n    });\n    const collections = await getCollections(db);\n    return {\n        db,\n        collections\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (initDb);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaS1ub2RlKS8uL2xpYi9kYi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQW1DO0FBRW5DLGtCQUFrQjtBQUNsQixNQUFNQyxpQkFBaUIsT0FBT0M7SUFDNUIsT0FBTztRQUNMQyxZQUFZRCxHQUFHRSxVQUFVLENBQUM7UUFDMUJDLFdBQVdILEdBQUdFLFVBQVUsQ0FBQztRQUN6QkUsbUJBQW1CSixHQUFHRSxVQUFVLENBQUM7UUFDakNHLFdBQVdMLEdBQUdFLFVBQVUsQ0FBQztRQUN6QkksUUFBUU4sR0FBR0UsVUFBVSxDQUFDO1FBQ3RCSyxTQUFTUCxHQUFHRSxVQUFVLENBQUM7UUFDdkIsNkRBQTZEO1FBQzdETSxXQUFXUixHQUFHRSxVQUFVLENBQUM7UUFDekJPLFNBQVNULEdBQUdFLFVBQVUsQ0FBQztRQUN2QlEsT0FBT1YsR0FBR0UsVUFBVSxDQUFDO1FBQ3JCUyxVQUFVWCxHQUFHRSxVQUFVLENBQUM7UUFDeEJVLFdBQVdaLEdBQUdFLFVBQVUsQ0FBQztRQUN6QlcsWUFBWWIsR0FBR0UsVUFBVSxDQUFDO1FBQzFCWSxZQUFZZCxHQUFHRSxVQUFVLENBQUM7SUFDNUI7QUFDRjtBQUVBLHNDQUFzQztBQUMvQixNQUFNYSxTQUFTO0lBQ3BCLE1BQU1DLFNBQVNDLFFBQVFDLEdBQUcsQ0FBQ0MsZ0JBQWdCLElBQUk7SUFFL0MsMkRBQTJEO0lBQzNELE1BQU1DLFdBQVcsSUFBSXRCLDhDQUFRQSxDQUFDO1FBQzVCdUIsS0FBS0osUUFBUUMsR0FBRyxDQUFDSSxZQUFZLElBQUk7UUFDakNDLGNBQWM7UUFDZEMsTUFBTTtZQUNKQyxVQUFVUixRQUFRQyxHQUFHLENBQUNRLGlCQUFpQixJQUFJO1lBQzNDQyxVQUFVVixRQUFRQyxHQUFHLENBQUNVLGlCQUFpQixJQUFJO1FBQzdDO0lBQ0Y7SUFFQSw2QkFBNkI7SUFDN0IsSUFBSTtRQUNGLE1BQU1SLFNBQVNTLGNBQWMsQ0FBQ2I7UUFDOUJjLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLG9CQUFvQixFQUFFZixRQUFRO0lBQzdDLEVBQUUsT0FBT2dCLE9BQU87UUFDZCxJQUFJQSxNQUFNQyxRQUFRLEtBQUssTUFBTTtZQUMzQkgsUUFBUUMsR0FBRyxDQUFDLENBQUMsV0FBVyxFQUFFZixPQUFPLGVBQWUsQ0FBQztRQUNuRCxPQUFPO1lBQ0xjLFFBQVFDLEdBQUcsQ0FBQywyQkFBMkJDLE1BQU1FLE9BQU87UUFDdEQ7SUFDRjtJQUVBLHVDQUF1QztJQUN2QyxNQUFNbEMsS0FBSyxJQUFJRiw4Q0FBUUEsQ0FBQztRQUN0QnVCLEtBQUtKLFFBQVFDLEdBQUcsQ0FBQ0ksWUFBWSxJQUFJO1FBQ2pDQyxjQUFjUDtRQUNkUSxNQUFNO1lBQ0pDLFVBQVVSLFFBQVFDLEdBQUcsQ0FBQ1EsaUJBQWlCLElBQUk7WUFDM0NDLFVBQVVWLFFBQVFDLEdBQUcsQ0FBQ1UsaUJBQWlCLElBQUk7UUFDN0M7SUFDRjtJQUVBLE1BQU1PLGNBQWMsTUFBTXBDLGVBQWVDO0lBQ3pDLE9BQU87UUFBRUE7UUFBSW1DO0lBQVk7QUFDM0IsRUFBQztBQUVELGlFQUFlcEIsTUFBTUEsRUFBQSIsInNvdXJjZXMiOlsiL1VzZXJzL2JyYWR5Z2Vvcmdlbi9Eb2N1bWVudHMvd29ya3NwYWNlL2NhbmRpZC1jb25uZWN0aW9ucy9saWIvZGIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgRGF0YWJhc2UgfSBmcm9tICdhcmFuZ29qcydcblxuLy8gR2V0IGNvbGxlY3Rpb25zXG5jb25zdCBnZXRDb2xsZWN0aW9ucyA9IGFzeW5jIChkYikgPT4ge1xuICByZXR1cm4ge1xuICAgIGpvYlNlZWtlcnM6IGRiLmNvbGxlY3Rpb24oJ2pvYlNlZWtlcnMnKSxcbiAgICBjb21wYW5pZXM6IGRiLmNvbGxlY3Rpb24oJ2NvbXBhbmllcycpLFxuICAgIGhpcmluZ0F1dGhvcml0aWVzOiBkYi5jb2xsZWN0aW9uKCdoaXJpbmdBdXRob3JpdGllcycpLFxuICAgIHBvc2l0aW9uczogZGIuY29sbGVjdGlvbigncG9zaXRpb25zJyksXG4gICAgc2tpbGxzOiBkYi5jb2xsZWN0aW9uKCdza2lsbHMnKSxcbiAgICBtYXRjaGVzOiBkYi5jb2xsZWN0aW9uKCdtYXRjaGVzJyksXG4gICAgLy8gRWRnZSBjb2xsZWN0aW9ucyAtIHVzaW5nIHJlZ3VsYXIgY29sbGVjdGlvbiBtZXRob2QgZm9yIG5vd1xuICAgIHdvcmtzX2ZvcjogZGIuY29sbGVjdGlvbignd29ya3NfZm9yJyksXG4gICAgZW1wbG95czogZGIuY29sbGVjdGlvbignZW1wbG95cycpLFxuICAgIHBvc3RzOiBkYi5jb2xsZWN0aW9uKCdwb3N0cycpLFxuICAgIHJlcXVpcmVzOiBkYi5jb2xsZWN0aW9uKCdyZXF1aXJlcycpLFxuICAgIGhhc19za2lsbDogZGIuY29sbGVjdGlvbignaGFzX3NraWxsJyksXG4gICAgbWF0Y2hlZF90bzogZGIuY29sbGVjdGlvbignbWF0Y2hlZF90bycpLFxuICAgIHJlcG9ydHNfdG86IGRiLmNvbGxlY3Rpb24oJ3JlcG9ydHNfdG8nKSxcbiAgfVxufVxuXG4vLyBJbml0aWFsaXplIGRhdGFiYXNlIGFuZCBjb2xsZWN0aW9uc1xuZXhwb3J0IGNvbnN0IGluaXREYiA9IGFzeW5jICgpID0+IHtcbiAgY29uc3QgZGJOYW1lID0gcHJvY2Vzcy5lbnYuQVJBTkdPREJfREJfTkFNRSB8fCAnY2FuZGlkX2Nvbm5lY3Rpb25zJ1xuXG4gIC8vIEZpcnN0IGNvbm5lY3QgdG8gX3N5c3RlbSBkYXRhYmFzZSB0byBjcmVhdGUgb3VyIGRhdGFiYXNlXG4gIGNvbnN0IHN5c3RlbURiID0gbmV3IERhdGFiYXNlKHtcbiAgICB1cmw6IHByb2Nlc3MuZW52LkFSQU5HT0RCX1VSTCB8fCAnaHR0cDovL2xvY2FsaG9zdDo4NTI5JyxcbiAgICBkYXRhYmFzZU5hbWU6ICdfc3lzdGVtJyxcbiAgICBhdXRoOiB7XG4gICAgICB1c2VybmFtZTogcHJvY2Vzcy5lbnYuQVJBTkdPREJfVVNFUk5BTUUgfHwgJ3Jvb3QnLFxuICAgICAgcGFzc3dvcmQ6IHByb2Nlc3MuZW52LkFSQU5HT0RCX1BBU1NXT1JEIHx8ICcnLFxuICAgIH0sXG4gIH0pXG5cbiAgLy8gRW5zdXJlIG91ciBkYXRhYmFzZSBleGlzdHNcbiAgdHJ5IHtcbiAgICBhd2FpdCBzeXN0ZW1EYi5jcmVhdGVEYXRhYmFzZShkYk5hbWUpXG4gICAgY29uc29sZS5sb2coYOKchSBDcmVhdGVkIGRhdGFiYXNlOiAke2RiTmFtZX1gKVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGlmIChlcnJvci5lcnJvck51bSA9PT0gMTIwNykge1xuICAgICAgY29uc29sZS5sb2coYOKchSBEYXRhYmFzZSAke2RiTmFtZX0gYWxyZWFkeSBleGlzdHNgKVxuICAgIH0gZWxzZSB7XG4gICAgICBjb25zb2xlLmxvZygnRGF0YWJhc2UgY3JlYXRpb24gbm90ZTonLCBlcnJvci5tZXNzYWdlKVxuICAgIH1cbiAgfVxuXG4gIC8vIE5vdyBjb25uZWN0IHRvIG91ciBzcGVjaWZpYyBkYXRhYmFzZVxuICBjb25zdCBkYiA9IG5ldyBEYXRhYmFzZSh7XG4gICAgdXJsOiBwcm9jZXNzLmVudi5BUkFOR09EQl9VUkwgfHwgJ2h0dHA6Ly9sb2NhbGhvc3Q6ODUyOScsXG4gICAgZGF0YWJhc2VOYW1lOiBkYk5hbWUsXG4gICAgYXV0aDoge1xuICAgICAgdXNlcm5hbWU6IHByb2Nlc3MuZW52LkFSQU5HT0RCX1VTRVJOQU1FIHx8ICdyb290JyxcbiAgICAgIHBhc3N3b3JkOiBwcm9jZXNzLmVudi5BUkFOR09EQl9QQVNTV09SRCB8fCAnJyxcbiAgICB9LFxuICB9KVxuXG4gIGNvbnN0IGNvbGxlY3Rpb25zID0gYXdhaXQgZ2V0Q29sbGVjdGlvbnMoZGIpXG4gIHJldHVybiB7IGRiLCBjb2xsZWN0aW9ucyB9XG59XG5cbmV4cG9ydCBkZWZhdWx0IGluaXREYiJdLCJuYW1lcyI6WyJEYXRhYmFzZSIsImdldENvbGxlY3Rpb25zIiwiZGIiLCJqb2JTZWVrZXJzIiwiY29sbGVjdGlvbiIsImNvbXBhbmllcyIsImhpcmluZ0F1dGhvcml0aWVzIiwicG9zaXRpb25zIiwic2tpbGxzIiwibWF0Y2hlcyIsIndvcmtzX2ZvciIsImVtcGxveXMiLCJwb3N0cyIsInJlcXVpcmVzIiwiaGFzX3NraWxsIiwibWF0Y2hlZF90byIsInJlcG9ydHNfdG8iLCJpbml0RGIiLCJkYk5hbWUiLCJwcm9jZXNzIiwiZW52IiwiQVJBTkdPREJfREJfTkFNRSIsInN5c3RlbURiIiwidXJsIiwiQVJBTkdPREJfVVJMIiwiZGF0YWJhc2VOYW1lIiwiYXV0aCIsInVzZXJuYW1lIiwiQVJBTkdPREJfVVNFUk5BTUUiLCJwYXNzd29yZCIsIkFSQU5HT0RCX1BBU1NXT1JEIiwiY3JlYXRlRGF0YWJhc2UiLCJjb25zb2xlIiwibG9nIiwiZXJyb3IiLCJlcnJvck51bSIsIm1lc3NhZ2UiLCJjb2xsZWN0aW9ucyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api-node)/./lib/db.js\n");

/***/ }),

/***/ "(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcompanies&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fcompanies.js&middlewareConfigBase64=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcompanies&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fcompanies.js&middlewareConfigBase64=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/./node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_companies_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages/api/companies.js */ \"(api-node)/./pages/api/companies.js\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_companies_js__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_companies_js__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/companies\",\n        pathname: \"/api/companies\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_companies_js__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaS1ub2RlKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtcm91dGUtbG9hZGVyL2luZGV4LmpzP2tpbmQ9UEFHRVNfQVBJJnBhZ2U9JTJGYXBpJTJGY29tcGFuaWVzJnByZWZlcnJlZFJlZ2lvbj0mYWJzb2x1dGVQYWdlUGF0aD0uJTJGcGFnZXMlMkZhcGklMkZjb21wYW5pZXMuanMmbWlkZGxld2FyZUNvbmZpZ0Jhc2U2ND1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQStGO0FBQ3ZDO0FBQ0U7QUFDMUQ7QUFDcUQ7QUFDckQ7QUFDQSxpRUFBZSx3RUFBSyxDQUFDLG9EQUFRLFlBQVksRUFBQztBQUMxQztBQUNPLGVBQWUsd0VBQUssQ0FBQyxvREFBUTtBQUNwQztBQUNPLHdCQUF3Qix5R0FBbUI7QUFDbEQ7QUFDQSxjQUFjLGtFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsWUFBWTtBQUNaLENBQUM7O0FBRUQiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQYWdlc0FQSVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUtbW9kdWxlcy9wYWdlcy1hcGkvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBob2lzdCB9IGZyb20gXCJuZXh0L2Rpc3QvYnVpbGQvdGVtcGxhdGVzL2hlbHBlcnNcIjtcbi8vIEltcG9ydCB0aGUgdXNlcmxhbmQgY29kZS5cbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCIuL3BhZ2VzL2FwaS9jb21wYW5pZXMuanNcIjtcbi8vIFJlLWV4cG9ydCB0aGUgaGFuZGxlciAoc2hvdWxkIGJlIHRoZSBkZWZhdWx0IGV4cG9ydCkuXG5leHBvcnQgZGVmYXVsdCBob2lzdCh1c2VybGFuZCwgJ2RlZmF1bHQnKTtcbi8vIFJlLWV4cG9ydCBjb25maWcuXG5leHBvcnQgY29uc3QgY29uZmlnID0gaG9pc3QodXNlcmxhbmQsICdjb25maWcnKTtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IFBhZ2VzQVBJUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLlBBR0VTX0FQSSxcbiAgICAgICAgcGFnZTogXCIvYXBpL2NvbXBhbmllc1wiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL2NvbXBhbmllc1wiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6ICcnLFxuICAgICAgICBmaWxlbmFtZTogJydcbiAgICB9LFxuICAgIHVzZXJsYW5kXG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cGFnZXMtYXBpLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcompanies&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fcompanies.js&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/./pages/api/companies.js":
/*!********************************!*\
  !*** ./pages/api/companies.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../lib/db */ \"(api-node)/./lib/db.js\");\n\nasync function handler(req, res) {\n    const { method } = req;\n    try {\n        const { db, collections } = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_0__.initDb)();\n        switch(method){\n            case 'GET':\n                await handleGet(req, res, db, collections);\n                break;\n            case 'POST':\n                await handlePost(req, res, db, collections);\n                break;\n            case 'PUT':\n                await handlePut(req, res, db, collections);\n                break;\n            case 'DELETE':\n                await handleDelete(req, res, db, collections);\n                break;\n            default:\n                res.setHeader('Allow', [\n                    'GET',\n                    'POST',\n                    'PUT',\n                    'DELETE'\n                ]);\n                res.status(405).end(`Method ${method} Not Allowed`);\n        }\n    } catch (error) {\n        console.error('API Error:', error);\n        res.status(500).json({\n            error: 'Internal Server Error'\n        });\n    }\n}\nasync function handleGet(req, res, db, collections) {\n    const { id, industry, size, location, limit = 50, offset = 0 } = req.query;\n    try {\n        if (id) {\n            // Get single company with related data\n            const query = `\n        LET company = DOCUMENT('companies', @id)\n        LET positions = (\n          FOR pos IN positions\n            FILTER pos.companyId == company._id\n            RETURN {\n              id: pos._key,\n              title: pos.title,\n              level: pos.level,\n              type: pos.type,\n              status: pos.status,\n              applicants: pos.applicants || 0\n            }\n        )\n        LET hiringAuthorities = (\n          FOR auth IN hiringAuthorities\n            FILTER auth.companyId == company._id\n            RETURN {\n              id: auth._key,\n              name: auth.name,\n              role: auth.role,\n              email: auth.email\n            }\n        )\n        RETURN {\n          id: company._key,\n          name: company.name,\n          industry: company.industry,\n          size: company.size,\n          location: company.location,\n          description: company.description,\n          founded: company.founded,\n          website: company.website,\n          logo: company.logo,\n          positions: positions,\n          hiringAuthorities: hiringAuthorities,\n          openPositions: LENGTH(positions[* FILTER CURRENT.status == 'active'])\n        }\n      `;\n            const cursor = await db.query(query, {\n                id\n            });\n            const result = await cursor.all();\n            if (result.length === 0) {\n                return res.status(404).json({\n                    error: 'Company not found'\n                });\n            }\n            res.status(200).json(result[0]);\n        } else {\n            // Get list of companies with filters\n            let query = `\n        FOR company IN companies\n      `;\n            const bindVars = {\n                limit: parseInt(limit),\n                offset: parseInt(offset)\n            };\n            const filters = [];\n            if (industry) {\n                filters.push('company.industry == @industry');\n                bindVars.industry = industry;\n            }\n            if (size) {\n                filters.push('company.size == @size');\n                bindVars.size = size;\n            }\n            if (location) {\n                filters.push('CONTAINS(LOWER(company.location), LOWER(@location))');\n                bindVars.location = location;\n            }\n            if (filters.length > 0) {\n                query += ` FILTER ${filters.join(' AND ')}`;\n            }\n            query += `\n        LET openPositions = LENGTH(\n          FOR pos IN positions\n            FILTER pos.companyId == company._id AND pos.status == 'active'\n            RETURN 1\n        )\n        SORT company.name ASC\n        LIMIT @offset, @limit\n        RETURN {\n          id: company._key,\n          name: company.name,\n          industry: company.industry,\n          size: company.size,\n          location: company.location,\n          description: company.description,\n          founded: company.founded,\n          website: company.website,\n          logo: company.logo,\n          openPositions: openPositions\n        }\n      `;\n            const cursor = await db.query(query, bindVars);\n            const companies = await cursor.all();\n            res.status(200).json(companies);\n        }\n    } catch (error) {\n        console.error('Error fetching companies:', error);\n        res.status(500).json({\n            error: 'Failed to fetch companies'\n        });\n    }\n}\nasync function handlePost(req, res, db, collections) {\n    const { name, industry, size, location, description, founded, website, logo } = req.body;\n    if (!name || !industry || !size || !location) {\n        return res.status(400).json({\n            error: 'Missing required fields: name, industry, size, location'\n        });\n    }\n    try {\n        // Check if company already exists\n        const existingQuery = `\n      FOR company IN companies\n        FILTER LOWER(company.name) == LOWER(@name)\n        RETURN company\n    `;\n        const existingCursor = await db.query(existingQuery, {\n            name\n        });\n        const existing = await existingCursor.all();\n        if (existing.length > 0) {\n            return res.status(409).json({\n                error: 'Company with this name already exists'\n            });\n        }\n        const companyData = {\n            name,\n            industry,\n            size,\n            location,\n            description: description || '',\n            founded: founded || null,\n            website: website || '',\n            logo: logo || '🏢',\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n        const result = await collections.companies.save(companyData);\n        res.status(201).json({\n            id: result._key,\n            ...companyData\n        });\n    } catch (error) {\n        console.error('Error creating company:', error);\n        res.status(500).json({\n            error: 'Failed to create company'\n        });\n    }\n}\nasync function handlePut(req, res, db, collections) {\n    const { id } = req.query;\n    const updateFields = req.body;\n    if (!id) {\n        return res.status(400).json({\n            error: 'Company ID is required'\n        });\n    }\n    try {\n        const updateData = {\n            ...updateFields,\n            updatedAt: new Date().toISOString()\n        };\n        // Remove fields that shouldn't be updated\n        delete updateData.id;\n        delete updateData.createdAt;\n        const result = await collections.companies.update(id, updateData);\n        if (!result._key) {\n            return res.status(404).json({\n                error: 'Company not found'\n            });\n        }\n        res.status(200).json({\n            id: result._key,\n            ...updateData\n        });\n    } catch (error) {\n        console.error('Error updating company:', error);\n        res.status(500).json({\n            error: 'Failed to update company'\n        });\n    }\n}\nasync function handleDelete(req, res, db, collections) {\n    const { id } = req.query;\n    if (!id) {\n        return res.status(400).json({\n            error: 'Company ID is required'\n        });\n    }\n    try {\n        // Check if company has active positions\n        const positionsQuery = `\n      FOR pos IN positions\n        FILTER pos.companyId == @companyId AND pos.status == 'active'\n        RETURN pos\n    `;\n        const positionsCursor = await db.query(positionsQuery, {\n            companyId: `companies/${id}`\n        });\n        const activePositions = await positionsCursor.all();\n        if (activePositions.length > 0) {\n            return res.status(400).json({\n                error: 'Cannot delete company with active positions. Please close all positions first.'\n            });\n        }\n        // Delete the company\n        await collections.companies.remove(id);\n        // Clean up related data\n        const cleanupQueries = [\n            // Delete hiring authorities\n            `\n        FOR auth IN hiringAuthorities\n          FILTER auth.companyId == @companyId\n          REMOVE auth IN hiringAuthorities\n      `,\n            // Delete employment relationships\n            `\n        FOR edge IN employs\n          FILTER STARTS_WITH(edge._from, @companyId)\n          REMOVE edge IN employs\n      `\n        ];\n        for (const query of cleanupQueries){\n            await db.query(query, {\n                companyId: `companies/${id}`\n            });\n        }\n        res.status(200).json({\n            message: 'Company deleted successfully'\n        });\n    } catch (error) {\n        if (error.errorNum === 1202) {\n            return res.status(404).json({\n                error: 'Company not found'\n            });\n        }\n        console.error('Error deleting company:', error);\n        res.status(500).json({\n            error: 'Failed to delete company'\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/companies.js\n");

/***/ }),

/***/ "arangojs":
/*!***************************!*\
  !*** external "arangojs" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("arangojs");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcompanies&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fcompanies.js&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();