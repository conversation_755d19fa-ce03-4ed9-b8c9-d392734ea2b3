"use strict";(()=>{var e={};e.id=552,e.ids=[552],e.modules={3480:(e,o,t)=>{e.exports=t(5600)},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5993:(e,o,t)=>{t.r(o),t.d(o,{config:()=>y,default:()=>m,routeModule:()=>E});var n={};t.r(n),t.d(n,{default:()=>c});var a=t(3480),s=t(8667),i=t(6435),r=t(7457);async function c(e,o){let{method:t}=e;try{let{db:n,collections:a}=await (0,r.X)();switch(t){case"GET":await p(e,o,n,a);break;case"POST":await l(e,o,n,a);break;case"PUT":await u(e,o,n,a);break;case"DELETE":await d(e,o,n,a);break;default:o.setHeader("Allow",["GET","POST","PUT","DELETE"]),o.status(405).end(`Method ${t} Not Allowed`)}}catch(e){console.error("API Error:",e),o.status(500).json({error:"Internal Server Error"})}}async function p(e,o,t,n){let{id:a,industry:s,size:i,location:r,limit:c=50,offset:p=0}=e.query;try{if(a){let e=`
        LET company = DOCUMENT('companies', @id)
        LET positions = (
          FOR pos IN positions
            FILTER pos.companyId == company._id
            RETURN {
              id: pos._key,
              title: pos.title,
              level: pos.level,
              type: pos.type,
              status: pos.status,
              applicants: pos.applicants || 0
            }
        )
        LET hiringAuthorities = (
          FOR auth IN hiringAuthorities
            FILTER auth.companyId == company._id
            RETURN {
              id: auth._key,
              name: auth.name,
              role: auth.role,
              email: auth.email
            }
        )
        RETURN {
          id: company._key,
          name: company.name,
          industry: company.industry,
          size: company.size,
          location: company.location,
          description: company.description,
          founded: company.founded,
          website: company.website,
          logo: company.logo,
          positions: positions,
          hiringAuthorities: hiringAuthorities,
          openPositions: LENGTH(positions[* FILTER CURRENT.status == 'active'])
        }
      `,n=await t.query(e,{id:a}),s=await n.all();if(0===s.length)return o.status(404).json({error:"Company not found"});o.status(200).json(s[0])}else{let e=`
        FOR company IN companies
      `,n={limit:parseInt(c),offset:parseInt(p)},a=[];s&&(a.push("company.industry == @industry"),n.industry=s),i&&(a.push("company.size == @size"),n.size=i),r&&(a.push("CONTAINS(LOWER(company.location), LOWER(@location))"),n.location=r),a.length>0&&(e+=` FILTER ${a.join(" AND ")}`),e+=`
        LET openPositions = LENGTH(
          FOR pos IN positions
            FILTER pos.companyId == company._id AND pos.status == 'active'
            RETURN 1
        )
        SORT company.name ASC
        LIMIT @offset, @limit
        RETURN {
          id: company._key,
          name: company.name,
          industry: company.industry,
          size: company.size,
          location: company.location,
          description: company.description,
          founded: company.founded,
          website: company.website,
          logo: company.logo,
          openPositions: openPositions
        }
      `;let l=await t.query(e,n),u=await l.all();o.status(200).json(u)}}catch(e){console.error("Error fetching companies:",e),o.status(500).json({error:"Failed to fetch companies"})}}async function l(e,o,t,n){let{name:a,industry:s,size:i,location:r,description:c,founded:p,website:l,logo:u}=e.body;if(!a||!s||!i||!r)return o.status(400).json({error:"Missing required fields: name, industry, size, location"});try{let e=`
      FOR company IN companies
        FILTER LOWER(company.name) == LOWER(@name)
        RETURN company
    `,d=await t.query(e,{name:a});if((await d.all()).length>0)return o.status(409).json({error:"Company with this name already exists"});let m={name:a,industry:s,size:i,location:r,description:c||"",founded:p||null,website:l||"",logo:u||"\uD83C\uDFE2",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},y=await n.companies.save(m);o.status(201).json({id:y._key,...m})}catch(e){console.error("Error creating company:",e),o.status(500).json({error:"Failed to create company"})}}async function u(e,o,t,n){let{id:a}=e.query,s=e.body;if(!a)return o.status(400).json({error:"Company ID is required"});try{let e={...s,updatedAt:new Date().toISOString()};delete e.id,delete e.createdAt;let t=await n.companies.update(a,e);if(!t._key)return o.status(404).json({error:"Company not found"});o.status(200).json({id:t._key,...e})}catch(e){console.error("Error updating company:",e),o.status(500).json({error:"Failed to update company"})}}async function d(e,o,t,n){let{id:a}=e.query;if(!a)return o.status(400).json({error:"Company ID is required"});try{let e=`
      FOR pos IN positions
        FILTER pos.companyId == @companyId AND pos.status == 'active'
        RETURN pos
    `,s=await t.query(e,{companyId:`companies/${a}`});if((await s.all()).length>0)return o.status(400).json({error:"Cannot delete company with active positions. Please close all positions first."});for(let e of(await n.companies.remove(a),[`
        FOR auth IN hiringAuthorities
          FILTER auth.companyId == @companyId
          REMOVE auth IN hiringAuthorities
      `,`
        FOR edge IN employs
          FILTER STARTS_WITH(edge._from, @companyId)
          REMOVE edge IN employs
      `]))await t.query(e,{companyId:`companies/${a}`});o.status(200).json({message:"Company deleted successfully"})}catch(e){if(1202===e.errorNum)return o.status(404).json({error:"Company not found"});console.error("Error deleting company:",e),o.status(500).json({error:"Failed to delete company"})}}let m=(0,i.M)(n,"default"),y=(0,i.M)(n,"config"),E=new a.PagesAPIRouteModule({definition:{kind:s.A.PAGES_API,page:"/api/companies",pathname:"/api/companies",bundlePath:"",filename:""},userland:n})},6435:(e,o)=>{Object.defineProperty(o,"M",{enumerable:!0,get:function(){return function e(o,t){return t in o?o[t]:"then"in o&&"function"==typeof o.then?o.then(o=>e(o,t)):"function"==typeof o&&"default"===t?o:void 0}}})},7457:(e,o,t)=>{t.d(o,{A:()=>r,X:()=>i});let n=require("arangojs"),a=()=>new n.Database({url:process.env.ARANGODB_URL||"http://localhost:8529",databaseName:process.env.ARANGODB_DB_NAME||"candid_connections",auth:{username:process.env.ARANGODB_USERNAME||"root",password:process.env.ARANGODB_PASSWORD||""}}),s=async e=>({jobSeekers:e.collection("jobSeekers"),companies:e.collection("companies"),hiringAuthorities:e.collection("hiringAuthorities"),positions:e.collection("positions"),skills:e.collection("skills"),matches:e.collection("matches"),works_for:e.edgeCollection("works_for"),employs:e.edgeCollection("employs"),posts:e.edgeCollection("posts"),requires:e.edgeCollection("requires"),has_skill:e.edgeCollection("has_skill"),matched_to:e.edgeCollection("matched_to")}),i=async()=>{let e=a(),o=await s(e);return{db:e,collections:o}},r=i},8667:(e,o)=>{Object.defineProperty(o,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})}};var o=require("../../webpack-api-runtime.js");o.C(e);var t=o(o.s=5993);module.exports=t})();