"use strict";(()=>{var e={};e.id=576,e.ids=[576],e.modules={2671:(e,t,r)=>{r.r(t),r.d(t,{config:()=>y,default:()=>d,routeModule:()=>m});var o={};r.r(o),r.d(o,{default:()=>u});var i=r(3480),a=r(8667),n=r(6435),s=r(7457);async function u(e,t){let{method:r}=e;try{let{db:o,collections:i}=await (0,s.X)();switch(r){case"GET":await c(e,t,o,i);break;case"POST":await l(e,t,o,i);break;case"PUT":await h(e,t,o,i);break;case"DELETE":await p(e,t,o,i);break;default:t.setHeader("Allow",["GET","POST","PUT","DELETE"]),t.status(405).end(`Method ${r} Not Allowed`)}}catch(e){console.error("API Error:",e),t.status(500).json({error:"Internal Server Error"})}}async function c(e,t,r,o){let{id:i,role:a,companySize:n,industry:s,limit:u=50,offset:c=0}=e.query;try{if(i){let e=`
        LET authority = DOCUMENT('hiringAuthorities', @id)
        LET company = DOCUMENT('companies', authority.companyId)
        LET positions = (
          FOR pos IN positions
            FILTER pos.hiringAuthorityId == authority._id
            RETURN {
              id: pos._key,
              title: pos.title,
              level: pos.level,
              type: pos.type,
              status: pos.status,
              requirements: pos.requirements || []
            }
        )
        LET matches = (
          FOR match IN matches
            FILTER match.hiringAuthorityId == authority._id
            LET jobSeeker = DOCUMENT('jobSeekers', match.jobSeekerId)
            RETURN {
              id: match._key,
              jobSeeker: {
                id: jobSeeker._key,
                name: jobSeeker.name,
                title: jobSeeker.currentTitle
              },
              score: match.score,
              status: match.status
            }
        )
        RETURN {
          id: authority._key,
          name: authority.name,
          role: authority.role,
          level: authority.level,
          email: authority.email,
          phone: authority.phone,
          hiringPower: authority.hiringPower,
          decisionMaker: authority.decisionMaker,
          company: {
            id: company._key,
            name: company.name,
            size: company.size,
            industry: company.industry
          },
          positions: positions,
          matches: matches,
          skillsLookingFor: authority.skillsLookingFor || [],
          preferredExperience: authority.preferredExperience,
          bio: authority.bio,
          linkedIn: authority.linkedIn
        }
      `,o=await r.query(e,{id:i}),a=await o.next();if(!a)return t.status(404).json({error:"Hiring authority not found"});return t.status(200).json(a)}{let e=[],o={limit:parseInt(u),offset:parseInt(c)};a&&(e.push("LOWER(auth.role) LIKE @role"),o.role=`%${a.toLowerCase()}%`),s&&(e.push("LOWER(company.industry) LIKE @industry"),o.industry=`%${s.toLowerCase()}%`),n&&(e.push("company.size == @companySize"),o.companySize=n);let i=e.length>0?`FILTER ${e.join(" AND ")}`:"",l=`
        FOR auth IN hiringAuthorities
          LET company = DOCUMENT('companies', auth.companyId)
          ${i}
          LET activePositions = LENGTH(
            FOR pos IN positions
              FILTER pos.hiringAuthorityId == auth._id AND pos.status == 'active'
              RETURN 1
          )
          SORT auth.hiringPower DESC, auth.name ASC
          LIMIT @offset, @limit
          RETURN {
            id: auth._key,
            name: auth.name,
            role: auth.role,
            level: auth.level,
            email: auth.email,
            hiringPower: auth.hiringPower,
            decisionMaker: auth.decisionMaker,
            company: {
              id: company._key,
              name: company.name,
              size: company.size,
              industry: company.industry
            },
            activePositions: activePositions,
            skillsLookingFor: auth.skillsLookingFor || [],
            preferredExperience: auth.preferredExperience,
            avatar: auth.avatar || '👔'
          }
      `,h=await r.query(l,o),p=await h.all(),d=`
        FOR auth IN hiringAuthorities
          LET company = DOCUMENT('companies', auth.companyId)
          ${i}
          COLLECT WITH COUNT INTO total
          RETURN total
      `,y=await r.query(d,o),m=await y.next()||0;return t.status(200).json({authorities:p,pagination:{total:m,limit:parseInt(u),offset:parseInt(c),hasMore:parseInt(c)+p.length<m}})}}catch(e){return console.error("Error fetching hiring authorities:",e),t.status(500).json({error:"Failed to fetch hiring authorities"})}}async function l(e,t,r,o){try{let{hiringAuthorities:r}=o,i={...e.body,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},a=await r.save(i);return t.status(201).json({id:a._key,...i})}catch(e){return console.error("Error creating hiring authority:",e),t.status(500).json({error:"Failed to create hiring authority"})}}async function h(e,t,r,o){let{id:i}=e.query;if(!i)return t.status(400).json({error:"Authority ID is required"});try{let{hiringAuthorities:r}=o,a={...e.body,updatedAt:new Date().toISOString()},n=await r.update(i,a);return t.status(200).json({id:n._key,...a})}catch(e){return console.error("Error updating hiring authority:",e),t.status(500).json({error:"Failed to update hiring authority"})}}async function p(e,t,r,o){let{id:i}=e.query;if(!i)return t.status(400).json({error:"Authority ID is required"});try{let{hiringAuthorities:e}=o;return await e.remove(i),t.status(200).json({message:"Hiring authority deleted successfully"})}catch(e){return console.error("Error deleting hiring authority:",e),t.status(500).json({error:"Failed to delete hiring authority"})}}let d=(0,n.M)(o,"default"),y=(0,n.M)(o,"config"),m=new i.PagesAPIRouteModule({definition:{kind:a.A.PAGES_API,page:"/api/hiring-authorities",pathname:"/api/hiring-authorities",bundlePath:"",filename:""},userland:o})},3480:(e,t,r)=>{e.exports=r(5600)},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6435:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},7457:(e,t,r)=>{r.d(t,{A:()=>s,X:()=>n});let o=require("arangojs"),i=()=>new o.Database({url:process.env.ARANGODB_URL||"http://localhost:8529",databaseName:process.env.ARANGODB_DB_NAME||"candid_connections",auth:{username:process.env.ARANGODB_USERNAME||"root",password:process.env.ARANGODB_PASSWORD||""}}),a=async e=>({jobSeekers:e.collection("jobSeekers"),companies:e.collection("companies"),hiringAuthorities:e.collection("hiringAuthorities"),positions:e.collection("positions"),skills:e.collection("skills"),matches:e.collection("matches"),works_for:e.collection("works_for"),employs:e.collection("employs"),posts:e.collection("posts"),requires:e.collection("requires"),has_skill:e.collection("has_skill"),matched_to:e.collection("matched_to"),reports_to:e.collection("reports_to")}),n=async()=>{let e=i();try{await e.createDatabase(process.env.ARANGODB_DB_NAME||"candid_connections")}catch(e){e.message.includes("duplicate name")||console.log("Database creation note:",e.message)}let t=await a(e);return{db:e,collections:t}},s=n},8667:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})}};var t=require("../../webpack-api-runtime.js");t.C(e);var r=t(t.s=2671);module.exports=r})();