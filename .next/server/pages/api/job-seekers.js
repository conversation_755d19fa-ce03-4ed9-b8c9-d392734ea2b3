"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/job-seekers";
exports.ids = ["pages/api/job-seekers"];
exports.modules = {

/***/ "(api-node)/./lib/db.js":
/*!*******************!*\
  !*** ./lib/db.js ***!
  \*******************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   initDb: () => (/* binding */ initDb)\n/* harmony export */ });\n/* harmony import */ var arangojs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! arangojs */ \"arangojs\");\n/* harmony import */ var arangojs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(arangojs__WEBPACK_IMPORTED_MODULE_0__);\n\n// Get collections\nconst getCollections = async (db)=>{\n    return {\n        jobSeekers: db.collection('jobSeekers'),\n        companies: db.collection('companies'),\n        hiringAuthorities: db.collection('hiringAuthorities'),\n        positions: db.collection('positions'),\n        skills: db.collection('skills'),\n        matches: db.collection('matches'),\n        // Edge collections - using regular collection method for now\n        works_for: db.collection('works_for'),\n        employs: db.collection('employs'),\n        posts: db.collection('posts'),\n        requires: db.collection('requires'),\n        has_skill: db.collection('has_skill'),\n        matched_to: db.collection('matched_to'),\n        reports_to: db.collection('reports_to')\n    };\n};\n// Initialize database and collections\nconst initDb = async ()=>{\n    const dbName = process.env.ARANGODB_DB_NAME || 'candid_connections';\n    // First connect to _system database to create our database\n    const systemDb = new arangojs__WEBPACK_IMPORTED_MODULE_0__.Database({\n        url: process.env.ARANGODB_URL || 'http://localhost:8529',\n        databaseName: '_system',\n        auth: {\n            username: process.env.ARANGODB_USERNAME || 'root',\n            password: process.env.ARANGODB_PASSWORD || ''\n        }\n    });\n    // Ensure our database exists\n    try {\n        await systemDb.createDatabase(dbName);\n        console.log(`✅ Created database: ${dbName}`);\n    } catch (error) {\n        if (error.errorNum === 1207) {\n            console.log(`✅ Database ${dbName} already exists`);\n        } else {\n            console.log('Database creation note:', error.message);\n        }\n    }\n    // Now connect to our specific database\n    const db = new arangojs__WEBPACK_IMPORTED_MODULE_0__.Database({\n        url: process.env.ARANGODB_URL || 'http://localhost:8529',\n        databaseName: dbName,\n        auth: {\n            username: process.env.ARANGODB_USERNAME || 'root',\n            password: process.env.ARANGODB_PASSWORD || ''\n        }\n    });\n    const collections = await getCollections(db);\n    return {\n        db,\n        collections\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (initDb);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./lib/db.js\n");

/***/ }),

/***/ "(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fjob-seekers&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fjob-seekers.js&middlewareConfigBase64=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fjob-seekers&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fjob-seekers.js&middlewareConfigBase64=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/./node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_job_seekers_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages/api/job-seekers.js */ \"(api-node)/./pages/api/job-seekers.js\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_job_seekers_js__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_job_seekers_js__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/job-seekers\",\n        pathname: \"/api/job-seekers\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_job_seekers_js__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaS1ub2RlKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtcm91dGUtbG9hZGVyL2luZGV4LmpzP2tpbmQ9UEFHRVNfQVBJJnBhZ2U9JTJGYXBpJTJGam9iLXNlZWtlcnMmcHJlZmVycmVkUmVnaW9uPSZhYnNvbHV0ZVBhZ2VQYXRoPS4lMkZwYWdlcyUyRmFwaSUyRmpvYi1zZWVrZXJzLmpzJm1pZGRsZXdhcmVDb25maWdCYXNlNjQ9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUErRjtBQUN2QztBQUNFO0FBQzFEO0FBQ3VEO0FBQ3ZEO0FBQ0EsaUVBQWUsd0VBQUssQ0FBQyxzREFBUSxZQUFZLEVBQUM7QUFDMUM7QUFDTyxlQUFlLHdFQUFLLENBQUMsc0RBQVE7QUFDcEM7QUFDTyx3QkFBd0IseUdBQW1CO0FBQ2xEO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLFlBQVk7QUFDWixDQUFDOztBQUVEIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUGFnZXNBUElSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvcGFnZXMtYXBpL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgaG9pc3QgfSBmcm9tIFwibmV4dC9kaXN0L2J1aWxkL3RlbXBsYXRlcy9oZWxwZXJzXCI7XG4vLyBJbXBvcnQgdGhlIHVzZXJsYW5kIGNvZGUuXG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiLi9wYWdlcy9hcGkvam9iLXNlZWtlcnMuanNcIjtcbi8vIFJlLWV4cG9ydCB0aGUgaGFuZGxlciAoc2hvdWxkIGJlIHRoZSBkZWZhdWx0IGV4cG9ydCkuXG5leHBvcnQgZGVmYXVsdCBob2lzdCh1c2VybGFuZCwgJ2RlZmF1bHQnKTtcbi8vIFJlLWV4cG9ydCBjb25maWcuXG5leHBvcnQgY29uc3QgY29uZmlnID0gaG9pc3QodXNlcmxhbmQsICdjb25maWcnKTtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IFBhZ2VzQVBJUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLlBBR0VTX0FQSSxcbiAgICAgICAgcGFnZTogXCIvYXBpL2pvYi1zZWVrZXJzXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvam9iLXNlZWtlcnNcIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiAnJyxcbiAgICAgICAgZmlsZW5hbWU6ICcnXG4gICAgfSxcbiAgICB1c2VybGFuZFxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBhZ2VzLWFwaS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fjob-seekers&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fjob-seekers.js&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/./pages/api/job-seekers.js":
/*!**********************************!*\
  !*** ./pages/api/job-seekers.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../lib/db */ \"(api-node)/./lib/db.js\");\n\nasync function handler(req, res) {\n    try {\n        const { db, collections } = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_0__[\"default\"])();\n        const { jobSeekers } = collections;\n        switch(req.method){\n            case 'GET':\n                const cursor = await jobSeekers.all();\n                const result = await cursor.all();\n                return res.status(200).json(result);\n            case 'POST':\n                const newJobSeeker = req.body;\n                const saved = await jobSeekers.save(newJobSeeker);\n                return res.status(201).json(saved);\n            default:\n                return res.status(405).json({\n                    message: 'Method not allowed'\n                });\n        }\n    } catch (error) {\n        console.error('Error handling job seekers:', error);\n        return res.status(500).json({\n            message: 'Internal server error',\n            error: error.message\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/job-seekers.js\n");

/***/ }),

/***/ "arangojs":
/*!***************************!*\
  !*** external "arangojs" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("arangojs");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fjob-seekers&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fjob-seekers.js&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();