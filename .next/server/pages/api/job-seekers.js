"use strict";(()=>{var e={};e.id=951,e.ids=[951],e.modules={3480:(e,t,o)=>{e.exports=o(5600)},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6435:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,o){return o in t?t[o]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,o)):"function"==typeof t&&"default"===o?t:void 0}}})},7457:(e,t,o)=>{o.d(t,{A:()=>n,X:()=>a});let s=require("arangojs"),r=async e=>({jobSeekers:e.collection("jobSeekers"),companies:e.collection("companies"),hiringAuthorities:e.collection("hiringAuthorities"),positions:e.collection("positions"),skills:e.collection("skills"),matches:e.collection("matches"),works_for:e.collection("works_for"),employs:e.collection("employs"),posts:e.collection("posts"),requires:e.collection("requires"),has_skill:e.collection("has_skill"),matched_to:e.collection("matched_to"),reports_to:e.collection("reports_to")}),a=async()=>{let e=process.env.ARANGODB_DB_NAME||"candid_connections",t=new s.Database({url:process.env.ARANGODB_URL||"http://localhost:8529",databaseName:"_system",auth:{username:process.env.ARANGODB_USERNAME||"root",password:process.env.ARANGODB_PASSWORD||""}});try{await t.createDatabase(e),console.log(`✅ Created database: ${e}`)}catch(t){1207===t.errorNum?console.log(`✅ Database ${e} already exists`):console.log("Database creation note:",t.message)}let o=new s.Database({url:process.env.ARANGODB_URL||"http://localhost:8529",databaseName:e,auth:{username:process.env.ARANGODB_USERNAME||"root",password:process.env.ARANGODB_PASSWORD||""}}),a=await r(o);return{db:o,collections:a}},n=a},8448:(e,t,o)=>{o.r(t),o.d(t,{config:()=>u,default:()=>c,routeModule:()=>A});var s={};o.r(s),o.d(s,{default:()=>i});var r=o(3480),a=o(8667),n=o(6435),l=o(7457);async function i(e,t){try{let{db:o,collections:s}=await (0,l.A)(),{jobSeekers:r}=s;switch(e.method){case"GET":let a=await r.all(),n=await a.all();return t.status(200).json(n);case"POST":let i=e.body,c=await r.save(i);return t.status(201).json(c);default:return t.status(405).json({message:"Method not allowed"})}}catch(e){return console.error("Error handling job seekers:",e),t.status(500).json({message:"Internal server error",error:e.message})}}let c=(0,n.M)(s,"default"),u=(0,n.M)(s,"config"),A=new r.PagesAPIRouteModule({definition:{kind:a.A.PAGES_API,page:"/api/job-seekers",pathname:"/api/job-seekers",bundlePath:"",filename:""},userland:s})},8667:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return o}});var o=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})}};var t=require("../../webpack-api-runtime.js");t.C(e);var o=t(t.s=8448);module.exports=o})();