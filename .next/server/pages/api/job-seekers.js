"use strict";(()=>{var e={};e.id=951,e.ids=[951],e.modules={3480:(e,t,o)=>{e.exports=o(5600)},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6435:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,o){return o in t?t[o]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,o)):"function"==typeof t&&"default"===o?t:void 0}}})},7457:(e,t,o)=>{o.d(t,{A:()=>i,X:()=>a});let s=require("arangojs"),n=()=>new s.Database({url:process.env.ARANGODB_URL||"http://localhost:8529",databaseName:process.env.ARANGODB_DB_NAME||"candid_connections",auth:{username:process.env.ARANGODB_USERNAME||"root",password:process.env.ARANGODB_PASSWORD||""}}),r=async e=>({jobSeekers:e.collection("jobSeekers"),companies:e.collection("companies"),hiringAuthorities:e.collection("hiringAuthorities"),positions:e.collection("positions"),skills:e.collection("skills"),matches:e.collection("matches"),works_for:e.collection("works_for"),employs:e.collection("employs"),posts:e.collection("posts"),requires:e.collection("requires"),has_skill:e.collection("has_skill"),matched_to:e.collection("matched_to"),reports_to:e.collection("reports_to")}),a=async()=>{let e=n();try{await e.createDatabase(process.env.ARANGODB_DB_NAME||"candid_connections")}catch(e){e.message.includes("duplicate name")||console.log("Database creation note:",e.message)}let t=await r(e);return{db:e,collections:t}},i=a},8448:(e,t,o)=>{o.r(t),o.d(t,{config:()=>u,default:()=>l,routeModule:()=>d});var s={};o.r(s),o.d(s,{default:()=>c});var n=o(3480),r=o(8667),a=o(6435),i=o(7457);async function c(e,t){try{let{db:o,collections:s}=await (0,i.A)(),{jobSeekers:n}=s;switch(e.method){case"GET":let r=await n.all(),a=await r.all();return t.status(200).json(a);case"POST":let c=e.body,l=await n.save(c);return t.status(201).json(l);default:return t.status(405).json({message:"Method not allowed"})}}catch(e){return console.error("Error handling job seekers:",e),t.status(500).json({message:"Internal server error",error:e.message})}}let l=(0,a.M)(s,"default"),u=(0,a.M)(s,"config"),d=new n.PagesAPIRouteModule({definition:{kind:r.A.PAGES_API,page:"/api/job-seekers",pathname:"/api/job-seekers",bundlePath:"",filename:""},userland:s})},8667:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return o}});var o=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})}};var t=require("../../webpack-api-runtime.js");t.C(e);var o=t(t.s=8448);module.exports=o})();