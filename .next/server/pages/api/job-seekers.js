"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/job-seekers";
exports.ids = ["pages/api/job-seekers"];
exports.modules = {

/***/ "(api-node)/./lib/db.js":
/*!*******************!*\
  !*** ./lib/db.js ***!
  \*******************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   initDb: () => (/* binding */ initDb)\n/* harmony export */ });\n/* harmony import */ var arangojs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! arangojs */ \"arangojs\");\n/* harmony import */ var arangojs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(arangojs__WEBPACK_IMPORTED_MODULE_0__);\n\n// Initialize the ArangoDB connection\nconst initDatabase = ()=>{\n    const db = new arangojs__WEBPACK_IMPORTED_MODULE_0__.Database({\n        url: process.env.ARANGODB_URL || 'http://localhost:8529',\n        databaseName: process.env.ARANGODB_DB_NAME || 'candid_connections',\n        auth: {\n            username: process.env.ARANGODB_USERNAME || 'root',\n            password: process.env.ARANGODB_PASSWORD || ''\n        }\n    });\n    return db;\n};\n// Get collections\nconst getCollections = async (db)=>{\n    return {\n        jobSeekers: db.collection('jobSeekers'),\n        companies: db.collection('companies'),\n        hiringAuthorities: db.collection('hiringAuthorities'),\n        positions: db.collection('positions'),\n        skills: db.collection('skills'),\n        matches: db.collection('matches'),\n        // Edge collections\n        works_for: db.edgeCollection('works_for'),\n        employs: db.edgeCollection('employs'),\n        posts: db.edgeCollection('posts'),\n        requires: db.edgeCollection('requires'),\n        has_skill: db.edgeCollection('has_skill'),\n        matched_to: db.edgeCollection('matched_to')\n    };\n};\n// Initialize database and collections\nconst initDb = async ()=>{\n    const db = initDatabase();\n    const collections = await getCollections(db);\n    return {\n        db,\n        collections\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (initDb);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./lib/db.js\n");

/***/ }),

/***/ "(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fjob-seekers&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fjob-seekers.js&middlewareConfigBase64=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fjob-seekers&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fjob-seekers.js&middlewareConfigBase64=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/./node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_job_seekers_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages/api/job-seekers.js */ \"(api-node)/./pages/api/job-seekers.js\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_job_seekers_js__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_job_seekers_js__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/job-seekers\",\n        pathname: \"/api/job-seekers\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_job_seekers_js__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaS1ub2RlKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtcm91dGUtbG9hZGVyL2luZGV4LmpzP2tpbmQ9UEFHRVNfQVBJJnBhZ2U9JTJGYXBpJTJGam9iLXNlZWtlcnMmcHJlZmVycmVkUmVnaW9uPSZhYnNvbHV0ZVBhZ2VQYXRoPS4lMkZwYWdlcyUyRmFwaSUyRmpvYi1zZWVrZXJzLmpzJm1pZGRsZXdhcmVDb25maWdCYXNlNjQ9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUErRjtBQUN2QztBQUNFO0FBQzFEO0FBQ3VEO0FBQ3ZEO0FBQ0EsaUVBQWUsd0VBQUssQ0FBQyxzREFBUSxZQUFZLEVBQUM7QUFDMUM7QUFDTyxlQUFlLHdFQUFLLENBQUMsc0RBQVE7QUFDcEM7QUFDTyx3QkFBd0IseUdBQW1CO0FBQ2xEO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLFlBQVk7QUFDWixDQUFDOztBQUVEIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUGFnZXNBUElSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvcGFnZXMtYXBpL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgaG9pc3QgfSBmcm9tIFwibmV4dC9kaXN0L2J1aWxkL3RlbXBsYXRlcy9oZWxwZXJzXCI7XG4vLyBJbXBvcnQgdGhlIHVzZXJsYW5kIGNvZGUuXG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiLi9wYWdlcy9hcGkvam9iLXNlZWtlcnMuanNcIjtcbi8vIFJlLWV4cG9ydCB0aGUgaGFuZGxlciAoc2hvdWxkIGJlIHRoZSBkZWZhdWx0IGV4cG9ydCkuXG5leHBvcnQgZGVmYXVsdCBob2lzdCh1c2VybGFuZCwgJ2RlZmF1bHQnKTtcbi8vIFJlLWV4cG9ydCBjb25maWcuXG5leHBvcnQgY29uc3QgY29uZmlnID0gaG9pc3QodXNlcmxhbmQsICdjb25maWcnKTtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IFBhZ2VzQVBJUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLlBBR0VTX0FQSSxcbiAgICAgICAgcGFnZTogXCIvYXBpL2pvYi1zZWVrZXJzXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvam9iLXNlZWtlcnNcIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiAnJyxcbiAgICAgICAgZmlsZW5hbWU6ICcnXG4gICAgfSxcbiAgICB1c2VybGFuZFxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBhZ2VzLWFwaS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fjob-seekers&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fjob-seekers.js&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/./pages/api/job-seekers.js":
/*!**********************************!*\
  !*** ./pages/api/job-seekers.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../lib/db */ \"(api-node)/./lib/db.js\");\n\nasync function handler(req, res) {\n    try {\n        const { db, collections } = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_0__[\"default\"])();\n        const { jobSeekers } = collections;\n        switch(req.method){\n            case 'GET':\n                const cursor = await jobSeekers.all();\n                const result = await cursor.all();\n                return res.status(200).json(result);\n            case 'POST':\n                const newJobSeeker = req.body;\n                const saved = await jobSeekers.save(newJobSeeker);\n                return res.status(201).json(saved);\n            default:\n                return res.status(405).json({\n                    message: 'Method not allowed'\n                });\n        }\n    } catch (error) {\n        console.error('Error handling job seekers:', error);\n        return res.status(500).json({\n            message: 'Internal server error',\n            error: error.message\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/job-seekers.js\n");

/***/ }),

/***/ "arangojs":
/*!***************************!*\
  !*** external "arangojs" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("arangojs");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fjob-seekers&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fjob-seekers.js&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();