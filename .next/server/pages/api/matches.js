"use strict";(()=>{var e={};e.id=770,e.ids=[770],e.modules={1603:(e,t,a)=>{a.r(t),a.d(t,{config:()=>p,default:()=>m,routeModule:()=>A});var o={};a.r(o),a.d(o,{default:()=>c});var r=a(3480),s=a(8667),n=a(6435),i=a(7457);async function c(e,t){let{method:a}=e;try{let{db:o,collections:r}=await (0,i.X)();switch(a){case"GET":await l(e,t,o,r);break;case"POST":await d(e,t,o,r);break;case"PUT":await u(e,t,o,r);break;case"DELETE":await h(e,t,o,r);break;default:t.setHeader("Allow",["GET","POST","PUT","DELETE"]),t.status(405).end(`Method ${a} Not Allowed`)}}catch(e){console.error("API Error:",e),t.status(500).json({error:"Internal Server Error"})}}async function l(e,t,a,o){let{status:r,jobSeekerId:s,positionId:n,limit:i=50,offset:c=0}=e.query;try{let e=`
      FOR match IN matches
        LET jobSeeker = DOCUMENT(match.jobSeekerId)
        LET position = DOCUMENT(match.positionId)
        LET company = DOCUMENT(position.companyId)
    `,o={limit:parseInt(i),offset:parseInt(c)},l=[];r&&(l.push("match.status == @status"),o.status=r),s&&(l.push("match.jobSeekerId == @jobSeekerId"),o.jobSeekerId=s),n&&(l.push("match.positionId == @positionId"),o.positionId=n),l.length>0&&(e+=` FILTER ${l.join(" AND ")}`),e+=`
        SORT match.score DESC, match.createdAt DESC
        LIMIT @offset, @limit
        RETURN {
          id: match._key,
          jobSeeker: {
            id: jobSeeker._key,
            name: jobSeeker.name,
            title: jobSeeker.title,
            skills: jobSeeker.skills || []
          },
          position: {
            id: position._key,
            title: position.title,
            company: company.name,
            requirements: position.requirements || []
          },
          score: match.score,
          status: match.status,
          createdAt: match.createdAt,
          matchReasons: match.matchReasons || []
        }
    `;let d=await a.query(e,o),u=await d.all();t.status(200).json(u)}catch(e){console.error("Error fetching matches:",e),t.status(500).json({error:"Failed to fetch matches"})}}async function d(e,t,a,o){let{jobSeekerId:r,positionId:s,score:n,matchReasons:i}=e.body;if(!r||!s||void 0===n)return t.status(400).json({error:"Missing required fields: jobSeekerId, positionId, score"});try{let e=`
      FOR match IN matches
        FILTER match.jobSeekerId == @jobSeekerId AND match.positionId == @positionId
        RETURN match
    `,c=await a.query(e,{jobSeekerId:r,positionId:s});if((await c.all()).length>0)return t.status(409).json({error:"Match already exists"});let l={jobSeekerId:r,positionId:s,score:n,status:"pending",matchReasons:i||[],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},d=await o.matches.save(l);await o.matched_to.save({_from:r,_to:s,matchId:d._id,score:n,createdAt:new Date().toISOString()}),t.status(201).json({id:d._key,...l})}catch(e){console.error("Error creating match:",e),t.status(500).json({error:"Failed to create match"})}}async function u(e,t,a,o){let{id:r}=e.query,{status:s,score:n,matchReasons:i}=e.body;if(!r)return t.status(400).json({error:"Match ID is required"});try{let e={updatedAt:new Date().toISOString()};s&&(e.status=s),void 0!==n&&(e.score=n),i&&(e.matchReasons=i);let a=await o.matches.update(r,e);if(!a._key)return t.status(404).json({error:"Match not found"});t.status(200).json({id:a._key,...e})}catch(e){console.error("Error updating match:",e),t.status(500).json({error:"Failed to update match"})}}async function h(e,t,a,o){let{id:r}=e.query;if(!r)return t.status(400).json({error:"Match ID is required"});try{await o.matches.document(r),await o.matches.remove(r);let e=`
      FOR edge IN matched_to
        FILTER edge.matchId == @matchId
        REMOVE edge IN matched_to
    `;await a.query(e,{matchId:`matches/${r}`}),t.status(200).json({message:"Match deleted successfully"})}catch(e){if(1202===e.errorNum)return t.status(404).json({error:"Match not found"});console.error("Error deleting match:",e),t.status(500).json({error:"Failed to delete match"})}}let m=(0,n.M)(o,"default"),p=(0,n.M)(o,"config"),A=new r.PagesAPIRouteModule({definition:{kind:s.A.PAGES_API,page:"/api/matches",pathname:"/api/matches",bundlePath:"",filename:""},userland:o})},3480:(e,t,a)=>{e.exports=a(5600)},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6435:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,a){return a in t?t[a]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,a)):"function"==typeof t&&"default"===a?t:void 0}}})},7457:(e,t,a)=>{a.d(t,{A:()=>i,X:()=>n});let o=require("arangojs"),r=()=>new o.Database({url:process.env.ARANGODB_URL||"http://localhost:8529",databaseName:process.env.ARANGODB_DB_NAME||"candid_connections",auth:{username:process.env.ARANGODB_USERNAME||"root",password:process.env.ARANGODB_PASSWORD||""}}),s=async e=>({jobSeekers:e.collection("jobSeekers"),companies:e.collection("companies"),hiringAuthorities:e.collection("hiringAuthorities"),positions:e.collection("positions"),skills:e.collection("skills"),matches:e.collection("matches"),works_for:e.edgeCollection("works_for"),employs:e.edgeCollection("employs"),posts:e.edgeCollection("posts"),requires:e.edgeCollection("requires"),has_skill:e.edgeCollection("has_skill"),matched_to:e.edgeCollection("matched_to")}),n=async()=>{let e=r(),t=await s(e);return{db:e,collections:t}},i=n},8667:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return a}});var a=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})}};var t=require("../../webpack-api-runtime.js");t.C(e);var a=t(t.s=1603);module.exports=a})();