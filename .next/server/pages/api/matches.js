"use strict";(()=>{var e={};e.id=770,e.ids=[770],e.modules={1603:(e,t,a)=>{a.r(t),a.d(t,{config:()=>p,default:()=>m,routeModule:()=>A});var o={};a.r(o),a.d(o,{default:()=>i});var s=a(3480),r=a(8667),n=a(6435),c=a(7457);async function i(e,t){let{method:a}=e;try{let{db:o,collections:s}=await (0,c.X)();switch(a){case"GET":await l(e,t,o,s);break;case"POST":await d(e,t,o,s);break;case"PUT":await u(e,t,o,s);break;case"DELETE":await h(e,t,o,s);break;default:t.setHeader("Allow",["GET","POST","PUT","DELETE"]),t.status(405).end(`Method ${a} Not Allowed`)}}catch(e){console.error("API Error:",e),t.status(500).json({error:"Internal Server Error"})}}async function l(e,t,a,o){let{status:s,jobSeekerId:r,positionId:n,limit:c=50,offset:i=0}=e.query;try{let e=`
      FOR match IN matches
        LET jobSeeker = DOCUMENT(match.jobSeekerId)
        LET position = DOCUMENT(match.positionId)
        LET company = DOCUMENT(position.companyId)
    `,o={limit:parseInt(c),offset:parseInt(i)},l=[];s&&(l.push("match.status == @status"),o.status=s),r&&(l.push("match.jobSeekerId == @jobSeekerId"),o.jobSeekerId=r),n&&(l.push("match.positionId == @positionId"),o.positionId=n),l.length>0&&(e+=` FILTER ${l.join(" AND ")}`),e+=`
        SORT match.score DESC, match.createdAt DESC
        LIMIT @offset, @limit
        RETURN {
          id: match._key,
          jobSeeker: {
            id: jobSeeker._key,
            name: jobSeeker.name,
            title: jobSeeker.title,
            skills: jobSeeker.skills || []
          },
          position: {
            id: position._key,
            title: position.title,
            company: company.name,
            requirements: position.requirements || []
          },
          score: match.score,
          status: match.status,
          createdAt: match.createdAt,
          matchReasons: match.matchReasons || []
        }
    `;let d=await a.query(e,o),u=await d.all();t.status(200).json(u)}catch(e){console.error("Error fetching matches:",e),t.status(500).json({error:"Failed to fetch matches"})}}async function d(e,t,a,o){let{jobSeekerId:s,positionId:r,score:n,matchReasons:c}=e.body;if(!s||!r||void 0===n)return t.status(400).json({error:"Missing required fields: jobSeekerId, positionId, score"});try{let e=`
      FOR match IN matches
        FILTER match.jobSeekerId == @jobSeekerId AND match.positionId == @positionId
        RETURN match
    `,i=await a.query(e,{jobSeekerId:s,positionId:r});if((await i.all()).length>0)return t.status(409).json({error:"Match already exists"});let l={jobSeekerId:s,positionId:r,score:n,status:"pending",matchReasons:c||[],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},d=await o.matches.save(l);await o.matched_to.save({_from:s,_to:r,matchId:d._id,score:n,createdAt:new Date().toISOString()}),t.status(201).json({id:d._key,...l})}catch(e){console.error("Error creating match:",e),t.status(500).json({error:"Failed to create match"})}}async function u(e,t,a,o){let{id:s}=e.query,{status:r,score:n,matchReasons:c}=e.body;if(!s)return t.status(400).json({error:"Match ID is required"});try{let e={updatedAt:new Date().toISOString()};r&&(e.status=r),void 0!==n&&(e.score=n),c&&(e.matchReasons=c);let a=await o.matches.update(s,e);if(!a._key)return t.status(404).json({error:"Match not found"});t.status(200).json({id:a._key,...e})}catch(e){console.error("Error updating match:",e),t.status(500).json({error:"Failed to update match"})}}async function h(e,t,a,o){let{id:s}=e.query;if(!s)return t.status(400).json({error:"Match ID is required"});try{await o.matches.document(s),await o.matches.remove(s);let e=`
      FOR edge IN matched_to
        FILTER edge.matchId == @matchId
        REMOVE edge IN matched_to
    `;await a.query(e,{matchId:`matches/${s}`}),t.status(200).json({message:"Match deleted successfully"})}catch(e){if(1202===e.errorNum)return t.status(404).json({error:"Match not found"});console.error("Error deleting match:",e),t.status(500).json({error:"Failed to delete match"})}}let m=(0,n.M)(o,"default"),p=(0,n.M)(o,"config"),A=new s.PagesAPIRouteModule({definition:{kind:r.A.PAGES_API,page:"/api/matches",pathname:"/api/matches",bundlePath:"",filename:""},userland:o})},3480:(e,t,a)=>{e.exports=a(5600)},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6435:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,a){return a in t?t[a]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,a)):"function"==typeof t&&"default"===a?t:void 0}}})},7457:(e,t,a)=>{a.d(t,{A:()=>n,X:()=>r});let o=require("arangojs"),s=async e=>({jobSeekers:e.collection("jobSeekers"),companies:e.collection("companies"),hiringAuthorities:e.collection("hiringAuthorities"),positions:e.collection("positions"),skills:e.collection("skills"),matches:e.collection("matches"),works_for:e.collection("works_for"),employs:e.collection("employs"),posts:e.collection("posts"),requires:e.collection("requires"),has_skill:e.collection("has_skill"),matched_to:e.collection("matched_to"),reports_to:e.collection("reports_to")}),r=async()=>{let e=process.env.ARANGODB_DB_NAME||"candid_connections",t=new o.Database({url:process.env.ARANGODB_URL||"http://localhost:8529",databaseName:"_system",auth:{username:process.env.ARANGODB_USERNAME||"root",password:process.env.ARANGODB_PASSWORD||""}});try{await t.createDatabase(e),console.log(`✅ Created database: ${e}`)}catch(t){1207===t.errorNum?console.log(`✅ Database ${e} already exists`):console.log("Database creation note:",t.message)}let a=new o.Database({url:process.env.ARANGODB_URL||"http://localhost:8529",databaseName:e,auth:{username:process.env.ARANGODB_USERNAME||"root",password:process.env.ARANGODB_PASSWORD||""}}),r=await s(a);return{db:a,collections:r}},n=r},8667:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return a}});var a=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})}};var t=require("../../webpack-api-runtime.js");t.C(e);var a=t(t.s=1603);module.exports=a})();