"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/positions";
exports.ids = ["pages/api/positions"];
exports.modules = {

/***/ "(api-node)/./lib/db.js":
/*!*******************!*\
  !*** ./lib/db.js ***!
  \*******************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   initDb: () => (/* binding */ initDb)\n/* harmony export */ });\n/* harmony import */ var arangojs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! arangojs */ \"arangojs\");\n/* harmony import */ var arangojs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(arangojs__WEBPACK_IMPORTED_MODULE_0__);\n\n// Get collections\nconst getCollections = async (db)=>{\n    return {\n        jobSeekers: db.collection('jobSeekers'),\n        companies: db.collection('companies'),\n        hiringAuthorities: db.collection('hiringAuthorities'),\n        positions: db.collection('positions'),\n        skills: db.collection('skills'),\n        matches: db.collection('matches'),\n        // Edge collections - using regular collection method for now\n        works_for: db.collection('works_for'),\n        employs: db.collection('employs'),\n        posts: db.collection('posts'),\n        requires: db.collection('requires'),\n        has_skill: db.collection('has_skill'),\n        matched_to: db.collection('matched_to'),\n        reports_to: db.collection('reports_to')\n    };\n};\n// Initialize database and collections\nconst initDb = async ()=>{\n    const dbName = process.env.ARANGODB_DB_NAME || 'candid_connections';\n    // First connect to _system database to create our database\n    const systemDb = new arangojs__WEBPACK_IMPORTED_MODULE_0__.Database({\n        url: process.env.ARANGODB_URL || 'http://localhost:8529',\n        databaseName: '_system',\n        auth: {\n            username: process.env.ARANGODB_USERNAME || 'root',\n            password: process.env.ARANGODB_PASSWORD || ''\n        }\n    });\n    // Ensure our database exists\n    try {\n        await systemDb.createDatabase(dbName);\n        console.log(`✅ Created database: ${dbName}`);\n    } catch (error) {\n        if (error.errorNum === 1207) {\n            console.log(`✅ Database ${dbName} already exists`);\n        } else {\n            console.log('Database creation note:', error.message);\n        }\n    }\n    // Now connect to our specific database\n    const db = new arangojs__WEBPACK_IMPORTED_MODULE_0__.Database({\n        url: process.env.ARANGODB_URL || 'http://localhost:8529',\n        databaseName: dbName,\n        auth: {\n            username: process.env.ARANGODB_USERNAME || 'root',\n            password: process.env.ARANGODB_PASSWORD || ''\n        }\n    });\n    const collections = await getCollections(db);\n    return {\n        db,\n        collections\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (initDb);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./lib/db.js\n");

/***/ }),

/***/ "(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpositions&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fpositions.js&middlewareConfigBase64=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpositions&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fpositions.js&middlewareConfigBase64=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/./node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_positions_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages/api/positions.js */ \"(api-node)/./pages/api/positions.js\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_positions_js__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_positions_js__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/positions\",\n        pathname: \"/api/positions\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_positions_js__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaS1ub2RlKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtcm91dGUtbG9hZGVyL2luZGV4LmpzP2tpbmQ9UEFHRVNfQVBJJnBhZ2U9JTJGYXBpJTJGcG9zaXRpb25zJnByZWZlcnJlZFJlZ2lvbj0mYWJzb2x1dGVQYWdlUGF0aD0uJTJGcGFnZXMlMkZhcGklMkZwb3NpdGlvbnMuanMmbWlkZGxld2FyZUNvbmZpZ0Jhc2U2ND1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQStGO0FBQ3ZDO0FBQ0U7QUFDMUQ7QUFDcUQ7QUFDckQ7QUFDQSxpRUFBZSx3RUFBSyxDQUFDLG9EQUFRLFlBQVksRUFBQztBQUMxQztBQUNPLGVBQWUsd0VBQUssQ0FBQyxvREFBUTtBQUNwQztBQUNPLHdCQUF3Qix5R0FBbUI7QUFDbEQ7QUFDQSxjQUFjLGtFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsWUFBWTtBQUNaLENBQUM7O0FBRUQiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQYWdlc0FQSVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUtbW9kdWxlcy9wYWdlcy1hcGkvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBob2lzdCB9IGZyb20gXCJuZXh0L2Rpc3QvYnVpbGQvdGVtcGxhdGVzL2hlbHBlcnNcIjtcbi8vIEltcG9ydCB0aGUgdXNlcmxhbmQgY29kZS5cbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCIuL3BhZ2VzL2FwaS9wb3NpdGlvbnMuanNcIjtcbi8vIFJlLWV4cG9ydCB0aGUgaGFuZGxlciAoc2hvdWxkIGJlIHRoZSBkZWZhdWx0IGV4cG9ydCkuXG5leHBvcnQgZGVmYXVsdCBob2lzdCh1c2VybGFuZCwgJ2RlZmF1bHQnKTtcbi8vIFJlLWV4cG9ydCBjb25maWcuXG5leHBvcnQgY29uc3QgY29uZmlnID0gaG9pc3QodXNlcmxhbmQsICdjb25maWcnKTtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IFBhZ2VzQVBJUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLlBBR0VTX0FQSSxcbiAgICAgICAgcGFnZTogXCIvYXBpL3Bvc2l0aW9uc1wiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL3Bvc2l0aW9uc1wiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6ICcnLFxuICAgICAgICBmaWxlbmFtZTogJydcbiAgICB9LFxuICAgIHVzZXJsYW5kXG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cGFnZXMtYXBpLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpositions&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fpositions.js&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/./pages/api/positions.js":
/*!********************************!*\
  !*** ./pages/api/positions.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../lib/db */ \"(api-node)/./lib/db.js\");\n\nasync function handler(req, res) {\n    const { method } = req;\n    try {\n        const { db, collections } = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_0__.initDb)();\n        switch(method){\n            case 'GET':\n                await handleGet(req, res, db, collections);\n                break;\n            case 'POST':\n                await handlePost(req, res, db, collections);\n                break;\n            case 'PUT':\n                await handlePut(req, res, db, collections);\n                break;\n            case 'DELETE':\n                await handleDelete(req, res, db, collections);\n                break;\n            default:\n                res.setHeader('Allow', [\n                    'GET',\n                    'POST',\n                    'PUT',\n                    'DELETE'\n                ]);\n                res.status(405).end(`Method ${method} Not Allowed`);\n        }\n    } catch (error) {\n        console.error('API Error:', error);\n        res.status(500).json({\n            error: 'Internal Server Error'\n        });\n    }\n}\nasync function handleGet(req, res, db, collections) {\n    const { id, companyId, level, type, status, remote, location, limit = 50, offset = 0 } = req.query;\n    try {\n        if (id) {\n            // Get single position with related data\n            const query = `\n        LET position = DOCUMENT('positions', @id)\n        LET company = DOCUMENT(position.companyId)\n        LET requirements = (\n          FOR req IN requires\n            FILTER req._from == position._id\n            LET skill = DOCUMENT(req._to)\n            RETURN skill.name\n        )\n        LET matches = (\n          FOR match IN matches\n            FILTER match.positionId == position._id\n            RETURN {\n              id: match._key,\n              jobSeekerId: match.jobSeekerId,\n              score: match.score,\n              status: match.status\n            }\n        )\n        RETURN {\n          id: position._key,\n          title: position.title,\n          company: {\n            id: company._key,\n            name: company.name,\n            logo: company.logo\n          },\n          level: position.level,\n          type: position.type,\n          location: position.location,\n          remote: position.remote,\n          salary: position.salary,\n          description: position.description,\n          requirements: requirements,\n          benefits: position.benefits || [],\n          status: position.status,\n          postedDate: position.postedDate,\n          applicants: position.applicants || 0,\n          matches: matches\n        }\n      `;\n            const cursor = await db.query(query, {\n                id\n            });\n            const result = await cursor.all();\n            if (result.length === 0) {\n                return res.status(404).json({\n                    error: 'Position not found'\n                });\n            }\n            res.status(200).json(result[0]);\n        } else {\n            // Get list of positions with filters\n            let query = `\n        FOR position IN positions\n          LET company = DOCUMENT(position.companyId)\n      `;\n            const bindVars = {\n                limit: parseInt(limit),\n                offset: parseInt(offset)\n            };\n            const filters = [];\n            if (companyId) {\n                filters.push('position.companyId == @companyId');\n                bindVars.companyId = companyId;\n            }\n            if (level) {\n                filters.push('position.level == @level');\n                bindVars.level = level;\n            }\n            if (type) {\n                filters.push('position.type == @type');\n                bindVars.type = type;\n            }\n            if (status) {\n                filters.push('position.status == @status');\n                bindVars.status = status;\n            }\n            if (remote !== undefined) {\n                filters.push('position.remote == @remote');\n                bindVars.remote = remote === 'true';\n            }\n            if (location) {\n                filters.push('CONTAINS(LOWER(position.location), LOWER(@location))');\n                bindVars.location = location;\n            }\n            if (filters.length > 0) {\n                query += ` FILTER ${filters.join(' AND ')}`;\n            }\n            query += `\n        LET requirements = (\n          FOR req IN requires\n            FILTER req._from == position._id\n            LET skill = DOCUMENT(req._to)\n            RETURN skill.name\n        )\n        SORT position.postedDate DESC\n        LIMIT @offset, @limit\n        RETURN {\n          id: position._key,\n          title: position.title,\n          company: {\n            id: company._key,\n            name: company.name,\n            logo: company.logo\n          },\n          level: position.level,\n          type: position.type,\n          location: position.location,\n          remote: position.remote,\n          salary: position.salary,\n          description: position.description,\n          requirements: requirements,\n          benefits: position.benefits || [],\n          status: position.status,\n          postedDate: position.postedDate,\n          applicants: position.applicants || 0\n        }\n      `;\n            const cursor = await db.query(query, bindVars);\n            const positions = await cursor.all();\n            res.status(200).json(positions);\n        }\n    } catch (error) {\n        console.error('Error fetching positions:', error);\n        res.status(500).json({\n            error: 'Failed to fetch positions'\n        });\n    }\n}\nasync function handlePost(req, res, db, collections) {\n    const { title, companyId, level, type, location, remote, salary, description, requirements, benefits } = req.body;\n    if (!title || !companyId || !level || !type || !location) {\n        return res.status(400).json({\n            error: 'Missing required fields: title, companyId, level, type, location'\n        });\n    }\n    try {\n        // Verify company exists\n        try {\n            await collections.companies.document(companyId);\n        } catch (error) {\n            return res.status(400).json({\n                error: 'Invalid company ID'\n            });\n        }\n        const positionData = {\n            title,\n            companyId: `companies/${companyId}`,\n            level,\n            type,\n            location,\n            remote: remote || false,\n            salary: salary || '',\n            description: description || '',\n            benefits: benefits || [],\n            status: 'active',\n            postedDate: new Date().toISOString(),\n            applicants: 0,\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n        const result = await collections.positions.save(positionData);\n        // Create skill requirement relationships\n        if (requirements && requirements.length > 0) {\n            for (const skillName of requirements){\n                // Find or create skill\n                let skillQuery = `\n          FOR skill IN skills\n            FILTER LOWER(skill.name) == LOWER(@skillName)\n            RETURN skill\n        `;\n                let skillCursor = await db.query(skillQuery, {\n                    skillName\n                });\n                let skills = await skillCursor.all();\n                let skillId;\n                if (skills.length === 0) {\n                    // Create new skill\n                    const skillResult = await collections.skills.save({\n                        name: skillName,\n                        category: 'General',\n                        createdAt: new Date().toISOString()\n                    });\n                    skillId = skillResult._id;\n                } else {\n                    skillId = skills[0]._id;\n                }\n                // Create requirement relationship\n                await collections.requires.save({\n                    _from: result._id,\n                    _to: skillId,\n                    createdAt: new Date().toISOString()\n                });\n            }\n        }\n        res.status(201).json({\n            id: result._key,\n            ...positionData,\n            requirements: requirements || []\n        });\n    } catch (error) {\n        console.error('Error creating position:', error);\n        res.status(500).json({\n            error: 'Failed to create position'\n        });\n    }\n}\nasync function handlePut(req, res, db, collections) {\n    const { id } = req.query;\n    const updateFields = req.body;\n    if (!id) {\n        return res.status(400).json({\n            error: 'Position ID is required'\n        });\n    }\n    try {\n        const updateData = {\n            ...updateFields,\n            updatedAt: new Date().toISOString()\n        };\n        // Remove fields that shouldn't be updated\n        delete updateData.id;\n        delete updateData.createdAt;\n        delete updateData.postedDate;\n        // Handle requirements update separately\n        const requirements = updateData.requirements;\n        delete updateData.requirements;\n        const result = await collections.positions.update(id, updateData);\n        if (!result._key) {\n            return res.status(404).json({\n                error: 'Position not found'\n            });\n        }\n        // Update requirements if provided\n        if (requirements) {\n            const positionId = `positions/${id}`;\n            // Remove existing requirements\n            await db.query(`\n        FOR req IN requires\n          FILTER req._from == @positionId\n          REMOVE req IN requires\n      `, {\n                positionId\n            });\n            // Add new requirements\n            for (const skillName of requirements){\n                let skillQuery = `\n          FOR skill IN skills\n            FILTER LOWER(skill.name) == LOWER(@skillName)\n            RETURN skill\n        `;\n                let skillCursor = await db.query(skillQuery, {\n                    skillName\n                });\n                let skills = await skillCursor.all();\n                let skillId;\n                if (skills.length === 0) {\n                    const skillResult = await collections.skills.save({\n                        name: skillName,\n                        category: 'General',\n                        createdAt: new Date().toISOString()\n                    });\n                    skillId = skillResult._id;\n                } else {\n                    skillId = skills[0]._id;\n                }\n                await collections.requires.save({\n                    _from: positionId,\n                    _to: skillId,\n                    createdAt: new Date().toISOString()\n                });\n            }\n        }\n        res.status(200).json({\n            id: result._key,\n            ...updateData,\n            requirements: requirements || []\n        });\n    } catch (error) {\n        console.error('Error updating position:', error);\n        res.status(500).json({\n            error: 'Failed to update position'\n        });\n    }\n}\nasync function handleDelete(req, res, db, collections) {\n    const { id } = req.query;\n    if (!id) {\n        return res.status(400).json({\n            error: 'Position ID is required'\n        });\n    }\n    try {\n        // Check if position has active matches\n        const matchesQuery = `\n      FOR match IN matches\n        FILTER match.positionId == @positionId AND match.status == 'pending'\n        RETURN match\n    `;\n        const matchesCursor = await db.query(matchesQuery, {\n            positionId: `positions/${id}`\n        });\n        const activeMatches = await matchesCursor.all();\n        if (activeMatches.length > 0) {\n            return res.status(400).json({\n                error: 'Cannot delete position with pending matches. Please resolve matches first.'\n            });\n        }\n        // Delete the position\n        await collections.positions.remove(id);\n        // Clean up related data\n        const positionId = `positions/${id}`;\n        const cleanupQueries = [\n            // Delete skill requirements\n            `\n        FOR req IN requires\n          FILTER req._from == @positionId\n          REMOVE req IN requires\n      `,\n            // Delete posting relationships\n            `\n        FOR edge IN posts\n          FILTER edge._to == @positionId\n          REMOVE edge IN posts\n      `,\n            // Delete completed matches\n            `\n        FOR match IN matches\n          FILTER match.positionId == @positionId\n          REMOVE match IN matches\n      `\n        ];\n        for (const query of cleanupQueries){\n            await db.query(query, {\n                positionId\n            });\n        }\n        res.status(200).json({\n            message: 'Position deleted successfully'\n        });\n    } catch (error) {\n        if (error.errorNum === 1202) {\n            return res.status(404).json({\n                error: 'Position not found'\n            });\n        }\n        console.error('Error deleting position:', error);\n        res.status(500).json({\n            error: 'Failed to delete position'\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/positions.js\n");

/***/ }),

/***/ "arangojs":
/*!***************************!*\
  !*** external "arangojs" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("arangojs");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpositions&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fpositions.js&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();