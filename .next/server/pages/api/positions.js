"use strict";(()=>{var e={};e.id=257,e.ids=[257],e.modules={3480:(e,t,o)=>{e.exports=o(5600)},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6435:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,o){return o in t?t[o]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,o)):"function"==typeof t&&"default"===o?t:void 0}}})},7181:(e,t,o)=>{o.r(t),o.d(t,{config:()=>E,default:()=>m,routeModule:()=>y});var i={};o.r(i),o.d(i,{default:()=>l});var s=o(3480),n=o(8667),a=o(6435),r=o(7457);async function l(e,t){let{method:o}=e;try{let{db:i,collections:s}=await (0,r.X)();switch(o){case"GET":await c(e,t,i,s);break;case"POST":await p(e,t,i,s);break;case"PUT":await u(e,t,i,s);break;case"DELETE":await d(e,t,i,s);break;default:t.setHeader("Allow",["GET","POST","PUT","DELETE"]),t.status(405).end(`Method ${o} Not Allowed`)}}catch(e){console.error("API Error:",e),t.status(500).json({error:"Internal Server Error"})}}async function c(e,t,o,i){let{id:s,companyId:n,level:a,type:r,status:l,remote:c,location:p,limit:u=50,offset:d=0}=e.query;try{if(s){let e=`
        LET position = DOCUMENT('positions', @id)
        LET company = DOCUMENT(position.companyId)
        LET requirements = (
          FOR req IN requires
            FILTER req._from == position._id
            LET skill = DOCUMENT(req._to)
            RETURN skill.name
        )
        LET matches = (
          FOR match IN matches
            FILTER match.positionId == position._id
            RETURN {
              id: match._key,
              jobSeekerId: match.jobSeekerId,
              score: match.score,
              status: match.status
            }
        )
        RETURN {
          id: position._key,
          title: position.title,
          company: {
            id: company._key,
            name: company.name,
            logo: company.logo
          },
          level: position.level,
          type: position.type,
          location: position.location,
          remote: position.remote,
          salary: position.salary,
          description: position.description,
          requirements: requirements,
          benefits: position.benefits || [],
          status: position.status,
          postedDate: position.postedDate,
          applicants: position.applicants || 0,
          matches: matches
        }
      `,i=await o.query(e,{id:s}),n=await i.all();if(0===n.length)return t.status(404).json({error:"Position not found"});t.status(200).json(n[0])}else{let e=`
        FOR position IN positions
          LET company = DOCUMENT(position.companyId)
      `,i={limit:parseInt(u),offset:parseInt(d)},s=[];n&&(s.push("position.companyId == @companyId"),i.companyId=n),a&&(s.push("position.level == @level"),i.level=a),r&&(s.push("position.type == @type"),i.type=r),l&&(s.push("position.status == @status"),i.status=l),void 0!==c&&(s.push("position.remote == @remote"),i.remote="true"===c),p&&(s.push("CONTAINS(LOWER(position.location), LOWER(@location))"),i.location=p),s.length>0&&(e+=` FILTER ${s.join(" AND ")}`),e+=`
        LET requirements = (
          FOR req IN requires
            FILTER req._from == position._id
            LET skill = DOCUMENT(req._to)
            RETURN skill.name
        )
        SORT position.postedDate DESC
        LIMIT @offset, @limit
        RETURN {
          id: position._key,
          title: position.title,
          company: {
            id: company._key,
            name: company.name,
            logo: company.logo
          },
          level: position.level,
          type: position.type,
          location: position.location,
          remote: position.remote,
          salary: position.salary,
          description: position.description,
          requirements: requirements,
          benefits: position.benefits || [],
          status: position.status,
          postedDate: position.postedDate,
          applicants: position.applicants || 0
        }
      `;let m=await o.query(e,i),E=await m.all();t.status(200).json(E)}}catch(e){console.error("Error fetching positions:",e),t.status(500).json({error:"Failed to fetch positions"})}}async function p(e,t,o,i){let{title:s,companyId:n,level:a,type:r,location:l,remote:c,salary:p,description:u,requirements:d,benefits:m}=e.body;if(!s||!n||!a||!r||!l)return t.status(400).json({error:"Missing required fields: title, companyId, level, type, location"});try{try{await i.companies.document(n)}catch(e){return t.status(400).json({error:"Invalid company ID"})}let e={title:s,companyId:`companies/${n}`,level:a,type:r,location:l,remote:c||!1,salary:p||"",description:u||"",benefits:m||[],status:"active",postedDate:new Date().toISOString(),applicants:0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},E=await i.positions.save(e);if(d&&d.length>0)for(let e of d){let t,s=`
          FOR skill IN skills
            FILTER LOWER(skill.name) == LOWER(@skillName)
            RETURN skill
        `,n=await o.query(s,{skillName:e}),a=await n.all();t=0===a.length?(await i.skills.save({name:e,category:"General",createdAt:new Date().toISOString()}))._id:a[0]._id,await i.requires.save({_from:E._id,_to:t,createdAt:new Date().toISOString()})}t.status(201).json({id:E._key,...e,requirements:d||[]})}catch(e){console.error("Error creating position:",e),t.status(500).json({error:"Failed to create position"})}}async function u(e,t,o,i){let{id:s}=e.query,n=e.body;if(!s)return t.status(400).json({error:"Position ID is required"});try{let e={...n,updatedAt:new Date().toISOString()};delete e.id,delete e.createdAt,delete e.postedDate;let a=e.requirements;delete e.requirements;let r=await i.positions.update(s,e);if(!r._key)return t.status(404).json({error:"Position not found"});if(a){let e=`positions/${s}`;for(let t of(await o.query(`
        FOR req IN requires
          FILTER req._from == @positionId
          REMOVE req IN requires
      `,{positionId:e}),a)){let s,n=`
          FOR skill IN skills
            FILTER LOWER(skill.name) == LOWER(@skillName)
            RETURN skill
        `,a=await o.query(n,{skillName:t}),r=await a.all();s=0===r.length?(await i.skills.save({name:t,category:"General",createdAt:new Date().toISOString()}))._id:r[0]._id,await i.requires.save({_from:e,_to:s,createdAt:new Date().toISOString()})}}t.status(200).json({id:r._key,...e,requirements:a||[]})}catch(e){console.error("Error updating position:",e),t.status(500).json({error:"Failed to update position"})}}async function d(e,t,o,i){let{id:s}=e.query;if(!s)return t.status(400).json({error:"Position ID is required"});try{let e=`
      FOR match IN matches
        FILTER match.positionId == @positionId AND match.status == 'pending'
        RETURN match
    `,n=await o.query(e,{positionId:`positions/${s}`});if((await n.all()).length>0)return t.status(400).json({error:"Cannot delete position with pending matches. Please resolve matches first."});await i.positions.remove(s);let a=`positions/${s}`;for(let e of[`
        FOR req IN requires
          FILTER req._from == @positionId
          REMOVE req IN requires
      `,`
        FOR edge IN posts
          FILTER edge._to == @positionId
          REMOVE edge IN posts
      `,`
        FOR match IN matches
          FILTER match.positionId == @positionId
          REMOVE match IN matches
      `])await o.query(e,{positionId:a});t.status(200).json({message:"Position deleted successfully"})}catch(e){if(1202===e.errorNum)return t.status(404).json({error:"Position not found"});console.error("Error deleting position:",e),t.status(500).json({error:"Failed to delete position"})}}let m=(0,a.M)(i,"default"),E=(0,a.M)(i,"config"),y=new s.PagesAPIRouteModule({definition:{kind:n.A.PAGES_API,page:"/api/positions",pathname:"/api/positions",bundlePath:"",filename:""},userland:i})},7457:(e,t,o)=>{o.d(t,{A:()=>r,X:()=>a});let i=require("arangojs"),s=()=>new i.Database({url:process.env.ARANGODB_URL||"http://localhost:8529",databaseName:process.env.ARANGODB_DB_NAME||"candid_connections",auth:{username:process.env.ARANGODB_USERNAME||"root",password:process.env.ARANGODB_PASSWORD||""}}),n=async e=>({jobSeekers:e.collection("jobSeekers"),companies:e.collection("companies"),hiringAuthorities:e.collection("hiringAuthorities"),positions:e.collection("positions"),skills:e.collection("skills"),matches:e.collection("matches"),works_for:e.collection("works_for"),employs:e.collection("employs"),posts:e.collection("posts"),requires:e.collection("requires"),has_skill:e.collection("has_skill"),matched_to:e.collection("matched_to"),reports_to:e.collection("reports_to")}),a=async()=>{let e=s();try{await e.createDatabase(process.env.ARANGODB_DB_NAME||"candid_connections")}catch(e){e.message.includes("duplicate name")||console.log("Database creation note:",e.message)}let t=await n(e);return{db:e,collections:t}},r=a},8667:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return o}});var o=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})}};var t=require("../../webpack-api-runtime.js");t.C(e);var o=t(t.s=7181);module.exports=o})();