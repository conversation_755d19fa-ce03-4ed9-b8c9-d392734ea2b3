"use strict";(()=>{var e={};e.id=257,e.ids=[257],e.modules={3480:(e,t,o)=>{e.exports=o(5600)},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6435:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,o){return o in t?t[o]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,o)):"function"==typeof t&&"default"===o?t:void 0}}})},7181:(e,t,o)=>{o.r(t),o.d(t,{config:()=>E,default:()=>m,routeModule:()=>y});var s={};o.r(s),o.d(s,{default:()=>l});var i=o(3480),a=o(8667),n=o(6435),r=o(7457);async function l(e,t){let{method:o}=e;try{let{db:s,collections:i}=await (0,r.X)();switch(o){case"GET":await c(e,t,s,i);break;case"POST":await p(e,t,s,i);break;case"PUT":await u(e,t,s,i);break;case"DELETE":await d(e,t,s,i);break;default:t.setHeader("Allow",["GET","POST","PUT","DELETE"]),t.status(405).end(`Method ${o} Not Allowed`)}}catch(e){console.error("API Error:",e),t.status(500).json({error:"Internal Server Error"})}}async function c(e,t,o,s){let{id:i,companyId:a,level:n,type:r,status:l,remote:c,location:p,limit:u=50,offset:d=0}=e.query;try{if(i){let e=`
        LET position = DOCUMENT('positions', @id)
        LET company = DOCUMENT(position.companyId)
        LET requirements = (
          FOR req IN requires
            FILTER req._from == position._id
            LET skill = DOCUMENT(req._to)
            RETURN skill.name
        )
        LET matches = (
          FOR match IN matches
            FILTER match.positionId == position._id
            RETURN {
              id: match._key,
              jobSeekerId: match.jobSeekerId,
              score: match.score,
              status: match.status
            }
        )
        RETURN {
          id: position._key,
          title: position.title,
          company: {
            id: company._key,
            name: company.name,
            logo: company.logo
          },
          level: position.level,
          type: position.type,
          location: position.location,
          remote: position.remote,
          salary: position.salary,
          description: position.description,
          requirements: requirements,
          benefits: position.benefits || [],
          status: position.status,
          postedDate: position.postedDate,
          applicants: position.applicants || 0,
          matches: matches
        }
      `,s=await o.query(e,{id:i}),a=await s.all();if(0===a.length)return t.status(404).json({error:"Position not found"});t.status(200).json(a[0])}else{let e=`
        FOR position IN positions
          LET company = DOCUMENT(position.companyId)
      `,s={limit:parseInt(u),offset:parseInt(d)},i=[];a&&(i.push("position.companyId == @companyId"),s.companyId=a),n&&(i.push("position.level == @level"),s.level=n),r&&(i.push("position.type == @type"),s.type=r),l&&(i.push("position.status == @status"),s.status=l),void 0!==c&&(i.push("position.remote == @remote"),s.remote="true"===c),p&&(i.push("CONTAINS(LOWER(position.location), LOWER(@location))"),s.location=p),i.length>0&&(e+=` FILTER ${i.join(" AND ")}`),e+=`
        LET requirements = (
          FOR req IN requires
            FILTER req._from == position._id
            LET skill = DOCUMENT(req._to)
            RETURN skill.name
        )
        SORT position.postedDate DESC
        LIMIT @offset, @limit
        RETURN {
          id: position._key,
          title: position.title,
          company: {
            id: company._key,
            name: company.name,
            logo: company.logo
          },
          level: position.level,
          type: position.type,
          location: position.location,
          remote: position.remote,
          salary: position.salary,
          description: position.description,
          requirements: requirements,
          benefits: position.benefits || [],
          status: position.status,
          postedDate: position.postedDate,
          applicants: position.applicants || 0
        }
      `;let m=await o.query(e,s),E=await m.all();t.status(200).json(E)}}catch(e){console.error("Error fetching positions:",e),t.status(500).json({error:"Failed to fetch positions"})}}async function p(e,t,o,s){let{title:i,companyId:a,level:n,type:r,location:l,remote:c,salary:p,description:u,requirements:d,benefits:m}=e.body;if(!i||!a||!n||!r||!l)return t.status(400).json({error:"Missing required fields: title, companyId, level, type, location"});try{try{await s.companies.document(a)}catch(e){return t.status(400).json({error:"Invalid company ID"})}let e={title:i,companyId:`companies/${a}`,level:n,type:r,location:l,remote:c||!1,salary:p||"",description:u||"",benefits:m||[],status:"active",postedDate:new Date().toISOString(),applicants:0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},E=await s.positions.save(e);if(d&&d.length>0)for(let e of d){let t,i=`
          FOR skill IN skills
            FILTER LOWER(skill.name) == LOWER(@skillName)
            RETURN skill
        `,a=await o.query(i,{skillName:e}),n=await a.all();t=0===n.length?(await s.skills.save({name:e,category:"General",createdAt:new Date().toISOString()}))._id:n[0]._id,await s.requires.save({_from:E._id,_to:t,createdAt:new Date().toISOString()})}t.status(201).json({id:E._key,...e,requirements:d||[]})}catch(e){console.error("Error creating position:",e),t.status(500).json({error:"Failed to create position"})}}async function u(e,t,o,s){let{id:i}=e.query,a=e.body;if(!i)return t.status(400).json({error:"Position ID is required"});try{let e={...a,updatedAt:new Date().toISOString()};delete e.id,delete e.createdAt,delete e.postedDate;let n=e.requirements;delete e.requirements;let r=await s.positions.update(i,e);if(!r._key)return t.status(404).json({error:"Position not found"});if(n){let e=`positions/${i}`;for(let t of(await o.query(`
        FOR req IN requires
          FILTER req._from == @positionId
          REMOVE req IN requires
      `,{positionId:e}),n)){let i,a=`
          FOR skill IN skills
            FILTER LOWER(skill.name) == LOWER(@skillName)
            RETURN skill
        `,n=await o.query(a,{skillName:t}),r=await n.all();i=0===r.length?(await s.skills.save({name:t,category:"General",createdAt:new Date().toISOString()}))._id:r[0]._id,await s.requires.save({_from:e,_to:i,createdAt:new Date().toISOString()})}}t.status(200).json({id:r._key,...e,requirements:n||[]})}catch(e){console.error("Error updating position:",e),t.status(500).json({error:"Failed to update position"})}}async function d(e,t,o,s){let{id:i}=e.query;if(!i)return t.status(400).json({error:"Position ID is required"});try{let e=`
      FOR match IN matches
        FILTER match.positionId == @positionId AND match.status == 'pending'
        RETURN match
    `,a=await o.query(e,{positionId:`positions/${i}`});if((await a.all()).length>0)return t.status(400).json({error:"Cannot delete position with pending matches. Please resolve matches first."});await s.positions.remove(i);let n=`positions/${i}`;for(let e of[`
        FOR req IN requires
          FILTER req._from == @positionId
          REMOVE req IN requires
      `,`
        FOR edge IN posts
          FILTER edge._to == @positionId
          REMOVE edge IN posts
      `,`
        FOR match IN matches
          FILTER match.positionId == @positionId
          REMOVE match IN matches
      `])await o.query(e,{positionId:n});t.status(200).json({message:"Position deleted successfully"})}catch(e){if(1202===e.errorNum)return t.status(404).json({error:"Position not found"});console.error("Error deleting position:",e),t.status(500).json({error:"Failed to delete position"})}}let m=(0,n.M)(s,"default"),E=(0,n.M)(s,"config"),y=new i.PagesAPIRouteModule({definition:{kind:a.A.PAGES_API,page:"/api/positions",pathname:"/api/positions",bundlePath:"",filename:""},userland:s})},7457:(e,t,o)=>{o.d(t,{A:()=>n,X:()=>a});let s=require("arangojs"),i=async e=>({jobSeekers:e.collection("jobSeekers"),companies:e.collection("companies"),hiringAuthorities:e.collection("hiringAuthorities"),positions:e.collection("positions"),skills:e.collection("skills"),matches:e.collection("matches"),works_for:e.collection("works_for"),employs:e.collection("employs"),posts:e.collection("posts"),requires:e.collection("requires"),has_skill:e.collection("has_skill"),matched_to:e.collection("matched_to"),reports_to:e.collection("reports_to")}),a=async()=>{let e=process.env.ARANGODB_DB_NAME||"candid_connections",t=new s.Database({url:process.env.ARANGODB_URL||"http://localhost:8529",databaseName:"_system",auth:{username:process.env.ARANGODB_USERNAME||"root",password:process.env.ARANGODB_PASSWORD||""}});try{await t.createDatabase(e),console.log(`✅ Created database: ${e}`)}catch(t){1207===t.errorNum?console.log(`✅ Database ${e} already exists`):console.log("Database creation note:",t.message)}let o=new s.Database({url:process.env.ARANGODB_URL||"http://localhost:8529",databaseName:e,auth:{username:process.env.ARANGODB_USERNAME||"root",password:process.env.ARANGODB_PASSWORD||""}}),a=await i(o);return{db:o,collections:a}},n=a},8667:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return o}});var o=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})}};var t=require("../../webpack-api-runtime.js");t.C(e);var o=t(t.s=7181);module.exports=o})();