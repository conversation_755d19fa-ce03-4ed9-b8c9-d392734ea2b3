"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/seed-database";
exports.ids = ["pages/api/seed-database"];
exports.modules = {

/***/ "(api-node)/./lib/db.js":
/*!*******************!*\
  !*** ./lib/db.js ***!
  \*******************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   initDb: () => (/* binding */ initDb)\n/* harmony export */ });\n/* harmony import */ var arangojs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! arangojs */ \"arangojs\");\n/* harmony import */ var arangojs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(arangojs__WEBPACK_IMPORTED_MODULE_0__);\n\n// Get collections\nconst getCollections = async (db)=>{\n    return {\n        jobSeekers: db.collection('jobSeekers'),\n        companies: db.collection('companies'),\n        hiringAuthorities: db.collection('hiringAuthorities'),\n        positions: db.collection('positions'),\n        skills: db.collection('skills'),\n        matches: db.collection('matches'),\n        // Edge collections - using regular collection method for now\n        works_for: db.collection('works_for'),\n        employs: db.collection('employs'),\n        posts: db.collection('posts'),\n        requires: db.collection('requires'),\n        has_skill: db.collection('has_skill'),\n        matched_to: db.collection('matched_to'),\n        reports_to: db.collection('reports_to')\n    };\n};\n// Initialize database and collections\nconst initDb = async ()=>{\n    const dbName = process.env.ARANGODB_DB_NAME || 'candid_connections';\n    // First connect to _system database to create our database\n    const systemDb = new arangojs__WEBPACK_IMPORTED_MODULE_0__.Database({\n        url: process.env.ARANGODB_URL || 'http://localhost:8529',\n        databaseName: '_system',\n        auth: {\n            username: process.env.ARANGODB_USERNAME || 'root',\n            password: process.env.ARANGODB_PASSWORD || ''\n        }\n    });\n    // Ensure our database exists\n    try {\n        await systemDb.createDatabase(dbName);\n        console.log(`✅ Created database: ${dbName}`);\n    } catch (error) {\n        if (error.errorNum === 1207) {\n            console.log(`✅ Database ${dbName} already exists`);\n        } else {\n            console.log('Database creation note:', error.message);\n        }\n    }\n    // Now connect to our specific database\n    const db = new arangojs__WEBPACK_IMPORTED_MODULE_0__.Database({\n        url: process.env.ARANGODB_URL || 'http://localhost:8529',\n        databaseName: dbName,\n        auth: {\n            username: process.env.ARANGODB_USERNAME || 'root',\n            password: process.env.ARANGODB_PASSWORD || ''\n        }\n    });\n    const collections = await getCollections(db);\n    return {\n        db,\n        collections\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (initDb);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./lib/db.js\n");

/***/ }),

/***/ "(api-node)/./lib/matchingAlgorithm.js":
/*!**********************************!*\
  !*** ./lib/matchingAlgorithm.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateAuthorityMatch: () => (/* binding */ calculateAuthorityMatch),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   generateAllMatches: () => (/* binding */ generateAllMatches)\n/* harmony export */ });\n// Hiring Authority Matching Algorithm\n// Based on company size logic and skill alignment\nfunction calculateAuthorityMatch(jobSeeker, hiringAuthority, company) {\n    let score = 0;\n    let matchReasons = [];\n    let hierarchyMatch = '';\n    // 1. Company Size Logic (30% of score)\n    const companySizeScore = calculateCompanySizeMatch(jobSeeker, hiringAuthority, company);\n    score += companySizeScore.score * 0.3;\n    matchReasons.push(...companySizeScore.reasons);\n    hierarchyMatch = companySizeScore.hierarchyMatch;\n    // 2. Skill Alignment (40% of score)\n    const skillScore = calculateSkillAlignment(jobSeeker, hiringAuthority);\n    score += skillScore.score * 0.4;\n    matchReasons.push(...skillScore.reasons);\n    // 3. Experience Level Match (20% of score)\n    const experienceScore = calculateExperienceMatch(jobSeeker, hiringAuthority);\n    score += experienceScore.score * 0.2;\n    matchReasons.push(...experienceScore.reasons);\n    // 4. Decision Making Power (10% of score)\n    const decisionScore = calculateDecisionPower(hiringAuthority);\n    score += decisionScore.score * 0.1;\n    matchReasons.push(...decisionScore.reasons);\n    return {\n        score: Math.round(score),\n        matchReasons: matchReasons.slice(0, 4),\n        hierarchyMatch,\n        connectionStrength: getConnectionStrength(score)\n    };\n}\nfunction calculateCompanySizeMatch(jobSeeker, hiringAuthority, company) {\n    const companySize = company.employeeCount;\n    const authorityLevel = hiringAuthority.level;\n    const jobSeekerExperience = jobSeeker.experience;\n    let score = 0;\n    let reasons = [];\n    let hierarchyMatch = '';\n    // Startup Logic (<100 employees)\n    if (companySize < 100) {\n        if (authorityLevel === 'C-Suite') {\n            score = 95;\n            reasons.push('Perfect startup match - direct access to decision maker');\n            hierarchyMatch = 'Perfect - C-Suite authority for startup environment';\n        } else {\n            score = 60;\n            reasons.push('Startup prefers C-Suite hiring decisions');\n            hierarchyMatch = 'Suboptimal - Startup should route to C-Suite';\n        }\n    } else if (companySize >= 100 && companySize <= 1000) {\n        if (authorityLevel === 'Executive' || authorityLevel === 'Director') {\n            score = 90;\n            reasons.push('Ideal mid-size company authority level');\n            hierarchyMatch = 'Perfect - Executive/Director level for mid-size company';\n        } else if (authorityLevel === 'C-Suite') {\n            score = 75;\n            reasons.push('C-Suite accessible but may delegate to directors');\n            hierarchyMatch = 'Good - C-Suite may delegate to department heads';\n        } else {\n            score = 65;\n            reasons.push('Manager level appropriate for specific roles');\n            hierarchyMatch = 'Acceptable - Manager level for specialized positions';\n        }\n    } else {\n        if (authorityLevel === 'Director' || authorityLevel === 'Manager') {\n            score = 85;\n            reasons.push('Appropriate enterprise hierarchy level');\n            hierarchyMatch = 'Perfect - Director/Manager level for enterprise';\n        } else if (authorityLevel === 'Executive') {\n            score = 70;\n            reasons.push('Executive level may be too high for initial contact');\n            hierarchyMatch = 'Good - Executive may route to appropriate director';\n        } else {\n            score = 50;\n            reasons.push('C-Suite rarely involved in individual hiring');\n            hierarchyMatch = 'Poor - C-Suite too high for enterprise individual hiring';\n        }\n    }\n    return {\n        score,\n        reasons,\n        hierarchyMatch\n    };\n}\nfunction calculateSkillAlignment(jobSeeker, hiringAuthority) {\n    const jobSeekerSkills = jobSeeker.skills || [];\n    const authoritySkills = hiringAuthority.skillsLookingFor || [];\n    let matchingSkills = [];\n    let score = 0;\n    let reasons = [];\n    // Find matching skills\n    for (const skill of jobSeekerSkills){\n        if (authoritySkills.includes(skill)) {\n            matchingSkills.push(skill);\n            // Weight by job seeker's skill level if available\n            const skillLevel = jobSeeker.skillLevels?.[skill] || 5;\n            score += skillLevel * 10 // Max 100 points per skill\n            ;\n        }\n    }\n    // Calculate percentage match\n    if (authoritySkills.length > 0) {\n        const matchPercentage = matchingSkills.length / authoritySkills.length * 100;\n        score = Math.min(score / matchingSkills.length || 0, 100) * (matchPercentage / 100);\n    }\n    // Generate reasons\n    if (matchingSkills.length > 0) {\n        reasons.push(`${matchingSkills.length}/${authoritySkills.length} required skills match`);\n        if (matchingSkills.length >= 3) {\n            reasons.push('Strong technical skill alignment');\n        }\n    } else {\n        reasons.push('Limited skill overlap - may need training');\n    }\n    return {\n        score,\n        reasons,\n        matchingSkills\n    };\n}\nfunction calculateExperienceMatch(jobSeeker, hiringAuthority) {\n    const jobSeekerExp = jobSeeker.experience;\n    const preferredExp = hiringAuthority.preferredExperience || '3-8 years';\n    // Parse preferred experience range\n    const expRange = preferredExp.match(/(\\d+)-(\\d+)/);\n    if (!expRange) return {\n        score: 50,\n        reasons: [\n            'Experience requirements unclear'\n        ]\n    };\n    const minExp = parseInt(expRange[1]);\n    const maxExp = parseInt(expRange[2]);\n    let score = 0;\n    let reasons = [];\n    if (jobSeekerExp >= minExp && jobSeekerExp <= maxExp) {\n        score = 90;\n        reasons.push(`Experience (${jobSeekerExp} years) perfectly matches requirements`);\n    } else if (jobSeekerExp < minExp) {\n        const gap = minExp - jobSeekerExp;\n        score = Math.max(40, 90 - gap * 15);\n        reasons.push(`${gap} years below preferred minimum experience`);\n    } else {\n        const excess = jobSeekerExp - maxExp;\n        score = Math.max(60, 90 - excess * 10);\n        reasons.push(`${excess} years above preferred maximum - may be overqualified`);\n    }\n    return {\n        score,\n        reasons\n    };\n}\nfunction calculateDecisionPower(hiringAuthority) {\n    const hiringPower = hiringAuthority.hiringPower;\n    const isDecisionMaker = hiringAuthority.decisionMaker;\n    let score = 0;\n    let reasons = [];\n    switch(hiringPower){\n        case 'Ultimate':\n            score = 100;\n            reasons.push('Ultimate hiring authority - can make immediate decisions');\n            break;\n        case 'High':\n            score = 85;\n            reasons.push('High hiring authority - minimal approval needed');\n            break;\n        case 'Medium':\n            score = 70;\n            reasons.push('Medium hiring authority - may need additional approvals');\n            break;\n        default:\n            score = 50;\n            reasons.push('Limited hiring authority');\n    }\n    if (isDecisionMaker) {\n        score = Math.min(100, score + 10);\n        reasons.push('Direct decision maker');\n    }\n    return {\n        score,\n        reasons\n    };\n}\nfunction getConnectionStrength(score) {\n    if (score >= 85) return 'Strong';\n    if (score >= 70) return 'Medium';\n    if (score >= 55) return 'Weak';\n    return 'Poor';\n}\n// Generate matches for all job seekers against all hiring authorities\nasync function generateAllMatches(jobSeekers, hiringAuthorities, companies) {\n    const matches = [];\n    for (const jobSeeker of jobSeekers){\n        for (const authority of hiringAuthorities){\n            // Find the authority's company\n            const company = companies.find((c)=>`companies/${c._key}` === authority.companyId);\n            if (!company) continue;\n            const match = calculateAuthorityMatch(jobSeeker, authority, company);\n            // Only include matches above threshold\n            if (match.score >= 50) {\n                matches.push({\n                    _key: `match_${jobSeeker._key}_${authority._key}`,\n                    jobSeekerId: `jobSeekers/${jobSeeker._key}`,\n                    hiringAuthorityId: `hiringAuthorities/${authority._key}`,\n                    companyId: `companies/${company._key}`,\n                    score: match.score,\n                    matchReasons: match.matchReasons,\n                    hierarchyMatch: match.hierarchyMatch,\n                    connectionStrength: match.connectionStrength,\n                    status: match.score >= 80 ? 'recommended' : 'potential',\n                    createdAt: new Date().toISOString()\n                });\n            }\n        }\n    }\n    // Sort by score descending\n    return matches.sort((a, b)=>b.score - a.score);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    calculateAuthorityMatch,\n    generateAllMatches,\n    getConnectionStrength\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./lib/matchingAlgorithm.js\n");

/***/ }),

/***/ "(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fseed-database&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fseed-database.js&middlewareConfigBase64=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fseed-database&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fseed-database.js&middlewareConfigBase64=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/./node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_seed_database_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages/api/seed-database.js */ \"(api-node)/./pages/api/seed-database.js\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_seed_database_js__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_seed_database_js__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/seed-database\",\n        pathname: \"/api/seed-database\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_seed_database_js__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaS1ub2RlKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtcm91dGUtbG9hZGVyL2luZGV4LmpzP2tpbmQ9UEFHRVNfQVBJJnBhZ2U9JTJGYXBpJTJGc2VlZC1kYXRhYmFzZSZwcmVmZXJyZWRSZWdpb249JmFic29sdXRlUGFnZVBhdGg9LiUyRnBhZ2VzJTJGYXBpJTJGc2VlZC1kYXRhYmFzZS5qcyZtaWRkbGV3YXJlQ29uZmlnQmFzZTY0PWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBK0Y7QUFDdkM7QUFDRTtBQUMxRDtBQUN5RDtBQUN6RDtBQUNBLGlFQUFlLHdFQUFLLENBQUMsd0RBQVEsWUFBWSxFQUFDO0FBQzFDO0FBQ08sZUFBZSx3RUFBSyxDQUFDLHdEQUFRO0FBQ3BDO0FBQ08sd0JBQXdCLHlHQUFtQjtBQUNsRDtBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxZQUFZO0FBQ1osQ0FBQzs7QUFFRCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFBhZ2VzQVBJUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1tb2R1bGVzL3BhZ2VzLWFwaS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IGhvaXN0IH0gZnJvbSBcIm5leHQvZGlzdC9idWlsZC90ZW1wbGF0ZXMvaGVscGVyc1wiO1xuLy8gSW1wb3J0IHRoZSB1c2VybGFuZCBjb2RlLlxuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIi4vcGFnZXMvYXBpL3NlZWQtZGF0YWJhc2UuanNcIjtcbi8vIFJlLWV4cG9ydCB0aGUgaGFuZGxlciAoc2hvdWxkIGJlIHRoZSBkZWZhdWx0IGV4cG9ydCkuXG5leHBvcnQgZGVmYXVsdCBob2lzdCh1c2VybGFuZCwgJ2RlZmF1bHQnKTtcbi8vIFJlLWV4cG9ydCBjb25maWcuXG5leHBvcnQgY29uc3QgY29uZmlnID0gaG9pc3QodXNlcmxhbmQsICdjb25maWcnKTtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IFBhZ2VzQVBJUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLlBBR0VTX0FQSSxcbiAgICAgICAgcGFnZTogXCIvYXBpL3NlZWQtZGF0YWJhc2VcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9zZWVkLWRhdGFiYXNlXCIsXG4gICAgICAgIC8vIFRoZSBmb2xsb3dpbmcgYXJlbid0IHVzZWQgaW4gcHJvZHVjdGlvbi5cbiAgICAgICAgYnVuZGxlUGF0aDogJycsXG4gICAgICAgIGZpbGVuYW1lOiAnJ1xuICAgIH0sXG4gICAgdXNlcmxhbmRcbn0pO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1wYWdlcy1hcGkuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fseed-database&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fseed-database.js&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/./pages/api/seed-database.js":
/*!************************************!*\
  !*** ./pages/api/seed-database.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _scripts_seedDatabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../scripts/seedDatabase.js */ \"(api-node)/./scripts/seedDatabase.js\");\n\nasync function handler(req, res) {\n    if (req.method !== 'POST') {\n        return res.status(405).json({\n            error: 'Method not allowed'\n        });\n    }\n    try {\n        console.log('🌱 Starting database seeding...');\n        const result = await (0,_scripts_seedDatabase_js__WEBPACK_IMPORTED_MODULE_0__.seedDatabase)();\n        if (result.success) {\n            return res.status(200).json({\n                message: 'Database seeded successfully',\n                stats: result.stats\n            });\n        } else {\n            return res.status(500).json({\n                error: 'Failed to seed database',\n                details: result.error\n            });\n        }\n    } catch (error) {\n        console.error('Error in seed API:', error);\n        return res.status(500).json({\n            error: 'Internal server error',\n            details: error.message\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/seed-database.js\n");

/***/ }),

/***/ "(api-node)/./scripts/seedDatabase.js":
/*!*********************************!*\
  !*** ./scripts/seedDatabase.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   seedDatabase: () => (/* binding */ seedDatabase)\n/* harmony export */ });\n/* harmony import */ var _lib_db_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/db.js */ \"(api-node)/./lib/db.js\");\n/* harmony import */ var _lib_matchingAlgorithm_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/matchingAlgorithm.js */ \"(api-node)/./lib/matchingAlgorithm.js\");\n\n\n// Comprehensive mock data for hiring authority matching demonstration\nconst mockData = {\n    // 5 Companies with different sizes and hierarchies\n    companies: [\n        {\n            _key: 'startup_techflow',\n            name: 'TechFlow Innovations',\n            industry: 'FinTech',\n            size: 'Startup',\n            employeeCount: 45,\n            location: 'San Francisco, CA',\n            description: 'Blockchain-based payment solutions startup',\n            founded: '2022',\n            website: 'https://techflow.com'\n        },\n        {\n            _key: 'midsize_cloudtech',\n            name: 'CloudTech Solutions',\n            industry: 'Technology',\n            size: 'Mid-size',\n            employeeCount: 350,\n            location: 'Austin, TX',\n            description: 'Cloud infrastructure and DevOps services',\n            founded: '2018',\n            website: 'https://cloudtech.com'\n        },\n        {\n            _key: 'enterprise_megacorp',\n            name: 'MegaCorp Industries',\n            industry: 'Manufacturing',\n            size: 'Enterprise',\n            employeeCount: 2500,\n            location: 'Detroit, MI',\n            description: 'Industrial automation and robotics',\n            founded: '1995',\n            website: 'https://megacorp.com'\n        },\n        {\n            _key: 'midsize_designstudio',\n            name: 'Design Studio Pro',\n            industry: 'Design',\n            size: 'Mid-size',\n            employeeCount: 180,\n            location: 'New York, NY',\n            description: 'Digital design and user experience agency',\n            founded: '2015',\n            website: 'https://designstudio.com'\n        },\n        {\n            _key: 'startup_aiventures',\n            name: 'AI Ventures',\n            industry: 'Artificial Intelligence',\n            size: 'Startup',\n            employeeCount: 28,\n            location: 'Seattle, WA',\n            description: 'Machine learning and AI consulting',\n            founded: '2023',\n            website: 'https://aiventures.com'\n        }\n    ],\n    // Hiring Authorities based on company size logic\n    hiringAuthorities: [\n        // TechFlow (Startup) - C-Suite hiring\n        {\n            _key: 'ceo_techflow',\n            name: 'Jennifer Martinez',\n            role: 'CEO & Founder',\n            level: 'C-Suite',\n            companyId: 'companies/startup_techflow',\n            email: '<EMAIL>',\n            hiringPower: 'Ultimate',\n            decisionMaker: true,\n            skillsLookingFor: [\n                'Leadership',\n                'FinTech',\n                'Blockchain',\n                'Full Stack',\n                'Startup Experience'\n            ],\n            preferredExperience: '3-8 years',\n            bio: 'Serial entrepreneur with 3 successful exits in FinTech'\n        },\n        {\n            _key: 'cto_techflow',\n            name: 'David Kim',\n            role: 'CTO',\n            level: 'C-Suite',\n            companyId: 'companies/startup_techflow',\n            email: '<EMAIL>',\n            hiringPower: 'Ultimate',\n            decisionMaker: true,\n            skillsLookingFor: [\n                'React',\n                'Node.js',\n                'Blockchain',\n                'Solidity',\n                'AWS'\n            ],\n            preferredExperience: '4-10 years',\n            bio: 'Former Google engineer specializing in distributed systems'\n        },\n        // CloudTech (Mid-size) - VP/Director level\n        {\n            _key: 'vp_eng_cloudtech',\n            name: 'Sarah Wilson',\n            role: 'VP Engineering',\n            level: 'Executive',\n            companyId: 'companies/midsize_cloudtech',\n            email: '<EMAIL>',\n            hiringPower: 'High',\n            decisionMaker: true,\n            skillsLookingFor: [\n                'Kubernetes',\n                'Docker',\n                'Terraform',\n                'Python',\n                'Leadership'\n            ],\n            preferredExperience: '5-12 years',\n            bio: 'Infrastructure expert with 15 years in cloud technologies'\n        },\n        {\n            _key: 'dir_product_cloudtech',\n            name: 'Mike Chen',\n            role: 'Director of Product',\n            level: 'Director',\n            companyId: 'companies/midsize_cloudtech',\n            email: '<EMAIL>',\n            hiringPower: 'High',\n            decisionMaker: false,\n            skillsLookingFor: [\n                'Product Management',\n                'UX/UI',\n                'Analytics',\n                'Agile',\n                'Cloud Platforms'\n            ],\n            preferredExperience: '4-8 years',\n            bio: 'Product leader focused on developer experience'\n        },\n        // MegaCorp (Enterprise) - HR and Department Heads\n        {\n            _key: 'hr_director_megacorp',\n            name: 'Lisa Thompson',\n            role: 'HR Director',\n            level: 'Director',\n            companyId: 'companies/enterprise_megacorp',\n            email: '<EMAIL>',\n            hiringPower: 'Medium',\n            decisionMaker: false,\n            skillsLookingFor: [\n                'Operations',\n                'Six Sigma',\n                'Project Management',\n                'Manufacturing'\n            ],\n            preferredExperience: '3-10 years',\n            bio: 'HR professional specializing in technical recruitment'\n        },\n        {\n            _key: 'eng_manager_megacorp',\n            name: 'Robert Davis',\n            role: 'Engineering Manager',\n            level: 'Manager',\n            companyId: 'companies/enterprise_megacorp',\n            email: '<EMAIL>',\n            hiringPower: 'Medium',\n            decisionMaker: false,\n            skillsLookingFor: [\n                'C++',\n                'Embedded Systems',\n                'Robotics',\n                'PLC Programming'\n            ],\n            preferredExperience: '5-15 years',\n            bio: 'Robotics engineer with industrial automation expertise'\n        },\n        // Design Studio (Mid-size) - Creative leadership\n        {\n            _key: 'creative_director_design',\n            name: 'Emma Rodriguez',\n            role: 'Creative Director',\n            level: 'Executive',\n            companyId: 'companies/midsize_designstudio',\n            email: '<EMAIL>',\n            hiringPower: 'High',\n            decisionMaker: true,\n            skillsLookingFor: [\n                'Figma',\n                'User Research',\n                'Design Systems',\n                'Prototyping',\n                'Leadership'\n            ],\n            preferredExperience: '4-10 years',\n            bio: 'Award-winning designer with Fortune 500 client experience'\n        },\n        // AI Ventures (Startup) - Technical founders\n        {\n            _key: 'founder_ai',\n            name: 'Dr. Alex Chen',\n            role: 'Founder & Chief Scientist',\n            level: 'C-Suite',\n            companyId: 'companies/startup_aiventures',\n            email: '<EMAIL>',\n            hiringPower: 'Ultimate',\n            decisionMaker: true,\n            skillsLookingFor: [\n                'Machine Learning',\n                'Python',\n                'TensorFlow',\n                'Research',\n                'PhD'\n            ],\n            preferredExperience: '3-12 years',\n            bio: 'Former Stanford AI researcher with 50+ publications'\n        }\n    ],\n    // Skills taxonomy\n    skills: [\n        {\n            _key: 'react',\n            name: 'React',\n            category: 'Frontend',\n            demand: 'High'\n        },\n        {\n            _key: 'nodejs',\n            name: 'Node.js',\n            category: 'Backend',\n            demand: 'High'\n        },\n        {\n            _key: 'python',\n            name: 'Python',\n            category: 'Backend',\n            demand: 'Very High'\n        },\n        {\n            _key: 'typescript',\n            name: 'TypeScript',\n            category: 'Frontend',\n            demand: 'High'\n        },\n        {\n            _key: 'kubernetes',\n            name: 'Kubernetes',\n            category: 'DevOps',\n            demand: 'High'\n        },\n        {\n            _key: 'docker',\n            name: 'Docker',\n            category: 'DevOps',\n            demand: 'High'\n        },\n        {\n            _key: 'terraform',\n            name: 'Terraform',\n            category: 'Infrastructure',\n            demand: 'Medium'\n        },\n        {\n            _key: 'aws',\n            name: 'AWS',\n            category: 'Cloud',\n            demand: 'Very High'\n        },\n        {\n            _key: 'blockchain',\n            name: 'Blockchain',\n            category: 'Emerging',\n            demand: 'Medium'\n        },\n        {\n            _key: 'solidity',\n            name: 'Solidity',\n            category: 'Blockchain',\n            demand: 'Low'\n        },\n        {\n            _key: 'figma',\n            name: 'Figma',\n            category: 'Design',\n            demand: 'High'\n        },\n        {\n            _key: 'user_research',\n            name: 'User Research',\n            category: 'UX',\n            demand: 'Medium'\n        },\n        {\n            _key: 'machine_learning',\n            name: 'Machine Learning',\n            category: 'AI',\n            demand: 'Very High'\n        },\n        {\n            _key: 'tensorflow',\n            name: 'TensorFlow',\n            category: 'AI',\n            demand: 'High'\n        },\n        {\n            _key: 'cpp',\n            name: 'C++',\n            category: 'Systems',\n            demand: 'Medium'\n        },\n        {\n            _key: 'embedded',\n            name: 'Embedded Systems',\n            category: 'Hardware',\n            demand: 'Medium'\n        },\n        {\n            _key: 'robotics',\n            name: 'Robotics',\n            category: 'Engineering',\n            demand: 'Low'\n        },\n        {\n            _key: 'leadership',\n            name: 'Leadership',\n            category: 'Soft Skills',\n            demand: 'High'\n        },\n        {\n            _key: 'product_mgmt',\n            name: 'Product Management',\n            category: 'Business',\n            demand: 'High'\n        },\n        {\n            _key: 'agile',\n            name: 'Agile',\n            category: 'Methodology',\n            demand: 'High'\n        }\n    ],\n    // 20 Job Seekers with diverse backgrounds\n    jobSeekers: [\n        {\n            _key: 'js_sarah_chen',\n            name: 'Sarah Chen',\n            email: '<EMAIL>',\n            currentTitle: 'Senior Frontend Developer',\n            experience: 6,\n            location: 'San Francisco, CA',\n            skills: [\n                'react',\n                'typescript',\n                'nodejs',\n                'aws'\n            ],\n            skillLevels: {\n                react: 9,\n                typescript: 8,\n                nodejs: 7,\n                aws: 6\n            },\n            desiredRole: 'Lead Frontend Engineer',\n            salaryExpectation: 140000,\n            remote: true,\n            bio: 'Passionate frontend developer with startup experience'\n        },\n        {\n            _key: 'js_marcus_johnson',\n            name: 'Marcus Johnson',\n            email: '<EMAIL>',\n            currentTitle: 'Full Stack Developer',\n            experience: 4,\n            location: 'Austin, TX',\n            skills: [\n                'python',\n                'react',\n                'aws',\n                'docker'\n            ],\n            skillLevels: {\n                python: 8,\n                react: 7,\n                aws: 6,\n                docker: 7\n            },\n            desiredRole: 'Senior Full Stack Developer',\n            salaryExpectation: 120000,\n            remote: false,\n            bio: 'Full stack developer with cloud expertise'\n        },\n        {\n            _key: 'js_emily_rodriguez',\n            name: 'Emily Rodriguez',\n            email: '<EMAIL>',\n            currentTitle: 'UX Designer',\n            experience: 5,\n            location: 'New York, NY',\n            skills: [\n                'figma',\n                'user_research',\n                'leadership'\n            ],\n            skillLevels: {\n                figma: 9,\n                user_research: 8,\n                leadership: 6\n            },\n            desiredRole: 'Senior UX Designer',\n            salaryExpectation: 110000,\n            remote: true,\n            bio: 'User-centered designer with Fortune 500 experience'\n        },\n        {\n            _key: 'js_david_park',\n            name: 'David Park',\n            email: '<EMAIL>',\n            currentTitle: 'DevOps Engineer',\n            experience: 7,\n            location: 'Seattle, WA',\n            skills: [\n                'kubernetes',\n                'docker',\n                'terraform',\n                'aws',\n                'python'\n            ],\n            skillLevels: {\n                kubernetes: 9,\n                docker: 9,\n                terraform: 8,\n                aws: 8,\n                python: 7\n            },\n            desiredRole: 'Senior DevOps Engineer',\n            salaryExpectation: 135000,\n            remote: true,\n            bio: 'Infrastructure automation specialist'\n        },\n        {\n            _key: 'js_lisa_wang',\n            name: 'Lisa Wang',\n            email: '<EMAIL>',\n            currentTitle: 'Machine Learning Engineer',\n            experience: 3,\n            location: 'San Francisco, CA',\n            skills: [\n                'machine_learning',\n                'python',\n                'tensorflow'\n            ],\n            skillLevels: {\n                machine_learning: 8,\n                python: 9,\n                tensorflow: 7\n            },\n            desiredRole: 'Senior ML Engineer',\n            salaryExpectation: 150000,\n            remote: true,\n            bio: 'PhD in Computer Science, AI research background'\n        },\n        {\n            _key: 'js_james_wilson',\n            name: 'James Wilson',\n            email: '<EMAIL>',\n            currentTitle: 'Blockchain Developer',\n            experience: 2,\n            location: 'Austin, TX',\n            skills: [\n                'blockchain',\n                'solidity',\n                'nodejs',\n                'react'\n            ],\n            skillLevels: {\n                blockchain: 7,\n                solidity: 8,\n                nodejs: 6,\n                react: 5\n            },\n            desiredRole: 'Senior Blockchain Developer',\n            salaryExpectation: 130000,\n            remote: true,\n            bio: 'Early blockchain adopter with DeFi experience'\n        },\n        {\n            _key: 'js_anna_kim',\n            name: 'Anna Kim',\n            email: '<EMAIL>',\n            currentTitle: 'Product Manager',\n            experience: 5,\n            location: 'New York, NY',\n            skills: [\n                'product_mgmt',\n                'agile',\n                'user_research',\n                'leadership'\n            ],\n            skillLevels: {\n                product_mgmt: 8,\n                agile: 7,\n                user_research: 6,\n                leadership: 7\n            },\n            desiredRole: 'Senior Product Manager',\n            salaryExpectation: 125000,\n            remote: false,\n            bio: 'Product leader with B2B SaaS experience'\n        },\n        {\n            _key: 'js_robert_davis',\n            name: 'Robert Davis',\n            email: '<EMAIL>',\n            currentTitle: 'Embedded Systems Engineer',\n            experience: 8,\n            location: 'Detroit, MI',\n            skills: [\n                'cpp',\n                'embedded',\n                'robotics'\n            ],\n            skillLevels: {\n                cpp: 9,\n                embedded: 8,\n                robotics: 7\n            },\n            desiredRole: 'Senior Embedded Engineer',\n            salaryExpectation: 115000,\n            remote: false,\n            bio: 'Automotive industry veteran with robotics expertise'\n        },\n        {\n            _key: 'js_maria_gonzalez',\n            name: 'Maria Gonzalez',\n            email: '<EMAIL>',\n            currentTitle: 'Frontend Developer',\n            experience: 3,\n            location: 'Los Angeles, CA',\n            skills: [\n                'react',\n                'typescript',\n                'figma'\n            ],\n            skillLevels: {\n                react: 7,\n                typescript: 6,\n                figma: 5\n            },\n            desiredRole: 'Senior Frontend Developer',\n            salaryExpectation: 105000,\n            remote: true,\n            bio: 'Creative developer with design background'\n        },\n        {\n            _key: 'js_kevin_lee',\n            name: 'Kevin Lee',\n            email: '<EMAIL>',\n            currentTitle: 'Backend Developer',\n            experience: 4,\n            location: 'Chicago, IL',\n            skills: [\n                'python',\n                'nodejs',\n                'aws',\n                'docker'\n            ],\n            skillLevels: {\n                python: 8,\n                nodejs: 7,\n                aws: 6,\n                docker: 6\n            },\n            desiredRole: 'Senior Backend Developer',\n            salaryExpectation: 115000,\n            remote: true,\n            bio: 'API design specialist with microservices experience'\n        },\n        // Additional 10 job seekers for comprehensive testing\n        {\n            _key: 'js_jennifer_brown',\n            name: 'Jennifer Brown',\n            email: '<EMAIL>',\n            currentTitle: 'Senior Product Designer',\n            experience: 6,\n            location: 'San Francisco, CA',\n            skills: [\n                'figma',\n                'user_research',\n                'leadership',\n                'product_mgmt'\n            ],\n            skillLevels: {\n                figma: 8,\n                user_research: 9,\n                leadership: 7,\n                product_mgmt: 6\n            },\n            desiredRole: 'Design Director',\n            salaryExpectation: 145000,\n            remote: true,\n            bio: 'Design leader with startup and enterprise experience'\n        },\n        {\n            _key: 'js_michael_zhang',\n            name: 'Michael Zhang',\n            email: '<EMAIL>',\n            currentTitle: 'Cloud Architect',\n            experience: 9,\n            location: 'Austin, TX',\n            skills: [\n                'aws',\n                'kubernetes',\n                'terraform',\n                'python',\n                'leadership'\n            ],\n            skillLevels: {\n                aws: 9,\n                kubernetes: 8,\n                terraform: 9,\n                python: 7,\n                leadership: 8\n            },\n            desiredRole: 'Principal Cloud Architect',\n            salaryExpectation: 160000,\n            remote: false,\n            bio: 'Cloud infrastructure expert with enterprise scaling experience'\n        },\n        {\n            _key: 'js_sophia_martinez',\n            name: 'Sophia Martinez',\n            email: '<EMAIL>',\n            currentTitle: 'AI Research Scientist',\n            experience: 4,\n            location: 'Seattle, WA',\n            skills: [\n                'machine_learning',\n                'python',\n                'tensorflow',\n                'leadership'\n            ],\n            skillLevels: {\n                machine_learning: 9,\n                python: 9,\n                tensorflow: 8,\n                leadership: 5\n            },\n            desiredRole: 'Senior AI Scientist',\n            salaryExpectation: 170000,\n            remote: true,\n            bio: 'PhD in Machine Learning with published research'\n        },\n        {\n            _key: 'js_alex_thompson',\n            name: 'Alex Thompson',\n            email: '<EMAIL>',\n            currentTitle: 'Full Stack Engineer',\n            experience: 5,\n            location: 'New York, NY',\n            skills: [\n                'react',\n                'nodejs',\n                'python',\n                'aws',\n                'leadership'\n            ],\n            skillLevels: {\n                react: 8,\n                nodejs: 8,\n                python: 7,\n                aws: 6,\n                leadership: 6\n            },\n            desiredRole: 'Engineering Manager',\n            salaryExpectation: 140000,\n            remote: false,\n            bio: 'Technical leader transitioning to management'\n        },\n        {\n            _key: 'js_rachel_kim',\n            name: 'Rachel Kim',\n            email: '<EMAIL>',\n            currentTitle: 'Robotics Engineer',\n            experience: 6,\n            location: 'Detroit, MI',\n            skills: [\n                'cpp',\n                'robotics',\n                'embedded',\n                'python'\n            ],\n            skillLevels: {\n                cpp: 8,\n                robotics: 9,\n                embedded: 7,\n                python: 6\n            },\n            desiredRole: 'Senior Robotics Engineer',\n            salaryExpectation: 125000,\n            remote: false,\n            bio: 'Autonomous systems specialist with manufacturing background'\n        },\n        {\n            _key: 'js_daniel_garcia',\n            name: 'Daniel Garcia',\n            email: '<EMAIL>',\n            currentTitle: 'Blockchain Architect',\n            experience: 4,\n            location: 'San Francisco, CA',\n            skills: [\n                'blockchain',\n                'solidity',\n                'nodejs',\n                'python',\n                'leadership'\n            ],\n            skillLevels: {\n                blockchain: 9,\n                solidity: 9,\n                nodejs: 7,\n                python: 6,\n                leadership: 5\n            },\n            desiredRole: 'Lead Blockchain Developer',\n            salaryExpectation: 155000,\n            remote: true,\n            bio: 'DeFi protocol architect with smart contract expertise'\n        },\n        {\n            _key: 'js_amanda_wilson',\n            name: 'Amanda Wilson',\n            email: '<EMAIL>',\n            currentTitle: 'Senior UX Researcher',\n            experience: 7,\n            location: 'New York, NY',\n            skills: [\n                'user_research',\n                'figma',\n                'product_mgmt',\n                'leadership'\n            ],\n            skillLevels: {\n                user_research: 9,\n                figma: 6,\n                product_mgmt: 7,\n                leadership: 8\n            },\n            desiredRole: 'Head of UX Research',\n            salaryExpectation: 135000,\n            remote: true,\n            bio: 'Research leader with quantitative and qualitative expertise'\n        },\n        {\n            _key: 'js_carlos_rodriguez',\n            name: 'Carlos Rodriguez',\n            email: '<EMAIL>',\n            currentTitle: 'DevOps Manager',\n            experience: 8,\n            location: 'Austin, TX',\n            skills: [\n                'kubernetes',\n                'docker',\n                'terraform',\n                'aws',\n                'leadership'\n            ],\n            skillLevels: {\n                kubernetes: 9,\n                docker: 8,\n                terraform: 8,\n                aws: 9,\n                leadership: 8\n            },\n            desiredRole: 'Director of Infrastructure',\n            salaryExpectation: 150000,\n            remote: false,\n            bio: 'Infrastructure leader with team management experience'\n        },\n        {\n            _key: 'js_natalie_chen',\n            name: 'Natalie Chen',\n            email: '<EMAIL>',\n            currentTitle: 'Frontend Architect',\n            experience: 7,\n            location: 'Los Angeles, CA',\n            skills: [\n                'react',\n                'typescript',\n                'nodejs',\n                'leadership'\n            ],\n            skillLevels: {\n                react: 9,\n                typescript: 9,\n                nodejs: 6,\n                leadership: 7\n            },\n            desiredRole: 'Principal Frontend Engineer',\n            salaryExpectation: 145000,\n            remote: true,\n            bio: 'Frontend architecture specialist with performance optimization expertise'\n        },\n        {\n            _key: 'js_thomas_lee',\n            name: 'Thomas Lee',\n            email: '<EMAIL>',\n            currentTitle: 'Data Scientist',\n            experience: 5,\n            location: 'Chicago, IL',\n            skills: [\n                'machine_learning',\n                'python',\n                'tensorflow',\n                'product_mgmt'\n            ],\n            skillLevels: {\n                machine_learning: 8,\n                python: 9,\n                tensorflow: 7,\n                product_mgmt: 5\n            },\n            desiredRole: 'Senior Data Scientist',\n            salaryExpectation: 130000,\n            remote: true,\n            bio: 'Data scientist with business impact focus'\n        }\n    ]\n};\n// Function to wipe and seed database\nasync function seedDatabase() {\n    try {\n        console.log('🗄️ Connecting to database...');\n        const { db, collections } = await (0,_lib_db_js__WEBPACK_IMPORTED_MODULE_0__.initDb)();\n        console.log('🧹 Clearing existing data...');\n        // Clear all collections\n        const collectionNames = Object.keys(collections);\n        for (const collectionName of collectionNames){\n            try {\n                await collections[collectionName].truncate();\n                console.log(`   ✅ Cleared ${collectionName}`);\n            } catch (error) {\n                console.log(`   ⚠️ Collection ${collectionName} doesn't exist, creating...`);\n                await collections[collectionName].create();\n            }\n        }\n        console.log('🏢 Seeding companies...');\n        for (const company of mockData.companies){\n            await collections.companies.save(company);\n        }\n        console.log('👔 Seeding hiring authorities...');\n        for (const authority of mockData.hiringAuthorities){\n            await collections.hiringAuthorities.save(authority);\n        }\n        console.log('🛠️ Seeding skills...');\n        for (const skill of mockData.skills){\n            await collections.skills.save(skill);\n        }\n        console.log('👥 Seeding job seekers...');\n        for (const jobSeeker of mockData.jobSeekers){\n            await collections.jobSeekers.save(jobSeeker);\n        }\n        console.log('🎯 Generating authority matches...');\n        const matches = await (0,_lib_matchingAlgorithm_js__WEBPACK_IMPORTED_MODULE_1__.generateAllMatches)(mockData.jobSeekers, mockData.hiringAuthorities, mockData.companies);\n        console.log(`   Generated ${matches.length} potential matches`);\n        for (const match of matches){\n            await collections.matches.save(match);\n        }\n        console.log('✅ Database seeded successfully!');\n        console.log(`   📊 ${mockData.companies.length} companies`);\n        console.log(`   👔 ${mockData.hiringAuthorities.length} hiring authorities`);\n        console.log(`   👥 ${mockData.jobSeekers.length} job seekers`);\n        console.log(`   🛠️ ${mockData.skills.length} skills`);\n        console.log(`   🎯 ${matches.length} authority matches`);\n        return {\n            success: true,\n            message: 'Database seeded successfully with authority matches',\n            stats: {\n                companies: mockData.companies.length,\n                hiringAuthorities: mockData.hiringAuthorities.length,\n                jobSeekers: mockData.jobSeekers.length,\n                skills: mockData.skills.length,\n                matches: matches.length\n            }\n        };\n    } catch (error) {\n        console.error('❌ Error seeding database:', error);\n        return {\n            success: false,\n            error: error.message\n        };\n    }\n}\n// Run if called directly\nif (\"file:///Users/<USER>/Documents/workspace/candid-connections/scripts/seedDatabase.js\" === `file://${process.argv[1]}`) {\n    seedDatabase().then((result)=>{\n        console.log('Seeding complete:', result);\n        process.exit(0);\n    }).catch((error)=>{\n        console.error('Seeding failed:', error);\n        process.exit(1);\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./scripts/seedDatabase.js\n");

/***/ }),

/***/ "arangojs":
/*!***************************!*\
  !*** external "arangojs" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("arangojs");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fseed-database&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fseed-database.js&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();