"use strict";(()=>{var e={};e.id=586,e.ids=[586],e.modules={3480:(e,i,r)=>{e.exports=r(5600)},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6435:(e,i)=>{Object.defineProperty(i,"M",{enumerable:!0,get:function(){return function e(i,r){return r in i?i[r]:"then"in i&&"function"==typeof i.then?i.then(i=>e(i,r)):"function"==typeof i&&"default"===r?i:void 0}}})},6830:(e,i,r)=>{r.r(i),r.d(i,{config:()=>h,default:()=>p,routeModule:()=>u});var o={};r.r(o),r.d(o,{default:()=>m});var t=r(3480),a=r(8667),n=r(6435),s=r(7457);async function l(e,i,r){let o=[];for(let t of e)for(let e of i){let i=r.find(i=>`companies/${i._key}`===e.companyId);if(!i)continue;let a=function(e,i,r){var o;let t=0,a=[],n="",s=function(e,i,r){let o=r.employeeCount,t=i.level;e.experience;let a=0,n=[],s="";return o<100?"C-Suite"===t?(a=95,n.push("Perfect startup match - direct access to decision maker"),s="Perfect - C-Suite authority for startup environment"):(a=60,n.push("Startup prefers C-Suite hiring decisions"),s="Suboptimal - Startup should route to C-Suite"):o>=100&&o<=1e3?"Executive"===t||"Director"===t?(a=90,n.push("Ideal mid-size company authority level"),s="Perfect - Executive/Director level for mid-size company"):"C-Suite"===t?(a=75,n.push("C-Suite accessible but may delegate to directors"),s="Good - C-Suite may delegate to department heads"):(a=65,n.push("Manager level appropriate for specific roles"),s="Acceptable - Manager level for specialized positions"):"Director"===t||"Manager"===t?(a=85,n.push("Appropriate enterprise hierarchy level"),s="Perfect - Director/Manager level for enterprise"):"Executive"===t?(a=70,n.push("Executive level may be too high for initial contact"),s="Good - Executive may route to appropriate director"):(a=50,n.push("C-Suite rarely involved in individual hiring"),s="Poor - C-Suite too high for enterprise individual hiring"),{score:a,reasons:n,hierarchyMatch:s}}(e,i,r);t+=.3*s.score,a.push(...s.reasons),n=s.hierarchyMatch;let l=function(e,i){let r=e.skills||[],o=i.skillsLookingFor||[],t=[],a=0,n=[];for(let i of r)o.includes(i)&&(t.push(i),a+=10*(e.skillLevels?.[i]||5));if(o.length>0){let e=t.length/o.length*100;a=Math.min(a/t.length||0,100)*(e/100)}return t.length>0?(n.push(`${t.length}/${o.length} required skills match`),t.length>=3&&n.push("Strong technical skill alignment")):n.push("Limited skill overlap - may need training"),{score:a,reasons:n,matchingSkills:t}}(e,i);t+=.4*l.score,a.push(...l.reasons);let c=function(e,i){let r=e.experience,o=(i.preferredExperience||"3-8 years").match(/(\d+)-(\d+)/);if(!o)return{score:50,reasons:["Experience requirements unclear"]};let t=parseInt(o[1]),a=parseInt(o[2]),n=0,s=[];if(r>=t&&r<=a)n=90,s.push(`Experience (${r} years) perfectly matches requirements`);else if(r<t){let e=t-r;n=Math.max(40,90-15*e),s.push(`${e} years below preferred minimum experience`)}else{let e=r-a;n=Math.max(60,90-10*e),s.push(`${e} years above preferred maximum - may be overqualified`)}return{score:n,reasons:s}}(e,i);t+=.2*c.score,a.push(...c.reasons);let d=function(e){let i=e.hiringPower,r=e.decisionMaker,o=0,t=[];switch(i){case"Ultimate":o=100,t.push("Ultimate hiring authority - can make immediate decisions");break;case"High":o=85,t.push("High hiring authority - minimal approval needed");break;case"Medium":o=70,t.push("Medium hiring authority - may need additional approvals");break;default:o=50,t.push("Limited hiring authority")}return r&&(o=Math.min(100,o+10),t.push("Direct decision maker")),{score:o,reasons:t}}(i);return t+=.1*d.score,a.push(...d.reasons),{score:Math.round(t),matchReasons:a.slice(0,4),hierarchyMatch:n,connectionStrength:(o=t)>=85?"Strong":o>=70?"Medium":o>=55?"Weak":"Poor"}}(t,e,i);a.score>=50&&o.push({_key:`match_${t._key}_${e._key}`,jobSeekerId:`jobSeekers/${t._key}`,hiringAuthorityId:`hiringAuthorities/${e._key}`,companyId:`companies/${i._key}`,score:a.score,matchReasons:a.matchReasons,hierarchyMatch:a.hierarchyMatch,connectionStrength:a.connectionStrength,status:a.score>=80?"recommended":"potential",createdAt:new Date().toISOString()})}return o.sort((e,i)=>i.score-e.score)}let c={companies:[{_key:"startup_techflow",name:"TechFlow Innovations",industry:"FinTech",size:"Startup",employeeCount:45,location:"San Francisco, CA",description:"Blockchain-based payment solutions startup",founded:"2022",website:"https://techflow.com"},{_key:"midsize_cloudtech",name:"CloudTech Solutions",industry:"Technology",size:"Mid-size",employeeCount:350,location:"Austin, TX",description:"Cloud infrastructure and DevOps services",founded:"2018",website:"https://cloudtech.com"},{_key:"enterprise_megacorp",name:"MegaCorp Industries",industry:"Manufacturing",size:"Enterprise",employeeCount:2500,location:"Detroit, MI",description:"Industrial automation and robotics",founded:"1995",website:"https://megacorp.com"},{_key:"midsize_designstudio",name:"Design Studio Pro",industry:"Design",size:"Mid-size",employeeCount:180,location:"New York, NY",description:"Digital design and user experience agency",founded:"2015",website:"https://designstudio.com"},{_key:"startup_aiventures",name:"AI Ventures",industry:"Artificial Intelligence",size:"Startup",employeeCount:28,location:"Seattle, WA",description:"Machine learning and AI consulting",founded:"2023",website:"https://aiventures.com"}],hiringAuthorities:[{_key:"ceo_techflow",name:"Jennifer Martinez",role:"CEO & Founder",level:"C-Suite",companyId:"companies/startup_techflow",email:"<EMAIL>",hiringPower:"Ultimate",decisionMaker:!0,skillsLookingFor:["Leadership","FinTech","Blockchain","Full Stack","Startup Experience"],preferredExperience:"3-8 years",bio:"Serial entrepreneur with 3 successful exits in FinTech"},{_key:"cto_techflow",name:"David Kim",role:"CTO",level:"C-Suite",companyId:"companies/startup_techflow",email:"<EMAIL>",hiringPower:"Ultimate",decisionMaker:!0,skillsLookingFor:["React","Node.js","Blockchain","Solidity","AWS"],preferredExperience:"4-10 years",bio:"Former Google engineer specializing in distributed systems"},{_key:"vp_eng_cloudtech",name:"Sarah Wilson",role:"VP Engineering",level:"Executive",companyId:"companies/midsize_cloudtech",email:"<EMAIL>",hiringPower:"High",decisionMaker:!0,skillsLookingFor:["Kubernetes","Docker","Terraform","Python","Leadership"],preferredExperience:"5-12 years",bio:"Infrastructure expert with 15 years in cloud technologies"},{_key:"dir_product_cloudtech",name:"Mike Chen",role:"Director of Product",level:"Director",companyId:"companies/midsize_cloudtech",email:"<EMAIL>",hiringPower:"High",decisionMaker:!1,skillsLookingFor:["Product Management","UX/UI","Analytics","Agile","Cloud Platforms"],preferredExperience:"4-8 years",bio:"Product leader focused on developer experience"},{_key:"hr_director_megacorp",name:"Lisa Thompson",role:"HR Director",level:"Director",companyId:"companies/enterprise_megacorp",email:"<EMAIL>",hiringPower:"Medium",decisionMaker:!1,skillsLookingFor:["Operations","Six Sigma","Project Management","Manufacturing"],preferredExperience:"3-10 years",bio:"HR professional specializing in technical recruitment"},{_key:"eng_manager_megacorp",name:"Robert Davis",role:"Engineering Manager",level:"Manager",companyId:"companies/enterprise_megacorp",email:"<EMAIL>",hiringPower:"Medium",decisionMaker:!1,skillsLookingFor:["C++","Embedded Systems","Robotics","PLC Programming"],preferredExperience:"5-15 years",bio:"Robotics engineer with industrial automation expertise"},{_key:"creative_director_design",name:"Emma Rodriguez",role:"Creative Director",level:"Executive",companyId:"companies/midsize_designstudio",email:"<EMAIL>",hiringPower:"High",decisionMaker:!0,skillsLookingFor:["Figma","User Research","Design Systems","Prototyping","Leadership"],preferredExperience:"4-10 years",bio:"Award-winning designer with Fortune 500 client experience"},{_key:"founder_ai",name:"Dr. Alex Chen",role:"Founder & Chief Scientist",level:"C-Suite",companyId:"companies/startup_aiventures",email:"<EMAIL>",hiringPower:"Ultimate",decisionMaker:!0,skillsLookingFor:["Machine Learning","Python","TensorFlow","Research","PhD"],preferredExperience:"3-12 years",bio:"Former Stanford AI researcher with 50+ publications"}],skills:[{_key:"react",name:"React",category:"Frontend",demand:"High"},{_key:"nodejs",name:"Node.js",category:"Backend",demand:"High"},{_key:"python",name:"Python",category:"Backend",demand:"Very High"},{_key:"typescript",name:"TypeScript",category:"Frontend",demand:"High"},{_key:"kubernetes",name:"Kubernetes",category:"DevOps",demand:"High"},{_key:"docker",name:"Docker",category:"DevOps",demand:"High"},{_key:"terraform",name:"Terraform",category:"Infrastructure",demand:"Medium"},{_key:"aws",name:"AWS",category:"Cloud",demand:"Very High"},{_key:"blockchain",name:"Blockchain",category:"Emerging",demand:"Medium"},{_key:"solidity",name:"Solidity",category:"Blockchain",demand:"Low"},{_key:"figma",name:"Figma",category:"Design",demand:"High"},{_key:"user_research",name:"User Research",category:"UX",demand:"Medium"},{_key:"machine_learning",name:"Machine Learning",category:"AI",demand:"Very High"},{_key:"tensorflow",name:"TensorFlow",category:"AI",demand:"High"},{_key:"cpp",name:"C++",category:"Systems",demand:"Medium"},{_key:"embedded",name:"Embedded Systems",category:"Hardware",demand:"Medium"},{_key:"robotics",name:"Robotics",category:"Engineering",demand:"Low"},{_key:"leadership",name:"Leadership",category:"Soft Skills",demand:"High"},{_key:"product_mgmt",name:"Product Management",category:"Business",demand:"High"},{_key:"agile",name:"Agile",category:"Methodology",demand:"High"}],jobSeekers:[{_key:"js_sarah_chen",name:"Sarah Chen",email:"<EMAIL>",currentTitle:"Senior Frontend Developer",experience:6,location:"San Francisco, CA",skills:["react","typescript","nodejs","aws"],skillLevels:{react:9,typescript:8,nodejs:7,aws:6},desiredRole:"Lead Frontend Engineer",salaryExpectation:14e4,remote:!0,bio:"Passionate frontend developer with startup experience"},{_key:"js_marcus_johnson",name:"Marcus Johnson",email:"<EMAIL>",currentTitle:"Full Stack Developer",experience:4,location:"Austin, TX",skills:["python","react","aws","docker"],skillLevels:{python:8,react:7,aws:6,docker:7},desiredRole:"Senior Full Stack Developer",salaryExpectation:12e4,remote:!1,bio:"Full stack developer with cloud expertise"},{_key:"js_emily_rodriguez",name:"Emily Rodriguez",email:"<EMAIL>",currentTitle:"UX Designer",experience:5,location:"New York, NY",skills:["figma","user_research","leadership"],skillLevels:{figma:9,user_research:8,leadership:6},desiredRole:"Senior UX Designer",salaryExpectation:11e4,remote:!0,bio:"User-centered designer with Fortune 500 experience"},{_key:"js_david_park",name:"David Park",email:"<EMAIL>",currentTitle:"DevOps Engineer",experience:7,location:"Seattle, WA",skills:["kubernetes","docker","terraform","aws","python"],skillLevels:{kubernetes:9,docker:9,terraform:8,aws:8,python:7},desiredRole:"Senior DevOps Engineer",salaryExpectation:135e3,remote:!0,bio:"Infrastructure automation specialist"},{_key:"js_lisa_wang",name:"Lisa Wang",email:"<EMAIL>",currentTitle:"Machine Learning Engineer",experience:3,location:"San Francisco, CA",skills:["machine_learning","python","tensorflow"],skillLevels:{machine_learning:8,python:9,tensorflow:7},desiredRole:"Senior ML Engineer",salaryExpectation:15e4,remote:!0,bio:"PhD in Computer Science, AI research background"},{_key:"js_james_wilson",name:"James Wilson",email:"<EMAIL>",currentTitle:"Blockchain Developer",experience:2,location:"Austin, TX",skills:["blockchain","solidity","nodejs","react"],skillLevels:{blockchain:7,solidity:8,nodejs:6,react:5},desiredRole:"Senior Blockchain Developer",salaryExpectation:13e4,remote:!0,bio:"Early blockchain adopter with DeFi experience"},{_key:"js_anna_kim",name:"Anna Kim",email:"<EMAIL>",currentTitle:"Product Manager",experience:5,location:"New York, NY",skills:["product_mgmt","agile","user_research","leadership"],skillLevels:{product_mgmt:8,agile:7,user_research:6,leadership:7},desiredRole:"Senior Product Manager",salaryExpectation:125e3,remote:!1,bio:"Product leader with B2B SaaS experience"},{_key:"js_robert_davis",name:"Robert Davis",email:"<EMAIL>",currentTitle:"Embedded Systems Engineer",experience:8,location:"Detroit, MI",skills:["cpp","embedded","robotics"],skillLevels:{cpp:9,embedded:8,robotics:7},desiredRole:"Senior Embedded Engineer",salaryExpectation:115e3,remote:!1,bio:"Automotive industry veteran with robotics expertise"},{_key:"js_maria_gonzalez",name:"Maria Gonzalez",email:"<EMAIL>",currentTitle:"Frontend Developer",experience:3,location:"Los Angeles, CA",skills:["react","typescript","figma"],skillLevels:{react:7,typescript:6,figma:5},desiredRole:"Senior Frontend Developer",salaryExpectation:105e3,remote:!0,bio:"Creative developer with design background"},{_key:"js_kevin_lee",name:"Kevin Lee",email:"<EMAIL>",currentTitle:"Backend Developer",experience:4,location:"Chicago, IL",skills:["python","nodejs","aws","docker"],skillLevels:{python:8,nodejs:7,aws:6,docker:6},desiredRole:"Senior Backend Developer",salaryExpectation:115e3,remote:!0,bio:"API design specialist with microservices experience"},{_key:"js_jennifer_brown",name:"Jennifer Brown",email:"<EMAIL>",currentTitle:"Senior Product Designer",experience:6,location:"San Francisco, CA",skills:["figma","user_research","leadership","product_mgmt"],skillLevels:{figma:8,user_research:9,leadership:7,product_mgmt:6},desiredRole:"Design Director",salaryExpectation:145e3,remote:!0,bio:"Design leader with startup and enterprise experience"},{_key:"js_michael_zhang",name:"Michael Zhang",email:"<EMAIL>",currentTitle:"Cloud Architect",experience:9,location:"Austin, TX",skills:["aws","kubernetes","terraform","python","leadership"],skillLevels:{aws:9,kubernetes:8,terraform:9,python:7,leadership:8},desiredRole:"Principal Cloud Architect",salaryExpectation:16e4,remote:!1,bio:"Cloud infrastructure expert with enterprise scaling experience"},{_key:"js_sophia_martinez",name:"Sophia Martinez",email:"<EMAIL>",currentTitle:"AI Research Scientist",experience:4,location:"Seattle, WA",skills:["machine_learning","python","tensorflow","leadership"],skillLevels:{machine_learning:9,python:9,tensorflow:8,leadership:5},desiredRole:"Senior AI Scientist",salaryExpectation:17e4,remote:!0,bio:"PhD in Machine Learning with published research"},{_key:"js_alex_thompson",name:"Alex Thompson",email:"<EMAIL>",currentTitle:"Full Stack Engineer",experience:5,location:"New York, NY",skills:["react","nodejs","python","aws","leadership"],skillLevels:{react:8,nodejs:8,python:7,aws:6,leadership:6},desiredRole:"Engineering Manager",salaryExpectation:14e4,remote:!1,bio:"Technical leader transitioning to management"},{_key:"js_rachel_kim",name:"Rachel Kim",email:"<EMAIL>",currentTitle:"Robotics Engineer",experience:6,location:"Detroit, MI",skills:["cpp","robotics","embedded","python"],skillLevels:{cpp:8,robotics:9,embedded:7,python:6},desiredRole:"Senior Robotics Engineer",salaryExpectation:125e3,remote:!1,bio:"Autonomous systems specialist with manufacturing background"},{_key:"js_daniel_garcia",name:"Daniel Garcia",email:"<EMAIL>",currentTitle:"Blockchain Architect",experience:4,location:"San Francisco, CA",skills:["blockchain","solidity","nodejs","python","leadership"],skillLevels:{blockchain:9,solidity:9,nodejs:7,python:6,leadership:5},desiredRole:"Lead Blockchain Developer",salaryExpectation:155e3,remote:!0,bio:"DeFi protocol architect with smart contract expertise"},{_key:"js_amanda_wilson",name:"Amanda Wilson",email:"<EMAIL>",currentTitle:"Senior UX Researcher",experience:7,location:"New York, NY",skills:["user_research","figma","product_mgmt","leadership"],skillLevels:{user_research:9,figma:6,product_mgmt:7,leadership:8},desiredRole:"Head of UX Research",salaryExpectation:135e3,remote:!0,bio:"Research leader with quantitative and qualitative expertise"},{_key:"js_carlos_rodriguez",name:"Carlos Rodriguez",email:"<EMAIL>",currentTitle:"DevOps Manager",experience:8,location:"Austin, TX",skills:["kubernetes","docker","terraform","aws","leadership"],skillLevels:{kubernetes:9,docker:8,terraform:8,aws:9,leadership:8},desiredRole:"Director of Infrastructure",salaryExpectation:15e4,remote:!1,bio:"Infrastructure leader with team management experience"},{_key:"js_natalie_chen",name:"Natalie Chen",email:"<EMAIL>",currentTitle:"Frontend Architect",experience:7,location:"Los Angeles, CA",skills:["react","typescript","nodejs","leadership"],skillLevels:{react:9,typescript:9,nodejs:6,leadership:7},desiredRole:"Principal Frontend Engineer",salaryExpectation:145e3,remote:!0,bio:"Frontend architecture specialist with performance optimization expertise"},{_key:"js_thomas_lee",name:"Thomas Lee",email:"<EMAIL>",currentTitle:"Data Scientist",experience:5,location:"Chicago, IL",skills:["machine_learning","python","tensorflow","product_mgmt"],skillLevels:{machine_learning:8,python:9,tensorflow:7,product_mgmt:5},desiredRole:"Senior Data Scientist",salaryExpectation:13e4,remote:!0,bio:"Data scientist with business impact focus"}]};async function d(){try{console.log("\uD83D\uDDC4️ Connecting to database...");let{db:e,collections:i}=await (0,s.X)();for(let e of(console.log("\uD83E\uDDF9 Clearing existing data..."),Object.keys(i)))try{await i[e].truncate(),console.log(`   ✅ Cleared ${e}`)}catch(r){console.log(`   ⚠️ Collection ${e} doesn't exist, creating...`),await i[e].create()}for(let e of(console.log("\uD83C\uDFE2 Seeding companies..."),c.companies))await i.companies.save(e);for(let e of(console.log("\uD83D\uDC54 Seeding hiring authorities..."),c.hiringAuthorities))await i.hiringAuthorities.save(e);for(let e of(console.log("\uD83D\uDEE0️ Seeding skills..."),c.skills))await i.skills.save(e);for(let e of(console.log("\uD83D\uDC65 Seeding job seekers..."),c.jobSeekers))await i.jobSeekers.save(e);console.log("\uD83C\uDFAF Generating authority matches...");let r=await l(c.jobSeekers,c.hiringAuthorities,c.companies);for(let e of(console.log(`   Generated ${r.length} potential matches`),r))await i.matches.save(e);return console.log("✅ Database seeded successfully!"),console.log(`   📊 ${c.companies.length} companies`),console.log(`   👔 ${c.hiringAuthorities.length} hiring authorities`),console.log(`   👥 ${c.jobSeekers.length} job seekers`),console.log(`   🛠️ ${c.skills.length} skills`),console.log(`   🎯 ${r.length} authority matches`),{success:!0,message:"Database seeded successfully with authority matches",stats:{companies:c.companies.length,hiringAuthorities:c.hiringAuthorities.length,jobSeekers:c.jobSeekers.length,skills:c.skills.length,matches:r.length}}}catch(e){return console.error("❌ Error seeding database:",e),{success:!1,error:e.message}}}async function m(e,i){if("POST"!==e.method)return i.status(405).json({error:"Method not allowed"});try{console.log("\uD83C\uDF31 Starting database seeding...");let e=await d();if(e.success)return i.status(200).json({message:"Database seeded successfully",stats:e.stats});return i.status(500).json({error:"Failed to seed database",details:e.error})}catch(e){return console.error("Error in seed API:",e),i.status(500).json({error:"Internal server error",details:e.message})}}"file:///Users/<USER>/Documents/workspace/candid-connections/scripts/seedDatabase.js"==`file://${process.argv[1]}`&&d().then(e=>{console.log("Seeding complete:",e),process.exit(0)}).catch(e=>{console.error("Seeding failed:",e),process.exit(1)});let p=(0,n.M)(o,"default"),h=(0,n.M)(o,"config"),u=new t.PagesAPIRouteModule({definition:{kind:a.A.PAGES_API,page:"/api/seed-database",pathname:"/api/seed-database",bundlePath:"",filename:""},userland:o})},7457:(e,i,r)=>{r.d(i,{A:()=>n,X:()=>a});let o=require("arangojs"),t=async e=>({jobSeekers:e.collection("jobSeekers"),companies:e.collection("companies"),hiringAuthorities:e.collection("hiringAuthorities"),positions:e.collection("positions"),skills:e.collection("skills"),matches:e.collection("matches"),works_for:e.collection("works_for"),employs:e.collection("employs"),posts:e.collection("posts"),requires:e.collection("requires"),has_skill:e.collection("has_skill"),matched_to:e.collection("matched_to"),reports_to:e.collection("reports_to")}),a=async()=>{let e=process.env.ARANGODB_DB_NAME||"candid_connections",i=new o.Database({url:process.env.ARANGODB_URL||"http://localhost:8529",databaseName:"_system",auth:{username:process.env.ARANGODB_USERNAME||"root",password:process.env.ARANGODB_PASSWORD||""}});try{await i.createDatabase(e),console.log(`✅ Created database: ${e}`)}catch(i){1207===i.errorNum?console.log(`✅ Database ${e} already exists`):console.log("Database creation note:",i.message)}let r=new o.Database({url:process.env.ARANGODB_URL||"http://localhost:8529",databaseName:e,auth:{username:process.env.ARANGODB_USERNAME||"root",password:process.env.ARANGODB_PASSWORD||""}}),a=await t(r);return{db:r,collections:a}},n=a},8667:(e,i)=>{Object.defineProperty(i,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})}};var i=require("../../webpack-api-runtime.js");i.C(e);var r=i(i.s=6830);module.exports=r})();