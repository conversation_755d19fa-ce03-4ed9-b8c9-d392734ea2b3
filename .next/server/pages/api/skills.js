"use strict";(()=>{var e={};e.id=349,e.ids=[349],e.modules={560:(e,s,t)=>{t.r(s),t.d(s,{config:()=>T,default:()=>p,routeModule:()=>R});var i={};t.r(i),t.d(i,{default:()=>n,getSkillAnalytics:()=>E});var l=t(3480),o=t(8667),r=t(6435),a=t(7457);async function n(e,s){let{method:t}=e;try{let{db:i,collections:l}=await (0,a.X)();switch(t){case"GET":await d(e,s,i,l);break;case"POST":await c(e,s,i,l);break;case"PUT":await u(e,s,i,l);break;case"DELETE":await k(e,s,i,l);break;default:s.setHeader("Allow",["GET","POST","PUT","DELETE"]),s.status(405).end(`Method ${t} Not Allowed`)}}catch(e){console.error("API Error:",e),s.status(500).json({error:"Internal Server Error"})}}async function d(e,s,t,i){let{id:l,category:o,limit:r=50,offset:a=0,includeStats:n=!1}=e.query;try{if(l){let e=`
        LET skill = DOCUMENT('skills', @id)
        LET jobSeekers = (
          FOR edge IN has_skill
            FILTER edge._to == skill._id
            RETURN DOCUMENT(edge._from)
        )
        LET positions = (
          FOR edge IN requires
            FILTER edge._to == skill._id
            RETURN DOCUMENT(edge._from)
        )
        LET relatedSkills = (
          FOR js IN jobSeekers
            FOR edge IN has_skill
              FILTER edge._from == js._id AND edge._to != skill._id
              LET relatedSkill = DOCUMENT(edge._to)
              COLLECT skillName = relatedSkill.name WITH COUNT INTO frequency
              SORT frequency DESC
              LIMIT 10
              RETURN skillName
        )
        RETURN {
          id: skill._key,
          name: skill.name,
          category: skill.category,
          description: skill.description,
          icon: skill.icon,
          jobSeekers: LENGTH(jobSeekers),
          openPositions: LENGTH(positions[* FILTER CURRENT.status == 'active']),
          totalPositions: LENGTH(positions),
          demand: LENGTH(positions[* FILTER CURRENT.status == 'active']) > 0 ? 
            MIN([100, (LENGTH(positions[* FILTER CURRENT.status == 'active']) / LENGTH(jobSeekers)) * 100]) : 0,
          supply: LENGTH(jobSeekers),
          relatedSkills: relatedSkills,
          averageSalary: AVG(positions[* FILTER CURRENT.salary != null].salary),
          growth: skill.growth || 0
        }
      `,i=await t.query(e,{id:l}),o=await i.all();if(0===o.length)return s.status(404).json({error:"Skill not found"});s.status(200).json(o[0])}else{let e=`
        FOR skill IN skills
      `,i={limit:parseInt(r),offset:parseInt(a)},l=[];o&&(l.push("skill.category == @category"),i.category=o),l.length>0&&(e+=` FILTER ${l.join(" AND ")}`),"true"===n?e+=`
          LET jobSeekers = (
            FOR edge IN has_skill
              FILTER edge._to == skill._id
              RETURN 1
          )
          LET positions = (
            FOR edge IN requires
              FILTER edge._to == skill._id
              LET position = DOCUMENT(edge._from)
              RETURN position
          )
          LET activePositions = positions[* FILTER CURRENT.status == 'active']
          SORT skill.name ASC
          LIMIT @offset, @limit
          RETURN {
            id: skill._key,
            name: skill.name,
            category: skill.category,
            description: skill.description,
            icon: skill.icon,
            jobSeekers: LENGTH(jobSeekers),
            openPositions: LENGTH(activePositions),
            demand: LENGTH(activePositions) > 0 ? 
              MIN([100, (LENGTH(activePositions) / MAX([1, LENGTH(jobSeekers)])) * 100]) : 0,
            supply: LENGTH(jobSeekers),
            growth: skill.growth || 0
          }
        `:e+=`
          SORT skill.name ASC
          LIMIT @offset, @limit
          RETURN {
            id: skill._key,
            name: skill.name,
            category: skill.category,
            description: skill.description,
            icon: skill.icon
          }
        `;let d=await t.query(e,i),c=await d.all();s.status(200).json(c)}}catch(e){console.error("Error fetching skills:",e),s.status(500).json({error:"Failed to fetch skills"})}}async function c(e,s,t,i){let{name:l,category:o,description:r,icon:a}=e.body;if(!l||!o)return s.status(400).json({error:"Missing required fields: name, category"});try{let e=`
      FOR skill IN skills
        FILTER LOWER(skill.name) == LOWER(@name)
        RETURN skill
    `,n=await t.query(e,{name:l});if((await n.all()).length>0)return s.status(409).json({error:"Skill with this name already exists"});let d={name:l,category:o,description:r||"",icon:a||"\uD83D\uDD27",growth:0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},c=await i.skills.save(d);s.status(201).json({id:c._key,...d})}catch(e){console.error("Error creating skill:",e),s.status(500).json({error:"Failed to create skill"})}}async function u(e,s,t,i){let{id:l}=e.query,o=e.body;if(!l)return s.status(400).json({error:"Skill ID is required"});try{let e={...o,updatedAt:new Date().toISOString()};delete e.id,delete e.createdAt;let t=await i.skills.update(l,e);if(!t._key)return s.status(404).json({error:"Skill not found"});s.status(200).json({id:t._key,...e})}catch(e){console.error("Error updating skill:",e),s.status(500).json({error:"Failed to update skill"})}}async function k(e,s,t,i){let{id:l}=e.query;if(!l)return s.status(400).json({error:"Skill ID is required"});try{let e=`
      LET hasSkillEdges = (
        FOR edge IN has_skill
          FILTER edge._to == @skillId
          RETURN edge
      )
      LET requiresEdges = (
        FOR edge IN requires
          FILTER edge._to == @skillId
          RETURN edge
      )
      RETURN {
        jobSeekers: LENGTH(hasSkillEdges),
        positions: LENGTH(requiresEdges)
      }
    `,o=await t.query(e,{skillId:`skills/${l}`}),r=await o.all();if(r[0].jobSeekers>0||r[0].positions>0)return s.status(400).json({error:`Cannot delete skill that is being used by ${r[0].jobSeekers} job seekers and ${r[0].positions} positions`});await i.skills.remove(l),s.status(200).json({message:"Skill deleted successfully"})}catch(e){if(1202===e.errorNum)return s.status(404).json({error:"Skill not found"});console.error("Error deleting skill:",e),s.status(500).json({error:"Failed to delete skill"})}}async function E(e,s,t,i){try{let e=`
      FOR skill IN skills
        LET jobSeekers = (
          FOR edge IN has_skill
            FILTER edge._to == skill._id
            RETURN 1
        )
        LET positions = (
          FOR edge IN requires
            FILTER edge._to == skill._id
            LET position = DOCUMENT(edge._from)
            RETURN position
        )
        LET activePositions = positions[* FILTER CURRENT.status == 'active']
        LET demand = LENGTH(activePositions)
        LET supply = LENGTH(jobSeekers)
        
        RETURN {
          skill: skill.name,
          category: skill.category,
          demand: demand,
          supply: supply,
          ratio: supply > 0 ? demand / supply : 0,
          gap: demand - supply
        }
    `,i=await t.query(e),l=await i.all(),o={totalSkills:l.length,highDemandSkills:l.filter(e=>e.demand>e.supply).length,oversuppliedSkills:l.filter(e=>e.supply>2*e.demand).length,balancedSkills:l.filter(e=>2>=Math.abs(e.demand-e.supply)).length,topDemandSkills:l.sort((e,s)=>s.demand-e.demand).slice(0,10),topSupplySkills:l.sort((e,s)=>s.supply-e.supply).slice(0,10),biggestGaps:l.sort((e,s)=>s.gap-e.gap).slice(0,10)};s.status(200).json(o)}catch(e){console.error("Error fetching skill analytics:",e),s.status(500).json({error:"Failed to fetch skill analytics"})}}let p=(0,r.M)(i,"default"),T=(0,r.M)(i,"config"),R=new l.PagesAPIRouteModule({definition:{kind:o.A.PAGES_API,page:"/api/skills",pathname:"/api/skills",bundlePath:"",filename:""},userland:i})},3480:(e,s,t)=>{e.exports=t(5600)},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6435:(e,s)=>{Object.defineProperty(s,"M",{enumerable:!0,get:function(){return function e(s,t){return t in s?s[t]:"then"in s&&"function"==typeof s.then?s.then(s=>e(s,t)):"function"==typeof s&&"default"===t?s:void 0}}})},7457:(e,s,t)=>{t.d(s,{A:()=>a,X:()=>r});let i=require("arangojs"),l=()=>new i.Database({url:process.env.ARANGODB_URL||"http://localhost:8529",databaseName:process.env.ARANGODB_DB_NAME||"candid_connections",auth:{username:process.env.ARANGODB_USERNAME||"root",password:process.env.ARANGODB_PASSWORD||""}}),o=async e=>({jobSeekers:e.collection("jobSeekers"),companies:e.collection("companies"),hiringAuthorities:e.collection("hiringAuthorities"),positions:e.collection("positions"),skills:e.collection("skills"),matches:e.collection("matches"),works_for:e.collection("works_for"),employs:e.collection("employs"),posts:e.collection("posts"),requires:e.collection("requires"),has_skill:e.collection("has_skill"),matched_to:e.collection("matched_to"),reports_to:e.collection("reports_to")}),r=async()=>{let e=l();try{await e.createDatabase(process.env.ARANGODB_DB_NAME||"candid_connections")}catch(e){e.message.includes("duplicate name")||console.log("Database creation note:",e.message)}let s=await o(e);return{db:e,collections:s}},a=r},8667:(e,s)=>{Object.defineProperty(s,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})}};var s=require("../../webpack-api-runtime.js");s.C(e);var t=s(s.s=560);module.exports=t})();