/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/hiring-authorities";
exports.ids = ["pages/hiring-authorities"];
exports.modules = {

/***/ "(pages-dir-node)/./components/Layout.js":
/*!******************************!*\
  !*** ./components/Layout.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Layout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Navigation */ \"(pages-dir-node)/./components/Navigation.js\");\n\n\nfunction Layout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Navigation__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Layout.js\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container-app section-padding\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Layout.js\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Layout.js\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL2NvbXBvbmVudHMvTGF5b3V0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXFDO0FBRXRCLFNBQVNDLE9BQU8sRUFBRUMsUUFBUSxFQUFFO0lBQ3pDLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0osbURBQVVBOzs7OzswQkFDWCw4REFBQ0s7Z0JBQUtELFdBQVU7MEJBQWlDRjs7Ozs7Ozs7Ozs7O0FBR3ZEIiwic291cmNlcyI6WyIvVXNlcnMvYnJhZHlnZW9yZ2VuL0RvY3VtZW50cy93b3Jrc3BhY2UvY2FuZGlkLWNvbm5lY3Rpb25zL2NvbXBvbmVudHMvTGF5b3V0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBOYXZpZ2F0aW9uIGZyb20gJy4vTmF2aWdhdGlvbidcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTGF5b3V0KHsgY2hpbGRyZW4gfSkge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLXdoaXRlXCI+XG4gICAgICA8TmF2aWdhdGlvbiAvPlxuICAgICAgPG1haW4gY2xhc3NOYW1lPVwiY29udGFpbmVyLWFwcCBzZWN0aW9uLXBhZGRpbmdcIj57Y2hpbGRyZW59PC9tYWluPlxuICAgIDwvZGl2PlxuICApXG59Il0sIm5hbWVzIjpbIk5hdmlnYXRpb24iLCJMYXlvdXQiLCJjaGlsZHJlbiIsImRpdiIsImNsYXNzTmFtZSIsIm1haW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/Layout.js\n");

/***/ }),

/***/ "(pages-dir-node)/./components/Navigation.js":
/*!**********************************!*\
  !*** ./components/Navigation.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(pages-dir-node)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-node)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nfunction Navigation() {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const navItems = [\n        {\n            name: 'Dashboard',\n            path: '/',\n            icon: '🏠'\n        },\n        {\n            name: 'Matches',\n            path: '/matches',\n            icon: '🎯'\n        },\n        {\n            name: 'Job Seekers',\n            path: '/job-seekers',\n            icon: '👥'\n        },\n        {\n            name: 'Hiring Authorities',\n            path: '/hiring-authorities',\n            icon: '👔'\n        },\n        {\n            name: 'Companies',\n            path: '/companies',\n            icon: '🏢'\n        },\n        {\n            name: 'Positions',\n            path: '/positions',\n            icon: '📋'\n        },\n        {\n            name: 'Skills',\n            path: '/skills',\n            icon: '🛠️'\n        },\n        {\n            name: 'Visualizations',\n            path: '/visualizations',\n            icon: '📊'\n        },\n        {\n            name: 'Network View',\n            path: '/global-view',\n            icon: '🌐'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-white shadow-soft border-b border-candid-gray-200\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-app\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0 flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/\",\n                                className: \"flex items-center space-x-3 group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 relative group-hover:scale-105 transition-transform duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            viewBox: \"0 0 48 48\",\n                                            className: \"w-full h-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"24\",\n                                                    cy: \"24\",\n                                                    r: \"22\",\n                                                    fill: \"none\",\n                                                    stroke: \"#1e3a8a\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 32,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"24\",\n                                                    cy: \"12\",\n                                                    r: \"3\",\n                                                    fill: \"#00d4ff\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 34,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"36\",\n                                                    cy: \"24\",\n                                                    r: \"3\",\n                                                    fill: \"#00d4ff\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 35,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"24\",\n                                                    cy: \"36\",\n                                                    r: \"3\",\n                                                    fill: \"#00d4ff\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 36,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"12\",\n                                                    cy: \"24\",\n                                                    r: \"3\",\n                                                    fill: \"#00d4ff\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 37,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"24\",\n                                                    cy: \"24\",\n                                                    r: \"4\",\n                                                    fill: \"#1e3a8a\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 38,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                    x1: \"24\",\n                                                    y1: \"15\",\n                                                    x2: \"24\",\n                                                    y2: \"20\",\n                                                    stroke: \"#00d4ff\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 40,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                    x1: \"33\",\n                                                    y1: \"24\",\n                                                    x2: \"28\",\n                                                    y2: \"24\",\n                                                    stroke: \"#00d4ff\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 41,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                    x1: \"24\",\n                                                    y1: \"33\",\n                                                    x2: \"24\",\n                                                    y2: \"28\",\n                                                    stroke: \"#00d4ff\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 42,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                    x1: \"15\",\n                                                    y1: \"24\",\n                                                    x2: \"20\",\n                                                    y2: \"24\",\n                                                    stroke: \"#00d4ff\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 43,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                            lineNumber: 30,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 29,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden sm:block\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold text-secondary-800 group-hover:text-primary-500 transition-colors duration-200\",\n                                                children: \"Candid Connections\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                lineNumber: 47,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-candid-gray-600 -mt-1\",\n                                                children: \"Katra Platform\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                lineNumber: 50,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                lineNumber: 27,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1\",\n                                children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: item.path,\n                                        className: `flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${router.pathname === item.path ? 'nav-link-active' : 'nav-link'}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-base\",\n                                                children: item.icon\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                lineNumber: 68,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                lineNumber: 69,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, item.path, true, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 59,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                lineNumber: 57,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/admin\",\n                                    className: \"btn-outline text-sm py-2 px-4\",\n                                    children: \"⚙️ Admin\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/visualizations\",\n                                    className: \"btn-outline text-sm py-2 px-4\",\n                                    children: \"\\uD83D\\uDCCA Visualize\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"https://portal.candid-connections.com/user/login\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"btn-primary text-sm py-2 px-4\",\n                                    children: \"Portal Login\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),\n                                className: \"p-2 rounded-lg text-candid-navy-600 hover:text-primary-600 hover:bg-primary-50 transition-colors duration-200\",\n                                \"aria-label\": \"Toggle mobile menu\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 108,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M4 6h16M4 12h16M4 18h16\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 110,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                    lineNumber: 106,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this),\n                isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"lg:hidden border-t border-candid-gray-200 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: item.path,\n                                    onClick: ()=>setIsMobileMenuOpen(false),\n                                    className: `flex items-center space-x-3 px-4 py-3 rounded-lg text-base font-medium transition-all duration-200 ${router.pathname === item.path ? 'nav-link-active' : 'nav-link'}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg\",\n                                            children: item.icon\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                            lineNumber: 132,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                            lineNumber: 133,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, item.path, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                    lineNumber: 122,\n                                    columnNumber: 17\n                                }, this)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-4 space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/visualizations\",\n                                        onClick: ()=>setIsMobileMenuOpen(false),\n                                        className: \"block w-full btn-outline text-center\",\n                                        children: \"\\uD83D\\uDCCA Visualize\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 139,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"https://portal.candid-connections.com/user/login\",\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        className: \"block w-full btn-primary text-center\",\n                                        children: \"Portal Login\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 146,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                lineNumber: 138,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                        lineNumber: 120,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                    lineNumber: 119,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/Navigation.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fhiring-authorities&preferredRegion=&absolutePagePath=.%2Fpages%2Fhiring-authorities.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fhiring-authorities&preferredRegion=&absolutePagePath=.%2Fpages%2Fhiring-authorities.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages/module.compiled */ \"(pages-dir-node)/./node_modules/next/dist/server/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(pages-dir-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(pages-dir-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"(pages-dir-node)/./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"(pages-dir-node)/./pages/_app.js\");\n/* harmony import */ var _pages_hiring_authorities_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages/hiring-authorities.js */ \"(pages-dir-node)/./pages/hiring-authorities.js\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_hiring_authorities_js__WEBPACK_IMPORTED_MODULE_5__, 'default'));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_hiring_authorities_js__WEBPACK_IMPORTED_MODULE_5__, 'getStaticProps');\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_hiring_authorities_js__WEBPACK_IMPORTED_MODULE_5__, 'getStaticPaths');\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_hiring_authorities_js__WEBPACK_IMPORTED_MODULE_5__, 'getServerSideProps');\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_hiring_authorities_js__WEBPACK_IMPORTED_MODULE_5__, 'config');\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_hiring_authorities_js__WEBPACK_IMPORTED_MODULE_5__, 'reportWebVitals');\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_hiring_authorities_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticProps');\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_hiring_authorities_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticPaths');\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_hiring_authorities_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticParams');\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_hiring_authorities_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerProps');\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_hiring_authorities_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/hiring-authorities\",\n        pathname: \"/hiring-authorities\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    components: {\n        // default export might not exist when optimized for data only\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_hiring_authorities_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtcm91dGUtbG9hZGVyL2luZGV4LmpzP2tpbmQ9UEFHRVMmcGFnZT0lMkZoaXJpbmctYXV0aG9yaXRpZXMmcHJlZmVycmVkUmVnaW9uPSZhYnNvbHV0ZVBhZ2VQYXRoPS4lMkZwYWdlcyUyRmhpcmluZy1hdXRob3JpdGllcy5qcyZhYnNvbHV0ZUFwcFBhdGg9cHJpdmF0ZS1uZXh0LXBhZ2VzJTJGX2FwcCZhYnNvbHV0ZURvY3VtZW50UGF0aD1wcml2YXRlLW5leHQtcGFnZXMlMkZfZG9jdW1lbnQmbWlkZGxld2FyZUNvbmZpZ0Jhc2U2ND1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXdGO0FBQ2hDO0FBQ0U7QUFDMUQ7QUFDeUQ7QUFDVjtBQUMvQztBQUMwRDtBQUMxRDtBQUNBLGlFQUFlLHdFQUFLLENBQUMseURBQVEsWUFBWSxFQUFDO0FBQzFDO0FBQ08sdUJBQXVCLHdFQUFLLENBQUMseURBQVE7QUFDckMsdUJBQXVCLHdFQUFLLENBQUMseURBQVE7QUFDckMsMkJBQTJCLHdFQUFLLENBQUMseURBQVE7QUFDekMsZUFBZSx3RUFBSyxDQUFDLHlEQUFRO0FBQzdCLHdCQUF3Qix3RUFBSyxDQUFDLHlEQUFRO0FBQzdDO0FBQ08sZ0NBQWdDLHdFQUFLLENBQUMseURBQVE7QUFDOUMsZ0NBQWdDLHdFQUFLLENBQUMseURBQVE7QUFDOUMsaUNBQWlDLHdFQUFLLENBQUMseURBQVE7QUFDL0MsZ0NBQWdDLHdFQUFLLENBQUMseURBQVE7QUFDOUMsb0NBQW9DLHdFQUFLLENBQUMseURBQVE7QUFDekQ7QUFDTyx3QkFBd0Isa0dBQWdCO0FBQy9DO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxhQUFhLDhEQUFXO0FBQ3hCLGtCQUFrQixvRUFBZ0I7QUFDbEMsS0FBSztBQUNMLFlBQVk7QUFDWixDQUFDOztBQUVEIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUGFnZXNSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvcGFnZXMvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBob2lzdCB9IGZyb20gXCJuZXh0L2Rpc3QvYnVpbGQvdGVtcGxhdGVzL2hlbHBlcnNcIjtcbi8vIEltcG9ydCB0aGUgYXBwIGFuZCBkb2N1bWVudCBtb2R1bGVzLlxuaW1wb3J0ICogYXMgZG9jdW1lbnQgZnJvbSBcInByaXZhdGUtbmV4dC1wYWdlcy9fZG9jdW1lbnRcIjtcbmltcG9ydCAqIGFzIGFwcCBmcm9tIFwicHJpdmF0ZS1uZXh0LXBhZ2VzL19hcHBcIjtcbi8vIEltcG9ydCB0aGUgdXNlcmxhbmQgY29kZS5cbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCIuL3BhZ2VzL2hpcmluZy1hdXRob3JpdGllcy5qc1wiO1xuLy8gUmUtZXhwb3J0IHRoZSBjb21wb25lbnQgKHNob3VsZCBiZSB0aGUgZGVmYXVsdCBleHBvcnQpLlxuZXhwb3J0IGRlZmF1bHQgaG9pc3QodXNlcmxhbmQsICdkZWZhdWx0Jyk7XG4vLyBSZS1leHBvcnQgbWV0aG9kcy5cbmV4cG9ydCBjb25zdCBnZXRTdGF0aWNQcm9wcyA9IGhvaXN0KHVzZXJsYW5kLCAnZ2V0U3RhdGljUHJvcHMnKTtcbmV4cG9ydCBjb25zdCBnZXRTdGF0aWNQYXRocyA9IGhvaXN0KHVzZXJsYW5kLCAnZ2V0U3RhdGljUGF0aHMnKTtcbmV4cG9ydCBjb25zdCBnZXRTZXJ2ZXJTaWRlUHJvcHMgPSBob2lzdCh1c2VybGFuZCwgJ2dldFNlcnZlclNpZGVQcm9wcycpO1xuZXhwb3J0IGNvbnN0IGNvbmZpZyA9IGhvaXN0KHVzZXJsYW5kLCAnY29uZmlnJyk7XG5leHBvcnQgY29uc3QgcmVwb3J0V2ViVml0YWxzID0gaG9pc3QodXNlcmxhbmQsICdyZXBvcnRXZWJWaXRhbHMnKTtcbi8vIFJlLWV4cG9ydCBsZWdhY3kgbWV0aG9kcy5cbmV4cG9ydCBjb25zdCB1bnN0YWJsZV9nZXRTdGF0aWNQcm9wcyA9IGhvaXN0KHVzZXJsYW5kLCAndW5zdGFibGVfZ2V0U3RhdGljUHJvcHMnKTtcbmV4cG9ydCBjb25zdCB1bnN0YWJsZV9nZXRTdGF0aWNQYXRocyA9IGhvaXN0KHVzZXJsYW5kLCAndW5zdGFibGVfZ2V0U3RhdGljUGF0aHMnKTtcbmV4cG9ydCBjb25zdCB1bnN0YWJsZV9nZXRTdGF0aWNQYXJhbXMgPSBob2lzdCh1c2VybGFuZCwgJ3Vuc3RhYmxlX2dldFN0YXRpY1BhcmFtcycpO1xuZXhwb3J0IGNvbnN0IHVuc3RhYmxlX2dldFNlcnZlclByb3BzID0gaG9pc3QodXNlcmxhbmQsICd1bnN0YWJsZV9nZXRTZXJ2ZXJQcm9wcycpO1xuZXhwb3J0IGNvbnN0IHVuc3RhYmxlX2dldFNlcnZlclNpZGVQcm9wcyA9IGhvaXN0KHVzZXJsYW5kLCAndW5zdGFibGVfZ2V0U2VydmVyU2lkZVByb3BzJyk7XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBQYWdlc1JvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5QQUdFUyxcbiAgICAgICAgcGFnZTogXCIvaGlyaW5nLWF1dGhvcml0aWVzXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9oaXJpbmctYXV0aG9yaXRpZXNcIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiAnJyxcbiAgICAgICAgZmlsZW5hbWU6ICcnXG4gICAgfSxcbiAgICBjb21wb25lbnRzOiB7XG4gICAgICAgIC8vIGRlZmF1bHQgZXhwb3J0IG1pZ2h0IG5vdCBleGlzdCB3aGVuIG9wdGltaXplZCBmb3IgZGF0YSBvbmx5XG4gICAgICAgIEFwcDogYXBwLmRlZmF1bHQsXG4gICAgICAgIERvY3VtZW50OiBkb2N1bWVudC5kZWZhdWx0XG4gICAgfSxcbiAgICB1c2VybGFuZFxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBhZ2VzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fhiring-authorities&preferredRegion=&absolutePagePath=.%2Fpages%2Fhiring-authorities.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"(pages-dir-node)/./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/_app.js\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3BhZ2VzL19hcHAuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQThCO0FBRWYsU0FBU0EsSUFBSSxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBRTtJQUNsRCxxQkFBTyw4REFBQ0Q7UUFBVyxHQUFHQyxTQUFTOzs7Ozs7QUFDakMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9icmFkeWdlb3JnZW4vRG9jdW1lbnRzL3dvcmtzcGFjZS9jYW5kaWQtY29ubmVjdGlvbnMvcGFnZXMvX2FwcC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgJy4uL3N0eWxlcy9nbG9iYWxzLmNzcydcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfSkge1xuICByZXR1cm4gPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxufVxuIl0sIm5hbWVzIjpbIkFwcCIsIkNvbXBvbmVudCIsInBhZ2VQcm9wcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_app.js\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/hiring-authorities.js":
/*!*************************************!*\
  !*** ./pages/hiring-authorities.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HiringAuthorities)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"(pages-dir-node)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(pages-dir-node)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/Layout */ \"(pages-dir-node)/./components/Layout.js\");\n\n\n\n\n\nfunction HiringAuthorities() {\n    const [authorities, setAuthorities] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        role: '',\n        companySize: '',\n        industry: ''\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HiringAuthorities.useEffect\": ()=>{\n            const fetchAuthorities = {\n                \"HiringAuthorities.useEffect.fetchAuthorities\": async ()=>{\n                    try {\n                        // Simulate API call - in real app this would fetch from /api/hiring-authorities\n                        setTimeout({\n                            \"HiringAuthorities.useEffect.fetchAuthorities\": ()=>{\n                                const sampleAuthorities = [\n                                    {\n                                        id: 'auth_1',\n                                        name: 'Sarah Wilson',\n                                        role: 'VP Engineering',\n                                        level: 'Executive',\n                                        company: 'TechCorp Inc.',\n                                        companySize: 'Enterprise (1000+)',\n                                        industry: 'Technology',\n                                        email: '<EMAIL>',\n                                        hiringPower: 'High',\n                                        activePositions: 5,\n                                        skillsLookingFor: [\n                                            'React',\n                                            'Node.js',\n                                            'Python',\n                                            'AWS',\n                                            'Leadership'\n                                        ],\n                                        preferredExperience: '5-10 years',\n                                        decisionMaker: true,\n                                        avatar: '👩‍💼',\n                                        connectionStrength: 92\n                                    },\n                                    {\n                                        id: 'auth_2',\n                                        name: 'Mike Chen',\n                                        role: 'Director of Product',\n                                        level: 'Director',\n                                        company: 'TechCorp Inc.',\n                                        companySize: 'Enterprise (1000+)',\n                                        industry: 'Technology',\n                                        email: '<EMAIL>',\n                                        hiringPower: 'Medium',\n                                        activePositions: 3,\n                                        skillsLookingFor: [\n                                            'Product Management',\n                                            'UX/UI',\n                                            'Analytics',\n                                            'Agile'\n                                        ],\n                                        preferredExperience: '3-7 years',\n                                        decisionMaker: false,\n                                        avatar: '👨‍💼',\n                                        connectionStrength: 87\n                                    },\n                                    {\n                                        id: 'auth_3',\n                                        name: 'Jennifer Rodriguez',\n                                        role: 'CEO',\n                                        level: 'C-Suite',\n                                        company: 'StartupFlow',\n                                        companySize: 'Startup (<100)',\n                                        industry: 'FinTech',\n                                        email: '<EMAIL>',\n                                        hiringPower: 'Ultimate',\n                                        activePositions: 8,\n                                        skillsLookingFor: [\n                                            'Full Stack',\n                                            'Blockchain',\n                                            'Finance',\n                                            'Startup Experience'\n                                        ],\n                                        preferredExperience: '2-8 years',\n                                        decisionMaker: true,\n                                        avatar: '👩‍💼',\n                                        connectionStrength: 95\n                                    },\n                                    {\n                                        id: 'auth_4',\n                                        name: 'David Park',\n                                        role: 'HR Director',\n                                        level: 'Director',\n                                        company: 'MegaCorp Industries',\n                                        companySize: 'Enterprise (1000+)',\n                                        industry: 'Manufacturing',\n                                        email: '<EMAIL>',\n                                        hiringPower: 'Medium',\n                                        activePositions: 12,\n                                        skillsLookingFor: [\n                                            'Operations',\n                                            'Six Sigma',\n                                            'Project Management',\n                                            'Engineering'\n                                        ],\n                                        preferredExperience: '3-10 years',\n                                        decisionMaker: false,\n                                        avatar: '👨‍💼',\n                                        connectionStrength: 78\n                                    },\n                                    {\n                                        id: 'auth_5',\n                                        name: 'Lisa Thompson',\n                                        role: 'CTO',\n                                        level: 'C-Suite',\n                                        company: 'InnovateTech',\n                                        companySize: 'Mid-size (100-1000)',\n                                        industry: 'Technology',\n                                        email: '<EMAIL>',\n                                        hiringPower: 'High',\n                                        activePositions: 6,\n                                        skillsLookingFor: [\n                                            'Architecture',\n                                            'DevOps',\n                                            'Machine Learning',\n                                            'Team Leadership'\n                                        ],\n                                        preferredExperience: '7-15 years',\n                                        decisionMaker: true,\n                                        avatar: '👩‍💼',\n                                        connectionStrength: 89\n                                    }\n                                ];\n                                setAuthorities(sampleAuthorities);\n                                setLoading(false);\n                            }\n                        }[\"HiringAuthorities.useEffect.fetchAuthorities\"], 1000);\n                    } catch (error) {\n                        console.error('Error fetching authorities:', error);\n                        setLoading(false);\n                    }\n                }\n            }[\"HiringAuthorities.useEffect.fetchAuthorities\"];\n            fetchAuthorities();\n        }\n    }[\"HiringAuthorities.useEffect\"], []);\n    const getHiringPowerColor = (power)=>{\n        switch(power){\n            case 'Ultimate':\n                return 'bg-red-100 text-red-800';\n            case 'High':\n                return 'bg-orange-100 text-orange-800';\n            case 'Medium':\n                return 'bg-yellow-100 text-yellow-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const getCompanySizeColor = (size)=>{\n        if (size.includes('Startup')) return 'bg-green-100 text-green-800';\n        if (size.includes('Mid-size')) return 'bg-blue-100 text-blue-800';\n        if (size.includes('Enterprise')) return 'bg-purple-100 text-purple-800';\n        return 'bg-gray-100 text-gray-800';\n    };\n    const filteredAuthorities = authorities.filter((auth)=>{\n        return (filters.role === '' || auth.level.toLowerCase().includes(filters.role.toLowerCase())) && (filters.companySize === '' || auth.companySize.includes(filters.companySize)) && (filters.industry === '' || auth.industry.toLowerCase().includes(filters.industry.toLowerCase()));\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Hiring Authorities | Candid Connections Katra\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Connect with the right hiring authorities based on company hierarchy and decision-making power.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-bold text-secondary-800 mb-4\",\n                                children: \"Hiring Authorities\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                lineNumber: 153,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-candid-gray-600 max-w-3xl mx-auto\",\n                                children: \"Connect directly with decision makers. Our graph database maps company hierarchies to identify the right hiring authority for your skills and experience level.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-body\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-secondary-800 mb-4\",\n                                    children: \"Filter Authorities\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"form-label\",\n                                                    children: \"Authority Level\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    className: \"form-input\",\n                                                    value: filters.role,\n                                                    onChange: (e)=>setFilters({\n                                                            ...filters,\n                                                            role: e.target.value\n                                                        }),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            children: \"All Levels\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"C-Suite\",\n                                                            children: \"C-Suite\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                            lineNumber: 174,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"Executive\",\n                                                            children: \"Executive\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"Director\",\n                                                            children: \"Director\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"Manager\",\n                                                            children: \"Manager\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                            lineNumber: 177,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"form-label\",\n                                                    children: \"Company Size\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    className: \"form-input\",\n                                                    value: filters.companySize,\n                                                    onChange: (e)=>setFilters({\n                                                            ...filters,\n                                                            companySize: e.target.value\n                                                        }),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            children: \"All Sizes\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                            lineNumber: 187,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"Startup\",\n                                                            children: \"Startup (<100)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                            lineNumber: 188,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"Mid-size\",\n                                                            children: \"Mid-size (100-1000)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"Enterprise\",\n                                                            children: \"Enterprise (1000+)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                            lineNumber: 190,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"form-label\",\n                                                    children: \"Industry\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    className: \"form-input\",\n                                                    value: filters.industry,\n                                                    onChange: (e)=>setFilters({\n                                                            ...filters,\n                                                            industry: e.target.value\n                                                        }),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            children: \"All Industries\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"Technology\",\n                                                            children: \"Technology\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"FinTech\",\n                                                            children: \"FinTech\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                            lineNumber: 202,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"Manufacturing\",\n                                                            children: \"Manufacturing\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                            lineNumber: 203,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"Healthcare\",\n                                                            children: \"Healthcare\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this),\n                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"loading-spinner w-8 h-8 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                lineNumber: 214,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-candid-gray-600\",\n                                children: \"Loading hiring authorities...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                lineNumber: 215,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                        lineNumber: 213,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                        children: filteredAuthorities.map((authority)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card-interactive\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-body\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-3xl\",\n                                                            children: authority.avatar\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-semibold text-secondary-800\",\n                                                                    children: authority.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                                    lineNumber: 227,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-candid-gray-600\",\n                                                                    children: authority.role\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                                    lineNumber: 228,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                            lineNumber: 226,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-primary-600\",\n                                                            children: [\n                                                                authority.connectionStrength,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                            lineNumber: 232,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-candid-gray-500\",\n                                                            children: \"Match Score\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                            lineNumber: 223,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium text-secondary-700\",\n                                                    children: authority.company\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-2 mt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: `badge ${getCompanySizeColor(authority.companySize)}`,\n                                                            children: authority.companySize\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: `badge ${getHiringPowerColor(authority.hiringPower)}`,\n                                                            children: [\n                                                                authority.hiringPower,\n                                                                \" Power\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        authority.decisionMaker && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"badge bg-green-100 text-green-800\",\n                                                            children: \"Decision Maker\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                            lineNumber: 238,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-secondary-700 mb-2\",\n                                                    children: \"Looking for:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-1\",\n                                                    children: [\n                                                        authority.skillsLookingFor.slice(0, 3).map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"badge badge-primary text-xs\",\n                                                                children: skill\n                                                            }, index, false, {\n                                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 25\n                                                            }, this)),\n                                                        authority.skillsLookingFor.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"badge badge-secondary text-xs\",\n                                                            children: [\n                                                                \"+\",\n                                                                authority.skillsLookingFor.length - 3,\n                                                                \" more\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                            lineNumber: 263,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                            lineNumber: 254,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between text-sm text-candid-gray-600 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        authority.activePositions,\n                                                        \" open positions\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        authority.preferredExperience,\n                                                        \" exp.\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                            lineNumber: 271,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: `/hiring-authorities/${authority.id}`,\n                                                    className: \"btn-primary text-sm py-2 px-4 flex-1 text-center\",\n                                                    children: \"View Profile\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"btn-outline text-sm py-2 px-4\",\n                                                    children: \"Connect\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                            lineNumber: 277,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                    lineNumber: 221,\n                                    columnNumber: 17\n                                }, this)\n                            }, authority.id, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                                lineNumber: 220,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                        lineNumber: 218,\n                        columnNumber: 11\n                    }, this),\n                    filteredAuthorities.length === 0 && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-candid-gray-600\",\n                            children: \"No hiring authorities match your current filters.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                            lineNumber: 296,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                        lineNumber: 295,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n                lineNumber: 150,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/hiring-authorities.js\",\n        lineNumber: 144,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/hiring-authorities.js\n");

/***/ }),

/***/ "(pages-dir-node)/./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fhiring-authorities&preferredRegion=&absolutePagePath=.%2Fpages%2Fhiring-authorities.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();