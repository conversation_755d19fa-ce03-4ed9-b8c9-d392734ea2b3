/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/positions";
exports.ids = ["pages/positions"];
exports.modules = {

/***/ "(pages-dir-node)/./components/GraphVisualization2D.js":
/*!********************************************!*\
  !*** ./components/GraphVisualization2D.js ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GraphVisualization2D)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var d3__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3 */ \"(pages-dir-node)/./node_modules/d3/src/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([d3__WEBPACK_IMPORTED_MODULE_2__]);\nd3__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nfunction GraphVisualization2D({ data }) {\n    const svgRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GraphVisualization2D.useEffect\": ()=>{\n            if (!data || !svgRef.current || \"undefined\" === 'undefined') return;\n            // Clear previous visualization\n            d3__WEBPACK_IMPORTED_MODULE_2__.select(svgRef.current).selectAll('*').remove();\n            const width = 800;\n            const height = 600;\n            // Create SVG\n            const svg = d3__WEBPACK_IMPORTED_MODULE_2__.select(svgRef.current).attr('width', width).attr('height', height).attr('viewBox', [\n                0,\n                0,\n                width,\n                height\n            ]);\n            // Create force simulation\n            const simulation = d3__WEBPACK_IMPORTED_MODULE_2__.forceSimulation(data.nodes).force('link', d3__WEBPACK_IMPORTED_MODULE_2__.forceLink(data.links).id({\n                \"GraphVisualization2D.useEffect.simulation\": (d)=>d.id\n            }[\"GraphVisualization2D.useEffect.simulation\"])).force('charge', d3__WEBPACK_IMPORTED_MODULE_2__.forceManyBody().strength(-400)).force('center', d3__WEBPACK_IMPORTED_MODULE_2__.forceCenter(width / 2, height / 2));\n            // Create links\n            const link = svg.append('g').selectAll('line').data(data.links).join('line').attr('stroke', '#999').attr('stroke-opacity', 0.6).attr('stroke-width', {\n                \"GraphVisualization2D.useEffect.link\": (d)=>Math.sqrt(d.value || 1)\n            }[\"GraphVisualization2D.useEffect.link\"]);\n            // Create nodes\n            const node = svg.append('g').selectAll('circle').data(data.nodes).join('circle').attr('r', {\n                \"GraphVisualization2D.useEffect.node\": (d)=>d.size || 5\n            }[\"GraphVisualization2D.useEffect.node\"]).attr('fill', {\n                \"GraphVisualization2D.useEffect.node\": (d)=>{\n                    switch(d.type){\n                        case 'jobSeeker':\n                            return '#3b82f6' // blue\n                            ;\n                        case 'company':\n                            return '#14b8a6' // teal\n                            ;\n                        case 'position':\n                            return '#10b981' // emerald\n                            ;\n                        case 'skill':\n                            return '#f59e0b' // amber\n                            ;\n                        default:\n                            return '#6366f1' // indigo\n                            ;\n                    }\n                }\n            }[\"GraphVisualization2D.useEffect.node\"]).call(drag(simulation));\n            // Add titles for nodes\n            node.append('title').text({\n                \"GraphVisualization2D.useEffect\": (d)=>d.name\n            }[\"GraphVisualization2D.useEffect\"]);\n            // Update positions on simulation tick\n            simulation.on('tick', {\n                \"GraphVisualization2D.useEffect\": ()=>{\n                    link.attr('x1', {\n                        \"GraphVisualization2D.useEffect\": (d)=>d.source.x\n                    }[\"GraphVisualization2D.useEffect\"]).attr('y1', {\n                        \"GraphVisualization2D.useEffect\": (d)=>d.source.y\n                    }[\"GraphVisualization2D.useEffect\"]).attr('x2', {\n                        \"GraphVisualization2D.useEffect\": (d)=>d.target.x\n                    }[\"GraphVisualization2D.useEffect\"]).attr('y2', {\n                        \"GraphVisualization2D.useEffect\": (d)=>d.target.y\n                    }[\"GraphVisualization2D.useEffect\"]);\n                    node.attr('cx', {\n                        \"GraphVisualization2D.useEffect\": (d)=>d.x\n                    }[\"GraphVisualization2D.useEffect\"]).attr('cy', {\n                        \"GraphVisualization2D.useEffect\": (d)=>d.y\n                    }[\"GraphVisualization2D.useEffect\"]);\n                }\n            }[\"GraphVisualization2D.useEffect\"]);\n            // Drag functionality\n            function drag(simulation) {\n                function dragstarted(event) {\n                    if (!event.active) simulation.alphaTarget(0.3).restart();\n                    event.subject.fx = event.subject.x;\n                    event.subject.fy = event.subject.y;\n                }\n                function dragged(event) {\n                    event.subject.fx = event.x;\n                    event.subject.fy = event.y;\n                }\n                function dragended(event) {\n                    if (!event.active) simulation.alphaTarget(0);\n                    event.subject.fx = null;\n                    event.subject.fy = null;\n                }\n                return d3__WEBPACK_IMPORTED_MODULE_2__.drag().on('start', dragstarted).on('drag', dragged).on('end', dragended);\n            }\n            return ({\n                \"GraphVisualization2D.useEffect\": ()=>{\n                    simulation.stop();\n                }\n            })[\"GraphVisualization2D.useEffect\"];\n        }\n    }[\"GraphVisualization2D.useEffect\"], [\n        data\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border rounded-lg shadow-sm bg-white p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            ref: svgRef,\n            className: \"w-full h-full\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/GraphVisualization2D.js\",\n            lineNumber: 103,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/GraphVisualization2D.js\",\n        lineNumber: 102,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/GraphVisualization2D.js\n");

/***/ }),

/***/ "(pages-dir-node)/./components/GraphVisualization3D.js":
/*!********************************************!*\
  !*** ./components/GraphVisualization3D.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GraphVisualization3D)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction GraphVisualization3D({ data }) {\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GraphVisualization3D.useEffect\": ()=>{\n            if (!data || !containerRef.current || \"undefined\" === 'undefined') return;\n            // Clear previous visualization\n            containerRef.current.innerHTML = '';\n            // Dynamically import and initialize 3D force graph\n            Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/3d-force-graph\"), __webpack_require__.e(\"_pages-dir-node___barrel_optimize___names_AmbientLight_DirectionalLight_REVISION_node_modules-e93221\")]).then(__webpack_require__.bind(__webpack_require__, /*! 3d-force-graph */ \"(pages-dir-node)/./node_modules/3d-force-graph/dist/3d-force-graph.mjs\")).then({\n                \"GraphVisualization3D.useEffect\": (ForceGraph3DModule)=>{\n                    const ForceGraph3DComponent = ForceGraph3DModule.default;\n                    const graph = ForceGraph3DComponent().width(containerRef.current.clientWidth).height(500).backgroundColor('#ffffff').nodeColor({\n                        \"GraphVisualization3D.useEffect.graph\": (node)=>{\n                            switch(node.type){\n                                case 'jobSeeker':\n                                    return '#3b82f6' // blue\n                                    ;\n                                case 'company':\n                                    return '#14b8a6' // teal\n                                    ;\n                                case 'position':\n                                    return '#10b981' // emerald\n                                    ;\n                                case 'skill':\n                                    return '#f59e0b' // amber\n                                    ;\n                                default:\n                                    return '#6366f1' // indigo\n                                    ;\n                            }\n                        }\n                    }[\"GraphVisualization3D.useEffect.graph\"]).nodeLabel({\n                        \"GraphVisualization3D.useEffect.graph\": (node)=>node.name\n                    }[\"GraphVisualization3D.useEffect.graph\"]).nodeVal({\n                        \"GraphVisualization3D.useEffect.graph\": (node)=>node.size || 5\n                    }[\"GraphVisualization3D.useEffect.graph\"]).linkWidth({\n                        \"GraphVisualization3D.useEffect.graph\": (link)=>link.value || 1\n                    }[\"GraphVisualization3D.useEffect.graph\"]).linkDirectionalParticles(2).linkDirectionalParticleSpeed(0.005).graphData(data)(containerRef.current);\n                    // Handle window resize\n                    const handleResize = {\n                        \"GraphVisualization3D.useEffect.handleResize\": ()=>{\n                            if (containerRef.current) {\n                                graph.width(containerRef.current.clientWidth);\n                            }\n                        }\n                    }[\"GraphVisualization3D.useEffect.handleResize\"];\n                    window.addEventListener('resize', handleResize);\n                }\n            }[\"GraphVisualization3D.useEffect\"]).catch({\n                \"GraphVisualization3D.useEffect\": (error)=>{\n                    console.error('Failed to load 3D visualization:', error);\n                    // Fallback to a simple message\n                    if (containerRef.current) {\n                        containerRef.current.innerHTML = '<div class=\"flex items-center justify-center h-full text-gray-500\">3D visualization unavailable</div>';\n                    }\n                }\n            }[\"GraphVisualization3D.useEffect\"]);\n        }\n    }[\"GraphVisualization3D.useEffect\"], [\n        data\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"w-full h-[500px] border rounded-lg shadow-sm bg-white\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/GraphVisualization3D.js\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/GraphVisualization3D.js\n");

/***/ }),

/***/ "(pages-dir-node)/./components/Layout.js":
/*!******************************!*\
  !*** ./components/Layout.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Layout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Navigation */ \"(pages-dir-node)/./components/Navigation.js\");\n\n\nfunction Layout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Navigation__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Layout.js\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container-app section-padding\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Layout.js\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Layout.js\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL2NvbXBvbmVudHMvTGF5b3V0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXFDO0FBRXRCLFNBQVNDLE9BQU8sRUFBRUMsUUFBUSxFQUFFO0lBQ3pDLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0osbURBQVVBOzs7OzswQkFDWCw4REFBQ0s7Z0JBQUtELFdBQVU7MEJBQWlDRjs7Ozs7Ozs7Ozs7O0FBR3ZEIiwic291cmNlcyI6WyIvVXNlcnMvYnJhZHlnZW9yZ2VuL0RvY3VtZW50cy93b3Jrc3BhY2UvY2FuZGlkLWNvbm5lY3Rpb25zL2NvbXBvbmVudHMvTGF5b3V0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBOYXZpZ2F0aW9uIGZyb20gJy4vTmF2aWdhdGlvbidcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTGF5b3V0KHsgY2hpbGRyZW4gfSkge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLXdoaXRlXCI+XG4gICAgICA8TmF2aWdhdGlvbiAvPlxuICAgICAgPG1haW4gY2xhc3NOYW1lPVwiY29udGFpbmVyLWFwcCBzZWN0aW9uLXBhZGRpbmdcIj57Y2hpbGRyZW59PC9tYWluPlxuICAgIDwvZGl2PlxuICApXG59Il0sIm5hbWVzIjpbIk5hdmlnYXRpb24iLCJMYXlvdXQiLCJjaGlsZHJlbiIsImRpdiIsImNsYXNzTmFtZSIsIm1haW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/Layout.js\n");

/***/ }),

/***/ "(pages-dir-node)/./components/Navigation.js":
/*!**********************************!*\
  !*** ./components/Navigation.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(pages-dir-node)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-node)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nfunction Navigation() {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const navItems = [\n        {\n            name: 'Dashboard',\n            path: '/',\n            icon: '🏠'\n        },\n        {\n            name: 'Authority Matches',\n            path: '/matches',\n            icon: '🎯'\n        },\n        {\n            name: 'Job Seekers',\n            path: '/job-seekers',\n            icon: '👥'\n        },\n        {\n            name: 'Hiring Authorities',\n            path: '/hiring-authorities',\n            icon: '👔'\n        },\n        {\n            name: 'Companies',\n            path: '/companies',\n            icon: '🏢'\n        },\n        {\n            name: 'Positions',\n            path: '/positions',\n            icon: '📋'\n        },\n        {\n            name: 'Skills',\n            path: '/skills',\n            icon: '🛠️'\n        },\n        {\n            name: 'Network View',\n            path: '/global-view',\n            icon: '🌐'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-white shadow-soft border-b border-candid-gray-200\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-app\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0 flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/\",\n                                className: \"flex items-center space-x-3 group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 relative group-hover:scale-105 transition-transform duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            viewBox: \"0 0 48 48\",\n                                            className: \"w-full h-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"24\",\n                                                    cy: \"24\",\n                                                    r: \"22\",\n                                                    fill: \"none\",\n                                                    stroke: \"#1e3a8a\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 31,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"24\",\n                                                    cy: \"12\",\n                                                    r: \"3\",\n                                                    fill: \"#00d4ff\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 33,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"36\",\n                                                    cy: \"24\",\n                                                    r: \"3\",\n                                                    fill: \"#00d4ff\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 34,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"24\",\n                                                    cy: \"36\",\n                                                    r: \"3\",\n                                                    fill: \"#00d4ff\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 35,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"12\",\n                                                    cy: \"24\",\n                                                    r: \"3\",\n                                                    fill: \"#00d4ff\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 36,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"24\",\n                                                    cy: \"24\",\n                                                    r: \"4\",\n                                                    fill: \"#1e3a8a\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 37,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                    x1: \"24\",\n                                                    y1: \"15\",\n                                                    x2: \"24\",\n                                                    y2: \"20\",\n                                                    stroke: \"#00d4ff\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 39,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                    x1: \"33\",\n                                                    y1: \"24\",\n                                                    x2: \"28\",\n                                                    y2: \"24\",\n                                                    stroke: \"#00d4ff\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 40,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                    x1: \"24\",\n                                                    y1: \"33\",\n                                                    x2: \"24\",\n                                                    y2: \"28\",\n                                                    stroke: \"#00d4ff\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 41,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                    x1: \"15\",\n                                                    y1: \"24\",\n                                                    x2: \"20\",\n                                                    y2: \"24\",\n                                                    stroke: \"#00d4ff\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 42,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                            lineNumber: 29,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 28,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden sm:block\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold text-secondary-800 group-hover:text-primary-500 transition-colors duration-200\",\n                                                children: \"Candid Connections\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                lineNumber: 46,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-candid-gray-600 -mt-1\",\n                                                children: \"Katra Platform\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                lineNumber: 49,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 45,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                lineNumber: 26,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1\",\n                                children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: item.path,\n                                        className: `flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${router.pathname === item.path ? 'nav-link-active' : 'nav-link'}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-base\",\n                                                children: item.icon\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                lineNumber: 67,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                lineNumber: 68,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, item.path, true, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 58,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                lineNumber: 56,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/global-view\",\n                                    className: \"btn-outline text-sm py-2 px-4\",\n                                    children: \"\\uD83C\\uDF10 Network View\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"https://portal.candid-connections.com/user/login\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"btn-primary text-sm py-2 px-4\",\n                                    children: \"Portal Login\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),\n                                className: \"p-2 rounded-lg text-candid-navy-600 hover:text-primary-600 hover:bg-primary-50 transition-colors duration-200\",\n                                \"aria-label\": \"Toggle mobile menu\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 101,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M4 6h16M4 12h16M4 18h16\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 103,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                    lineNumber: 99,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                lineNumber: 94,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this),\n                isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"lg:hidden border-t border-candid-gray-200 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: item.path,\n                                    onClick: ()=>setIsMobileMenuOpen(false),\n                                    className: `flex items-center space-x-3 px-4 py-3 rounded-lg text-base font-medium transition-all duration-200 ${router.pathname === item.path ? 'nav-link-active' : 'nav-link'}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg\",\n                                            children: item.icon\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                            lineNumber: 125,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                            lineNumber: 126,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, item.path, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                    lineNumber: 115,\n                                    columnNumber: 17\n                                }, this)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-4 space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/global-view\",\n                                        onClick: ()=>setIsMobileMenuOpen(false),\n                                        className: \"block w-full btn-outline text-center\",\n                                        children: \"\\uD83C\\uDF10 Network View\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 132,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"https://portal.candid-connections.com/user/login\",\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        className: \"block w-full btn-primary text-center\",\n                                        children: \"Portal Login\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 139,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                lineNumber: 131,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                        lineNumber: 113,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                    lineNumber: 112,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/Navigation.js\n");

/***/ }),

/***/ "(pages-dir-node)/./components/VisualizationModal.js":
/*!******************************************!*\
  !*** ./components/VisualizationModal.js ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ VisualizationModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _GraphVisualization2D__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./GraphVisualization2D */ \"(pages-dir-node)/./components/GraphVisualization2D.js\");\n/* harmony import */ var _GraphVisualization3D__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./GraphVisualization3D */ \"(pages-dir-node)/./components/GraphVisualization3D.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_GraphVisualization2D__WEBPACK_IMPORTED_MODULE_2__]);\n_GraphVisualization2D__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nfunction VisualizationModal({ isOpen, onClose, data, title }) {\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('2D');\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 overflow-y-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 transition-opacity\",\n                    \"aria-hidden\": \"true\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gray-500 opacity-75\",\n                        onClick: onClose\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/VisualizationModal.js\",\n                        lineNumber: 14,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/VisualizationModal.js\",\n                    lineNumber: 13,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-5xl sm:w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"sm:flex sm:items-start\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg leading-6 font-medium text-gray-900\",\n                                                    children: title || 'Network Visualization'\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/VisualizationModal.js\",\n                                                    lineNumber: 22,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>setViewMode('2D'),\n                                                            className: `px-3 py-1 text-sm font-medium rounded-md ${viewMode === '2D' ? 'bg-indigo-100 text-indigo-700' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,\n                                                            children: \"2D View\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/VisualizationModal.js\",\n                                                            lineNumber: 24,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>setViewMode('3D'),\n                                                            className: `px-3 py-1 text-sm font-medium rounded-md ${viewMode === '3D' ? 'bg-indigo-100 text-indigo-700' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,\n                                                            children: \"3D View\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/VisualizationModal.js\",\n                                                            lineNumber: 35,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/VisualizationModal.js\",\n                                                    lineNumber: 23,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/VisualizationModal.js\",\n                                            lineNumber: 21,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2\",\n                                            children: viewMode === '2D' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GraphVisualization2D__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                data: data\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/VisualizationModal.js\",\n                                                lineNumber: 51,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GraphVisualization3D__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                data: data\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/VisualizationModal.js\",\n                                                lineNumber: 53,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/VisualizationModal.js\",\n                                            lineNumber: 49,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/VisualizationModal.js\",\n                                    lineNumber: 20,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/VisualizationModal.js\",\n                                lineNumber: 19,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/VisualizationModal.js\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                className: \"btn-secondary\",\n                                onClick: onClose,\n                                children: \"Close\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/VisualizationModal.js\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/VisualizationModal.js\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/VisualizationModal.js\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/VisualizationModal.js\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/VisualizationModal.js\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/VisualizationModal.js\n");

/***/ }),

/***/ "(pages-dir-node)/./lib/graphData.js":
/*!**************************!*\
  !*** ./lib/graphData.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateSampleGraphData: () => (/* binding */ generateSampleGraphData),\n/* harmony export */   processGraphData: () => (/* binding */ processGraphData)\n/* harmony export */ });\n// Process raw data from ArangoDB into format for D3.js and 3D-Force-Graph\nconst processGraphData = (data)=>{\n    if (!data) return {\n        nodes: [],\n        links: []\n    };\n    const { entities, relationships } = data;\n    // Process nodes\n    const nodes = entities.map((entity)=>({\n            id: entity._id,\n            name: entity.name || entity.title || 'Unnamed',\n            type: getEntityType(entity._id),\n            size: getNodeSize(entity),\n            ...entity // Include all original properties\n        }));\n    // Process links\n    const links = relationships.map((rel)=>({\n            source: rel._from,\n            target: rel._to,\n            value: rel.strength || 1,\n            type: rel._type || getEdgeType(rel._id),\n            ...rel // Include all original properties\n        }));\n    return {\n        nodes,\n        links\n    };\n};\n// Determine entity type from _id\nconst getEntityType = (id)=>{\n    if (!id) return 'unknown';\n    if (id.includes('/jobSeekers/')) return 'jobSeeker';\n    if (id.includes('/companies/')) return 'company';\n    if (id.includes('/hiringAuthorities/')) return 'hiringAuthority';\n    if (id.includes('/positions/')) return 'position';\n    if (id.includes('/skills/')) return 'skill';\n    return 'unknown';\n};\n// Determine edge type from _id\nconst getEdgeType = (id)=>{\n    if (!id) return 'unknown';\n    if (id.includes('/works_for/')) return 'works_for';\n    if (id.includes('/employs/')) return 'employs';\n    if (id.includes('/posts/')) return 'posts';\n    if (id.includes('/requires/')) return 'requires';\n    if (id.includes('/has_skill/')) return 'has_skill';\n    if (id.includes('/matched_to/')) return 'matched_to';\n    return 'unknown';\n};\n// Determine node size based on entity type and properties\nconst getNodeSize = (entity)=>{\n    const type = getEntityType(entity._id);\n    switch(type){\n        case 'jobSeeker':\n            return 8;\n        case 'company':\n            return 10;\n        case 'hiringAuthority':\n            return 7;\n        case 'position':\n            return 9;\n        case 'skill':\n            // Skills with higher demand are larger\n            return entity.demandScore ? 5 + entity.demandScore / 20 : 6;\n        default:\n            return 5;\n    }\n};\n// Generate sample graph data for testing\nconst generateSampleGraphData = ()=>{\n    // Create sample nodes\n    const jobSeekers = Array.from({\n        length: 5\n    }, (_, i)=>({\n            id: `jobSeekers/${i}`,\n            name: `Job Seeker ${i + 1}`,\n            type: 'jobSeeker',\n            size: 8\n        }));\n    const companies = Array.from({\n        length: 3\n    }, (_, i)=>({\n            id: `companies/${i}`,\n            name: `Company ${i + 1}`,\n            type: 'company',\n            size: 10\n        }));\n    const positions = Array.from({\n        length: 4\n    }, (_, i)=>({\n            id: `positions/${i}`,\n            name: `Position ${i + 1}`,\n            type: 'position',\n            size: 9\n        }));\n    const skills = Array.from({\n        length: 8\n    }, (_, i)=>({\n            id: `skills/${i}`,\n            name: `Skill ${i + 1}`,\n            type: 'skill',\n            size: 6\n        }));\n    const nodes = [\n        ...jobSeekers,\n        ...companies,\n        ...positions,\n        ...skills\n    ];\n    // Create sample links\n    const links = [];\n    // Job seekers to companies\n    jobSeekers.forEach((js, i)=>{\n        links.push({\n            source: js.id,\n            target: companies[i % companies.length].id,\n            type: 'works_for',\n            value: 1\n        });\n    });\n    // Companies to positions\n    companies.forEach((company, i)=>{\n        positions.forEach((position, j)=>{\n            if ((i + j) % 2 === 0) {\n                links.push({\n                    source: company.id,\n                    target: position.id,\n                    type: 'posts',\n                    value: 1\n                });\n            }\n        });\n    });\n    // Job seekers to skills\n    jobSeekers.forEach((js)=>{\n        // Each job seeker has 2-4 random skills\n        const numSkills = 2 + Math.floor(Math.random() * 3);\n        const shuffled = [\n            ...skills\n        ].sort(()=>0.5 - Math.random());\n        const selectedSkills = shuffled.slice(0, numSkills);\n        selectedSkills.forEach((skill)=>{\n            links.push({\n                source: js.id,\n                target: skill.id,\n                type: 'has_skill',\n                value: 1\n            });\n        });\n    });\n    // Positions to skills\n    positions.forEach((position)=>{\n        // Each position requires 2-3 random skills\n        const numSkills = 2 + Math.floor(Math.random() * 2);\n        const shuffled = [\n            ...skills\n        ].sort(()=>0.5 - Math.random());\n        const selectedSkills = shuffled.slice(0, numSkills);\n        selectedSkills.forEach((skill)=>{\n            links.push({\n                source: position.id,\n                target: skill.id,\n                type: 'requires',\n                value: 1\n            });\n        });\n    });\n    return {\n        nodes,\n        links\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./lib/graphData.js\n");

/***/ }),

/***/ "(pages-dir-node)/./lib/utils.js":
/*!**********************!*\
  !*** ./lib/utils.js ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateMatchScore: () => (/* binding */ calculateMatchScore),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   getEntityIcon: () => (/* binding */ getEntityIcon),\n/* harmony export */   getMatchColor: () => (/* binding */ getMatchColor),\n/* harmony export */   getRelationshipDescription: () => (/* binding */ getRelationshipDescription),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n// Format date to readable string\nconst formatDate = (date)=>{\n    if (!date) return '';\n    const d = new Date(date);\n    return d.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n    });\n};\n// Calculate match score between job seeker and position\nconst calculateMatchScore = (jobSeeker, position)=>{\n    if (!jobSeeker || !position) return 0;\n    // In a real implementation, this would compare skills and requirements\n    // For now, return a random score between 0-100\n    return Math.floor(Math.random() * 101);\n};\n// Generate color based on match score\nconst getMatchColor = (score)=>{\n    if (score >= 80) return 'bg-emerald-100 text-emerald-800';\n    if (score >= 60) return 'bg-green-100 text-green-800';\n    if (score >= 40) return 'bg-yellow-100 text-yellow-800';\n    return 'bg-red-100 text-red-800';\n};\n// Truncate text with ellipsis\nconst truncateText = (text, maxLength = 100)=>{\n    if (!text || text.length <= maxLength) return text;\n    return text.slice(0, maxLength) + '...';\n};\n// Generate entity icon based on type\nconst getEntityIcon = (type)=>{\n    switch(type){\n        case 'jobSeeker':\n            return '👤';\n        case 'company':\n            return '🏢';\n        case 'hiringAuthority':\n            return '👔';\n        case 'position':\n            return '📋';\n        case 'skill':\n            return '🔧';\n        default:\n            return '📄';\n    }\n};\n// Generate relationship description\nconst getRelationshipDescription = (type)=>{\n    switch(type){\n        case 'works_for':\n            return 'Works for';\n        case 'employs':\n            return 'Employs';\n        case 'posts':\n            return 'Posted by';\n        case 'requires':\n            return 'Requires';\n        case 'has_skill':\n            return 'Has skill';\n        case 'matched_to':\n            return 'Matched to';\n        default:\n            return 'Related to';\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./lib/utils.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fpositions&preferredRegion=&absolutePagePath=.%2Fpages%2Fpositions.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fpositions&preferredRegion=&absolutePagePath=.%2Fpages%2Fpositions.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages/module.compiled */ \"(pages-dir-node)/./node_modules/next/dist/server/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(pages-dir-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(pages-dir-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"(pages-dir-node)/./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"(pages-dir-node)/./pages/_app.js\");\n/* harmony import */ var _pages_positions_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages/positions.js */ \"(pages-dir-node)/./pages/positions.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_pages_positions_js__WEBPACK_IMPORTED_MODULE_5__]);\n_pages_positions_js__WEBPACK_IMPORTED_MODULE_5__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_positions_js__WEBPACK_IMPORTED_MODULE_5__, 'default'));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_positions_js__WEBPACK_IMPORTED_MODULE_5__, 'getStaticProps');\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_positions_js__WEBPACK_IMPORTED_MODULE_5__, 'getStaticPaths');\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_positions_js__WEBPACK_IMPORTED_MODULE_5__, 'getServerSideProps');\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_positions_js__WEBPACK_IMPORTED_MODULE_5__, 'config');\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_positions_js__WEBPACK_IMPORTED_MODULE_5__, 'reportWebVitals');\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_positions_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticProps');\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_positions_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticPaths');\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_positions_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticParams');\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_positions_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerProps');\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_positions_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/positions\",\n        pathname: \"/positions\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    components: {\n        // default export might not exist when optimized for data only\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_positions_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fpositions&preferredRegion=&absolutePagePath=.%2Fpages%2Fpositions.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"(pages-dir-node)/./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/_app.js\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3BhZ2VzL19hcHAuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQThCO0FBRWYsU0FBU0EsSUFBSSxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBRTtJQUNsRCxxQkFBTyw4REFBQ0Q7UUFBVyxHQUFHQyxTQUFTOzs7Ozs7QUFDakMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9icmFkeWdlb3JnZW4vRG9jdW1lbnRzL3dvcmtzcGFjZS9jYW5kaWQtY29ubmVjdGlvbnMvcGFnZXMvX2FwcC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgJy4uL3N0eWxlcy9nbG9iYWxzLmNzcydcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfSkge1xuICByZXR1cm4gPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxufVxuIl0sIm5hbWVzIjpbIkFwcCIsIkNvbXBvbmVudCIsInBhZ2VQcm9wcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_app.js\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/positions.js":
/*!****************************!*\
  !*** ./pages/positions.js ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Positions)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"(pages-dir-node)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/Layout */ \"(pages-dir-node)/./components/Layout.js\");\n/* harmony import */ var _components_VisualizationModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/VisualizationModal */ \"(pages-dir-node)/./components/VisualizationModal.js\");\n/* harmony import */ var _lib_graphData__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/graphData */ \"(pages-dir-node)/./lib/graphData.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../lib/utils */ \"(pages-dir-node)/./lib/utils.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_VisualizationModal__WEBPACK_IMPORTED_MODULE_4__]);\n_components_VisualizationModal__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\nfunction Positions() {\n    const [positions, setPositions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showVisualization, setShowVisualization] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [graphData, setGraphData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [filterLevel, setFilterLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [filterType, setFilterType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('posted');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Positions.useEffect\": ()=>{\n            const fetchPositions = {\n                \"Positions.useEffect.fetchPositions\": async ()=>{\n                    try {\n                        // Simulate API call - in real app this would fetch from /api/positions\n                        setTimeout({\n                            \"Positions.useEffect.fetchPositions\": ()=>{\n                                const samplePositions = [\n                                    {\n                                        id: 'pos_1',\n                                        title: 'Lead Frontend Engineer',\n                                        company: 'TechCorp Inc.',\n                                        companyLogo: '🏢',\n                                        level: 'Senior',\n                                        type: 'Full-time',\n                                        location: 'San Francisco, CA',\n                                        remote: true,\n                                        salary: '$140,000 - $180,000',\n                                        description: 'Lead a team of frontend engineers building next-generation web applications using React and TypeScript.',\n                                        requirements: [\n                                            'React',\n                                            'TypeScript',\n                                            'Leadership',\n                                            'GraphQL',\n                                            'Node.js'\n                                        ],\n                                        benefits: [\n                                            'Health Insurance',\n                                            'Stock Options',\n                                            'Remote Work',\n                                            '401k'\n                                        ],\n                                        postedDate: new Date('2024-01-15'),\n                                        applicants: 23,\n                                        status: 'active'\n                                    },\n                                    {\n                                        id: 'pos_2',\n                                        title: 'Backend Engineer',\n                                        company: 'DataFlow Systems',\n                                        companyLogo: '📊',\n                                        level: 'Mid',\n                                        type: 'Full-time',\n                                        location: 'Austin, TX',\n                                        remote: false,\n                                        salary: '$100,000 - $130,000',\n                                        description: 'Build scalable backend systems for our data analytics platform using Python and Django.',\n                                        requirements: [\n                                            'Python',\n                                            'Django',\n                                            'PostgreSQL',\n                                            'AWS',\n                                            'Docker'\n                                        ],\n                                        benefits: [\n                                            'Health Insurance',\n                                            'Flexible Hours',\n                                            'Learning Budget'\n                                        ],\n                                        postedDate: new Date('2024-01-14'),\n                                        applicants: 18,\n                                        status: 'active'\n                                    },\n                                    {\n                                        id: 'pos_3',\n                                        title: 'Senior UX Designer',\n                                        company: 'Design Studio Pro',\n                                        companyLogo: '🎨',\n                                        level: 'Senior',\n                                        type: 'Contract',\n                                        location: 'New York, NY',\n                                        remote: true,\n                                        salary: '$80 - $120/hour',\n                                        description: 'Design intuitive user experiences for our client projects across various industries.',\n                                        requirements: [\n                                            'Figma',\n                                            'User Research',\n                                            'Design Systems',\n                                            'Prototyping'\n                                        ],\n                                        benefits: [\n                                            'Flexible Schedule',\n                                            'Creative Freedom',\n                                            'Portfolio Building'\n                                        ],\n                                        postedDate: new Date('2024-01-13'),\n                                        applicants: 31,\n                                        status: 'active'\n                                    },\n                                    {\n                                        id: 'pos_4',\n                                        title: 'Cloud Infrastructure Engineer',\n                                        company: 'CloudTech Solutions',\n                                        companyLogo: '☁️',\n                                        level: 'Senior',\n                                        type: 'Full-time',\n                                        location: 'Seattle, WA',\n                                        remote: true,\n                                        salary: '$130,000 - $160,000',\n                                        description: 'Design and maintain cloud infrastructure for enterprise clients using Kubernetes and Terraform.',\n                                        requirements: [\n                                            'Kubernetes',\n                                            'Terraform',\n                                            'AWS',\n                                            'Docker',\n                                            'Monitoring'\n                                        ],\n                                        benefits: [\n                                            'Health Insurance',\n                                            'Stock Options',\n                                            'Remote Work',\n                                            'Conference Budget'\n                                        ],\n                                        postedDate: new Date('2024-01-12'),\n                                        applicants: 15,\n                                        status: 'active'\n                                    },\n                                    {\n                                        id: 'pos_5',\n                                        title: 'Junior Frontend Developer',\n                                        company: 'TechCorp Inc.',\n                                        companyLogo: '🏢',\n                                        level: 'Junior',\n                                        type: 'Full-time',\n                                        location: 'San Francisco, CA',\n                                        remote: false,\n                                        salary: '$70,000 - $90,000',\n                                        description: 'Join our frontend team to build user interfaces and learn from experienced developers.',\n                                        requirements: [\n                                            'JavaScript',\n                                            'React',\n                                            'CSS',\n                                            'Git'\n                                        ],\n                                        benefits: [\n                                            'Health Insurance',\n                                            'Mentorship',\n                                            'Learning Budget'\n                                        ],\n                                        postedDate: new Date('2024-01-10'),\n                                        applicants: 42,\n                                        status: 'paused'\n                                    }\n                                ];\n                                setPositions(samplePositions);\n                                setGraphData((0,_lib_graphData__WEBPACK_IMPORTED_MODULE_5__.generateSampleGraphData)());\n                                setLoading(false);\n                            }\n                        }[\"Positions.useEffect.fetchPositions\"], 1000);\n                    } catch (err) {\n                        setError(err.message);\n                        setLoading(false);\n                    }\n                }\n            }[\"Positions.useEffect.fetchPositions\"];\n            fetchPositions();\n        }\n    }[\"Positions.useEffect\"], []);\n    const filteredPositions = positions.filter((position)=>{\n        const matchesSearch = position.title.toLowerCase().includes(searchTerm.toLowerCase()) || position.company.toLowerCase().includes(searchTerm.toLowerCase()) || position.location.toLowerCase().includes(searchTerm.toLowerCase()) || position.requirements.some((req)=>req.toLowerCase().includes(searchTerm.toLowerCase()));\n        const matchesLevel = filterLevel === 'all' || position.level === filterLevel;\n        const matchesType = filterType === 'all' || position.type === filterType;\n        return matchesSearch && matchesLevel && matchesType;\n    });\n    const sortedPositions = [\n        ...filteredPositions\n    ].sort((a, b)=>{\n        switch(sortBy){\n            case 'posted':\n                return new Date(b.postedDate) - new Date(a.postedDate);\n            case 'title':\n                return a.title.localeCompare(b.title);\n            case 'company':\n                return a.company.localeCompare(b.company);\n            case 'applicants':\n                return b.applicants - a.applicants;\n            default:\n                return 0;\n        }\n    });\n    const getLevelColor = (level)=>{\n        switch(level){\n            case 'Junior':\n                return 'bg-green-100 text-green-800';\n            case 'Mid':\n                return 'bg-blue-100 text-blue-800';\n            case 'Senior':\n                return 'bg-purple-100 text-purple-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const getTypeColor = (type)=>{\n        switch(type){\n            case 'Full-time':\n                return 'bg-emerald-100 text-emerald-800';\n            case 'Part-time':\n                return 'bg-yellow-100 text-yellow-800';\n            case 'Contract':\n                return 'bg-orange-100 text-orange-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'active':\n                return 'bg-green-100 text-green-800';\n            case 'paused':\n                return 'bg-yellow-100 text-yellow-800';\n            case 'closed':\n                return 'bg-red-100 text-red-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center items-center h-64\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-emerald-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                        lineNumber: 184,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                    lineNumber: 183,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                lineNumber: 182,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n            lineNumber: 181,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n                    children: [\n                        \"Error: \",\n                        error\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                    lineNumber: 195,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                lineNumber: 194,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n            lineNumber: 193,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                    children: \"Positions | Candid Connections Katra\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                    lineNumber: 206,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                lineNumber: 205,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold\",\n                                children: \"Open Positions\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowVisualization(true),\n                                className: \"bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors\",\n                                children: \"\\uD83C\\uDF10 View Network\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm p-4 mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-4 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-64\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search positions, companies, skills...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"w-full border border-gray-300 rounded-md px-3 py-2 text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"Level:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                            lineNumber: 234,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: filterLevel,\n                                            onChange: (e)=>setFilterLevel(e.target.value),\n                                            className: \"border border-gray-300 rounded-md px-3 py-1 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"all\",\n                                                    children: \"All Levels\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Junior\",\n                                                    children: \"Junior\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Mid\",\n                                                    children: \"Mid\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Senior\",\n                                                    children: \"Senior\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                    lineNumber: 233,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"Type:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                            lineNumber: 248,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: filterType,\n                                            onChange: (e)=>setFilterType(e.target.value),\n                                            className: \"border border-gray-300 rounded-md px-3 py-1 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"all\",\n                                                    children: \"All Types\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Full-time\",\n                                                    children: \"Full-time\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Part-time\",\n                                                    children: \"Part-time\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Contract\",\n                                                    children: \"Contract\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                            lineNumber: 249,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"Sort by:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                            lineNumber: 262,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: sortBy,\n                                            onChange: (e)=>setSortBy(e.target.value),\n                                            className: \"border border-gray-300 rounded-md px-3 py-1 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"posted\",\n                                                    children: \"Date Posted\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"title\",\n                                                    children: \"Position Title\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"company\",\n                                                    children: \"Company\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"applicants\",\n                                                    children: \"Applicants\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                            lineNumber: 263,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                    lineNumber: 261,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        sortedPositions.length,\n                                        \" positions found\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                    lineNumber: 275,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: sortedPositions.map((position)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-start mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl mr-3\",\n                                                        children: position.companyLogo\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-lg text-gray-900\",\n                                                                children: position.title\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                                lineNumber: 290,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600\",\n                                                                children: position.company\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                lineNumber: 287,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getLevelColor(position.level)}`,\n                                                        children: position.level\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(position.type)}`,\n                                                        children: position.type\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(position.status)}`,\n                                                        children: position.status\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                lineNumber: 294,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                        lineNumber: 286,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-sm text-gray-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-2\",\n                                                        children: \"\\uD83D\\uDCCD\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    position.location,\n                                                    \" \",\n                                                    position.remote && '(Remote OK)'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                lineNumber: 309,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-sm text-gray-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-2\",\n                                                        children: \"\\uD83D\\uDCB0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    position.salary\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                lineNumber: 313,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-sm text-gray-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-2\",\n                                                        children: \"\\uD83D\\uDC65\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    position.applicants,\n                                                    \" applicants\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                lineNumber: 317,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                        lineNumber: 308,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-700 text-sm mb-4\",\n                                        children: position.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                        lineNumber: 324,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                className: \"text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Required Skills:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-1\",\n                                                children: position.requirements.map((req, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded\",\n                                                        children: req\n                                                    }, index, false, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                lineNumber: 331,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                        lineNumber: 329,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                className: \"text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Benefits:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                lineNumber: 342,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-1\",\n                                                children: position.benefits.map((benefit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"bg-emerald-100 text-emerald-800 text-xs px-2 py-1 rounded\",\n                                                        children: benefit\n                                                    }, index, false, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                lineNumber: 343,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center pt-4 border-t border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"Posted \",\n                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatDate)(position.postedDate)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                lineNumber: 354,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"bg-emerald-600 text-white px-4 py-2 rounded text-sm hover:bg-emerald-700 transition-colors\",\n                                                        children: \"View Details\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"border border-emerald-600 text-emerald-600 px-4 py-2 rounded text-sm hover:bg-emerald-50 transition-colors\",\n                                                        children: \"Find Matches\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                lineNumber: 357,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                        lineNumber: 353,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, position.id, true, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                lineNumber: 284,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                        lineNumber: 282,\n                        columnNumber: 9\n                    }, this),\n                    sortedPositions.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400 text-6xl mb-4\",\n                                children: \"\\uD83D\\uDCCB\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                lineNumber: 372,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                children: \"No positions found\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                lineNumber: 373,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Try adjusting your search or filters to see more results.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                lineNumber: 374,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                        lineNumber: 371,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                lineNumber: 209,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_VisualizationModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: showVisualization,\n                onClose: ()=>setShowVisualization(false),\n                data: graphData,\n                title: \"Positions Network\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                lineNumber: 380,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n        lineNumber: 204,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/positions.js\n");

/***/ }),

/***/ "(pages-dir-node)/./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "@tweenjs/tween.js":
/*!************************************!*\
  !*** external "@tweenjs/tween.js" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@tweenjs/tween.js");;

/***/ }),

/***/ "accessor-fn":
/*!******************************!*\
  !*** external "accessor-fn" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = import("accessor-fn");;

/***/ }),

/***/ "d3-array":
/*!***************************!*\
  !*** external "d3-array" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-array");;

/***/ }),

/***/ "d3-axis":
/*!**************************!*\
  !*** external "d3-axis" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-axis");;

/***/ }),

/***/ "d3-brush":
/*!***************************!*\
  !*** external "d3-brush" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-brush");;

/***/ }),

/***/ "d3-chord":
/*!***************************!*\
  !*** external "d3-chord" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-chord");;

/***/ }),

/***/ "d3-color":
/*!***************************!*\
  !*** external "d3-color" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-color");;

/***/ }),

/***/ "d3-contour":
/*!*****************************!*\
  !*** external "d3-contour" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-contour");;

/***/ }),

/***/ "d3-delaunay":
/*!******************************!*\
  !*** external "d3-delaunay" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-delaunay");;

/***/ }),

/***/ "d3-dispatch":
/*!******************************!*\
  !*** external "d3-dispatch" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-dispatch");;

/***/ }),

/***/ "d3-drag":
/*!**************************!*\
  !*** external "d3-drag" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-drag");;

/***/ }),

/***/ "d3-dsv":
/*!*************************!*\
  !*** external "d3-dsv" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-dsv");;

/***/ }),

/***/ "d3-ease":
/*!**************************!*\
  !*** external "d3-ease" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-ease");;

/***/ }),

/***/ "d3-fetch":
/*!***************************!*\
  !*** external "d3-fetch" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-fetch");;

/***/ }),

/***/ "d3-force":
/*!***************************!*\
  !*** external "d3-force" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-force");;

/***/ }),

/***/ "d3-format":
/*!****************************!*\
  !*** external "d3-format" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-format");;

/***/ }),

/***/ "d3-geo":
/*!*************************!*\
  !*** external "d3-geo" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-geo");;

/***/ }),

/***/ "d3-hierarchy":
/*!*******************************!*\
  !*** external "d3-hierarchy" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-hierarchy");;

/***/ }),

/***/ "d3-interpolate":
/*!*********************************!*\
  !*** external "d3-interpolate" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-interpolate");;

/***/ }),

/***/ "d3-path":
/*!**************************!*\
  !*** external "d3-path" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-path");;

/***/ }),

/***/ "d3-polygon":
/*!*****************************!*\
  !*** external "d3-polygon" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-polygon");;

/***/ }),

/***/ "d3-quadtree":
/*!******************************!*\
  !*** external "d3-quadtree" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-quadtree");;

/***/ }),

/***/ "d3-random":
/*!****************************!*\
  !*** external "d3-random" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-random");;

/***/ }),

/***/ "d3-scale":
/*!***************************!*\
  !*** external "d3-scale" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-scale");;

/***/ }),

/***/ "d3-scale-chromatic":
/*!*************************************!*\
  !*** external "d3-scale-chromatic" ***!
  \*************************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-scale-chromatic");;

/***/ }),

/***/ "d3-selection":
/*!*******************************!*\
  !*** external "d3-selection" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-selection");;

/***/ }),

/***/ "d3-shape":
/*!***************************!*\
  !*** external "d3-shape" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-shape");;

/***/ }),

/***/ "d3-time":
/*!**************************!*\
  !*** external "d3-time" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-time");;

/***/ }),

/***/ "d3-time-format":
/*!*********************************!*\
  !*** external "d3-time-format" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-time-format");;

/***/ }),

/***/ "d3-timer":
/*!***************************!*\
  !*** external "d3-timer" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-timer");;

/***/ }),

/***/ "d3-transition":
/*!********************************!*\
  !*** external "d3-transition" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-transition");;

/***/ }),

/***/ "d3-zoom":
/*!**************************!*\
  !*** external "d3-zoom" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-zoom");;

/***/ }),

/***/ "float-tooltip":
/*!********************************!*\
  !*** external "float-tooltip" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = import("float-tooltip");;

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "kapsule":
/*!**************************!*\
  !*** external "kapsule" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = import("kapsule");;

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "polished":
/*!***************************!*\
  !*** external "polished" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("polished");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "three-forcegraph":
/*!***********************************!*\
  !*** external "three-forcegraph" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = import("three-forcegraph");;

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/d3"], () => (__webpack_exec__("(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fpositions&preferredRegion=&absolutePagePath=.%2Fpages%2Fpositions.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();