/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/positions";
exports.ids = ["pages/positions"];
exports.modules = {

/***/ "(pages-dir-node)/./components/Layout.js":
/*!******************************!*\
  !*** ./components/Layout.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Layout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Navigation */ \"(pages-dir-node)/./components/Navigation.js\");\n\n\nfunction Layout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Navigation__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Layout.js\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container-app section-padding\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Layout.js\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Layout.js\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL2NvbXBvbmVudHMvTGF5b3V0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXFDO0FBRXRCLFNBQVNDLE9BQU8sRUFBRUMsUUFBUSxFQUFFO0lBQ3pDLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0osbURBQVVBOzs7OzswQkFDWCw4REFBQ0s7Z0JBQUtELFdBQVU7MEJBQWlDRjs7Ozs7Ozs7Ozs7O0FBR3ZEIiwic291cmNlcyI6WyIvVXNlcnMvYnJhZHlnZW9yZ2VuL0RvY3VtZW50cy93b3Jrc3BhY2UvY2FuZGlkLWNvbm5lY3Rpb25zL2NvbXBvbmVudHMvTGF5b3V0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBOYXZpZ2F0aW9uIGZyb20gJy4vTmF2aWdhdGlvbidcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTGF5b3V0KHsgY2hpbGRyZW4gfSkge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLXdoaXRlXCI+XG4gICAgICA8TmF2aWdhdGlvbiAvPlxuICAgICAgPG1haW4gY2xhc3NOYW1lPVwiY29udGFpbmVyLWFwcCBzZWN0aW9uLXBhZGRpbmdcIj57Y2hpbGRyZW59PC9tYWluPlxuICAgIDwvZGl2PlxuICApXG59Il0sIm5hbWVzIjpbIk5hdmlnYXRpb24iLCJMYXlvdXQiLCJjaGlsZHJlbiIsImRpdiIsImNsYXNzTmFtZSIsIm1haW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/Layout.js\n");

/***/ }),

/***/ "(pages-dir-node)/./components/Navigation.js":
/*!**********************************!*\
  !*** ./components/Navigation.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(pages-dir-node)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-node)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nfunction Navigation() {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const navItems = [\n        {\n            name: 'Dashboard',\n            path: '/',\n            icon: '🏠'\n        },\n        {\n            name: 'Matches',\n            path: '/matches',\n            icon: '🎯'\n        },\n        {\n            name: 'Job Seekers',\n            path: '/job-seekers',\n            icon: '👥'\n        },\n        {\n            name: 'Hiring Authorities',\n            path: '/hiring-authorities',\n            icon: '👔'\n        },\n        {\n            name: 'Companies',\n            path: '/companies',\n            icon: '🏢'\n        },\n        {\n            name: 'Positions',\n            path: '/positions',\n            icon: '📋'\n        },\n        {\n            name: 'Skills',\n            path: '/skills',\n            icon: '🛠️'\n        },\n        {\n            name: 'Visualizations',\n            path: '/visualizations',\n            icon: '📊'\n        },\n        {\n            name: 'Network View',\n            path: '/global-view',\n            icon: '🌐'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-white shadow-soft border-b border-candid-gray-200\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-app\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0 flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/\",\n                                className: \"flex items-center space-x-3 group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 relative group-hover:scale-105 transition-transform duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            viewBox: \"0 0 48 48\",\n                                            className: \"w-full h-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"24\",\n                                                    cy: \"24\",\n                                                    r: \"22\",\n                                                    fill: \"none\",\n                                                    stroke: \"#1e3a8a\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 32,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"24\",\n                                                    cy: \"12\",\n                                                    r: \"3\",\n                                                    fill: \"#00d4ff\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 34,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"36\",\n                                                    cy: \"24\",\n                                                    r: \"3\",\n                                                    fill: \"#00d4ff\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 35,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"24\",\n                                                    cy: \"36\",\n                                                    r: \"3\",\n                                                    fill: \"#00d4ff\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 36,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"12\",\n                                                    cy: \"24\",\n                                                    r: \"3\",\n                                                    fill: \"#00d4ff\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 37,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"24\",\n                                                    cy: \"24\",\n                                                    r: \"4\",\n                                                    fill: \"#1e3a8a\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 38,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                    x1: \"24\",\n                                                    y1: \"15\",\n                                                    x2: \"24\",\n                                                    y2: \"20\",\n                                                    stroke: \"#00d4ff\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 40,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                    x1: \"33\",\n                                                    y1: \"24\",\n                                                    x2: \"28\",\n                                                    y2: \"24\",\n                                                    stroke: \"#00d4ff\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 41,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                    x1: \"24\",\n                                                    y1: \"33\",\n                                                    x2: \"24\",\n                                                    y2: \"28\",\n                                                    stroke: \"#00d4ff\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 42,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                    x1: \"15\",\n                                                    y1: \"24\",\n                                                    x2: \"20\",\n                                                    y2: \"24\",\n                                                    stroke: \"#00d4ff\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 43,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                            lineNumber: 30,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 29,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden sm:block\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold text-secondary-800 group-hover:text-primary-500 transition-colors duration-200\",\n                                                children: \"Candid Connections\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                lineNumber: 47,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-candid-gray-600 -mt-1\",\n                                                children: \"Katra Platform\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                lineNumber: 50,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                lineNumber: 27,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1\",\n                                children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: item.path,\n                                        className: `flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${router.pathname === item.path ? 'nav-link-active' : 'nav-link'}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-base\",\n                                                children: item.icon\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                lineNumber: 68,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                lineNumber: 69,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, item.path, true, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 59,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                lineNumber: 57,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/admin\",\n                                    className: \"btn-outline text-sm py-2 px-4\",\n                                    children: \"⚙️ Admin\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/visualizations\",\n                                    className: \"btn-outline text-sm py-2 px-4\",\n                                    children: \"\\uD83D\\uDCCA Visualize\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"https://portal.candid-connections.com/user/login\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"btn-primary text-sm py-2 px-4\",\n                                    children: \"Portal Login\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),\n                                className: \"p-2 rounded-lg text-candid-navy-600 hover:text-primary-600 hover:bg-primary-50 transition-colors duration-200\",\n                                \"aria-label\": \"Toggle mobile menu\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 108,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M4 6h16M4 12h16M4 18h16\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 110,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                    lineNumber: 106,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this),\n                isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"lg:hidden border-t border-candid-gray-200 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: item.path,\n                                    onClick: ()=>setIsMobileMenuOpen(false),\n                                    className: `flex items-center space-x-3 px-4 py-3 rounded-lg text-base font-medium transition-all duration-200 ${router.pathname === item.path ? 'nav-link-active' : 'nav-link'}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg\",\n                                            children: item.icon\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                            lineNumber: 132,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                            lineNumber: 133,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, item.path, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                    lineNumber: 122,\n                                    columnNumber: 17\n                                }, this)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-4 space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/visualizations\",\n                                        onClick: ()=>setIsMobileMenuOpen(false),\n                                        className: \"block w-full btn-outline text-center\",\n                                        children: \"\\uD83D\\uDCCA Visualize\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 139,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"https://portal.candid-connections.com/user/login\",\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        className: \"block w-full btn-primary text-center\",\n                                        children: \"Portal Login\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 146,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                lineNumber: 138,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                        lineNumber: 120,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                    lineNumber: 119,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/Navigation.js\n");

/***/ }),

/***/ "(pages-dir-node)/./components/ui/DetailModal.js":
/*!**************************************!*\
  !*** ./components/ui/DetailModal.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DetailModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-node)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Modal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Modal */ \"(pages-dir-node)/./components/ui/Modal.js\");\n\n\n\n\nfunction DetailModal({ isOpen, onClose, entity, entityType, onFindTalent, onFindMatches }) {\n    const [aiDescription, setAiDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DetailModal.useEffect\": ()=>{\n            if (isOpen && entity) {\n                generateAIDescription();\n            }\n        }\n    }[\"DetailModal.useEffect\"], [\n        isOpen,\n        entity\n    ]);\n    const generateAIDescription = async ()=>{\n        if (!entity) return;\n        setLoading(true);\n        try {\n            const response = await fetch('/api/ai/describe', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    entity,\n                    entityType\n                })\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setAiDescription(data.description);\n            } else {\n                setAiDescription('Unable to generate description at this time.');\n            }\n        } catch (error) {\n            console.error('Error generating AI description:', error);\n            setAiDescription('Unable to generate description at this time.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleFindTalent = ()=>{\n        if (onFindTalent) {\n            onFindTalent(entity);\n        } else {\n            // Default behavior - navigate to job seekers with skill filter\n            const skillName = entity.name || entity.title;\n            router.push(`/job-seekers?skill=${encodeURIComponent(skillName)}`);\n        }\n        onClose();\n    };\n    const handleFindMatches = ()=>{\n        if (onFindMatches) {\n            onFindMatches(entity);\n        } else {\n            // Default behavior - navigate to matches with entity filter\n            const entityId = entity._key || entity.id;\n            router.push(`/matches?${entityType}=${entityId}`);\n        }\n        onClose();\n    };\n    const renderEntitySpecificContent = ()=>{\n        switch(entityType){\n            case 'skill':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: \"Category\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                        lineNumber: 82,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-900\",\n                                        children: entity.category || 'N/A'\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                        lineNumber: 83,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                lineNumber: 81,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: \"Market Demand\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                        lineNumber: 86,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `inline-flex px-2 py-1 text-xs font-medium rounded-full ${entity.demand === 'Very High' ? 'bg-red-100 text-red-800' : entity.demand === 'High' ? 'bg-orange-100 text-orange-800' : entity.demand === 'Medium' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'}`,\n                                        children: entity.demand || 'Medium'\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                        lineNumber: 87,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                lineNumber: 85,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                        lineNumber: 80,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                    lineNumber: 79,\n                    columnNumber: 11\n                }, this);\n            case 'position':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"Level\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                            lineNumber: 105,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-900\",\n                                            children: entity.level || 'N/A'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                            lineNumber: 106,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"Type\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                            lineNumber: 109,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-900\",\n                                            children: entity.type || 'N/A'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                            lineNumber: 110,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                    lineNumber: 108,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                            lineNumber: 103,\n                            columnNumber: 13\n                        }, this),\n                        entity.requirements && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-sm font-medium text-gray-700\",\n                                    children: \"Requirements\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                    lineNumber: 115,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-1 mt-1\",\n                                    children: entity.requirements.map((req, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"badge badge-secondary text-xs\",\n                                            children: req\n                                        }, index, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                            lineNumber: 118,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                    lineNumber: 116,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                            lineNumber: 114,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                    lineNumber: 102,\n                    columnNumber: 11\n                }, this);\n            case 'company':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"Industry\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                            lineNumber: 133,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-900\",\n                                            children: entity.industry || 'N/A'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                            lineNumber: 134,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                    lineNumber: 132,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"Size\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                            lineNumber: 137,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-900\",\n                                            children: [\n                                                entity.employeeCount,\n                                                \" employees\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                            lineNumber: 138,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                    lineNumber: 136,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                            lineNumber: 131,\n                            columnNumber: 13\n                        }, this),\n                        entity.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-sm font-medium text-gray-700\",\n                                    children: \"Description\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                    lineNumber: 143,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-900\",\n                                    children: entity.description\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                    lineNumber: 144,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                            lineNumber: 142,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                    lineNumber: 130,\n                    columnNumber: 11\n                }, this);\n            default:\n                return null;\n        }\n    };\n    if (!entity) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Modal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        isOpen: isOpen,\n        onClose: onClose,\n        title: `${entityType.charAt(0).toUpperCase() + entityType.slice(1)} Details`,\n        size: \"lg\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-secondary-800\",\n                            children: entity.name || entity.title\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, this),\n                        entity.role && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-candid-gray-600\",\n                            children: entity.role\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                            lineNumber: 171,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                    lineNumber: 166,\n                    columnNumber: 9\n                }, this),\n                renderEntitySpecificContent(),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t pt-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"text-sm font-medium text-gray-700 mb-2 block\",\n                            children: \"AI Analysis\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, this),\n                        loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"loading-spinner w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                    lineNumber: 185,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-candid-gray-600\",\n                                    children: \"Generating intelligent analysis...\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                    lineNumber: 186,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                            lineNumber: 184,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-blue-900 leading-relaxed\",\n                                children: aiDescription || 'No description available.'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                lineNumber: 190,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                            lineNumber: 189,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                    lineNumber: 179,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-3 pt-4 border-t\",\n                    children: [\n                        (entityType === 'skill' || entityType === 'position') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleFindTalent,\n                            className: \"btn-primary flex-1\",\n                            children: \"Find Talent\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                            lineNumber: 200,\n                            columnNumber: 13\n                        }, this),\n                        entityType !== 'skill' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleFindMatches,\n                            className: \"btn-outline flex-1\",\n                            children: \"Find Matches\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                            lineNumber: 209,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"btn-outline\",\n                            children: \"Close\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                    lineNumber: 198,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n            lineNumber: 164,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n        lineNumber: 158,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL2NvbXBvbmVudHMvdWkvRGV0YWlsTW9kYWwuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQTJDO0FBQ0o7QUFDWjtBQUVaLFNBQVNJLFlBQVksRUFDbENDLE1BQU0sRUFDTkMsT0FBTyxFQUNQQyxNQUFNLEVBQ05DLFVBQVUsRUFDVkMsWUFBWSxFQUNaQyxhQUFhLEVBQ2Q7SUFDQyxNQUFNLENBQUNDLGVBQWVDLGlCQUFpQixHQUFHWiwrQ0FBUUEsQ0FBQztJQUNuRCxNQUFNLENBQUNhLFNBQVNDLFdBQVcsR0FBR2QsK0NBQVFBLENBQUM7SUFDdkMsTUFBTWUsU0FBU2Isc0RBQVNBO0lBRXhCRCxnREFBU0E7aUNBQUM7WUFDUixJQUFJSSxVQUFVRSxRQUFRO2dCQUNwQlM7WUFDRjtRQUNGO2dDQUFHO1FBQUNYO1FBQVFFO0tBQU87SUFFbkIsTUFBTVMsd0JBQXdCO1FBQzVCLElBQUksQ0FBQ1QsUUFBUTtRQUViTyxXQUFXO1FBQ1gsSUFBSTtZQUNGLE1BQU1HLFdBQVcsTUFBTUMsTUFBTSxvQkFBb0I7Z0JBQy9DQyxRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFDbkJoQjtvQkFDQUM7Z0JBQ0Y7WUFDRjtZQUVBLElBQUlTLFNBQVNPLEVBQUUsRUFBRTtnQkFDZixNQUFNQyxPQUFPLE1BQU1SLFNBQVNTLElBQUk7Z0JBQ2hDZCxpQkFBaUJhLEtBQUtFLFdBQVc7WUFDbkMsT0FBTztnQkFDTGYsaUJBQWlCO1lBQ25CO1FBQ0YsRUFBRSxPQUFPZ0IsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsb0NBQW9DQTtZQUNsRGhCLGlCQUFpQjtRQUNuQixTQUFVO1lBQ1JFLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTWdCLG1CQUFtQjtRQUN2QixJQUFJckIsY0FBYztZQUNoQkEsYUFBYUY7UUFDZixPQUFPO1lBQ0wsK0RBQStEO1lBQy9ELE1BQU13QixZQUFZeEIsT0FBT3lCLElBQUksSUFBSXpCLE9BQU8wQixLQUFLO1lBQzdDbEIsT0FBT21CLElBQUksQ0FBQyxDQUFDLG1CQUFtQixFQUFFQyxtQkFBbUJKLFlBQVk7UUFDbkU7UUFDQXpCO0lBQ0Y7SUFFQSxNQUFNOEIsb0JBQW9CO1FBQ3hCLElBQUkxQixlQUFlO1lBQ2pCQSxjQUFjSDtRQUNoQixPQUFPO1lBQ0wsNERBQTREO1lBQzVELE1BQU04QixXQUFXOUIsT0FBTytCLElBQUksSUFBSS9CLE9BQU9nQyxFQUFFO1lBQ3pDeEIsT0FBT21CLElBQUksQ0FBQyxDQUFDLFNBQVMsRUFBRTFCLFdBQVcsQ0FBQyxFQUFFNkIsVUFBVTtRQUNsRDtRQUNBL0I7SUFDRjtJQUVBLE1BQU1rQyw4QkFBOEI7UUFDbEMsT0FBUWhDO1lBQ04sS0FBSztnQkFDSCxxQkFDRSw4REFBQ2lDO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEOztrREFDQyw4REFBQ0U7d0NBQU1ELFdBQVU7a0RBQW9DOzs7Ozs7a0RBQ3JELDhEQUFDRTt3Q0FBRUYsV0FBVTtrREFBeUJuQyxPQUFPc0MsUUFBUSxJQUFJOzs7Ozs7Ozs7Ozs7MENBRTNELDhEQUFDSjs7a0RBQ0MsOERBQUNFO3dDQUFNRCxXQUFVO2tEQUFvQzs7Ozs7O2tEQUNyRCw4REFBQ0k7d0NBQUtKLFdBQVcsQ0FBQyx1REFBdUQsRUFDdkVuQyxPQUFPd0MsTUFBTSxLQUFLLGNBQWMsNEJBQ2hDeEMsT0FBT3dDLE1BQU0sS0FBSyxTQUFTLGtDQUMzQnhDLE9BQU93QyxNQUFNLEtBQUssV0FBVyxrQ0FDN0IsNkJBQ0E7a0RBQ0N4QyxPQUFPd0MsTUFBTSxJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQU85QixLQUFLO2dCQUNILHFCQUNFLDhEQUFDTjtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7O3NEQUNDLDhEQUFDRTs0Q0FBTUQsV0FBVTtzREFBb0M7Ozs7OztzREFDckQsOERBQUNFOzRDQUFFRixXQUFVO3NEQUF5Qm5DLE9BQU95QyxLQUFLLElBQUk7Ozs7Ozs7Ozs7Ozs4Q0FFeEQsOERBQUNQOztzREFDQyw4REFBQ0U7NENBQU1ELFdBQVU7c0RBQW9DOzs7Ozs7c0RBQ3JELDhEQUFDRTs0Q0FBRUYsV0FBVTtzREFBeUJuQyxPQUFPMEMsSUFBSSxJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7d0JBR3hEMUMsT0FBTzJDLFlBQVksa0JBQ2xCLDhEQUFDVDs7OENBQ0MsOERBQUNFO29DQUFNRCxXQUFVOzhDQUFvQzs7Ozs7OzhDQUNyRCw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ1puQyxPQUFPMkMsWUFBWSxDQUFDQyxHQUFHLENBQUMsQ0FBQ0MsS0FBS0Msc0JBQzdCLDhEQUFDUDs0Q0FBaUJKLFdBQVU7c0RBQ3pCVTsyQ0FEUUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFVekIsS0FBSztnQkFDSCxxQkFDRSw4REFBQ1o7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEOztzREFDQyw4REFBQ0U7NENBQU1ELFdBQVU7c0RBQW9DOzs7Ozs7c0RBQ3JELDhEQUFDRTs0Q0FBRUYsV0FBVTtzREFBeUJuQyxPQUFPK0MsUUFBUSxJQUFJOzs7Ozs7Ozs7Ozs7OENBRTNELDhEQUFDYjs7c0RBQ0MsOERBQUNFOzRDQUFNRCxXQUFVO3NEQUFvQzs7Ozs7O3NEQUNyRCw4REFBQ0U7NENBQUVGLFdBQVU7O2dEQUF5Qm5DLE9BQU9nRCxhQUFhO2dEQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3dCQUc5RGhELE9BQU9vQixXQUFXLGtCQUNqQiw4REFBQ2M7OzhDQUNDLDhEQUFDRTtvQ0FBTUQsV0FBVTs4Q0FBb0M7Ozs7Ozs4Q0FDckQsOERBQUNFO29DQUFFRixXQUFVOzhDQUF5Qm5DLE9BQU9vQixXQUFXOzs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFNbEU7Z0JBQ0UsT0FBTztRQUNYO0lBQ0Y7SUFFQSxJQUFJLENBQUNwQixRQUFRLE9BQU87SUFFcEIscUJBQ0UsOERBQUNKLDhDQUFLQTtRQUNKRSxRQUFRQTtRQUNSQyxTQUFTQTtRQUNUMkIsT0FBTyxHQUFHekIsV0FBV2dELE1BQU0sQ0FBQyxHQUFHQyxXQUFXLEtBQUtqRCxXQUFXa0QsS0FBSyxDQUFDLEdBQUcsUUFBUSxDQUFDO1FBQzVFQyxNQUFLO2tCQUVMLDRFQUFDbEI7WUFBSUMsV0FBVTs7OEJBRWIsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ2tCOzRCQUFHbEIsV0FBVTtzQ0FDWG5DLE9BQU95QixJQUFJLElBQUl6QixPQUFPMEIsS0FBSzs7Ozs7O3dCQUU3QjFCLE9BQU9zRCxJQUFJLGtCQUNWLDhEQUFDakI7NEJBQUVGLFdBQVU7c0NBQXdCbkMsT0FBT3NELElBQUk7Ozs7Ozs7Ozs7OztnQkFLbkRyQjs4QkFHRCw4REFBQ0M7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDQzs0QkFBTUQsV0FBVTtzQ0FBK0M7Ozs7Ozt3QkFHL0Q3Qix3QkFDQyw4REFBQzRCOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7Ozs7Ozs4Q0FDZiw4REFBQ0k7b0NBQUtKLFdBQVU7OENBQStCOzs7Ozs7Ozs7OztpREFHakQsOERBQUNEOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDRTtnQ0FBRUYsV0FBVTswQ0FDVi9CLGlCQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBTzFCLDhEQUFDOEI7b0JBQUlDLFdBQVU7O3dCQUNYbEMsQ0FBQUEsZUFBZSxXQUFXQSxlQUFlLFVBQVMsbUJBQ2xELDhEQUFDc0Q7NEJBQ0NDLFNBQVNqQzs0QkFDVFksV0FBVTtzQ0FDWDs7Ozs7O3dCQUtGbEMsZUFBZSx5QkFDZCw4REFBQ3NEOzRCQUNDQyxTQUFTM0I7NEJBQ1RNLFdBQVU7c0NBQ1g7Ozs7OztzQ0FLSCw4REFBQ29COzRCQUNDQyxTQUFTekQ7NEJBQ1RvQyxXQUFVO3NDQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU9YIiwic291cmNlcyI6WyIvVXNlcnMvYnJhZHlnZW9yZ2VuL0RvY3VtZW50cy93b3Jrc3BhY2UvY2FuZGlkLWNvbm5lY3Rpb25zL2NvbXBvbmVudHMvdWkvRGV0YWlsTW9kYWwuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9yb3V0ZXInXG5pbXBvcnQgTW9kYWwgZnJvbSAnLi9Nb2RhbCdcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRGV0YWlsTW9kYWwoeyBcbiAgaXNPcGVuLCBcbiAgb25DbG9zZSwgXG4gIGVudGl0eSwgXG4gIGVudGl0eVR5cGUsIFxuICBvbkZpbmRUYWxlbnQsXG4gIG9uRmluZE1hdGNoZXMgXG59KSB7XG4gIGNvbnN0IFthaURlc2NyaXB0aW9uLCBzZXRBaURlc2NyaXB0aW9uXSA9IHVzZVN0YXRlKCcnKVxuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKClcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChpc09wZW4gJiYgZW50aXR5KSB7XG4gICAgICBnZW5lcmF0ZUFJRGVzY3JpcHRpb24oKVxuICAgIH1cbiAgfSwgW2lzT3BlbiwgZW50aXR5XSlcblxuICBjb25zdCBnZW5lcmF0ZUFJRGVzY3JpcHRpb24gPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFlbnRpdHkpIHJldHVyblxuICAgIFxuICAgIHNldExvYWRpbmcodHJ1ZSlcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9haS9kZXNjcmliZScsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgZW50aXR5LFxuICAgICAgICAgIGVudGl0eVR5cGVcbiAgICAgICAgfSlcbiAgICAgIH0pXG5cbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpXG4gICAgICAgIHNldEFpRGVzY3JpcHRpb24oZGF0YS5kZXNjcmlwdGlvbilcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHNldEFpRGVzY3JpcHRpb24oJ1VuYWJsZSB0byBnZW5lcmF0ZSBkZXNjcmlwdGlvbiBhdCB0aGlzIHRpbWUuJylcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZ2VuZXJhdGluZyBBSSBkZXNjcmlwdGlvbjonLCBlcnJvcilcbiAgICAgIHNldEFpRGVzY3JpcHRpb24oJ1VuYWJsZSB0byBnZW5lcmF0ZSBkZXNjcmlwdGlvbiBhdCB0aGlzIHRpbWUuJylcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBoYW5kbGVGaW5kVGFsZW50ID0gKCkgPT4ge1xuICAgIGlmIChvbkZpbmRUYWxlbnQpIHtcbiAgICAgIG9uRmluZFRhbGVudChlbnRpdHkpXG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIERlZmF1bHQgYmVoYXZpb3IgLSBuYXZpZ2F0ZSB0byBqb2Igc2Vla2VycyB3aXRoIHNraWxsIGZpbHRlclxuICAgICAgY29uc3Qgc2tpbGxOYW1lID0gZW50aXR5Lm5hbWUgfHwgZW50aXR5LnRpdGxlXG4gICAgICByb3V0ZXIucHVzaChgL2pvYi1zZWVrZXJzP3NraWxsPSR7ZW5jb2RlVVJJQ29tcG9uZW50KHNraWxsTmFtZSl9YClcbiAgICB9XG4gICAgb25DbG9zZSgpXG4gIH1cblxuICBjb25zdCBoYW5kbGVGaW5kTWF0Y2hlcyA9ICgpID0+IHtcbiAgICBpZiAob25GaW5kTWF0Y2hlcykge1xuICAgICAgb25GaW5kTWF0Y2hlcyhlbnRpdHkpXG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIERlZmF1bHQgYmVoYXZpb3IgLSBuYXZpZ2F0ZSB0byBtYXRjaGVzIHdpdGggZW50aXR5IGZpbHRlclxuICAgICAgY29uc3QgZW50aXR5SWQgPSBlbnRpdHkuX2tleSB8fCBlbnRpdHkuaWRcbiAgICAgIHJvdXRlci5wdXNoKGAvbWF0Y2hlcz8ke2VudGl0eVR5cGV9PSR7ZW50aXR5SWR9YClcbiAgICB9XG4gICAgb25DbG9zZSgpXG4gIH1cblxuICBjb25zdCByZW5kZXJFbnRpdHlTcGVjaWZpY0NvbnRlbnQgPSAoKSA9PiB7XG4gICAgc3dpdGNoIChlbnRpdHlUeXBlKSB7XG4gICAgICBjYXNlICdza2lsbCc6XG4gICAgICAgIHJldHVybiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDBcIj5DYXRlZ29yeTwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktOTAwXCI+e2VudGl0eS5jYXRlZ29yeSB8fCAnTi9BJ308L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDBcIj5NYXJrZXQgRGVtYW5kPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2BpbmxpbmUtZmxleCBweC0yIHB5LTEgdGV4dC14cyBmb250LW1lZGl1bSByb3VuZGVkLWZ1bGwgJHtcbiAgICAgICAgICAgICAgICAgIGVudGl0eS5kZW1hbmQgPT09ICdWZXJ5IEhpZ2gnID8gJ2JnLXJlZC0xMDAgdGV4dC1yZWQtODAwJyA6XG4gICAgICAgICAgICAgICAgICBlbnRpdHkuZGVtYW5kID09PSAnSGlnaCcgPyAnYmctb3JhbmdlLTEwMCB0ZXh0LW9yYW5nZS04MDAnIDpcbiAgICAgICAgICAgICAgICAgIGVudGl0eS5kZW1hbmQgPT09ICdNZWRpdW0nID8gJ2JnLXllbGxvdy0xMDAgdGV4dC15ZWxsb3ctODAwJyA6XG4gICAgICAgICAgICAgICAgICAnYmctZ3JheS0xMDAgdGV4dC1ncmF5LTgwMCdcbiAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICB7ZW50aXR5LmRlbWFuZCB8fCAnTWVkaXVtJ31cbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIClcbiAgICAgIFxuICAgICAgY2FzZSAncG9zaXRpb24nOlxuICAgICAgICByZXR1cm4gKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+TGV2ZWw8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTkwMFwiPntlbnRpdHkubGV2ZWwgfHwgJ04vQSd9PC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+VHlwZTwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktOTAwXCI+e2VudGl0eS50eXBlIHx8ICdOL0EnfTwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIHtlbnRpdHkucmVxdWlyZW1lbnRzICYmIChcbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+UmVxdWlyZW1lbnRzPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGdhcC0xIG10LTFcIj5cbiAgICAgICAgICAgICAgICAgIHtlbnRpdHkucmVxdWlyZW1lbnRzLm1hcCgocmVxLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJiYWRnZSBiYWRnZS1zZWNvbmRhcnkgdGV4dC14c1wiPlxuICAgICAgICAgICAgICAgICAgICAgIHtyZXF9XG4gICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIClcbiAgICAgIFxuICAgICAgY2FzZSAnY29tcGFueSc6XG4gICAgICAgIHJldHVybiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDBcIj5JbmR1c3RyeTwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktOTAwXCI+e2VudGl0eS5pbmR1c3RyeSB8fCAnTi9BJ308L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDBcIj5TaXplPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS05MDBcIj57ZW50aXR5LmVtcGxveWVlQ291bnR9IGVtcGxveWVlczwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIHtlbnRpdHkuZGVzY3JpcHRpb24gJiYgKFxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDBcIj5EZXNjcmlwdGlvbjwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktOTAwXCI+e2VudGl0eS5kZXNjcmlwdGlvbn08L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKVxuICAgICAgXG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gbnVsbFxuICAgIH1cbiAgfVxuXG4gIGlmICghZW50aXR5KSByZXR1cm4gbnVsbFxuXG4gIHJldHVybiAoXG4gICAgPE1vZGFsXG4gICAgICBpc09wZW49e2lzT3Blbn1cbiAgICAgIG9uQ2xvc2U9e29uQ2xvc2V9XG4gICAgICB0aXRsZT17YCR7ZW50aXR5VHlwZS5jaGFyQXQoMCkudG9VcHBlckNhc2UoKSArIGVudGl0eVR5cGUuc2xpY2UoMSl9IERldGFpbHNgfVxuICAgICAgc2l6ZT1cImxnXCJcbiAgICA+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICB7LyogRW50aXR5IEhlYWRlciAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1zZWNvbmRhcnktODAwXCI+XG4gICAgICAgICAgICB7ZW50aXR5Lm5hbWUgfHwgZW50aXR5LnRpdGxlfVxuICAgICAgICAgIDwvaDI+XG4gICAgICAgICAge2VudGl0eS5yb2xlICYmIChcbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtY2FuZGlkLWdyYXktNjAwXCI+e2VudGl0eS5yb2xlfTwvcD5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogRW50aXR5LXNwZWNpZmljIGNvbnRlbnQgKi99XG4gICAgICAgIHtyZW5kZXJFbnRpdHlTcGVjaWZpY0NvbnRlbnQoKX1cblxuICAgICAgICB7LyogQUkgRGVzY3JpcHRpb24gKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYm9yZGVyLXQgcHQtNFwiPlxuICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMiBibG9ja1wiPlxuICAgICAgICAgICAgQUkgQW5hbHlzaXNcbiAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgIHtsb2FkaW5nID8gKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJsb2FkaW5nLXNwaW5uZXIgdy00IGgtNFwiPjwvZGl2PlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtY2FuZGlkLWdyYXktNjAwXCI+R2VuZXJhdGluZyBpbnRlbGxpZ2VudCBhbmFseXNpcy4uLjwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWJsdWUtNTAgYm9yZGVyIGJvcmRlci1ibHVlLTIwMCByb3VuZGVkLWxnIHAtNFwiPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtYmx1ZS05MDAgbGVhZGluZy1yZWxheGVkXCI+XG4gICAgICAgICAgICAgICAge2FpRGVzY3JpcHRpb24gfHwgJ05vIGRlc2NyaXB0aW9uIGF2YWlsYWJsZS4nfVxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogQWN0aW9uIEJ1dHRvbnMgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTMgcHQtNCBib3JkZXItdFwiPlxuICAgICAgICAgIHsoZW50aXR5VHlwZSA9PT0gJ3NraWxsJyB8fCBlbnRpdHlUeXBlID09PSAncG9zaXRpb24nKSAmJiAoXG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUZpbmRUYWxlbnR9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJ0bi1wcmltYXJ5IGZsZXgtMVwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIEZpbmQgVGFsZW50XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICApfVxuICAgICAgICAgIFxuICAgICAgICAgIHtlbnRpdHlUeXBlICE9PSAnc2tpbGwnICYmIChcbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlRmluZE1hdGNoZXN9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJ0bi1vdXRsaW5lIGZsZXgtMVwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIEZpbmQgTWF0Y2hlc1xuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgKX1cbiAgICAgICAgICBcbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXtvbkNsb3NlfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiYnRuLW91dGxpbmVcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIENsb3NlXG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9Nb2RhbD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlUm91dGVyIiwiTW9kYWwiLCJEZXRhaWxNb2RhbCIsImlzT3BlbiIsIm9uQ2xvc2UiLCJlbnRpdHkiLCJlbnRpdHlUeXBlIiwib25GaW5kVGFsZW50Iiwib25GaW5kTWF0Y2hlcyIsImFpRGVzY3JpcHRpb24iLCJzZXRBaURlc2NyaXB0aW9uIiwibG9hZGluZyIsInNldExvYWRpbmciLCJyb3V0ZXIiLCJnZW5lcmF0ZUFJRGVzY3JpcHRpb24iLCJyZXNwb25zZSIsImZldGNoIiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5Iiwib2siLCJkYXRhIiwianNvbiIsImRlc2NyaXB0aW9uIiwiZXJyb3IiLCJjb25zb2xlIiwiaGFuZGxlRmluZFRhbGVudCIsInNraWxsTmFtZSIsIm5hbWUiLCJ0aXRsZSIsInB1c2giLCJlbmNvZGVVUklDb21wb25lbnQiLCJoYW5kbGVGaW5kTWF0Y2hlcyIsImVudGl0eUlkIiwiX2tleSIsImlkIiwicmVuZGVyRW50aXR5U3BlY2lmaWNDb250ZW50IiwiZGl2IiwiY2xhc3NOYW1lIiwibGFiZWwiLCJwIiwiY2F0ZWdvcnkiLCJzcGFuIiwiZGVtYW5kIiwibGV2ZWwiLCJ0eXBlIiwicmVxdWlyZW1lbnRzIiwibWFwIiwicmVxIiwiaW5kZXgiLCJpbmR1c3RyeSIsImVtcGxveWVlQ291bnQiLCJjaGFyQXQiLCJ0b1VwcGVyQ2FzZSIsInNsaWNlIiwic2l6ZSIsImgyIiwicm9sZSIsImJ1dHRvbiIsIm9uQ2xpY2siXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/ui/DetailModal.js\n");

/***/ }),

/***/ "(pages-dir-node)/./components/ui/LinkButton.js":
/*!*************************************!*\
  !*** ./components/ui/LinkButton.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthorityLink: () => (/* binding */ AuthorityLink),\n/* harmony export */   CompanyLink: () => (/* binding */ CompanyLink),\n/* harmony export */   PositionLink: () => (/* binding */ PositionLink),\n/* harmony export */   SkillLink: () => (/* binding */ SkillLink),\n/* harmony export */   \"default\": () => (/* binding */ LinkButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(pages-dir-node)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n// Reusable link button component with consistent styling and hover effects\nfunction LinkButton({ href, children, variant = 'primary', size = 'sm', showPreview = false, previewContent = null, className = '', onClick, external = false }) {\n    const [showPreviewModal, setShowPreviewModal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const baseClasses = 'inline-flex items-center justify-center font-medium transition-all duration-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2';\n    const variants = {\n        primary: 'bg-primary-500 hover:bg-primary-600 text-white focus:ring-primary-500 shadow-sm hover:shadow-md',\n        secondary: 'bg-white hover:bg-gray-50 text-gray-700 border border-gray-300 hover:border-gray-400 focus:ring-gray-500 shadow-sm hover:shadow-md',\n        accent: 'bg-accent-500 hover:bg-accent-600 text-white focus:ring-accent-500 shadow-sm hover:shadow-md',\n        skill: 'bg-blue-50 hover:bg-blue-100 text-blue-700 border border-blue-200 hover:border-blue-300 focus:ring-blue-500',\n        company: 'bg-purple-50 hover:bg-purple-100 text-purple-700 border border-purple-200 hover:border-purple-300 focus:ring-purple-500',\n        authority: 'bg-cyan-50 hover:bg-cyan-100 text-cyan-700 border border-cyan-200 hover:border-cyan-300 focus:ring-cyan-500',\n        position: 'bg-orange-50 hover:bg-orange-100 text-orange-700 border border-orange-200 hover:border-orange-300 focus:ring-orange-500',\n        success: 'bg-green-500 hover:bg-green-600 text-white focus:ring-green-500 shadow-sm hover:shadow-md',\n        warning: 'bg-yellow-500 hover:bg-yellow-600 text-white focus:ring-yellow-500 shadow-sm hover:shadow-md',\n        danger: 'bg-red-500 hover:bg-red-600 text-white focus:ring-red-500 shadow-sm hover:shadow-md'\n    };\n    const sizes = {\n        xs: 'px-2 py-1 text-xs',\n        sm: 'px-3 py-1.5 text-sm',\n        md: 'px-4 py-2 text-sm',\n        lg: 'px-6 py-3 text-base'\n    };\n    const classes = `${baseClasses} ${variants[variant]} ${sizes[size]} ${className}`;\n    const handleClick = (e)=>{\n        if (onClick) {\n            e.preventDefault();\n            onClick();\n        }\n        if (showPreview && previewContent) {\n            e.preventDefault();\n            setShowPreviewModal(true);\n        }\n    };\n    const content = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"flex items-center space-x-1\",\n        children: [\n            children,\n            external && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-3 h-3 ml-1\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                    lineNumber: 58,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                lineNumber: 57,\n                columnNumber: 9\n            }, this),\n            showPreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-3 h-3 ml-1\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                lineNumber: 62,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n    if (external) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n            href: href,\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            className: classes,\n            onClick: handleClick,\n            children: content\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n            lineNumber: 72,\n            columnNumber: 7\n        }, this);\n    }\n    if (onClick || showPreview) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: classes,\n                    onClick: handleClick,\n                    children: content\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, this),\n                showPreviewModal && previewContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 z-50 overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 bg-black bg-opacity-50\",\n                            onClick: ()=>setShowPreviewModal(false)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                            lineNumber: 94,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex min-h-full items-center justify-center p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative bg-white rounded-xl shadow-xl w-full max-w-md transform transition-all\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-900\",\n                                                    children: \"Quick Preview\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowPreviewModal(false),\n                                                    className: \"text-gray-400 hover:text-gray-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M6 18L18 6M6 6l12 12\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                                                            lineNumber: 105,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                                                        lineNumber: 104,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                                                    lineNumber: 100,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                                            lineNumber: 98,\n                                            columnNumber: 19\n                                        }, this),\n                                        previewContent,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: href,\n                                                    className: \"btn-primary flex-1 text-center\",\n                                                    children: \"View Full Details\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowPreviewModal(false),\n                                                    className: \"btn-outline flex-1\",\n                                                    children: \"Close\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                                            lineNumber: 110,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                                    lineNumber: 97,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                                lineNumber: 96,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                            lineNumber: 95,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                    lineNumber: 93,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n        href: href,\n        className: classes,\n        onClick: handleClick,\n        children: content\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n        lineNumber: 131,\n        columnNumber: 5\n    }, this);\n}\n// Specialized link buttons for different entity types\nfunction SkillLink({ skill, children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LinkButton, {\n        href: `/skills?highlight=${skill._key || skill.id}`,\n        variant: \"skill\",\n        previewContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: skill.description || `Professional skill in ${skill.name}`\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                    lineNumber: 145,\n                    columnNumber: 11\n                }, void 0),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between text-xs\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"Category: \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: skill.category\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                                    lineNumber: 147,\n                                    columnNumber: 29\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                            lineNumber: 147,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"Demand: \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: skill.demand\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                                    lineNumber: 148,\n                                    columnNumber: 27\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                            lineNumber: 148,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                    lineNumber: 146,\n                    columnNumber: 11\n                }, void 0)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n            lineNumber: 144,\n            columnNumber: 9\n        }, void 0),\n        showPreview: true,\n        ...props,\n        children: children || skill.name\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n        lineNumber: 140,\n        columnNumber: 5\n    }, this);\n}\nfunction CompanyLink({ company, children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LinkButton, {\n        href: `/companies?highlight=${company._key || company.id}`,\n        variant: \"company\",\n        previewContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: company.description || `${company.industry} company`\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                    lineNumber: 167,\n                    columnNumber: 11\n                }, void 0),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between text-xs\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"Industry: \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: company.industry\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                                    lineNumber: 169,\n                                    columnNumber: 29\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                            lineNumber: 169,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"Size: \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: [\n                                        company.employeeCount,\n                                        \" employees\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                                    lineNumber: 170,\n                                    columnNumber: 25\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                            lineNumber: 170,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                    lineNumber: 168,\n                    columnNumber: 11\n                }, void 0)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n            lineNumber: 166,\n            columnNumber: 9\n        }, void 0),\n        showPreview: true,\n        ...props,\n        children: children || company.name\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n        lineNumber: 162,\n        columnNumber: 5\n    }, this);\n}\nfunction AuthorityLink({ authority, children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LinkButton, {\n        href: `/hiring-authorities?highlight=${authority._key || authority.id}`,\n        variant: \"authority\",\n        previewContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: [\n                        authority.role,\n                        \" with \",\n                        authority.hiringPower,\n                        \" hiring power\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                    lineNumber: 189,\n                    columnNumber: 11\n                }, void 0),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between text-xs\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"Level: \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: authority.level\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                                    lineNumber: 191,\n                                    columnNumber: 26\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                            lineNumber: 191,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"Decision Maker: \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: authority.decisionMaker ? 'Yes' : 'No'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                                    lineNumber: 192,\n                                    columnNumber: 35\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                            lineNumber: 192,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                    lineNumber: 190,\n                    columnNumber: 11\n                }, void 0)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n            lineNumber: 188,\n            columnNumber: 9\n        }, void 0),\n        showPreview: true,\n        ...props,\n        children: children || authority.name\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n        lineNumber: 184,\n        columnNumber: 5\n    }, this);\n}\nfunction PositionLink({ position, children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LinkButton, {\n        href: `/positions?highlight=${position._key || position.id}`,\n        variant: \"position\",\n        previewContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: [\n                        position.level,\n                        \" level \",\n                        position.type,\n                        \" position\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                    lineNumber: 211,\n                    columnNumber: 11\n                }, void 0),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: [\n                            \"Requirements: \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: position.requirements?.slice(0, 3).join(', ')\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                                lineNumber: 213,\n                                columnNumber: 33\n                            }, void 0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                        lineNumber: 213,\n                        columnNumber: 13\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n                    lineNumber: 212,\n                    columnNumber: 11\n                }, void 0)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n            lineNumber: 210,\n            columnNumber: 9\n        }, void 0),\n        showPreview: true,\n        ...props,\n        children: children || position.title\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/LinkButton.js\",\n        lineNumber: 206,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/ui/LinkButton.js\n");

/***/ }),

/***/ "(pages-dir-node)/./components/ui/Modal.js":
/*!********************************!*\
  !*** ./components/ui/Modal.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Modal),\n/* harmony export */   useModal: () => (/* binding */ useModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Modal({ isOpen, onClose, title, children, size = 'md', showCloseButton = true }) {\n    // Close modal on escape key\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Modal.useEffect\": ()=>{\n            const handleEscape = {\n                \"Modal.useEffect.handleEscape\": (e)=>{\n                    if (e.key === 'Escape' && isOpen) {\n                        onClose();\n                    }\n                }\n            }[\"Modal.useEffect.handleEscape\"];\n            if (isOpen) {\n                document.addEventListener('keydown', handleEscape);\n                document.body.style.overflow = 'hidden';\n            }\n            return ({\n                \"Modal.useEffect\": ()=>{\n                    document.removeEventListener('keydown', handleEscape);\n                    document.body.style.overflow = 'unset';\n                }\n            })[\"Modal.useEffect\"];\n        }\n    }[\"Modal.useEffect\"], [\n        isOpen,\n        onClose\n    ]);\n    if (!isOpen) return null;\n    const sizeClasses = {\n        sm: 'max-w-md',\n        md: 'max-w-2xl',\n        lg: 'max-w-4xl',\n        xl: 'max-w-6xl',\n        full: 'max-w-7xl'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 overflow-y-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 transition-opacity\",\n                onClick: onClose\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/Modal.js\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex min-h-full items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `relative bg-white rounded-xl shadow-xl w-full ${sizeClasses[size]} transform transition-all`,\n                    onClick: (e)=>e.stopPropagation(),\n                    children: [\n                        (title || showCloseButton) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n                            children: [\n                                title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-secondary-800\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/Modal.js\",\n                                    lineNumber: 58,\n                                    columnNumber: 17\n                                }, this),\n                                showCloseButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"ml-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 inline-flex items-center justify-center h-8 w-8 transition-colors\",\n                                    \"aria-label\": \"Close modal\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-3 h-3\",\n                                        \"aria-hidden\": \"true\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 14 14\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            stroke: \"currentColor\",\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: \"2\",\n                                            d: \"m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/Modal.js\",\n                                            lineNumber: 70,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/Modal.js\",\n                                        lineNumber: 69,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/Modal.js\",\n                                    lineNumber: 64,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/Modal.js\",\n                            lineNumber: 56,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/Modal.js\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/Modal.js\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/Modal.js\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/Modal.js\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n// Reusable modal hook\nfunction useModal() {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const openModal = ()=>setIsOpen(true);\n    const closeModal = ()=>setIsOpen(false);\n    return {\n        isOpen,\n        openModal,\n        closeModal\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/ui/Modal.js\n");

/***/ }),

/***/ "(pages-dir-node)/./lib/utils.js":
/*!**********************!*\
  !*** ./lib/utils.js ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateMatchScore: () => (/* binding */ calculateMatchScore),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   getEntityIcon: () => (/* binding */ getEntityIcon),\n/* harmony export */   getMatchColor: () => (/* binding */ getMatchColor),\n/* harmony export */   getRelationshipDescription: () => (/* binding */ getRelationshipDescription),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n// Format date to readable string\nconst formatDate = (date)=>{\n    if (!date) return '';\n    const d = new Date(date);\n    return d.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n    });\n};\n// Calculate match score between job seeker and position\nconst calculateMatchScore = (jobSeeker, position)=>{\n    if (!jobSeeker || !position) return 0;\n    // In a real implementation, this would compare skills and requirements\n    // For now, return a random score between 0-100\n    return Math.floor(Math.random() * 101);\n};\n// Generate color based on match score\nconst getMatchColor = (score)=>{\n    if (score >= 80) return 'bg-emerald-100 text-emerald-800';\n    if (score >= 60) return 'bg-green-100 text-green-800';\n    if (score >= 40) return 'bg-yellow-100 text-yellow-800';\n    return 'bg-red-100 text-red-800';\n};\n// Truncate text with ellipsis\nconst truncateText = (text, maxLength = 100)=>{\n    if (!text || text.length <= maxLength) return text;\n    return text.slice(0, maxLength) + '...';\n};\n// Generate entity icon based on type\nconst getEntityIcon = (type)=>{\n    switch(type){\n        case 'jobSeeker':\n            return '👤';\n        case 'company':\n            return '🏢';\n        case 'hiringAuthority':\n            return '👔';\n        case 'position':\n            return '📋';\n        case 'skill':\n            return '🔧';\n        default:\n            return '📄';\n    }\n};\n// Generate relationship description\nconst getRelationshipDescription = (type)=>{\n    switch(type){\n        case 'works_for':\n            return 'Works for';\n        case 'employs':\n            return 'Employs';\n        case 'posts':\n            return 'Posted by';\n        case 'requires':\n            return 'Requires';\n        case 'has_skill':\n            return 'Has skill';\n        case 'matched_to':\n            return 'Matched to';\n        default:\n            return 'Related to';\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./lib/utils.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fpositions&preferredRegion=&absolutePagePath=.%2Fpages%2Fpositions.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fpositions&preferredRegion=&absolutePagePath=.%2Fpages%2Fpositions.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages/module.compiled */ \"(pages-dir-node)/./node_modules/next/dist/server/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(pages-dir-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(pages-dir-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"(pages-dir-node)/./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"(pages-dir-node)/./pages/_app.js\");\n/* harmony import */ var _pages_positions_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages/positions.js */ \"(pages-dir-node)/./pages/positions.js\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_positions_js__WEBPACK_IMPORTED_MODULE_5__, 'default'));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_positions_js__WEBPACK_IMPORTED_MODULE_5__, 'getStaticProps');\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_positions_js__WEBPACK_IMPORTED_MODULE_5__, 'getStaticPaths');\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_positions_js__WEBPACK_IMPORTED_MODULE_5__, 'getServerSideProps');\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_positions_js__WEBPACK_IMPORTED_MODULE_5__, 'config');\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_positions_js__WEBPACK_IMPORTED_MODULE_5__, 'reportWebVitals');\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_positions_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticProps');\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_positions_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticPaths');\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_positions_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticParams');\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_positions_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerProps');\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_positions_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/positions\",\n        pathname: \"/positions\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    components: {\n        // default export might not exist when optimized for data only\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_positions_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtcm91dGUtbG9hZGVyL2luZGV4LmpzP2tpbmQ9UEFHRVMmcGFnZT0lMkZwb3NpdGlvbnMmcHJlZmVycmVkUmVnaW9uPSZhYnNvbHV0ZVBhZ2VQYXRoPS4lMkZwYWdlcyUyRnBvc2l0aW9ucy5qcyZhYnNvbHV0ZUFwcFBhdGg9cHJpdmF0ZS1uZXh0LXBhZ2VzJTJGX2FwcCZhYnNvbHV0ZURvY3VtZW50UGF0aD1wcml2YXRlLW5leHQtcGFnZXMlMkZfZG9jdW1lbnQmbWlkZGxld2FyZUNvbmZpZ0Jhc2U2ND1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXdGO0FBQ2hDO0FBQ0U7QUFDMUQ7QUFDeUQ7QUFDVjtBQUMvQztBQUNpRDtBQUNqRDtBQUNBLGlFQUFlLHdFQUFLLENBQUMsZ0RBQVEsWUFBWSxFQUFDO0FBQzFDO0FBQ08sdUJBQXVCLHdFQUFLLENBQUMsZ0RBQVE7QUFDckMsdUJBQXVCLHdFQUFLLENBQUMsZ0RBQVE7QUFDckMsMkJBQTJCLHdFQUFLLENBQUMsZ0RBQVE7QUFDekMsZUFBZSx3RUFBSyxDQUFDLGdEQUFRO0FBQzdCLHdCQUF3Qix3RUFBSyxDQUFDLGdEQUFRO0FBQzdDO0FBQ08sZ0NBQWdDLHdFQUFLLENBQUMsZ0RBQVE7QUFDOUMsZ0NBQWdDLHdFQUFLLENBQUMsZ0RBQVE7QUFDOUMsaUNBQWlDLHdFQUFLLENBQUMsZ0RBQVE7QUFDL0MsZ0NBQWdDLHdFQUFLLENBQUMsZ0RBQVE7QUFDOUMsb0NBQW9DLHdFQUFLLENBQUMsZ0RBQVE7QUFDekQ7QUFDTyx3QkFBd0Isa0dBQWdCO0FBQy9DO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxhQUFhLDhEQUFXO0FBQ3hCLGtCQUFrQixvRUFBZ0I7QUFDbEMsS0FBSztBQUNMLFlBQVk7QUFDWixDQUFDOztBQUVEIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUGFnZXNSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvcGFnZXMvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBob2lzdCB9IGZyb20gXCJuZXh0L2Rpc3QvYnVpbGQvdGVtcGxhdGVzL2hlbHBlcnNcIjtcbi8vIEltcG9ydCB0aGUgYXBwIGFuZCBkb2N1bWVudCBtb2R1bGVzLlxuaW1wb3J0ICogYXMgZG9jdW1lbnQgZnJvbSBcInByaXZhdGUtbmV4dC1wYWdlcy9fZG9jdW1lbnRcIjtcbmltcG9ydCAqIGFzIGFwcCBmcm9tIFwicHJpdmF0ZS1uZXh0LXBhZ2VzL19hcHBcIjtcbi8vIEltcG9ydCB0aGUgdXNlcmxhbmQgY29kZS5cbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCIuL3BhZ2VzL3Bvc2l0aW9ucy5qc1wiO1xuLy8gUmUtZXhwb3J0IHRoZSBjb21wb25lbnQgKHNob3VsZCBiZSB0aGUgZGVmYXVsdCBleHBvcnQpLlxuZXhwb3J0IGRlZmF1bHQgaG9pc3QodXNlcmxhbmQsICdkZWZhdWx0Jyk7XG4vLyBSZS1leHBvcnQgbWV0aG9kcy5cbmV4cG9ydCBjb25zdCBnZXRTdGF0aWNQcm9wcyA9IGhvaXN0KHVzZXJsYW5kLCAnZ2V0U3RhdGljUHJvcHMnKTtcbmV4cG9ydCBjb25zdCBnZXRTdGF0aWNQYXRocyA9IGhvaXN0KHVzZXJsYW5kLCAnZ2V0U3RhdGljUGF0aHMnKTtcbmV4cG9ydCBjb25zdCBnZXRTZXJ2ZXJTaWRlUHJvcHMgPSBob2lzdCh1c2VybGFuZCwgJ2dldFNlcnZlclNpZGVQcm9wcycpO1xuZXhwb3J0IGNvbnN0IGNvbmZpZyA9IGhvaXN0KHVzZXJsYW5kLCAnY29uZmlnJyk7XG5leHBvcnQgY29uc3QgcmVwb3J0V2ViVml0YWxzID0gaG9pc3QodXNlcmxhbmQsICdyZXBvcnRXZWJWaXRhbHMnKTtcbi8vIFJlLWV4cG9ydCBsZWdhY3kgbWV0aG9kcy5cbmV4cG9ydCBjb25zdCB1bnN0YWJsZV9nZXRTdGF0aWNQcm9wcyA9IGhvaXN0KHVzZXJsYW5kLCAndW5zdGFibGVfZ2V0U3RhdGljUHJvcHMnKTtcbmV4cG9ydCBjb25zdCB1bnN0YWJsZV9nZXRTdGF0aWNQYXRocyA9IGhvaXN0KHVzZXJsYW5kLCAndW5zdGFibGVfZ2V0U3RhdGljUGF0aHMnKTtcbmV4cG9ydCBjb25zdCB1bnN0YWJsZV9nZXRTdGF0aWNQYXJhbXMgPSBob2lzdCh1c2VybGFuZCwgJ3Vuc3RhYmxlX2dldFN0YXRpY1BhcmFtcycpO1xuZXhwb3J0IGNvbnN0IHVuc3RhYmxlX2dldFNlcnZlclByb3BzID0gaG9pc3QodXNlcmxhbmQsICd1bnN0YWJsZV9nZXRTZXJ2ZXJQcm9wcycpO1xuZXhwb3J0IGNvbnN0IHVuc3RhYmxlX2dldFNlcnZlclNpZGVQcm9wcyA9IGhvaXN0KHVzZXJsYW5kLCAndW5zdGFibGVfZ2V0U2VydmVyU2lkZVByb3BzJyk7XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBQYWdlc1JvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5QQUdFUyxcbiAgICAgICAgcGFnZTogXCIvcG9zaXRpb25zXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9wb3NpdGlvbnNcIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiAnJyxcbiAgICAgICAgZmlsZW5hbWU6ICcnXG4gICAgfSxcbiAgICBjb21wb25lbnRzOiB7XG4gICAgICAgIC8vIGRlZmF1bHQgZXhwb3J0IG1pZ2h0IG5vdCBleGlzdCB3aGVuIG9wdGltaXplZCBmb3IgZGF0YSBvbmx5XG4gICAgICAgIEFwcDogYXBwLmRlZmF1bHQsXG4gICAgICAgIERvY3VtZW50OiBkb2N1bWVudC5kZWZhdWx0XG4gICAgfSxcbiAgICB1c2VybGFuZFxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBhZ2VzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fpositions&preferredRegion=&absolutePagePath=.%2Fpages%2Fpositions.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"(pages-dir-node)/./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/_app.js\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3BhZ2VzL19hcHAuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQThCO0FBRWYsU0FBU0EsSUFBSSxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBRTtJQUNsRCxxQkFBTyw4REFBQ0Q7UUFBVyxHQUFHQyxTQUFTOzs7Ozs7QUFDakMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9icmFkeWdlb3JnZW4vRG9jdW1lbnRzL3dvcmtzcGFjZS9jYW5kaWQtY29ubmVjdGlvbnMvcGFnZXMvX2FwcC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgJy4uL3N0eWxlcy9nbG9iYWxzLmNzcydcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfSkge1xuICByZXR1cm4gPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxufVxuIl0sIm5hbWVzIjpbIkFwcCIsIkNvbXBvbmVudCIsInBhZ2VQcm9wcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_app.js\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/positions.js":
/*!****************************!*\
  !*** ./pages/positions.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Positions)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"(pages-dir-node)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"(pages-dir-node)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/Layout */ \"(pages-dir-node)/./components/Layout.js\");\n/* harmony import */ var _components_ui_DetailModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/ui/DetailModal */ \"(pages-dir-node)/./components/ui/DetailModal.js\");\n/* harmony import */ var _components_ui_LinkButton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/ui/LinkButton */ \"(pages-dir-node)/./components/ui/LinkButton.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../lib/utils */ \"(pages-dir-node)/./lib/utils.js\");\n\n\n\n\n\n\n\n\nfunction Positions() {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [positions, setPositions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [skills, setSkills] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedPosition, setSelectedPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showDetailModal, setShowDetailModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [filterLevel, setFilterLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [filterType, setFilterType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('posted');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Positions.useEffect\": ()=>{\n            const fetchData = {\n                \"Positions.useEffect.fetchData\": async ()=>{\n                    try {\n                        // Fetch positions, companies, and skills in parallel\n                        const [positionsRes, companiesRes, skillsRes] = await Promise.all([\n                            fetch('/api/positions'),\n                            fetch('/api/companies'),\n                            fetch('/api/skills')\n                        ]);\n                        if (positionsRes.ok && companiesRes.ok && skillsRes.ok) {\n                            const [positionsData, companiesData, skillsData] = await Promise.all([\n                                positionsRes.json(),\n                                companiesRes.json(),\n                                skillsRes.json()\n                            ]);\n                            setPositions(positionsData.positions || positionsData);\n                            setCompanies(companiesData.companies || companiesData);\n                            setSkills(skillsData.skills || skillsData);\n                        } else {\n                            // Fallback to sample data\n                            const samplePositions = [\n                                {\n                                    id: 'pos_1',\n                                    title: 'Lead Frontend Engineer',\n                                    company: 'TechCorp Inc.',\n                                    companyLogo: '🏢',\n                                    level: 'Senior',\n                                    type: 'Full-time',\n                                    location: 'San Francisco, CA',\n                                    remote: true,\n                                    salary: '$140,000 - $180,000',\n                                    description: 'Lead a team of frontend engineers building next-generation web applications using React and TypeScript.',\n                                    requirements: [\n                                        'React',\n                                        'TypeScript',\n                                        'Leadership',\n                                        'GraphQL',\n                                        'Node.js'\n                                    ],\n                                    benefits: [\n                                        'Health Insurance',\n                                        'Stock Options',\n                                        'Remote Work',\n                                        '401k'\n                                    ],\n                                    postedDate: new Date('2024-01-15'),\n                                    applicants: 23,\n                                    status: 'active'\n                                },\n                                {\n                                    id: 'pos_2',\n                                    title: 'Backend Engineer',\n                                    company: 'DataFlow Systems',\n                                    companyLogo: '📊',\n                                    level: 'Mid',\n                                    type: 'Full-time',\n                                    location: 'Austin, TX',\n                                    remote: false,\n                                    salary: '$100,000 - $130,000',\n                                    description: 'Build scalable backend systems for our data analytics platform using Python and Django.',\n                                    requirements: [\n                                        'Python',\n                                        'Django',\n                                        'PostgreSQL',\n                                        'AWS',\n                                        'Docker'\n                                    ],\n                                    benefits: [\n                                        'Health Insurance',\n                                        'Flexible Hours',\n                                        'Learning Budget'\n                                    ],\n                                    postedDate: new Date('2024-01-14'),\n                                    applicants: 18,\n                                    status: 'active'\n                                },\n                                {\n                                    id: 'pos_3',\n                                    title: 'Senior UX Designer',\n                                    company: 'Design Studio Pro',\n                                    companyLogo: '🎨',\n                                    level: 'Senior',\n                                    type: 'Contract',\n                                    location: 'New York, NY',\n                                    remote: true,\n                                    salary: '$80 - $120/hour',\n                                    description: 'Design intuitive user experiences for our client projects across various industries.',\n                                    requirements: [\n                                        'Figma',\n                                        'User Research',\n                                        'Design Systems',\n                                        'Prototyping'\n                                    ],\n                                    benefits: [\n                                        'Flexible Schedule',\n                                        'Creative Freedom',\n                                        'Portfolio Building'\n                                    ],\n                                    postedDate: new Date('2024-01-13'),\n                                    applicants: 31,\n                                    status: 'active'\n                                },\n                                {\n                                    id: 'pos_4',\n                                    title: 'Cloud Infrastructure Engineer',\n                                    company: 'CloudTech Solutions',\n                                    companyLogo: '☁️',\n                                    level: 'Senior',\n                                    type: 'Full-time',\n                                    location: 'Seattle, WA',\n                                    remote: true,\n                                    salary: '$130,000 - $160,000',\n                                    description: 'Design and maintain cloud infrastructure for enterprise clients using Kubernetes and Terraform.',\n                                    requirements: [\n                                        'Kubernetes',\n                                        'Terraform',\n                                        'AWS',\n                                        'Docker',\n                                        'Monitoring'\n                                    ],\n                                    benefits: [\n                                        'Health Insurance',\n                                        'Stock Options',\n                                        'Remote Work',\n                                        'Conference Budget'\n                                    ],\n                                    postedDate: new Date('2024-01-12'),\n                                    applicants: 15,\n                                    status: 'active'\n                                },\n                                {\n                                    id: 'pos_5',\n                                    title: 'Junior Frontend Developer',\n                                    company: 'TechCorp Inc.',\n                                    companyLogo: '🏢',\n                                    level: 'Junior',\n                                    type: 'Full-time',\n                                    location: 'San Francisco, CA',\n                                    remote: false,\n                                    salary: '$70,000 - $90,000',\n                                    description: 'Join our frontend team to build user interfaces and learn from experienced developers.',\n                                    requirements: [\n                                        'JavaScript',\n                                        'React',\n                                        'CSS',\n                                        'Git'\n                                    ],\n                                    benefits: [\n                                        'Health Insurance',\n                                        'Mentorship',\n                                        'Learning Budget'\n                                    ],\n                                    postedDate: new Date('2024-01-10'),\n                                    applicants: 42,\n                                    status: 'paused'\n                                }\n                            ];\n                            setPositions(samplePositions);\n                            setCompanies([]) // Sample companies would go here\n                            ;\n                            setSkills([]) // Sample skills would go here\n                            ;\n                        }\n                        setLoading(false);\n                    } catch (err) {\n                        setError(err.message);\n                        setLoading(false);\n                    }\n                }\n            }[\"Positions.useEffect.fetchData\"];\n            fetchData();\n        }\n    }[\"Positions.useEffect\"], []);\n    // Helper functions\n    const handleViewDetails = (position)=>{\n        setSelectedPosition(position);\n        setShowDetailModal(true);\n    };\n    const handleFindMatches = (position)=>{\n        router.push(`/matches?position=${position.id}`);\n    };\n    const getCompanyByName = (companyName)=>{\n        return companies.find((c)=>c.name === companyName) || {\n            name: companyName,\n            _key: companyName.toLowerCase().replace(/\\s+/g, '-'),\n            industry: 'Technology',\n            employeeCount: 100\n        };\n    };\n    const getSkillByName = (skillName)=>{\n        return skills.find((s)=>s.name === skillName) || {\n            name: skillName,\n            _key: skillName.toLowerCase().replace(/\\s+/g, '-'),\n            category: 'Technology',\n            demand: 'High'\n        };\n    };\n    const filteredPositions = positions.filter((position)=>{\n        const matchesSearch = position.title.toLowerCase().includes(searchTerm.toLowerCase()) || position.company.toLowerCase().includes(searchTerm.toLowerCase()) || position.location.toLowerCase().includes(searchTerm.toLowerCase()) || position.requirements.some((req)=>req.toLowerCase().includes(searchTerm.toLowerCase()));\n        const matchesLevel = filterLevel === 'all' || position.level === filterLevel;\n        const matchesType = filterType === 'all' || position.type === filterType;\n        return matchesSearch && matchesLevel && matchesType;\n    });\n    const sortedPositions = [\n        ...filteredPositions\n    ].sort((a, b)=>{\n        switch(sortBy){\n            case 'posted':\n                return new Date(b.postedDate) - new Date(a.postedDate);\n            case 'title':\n                return a.title.localeCompare(b.title);\n            case 'company':\n                return a.company.localeCompare(b.company);\n            case 'applicants':\n                return b.applicants - a.applicants;\n            default:\n                return 0;\n        }\n    });\n    const getLevelColor = (level)=>{\n        switch(level){\n            case 'Junior':\n                return 'bg-green-100 text-green-800';\n            case 'Mid':\n                return 'bg-blue-100 text-blue-800';\n            case 'Senior':\n                return 'bg-purple-100 text-purple-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const getTypeColor = (type)=>{\n        switch(type){\n            case 'Full-time':\n                return 'bg-emerald-100 text-emerald-800';\n            case 'Part-time':\n                return 'bg-yellow-100 text-yellow-800';\n            case 'Contract':\n                return 'bg-orange-100 text-orange-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'active':\n                return 'bg-green-100 text-green-800';\n            case 'paused':\n                return 'bg-yellow-100 text-yellow-800';\n            case 'closed':\n                return 'bg-red-100 text-red-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center items-center h-64\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-emerald-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                        lineNumber: 233,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                    lineNumber: 232,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                lineNumber: 231,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n            lineNumber: 230,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n                    children: [\n                        \"Error: \",\n                        error\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                    lineNumber: 244,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                lineNumber: 243,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n            lineNumber: 242,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                    children: \"Positions | Candid Connections Katra\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                    lineNumber: 255,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                lineNumber: 254,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold\",\n                                children: \"Open Positions\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                lineNumber: 260,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push('/visualizations'),\n                                className: \"bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors\",\n                                children: \"\\uD83D\\uDCCA Visualize\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                lineNumber: 261,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                        lineNumber: 259,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm p-4 mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-4 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-64\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search positions, companies, skills...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"w-full border border-gray-300 rounded-md px-3 py-2 text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                        lineNumber: 273,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"Level:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                            lineNumber: 283,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: filterLevel,\n                                            onChange: (e)=>setFilterLevel(e.target.value),\n                                            className: \"border border-gray-300 rounded-md px-3 py-1 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"all\",\n                                                    children: \"All Levels\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Junior\",\n                                                    children: \"Junior\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Mid\",\n                                                    children: \"Mid\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Senior\",\n                                                    children: \"Senior\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                            lineNumber: 284,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                    lineNumber: 282,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"Type:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                            lineNumber: 297,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: filterType,\n                                            onChange: (e)=>setFilterType(e.target.value),\n                                            className: \"border border-gray-300 rounded-md px-3 py-1 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"all\",\n                                                    children: \"All Types\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Full-time\",\n                                                    children: \"Full-time\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Part-time\",\n                                                    children: \"Part-time\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Contract\",\n                                                    children: \"Contract\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                            lineNumber: 298,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"Sort by:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                            lineNumber: 311,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: sortBy,\n                                            onChange: (e)=>setSortBy(e.target.value),\n                                            className: \"border border-gray-300 rounded-md px-3 py-1 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"posted\",\n                                                    children: \"Date Posted\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"title\",\n                                                    children: \"Position Title\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"company\",\n                                                    children: \"Company\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"applicants\",\n                                                    children: \"Applicants\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                            lineNumber: 312,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                    lineNumber: 310,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        sortedPositions.length,\n                                        \" positions found\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                    lineNumber: 324,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                            lineNumber: 271,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                        lineNumber: 270,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: sortedPositions.map((position)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-start mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl mr-3\",\n                                                        children: position.companyLogo\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-lg text-gray-900\",\n                                                                children: position.title\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                                lineNumber: 339,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LinkButton__WEBPACK_IMPORTED_MODULE_6__.CompanyLink, {\n                                                                company: getCompanyByName(position.company),\n                                                                size: \"sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                lineNumber: 336,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getLevelColor(position.level)}`,\n                                                        children: position.level\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(position.type)}`,\n                                                        children: position.type\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(position.status)}`,\n                                                        children: position.status\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                lineNumber: 343,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                        lineNumber: 335,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-sm text-gray-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-2\",\n                                                        children: \"\\uD83D\\uDCCD\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    position.location,\n                                                    \" \",\n                                                    position.remote && '(Remote OK)'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                lineNumber: 358,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-sm text-gray-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-2\",\n                                                        children: \"\\uD83D\\uDCB0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    position.salary\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                lineNumber: 362,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-sm text-gray-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-2\",\n                                                        children: \"\\uD83D\\uDC65\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    position.applicants,\n                                                    \" applicants\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                lineNumber: 366,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                        lineNumber: 357,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-700 text-sm mb-4\",\n                                        children: position.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                        lineNumber: 373,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                className: \"text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Required Skills:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                lineNumber: 379,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: position.requirements.map((req, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LinkButton__WEBPACK_IMPORTED_MODULE_6__.SkillLink, {\n                                                        skill: getSkillByName(req),\n                                                        size: \"xs\"\n                                                    }, index, false, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                lineNumber: 380,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                        lineNumber: 378,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                className: \"text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Benefits:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                lineNumber: 389,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-1\",\n                                                children: position.benefits.map((benefit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"bg-emerald-100 text-emerald-800 text-xs px-2 py-1 rounded\",\n                                                        children: benefit\n                                                    }, index, false, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                lineNumber: 390,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                        lineNumber: 388,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center pt-4 border-t border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"Posted \",\n                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatDate)(position.postedDate)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                lineNumber: 401,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleViewDetails(position),\n                                                        className: \"bg-primary-600 text-white px-4 py-2 rounded text-sm hover:bg-primary-700 transition-colors\",\n                                                        children: \"View Details\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleFindMatches(position),\n                                                        className: \"border border-primary-600 text-primary-600 px-4 py-2 rounded text-sm hover:bg-primary-50 transition-colors\",\n                                                        children: \"Find Matches\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                                lineNumber: 404,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, position.id, true, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                lineNumber: 333,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                        lineNumber: 331,\n                        columnNumber: 9\n                    }, this),\n                    sortedPositions.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400 text-6xl mb-4\",\n                                children: \"\\uD83D\\uDCCB\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                lineNumber: 425,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                children: \"No positions found\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                lineNumber: 426,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Try adjusting your search or filters to see more results.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                                lineNumber: 427,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                        lineNumber: 424,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                lineNumber: 258,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_DetailModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: showDetailModal,\n                onClose: ()=>setShowDetailModal(false),\n                entity: selectedPosition,\n                entityType: \"position\",\n                onFindMatches: handleFindMatches\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n                lineNumber: 433,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/positions.js\",\n        lineNumber: 253,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/positions.js\n");

/***/ }),

/***/ "(pages-dir-node)/./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fpositions&preferredRegion=&absolutePagePath=.%2Fpages%2Fpositions.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();