/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/skills";
exports.ids = ["pages/skills"];
exports.modules = {

/***/ "(pages-dir-node)/./components/GraphVisualization2D.js":
/*!********************************************!*\
  !*** ./components/GraphVisualization2D.js ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GraphVisualization2D)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var d3__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3 */ \"(pages-dir-node)/./node_modules/d3/src/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([d3__WEBPACK_IMPORTED_MODULE_2__]);\nd3__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nfunction GraphVisualization2D({ data }) {\n    const svgRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GraphVisualization2D.useEffect\": ()=>{\n            if (!data || !svgRef.current || \"undefined\" === 'undefined') return;\n            // Clear previous visualization\n            d3__WEBPACK_IMPORTED_MODULE_2__.select(svgRef.current).selectAll('*').remove();\n            const width = 800;\n            const height = 600;\n            // Create SVG\n            const svg = d3__WEBPACK_IMPORTED_MODULE_2__.select(svgRef.current).attr('width', width).attr('height', height).attr('viewBox', [\n                0,\n                0,\n                width,\n                height\n            ]);\n            // Create force simulation\n            const simulation = d3__WEBPACK_IMPORTED_MODULE_2__.forceSimulation(data.nodes).force('link', d3__WEBPACK_IMPORTED_MODULE_2__.forceLink(data.links).id({\n                \"GraphVisualization2D.useEffect.simulation\": (d)=>d.id\n            }[\"GraphVisualization2D.useEffect.simulation\"])).force('charge', d3__WEBPACK_IMPORTED_MODULE_2__.forceManyBody().strength(-400)).force('center', d3__WEBPACK_IMPORTED_MODULE_2__.forceCenter(width / 2, height / 2));\n            // Create links\n            const link = svg.append('g').selectAll('line').data(data.links).join('line').attr('stroke', '#999').attr('stroke-opacity', 0.6).attr('stroke-width', {\n                \"GraphVisualization2D.useEffect.link\": (d)=>Math.sqrt(d.value || 1)\n            }[\"GraphVisualization2D.useEffect.link\"]);\n            // Create nodes\n            const node = svg.append('g').selectAll('circle').data(data.nodes).join('circle').attr('r', {\n                \"GraphVisualization2D.useEffect.node\": (d)=>d.size || 5\n            }[\"GraphVisualization2D.useEffect.node\"]).attr('fill', {\n                \"GraphVisualization2D.useEffect.node\": (d)=>{\n                    switch(d.type){\n                        case 'jobSeeker':\n                            return '#3b82f6' // blue\n                            ;\n                        case 'company':\n                            return '#14b8a6' // teal\n                            ;\n                        case 'position':\n                            return '#10b981' // emerald\n                            ;\n                        case 'skill':\n                            return '#f59e0b' // amber\n                            ;\n                        default:\n                            return '#6366f1' // indigo\n                            ;\n                    }\n                }\n            }[\"GraphVisualization2D.useEffect.node\"]).call(drag(simulation));\n            // Add titles for nodes\n            node.append('title').text({\n                \"GraphVisualization2D.useEffect\": (d)=>d.name\n            }[\"GraphVisualization2D.useEffect\"]);\n            // Update positions on simulation tick\n            simulation.on('tick', {\n                \"GraphVisualization2D.useEffect\": ()=>{\n                    link.attr('x1', {\n                        \"GraphVisualization2D.useEffect\": (d)=>d.source.x\n                    }[\"GraphVisualization2D.useEffect\"]).attr('y1', {\n                        \"GraphVisualization2D.useEffect\": (d)=>d.source.y\n                    }[\"GraphVisualization2D.useEffect\"]).attr('x2', {\n                        \"GraphVisualization2D.useEffect\": (d)=>d.target.x\n                    }[\"GraphVisualization2D.useEffect\"]).attr('y2', {\n                        \"GraphVisualization2D.useEffect\": (d)=>d.target.y\n                    }[\"GraphVisualization2D.useEffect\"]);\n                    node.attr('cx', {\n                        \"GraphVisualization2D.useEffect\": (d)=>d.x\n                    }[\"GraphVisualization2D.useEffect\"]).attr('cy', {\n                        \"GraphVisualization2D.useEffect\": (d)=>d.y\n                    }[\"GraphVisualization2D.useEffect\"]);\n                }\n            }[\"GraphVisualization2D.useEffect\"]);\n            // Drag functionality\n            function drag(simulation) {\n                function dragstarted(event) {\n                    if (!event.active) simulation.alphaTarget(0.3).restart();\n                    event.subject.fx = event.subject.x;\n                    event.subject.fy = event.subject.y;\n                }\n                function dragged(event) {\n                    event.subject.fx = event.x;\n                    event.subject.fy = event.y;\n                }\n                function dragended(event) {\n                    if (!event.active) simulation.alphaTarget(0);\n                    event.subject.fx = null;\n                    event.subject.fy = null;\n                }\n                return d3__WEBPACK_IMPORTED_MODULE_2__.drag().on('start', dragstarted).on('drag', dragged).on('end', dragended);\n            }\n            return ({\n                \"GraphVisualization2D.useEffect\": ()=>{\n                    simulation.stop();\n                }\n            })[\"GraphVisualization2D.useEffect\"];\n        }\n    }[\"GraphVisualization2D.useEffect\"], [\n        data\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border rounded-lg shadow-sm bg-white p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            ref: svgRef,\n            className: \"w-full h-full\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/GraphVisualization2D.js\",\n            lineNumber: 103,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/GraphVisualization2D.js\",\n        lineNumber: 102,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/GraphVisualization2D.js\n");

/***/ }),

/***/ "(pages-dir-node)/./components/GraphVisualization3D.js":
/*!********************************************!*\
  !*** ./components/GraphVisualization3D.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GraphVisualization3D)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction GraphVisualization3D({ data }) {\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GraphVisualization3D.useEffect\": ()=>{\n            if (!data || !containerRef.current || \"undefined\" === 'undefined') return;\n            // Clear previous visualization\n            containerRef.current.innerHTML = '';\n            // Dynamically import and initialize 3D force graph\n            Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/3d-force-graph\"), __webpack_require__.e(\"_pages-dir-node___barrel_optimize___names_AmbientLight_DirectionalLight_REVISION_node_modules-e93221\")]).then(__webpack_require__.bind(__webpack_require__, /*! 3d-force-graph */ \"(pages-dir-node)/./node_modules/3d-force-graph/dist/3d-force-graph.mjs\")).then({\n                \"GraphVisualization3D.useEffect\": (ForceGraph3DModule)=>{\n                    const ForceGraph3DComponent = ForceGraph3DModule.default;\n                    const graph = ForceGraph3DComponent().width(containerRef.current.clientWidth).height(500).backgroundColor('#ffffff').nodeColor({\n                        \"GraphVisualization3D.useEffect.graph\": (node)=>{\n                            switch(node.type){\n                                case 'jobSeeker':\n                                    return '#3b82f6' // blue\n                                    ;\n                                case 'company':\n                                    return '#14b8a6' // teal\n                                    ;\n                                case 'position':\n                                    return '#10b981' // emerald\n                                    ;\n                                case 'skill':\n                                    return '#f59e0b' // amber\n                                    ;\n                                default:\n                                    return '#6366f1' // indigo\n                                    ;\n                            }\n                        }\n                    }[\"GraphVisualization3D.useEffect.graph\"]).nodeLabel({\n                        \"GraphVisualization3D.useEffect.graph\": (node)=>node.name\n                    }[\"GraphVisualization3D.useEffect.graph\"]).nodeVal({\n                        \"GraphVisualization3D.useEffect.graph\": (node)=>node.size || 5\n                    }[\"GraphVisualization3D.useEffect.graph\"]).linkWidth({\n                        \"GraphVisualization3D.useEffect.graph\": (link)=>link.value || 1\n                    }[\"GraphVisualization3D.useEffect.graph\"]).linkDirectionalParticles(2).linkDirectionalParticleSpeed(0.005).graphData(data)(containerRef.current);\n                    // Handle window resize\n                    const handleResize = {\n                        \"GraphVisualization3D.useEffect.handleResize\": ()=>{\n                            if (containerRef.current) {\n                                graph.width(containerRef.current.clientWidth);\n                            }\n                        }\n                    }[\"GraphVisualization3D.useEffect.handleResize\"];\n                    window.addEventListener('resize', handleResize);\n                }\n            }[\"GraphVisualization3D.useEffect\"]).catch({\n                \"GraphVisualization3D.useEffect\": (error)=>{\n                    console.error('Failed to load 3D visualization:', error);\n                    // Fallback to a simple message\n                    if (containerRef.current) {\n                        containerRef.current.innerHTML = '<div class=\"flex items-center justify-center h-full text-gray-500\">3D visualization unavailable</div>';\n                    }\n                }\n            }[\"GraphVisualization3D.useEffect\"]);\n        }\n    }[\"GraphVisualization3D.useEffect\"], [\n        data\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"w-full h-[500px] border rounded-lg shadow-sm bg-white\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/GraphVisualization3D.js\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/GraphVisualization3D.js\n");

/***/ }),

/***/ "(pages-dir-node)/./components/Layout.js":
/*!******************************!*\
  !*** ./components/Layout.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Layout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Navigation */ \"(pages-dir-node)/./components/Navigation.js\");\n\n\nfunction Layout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Navigation__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Layout.js\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container-app section-padding\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Layout.js\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Layout.js\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL2NvbXBvbmVudHMvTGF5b3V0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXFDO0FBRXRCLFNBQVNDLE9BQU8sRUFBRUMsUUFBUSxFQUFFO0lBQ3pDLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0osbURBQVVBOzs7OzswQkFDWCw4REFBQ0s7Z0JBQUtELFdBQVU7MEJBQWlDRjs7Ozs7Ozs7Ozs7O0FBR3ZEIiwic291cmNlcyI6WyIvVXNlcnMvYnJhZHlnZW9yZ2VuL0RvY3VtZW50cy93b3Jrc3BhY2UvY2FuZGlkLWNvbm5lY3Rpb25zL2NvbXBvbmVudHMvTGF5b3V0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBOYXZpZ2F0aW9uIGZyb20gJy4vTmF2aWdhdGlvbidcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTGF5b3V0KHsgY2hpbGRyZW4gfSkge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLXdoaXRlXCI+XG4gICAgICA8TmF2aWdhdGlvbiAvPlxuICAgICAgPG1haW4gY2xhc3NOYW1lPVwiY29udGFpbmVyLWFwcCBzZWN0aW9uLXBhZGRpbmdcIj57Y2hpbGRyZW59PC9tYWluPlxuICAgIDwvZGl2PlxuICApXG59Il0sIm5hbWVzIjpbIk5hdmlnYXRpb24iLCJMYXlvdXQiLCJjaGlsZHJlbiIsImRpdiIsImNsYXNzTmFtZSIsIm1haW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/Layout.js\n");

/***/ }),

/***/ "(pages-dir-node)/./components/Navigation.js":
/*!**********************************!*\
  !*** ./components/Navigation.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(pages-dir-node)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-node)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nfunction Navigation() {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const navItems = [\n        {\n            name: 'Dashboard',\n            path: '/',\n            icon: '🏠'\n        },\n        {\n            name: 'Authority Matches',\n            path: '/matches',\n            icon: '🎯'\n        },\n        {\n            name: 'Job Seekers',\n            path: '/job-seekers',\n            icon: '👥'\n        },\n        {\n            name: 'Hiring Authorities',\n            path: '/hiring-authorities',\n            icon: '👔'\n        },\n        {\n            name: 'Companies',\n            path: '/companies',\n            icon: '🏢'\n        },\n        {\n            name: 'Positions',\n            path: '/positions',\n            icon: '📋'\n        },\n        {\n            name: 'Skills',\n            path: '/skills',\n            icon: '🛠️'\n        },\n        {\n            name: 'Network View',\n            path: '/global-view',\n            icon: '🌐'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-white shadow-soft border-b border-candid-gray-200\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-app\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0 flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/\",\n                                className: \"flex items-center space-x-3 group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 relative group-hover:scale-105 transition-transform duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            viewBox: \"0 0 48 48\",\n                                            className: \"w-full h-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"24\",\n                                                    cy: \"24\",\n                                                    r: \"22\",\n                                                    fill: \"none\",\n                                                    stroke: \"#1e3a8a\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 31,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"24\",\n                                                    cy: \"12\",\n                                                    r: \"3\",\n                                                    fill: \"#00d4ff\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 33,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"36\",\n                                                    cy: \"24\",\n                                                    r: \"3\",\n                                                    fill: \"#00d4ff\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 34,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"24\",\n                                                    cy: \"36\",\n                                                    r: \"3\",\n                                                    fill: \"#00d4ff\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 35,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"12\",\n                                                    cy: \"24\",\n                                                    r: \"3\",\n                                                    fill: \"#00d4ff\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 36,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"24\",\n                                                    cy: \"24\",\n                                                    r: \"4\",\n                                                    fill: \"#1e3a8a\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 37,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                    x1: \"24\",\n                                                    y1: \"15\",\n                                                    x2: \"24\",\n                                                    y2: \"20\",\n                                                    stroke: \"#00d4ff\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 39,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                    x1: \"33\",\n                                                    y1: \"24\",\n                                                    x2: \"28\",\n                                                    y2: \"24\",\n                                                    stroke: \"#00d4ff\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 40,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                    x1: \"24\",\n                                                    y1: \"33\",\n                                                    x2: \"24\",\n                                                    y2: \"28\",\n                                                    stroke: \"#00d4ff\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 41,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                    x1: \"15\",\n                                                    y1: \"24\",\n                                                    x2: \"20\",\n                                                    y2: \"24\",\n                                                    stroke: \"#00d4ff\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 42,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                            lineNumber: 29,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 28,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden sm:block\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold text-secondary-800 group-hover:text-primary-500 transition-colors duration-200\",\n                                                children: \"Candid Connections\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                lineNumber: 46,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-candid-gray-600 -mt-1\",\n                                                children: \"Katra Platform\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                lineNumber: 49,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 45,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                lineNumber: 26,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1\",\n                                children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: item.path,\n                                        className: `flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${router.pathname === item.path ? 'nav-link-active' : 'nav-link'}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-base\",\n                                                children: item.icon\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                lineNumber: 67,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                lineNumber: 68,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, item.path, true, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 58,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                lineNumber: 56,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/admin\",\n                                    className: \"btn-outline text-sm py-2 px-4\",\n                                    children: \"⚙️ Admin\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/global-view\",\n                                    className: \"btn-outline text-sm py-2 px-4\",\n                                    children: \"\\uD83C\\uDF10 Network View\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"https://portal.candid-connections.com/user/login\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"btn-primary text-sm py-2 px-4\",\n                                    children: \"Portal Login\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),\n                                className: \"p-2 rounded-lg text-candid-navy-600 hover:text-primary-600 hover:bg-primary-50 transition-colors duration-200\",\n                                \"aria-label\": \"Toggle mobile menu\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 107,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M4 6h16M4 12h16M4 18h16\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 109,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                    lineNumber: 105,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this),\n                isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"lg:hidden border-t border-candid-gray-200 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: item.path,\n                                    onClick: ()=>setIsMobileMenuOpen(false),\n                                    className: `flex items-center space-x-3 px-4 py-3 rounded-lg text-base font-medium transition-all duration-200 ${router.pathname === item.path ? 'nav-link-active' : 'nav-link'}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg\",\n                                            children: item.icon\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                            lineNumber: 131,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                            lineNumber: 132,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, item.path, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                    lineNumber: 121,\n                                    columnNumber: 17\n                                }, this)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-4 space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/global-view\",\n                                        onClick: ()=>setIsMobileMenuOpen(false),\n                                        className: \"block w-full btn-outline text-center\",\n                                        children: \"\\uD83C\\uDF10 Network View\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 138,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"https://portal.candid-connections.com/user/login\",\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        className: \"block w-full btn-primary text-center\",\n                                        children: \"Portal Login\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 145,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                lineNumber: 137,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                        lineNumber: 119,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                    lineNumber: 118,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/Navigation.js\n");

/***/ }),

/***/ "(pages-dir-node)/./components/VisualizationModal.js":
/*!******************************************!*\
  !*** ./components/VisualizationModal.js ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ VisualizationModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _GraphVisualization2D__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./GraphVisualization2D */ \"(pages-dir-node)/./components/GraphVisualization2D.js\");\n/* harmony import */ var _GraphVisualization3D__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./GraphVisualization3D */ \"(pages-dir-node)/./components/GraphVisualization3D.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_GraphVisualization2D__WEBPACK_IMPORTED_MODULE_2__]);\n_GraphVisualization2D__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nfunction VisualizationModal({ isOpen, onClose, data, title }) {\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('2D');\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 overflow-y-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 transition-opacity\",\n                    \"aria-hidden\": \"true\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gray-500 opacity-75\",\n                        onClick: onClose\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/VisualizationModal.js\",\n                        lineNumber: 14,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/VisualizationModal.js\",\n                    lineNumber: 13,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-5xl sm:w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"sm:flex sm:items-start\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg leading-6 font-medium text-gray-900\",\n                                                    children: title || 'Network Visualization'\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/VisualizationModal.js\",\n                                                    lineNumber: 22,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>setViewMode('2D'),\n                                                            className: `px-3 py-1 text-sm font-medium rounded-md ${viewMode === '2D' ? 'bg-indigo-100 text-indigo-700' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,\n                                                            children: \"2D View\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/VisualizationModal.js\",\n                                                            lineNumber: 24,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>setViewMode('3D'),\n                                                            className: `px-3 py-1 text-sm font-medium rounded-md ${viewMode === '3D' ? 'bg-indigo-100 text-indigo-700' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,\n                                                            children: \"3D View\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/VisualizationModal.js\",\n                                                            lineNumber: 35,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/VisualizationModal.js\",\n                                                    lineNumber: 23,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/VisualizationModal.js\",\n                                            lineNumber: 21,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2\",\n                                            children: viewMode === '2D' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GraphVisualization2D__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                data: data\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/VisualizationModal.js\",\n                                                lineNumber: 51,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GraphVisualization3D__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                data: data\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/VisualizationModal.js\",\n                                                lineNumber: 53,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/VisualizationModal.js\",\n                                            lineNumber: 49,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/VisualizationModal.js\",\n                                    lineNumber: 20,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/VisualizationModal.js\",\n                                lineNumber: 19,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/VisualizationModal.js\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                className: \"btn-secondary\",\n                                onClick: onClose,\n                                children: \"Close\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/VisualizationModal.js\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/VisualizationModal.js\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/VisualizationModal.js\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/VisualizationModal.js\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/VisualizationModal.js\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/VisualizationModal.js\n");

/***/ }),

/***/ "(pages-dir-node)/./lib/graphData.js":
/*!**************************!*\
  !*** ./lib/graphData.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateSampleGraphData: () => (/* binding */ generateSampleGraphData),\n/* harmony export */   processGraphData: () => (/* binding */ processGraphData)\n/* harmony export */ });\n// Process raw data from ArangoDB into format for D3.js and 3D-Force-Graph\nconst processGraphData = (data)=>{\n    if (!data) return {\n        nodes: [],\n        links: []\n    };\n    const { entities, relationships } = data;\n    // Process nodes\n    const nodes = entities.map((entity)=>({\n            id: entity._id,\n            name: entity.name || entity.title || 'Unnamed',\n            type: getEntityType(entity._id),\n            size: getNodeSize(entity),\n            ...entity // Include all original properties\n        }));\n    // Process links\n    const links = relationships.map((rel)=>({\n            source: rel._from,\n            target: rel._to,\n            value: rel.strength || 1,\n            type: rel._type || getEdgeType(rel._id),\n            ...rel // Include all original properties\n        }));\n    return {\n        nodes,\n        links\n    };\n};\n// Determine entity type from _id\nconst getEntityType = (id)=>{\n    if (!id) return 'unknown';\n    if (id.includes('/jobSeekers/')) return 'jobSeeker';\n    if (id.includes('/companies/')) return 'company';\n    if (id.includes('/hiringAuthorities/')) return 'hiringAuthority';\n    if (id.includes('/positions/')) return 'position';\n    if (id.includes('/skills/')) return 'skill';\n    return 'unknown';\n};\n// Determine edge type from _id\nconst getEdgeType = (id)=>{\n    if (!id) return 'unknown';\n    if (id.includes('/works_for/')) return 'works_for';\n    if (id.includes('/employs/')) return 'employs';\n    if (id.includes('/posts/')) return 'posts';\n    if (id.includes('/requires/')) return 'requires';\n    if (id.includes('/has_skill/')) return 'has_skill';\n    if (id.includes('/matched_to/')) return 'matched_to';\n    return 'unknown';\n};\n// Determine node size based on entity type and properties\nconst getNodeSize = (entity)=>{\n    const type = getEntityType(entity._id);\n    switch(type){\n        case 'jobSeeker':\n            return 8;\n        case 'company':\n            return 10;\n        case 'hiringAuthority':\n            return 7;\n        case 'position':\n            return 9;\n        case 'skill':\n            // Skills with higher demand are larger\n            return entity.demandScore ? 5 + entity.demandScore / 20 : 6;\n        default:\n            return 5;\n    }\n};\n// Generate sample graph data for testing\nconst generateSampleGraphData = ()=>{\n    // Create sample nodes\n    const jobSeekers = Array.from({\n        length: 5\n    }, (_, i)=>({\n            id: `jobSeekers/${i}`,\n            name: `Job Seeker ${i + 1}`,\n            type: 'jobSeeker',\n            size: 8\n        }));\n    const companies = Array.from({\n        length: 3\n    }, (_, i)=>({\n            id: `companies/${i}`,\n            name: `Company ${i + 1}`,\n            type: 'company',\n            size: 10\n        }));\n    const positions = Array.from({\n        length: 4\n    }, (_, i)=>({\n            id: `positions/${i}`,\n            name: `Position ${i + 1}`,\n            type: 'position',\n            size: 9\n        }));\n    const skills = Array.from({\n        length: 8\n    }, (_, i)=>({\n            id: `skills/${i}`,\n            name: `Skill ${i + 1}`,\n            type: 'skill',\n            size: 6\n        }));\n    const nodes = [\n        ...jobSeekers,\n        ...companies,\n        ...positions,\n        ...skills\n    ];\n    // Create sample links\n    const links = [];\n    // Job seekers to companies\n    jobSeekers.forEach((js, i)=>{\n        links.push({\n            source: js.id,\n            target: companies[i % companies.length].id,\n            type: 'works_for',\n            value: 1\n        });\n    });\n    // Companies to positions\n    companies.forEach((company, i)=>{\n        positions.forEach((position, j)=>{\n            if ((i + j) % 2 === 0) {\n                links.push({\n                    source: company.id,\n                    target: position.id,\n                    type: 'posts',\n                    value: 1\n                });\n            }\n        });\n    });\n    // Job seekers to skills\n    jobSeekers.forEach((js)=>{\n        // Each job seeker has 2-4 random skills\n        const numSkills = 2 + Math.floor(Math.random() * 3);\n        const shuffled = [\n            ...skills\n        ].sort(()=>0.5 - Math.random());\n        const selectedSkills = shuffled.slice(0, numSkills);\n        selectedSkills.forEach((skill)=>{\n            links.push({\n                source: js.id,\n                target: skill.id,\n                type: 'has_skill',\n                value: 1\n            });\n        });\n    });\n    // Positions to skills\n    positions.forEach((position)=>{\n        // Each position requires 2-3 random skills\n        const numSkills = 2 + Math.floor(Math.random() * 2);\n        const shuffled = [\n            ...skills\n        ].sort(()=>0.5 - Math.random());\n        const selectedSkills = shuffled.slice(0, numSkills);\n        selectedSkills.forEach((skill)=>{\n            links.push({\n                source: position.id,\n                target: skill.id,\n                type: 'requires',\n                value: 1\n            });\n        });\n    });\n    return {\n        nodes,\n        links\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./lib/graphData.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fskills&preferredRegion=&absolutePagePath=.%2Fpages%2Fskills.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fskills&preferredRegion=&absolutePagePath=.%2Fpages%2Fskills.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages/module.compiled */ \"(pages-dir-node)/./node_modules/next/dist/server/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(pages-dir-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(pages-dir-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"(pages-dir-node)/./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"(pages-dir-node)/./pages/_app.js\");\n/* harmony import */ var _pages_skills_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages/skills.js */ \"(pages-dir-node)/./pages/skills.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_pages_skills_js__WEBPACK_IMPORTED_MODULE_5__]);\n_pages_skills_js__WEBPACK_IMPORTED_MODULE_5__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_skills_js__WEBPACK_IMPORTED_MODULE_5__, 'default'));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_skills_js__WEBPACK_IMPORTED_MODULE_5__, 'getStaticProps');\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_skills_js__WEBPACK_IMPORTED_MODULE_5__, 'getStaticPaths');\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_skills_js__WEBPACK_IMPORTED_MODULE_5__, 'getServerSideProps');\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_skills_js__WEBPACK_IMPORTED_MODULE_5__, 'config');\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_skills_js__WEBPACK_IMPORTED_MODULE_5__, 'reportWebVitals');\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_skills_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticProps');\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_skills_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticPaths');\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_skills_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticParams');\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_skills_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerProps');\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_skills_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/skills\",\n        pathname: \"/skills\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    components: {\n        // default export might not exist when optimized for data only\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_skills_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fskills&preferredRegion=&absolutePagePath=.%2Fpages%2Fskills.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"(pages-dir-node)/./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/_app.js\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3BhZ2VzL19hcHAuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQThCO0FBRWYsU0FBU0EsSUFBSSxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBRTtJQUNsRCxxQkFBTyw4REFBQ0Q7UUFBVyxHQUFHQyxTQUFTOzs7Ozs7QUFDakMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9icmFkeWdlb3JnZW4vRG9jdW1lbnRzL3dvcmtzcGFjZS9jYW5kaWQtY29ubmVjdGlvbnMvcGFnZXMvX2FwcC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgJy4uL3N0eWxlcy9nbG9iYWxzLmNzcydcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfSkge1xuICByZXR1cm4gPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxufVxuIl0sIm5hbWVzIjpbIkFwcCIsIkNvbXBvbmVudCIsInBhZ2VQcm9wcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_app.js\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/skills.js":
/*!*************************!*\
  !*** ./pages/skills.js ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Skills)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"(pages-dir-node)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/Layout */ \"(pages-dir-node)/./components/Layout.js\");\n/* harmony import */ var _components_VisualizationModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/VisualizationModal */ \"(pages-dir-node)/./components/VisualizationModal.js\");\n/* harmony import */ var _lib_graphData__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/graphData */ \"(pages-dir-node)/./lib/graphData.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_VisualizationModal__WEBPACK_IMPORTED_MODULE_4__]);\n_components_VisualizationModal__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nfunction Skills() {\n    const [skills, setSkills] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showVisualization, setShowVisualization] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [graphData, setGraphData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [filterCategory, setFilterCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('demand');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Skills.useEffect\": ()=>{\n            const fetchSkills = {\n                \"Skills.useEffect.fetchSkills\": async ()=>{\n                    try {\n                        // Simulate API call - in real app this would fetch from /api/skills\n                        setTimeout({\n                            \"Skills.useEffect.fetchSkills\": ()=>{\n                                const sampleSkills = [\n                                    {\n                                        id: 'skill_1',\n                                        name: 'React',\n                                        category: 'Frontend',\n                                        demand: 95,\n                                        supply: 78,\n                                        averageSalary: '$105,000',\n                                        jobSeekers: 156,\n                                        openPositions: 89,\n                                        growth: '+12%',\n                                        description: 'JavaScript library for building user interfaces',\n                                        relatedSkills: [\n                                            'JavaScript',\n                                            'TypeScript',\n                                            'Redux',\n                                            'Next.js'\n                                        ],\n                                        icon: '⚛️'\n                                    },\n                                    {\n                                        id: 'skill_2',\n                                        name: 'Python',\n                                        category: 'Backend',\n                                        demand: 92,\n                                        supply: 85,\n                                        averageSalary: '$98,000',\n                                        jobSeekers: 203,\n                                        openPositions: 76,\n                                        growth: '+8%',\n                                        description: 'High-level programming language for web development, data science, and automation',\n                                        relatedSkills: [\n                                            'Django',\n                                            'Flask',\n                                            'FastAPI',\n                                            'NumPy'\n                                        ],\n                                        icon: '🐍'\n                                    },\n                                    {\n                                        id: 'skill_3',\n                                        name: 'Kubernetes',\n                                        category: 'DevOps',\n                                        demand: 88,\n                                        supply: 45,\n                                        averageSalary: '$125,000',\n                                        jobSeekers: 67,\n                                        openPositions: 52,\n                                        growth: '+25%',\n                                        description: 'Container orchestration platform for automating deployment and scaling',\n                                        relatedSkills: [\n                                            'Docker',\n                                            'Terraform',\n                                            'AWS',\n                                            'Jenkins'\n                                        ],\n                                        icon: '☸️'\n                                    },\n                                    {\n                                        id: 'skill_4',\n                                        name: 'Figma',\n                                        category: 'Design',\n                                        demand: 85,\n                                        supply: 72,\n                                        averageSalary: '$85,000',\n                                        jobSeekers: 134,\n                                        openPositions: 43,\n                                        growth: '+15%',\n                                        description: 'Collaborative design tool for creating user interfaces and prototypes',\n                                        relatedSkills: [\n                                            'Sketch',\n                                            'Adobe XD',\n                                            'Prototyping',\n                                            'User Research'\n                                        ],\n                                        icon: '🎨'\n                                    },\n                                    {\n                                        id: 'skill_5',\n                                        name: 'TypeScript',\n                                        category: 'Frontend',\n                                        demand: 82,\n                                        supply: 65,\n                                        averageSalary: '$108,000',\n                                        jobSeekers: 98,\n                                        openPositions: 67,\n                                        growth: '+18%',\n                                        description: 'Typed superset of JavaScript that compiles to plain JavaScript',\n                                        relatedSkills: [\n                                            'JavaScript',\n                                            'React',\n                                            'Angular',\n                                            'Node.js'\n                                        ],\n                                        icon: '📘'\n                                    },\n                                    {\n                                        id: 'skill_6',\n                                        name: 'AWS',\n                                        category: 'Cloud',\n                                        demand: 90,\n                                        supply: 58,\n                                        averageSalary: '$115,000',\n                                        jobSeekers: 89,\n                                        openPositions: 78,\n                                        growth: '+20%',\n                                        description: 'Amazon Web Services cloud computing platform',\n                                        relatedSkills: [\n                                            'EC2',\n                                            'S3',\n                                            'Lambda',\n                                            'CloudFormation'\n                                        ],\n                                        icon: '☁️'\n                                    },\n                                    {\n                                        id: 'skill_7',\n                                        name: 'Machine Learning',\n                                        category: 'Data Science',\n                                        demand: 87,\n                                        supply: 42,\n                                        averageSalary: '$130,000',\n                                        jobSeekers: 45,\n                                        openPositions: 38,\n                                        growth: '+30%',\n                                        description: 'AI technique that enables computers to learn and improve from experience',\n                                        relatedSkills: [\n                                            'Python',\n                                            'TensorFlow',\n                                            'PyTorch',\n                                            'Scikit-learn'\n                                        ],\n                                        icon: '🤖'\n                                    },\n                                    {\n                                        id: 'skill_8',\n                                        name: 'Node.js',\n                                        category: 'Backend',\n                                        demand: 78,\n                                        supply: 82,\n                                        averageSalary: '$95,000',\n                                        jobSeekers: 167,\n                                        openPositions: 54,\n                                        growth: '+5%',\n                                        description: 'JavaScript runtime for building server-side applications',\n                                        relatedSkills: [\n                                            'Express.js',\n                                            'MongoDB',\n                                            'GraphQL',\n                                            'REST APIs'\n                                        ],\n                                        icon: '🟢'\n                                    }\n                                ];\n                                setSkills(sampleSkills);\n                                setGraphData((0,_lib_graphData__WEBPACK_IMPORTED_MODULE_5__.generateSampleGraphData)());\n                                setLoading(false);\n                            }\n                        }[\"Skills.useEffect.fetchSkills\"], 1000);\n                    } catch (err) {\n                        setError(err.message);\n                        setLoading(false);\n                    }\n                }\n            }[\"Skills.useEffect.fetchSkills\"];\n            fetchSkills();\n        }\n    }[\"Skills.useEffect\"], []);\n    const filteredSkills = skills.filter((skill)=>{\n        const matchesSearch = skill.name.toLowerCase().includes(searchTerm.toLowerCase()) || skill.category.toLowerCase().includes(searchTerm.toLowerCase()) || skill.description.toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesCategory = filterCategory === 'all' || skill.category === filterCategory;\n        return matchesSearch && matchesCategory;\n    });\n    const sortedSkills = [\n        ...filteredSkills\n    ].sort((a, b)=>{\n        switch(sortBy){\n            case 'demand':\n                return b.demand - a.demand;\n            case 'supply':\n                return b.supply - a.supply;\n            case 'salary':\n                return parseInt(b.averageSalary.replace(/[$,]/g, '')) - parseInt(a.averageSalary.replace(/[$,]/g, ''));\n            case 'growth':\n                return parseInt(b.growth.replace(/[+%]/g, '')) - parseInt(a.growth.replace(/[+%]/g, ''));\n            case 'name':\n                return a.name.localeCompare(b.name);\n            default:\n                return 0;\n        }\n    });\n    const getDemandColor = (demand)=>{\n        if (demand >= 90) return 'bg-red-100 text-red-800';\n        if (demand >= 80) return 'bg-orange-100 text-orange-800';\n        if (demand >= 70) return 'bg-yellow-100 text-yellow-800';\n        return 'bg-green-100 text-green-800';\n    };\n    const getSupplyColor = (supply)=>{\n        if (supply >= 80) return 'bg-green-100 text-green-800';\n        if (supply >= 60) return 'bg-yellow-100 text-yellow-800';\n        if (supply >= 40) return 'bg-orange-100 text-orange-800';\n        return 'bg-red-100 text-red-800';\n    };\n    const getGrowthColor = (growth)=>{\n        const growthNum = parseInt(growth.replace(/[+%]/g, ''));\n        if (growthNum >= 20) return 'bg-emerald-100 text-emerald-800';\n        if (growthNum >= 10) return 'bg-green-100 text-green-800';\n        if (growthNum >= 5) return 'bg-yellow-100 text-yellow-800';\n        return 'bg-gray-100 text-gray-800';\n    };\n    const categories = [\n        'all',\n        ...new Set(skills.map((skill)=>skill.category))\n    ];\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center items-center h-64\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-amber-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                        lineNumber: 206,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                    lineNumber: 205,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                lineNumber: 204,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n            lineNumber: 203,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n                    children: [\n                        \"Error: \",\n                        error\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                    lineNumber: 217,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                lineNumber: 216,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n            lineNumber: 215,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                    children: \"Skills Analysis | Candid Connections Katra\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                    lineNumber: 228,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold\",\n                                children: \"Skills Analysis\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowVisualization(true),\n                                className: \"bg-amber-600 text-white px-4 py-2 rounded-lg hover:bg-amber-700 transition-colors\",\n                                children: \"\\uD83C\\uDF10 View Network\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                lineNumber: 234,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm p-4 mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-4 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-64\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search skills, categories, or descriptions...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"w-full border border-gray-300 rounded-md px-3 py-2 text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"Category:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                            lineNumber: 256,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: filterCategory,\n                                            onChange: (e)=>setFilterCategory(e.target.value),\n                                            className: \"border border-gray-300 rounded-md px-3 py-1 text-sm\",\n                                            children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: category,\n                                                    children: category === 'all' ? 'All Categories' : category\n                                                }, category, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                            lineNumber: 257,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                    lineNumber: 255,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"Sort by:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                            lineNumber: 271,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: sortBy,\n                                            onChange: (e)=>setSortBy(e.target.value),\n                                            className: \"border border-gray-300 rounded-md px-3 py-1 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"demand\",\n                                                    children: \"Market Demand\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"supply\",\n                                                    children: \"Talent Supply\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"salary\",\n                                                    children: \"Average Salary\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"growth\",\n                                                    children: \"Growth Rate\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"name\",\n                                                    children: \"Skill Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                            lineNumber: 272,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                    lineNumber: 270,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        sortedSkills.length,\n                                        \" skills found\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                    lineNumber: 285,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\",\n                        children: sortedSkills.map((skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl mr-3\",\n                                                        children: skill.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-lg text-gray-900\",\n                                                                children: skill.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 text-sm\",\n                                                                children: skill.category\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                                lineNumber: 297,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getGrowthColor(skill.growth)}`,\n                                                children: skill.growth\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                        lineNumber: 296,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getDemandColor(skill.demand)}`,\n                                                        children: [\n                                                            skill.demand,\n                                                            \"% Demand\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                        children: \"Market Demand\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                                lineNumber: 311,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getSupplyColor(skill.supply)}`,\n                                                        children: [\n                                                            skill.supply,\n                                                            \"% Supply\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                        children: \"Talent Supply\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                                lineNumber: 317,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                        lineNumber: 310,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Average Salary:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-emerald-600\",\n                                                        children: skill.averageSalary\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                                lineNumber: 327,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Job Seekers:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: skill.jobSeekers\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                                lineNumber: 331,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Open Positions:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: skill.openPositions\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                                lineNumber: 335,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                        lineNumber: 326,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-700 text-sm mb-4\",\n                                        children: skill.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                        lineNumber: 342,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                className: \"text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Related Skills:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                                lineNumber: 348,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-1\",\n                                                children: skill.relatedSkills.map((relatedSkill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded\",\n                                                        children: relatedSkill\n                                                    }, index, false, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                                lineNumber: 349,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                        lineNumber: 347,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"flex-1 bg-amber-600 text-white px-3 py-2 rounded text-sm hover:bg-amber-700 transition-colors\",\n                                                children: \"View Details\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                                lineNumber: 360,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"flex-1 border border-amber-600 text-amber-600 px-3 py-2 rounded text-sm hover:bg-amber-50 transition-colors\",\n                                                children: \"Find Talent\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                                lineNumber: 363,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                        lineNumber: 359,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, skill.id, true, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                lineNumber: 294,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                        lineNumber: 292,\n                        columnNumber: 9\n                    }, this),\n                    sortedSkills.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400 text-6xl mb-4\",\n                                children: \"\\uD83D\\uDEE0️\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                lineNumber: 373,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                children: \"No skills found\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                lineNumber: 374,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Try adjusting your search or filters to see more results.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                lineNumber: 375,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                        lineNumber: 372,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_VisualizationModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: showVisualization,\n                onClose: ()=>setShowVisualization(false),\n                data: graphData,\n                title: \"Skills Network\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                lineNumber: 381,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n        lineNumber: 226,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/skills.js\n");

/***/ }),

/***/ "(pages-dir-node)/./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "@tweenjs/tween.js":
/*!************************************!*\
  !*** external "@tweenjs/tween.js" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@tweenjs/tween.js");;

/***/ }),

/***/ "accessor-fn":
/*!******************************!*\
  !*** external "accessor-fn" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = import("accessor-fn");;

/***/ }),

/***/ "d3-array":
/*!***************************!*\
  !*** external "d3-array" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-array");;

/***/ }),

/***/ "d3-axis":
/*!**************************!*\
  !*** external "d3-axis" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-axis");;

/***/ }),

/***/ "d3-brush":
/*!***************************!*\
  !*** external "d3-brush" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-brush");;

/***/ }),

/***/ "d3-chord":
/*!***************************!*\
  !*** external "d3-chord" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-chord");;

/***/ }),

/***/ "d3-color":
/*!***************************!*\
  !*** external "d3-color" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-color");;

/***/ }),

/***/ "d3-contour":
/*!*****************************!*\
  !*** external "d3-contour" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-contour");;

/***/ }),

/***/ "d3-delaunay":
/*!******************************!*\
  !*** external "d3-delaunay" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-delaunay");;

/***/ }),

/***/ "d3-dispatch":
/*!******************************!*\
  !*** external "d3-dispatch" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-dispatch");;

/***/ }),

/***/ "d3-drag":
/*!**************************!*\
  !*** external "d3-drag" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-drag");;

/***/ }),

/***/ "d3-dsv":
/*!*************************!*\
  !*** external "d3-dsv" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-dsv");;

/***/ }),

/***/ "d3-ease":
/*!**************************!*\
  !*** external "d3-ease" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-ease");;

/***/ }),

/***/ "d3-fetch":
/*!***************************!*\
  !*** external "d3-fetch" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-fetch");;

/***/ }),

/***/ "d3-force":
/*!***************************!*\
  !*** external "d3-force" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-force");;

/***/ }),

/***/ "d3-format":
/*!****************************!*\
  !*** external "d3-format" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-format");;

/***/ }),

/***/ "d3-geo":
/*!*************************!*\
  !*** external "d3-geo" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-geo");;

/***/ }),

/***/ "d3-hierarchy":
/*!*******************************!*\
  !*** external "d3-hierarchy" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-hierarchy");;

/***/ }),

/***/ "d3-interpolate":
/*!*********************************!*\
  !*** external "d3-interpolate" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-interpolate");;

/***/ }),

/***/ "d3-path":
/*!**************************!*\
  !*** external "d3-path" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-path");;

/***/ }),

/***/ "d3-polygon":
/*!*****************************!*\
  !*** external "d3-polygon" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-polygon");;

/***/ }),

/***/ "d3-quadtree":
/*!******************************!*\
  !*** external "d3-quadtree" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-quadtree");;

/***/ }),

/***/ "d3-random":
/*!****************************!*\
  !*** external "d3-random" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-random");;

/***/ }),

/***/ "d3-scale":
/*!***************************!*\
  !*** external "d3-scale" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-scale");;

/***/ }),

/***/ "d3-scale-chromatic":
/*!*************************************!*\
  !*** external "d3-scale-chromatic" ***!
  \*************************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-scale-chromatic");;

/***/ }),

/***/ "d3-selection":
/*!*******************************!*\
  !*** external "d3-selection" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-selection");;

/***/ }),

/***/ "d3-shape":
/*!***************************!*\
  !*** external "d3-shape" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-shape");;

/***/ }),

/***/ "d3-time":
/*!**************************!*\
  !*** external "d3-time" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-time");;

/***/ }),

/***/ "d3-time-format":
/*!*********************************!*\
  !*** external "d3-time-format" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-time-format");;

/***/ }),

/***/ "d3-timer":
/*!***************************!*\
  !*** external "d3-timer" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-timer");;

/***/ }),

/***/ "d3-transition":
/*!********************************!*\
  !*** external "d3-transition" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-transition");;

/***/ }),

/***/ "d3-zoom":
/*!**************************!*\
  !*** external "d3-zoom" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = import("d3-zoom");;

/***/ }),

/***/ "float-tooltip":
/*!********************************!*\
  !*** external "float-tooltip" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = import("float-tooltip");;

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "kapsule":
/*!**************************!*\
  !*** external "kapsule" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = import("kapsule");;

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "polished":
/*!***************************!*\
  !*** external "polished" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("polished");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "three-forcegraph":
/*!***********************************!*\
  !*** external "three-forcegraph" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = import("three-forcegraph");;

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/d3"], () => (__webpack_exec__("(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fskills&preferredRegion=&absolutePagePath=.%2Fpages%2Fskills.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();