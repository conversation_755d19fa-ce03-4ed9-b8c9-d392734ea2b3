/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/skills";
exports.ids = ["pages/skills"];
exports.modules = {

/***/ "(pages-dir-node)/./components/Layout.js":
/*!******************************!*\
  !*** ./components/Layout.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Layout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Navigation */ \"(pages-dir-node)/./components/Navigation.js\");\n\n\nfunction Layout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Navigation__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Layout.js\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container-app section-padding\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Layout.js\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Layout.js\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL2NvbXBvbmVudHMvTGF5b3V0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXFDO0FBRXRCLFNBQVNDLE9BQU8sRUFBRUMsUUFBUSxFQUFFO0lBQ3pDLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0osbURBQVVBOzs7OzswQkFDWCw4REFBQ0s7Z0JBQUtELFdBQVU7MEJBQWlDRjs7Ozs7Ozs7Ozs7O0FBR3ZEIiwic291cmNlcyI6WyIvVXNlcnMvYnJhZHlnZW9yZ2VuL0RvY3VtZW50cy93b3Jrc3BhY2UvY2FuZGlkLWNvbm5lY3Rpb25zL2NvbXBvbmVudHMvTGF5b3V0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBOYXZpZ2F0aW9uIGZyb20gJy4vTmF2aWdhdGlvbidcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTGF5b3V0KHsgY2hpbGRyZW4gfSkge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLXdoaXRlXCI+XG4gICAgICA8TmF2aWdhdGlvbiAvPlxuICAgICAgPG1haW4gY2xhc3NOYW1lPVwiY29udGFpbmVyLWFwcCBzZWN0aW9uLXBhZGRpbmdcIj57Y2hpbGRyZW59PC9tYWluPlxuICAgIDwvZGl2PlxuICApXG59Il0sIm5hbWVzIjpbIk5hdmlnYXRpb24iLCJMYXlvdXQiLCJjaGlsZHJlbiIsImRpdiIsImNsYXNzTmFtZSIsIm1haW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/Layout.js\n");

/***/ }),

/***/ "(pages-dir-node)/./components/Navigation.js":
/*!**********************************!*\
  !*** ./components/Navigation.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(pages-dir-node)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-node)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nfunction Navigation() {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const navItems = [\n        {\n            name: 'Dashboard',\n            path: '/',\n            icon: '🏠'\n        },\n        {\n            name: 'Matches',\n            path: '/matches',\n            icon: '🎯'\n        },\n        {\n            name: 'Job Seekers',\n            path: '/job-seekers',\n            icon: '👥'\n        },\n        {\n            name: 'Hiring Authorities',\n            path: '/hiring-authorities',\n            icon: '👔'\n        },\n        {\n            name: 'Companies',\n            path: '/companies',\n            icon: '🏢'\n        },\n        {\n            name: 'Positions',\n            path: '/positions',\n            icon: '📋'\n        },\n        {\n            name: 'Skills',\n            path: '/skills',\n            icon: '🛠️'\n        },\n        {\n            name: 'Visualizations',\n            path: '/visualizations',\n            icon: '📊'\n        },\n        {\n            name: 'Network View',\n            path: '/global-view',\n            icon: '🌐'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-white shadow-soft border-b border-candid-gray-200\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-app\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0 flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/\",\n                                className: \"flex items-center space-x-3 group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 relative group-hover:scale-105 transition-transform duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            viewBox: \"0 0 48 48\",\n                                            className: \"w-full h-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"24\",\n                                                    cy: \"24\",\n                                                    r: \"22\",\n                                                    fill: \"none\",\n                                                    stroke: \"#1e3a8a\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 32,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"24\",\n                                                    cy: \"12\",\n                                                    r: \"3\",\n                                                    fill: \"#00d4ff\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 34,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"36\",\n                                                    cy: \"24\",\n                                                    r: \"3\",\n                                                    fill: \"#00d4ff\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 35,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"24\",\n                                                    cy: \"36\",\n                                                    r: \"3\",\n                                                    fill: \"#00d4ff\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 36,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"12\",\n                                                    cy: \"24\",\n                                                    r: \"3\",\n                                                    fill: \"#00d4ff\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 37,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"24\",\n                                                    cy: \"24\",\n                                                    r: \"4\",\n                                                    fill: \"#1e3a8a\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 38,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                    x1: \"24\",\n                                                    y1: \"15\",\n                                                    x2: \"24\",\n                                                    y2: \"20\",\n                                                    stroke: \"#00d4ff\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 40,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                    x1: \"33\",\n                                                    y1: \"24\",\n                                                    x2: \"28\",\n                                                    y2: \"24\",\n                                                    stroke: \"#00d4ff\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 41,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                    x1: \"24\",\n                                                    y1: \"33\",\n                                                    x2: \"24\",\n                                                    y2: \"28\",\n                                                    stroke: \"#00d4ff\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 42,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                    x1: \"15\",\n                                                    y1: \"24\",\n                                                    x2: \"20\",\n                                                    y2: \"24\",\n                                                    stroke: \"#00d4ff\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 43,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                            lineNumber: 30,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 29,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden sm:block\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold text-secondary-800 group-hover:text-primary-500 transition-colors duration-200\",\n                                                children: \"Candid Connections\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                lineNumber: 47,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-candid-gray-600 -mt-1\",\n                                                children: \"Katra Platform\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                lineNumber: 50,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                lineNumber: 27,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1\",\n                                children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: item.path,\n                                        className: `flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${router.pathname === item.path ? 'nav-link-active' : 'nav-link'}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-base\",\n                                                children: item.icon\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                lineNumber: 68,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                lineNumber: 69,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, item.path, true, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 59,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                lineNumber: 57,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/admin\",\n                                    className: \"btn-outline text-sm py-2 px-4\",\n                                    children: \"⚙️ Admin\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/visualizations\",\n                                    className: \"btn-outline text-sm py-2 px-4\",\n                                    children: \"\\uD83D\\uDCCA Visualize\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"https://portal.candid-connections.com/user/login\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"btn-primary text-sm py-2 px-4\",\n                                    children: \"Portal Login\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),\n                                className: \"p-2 rounded-lg text-candid-navy-600 hover:text-primary-600 hover:bg-primary-50 transition-colors duration-200\",\n                                \"aria-label\": \"Toggle mobile menu\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 108,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M4 6h16M4 12h16M4 18h16\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 110,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                    lineNumber: 106,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this),\n                isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"lg:hidden border-t border-candid-gray-200 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: item.path,\n                                    onClick: ()=>setIsMobileMenuOpen(false),\n                                    className: `flex items-center space-x-3 px-4 py-3 rounded-lg text-base font-medium transition-all duration-200 ${router.pathname === item.path ? 'nav-link-active' : 'nav-link'}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg\",\n                                            children: item.icon\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                            lineNumber: 132,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                            lineNumber: 133,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, item.path, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                    lineNumber: 122,\n                                    columnNumber: 17\n                                }, this)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-4 space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/visualizations\",\n                                        onClick: ()=>setIsMobileMenuOpen(false),\n                                        className: \"block w-full btn-outline text-center\",\n                                        children: \"\\uD83D\\uDCCA Visualize\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 139,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"https://portal.candid-connections.com/user/login\",\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        className: \"block w-full btn-primary text-center\",\n                                        children: \"Portal Login\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 146,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                lineNumber: 138,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                        lineNumber: 120,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                    lineNumber: 119,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/Navigation.js\n");

/***/ }),

/***/ "(pages-dir-node)/./components/ui/CollapsibleCard.js":
/*!******************************************!*\
  !*** ./components/ui/CollapsibleCard.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CompanyCard: () => (/* binding */ CompanyCard),\n/* harmony export */   SkillCard: () => (/* binding */ SkillCard),\n/* harmony export */   \"default\": () => (/* binding */ CollapsibleCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction CollapsibleCard({ title, subtitle, icon, primaryMetrics, expandedContent, actions, variant = 'default', className = '' }) {\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const variants = {\n        default: 'border-gray-200 hover:border-gray-300',\n        skill: 'border-blue-200 hover:border-blue-300 bg-gradient-to-br from-blue-50 to-white',\n        company: 'border-purple-200 hover:border-purple-300 bg-gradient-to-br from-purple-50 to-white',\n        authority: 'border-cyan-200 hover:border-cyan-300 bg-gradient-to-br from-cyan-50 to-white',\n        position: 'border-orange-200 hover:border-orange-300 bg-gradient-to-br from-orange-50 to-white'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `card ${variants[variant]} transition-all duration-300 ${isExpanded ? 'shadow-lg' : 'shadow-sm hover:shadow-md'} ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 flex-1\",\n                            children: [\n                                icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl\",\n                                    children: icon\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                                    lineNumber: 30,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-900 truncate\",\n                                            children: title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                                            lineNumber: 33,\n                                            columnNumber: 15\n                                        }, this),\n                                        subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 truncate\",\n                                            children: subtitle\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                                            lineNumber: 35,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4 mr-4\",\n                            children: primaryMetrics\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsExpanded(!isExpanded),\n                            className: \"p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500\",\n                            \"aria-label\": isExpanded ? 'Collapse' : 'Expand',\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: `w-5 h-5 text-gray-500 transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`,\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M19 9l-7 7-7-7\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                                    lineNumber: 57,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                                lineNumber: 51,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-200 bg-white bg-opacity-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 space-y-4\",\n                    children: [\n                        expandedContent,\n                        actions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2 pt-4 border-t border-gray-200\",\n                            children: actions\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                            lineNumber: 71,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                    lineNumber: 66,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                lineNumber: 65,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n// Specialized collapsible cards for different entity types\nfunction SkillCard({ skill, onViewDetails, onFindTalent }) {\n    const primaryMetrics = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-lg font-bold text-blue-600\",\n                        children: skill.demand === 'Very High' ? '95%' : skill.demand === 'High' ? '85%' : '70%'\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-500\",\n                        children: \"Demand\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-lg font-bold text-green-600\",\n                        children: [\n                            skill.supply || Math.floor(Math.random() * 40) + 60,\n                            \"%\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-500\",\n                        children: \"Supply\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n    const expandedContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-sm font-medium text-gray-700\",\n                                children: \"Category\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-900\",\n                                children: skill.category\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-sm font-medium text-gray-700\",\n                                children: \"Average Salary\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-900\",\n                                children: skill.averageSalary || '$95,000'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-sm font-medium text-gray-700\",\n                                children: \"Job Seekers\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-900\",\n                                children: skill.jobSeekers || Math.floor(Math.random() * 100) + 50\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-sm font-medium text-gray-700\",\n                                children: \"Open Positions\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-900\",\n                                children: skill.openPositions || Math.floor(Math.random() * 50) + 20\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this),\n            skill.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"text-sm font-medium text-gray-700\",\n                        children: \"Description\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                        lineNumber: 123,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-900\",\n                        children: skill.description\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                        lineNumber: 124,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                lineNumber: 122,\n                columnNumber: 9\n            }, this),\n            skill.relatedSkills && skill.relatedSkills.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"text-sm font-medium text-gray-700\",\n                        children: \"Related Skills\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                        lineNumber: 130,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-1 mt-1\",\n                        children: skill.relatedSkills.map((relatedSkill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"badge badge-secondary text-xs\",\n                                children: relatedSkill\n                            }, index, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                                lineNumber: 133,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                lineNumber: 129,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, this);\n    const actions = [\n        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: ()=>onViewDetails(skill),\n            className: \"btn-primary text-sm px-4 py-2\",\n            children: \"View Details\"\n        }, \"details\", false, {\n            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n            lineNumber: 144,\n            columnNumber: 5\n        }, this),\n        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: ()=>onFindTalent(skill),\n            className: \"btn-outline text-sm px-4 py-2\",\n            children: \"Find Talent\"\n        }, \"talent\", false, {\n            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n            lineNumber: 151,\n            columnNumber: 5\n        }, this)\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CollapsibleCard, {\n        title: skill.name,\n        subtitle: skill.category,\n        icon: skill.icon || '🛠️',\n        primaryMetrics: primaryMetrics,\n        expandedContent: expandedContent,\n        actions: actions,\n        variant: \"skill\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n        lineNumber: 161,\n        columnNumber: 5\n    }, this);\n}\nfunction CompanyCard({ company, onViewDetails }) {\n    const primaryMetrics = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-lg font-bold text-purple-600\",\n                        children: company.employeeCount\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-500\",\n                        children: \"Employees\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                lineNumber: 176,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-lg font-bold text-orange-600\",\n                        children: company.openPositions || Math.floor(Math.random() * 20) + 5\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-500\",\n                        children: \"Open Roles\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                lineNumber: 180,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n    const expandedContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-sm font-medium text-gray-700\",\n                                children: \"Industry\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-900\",\n                                children: company.industry\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                                lineNumber: 192,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                        lineNumber: 190,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-sm font-medium text-gray-700\",\n                                children: \"Founded\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-900\",\n                                children: company.founded || 'N/A'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                                lineNumber: 196,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                        lineNumber: 194,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, this),\n            company.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"text-sm font-medium text-gray-700\",\n                        children: \"Description\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                        lineNumber: 202,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-900\",\n                        children: company.description\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                        lineNumber: 203,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                lineNumber: 201,\n                columnNumber: 9\n            }, this),\n            company.website && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"text-sm font-medium text-gray-700\",\n                        children: \"Website\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                        lineNumber: 209,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: company.website,\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        className: \"text-sm text-primary-600 hover:text-primary-700\",\n                        children: company.website\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                        lineNumber: 210,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n                lineNumber: 208,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n        lineNumber: 188,\n        columnNumber: 5\n    }, this);\n    const actions = [\n        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: ()=>onViewDetails(company),\n            className: \"btn-primary text-sm px-4 py-2\",\n            children: \"View Details\"\n        }, \"details\", false, {\n            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n            lineNumber: 219,\n            columnNumber: 5\n        }, this)\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CollapsibleCard, {\n        title: company.name,\n        subtitle: `${company.industry} • ${company.size}`,\n        icon: \"\\uD83C\\uDFE2\",\n        primaryMetrics: primaryMetrics,\n        expandedContent: expandedContent,\n        actions: actions,\n        variant: \"company\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/CollapsibleCard.js\",\n        lineNumber: 229,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/ui/CollapsibleCard.js\n");

/***/ }),

/***/ "(pages-dir-node)/./components/ui/DetailModal.js":
/*!**************************************!*\
  !*** ./components/ui/DetailModal.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DetailModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-node)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Modal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Modal */ \"(pages-dir-node)/./components/ui/Modal.js\");\n\n\n\n\nfunction DetailModal({ isOpen, onClose, entity, entityType, onFindTalent, onFindMatches }) {\n    const [aiDescription, setAiDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DetailModal.useEffect\": ()=>{\n            if (isOpen && entity) {\n                generateAIDescription();\n            }\n        }\n    }[\"DetailModal.useEffect\"], [\n        isOpen,\n        entity\n    ]);\n    const generateAIDescription = async ()=>{\n        if (!entity) return;\n        setLoading(true);\n        try {\n            const response = await fetch('/api/ai/describe', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    entity,\n                    entityType\n                })\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setAiDescription(data.description);\n            } else {\n                setAiDescription('Unable to generate description at this time.');\n            }\n        } catch (error) {\n            console.error('Error generating AI description:', error);\n            setAiDescription('Unable to generate description at this time.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleFindTalent = ()=>{\n        if (onFindTalent) {\n            onFindTalent(entity);\n        } else {\n            // Default behavior - navigate to job seekers with skill filter\n            const skillName = entity.name || entity.title;\n            router.push(`/job-seekers?skill=${encodeURIComponent(skillName)}`);\n        }\n        onClose();\n    };\n    const handleFindMatches = ()=>{\n        if (onFindMatches) {\n            onFindMatches(entity);\n        } else {\n            // Default behavior - navigate to matches with entity filter\n            const entityId = entity._key || entity.id;\n            router.push(`/matches?${entityType}=${entityId}`);\n        }\n        onClose();\n    };\n    const renderEntitySpecificContent = ()=>{\n        switch(entityType){\n            case 'skill':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: \"Category\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                        lineNumber: 82,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-900\",\n                                        children: entity.category || 'N/A'\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                        lineNumber: 83,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                lineNumber: 81,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: \"Market Demand\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                        lineNumber: 86,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `inline-flex px-2 py-1 text-xs font-medium rounded-full ${entity.demand === 'Very High' ? 'bg-red-100 text-red-800' : entity.demand === 'High' ? 'bg-orange-100 text-orange-800' : entity.demand === 'Medium' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'}`,\n                                        children: entity.demand || 'Medium'\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                        lineNumber: 87,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                lineNumber: 85,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                        lineNumber: 80,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                    lineNumber: 79,\n                    columnNumber: 11\n                }, this);\n            case 'position':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"Level\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                            lineNumber: 105,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-900\",\n                                            children: entity.level || 'N/A'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                            lineNumber: 106,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"Type\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                            lineNumber: 109,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-900\",\n                                            children: entity.type || 'N/A'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                            lineNumber: 110,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                    lineNumber: 108,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                            lineNumber: 103,\n                            columnNumber: 13\n                        }, this),\n                        entity.requirements && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-sm font-medium text-gray-700\",\n                                    children: \"Requirements\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                    lineNumber: 115,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-1 mt-1\",\n                                    children: entity.requirements.map((req, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"badge badge-secondary text-xs\",\n                                            children: req\n                                        }, index, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                            lineNumber: 118,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                    lineNumber: 116,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                            lineNumber: 114,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                    lineNumber: 102,\n                    columnNumber: 11\n                }, this);\n            case 'company':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"Industry\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                            lineNumber: 133,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-900\",\n                                            children: entity.industry || 'N/A'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                            lineNumber: 134,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                    lineNumber: 132,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"Size\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                            lineNumber: 137,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-900\",\n                                            children: [\n                                                entity.employeeCount,\n                                                \" employees\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                            lineNumber: 138,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                    lineNumber: 136,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                            lineNumber: 131,\n                            columnNumber: 13\n                        }, this),\n                        entity.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-sm font-medium text-gray-700\",\n                                    children: \"Description\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                    lineNumber: 143,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-900\",\n                                    children: entity.description\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                    lineNumber: 144,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                            lineNumber: 142,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                    lineNumber: 130,\n                    columnNumber: 11\n                }, this);\n            default:\n                return null;\n        }\n    };\n    if (!entity) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Modal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        isOpen: isOpen,\n        onClose: onClose,\n        title: `${entityType.charAt(0).toUpperCase() + entityType.slice(1)} Details`,\n        size: \"lg\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-secondary-800\",\n                            children: entity.name || entity.title\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, this),\n                        entity.role && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-candid-gray-600\",\n                            children: entity.role\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                            lineNumber: 171,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                    lineNumber: 166,\n                    columnNumber: 9\n                }, this),\n                renderEntitySpecificContent(),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t pt-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"text-sm font-medium text-gray-700 mb-2 block\",\n                            children: \"AI Analysis\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, this),\n                        loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"loading-spinner w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                    lineNumber: 185,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-candid-gray-600\",\n                                    children: \"Generating intelligent analysis...\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                    lineNumber: 186,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                            lineNumber: 184,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-blue-900 leading-relaxed\",\n                                children: aiDescription || 'No description available.'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                                lineNumber: 190,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                            lineNumber: 189,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                    lineNumber: 179,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-3 pt-4 border-t\",\n                    children: [\n                        (entityType === 'skill' || entityType === 'position') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleFindTalent,\n                            className: \"btn-primary flex-1\",\n                            children: \"Find Talent\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                            lineNumber: 200,\n                            columnNumber: 13\n                        }, this),\n                        entityType !== 'skill' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleFindMatches,\n                            className: \"btn-outline flex-1\",\n                            children: \"Find Matches\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                            lineNumber: 209,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"btn-outline\",\n                            children: \"Close\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n                    lineNumber: 198,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n            lineNumber: 164,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/DetailModal.js\",\n        lineNumber: 158,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/ui/DetailModal.js\n");

/***/ }),

/***/ "(pages-dir-node)/./components/ui/Modal.js":
/*!********************************!*\
  !*** ./components/ui/Modal.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Modal),\n/* harmony export */   useModal: () => (/* binding */ useModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Modal({ isOpen, onClose, title, children, size = 'md', showCloseButton = true }) {\n    // Close modal on escape key\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Modal.useEffect\": ()=>{\n            const handleEscape = {\n                \"Modal.useEffect.handleEscape\": (e)=>{\n                    if (e.key === 'Escape' && isOpen) {\n                        onClose();\n                    }\n                }\n            }[\"Modal.useEffect.handleEscape\"];\n            if (isOpen) {\n                document.addEventListener('keydown', handleEscape);\n                document.body.style.overflow = 'hidden';\n            }\n            return ({\n                \"Modal.useEffect\": ()=>{\n                    document.removeEventListener('keydown', handleEscape);\n                    document.body.style.overflow = 'unset';\n                }\n            })[\"Modal.useEffect\"];\n        }\n    }[\"Modal.useEffect\"], [\n        isOpen,\n        onClose\n    ]);\n    if (!isOpen) return null;\n    const sizeClasses = {\n        sm: 'max-w-md',\n        md: 'max-w-2xl',\n        lg: 'max-w-4xl',\n        xl: 'max-w-6xl',\n        full: 'max-w-7xl'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 overflow-y-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 transition-opacity\",\n                onClick: onClose\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/Modal.js\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex min-h-full items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `relative bg-white rounded-xl shadow-xl w-full ${sizeClasses[size]} transform transition-all`,\n                    onClick: (e)=>e.stopPropagation(),\n                    children: [\n                        (title || showCloseButton) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n                            children: [\n                                title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-secondary-800\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/Modal.js\",\n                                    lineNumber: 58,\n                                    columnNumber: 17\n                                }, this),\n                                showCloseButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"ml-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 inline-flex items-center justify-center h-8 w-8 transition-colors\",\n                                    \"aria-label\": \"Close modal\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-3 h-3\",\n                                        \"aria-hidden\": \"true\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 14 14\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            stroke: \"currentColor\",\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: \"2\",\n                                            d: \"m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/Modal.js\",\n                                            lineNumber: 70,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/Modal.js\",\n                                        lineNumber: 69,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/Modal.js\",\n                                    lineNumber: 64,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/Modal.js\",\n                            lineNumber: 56,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/Modal.js\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/Modal.js\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/Modal.js\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/ui/Modal.js\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n// Reusable modal hook\nfunction useModal() {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const openModal = ()=>setIsOpen(true);\n    const closeModal = ()=>setIsOpen(false);\n    return {\n        isOpen,\n        openModal,\n        closeModal\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/ui/Modal.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fskills&preferredRegion=&absolutePagePath=.%2Fpages%2Fskills.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fskills&preferredRegion=&absolutePagePath=.%2Fpages%2Fskills.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages/module.compiled */ \"(pages-dir-node)/./node_modules/next/dist/server/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(pages-dir-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(pages-dir-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"(pages-dir-node)/./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"(pages-dir-node)/./pages/_app.js\");\n/* harmony import */ var _pages_skills_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages/skills.js */ \"(pages-dir-node)/./pages/skills.js\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_skills_js__WEBPACK_IMPORTED_MODULE_5__, 'default'));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_skills_js__WEBPACK_IMPORTED_MODULE_5__, 'getStaticProps');\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_skills_js__WEBPACK_IMPORTED_MODULE_5__, 'getStaticPaths');\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_skills_js__WEBPACK_IMPORTED_MODULE_5__, 'getServerSideProps');\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_skills_js__WEBPACK_IMPORTED_MODULE_5__, 'config');\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_skills_js__WEBPACK_IMPORTED_MODULE_5__, 'reportWebVitals');\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_skills_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticProps');\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_skills_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticPaths');\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_skills_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticParams');\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_skills_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerProps');\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_skills_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/skills\",\n        pathname: \"/skills\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    components: {\n        // default export might not exist when optimized for data only\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_skills_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fskills&preferredRegion=&absolutePagePath=.%2Fpages%2Fskills.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"(pages-dir-node)/./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/_app.js\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3BhZ2VzL19hcHAuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQThCO0FBRWYsU0FBU0EsSUFBSSxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBRTtJQUNsRCxxQkFBTyw4REFBQ0Q7UUFBVyxHQUFHQyxTQUFTOzs7Ozs7QUFDakMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9icmFkeWdlb3JnZW4vRG9jdW1lbnRzL3dvcmtzcGFjZS9jYW5kaWQtY29ubmVjdGlvbnMvcGFnZXMvX2FwcC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgJy4uL3N0eWxlcy9nbG9iYWxzLmNzcydcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfSkge1xuICByZXR1cm4gPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxufVxuIl0sIm5hbWVzIjpbIkFwcCIsIkNvbXBvbmVudCIsInBhZ2VQcm9wcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_app.js\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/skills.js":
/*!*************************!*\
  !*** ./pages/skills.js ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Skills)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"(pages-dir-node)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"(pages-dir-node)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/Layout */ \"(pages-dir-node)/./components/Layout.js\");\n/* harmony import */ var _components_ui_DetailModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/ui/DetailModal */ \"(pages-dir-node)/./components/ui/DetailModal.js\");\n/* harmony import */ var _components_ui_CollapsibleCard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/ui/CollapsibleCard */ \"(pages-dir-node)/./components/ui/CollapsibleCard.js\");\n\n\n\n\n\n\n\nfunction Skills() {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [skills, setSkills] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedSkill, setSelectedSkill] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showDetailModal, setShowDetailModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [filterCategory, setFilterCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('demand');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Skills.useEffect\": ()=>{\n            const fetchSkills = {\n                \"Skills.useEffect.fetchSkills\": async ()=>{\n                    try {\n                        const response = await fetch('/api/skills');\n                        if (response.ok) {\n                            const data = await response.json();\n                            const skillsData = data.skills || data;\n                            // Enhance skills with calculated metrics\n                            const enhancedSkills = skillsData.map({\n                                \"Skills.useEffect.fetchSkills.enhancedSkills\": (skill)=>({\n                                        ...skill,\n                                        demand: skill.demand === 'Very High' ? 95 : skill.demand === 'High' ? 85 : skill.demand === 'Medium' ? 70 : 60,\n                                        supply: Math.floor(Math.random() * 40) + 60,\n                                        averageSalary: calculateAverageSalary(skill.category),\n                                        jobSeekers: Math.floor(Math.random() * 100) + 50,\n                                        openPositions: Math.floor(Math.random() * 50) + 20,\n                                        growth: `+${Math.floor(Math.random() * 25) + 5}%`,\n                                        description: getSkillDescription(skill.name),\n                                        relatedSkills: getRelatedSkills(skill.name),\n                                        icon: getSkillIcon(skill.category)\n                                    })\n                            }[\"Skills.useEffect.fetchSkills.enhancedSkills\"]);\n                            setSkills(enhancedSkills);\n                        } else {\n                            // Fallback to sample data if API fails\n                            const sampleSkills = [\n                                {\n                                    id: 'skill_1',\n                                    name: 'React',\n                                    category: 'Frontend',\n                                    demand: 95,\n                                    supply: 78,\n                                    averageSalary: '$105,000',\n                                    jobSeekers: 156,\n                                    openPositions: 89,\n                                    growth: '+12%',\n                                    description: 'JavaScript library for building user interfaces',\n                                    relatedSkills: [\n                                        'JavaScript',\n                                        'TypeScript',\n                                        'Redux',\n                                        'Next.js'\n                                    ],\n                                    icon: '⚛️'\n                                },\n                                {\n                                    id: 'skill_2',\n                                    name: 'Python',\n                                    category: 'Backend',\n                                    demand: 92,\n                                    supply: 85,\n                                    averageSalary: '$98,000',\n                                    jobSeekers: 203,\n                                    openPositions: 76,\n                                    growth: '+8%',\n                                    description: 'High-level programming language for web development, data science, and automation',\n                                    relatedSkills: [\n                                        'Django',\n                                        'Flask',\n                                        'FastAPI',\n                                        'NumPy'\n                                    ],\n                                    icon: '🐍'\n                                },\n                                {\n                                    id: 'skill_3',\n                                    name: 'Kubernetes',\n                                    category: 'DevOps',\n                                    demand: 88,\n                                    supply: 45,\n                                    averageSalary: '$125,000',\n                                    jobSeekers: 67,\n                                    openPositions: 52,\n                                    growth: '+25%',\n                                    description: 'Container orchestration platform for automating deployment and scaling',\n                                    relatedSkills: [\n                                        'Docker',\n                                        'Terraform',\n                                        'AWS',\n                                        'Jenkins'\n                                    ],\n                                    icon: '☸️'\n                                },\n                                {\n                                    id: 'skill_4',\n                                    name: 'Figma',\n                                    category: 'Design',\n                                    demand: 85,\n                                    supply: 72,\n                                    averageSalary: '$85,000',\n                                    jobSeekers: 134,\n                                    openPositions: 43,\n                                    growth: '+15%',\n                                    description: 'Collaborative design tool for creating user interfaces and prototypes',\n                                    relatedSkills: [\n                                        'Sketch',\n                                        'Adobe XD',\n                                        'Prototyping',\n                                        'User Research'\n                                    ],\n                                    icon: '🎨'\n                                },\n                                {\n                                    id: 'skill_5',\n                                    name: 'TypeScript',\n                                    category: 'Frontend',\n                                    demand: 82,\n                                    supply: 65,\n                                    averageSalary: '$108,000',\n                                    jobSeekers: 98,\n                                    openPositions: 67,\n                                    growth: '+18%',\n                                    description: 'Typed superset of JavaScript that compiles to plain JavaScript',\n                                    relatedSkills: [\n                                        'JavaScript',\n                                        'React',\n                                        'Angular',\n                                        'Node.js'\n                                    ],\n                                    icon: '📘'\n                                },\n                                {\n                                    id: 'skill_6',\n                                    name: 'AWS',\n                                    category: 'Cloud',\n                                    demand: 90,\n                                    supply: 58,\n                                    averageSalary: '$115,000',\n                                    jobSeekers: 89,\n                                    openPositions: 78,\n                                    growth: '+20%',\n                                    description: 'Amazon Web Services cloud computing platform',\n                                    relatedSkills: [\n                                        'EC2',\n                                        'S3',\n                                        'Lambda',\n                                        'CloudFormation'\n                                    ],\n                                    icon: '☁️'\n                                },\n                                {\n                                    id: 'skill_7',\n                                    name: 'Machine Learning',\n                                    category: 'Data Science',\n                                    demand: 87,\n                                    supply: 42,\n                                    averageSalary: '$130,000',\n                                    jobSeekers: 45,\n                                    openPositions: 38,\n                                    growth: '+30%',\n                                    description: 'AI technique that enables computers to learn and improve from experience',\n                                    relatedSkills: [\n                                        'Python',\n                                        'TensorFlow',\n                                        'PyTorch',\n                                        'Scikit-learn'\n                                    ],\n                                    icon: '🤖'\n                                },\n                                {\n                                    id: 'skill_8',\n                                    name: 'Node.js',\n                                    category: 'Backend',\n                                    demand: 78,\n                                    supply: 82,\n                                    averageSalary: '$95,000',\n                                    jobSeekers: 167,\n                                    openPositions: 54,\n                                    growth: '+5%',\n                                    description: 'JavaScript runtime for building server-side applications',\n                                    relatedSkills: [\n                                        'Express.js',\n                                        'MongoDB',\n                                        'GraphQL',\n                                        'REST APIs'\n                                    ],\n                                    icon: '🟢'\n                                }\n                            ];\n                            setSkills(sampleSkills);\n                        }\n                        setLoading(false);\n                    } catch (err) {\n                        setError(err.message);\n                        setLoading(false);\n                    }\n                }\n            }[\"Skills.useEffect.fetchSkills\"];\n            fetchSkills();\n        }\n    }[\"Skills.useEffect\"], []);\n    // Helper functions\n    const calculateAverageSalary = (category)=>{\n        const salaryRanges = {\n            'Frontend': '$95,000',\n            'Backend': '$98,000',\n            'DevOps': '$115,000',\n            'Design': '$85,000',\n            'Cloud': '$110,000',\n            'AI': '$125,000',\n            'Data Science': '$120,000',\n            'Systems': '$105,000',\n            'Soft Skills': '$90,000',\n            'Business': '$95,000',\n            'Methodology': '$85,000'\n        };\n        return salaryRanges[category] || '$95,000';\n    };\n    const getSkillDescription = (skillName)=>{\n        const descriptions = {\n            'React': 'JavaScript library for building user interfaces',\n            'Node.js': 'JavaScript runtime for server-side development',\n            'Python': 'High-level programming language for web development and data science',\n            'TypeScript': 'Typed superset of JavaScript',\n            'Kubernetes': 'Container orchestration platform',\n            'Docker': 'Containerization platform',\n            'Terraform': 'Infrastructure as code tool',\n            'AWS': 'Amazon Web Services cloud platform',\n            'Blockchain': 'Distributed ledger technology',\n            'Solidity': 'Programming language for smart contracts',\n            'Figma': 'Collaborative design tool',\n            'User Research': 'Methods for understanding user needs',\n            'Machine Learning': 'AI technique for pattern recognition',\n            'TensorFlow': 'Open-source machine learning framework',\n            'C++': 'General-purpose programming language',\n            'Embedded Systems': 'Computer systems with dedicated functions',\n            'Robotics': 'Technology for automated machines',\n            'Leadership': 'Ability to guide and inspire teams',\n            'Product Management': 'Strategic product development and planning',\n            'Agile': 'Iterative software development methodology'\n        };\n        return descriptions[skillName] || `Professional skill in ${skillName}`;\n    };\n    const getRelatedSkills = (skillName)=>{\n        const related = {\n            'React': [\n                'JavaScript',\n                'TypeScript',\n                'Redux',\n                'Next.js'\n            ],\n            'Node.js': [\n                'Express.js',\n                'MongoDB',\n                'GraphQL',\n                'REST APIs'\n            ],\n            'Python': [\n                'Django',\n                'Flask',\n                'FastAPI',\n                'NumPy'\n            ],\n            'TypeScript': [\n                'JavaScript',\n                'React',\n                'Angular',\n                'Node.js'\n            ],\n            'Kubernetes': [\n                'Docker',\n                'Terraform',\n                'AWS',\n                'Jenkins'\n            ],\n            'Docker': [\n                'Kubernetes',\n                'CI/CD',\n                'DevOps',\n                'Containerization'\n            ],\n            'AWS': [\n                'Cloud Computing',\n                'EC2',\n                'S3',\n                'Lambda'\n            ],\n            'Figma': [\n                'Sketch',\n                'Adobe XD',\n                'Prototyping',\n                'User Research'\n            ],\n            'Machine Learning': [\n                'Python',\n                'TensorFlow',\n                'PyTorch',\n                'Data Science'\n            ]\n        };\n        return related[skillName] || [\n            'Technology',\n            'Software',\n            'Development'\n        ];\n    };\n    const getSkillIcon = (category)=>{\n        const icons = {\n            'Frontend': '⚛️',\n            'Backend': '🐍',\n            'DevOps': '☸️',\n            'Design': '🎨',\n            'Cloud': '☁️',\n            'AI': '🤖',\n            'Data Science': '📊',\n            'Systems': '⚙️',\n            'Soft Skills': '👥',\n            'Business': '💼',\n            'Methodology': '📋',\n            'Blockchain': '⛓️',\n            'Hardware': '🔧',\n            'Engineering': '🏗️'\n        };\n        return icons[category] || '🛠️';\n    };\n    const handleViewDetails = (skill)=>{\n        setSelectedSkill(skill);\n        setShowDetailModal(true);\n    };\n    const handleFindTalent = (skill)=>{\n        router.push(`/job-seekers?skill=${encodeURIComponent(skill.name)}`);\n    };\n    const filteredSkills = skills.filter((skill)=>{\n        const matchesSearch = skill.name.toLowerCase().includes(searchTerm.toLowerCase()) || skill.category.toLowerCase().includes(searchTerm.toLowerCase()) || skill.description.toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesCategory = filterCategory === 'all' || skill.category === filterCategory;\n        return matchesSearch && matchesCategory;\n    });\n    const sortedSkills = [\n        ...filteredSkills\n    ].sort((a, b)=>{\n        switch(sortBy){\n            case 'demand':\n                return b.demand - a.demand;\n            case 'supply':\n                return b.supply - a.supply;\n            case 'salary':\n                return parseInt(b.averageSalary.replace(/[$,]/g, '')) - parseInt(a.averageSalary.replace(/[$,]/g, ''));\n            case 'growth':\n                return parseInt(b.growth.replace(/[+%]/g, '')) - parseInt(a.growth.replace(/[+%]/g, ''));\n            case 'name':\n                return a.name.localeCompare(b.name);\n            default:\n                return 0;\n        }\n    });\n    const getDemandColor = (demand)=>{\n        if (demand >= 90) return 'bg-red-100 text-red-800';\n        if (demand >= 80) return 'bg-orange-100 text-orange-800';\n        if (demand >= 70) return 'bg-yellow-100 text-yellow-800';\n        return 'bg-green-100 text-green-800';\n    };\n    const getSupplyColor = (supply)=>{\n        if (supply >= 80) return 'bg-green-100 text-green-800';\n        if (supply >= 60) return 'bg-yellow-100 text-yellow-800';\n        if (supply >= 40) return 'bg-orange-100 text-orange-800';\n        return 'bg-red-100 text-red-800';\n    };\n    const getGrowthColor = (growth)=>{\n        const growthNum = parseInt(growth.replace(/[+%]/g, ''));\n        if (growthNum >= 20) return 'bg-emerald-100 text-emerald-800';\n        if (growthNum >= 10) return 'bg-green-100 text-green-800';\n        if (growthNum >= 5) return 'bg-yellow-100 text-yellow-800';\n        return 'bg-gray-100 text-gray-800';\n    };\n    const categories = [\n        'all',\n        ...new Set(skills.map((skill)=>skill.category))\n    ];\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center items-center h-64\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-amber-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                        lineNumber: 314,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                    lineNumber: 313,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                lineNumber: 312,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n            lineNumber: 311,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n                    children: [\n                        \"Error: \",\n                        error\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                    lineNumber: 325,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                lineNumber: 324,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n            lineNumber: 323,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                    children: \"Skills Analysis | Candid Connections Katra\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                    lineNumber: 336,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                lineNumber: 335,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold\",\n                                children: \"Skills Analysis\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                lineNumber: 341,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push('/visualizations'),\n                                className: \"bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors\",\n                                children: \"\\uD83D\\uDCCA Visualize\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                lineNumber: 342,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                        lineNumber: 340,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm p-4 mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-4 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-64\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search skills, categories, or descriptions...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"w-full border border-gray-300 rounded-md px-3 py-2 text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                        lineNumber: 354,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                    lineNumber: 353,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"Category:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                            lineNumber: 364,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: filterCategory,\n                                            onChange: (e)=>setFilterCategory(e.target.value),\n                                            className: \"border border-gray-300 rounded-md px-3 py-1 text-sm\",\n                                            children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: category,\n                                                    children: category === 'all' ? 'All Categories' : category\n                                                }, category, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                            lineNumber: 365,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                    lineNumber: 363,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"Sort by:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                            lineNumber: 379,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: sortBy,\n                                            onChange: (e)=>setSortBy(e.target.value),\n                                            className: \"border border-gray-300 rounded-md px-3 py-1 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"demand\",\n                                                    children: \"Market Demand\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"supply\",\n                                                    children: \"Talent Supply\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"salary\",\n                                                    children: \"Average Salary\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"growth\",\n                                                    children: \"Growth Rate\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"name\",\n                                                    children: \"Skill Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                            lineNumber: 380,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                    lineNumber: 378,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        sortedSkills.length,\n                                        \" skills found\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                    lineNumber: 393,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                            lineNumber: 352,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                        lineNumber: 351,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: sortedSkills.map((skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CollapsibleCard__WEBPACK_IMPORTED_MODULE_6__.SkillCard, {\n                                skill: skill,\n                                onViewDetails: handleViewDetails,\n                                onFindTalent: handleFindTalent\n                            }, skill._key || skill.id, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                lineNumber: 402,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                        lineNumber: 400,\n                        columnNumber: 9\n                    }, this),\n                    sortedSkills.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400 text-6xl mb-4\",\n                                children: \"\\uD83D\\uDEE0️\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                lineNumber: 413,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                children: \"No skills found\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                lineNumber: 414,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Try adjusting your search or filters to see more results.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                                lineNumber: 415,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                        lineNumber: 412,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                lineNumber: 339,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_DetailModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: showDetailModal,\n                onClose: ()=>setShowDetailModal(false),\n                entity: selectedSkill,\n                entityType: \"skill\",\n                onFindTalent: handleFindTalent\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n                lineNumber: 421,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/skills.js\",\n        lineNumber: 334,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/skills.js\n");

/***/ }),

/***/ "(pages-dir-node)/./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fskills&preferredRegion=&absolutePagePath=.%2Fpages%2Fskills.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();