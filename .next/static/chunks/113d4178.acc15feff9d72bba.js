"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[728],{9220:(t,e,i)=>{let s,r;i.d(e,{$EB:()=>g,$Yl:()=>U,$_I:()=>t_,$ei:()=>I,$p8:()=>hs,A$4:()=>s5,AQS:()=>ia,B69:()=>sT,BH$:()=>n9,BKk:()=>r_,BXX:()=>tK,B_h:()=>t8,CR7:()=>eN,CSG:()=>ow,CVz:()=>t3,CWW:()=>ef,Cfg:()=>tM,Df:()=>ov,Dmk:()=>tV,EZo:()=>b,EdD:()=>w,F1T:()=>rB,FFZ:()=>eX,FNr:()=>o_,FV:()=>tn,FXf:()=>A,FZo:()=>ha,Fn:()=>eu,Fvt:()=>oA,GJx:()=>tf,GWd:()=>tj,GYF:()=>no,G_z:()=>oM,Gu$:()=>oa,Gwm:()=>Z,H23:()=>ec,H2z:()=>hN,HIg:()=>tF,HLH:()=>eI,HO_:()=>ey,HPb:()=>eE,HXV:()=>t1,HiM:()=>o7,Ho_:()=>au,I9Y:()=>ii,IE4:()=>tZ,IUQ:()=>iI,Iit:()=>rM,Jnc:()=>u,K52:()=>Y,KDk:()=>t4,KLL:()=>e_,KRh:()=>$,Kef:()=>ep,Kwu:()=>M,LAk:()=>to,LiQ:()=>O,LlO:()=>rS,LoY:()=>rn,LuO:()=>hC,MW4:()=>s4,Mjd:()=>ts,Mmk:()=>iy,N5j:()=>eg,NRn:()=>iV,NTi:()=>v,NZq:()=>tA,Nex:()=>hV,Nt7:()=>N,Nz6:()=>tY,O0B:()=>iC,O9p:()=>su,OUM:()=>tz,Oax:()=>s6,Om:()=>ty,OuU:()=>R,PPD:()=>nz,PTz:()=>iE,Pem:()=>an,Pq0:()=>iR,Q1f:()=>sq,QP0:()=>d,Qev:()=>e0,Qrf:()=>et,R3r:()=>rP,ROr:()=>eC,RQf:()=>tO,RcT:()=>eV,Riy:()=>t5,RlV:()=>i7,RoJ:()=>rD,RrE:()=>j,Ru$:()=>eB,RyA:()=>m,S$4:()=>el,S7T:()=>ao,THS:()=>s3,TMh:()=>eF,Tap:()=>oY,TdN:()=>eK,TiK:()=>eH,TkQ:()=>tq,U3G:()=>X,UTZ:()=>tu,V3x:()=>tN,V9B:()=>sY,VCu:()=>aa,VGF:()=>tG,VT0:()=>tW,VVr:()=>ez,Vb5:()=>l,Vnu:()=>eY,VxR:()=>eS,W9U:()=>ed,WNZ:()=>h,Wdf:()=>eQ,Wew:()=>tR,Wk7:()=>p,XG_:()=>em,XIg:()=>x,XTe:()=>hc,XrR:()=>tt,Y9S:()=>oX,YHV:()=>hR,YJl:()=>rE,Yuy:()=>tB,Z58:()=>rL,ZQM:()=>tH,Zcv:()=>nS,Zr2:()=>ew,ZyN:()=>hi,_4j:()=>of,_QJ:()=>es,_Ut:()=>rw,a55:()=>e5,a5J:()=>ei,aEY:()=>L,aHM:()=>oJ,aJ8:()=>th,aVO:()=>oS,agE:()=>eZ,amv:()=>eU,b4q:()=>rk,bC7:()=>ea,bCz:()=>S,bI3:()=>ex,bTm:()=>c,bdM:()=>os,bkx:()=>tk,brA:()=>q,bw0:()=>Q,c90:()=>tX,cHt:()=>tC,caT:()=>K,cj9:()=>ie,czI:()=>t9,dYF:()=>iO,dcC:()=>tD,dth:()=>oK,dwI:()=>is,e0p:()=>D,eB$:()=>rF,eHc:()=>H,eHs:()=>rU,eaF:()=>rg,eoi:()=>eJ,er$:()=>eM,f4X:()=>k,fBL:()=>tI,fJr:()=>ek,g7M:()=>ta,gJ2:()=>tP,gO9:()=>_,gPd:()=>iT,gWB:()=>eG,ghU:()=>tg,hB5:()=>y,hdd:()=>P,hgQ:()=>F,hsX:()=>f,hxR:()=>tv,hy7:()=>tc,i7d:()=>rA,i7u:()=>e$,iNn:()=>rv,ie2:()=>E,imn:()=>sZ,ix0:()=>tE,iyt:()=>i1,jR7:()=>tQ,jej:()=>ip,jf0:()=>eb,jsO:()=>eP,jut:()=>ho,jzd:()=>eq,k6Q:()=>t$,k6q:()=>tw,kBv:()=>a,kG0:()=>eT,kO0:()=>eW,kRr:()=>tS,kTW:()=>tx,kTp:()=>t0,kYr:()=>eR,k_V:()=>hP,klZ:()=>eL,kn4:()=>st,kqe:()=>eA,kyO:()=>ti,lGu:()=>J,lPF:()=>iu,lxW:()=>rb,lyL:()=>en,mcG:()=>id,mrM:()=>nX,nCl:()=>o5,nNL:()=>tr,nST:()=>z,nWS:()=>iB,nZQ:()=>hy,o6l:()=>rO,oVO:()=>eO,ojh:()=>C,ojs:()=>eh,ov9:()=>W,pBf:()=>t2,pHI:()=>tb,paN:()=>tJ,ppV:()=>iv,psI:()=>ee,q2:()=>om,qBx:()=>ob,qUd:()=>ht,qa3:()=>t6,qad:()=>B,qq$:()=>il,qtW:()=>s8,rFo:()=>ik,rSH:()=>t7,rrX:()=>hI,sKt:()=>ej,sPf:()=>n,tBo:()=>hk,tJf:()=>tT,tXL:()=>ox,uB5:()=>er,uSd:()=>og,uV5:()=>tm,uWO:()=>nc,ubm:()=>rC,uf3:()=>nC,ure:()=>hr,vim:()=>eD,vyJ:()=>ev,wfO:()=>tp,wn6:()=>V,wrO:()=>tL,wtR:()=>o,xFO:()=>td,xSv:()=>G,xiE:()=>im,y3Z:()=>eo,y_p:()=>te,zD7:()=>hf,zdS:()=>tU,zgK:()=>sc,znC:()=>T});let n="176",a={LEFT:0,MIDDLE:1,RIGHT:2,ROTATE:0,DOLLY:1,PAN:2},o={ROTATE:0,PAN:1,DOLLY_PAN:2,DOLLY_ROTATE:3},h=0,l=1,u=2,c=0,d=1,p=2,m=3,y=0,f=1,g=2,x=0,v=1,b=2,M=3,w=4,S=5,_=100,A=101,z=102,T=103,I=104,C=200,B=201,k=202,O=203,E=204,R=205,P=206,N=207,V=208,L=209,F=210,j=211,U=212,D=213,W=214,H=0,J=1,q=2,G=3,X=4,Z=5,Y=6,Q=7,K=0,$=1,tt=2,te=0,ti=1,ts=2,tr=3,tn=4,ta=5,to=6,th=7,tl="attached",tu=300,tc=301,td=302,tp=303,tm=304,ty=306,tf=1e3,tg=1001,tx=1002,tv=1003,tb=1004,tM=1005,tw=1006,tS=1007,t_=1008,tA=1008,tz=1009,tT=1010,tI=1011,tC=1012,tB=1013,tk=1014,tO=1015,tE=1016,tR=1017,tP=1018,tN=1020,tV=35902,tL=1021,tF=1022,tj=1023,tU=1026,tD=1027,tW=1028,tH=1029,tJ=1030,tq=1031,tG=1032,tX=1033,tZ=33776,tY=33777,tQ=33778,tK=33779,t$=35840,t0=35841,t1=35842,t2=35843,t3=36196,t5=37492,t4=37496,t6=37808,t8=37809,t9=37810,t7=37811,et=37812,ee=37813,ei=37814,es=37815,er=37816,en=37817,ea=37818,eo=37819,eh=37820,el=37821,eu=36492,ec=36494,ed=36495,ep=36283,em=36284,ey=36285,ef=36286,eg=3201,ex=0,ev=1,eb="",eM="srgb",ew="srgb-linear",eS="linear",e_="srgb",eA=0,ez=7680,eT=7681,eI=7682,eC=7683,eB=34055,ek=34056,eO=5386,eE=512,eR=513,eP=514,eN=515,eV=516,eL=517,eF=518,ej=519,eU=512,eD=513,eW=514,eH=515,eJ=516,eq=517,eG=518,eX=519,eZ=35044,eY=35048,eQ="300 es",eK=2e3,e$=2001;class e0{addEventListener(t,e){void 0===this._listeners&&(this._listeners={});let i=this._listeners;void 0===i[t]&&(i[t]=[]),-1===i[t].indexOf(e)&&i[t].push(e)}hasEventListener(t,e){let i=this._listeners;return void 0!==i&&void 0!==i[t]&&-1!==i[t].indexOf(e)}removeEventListener(t,e){let i=this._listeners;if(void 0===i)return;let s=i[t];if(void 0!==s){let t=s.indexOf(e);-1!==t&&s.splice(t,1)}}dispatchEvent(t){let e=this._listeners;if(void 0===e)return;let i=e[t.type];if(void 0!==i){t.target=this;let e=i.slice(0);for(let i=0,s=e.length;i<s;i++)e[i].call(this,t);t.target=null}}}let e1=["00","01","02","03","04","05","06","07","08","09","0a","0b","0c","0d","0e","0f","10","11","12","13","14","15","16","17","18","19","1a","1b","1c","1d","1e","1f","20","21","22","23","24","25","26","27","28","29","2a","2b","2c","2d","2e","2f","30","31","32","33","34","35","36","37","38","39","3a","3b","3c","3d","3e","3f","40","41","42","43","44","45","46","47","48","49","4a","4b","4c","4d","4e","4f","50","51","52","53","54","55","56","57","58","59","5a","5b","5c","5d","5e","5f","60","61","62","63","64","65","66","67","68","69","6a","6b","6c","6d","6e","6f","70","71","72","73","74","75","76","77","78","79","7a","7b","7c","7d","7e","7f","80","81","82","83","84","85","86","87","88","89","8a","8b","8c","8d","8e","8f","90","91","92","93","94","95","96","97","98","99","9a","9b","9c","9d","9e","9f","a0","a1","a2","a3","a4","a5","a6","a7","a8","a9","aa","ab","ac","ad","ae","af","b0","b1","b2","b3","b4","b5","b6","b7","b8","b9","ba","bb","bc","bd","be","bf","c0","c1","c2","c3","c4","c5","c6","c7","c8","c9","ca","cb","cc","cd","ce","cf","d0","d1","d2","d3","d4","d5","d6","d7","d8","d9","da","db","dc","dd","de","df","e0","e1","e2","e3","e4","e5","e6","e7","e8","e9","ea","eb","ec","ed","ee","ef","f0","f1","f2","f3","f4","f5","f6","f7","f8","f9","fa","fb","fc","fd","fe","ff"],e2=1234567,e3=Math.PI/180,e5=180/Math.PI;function e4(){let t=0xffffffff*Math.random()|0,e=0xffffffff*Math.random()|0,i=0xffffffff*Math.random()|0,s=0xffffffff*Math.random()|0;return(e1[255&t]+e1[t>>8&255]+e1[t>>16&255]+e1[t>>24&255]+"-"+e1[255&e]+e1[e>>8&255]+"-"+e1[e>>16&15|64]+e1[e>>24&255]+"-"+e1[63&i|128]+e1[i>>8&255]+"-"+e1[i>>16&255]+e1[i>>24&255]+e1[255&s]+e1[s>>8&255]+e1[s>>16&255]+e1[s>>24&255]).toLowerCase()}function e6(t,e,i){return Math.max(e,Math.min(i,t))}function e8(t,e){return(t%e+e)%e}function e9(t,e,i){return(1-i)*t+i*e}function e7(t,e){switch(e.constructor){case Float32Array:return t;case Uint32Array:return t/0xffffffff;case Uint16Array:return t/65535;case Uint8Array:return t/255;case Int32Array:return Math.max(t/0x7fffffff,-1);case Int16Array:return Math.max(t/32767,-1);case Int8Array:return Math.max(t/127,-1);default:throw Error("Invalid component type.")}}function it(t,e){switch(e.constructor){case Float32Array:return t;case Uint32Array:return Math.round(0xffffffff*t);case Uint16Array:return Math.round(65535*t);case Uint8Array:return Math.round(255*t);case Int32Array:return Math.round(0x7fffffff*t);case Int16Array:return Math.round(32767*t);case Int8Array:return Math.round(127*t);default:throw Error("Invalid component type.")}}let ie={DEG2RAD:e3,RAD2DEG:e5,generateUUID:e4,clamp:e6,euclideanModulo:e8,mapLinear:function(t,e,i,s,r){return s+(t-e)*(r-s)/(i-e)},inverseLerp:function(t,e,i){return t!==e?(i-t)/(e-t):0},lerp:e9,damp:function(t,e,i,s){return e9(t,e,1-Math.exp(-i*s))},pingpong:function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return e-Math.abs(e8(t,2*e)-e)},smoothstep:function(t,e,i){return t<=e?0:t>=i?1:(t=(t-e)/(i-e))*t*(3-2*t)},smootherstep:function(t,e,i){return t<=e?0:t>=i?1:(t=(t-e)/(i-e))*t*t*(t*(6*t-15)+10)},randInt:function(t,e){return t+Math.floor(Math.random()*(e-t+1))},randFloat:function(t,e){return t+Math.random()*(e-t)},randFloatSpread:function(t){return t*(.5-Math.random())},seededRandom:function(t){void 0!==t&&(e2=t);let e=e2+=0x6d2b79f5;return e=Math.imul(e^e>>>15,1|e),(((e^=e+Math.imul(e^e>>>7,61|e))^e>>>14)>>>0)/0x100000000},degToRad:function(t){return t*e3},radToDeg:function(t){return t*e5},isPowerOfTwo:function(t){return(t&t-1)==0&&0!==t},ceilPowerOfTwo:function(t){return Math.pow(2,Math.ceil(Math.log(t)/Math.LN2))},floorPowerOfTwo:function(t){return Math.pow(2,Math.floor(Math.log(t)/Math.LN2))},setQuaternionFromProperEuler:function(t,e,i,s,r){let n=Math.cos,a=Math.sin,o=n(i/2),h=a(i/2),l=n((e+s)/2),u=a((e+s)/2),c=n((e-s)/2),d=a((e-s)/2),p=n((s-e)/2),m=a((s-e)/2);switch(r){case"XYX":t.set(o*u,h*c,h*d,o*l);break;case"YZY":t.set(h*d,o*u,h*c,o*l);break;case"ZXZ":t.set(h*c,h*d,o*u,o*l);break;case"XZX":t.set(o*u,h*m,h*p,o*l);break;case"YXY":t.set(h*p,o*u,h*m,o*l);break;case"ZYZ":t.set(h*m,h*p,o*u,o*l);break;default:console.warn("THREE.MathUtils: .setQuaternionFromProperEuler() encountered an unknown order: "+r)}},normalize:it,denormalize:e7};class ii{get width(){return this.x}set width(t){this.x=t}get height(){return this.y}set height(t){this.y=t}set(t,e){return this.x=t,this.y=e,this}setScalar(t){return this.x=t,this.y=t,this}setX(t){return this.x=t,this}setY(t){return this.y=t,this}setComponent(t,e){switch(t){case 0:this.x=e;break;case 1:this.y=e;break;default:throw Error("index is out of range: "+t)}return this}getComponent(t){switch(t){case 0:return this.x;case 1:return this.y;default:throw Error("index is out of range: "+t)}}clone(){return new this.constructor(this.x,this.y)}copy(t){return this.x=t.x,this.y=t.y,this}add(t){return this.x+=t.x,this.y+=t.y,this}addScalar(t){return this.x+=t,this.y+=t,this}addVectors(t,e){return this.x=t.x+e.x,this.y=t.y+e.y,this}addScaledVector(t,e){return this.x+=t.x*e,this.y+=t.y*e,this}sub(t){return this.x-=t.x,this.y-=t.y,this}subScalar(t){return this.x-=t,this.y-=t,this}subVectors(t,e){return this.x=t.x-e.x,this.y=t.y-e.y,this}multiply(t){return this.x*=t.x,this.y*=t.y,this}multiplyScalar(t){return this.x*=t,this.y*=t,this}divide(t){return this.x/=t.x,this.y/=t.y,this}divideScalar(t){return this.multiplyScalar(1/t)}applyMatrix3(t){let e=this.x,i=this.y,s=t.elements;return this.x=s[0]*e+s[3]*i+s[6],this.y=s[1]*e+s[4]*i+s[7],this}min(t){return this.x=Math.min(this.x,t.x),this.y=Math.min(this.y,t.y),this}max(t){return this.x=Math.max(this.x,t.x),this.y=Math.max(this.y,t.y),this}clamp(t,e){return this.x=e6(this.x,t.x,e.x),this.y=e6(this.y,t.y,e.y),this}clampScalar(t,e){return this.x=e6(this.x,t,e),this.y=e6(this.y,t,e),this}clampLength(t,e){let i=this.length();return this.divideScalar(i||1).multiplyScalar(e6(i,t,e))}floor(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this}ceil(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this}round(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this}roundToZero(){return this.x=Math.trunc(this.x),this.y=Math.trunc(this.y),this}negate(){return this.x=-this.x,this.y=-this.y,this}dot(t){return this.x*t.x+this.y*t.y}cross(t){return this.x*t.y-this.y*t.x}lengthSq(){return this.x*this.x+this.y*this.y}length(){return Math.sqrt(this.x*this.x+this.y*this.y)}manhattanLength(){return Math.abs(this.x)+Math.abs(this.y)}normalize(){return this.divideScalar(this.length()||1)}angle(){return Math.atan2(-this.y,-this.x)+Math.PI}angleTo(t){let e=Math.sqrt(this.lengthSq()*t.lengthSq());return 0===e?Math.PI/2:Math.acos(e6(this.dot(t)/e,-1,1))}distanceTo(t){return Math.sqrt(this.distanceToSquared(t))}distanceToSquared(t){let e=this.x-t.x,i=this.y-t.y;return e*e+i*i}manhattanDistanceTo(t){return Math.abs(this.x-t.x)+Math.abs(this.y-t.y)}setLength(t){return this.normalize().multiplyScalar(t)}lerp(t,e){return this.x+=(t.x-this.x)*e,this.y+=(t.y-this.y)*e,this}lerpVectors(t,e,i){return this.x=t.x+(e.x-t.x)*i,this.y=t.y+(e.y-t.y)*i,this}equals(t){return t.x===this.x&&t.y===this.y}fromArray(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return this.x=t[e],this.y=t[e+1],this}toArray(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return t[e]=this.x,t[e+1]=this.y,t}fromBufferAttribute(t,e){return this.x=t.getX(e),this.y=t.getY(e),this}rotateAround(t,e){let i=Math.cos(e),s=Math.sin(e),r=this.x-t.x,n=this.y-t.y;return this.x=r*i-n*s+t.x,this.y=r*s+n*i+t.y,this}random(){return this.x=Math.random(),this.y=Math.random(),this}*[Symbol.iterator](){yield this.x,yield this.y}constructor(t=0,e=0){ii.prototype.isVector2=!0,this.x=t,this.y=e}}class is{set(t,e,i,s,r,n,a,o,h){let l=this.elements;return l[0]=t,l[1]=s,l[2]=a,l[3]=e,l[4]=r,l[5]=o,l[6]=i,l[7]=n,l[8]=h,this}identity(){return this.set(1,0,0,0,1,0,0,0,1),this}copy(t){let e=this.elements,i=t.elements;return e[0]=i[0],e[1]=i[1],e[2]=i[2],e[3]=i[3],e[4]=i[4],e[5]=i[5],e[6]=i[6],e[7]=i[7],e[8]=i[8],this}extractBasis(t,e,i){return t.setFromMatrix3Column(this,0),e.setFromMatrix3Column(this,1),i.setFromMatrix3Column(this,2),this}setFromMatrix4(t){let e=t.elements;return this.set(e[0],e[4],e[8],e[1],e[5],e[9],e[2],e[6],e[10]),this}multiply(t){return this.multiplyMatrices(this,t)}premultiply(t){return this.multiplyMatrices(t,this)}multiplyMatrices(t,e){let i=t.elements,s=e.elements,r=this.elements,n=i[0],a=i[3],o=i[6],h=i[1],l=i[4],u=i[7],c=i[2],d=i[5],p=i[8],m=s[0],y=s[3],f=s[6],g=s[1],x=s[4],v=s[7],b=s[2],M=s[5],w=s[8];return r[0]=n*m+a*g+o*b,r[3]=n*y+a*x+o*M,r[6]=n*f+a*v+o*w,r[1]=h*m+l*g+u*b,r[4]=h*y+l*x+u*M,r[7]=h*f+l*v+u*w,r[2]=c*m+d*g+p*b,r[5]=c*y+d*x+p*M,r[8]=c*f+d*v+p*w,this}multiplyScalar(t){let e=this.elements;return e[0]*=t,e[3]*=t,e[6]*=t,e[1]*=t,e[4]*=t,e[7]*=t,e[2]*=t,e[5]*=t,e[8]*=t,this}determinant(){let t=this.elements,e=t[0],i=t[1],s=t[2],r=t[3],n=t[4],a=t[5],o=t[6],h=t[7],l=t[8];return e*n*l-e*a*h-i*r*l+i*a*o+s*r*h-s*n*o}invert(){let t=this.elements,e=t[0],i=t[1],s=t[2],r=t[3],n=t[4],a=t[5],o=t[6],h=t[7],l=t[8],u=l*n-a*h,c=a*o-l*r,d=h*r-n*o,p=e*u+i*c+s*d;if(0===p)return this.set(0,0,0,0,0,0,0,0,0);let m=1/p;return t[0]=u*m,t[1]=(s*h-l*i)*m,t[2]=(a*i-s*n)*m,t[3]=c*m,t[4]=(l*e-s*o)*m,t[5]=(s*r-a*e)*m,t[6]=d*m,t[7]=(i*o-h*e)*m,t[8]=(n*e-i*r)*m,this}transpose(){let t,e=this.elements;return t=e[1],e[1]=e[3],e[3]=t,t=e[2],e[2]=e[6],e[6]=t,t=e[5],e[5]=e[7],e[7]=t,this}getNormalMatrix(t){return this.setFromMatrix4(t).invert().transpose()}transposeIntoArray(t){let e=this.elements;return t[0]=e[0],t[1]=e[3],t[2]=e[6],t[3]=e[1],t[4]=e[4],t[5]=e[7],t[6]=e[2],t[7]=e[5],t[8]=e[8],this}setUvTransform(t,e,i,s,r,n,a){let o=Math.cos(r),h=Math.sin(r);return this.set(i*o,i*h,-i*(o*n+h*a)+n+t,-s*h,s*o,-s*(-h*n+o*a)+a+e,0,0,1),this}scale(t,e){return this.premultiply(ir.makeScale(t,e)),this}rotate(t){return this.premultiply(ir.makeRotation(-t)),this}translate(t,e){return this.premultiply(ir.makeTranslation(t,e)),this}makeTranslation(t,e){return t.isVector2?this.set(1,0,t.x,0,1,t.y,0,0,1):this.set(1,0,t,0,1,e,0,0,1),this}makeRotation(t){let e=Math.cos(t),i=Math.sin(t);return this.set(e,-i,0,i,e,0,0,0,1),this}makeScale(t,e){return this.set(t,0,0,0,e,0,0,0,1),this}equals(t){let e=this.elements,i=t.elements;for(let t=0;t<9;t++)if(e[t]!==i[t])return!1;return!0}fromArray(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;for(let i=0;i<9;i++)this.elements[i]=t[i+e];return this}toArray(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=this.elements;return t[e]=i[0],t[e+1]=i[1],t[e+2]=i[2],t[e+3]=i[3],t[e+4]=i[4],t[e+5]=i[5],t[e+6]=i[6],t[e+7]=i[7],t[e+8]=i[8],t}clone(){return new this.constructor().fromArray(this.elements)}constructor(t,e,i,s,r,n,a,o,h){is.prototype.isMatrix3=!0,this.elements=[1,0,0,0,1,0,0,0,1],void 0!==t&&this.set(t,e,i,s,r,n,a,o,h)}}let ir=new is;function ia(t){for(let e=t.length-1;e>=0;--e)if(t[e]>=65535)return!0;return!1}let io={Int8Array:Int8Array,Uint8Array:Uint8Array,Uint8ClampedArray:Uint8ClampedArray,Int16Array:Int16Array,Uint16Array:Uint16Array,Int32Array:Int32Array,Uint32Array:Uint32Array,Float32Array:Float32Array,Float64Array:Float64Array};function ih(t,e){return new io[t](e)}function il(t){return document.createElementNS("http://www.w3.org/1999/xhtml",t)}function iu(){let t=il("canvas");return t.style.display="block",t}let ic={};function id(t){t in ic||(ic[t]=!0,console.warn(t))}function ip(t,e,i){return new Promise(function(s,r){setTimeout(function n(){switch(t.clientWaitSync(e,t.SYNC_FLUSH_COMMANDS_BIT,0)){case t.WAIT_FAILED:r();break;case t.TIMEOUT_EXPIRED:setTimeout(n,i);break;default:s()}},i)})}function im(t){let e=t.elements;e[2]=.5*e[2]+.5*e[3],e[6]=.5*e[6]+.5*e[7],e[10]=.5*e[10]+.5*e[11],e[14]=.5*e[14]+.5*e[15]}function iy(t){let e=t.elements;-1===e[11]?(e[10]=-e[10]-1,e[14]=-e[14]):(e[10]=-e[10],e[14]=-e[14]+1)}let ig=new is().set(.4123908,.3575843,.1804808,.212639,.7151687,.0721923,.0193308,.1191948,.9505322),ix=new is().set(3.2409699,-1.5373832,-.4986108,-.9692436,1.8759675,.0415551,.0556301,-.203977,1.0569715),iv=function(){let t={enabled:!0,workingColorSpace:ew,spaces:{},convert:function(t,e,i){return!1!==this.enabled&&e!==i&&e&&i&&(this.spaces[e].transfer===e_&&(t.r=ib(t.r),t.g=ib(t.g),t.b=ib(t.b)),this.spaces[e].primaries!==this.spaces[i].primaries&&(t.applyMatrix3(this.spaces[e].toXYZ),t.applyMatrix3(this.spaces[i].fromXYZ)),this.spaces[i].transfer===e_&&(t.r=iM(t.r),t.g=iM(t.g),t.b=iM(t.b))),t},fromWorkingColorSpace:function(t,e){return this.convert(t,this.workingColorSpace,e)},toWorkingColorSpace:function(t,e){return this.convert(t,e,this.workingColorSpace)},getPrimaries:function(t){return this.spaces[t].primaries},getTransfer:function(t){return t===eb?eS:this.spaces[t].transfer},getLuminanceCoefficients:function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.workingColorSpace;return t.fromArray(this.spaces[e].luminanceCoefficients)},define:function(t){Object.assign(this.spaces,t)},_getMatrix:function(t,e,i){return t.copy(this.spaces[e].toXYZ).multiply(this.spaces[i].fromXYZ)},_getDrawingBufferColorSpace:function(t){return this.spaces[t].outputColorSpaceConfig.drawingBufferColorSpace},_getUnpackColorSpace:function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.workingColorSpace;return this.spaces[t].workingColorSpaceConfig.unpackColorSpace}},e=[.64,.33,.3,.6,.15,.06],i=[.2126,.7152,.0722],s=[.3127,.329];return t.define({[ew]:{primaries:e,whitePoint:s,transfer:eS,toXYZ:ig,fromXYZ:ix,luminanceCoefficients:i,workingColorSpaceConfig:{unpackColorSpace:eM},outputColorSpaceConfig:{drawingBufferColorSpace:eM}},[eM]:{primaries:e,whitePoint:s,transfer:e_,toXYZ:ig,fromXYZ:ix,luminanceCoefficients:i,outputColorSpaceConfig:{drawingBufferColorSpace:eM}}}),t}();function ib(t){return t<.04045?.0773993808*t:Math.pow(.9478672986*t+.0521327014,2.4)}function iM(t){return t<.0031308?12.92*t:1.055*Math.pow(t,.41666)-.055}class iw{static getDataURL(t){let e,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"image/png";if(/^data:/i.test(t.src)||"undefined"==typeof HTMLCanvasElement)return t.src;if(t instanceof HTMLCanvasElement)e=t;else{void 0===s&&(s=il("canvas")),s.width=t.width,s.height=t.height;let i=s.getContext("2d");t instanceof ImageData?i.putImageData(t,0,0):i.drawImage(t,0,0,t.width,t.height),e=s}return e.toDataURL(i)}static sRGBToLinear(t){if("undefined"!=typeof HTMLImageElement&&t instanceof HTMLImageElement||"undefined"!=typeof HTMLCanvasElement&&t instanceof HTMLCanvasElement||"undefined"!=typeof ImageBitmap&&t instanceof ImageBitmap){let e=il("canvas");e.width=t.width,e.height=t.height;let i=e.getContext("2d");i.drawImage(t,0,0,t.width,t.height);let s=i.getImageData(0,0,t.width,t.height),r=s.data;for(let t=0;t<r.length;t++)r[t]=255*ib(r[t]/255);return i.putImageData(s,0,0),e}if(!t.data)return console.warn("THREE.ImageUtils.sRGBToLinear(): Unsupported image type. No color space conversion applied."),t;{let e=t.data.slice(0);for(let t=0;t<e.length;t++)e instanceof Uint8Array||e instanceof Uint8ClampedArray?e[t]=Math.floor(255*ib(e[t]/255)):e[t]=ib(e[t]);return{data:e,width:t.width,height:t.height}}}}let iS=0;class i_{set needsUpdate(t){!0===t&&this.version++}toJSON(t){let e=void 0===t||"string"==typeof t;if(!e&&void 0!==t.images[this.uuid])return t.images[this.uuid];let i={uuid:this.uuid,url:""},s=this.data;if(null!==s){let t;if(Array.isArray(s)){t=[];for(let e=0,i=s.length;e<i;e++)s[e].isDataTexture?t.push(iA(s[e].image)):t.push(iA(s[e]))}else t=iA(s);i.url=t}return e||(t.images[this.uuid]=i),i}constructor(t=null){this.isSource=!0,Object.defineProperty(this,"id",{value:iS++}),this.uuid=e4(),this.data=t,this.dataReady=!0,this.version=0}}function iA(t){return"undefined"!=typeof HTMLImageElement&&t instanceof HTMLImageElement||"undefined"!=typeof HTMLCanvasElement&&t instanceof HTMLCanvasElement||"undefined"!=typeof ImageBitmap&&t instanceof ImageBitmap?iw.getDataURL(t):t.data?{data:Array.from(t.data),width:t.width,height:t.height,type:t.data.constructor.name}:(console.warn("THREE.Texture: Unable to serialize Texture."),{})}let iz=0;class iT extends e0{get image(){return this.source.data}set image(t){void 0===t&&(t=null),this.source.data=t}updateMatrix(){this.matrix.setUvTransform(this.offset.x,this.offset.y,this.repeat.x,this.repeat.y,this.rotation,this.center.x,this.center.y)}clone(){return new this.constructor().copy(this)}copy(t){return this.name=t.name,this.source=t.source,this.mipmaps=t.mipmaps.slice(0),this.mapping=t.mapping,this.channel=t.channel,this.wrapS=t.wrapS,this.wrapT=t.wrapT,this.magFilter=t.magFilter,this.minFilter=t.minFilter,this.anisotropy=t.anisotropy,this.format=t.format,this.internalFormat=t.internalFormat,this.type=t.type,this.offset.copy(t.offset),this.repeat.copy(t.repeat),this.center.copy(t.center),this.rotation=t.rotation,this.matrixAutoUpdate=t.matrixAutoUpdate,this.matrix.copy(t.matrix),this.generateMipmaps=t.generateMipmaps,this.premultiplyAlpha=t.premultiplyAlpha,this.flipY=t.flipY,this.unpackAlignment=t.unpackAlignment,this.colorSpace=t.colorSpace,this.renderTarget=t.renderTarget,this.isRenderTargetTexture=t.isRenderTargetTexture,this.isTextureArray=t.isTextureArray,this.userData=JSON.parse(JSON.stringify(t.userData)),this.needsUpdate=!0,this}toJSON(t){let e=void 0===t||"string"==typeof t;if(!e&&void 0!==t.textures[this.uuid])return t.textures[this.uuid];let i={metadata:{version:4.6,type:"Texture",generator:"Texture.toJSON"},uuid:this.uuid,name:this.name,image:this.source.toJSON(t).uuid,mapping:this.mapping,channel:this.channel,repeat:[this.repeat.x,this.repeat.y],offset:[this.offset.x,this.offset.y],center:[this.center.x,this.center.y],rotation:this.rotation,wrap:[this.wrapS,this.wrapT],format:this.format,internalFormat:this.internalFormat,type:this.type,colorSpace:this.colorSpace,minFilter:this.minFilter,magFilter:this.magFilter,anisotropy:this.anisotropy,flipY:this.flipY,generateMipmaps:this.generateMipmaps,premultiplyAlpha:this.premultiplyAlpha,unpackAlignment:this.unpackAlignment};return Object.keys(this.userData).length>0&&(i.userData=this.userData),e||(t.textures[this.uuid]=i),i}dispose(){this.dispatchEvent({type:"dispose"})}transformUv(t){if(this.mapping!==tu)return t;if(t.applyMatrix3(this.matrix),t.x<0||t.x>1)switch(this.wrapS){case tf:t.x=t.x-Math.floor(t.x);break;case tg:t.x=t.x<0?0:1;break;case tx:1===Math.abs(Math.floor(t.x)%2)?t.x=Math.ceil(t.x)-t.x:t.x=t.x-Math.floor(t.x)}if(t.y<0||t.y>1)switch(this.wrapT){case tf:t.y=t.y-Math.floor(t.y);break;case tg:t.y=t.y<0?0:1;break;case tx:1===Math.abs(Math.floor(t.y)%2)?t.y=Math.ceil(t.y)-t.y:t.y=t.y-Math.floor(t.y)}return this.flipY&&(t.y=1-t.y),t}set needsUpdate(t){!0===t&&(this.version++,this.source.needsUpdate=!0)}set needsPMREMUpdate(t){!0===t&&this.pmremVersion++}constructor(t=iT.DEFAULT_IMAGE,e=iT.DEFAULT_MAPPING,i=tg,s=tg,r=tw,n=t_,a=tj,o=tz,h=iT.DEFAULT_ANISOTROPY,l=eb){super(),this.isTexture=!0,Object.defineProperty(this,"id",{value:iz++}),this.uuid=e4(),this.name="",this.source=new i_(t),this.mipmaps=[],this.mapping=e,this.channel=0,this.wrapS=i,this.wrapT=s,this.magFilter=r,this.minFilter=n,this.anisotropy=h,this.format=a,this.internalFormat=null,this.type=o,this.offset=new ii(0,0),this.repeat=new ii(1,1),this.center=new ii(0,0),this.rotation=0,this.matrixAutoUpdate=!0,this.matrix=new is,this.generateMipmaps=!0,this.premultiplyAlpha=!1,this.flipY=!0,this.unpackAlignment=4,this.colorSpace=l,this.userData={},this.version=0,this.onUpdate=null,this.renderTarget=null,this.isRenderTargetTexture=!1,this.isTextureArray=!1,this.pmremVersion=0}}iT.DEFAULT_IMAGE=null,iT.DEFAULT_MAPPING=tu,iT.DEFAULT_ANISOTROPY=1;class iI{get width(){return this.z}set width(t){this.z=t}get height(){return this.w}set height(t){this.w=t}set(t,e,i,s){return this.x=t,this.y=e,this.z=i,this.w=s,this}setScalar(t){return this.x=t,this.y=t,this.z=t,this.w=t,this}setX(t){return this.x=t,this}setY(t){return this.y=t,this}setZ(t){return this.z=t,this}setW(t){return this.w=t,this}setComponent(t,e){switch(t){case 0:this.x=e;break;case 1:this.y=e;break;case 2:this.z=e;break;case 3:this.w=e;break;default:throw Error("index is out of range: "+t)}return this}getComponent(t){switch(t){case 0:return this.x;case 1:return this.y;case 2:return this.z;case 3:return this.w;default:throw Error("index is out of range: "+t)}}clone(){return new this.constructor(this.x,this.y,this.z,this.w)}copy(t){return this.x=t.x,this.y=t.y,this.z=t.z,this.w=void 0!==t.w?t.w:1,this}add(t){return this.x+=t.x,this.y+=t.y,this.z+=t.z,this.w+=t.w,this}addScalar(t){return this.x+=t,this.y+=t,this.z+=t,this.w+=t,this}addVectors(t,e){return this.x=t.x+e.x,this.y=t.y+e.y,this.z=t.z+e.z,this.w=t.w+e.w,this}addScaledVector(t,e){return this.x+=t.x*e,this.y+=t.y*e,this.z+=t.z*e,this.w+=t.w*e,this}sub(t){return this.x-=t.x,this.y-=t.y,this.z-=t.z,this.w-=t.w,this}subScalar(t){return this.x-=t,this.y-=t,this.z-=t,this.w-=t,this}subVectors(t,e){return this.x=t.x-e.x,this.y=t.y-e.y,this.z=t.z-e.z,this.w=t.w-e.w,this}multiply(t){return this.x*=t.x,this.y*=t.y,this.z*=t.z,this.w*=t.w,this}multiplyScalar(t){return this.x*=t,this.y*=t,this.z*=t,this.w*=t,this}applyMatrix4(t){let e=this.x,i=this.y,s=this.z,r=this.w,n=t.elements;return this.x=n[0]*e+n[4]*i+n[8]*s+n[12]*r,this.y=n[1]*e+n[5]*i+n[9]*s+n[13]*r,this.z=n[2]*e+n[6]*i+n[10]*s+n[14]*r,this.w=n[3]*e+n[7]*i+n[11]*s+n[15]*r,this}divide(t){return this.x/=t.x,this.y/=t.y,this.z/=t.z,this.w/=t.w,this}divideScalar(t){return this.multiplyScalar(1/t)}setAxisAngleFromQuaternion(t){this.w=2*Math.acos(t.w);let e=Math.sqrt(1-t.w*t.w);return e<1e-4?(this.x=1,this.y=0,this.z=0):(this.x=t.x/e,this.y=t.y/e,this.z=t.z/e),this}setAxisAngleFromRotationMatrix(t){let e,i,s,r,n=t.elements,a=n[0],o=n[4],h=n[8],l=n[1],u=n[5],c=n[9],d=n[2],p=n[6],m=n[10];if(.01>Math.abs(o-l)&&.01>Math.abs(h-d)&&.01>Math.abs(c-p)){if(.1>Math.abs(o+l)&&.1>Math.abs(h+d)&&.1>Math.abs(c+p)&&.1>Math.abs(a+u+m-3))return this.set(1,0,0,0),this;e=Math.PI;let t=(a+1)/2,n=(u+1)/2,y=(m+1)/2,f=(o+l)/4,g=(h+d)/4,x=(c+p)/4;return t>n&&t>y?t<.01?(i=0,s=.*********,r=.*********):(s=f/(i=Math.sqrt(t)),r=g/i):n>y?n<.01?(i=.*********,s=0,r=.*********):(i=f/(s=Math.sqrt(n)),r=x/s):y<.01?(i=.*********,s=.*********,r=0):(i=g/(r=Math.sqrt(y)),s=x/r),this.set(i,s,r,e),this}let y=Math.sqrt((p-c)*(p-c)+(h-d)*(h-d)+(l-o)*(l-o));return .001>Math.abs(y)&&(y=1),this.x=(p-c)/y,this.y=(h-d)/y,this.z=(l-o)/y,this.w=Math.acos((a+u+m-1)/2),this}setFromMatrixPosition(t){let e=t.elements;return this.x=e[12],this.y=e[13],this.z=e[14],this.w=e[15],this}min(t){return this.x=Math.min(this.x,t.x),this.y=Math.min(this.y,t.y),this.z=Math.min(this.z,t.z),this.w=Math.min(this.w,t.w),this}max(t){return this.x=Math.max(this.x,t.x),this.y=Math.max(this.y,t.y),this.z=Math.max(this.z,t.z),this.w=Math.max(this.w,t.w),this}clamp(t,e){return this.x=e6(this.x,t.x,e.x),this.y=e6(this.y,t.y,e.y),this.z=e6(this.z,t.z,e.z),this.w=e6(this.w,t.w,e.w),this}clampScalar(t,e){return this.x=e6(this.x,t,e),this.y=e6(this.y,t,e),this.z=e6(this.z,t,e),this.w=e6(this.w,t,e),this}clampLength(t,e){let i=this.length();return this.divideScalar(i||1).multiplyScalar(e6(i,t,e))}floor(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this.z=Math.floor(this.z),this.w=Math.floor(this.w),this}ceil(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this.z=Math.ceil(this.z),this.w=Math.ceil(this.w),this}round(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this.z=Math.round(this.z),this.w=Math.round(this.w),this}roundToZero(){return this.x=Math.trunc(this.x),this.y=Math.trunc(this.y),this.z=Math.trunc(this.z),this.w=Math.trunc(this.w),this}negate(){return this.x=-this.x,this.y=-this.y,this.z=-this.z,this.w=-this.w,this}dot(t){return this.x*t.x+this.y*t.y+this.z*t.z+this.w*t.w}lengthSq(){return this.x*this.x+this.y*this.y+this.z*this.z+this.w*this.w}length(){return Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z+this.w*this.w)}manhattanLength(){return Math.abs(this.x)+Math.abs(this.y)+Math.abs(this.z)+Math.abs(this.w)}normalize(){return this.divideScalar(this.length()||1)}setLength(t){return this.normalize().multiplyScalar(t)}lerp(t,e){return this.x+=(t.x-this.x)*e,this.y+=(t.y-this.y)*e,this.z+=(t.z-this.z)*e,this.w+=(t.w-this.w)*e,this}lerpVectors(t,e,i){return this.x=t.x+(e.x-t.x)*i,this.y=t.y+(e.y-t.y)*i,this.z=t.z+(e.z-t.z)*i,this.w=t.w+(e.w-t.w)*i,this}equals(t){return t.x===this.x&&t.y===this.y&&t.z===this.z&&t.w===this.w}fromArray(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return this.x=t[e],this.y=t[e+1],this.z=t[e+2],this.w=t[e+3],this}toArray(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return t[e]=this.x,t[e+1]=this.y,t[e+2]=this.z,t[e+3]=this.w,t}fromBufferAttribute(t,e){return this.x=t.getX(e),this.y=t.getY(e),this.z=t.getZ(e),this.w=t.getW(e),this}random(){return this.x=Math.random(),this.y=Math.random(),this.z=Math.random(),this.w=Math.random(),this}*[Symbol.iterator](){yield this.x,yield this.y,yield this.z,yield this.w}constructor(t=0,e=0,i=0,s=1){iI.prototype.isVector4=!0,this.x=t,this.y=e,this.z=i,this.w=s}}class iC extends e0{get texture(){return this.textures[0]}set texture(t){this.textures[0]=t}set depthTexture(t){null!==this._depthTexture&&(this._depthTexture.renderTarget=null),null!==t&&(t.renderTarget=this),this._depthTexture=t}get depthTexture(){return this._depthTexture}setSize(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;if(this.width!==t||this.height!==e||this.depth!==i){this.width=t,this.height=e,this.depth=i;for(let s=0,r=this.textures.length;s<r;s++)this.textures[s].image.width=t,this.textures[s].image.height=e,this.textures[s].image.depth=i;this.dispose()}this.viewport.set(0,0,t,e),this.scissor.set(0,0,t,e)}clone(){return new this.constructor().copy(this)}copy(t){this.width=t.width,this.height=t.height,this.depth=t.depth,this.scissor.copy(t.scissor),this.scissorTest=t.scissorTest,this.viewport.copy(t.viewport),this.textures.length=0;for(let e=0,i=t.textures.length;e<i;e++){this.textures[e]=t.textures[e].clone(),this.textures[e].isRenderTargetTexture=!0,this.textures[e].renderTarget=this;let i=Object.assign({},t.textures[e].image);this.textures[e].source=new i_(i)}return this.depthBuffer=t.depthBuffer,this.stencilBuffer=t.stencilBuffer,this.resolveDepthBuffer=t.resolveDepthBuffer,this.resolveStencilBuffer=t.resolveStencilBuffer,null!==t.depthTexture&&(this.depthTexture=t.depthTexture.clone()),this.samples=t.samples,this}dispose(){this.dispatchEvent({type:"dispose"})}constructor(t=1,e=1,i={}){super(),this.isRenderTarget=!0,this.width=t,this.height=e,this.depth=i.depth?i.depth:1,this.scissor=new iI(0,0,t,e),this.scissorTest=!1,this.viewport=new iI(0,0,t,e);let s=new iT({width:t,height:e,depth:this.depth},(i=Object.assign({generateMipmaps:!1,internalFormat:null,minFilter:tw,depthBuffer:!0,stencilBuffer:!1,resolveDepthBuffer:!0,resolveStencilBuffer:!0,depthTexture:null,samples:0,count:1,multiview:!1},i)).mapping,i.wrapS,i.wrapT,i.magFilter,i.minFilter,i.format,i.type,i.anisotropy,i.colorSpace);s.flipY=!1,s.generateMipmaps=i.generateMipmaps,s.internalFormat=i.internalFormat,this.textures=[];let r=i.count;for(let t=0;t<r;t++)this.textures[t]=s.clone(),this.textures[t].isRenderTargetTexture=!0,this.textures[t].renderTarget=this;this.depthBuffer=i.depthBuffer,this.stencilBuffer=i.stencilBuffer,this.resolveDepthBuffer=i.resolveDepthBuffer,this.resolveStencilBuffer=i.resolveStencilBuffer,this._depthTexture=null,this.depthTexture=i.depthTexture,this.samples=i.samples,this.multiview=i.multiview}}class iB extends iC{constructor(t=1,e=1,i={}){super(t,e,i),this.isWebGLRenderTarget=!0}}class ik extends iT{addLayerUpdate(t){this.layerUpdates.add(t)}clearLayerUpdates(){this.layerUpdates.clear()}constructor(t=null,e=1,i=1,s=1){super(null),this.isDataArrayTexture=!0,this.image={data:t,width:e,height:i,depth:s},this.magFilter=tv,this.minFilter=tv,this.wrapR=tg,this.generateMipmaps=!1,this.flipY=!1,this.unpackAlignment=1,this.layerUpdates=new Set}}class iO extends iT{constructor(t=null,e=1,i=1,s=1){super(null),this.isData3DTexture=!0,this.image={data:t,width:e,height:i,depth:s},this.magFilter=tv,this.minFilter=tv,this.wrapR=tg,this.generateMipmaps=!1,this.flipY=!1,this.unpackAlignment=1}}class iE{static slerpFlat(t,e,i,s,r,n,a){let o=i[s+0],h=i[s+1],l=i[s+2],u=i[s+3],c=r[n+0],d=r[n+1],p=r[n+2],m=r[n+3];if(0===a){t[e+0]=o,t[e+1]=h,t[e+2]=l,t[e+3]=u;return}if(1===a){t[e+0]=c,t[e+1]=d,t[e+2]=p,t[e+3]=m;return}if(u!==m||o!==c||h!==d||l!==p){let t=1-a,e=o*c+h*d+l*p+u*m,i=e>=0?1:-1,s=1-e*e;if(s>Number.EPSILON){let r=Math.sqrt(s),n=Math.atan2(r,e*i);t=Math.sin(t*n)/r,a=Math.sin(a*n)/r}let r=a*i;if(o=o*t+c*r,h=h*t+d*r,l=l*t+p*r,u=u*t+m*r,t===1-a){let t=1/Math.sqrt(o*o+h*h+l*l+u*u);o*=t,h*=t,l*=t,u*=t}}t[e]=o,t[e+1]=h,t[e+2]=l,t[e+3]=u}static multiplyQuaternionsFlat(t,e,i,s,r,n){let a=i[s],o=i[s+1],h=i[s+2],l=i[s+3],u=r[n],c=r[n+1],d=r[n+2],p=r[n+3];return t[e]=a*p+l*u+o*d-h*c,t[e+1]=o*p+l*c+h*u-a*d,t[e+2]=h*p+l*d+a*c-o*u,t[e+3]=l*p-a*u-o*c-h*d,t}get x(){return this._x}set x(t){this._x=t,this._onChangeCallback()}get y(){return this._y}set y(t){this._y=t,this._onChangeCallback()}get z(){return this._z}set z(t){this._z=t,this._onChangeCallback()}get w(){return this._w}set w(t){this._w=t,this._onChangeCallback()}set(t,e,i,s){return this._x=t,this._y=e,this._z=i,this._w=s,this._onChangeCallback(),this}clone(){return new this.constructor(this._x,this._y,this._z,this._w)}copy(t){return this._x=t.x,this._y=t.y,this._z=t.z,this._w=t.w,this._onChangeCallback(),this}setFromEuler(t){let e=!(arguments.length>1)||void 0===arguments[1]||arguments[1],i=t._x,s=t._y,r=t._z,n=t._order,a=Math.cos,o=Math.sin,h=a(i/2),l=a(s/2),u=a(r/2),c=o(i/2),d=o(s/2),p=o(r/2);switch(n){case"XYZ":this._x=c*l*u+h*d*p,this._y=h*d*u-c*l*p,this._z=h*l*p+c*d*u,this._w=h*l*u-c*d*p;break;case"YXZ":this._x=c*l*u+h*d*p,this._y=h*d*u-c*l*p,this._z=h*l*p-c*d*u,this._w=h*l*u+c*d*p;break;case"ZXY":this._x=c*l*u-h*d*p,this._y=h*d*u+c*l*p,this._z=h*l*p+c*d*u,this._w=h*l*u-c*d*p;break;case"ZYX":this._x=c*l*u-h*d*p,this._y=h*d*u+c*l*p,this._z=h*l*p-c*d*u,this._w=h*l*u+c*d*p;break;case"YZX":this._x=c*l*u+h*d*p,this._y=h*d*u+c*l*p,this._z=h*l*p-c*d*u,this._w=h*l*u-c*d*p;break;case"XZY":this._x=c*l*u-h*d*p,this._y=h*d*u-c*l*p,this._z=h*l*p+c*d*u,this._w=h*l*u+c*d*p;break;default:console.warn("THREE.Quaternion: .setFromEuler() encountered an unknown order: "+n)}return!0===e&&this._onChangeCallback(),this}setFromAxisAngle(t,e){let i=e/2,s=Math.sin(i);return this._x=t.x*s,this._y=t.y*s,this._z=t.z*s,this._w=Math.cos(i),this._onChangeCallback(),this}setFromRotationMatrix(t){let e=t.elements,i=e[0],s=e[4],r=e[8],n=e[1],a=e[5],o=e[9],h=e[2],l=e[6],u=e[10],c=i+a+u;if(c>0){let t=.5/Math.sqrt(c+1);this._w=.25/t,this._x=(l-o)*t,this._y=(r-h)*t,this._z=(n-s)*t}else if(i>a&&i>u){let t=2*Math.sqrt(1+i-a-u);this._w=(l-o)/t,this._x=.25*t,this._y=(s+n)/t,this._z=(r+h)/t}else if(a>u){let t=2*Math.sqrt(1+a-i-u);this._w=(r-h)/t,this._x=(s+n)/t,this._y=.25*t,this._z=(o+l)/t}else{let t=2*Math.sqrt(1+u-i-a);this._w=(n-s)/t,this._x=(r+h)/t,this._y=(o+l)/t,this._z=.25*t}return this._onChangeCallback(),this}setFromUnitVectors(t,e){let i=t.dot(e)+1;return i<Number.EPSILON?(i=0,Math.abs(t.x)>Math.abs(t.z)?(this._x=-t.y,this._y=t.x,this._z=0):(this._x=0,this._y=-t.z,this._z=t.y)):(this._x=t.y*e.z-t.z*e.y,this._y=t.z*e.x-t.x*e.z,this._z=t.x*e.y-t.y*e.x),this._w=i,this.normalize()}angleTo(t){return 2*Math.acos(Math.abs(e6(this.dot(t),-1,1)))}rotateTowards(t,e){let i=this.angleTo(t);if(0===i)return this;let s=Math.min(1,e/i);return this.slerp(t,s),this}identity(){return this.set(0,0,0,1)}invert(){return this.conjugate()}conjugate(){return this._x*=-1,this._y*=-1,this._z*=-1,this._onChangeCallback(),this}dot(t){return this._x*t._x+this._y*t._y+this._z*t._z+this._w*t._w}lengthSq(){return this._x*this._x+this._y*this._y+this._z*this._z+this._w*this._w}length(){return Math.sqrt(this._x*this._x+this._y*this._y+this._z*this._z+this._w*this._w)}normalize(){let t=this.length();return 0===t?(this._x=0,this._y=0,this._z=0,this._w=1):(t=1/t,this._x=this._x*t,this._y=this._y*t,this._z=this._z*t,this._w=this._w*t),this._onChangeCallback(),this}multiply(t){return this.multiplyQuaternions(this,t)}premultiply(t){return this.multiplyQuaternions(t,this)}multiplyQuaternions(t,e){let i=t._x,s=t._y,r=t._z,n=t._w,a=e._x,o=e._y,h=e._z,l=e._w;return this._x=i*l+n*a+s*h-r*o,this._y=s*l+n*o+r*a-i*h,this._z=r*l+n*h+i*o-s*a,this._w=n*l-i*a-s*o-r*h,this._onChangeCallback(),this}slerp(t,e){if(0===e)return this;if(1===e)return this.copy(t);let i=this._x,s=this._y,r=this._z,n=this._w,a=n*t._w+i*t._x+s*t._y+r*t._z;if(a<0?(this._w=-t._w,this._x=-t._x,this._y=-t._y,this._z=-t._z,a=-a):this.copy(t),a>=1)return this._w=n,this._x=i,this._y=s,this._z=r,this;let o=1-a*a;if(o<=Number.EPSILON){let t=1-e;return this._w=t*n+e*this._w,this._x=t*i+e*this._x,this._y=t*s+e*this._y,this._z=t*r+e*this._z,this.normalize(),this}let h=Math.sqrt(o),l=Math.atan2(h,a),u=Math.sin((1-e)*l)/h,c=Math.sin(e*l)/h;return this._w=n*u+this._w*c,this._x=i*u+this._x*c,this._y=s*u+this._y*c,this._z=r*u+this._z*c,this._onChangeCallback(),this}slerpQuaternions(t,e,i){return this.copy(t).slerp(e,i)}random(){let t=2*Math.PI*Math.random(),e=2*Math.PI*Math.random(),i=Math.random(),s=Math.sqrt(1-i),r=Math.sqrt(i);return this.set(s*Math.sin(t),s*Math.cos(t),r*Math.sin(e),r*Math.cos(e))}equals(t){return t._x===this._x&&t._y===this._y&&t._z===this._z&&t._w===this._w}fromArray(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return this._x=t[e],this._y=t[e+1],this._z=t[e+2],this._w=t[e+3],this._onChangeCallback(),this}toArray(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return t[e]=this._x,t[e+1]=this._y,t[e+2]=this._z,t[e+3]=this._w,t}fromBufferAttribute(t,e){return this._x=t.getX(e),this._y=t.getY(e),this._z=t.getZ(e),this._w=t.getW(e),this._onChangeCallback(),this}toJSON(){return this.toArray()}_onChange(t){return this._onChangeCallback=t,this}_onChangeCallback(){}*[Symbol.iterator](){yield this._x,yield this._y,yield this._z,yield this._w}constructor(t=0,e=0,i=0,s=1){this.isQuaternion=!0,this._x=t,this._y=e,this._z=i,this._w=s}}class iR{set(t,e,i){return void 0===i&&(i=this.z),this.x=t,this.y=e,this.z=i,this}setScalar(t){return this.x=t,this.y=t,this.z=t,this}setX(t){return this.x=t,this}setY(t){return this.y=t,this}setZ(t){return this.z=t,this}setComponent(t,e){switch(t){case 0:this.x=e;break;case 1:this.y=e;break;case 2:this.z=e;break;default:throw Error("index is out of range: "+t)}return this}getComponent(t){switch(t){case 0:return this.x;case 1:return this.y;case 2:return this.z;default:throw Error("index is out of range: "+t)}}clone(){return new this.constructor(this.x,this.y,this.z)}copy(t){return this.x=t.x,this.y=t.y,this.z=t.z,this}add(t){return this.x+=t.x,this.y+=t.y,this.z+=t.z,this}addScalar(t){return this.x+=t,this.y+=t,this.z+=t,this}addVectors(t,e){return this.x=t.x+e.x,this.y=t.y+e.y,this.z=t.z+e.z,this}addScaledVector(t,e){return this.x+=t.x*e,this.y+=t.y*e,this.z+=t.z*e,this}sub(t){return this.x-=t.x,this.y-=t.y,this.z-=t.z,this}subScalar(t){return this.x-=t,this.y-=t,this.z-=t,this}subVectors(t,e){return this.x=t.x-e.x,this.y=t.y-e.y,this.z=t.z-e.z,this}multiply(t){return this.x*=t.x,this.y*=t.y,this.z*=t.z,this}multiplyScalar(t){return this.x*=t,this.y*=t,this.z*=t,this}multiplyVectors(t,e){return this.x=t.x*e.x,this.y=t.y*e.y,this.z=t.z*e.z,this}applyEuler(t){return this.applyQuaternion(iN.setFromEuler(t))}applyAxisAngle(t,e){return this.applyQuaternion(iN.setFromAxisAngle(t,e))}applyMatrix3(t){let e=this.x,i=this.y,s=this.z,r=t.elements;return this.x=r[0]*e+r[3]*i+r[6]*s,this.y=r[1]*e+r[4]*i+r[7]*s,this.z=r[2]*e+r[5]*i+r[8]*s,this}applyNormalMatrix(t){return this.applyMatrix3(t).normalize()}applyMatrix4(t){let e=this.x,i=this.y,s=this.z,r=t.elements,n=1/(r[3]*e+r[7]*i+r[11]*s+r[15]);return this.x=(r[0]*e+r[4]*i+r[8]*s+r[12])*n,this.y=(r[1]*e+r[5]*i+r[9]*s+r[13])*n,this.z=(r[2]*e+r[6]*i+r[10]*s+r[14])*n,this}applyQuaternion(t){let e=this.x,i=this.y,s=this.z,r=t.x,n=t.y,a=t.z,o=t.w,h=2*(n*s-a*i),l=2*(a*e-r*s),u=2*(r*i-n*e);return this.x=e+o*h+n*u-a*l,this.y=i+o*l+a*h-r*u,this.z=s+o*u+r*l-n*h,this}project(t){return this.applyMatrix4(t.matrixWorldInverse).applyMatrix4(t.projectionMatrix)}unproject(t){return this.applyMatrix4(t.projectionMatrixInverse).applyMatrix4(t.matrixWorld)}transformDirection(t){let e=this.x,i=this.y,s=this.z,r=t.elements;return this.x=r[0]*e+r[4]*i+r[8]*s,this.y=r[1]*e+r[5]*i+r[9]*s,this.z=r[2]*e+r[6]*i+r[10]*s,this.normalize()}divide(t){return this.x/=t.x,this.y/=t.y,this.z/=t.z,this}divideScalar(t){return this.multiplyScalar(1/t)}min(t){return this.x=Math.min(this.x,t.x),this.y=Math.min(this.y,t.y),this.z=Math.min(this.z,t.z),this}max(t){return this.x=Math.max(this.x,t.x),this.y=Math.max(this.y,t.y),this.z=Math.max(this.z,t.z),this}clamp(t,e){return this.x=e6(this.x,t.x,e.x),this.y=e6(this.y,t.y,e.y),this.z=e6(this.z,t.z,e.z),this}clampScalar(t,e){return this.x=e6(this.x,t,e),this.y=e6(this.y,t,e),this.z=e6(this.z,t,e),this}clampLength(t,e){let i=this.length();return this.divideScalar(i||1).multiplyScalar(e6(i,t,e))}floor(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this.z=Math.floor(this.z),this}ceil(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this.z=Math.ceil(this.z),this}round(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this.z=Math.round(this.z),this}roundToZero(){return this.x=Math.trunc(this.x),this.y=Math.trunc(this.y),this.z=Math.trunc(this.z),this}negate(){return this.x=-this.x,this.y=-this.y,this.z=-this.z,this}dot(t){return this.x*t.x+this.y*t.y+this.z*t.z}lengthSq(){return this.x*this.x+this.y*this.y+this.z*this.z}length(){return Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z)}manhattanLength(){return Math.abs(this.x)+Math.abs(this.y)+Math.abs(this.z)}normalize(){return this.divideScalar(this.length()||1)}setLength(t){return this.normalize().multiplyScalar(t)}lerp(t,e){return this.x+=(t.x-this.x)*e,this.y+=(t.y-this.y)*e,this.z+=(t.z-this.z)*e,this}lerpVectors(t,e,i){return this.x=t.x+(e.x-t.x)*i,this.y=t.y+(e.y-t.y)*i,this.z=t.z+(e.z-t.z)*i,this}cross(t){return this.crossVectors(this,t)}crossVectors(t,e){let i=t.x,s=t.y,r=t.z,n=e.x,a=e.y,o=e.z;return this.x=s*o-r*a,this.y=r*n-i*o,this.z=i*a-s*n,this}projectOnVector(t){let e=t.lengthSq();if(0===e)return this.set(0,0,0);let i=t.dot(this)/e;return this.copy(t).multiplyScalar(i)}projectOnPlane(t){return iP.copy(this).projectOnVector(t),this.sub(iP)}reflect(t){return this.sub(iP.copy(t).multiplyScalar(2*this.dot(t)))}angleTo(t){let e=Math.sqrt(this.lengthSq()*t.lengthSq());return 0===e?Math.PI/2:Math.acos(e6(this.dot(t)/e,-1,1))}distanceTo(t){return Math.sqrt(this.distanceToSquared(t))}distanceToSquared(t){let e=this.x-t.x,i=this.y-t.y,s=this.z-t.z;return e*e+i*i+s*s}manhattanDistanceTo(t){return Math.abs(this.x-t.x)+Math.abs(this.y-t.y)+Math.abs(this.z-t.z)}setFromSpherical(t){return this.setFromSphericalCoords(t.radius,t.phi,t.theta)}setFromSphericalCoords(t,e,i){let s=Math.sin(e)*t;return this.x=s*Math.sin(i),this.y=Math.cos(e)*t,this.z=s*Math.cos(i),this}setFromCylindrical(t){return this.setFromCylindricalCoords(t.radius,t.theta,t.y)}setFromCylindricalCoords(t,e,i){return this.x=t*Math.sin(e),this.y=i,this.z=t*Math.cos(e),this}setFromMatrixPosition(t){let e=t.elements;return this.x=e[12],this.y=e[13],this.z=e[14],this}setFromMatrixScale(t){let e=this.setFromMatrixColumn(t,0).length(),i=this.setFromMatrixColumn(t,1).length(),s=this.setFromMatrixColumn(t,2).length();return this.x=e,this.y=i,this.z=s,this}setFromMatrixColumn(t,e){return this.fromArray(t.elements,4*e)}setFromMatrix3Column(t,e){return this.fromArray(t.elements,3*e)}setFromEuler(t){return this.x=t._x,this.y=t._y,this.z=t._z,this}setFromColor(t){return this.x=t.r,this.y=t.g,this.z=t.b,this}equals(t){return t.x===this.x&&t.y===this.y&&t.z===this.z}fromArray(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return this.x=t[e],this.y=t[e+1],this.z=t[e+2],this}toArray(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return t[e]=this.x,t[e+1]=this.y,t[e+2]=this.z,t}fromBufferAttribute(t,e){return this.x=t.getX(e),this.y=t.getY(e),this.z=t.getZ(e),this}random(){return this.x=Math.random(),this.y=Math.random(),this.z=Math.random(),this}randomDirection(){let t=Math.random()*Math.PI*2,e=2*Math.random()-1,i=Math.sqrt(1-e*e);return this.x=i*Math.cos(t),this.y=e,this.z=i*Math.sin(t),this}*[Symbol.iterator](){yield this.x,yield this.y,yield this.z}constructor(t=0,e=0,i=0){iR.prototype.isVector3=!0,this.x=t,this.y=e,this.z=i}}let iP=new iR,iN=new iE;class iV{set(t,e){return this.min.copy(t),this.max.copy(e),this}setFromArray(t){this.makeEmpty();for(let e=0,i=t.length;e<i;e+=3)this.expandByPoint(iF.fromArray(t,e));return this}setFromBufferAttribute(t){this.makeEmpty();for(let e=0,i=t.count;e<i;e++)this.expandByPoint(iF.fromBufferAttribute(t,e));return this}setFromPoints(t){this.makeEmpty();for(let e=0,i=t.length;e<i;e++)this.expandByPoint(t[e]);return this}setFromCenterAndSize(t,e){let i=iF.copy(e).multiplyScalar(.5);return this.min.copy(t).sub(i),this.max.copy(t).add(i),this}setFromObject(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return this.makeEmpty(),this.expandByObject(t,e)}clone(){return new this.constructor().copy(this)}copy(t){return this.min.copy(t.min),this.max.copy(t.max),this}makeEmpty(){return this.min.x=this.min.y=this.min.z=Infinity,this.max.x=this.max.y=this.max.z=-1/0,this}isEmpty(){return this.max.x<this.min.x||this.max.y<this.min.y||this.max.z<this.min.z}getCenter(t){return this.isEmpty()?t.set(0,0,0):t.addVectors(this.min,this.max).multiplyScalar(.5)}getSize(t){return this.isEmpty()?t.set(0,0,0):t.subVectors(this.max,this.min)}expandByPoint(t){return this.min.min(t),this.max.max(t),this}expandByVector(t){return this.min.sub(t),this.max.add(t),this}expandByScalar(t){return this.min.addScalar(-t),this.max.addScalar(t),this}expandByObject(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];t.updateWorldMatrix(!1,!1);let i=t.geometry;if(void 0!==i){let s=i.getAttribute("position");if(!0===e&&void 0!==s&&!0!==t.isInstancedMesh)for(let e=0,i=s.count;e<i;e++)!0===t.isMesh?t.getVertexPosition(e,iF):iF.fromBufferAttribute(s,e),iF.applyMatrix4(t.matrixWorld),this.expandByPoint(iF);else void 0!==t.boundingBox?(null===t.boundingBox&&t.computeBoundingBox(),ij.copy(t.boundingBox)):(null===i.boundingBox&&i.computeBoundingBox(),ij.copy(i.boundingBox)),ij.applyMatrix4(t.matrixWorld),this.union(ij)}let s=t.children;for(let t=0,i=s.length;t<i;t++)this.expandByObject(s[t],e);return this}containsPoint(t){return t.x>=this.min.x&&t.x<=this.max.x&&t.y>=this.min.y&&t.y<=this.max.y&&t.z>=this.min.z&&t.z<=this.max.z}containsBox(t){return this.min.x<=t.min.x&&t.max.x<=this.max.x&&this.min.y<=t.min.y&&t.max.y<=this.max.y&&this.min.z<=t.min.z&&t.max.z<=this.max.z}getParameter(t,e){return e.set((t.x-this.min.x)/(this.max.x-this.min.x),(t.y-this.min.y)/(this.max.y-this.min.y),(t.z-this.min.z)/(this.max.z-this.min.z))}intersectsBox(t){return t.max.x>=this.min.x&&t.min.x<=this.max.x&&t.max.y>=this.min.y&&t.min.y<=this.max.y&&t.max.z>=this.min.z&&t.min.z<=this.max.z}intersectsSphere(t){return this.clampPoint(t.center,iF),iF.distanceToSquared(t.center)<=t.radius*t.radius}intersectsPlane(t){let e,i;return t.normal.x>0?(e=t.normal.x*this.min.x,i=t.normal.x*this.max.x):(e=t.normal.x*this.max.x,i=t.normal.x*this.min.x),t.normal.y>0?(e+=t.normal.y*this.min.y,i+=t.normal.y*this.max.y):(e+=t.normal.y*this.max.y,i+=t.normal.y*this.min.y),t.normal.z>0?(e+=t.normal.z*this.min.z,i+=t.normal.z*this.max.z):(e+=t.normal.z*this.max.z,i+=t.normal.z*this.min.z),e<=-t.constant&&i>=-t.constant}intersectsTriangle(t){if(this.isEmpty())return!1;this.getCenter(iG),iX.subVectors(this.max,iG),iU.subVectors(t.a,iG),iD.subVectors(t.b,iG),iW.subVectors(t.c,iG),iH.subVectors(iD,iU),iJ.subVectors(iW,iD),iq.subVectors(iU,iW);let e=[0,-iH.z,iH.y,0,-iJ.z,iJ.y,0,-iq.z,iq.y,iH.z,0,-iH.x,iJ.z,0,-iJ.x,iq.z,0,-iq.x,-iH.y,iH.x,0,-iJ.y,iJ.x,0,-iq.y,iq.x,0];return!!iQ(e,iU,iD,iW,iX)&&!!iQ(e=[1,0,0,0,1,0,0,0,1],iU,iD,iW,iX)&&(iZ.crossVectors(iH,iJ),iQ(e=[iZ.x,iZ.y,iZ.z],iU,iD,iW,iX))}clampPoint(t,e){return e.copy(t).clamp(this.min,this.max)}distanceToPoint(t){return this.clampPoint(t,iF).distanceTo(t)}getBoundingSphere(t){return this.isEmpty()?t.makeEmpty():(this.getCenter(t.center),t.radius=.5*this.getSize(iF).length()),t}intersect(t){return this.min.max(t.min),this.max.min(t.max),this.isEmpty()&&this.makeEmpty(),this}union(t){return this.min.min(t.min),this.max.max(t.max),this}applyMatrix4(t){return this.isEmpty()||(iL[0].set(this.min.x,this.min.y,this.min.z).applyMatrix4(t),iL[1].set(this.min.x,this.min.y,this.max.z).applyMatrix4(t),iL[2].set(this.min.x,this.max.y,this.min.z).applyMatrix4(t),iL[3].set(this.min.x,this.max.y,this.max.z).applyMatrix4(t),iL[4].set(this.max.x,this.min.y,this.min.z).applyMatrix4(t),iL[5].set(this.max.x,this.min.y,this.max.z).applyMatrix4(t),iL[6].set(this.max.x,this.max.y,this.min.z).applyMatrix4(t),iL[7].set(this.max.x,this.max.y,this.max.z).applyMatrix4(t),this.setFromPoints(iL)),this}translate(t){return this.min.add(t),this.max.add(t),this}equals(t){return t.min.equals(this.min)&&t.max.equals(this.max)}constructor(t=new iR(Infinity,Infinity,Infinity),e=new iR(-1/0,-1/0,-1/0)){this.isBox3=!0,this.min=t,this.max=e}}let iL=[new iR,new iR,new iR,new iR,new iR,new iR,new iR,new iR],iF=new iR,ij=new iV,iU=new iR,iD=new iR,iW=new iR,iH=new iR,iJ=new iR,iq=new iR,iG=new iR,iX=new iR,iZ=new iR,iY=new iR;function iQ(t,e,i,s,r){for(let n=0,a=t.length-3;n<=a;n+=3){iY.fromArray(t,n);let a=r.x*Math.abs(iY.x)+r.y*Math.abs(iY.y)+r.z*Math.abs(iY.z),o=e.dot(iY),h=i.dot(iY),l=s.dot(iY);if(Math.max(-Math.max(o,h,l),Math.min(o,h,l))>a)return!1}return!0}let iK=new iV,i$=new iR,i0=new iR;class i1{set(t,e){return this.center.copy(t),this.radius=e,this}setFromPoints(t,e){let i=this.center;void 0!==e?i.copy(e):iK.setFromPoints(t).getCenter(i);let s=0;for(let e=0,r=t.length;e<r;e++)s=Math.max(s,i.distanceToSquared(t[e]));return this.radius=Math.sqrt(s),this}copy(t){return this.center.copy(t.center),this.radius=t.radius,this}isEmpty(){return this.radius<0}makeEmpty(){return this.center.set(0,0,0),this.radius=-1,this}containsPoint(t){return t.distanceToSquared(this.center)<=this.radius*this.radius}distanceToPoint(t){return t.distanceTo(this.center)-this.radius}intersectsSphere(t){let e=this.radius+t.radius;return t.center.distanceToSquared(this.center)<=e*e}intersectsBox(t){return t.intersectsSphere(this)}intersectsPlane(t){return Math.abs(t.distanceToPoint(this.center))<=this.radius}clampPoint(t,e){let i=this.center.distanceToSquared(t);return e.copy(t),i>this.radius*this.radius&&(e.sub(this.center).normalize(),e.multiplyScalar(this.radius).add(this.center)),e}getBoundingBox(t){return this.isEmpty()?t.makeEmpty():(t.set(this.center,this.center),t.expandByScalar(this.radius)),t}applyMatrix4(t){return this.center.applyMatrix4(t),this.radius=this.radius*t.getMaxScaleOnAxis(),this}translate(t){return this.center.add(t),this}expandByPoint(t){if(this.isEmpty())return this.center.copy(t),this.radius=0,this;i$.subVectors(t,this.center);let e=i$.lengthSq();if(e>this.radius*this.radius){let t=Math.sqrt(e),i=(t-this.radius)*.5;this.center.addScaledVector(i$,i/t),this.radius+=i}return this}union(t){return t.isEmpty()||(this.isEmpty()?this.copy(t):!0===this.center.equals(t.center)?this.radius=Math.max(this.radius,t.radius):(i0.subVectors(t.center,this.center).setLength(t.radius),this.expandByPoint(i$.copy(t.center).add(i0)),this.expandByPoint(i$.copy(t.center).sub(i0)))),this}equals(t){return t.center.equals(this.center)&&t.radius===this.radius}clone(){return new this.constructor().copy(this)}constructor(t=new iR,e=-1){this.isSphere=!0,this.center=t,this.radius=e}}let i2=new iR,i3=new iR,i5=new iR,i4=new iR,i6=new iR,i8=new iR,i9=new iR;class i7{set(t,e){return this.origin.copy(t),this.direction.copy(e),this}copy(t){return this.origin.copy(t.origin),this.direction.copy(t.direction),this}at(t,e){return e.copy(this.origin).addScaledVector(this.direction,t)}lookAt(t){return this.direction.copy(t).sub(this.origin).normalize(),this}recast(t){return this.origin.copy(this.at(t,i2)),this}closestPointToPoint(t,e){e.subVectors(t,this.origin);let i=e.dot(this.direction);return i<0?e.copy(this.origin):e.copy(this.origin).addScaledVector(this.direction,i)}distanceToPoint(t){return Math.sqrt(this.distanceSqToPoint(t))}distanceSqToPoint(t){let e=i2.subVectors(t,this.origin).dot(this.direction);return e<0?this.origin.distanceToSquared(t):(i2.copy(this.origin).addScaledVector(this.direction,e),i2.distanceToSquared(t))}distanceSqToSegment(t,e,i,s){let r,n,a,o;i3.copy(t).add(e).multiplyScalar(.5),i5.copy(e).sub(t).normalize(),i4.copy(this.origin).sub(i3);let h=.5*t.distanceTo(e),l=-this.direction.dot(i5),u=i4.dot(this.direction),c=-i4.dot(i5),d=i4.lengthSq(),p=Math.abs(1-l*l);if(p>0)if(r=l*c-u,n=l*u-c,o=h*p,r>=0)if(n>=-o)if(n<=o){let t=1/p;r*=t,n*=t,a=r*(r+l*n+2*u)+n*(l*r+n+2*c)+d}else a=-(r=Math.max(0,-(l*(n=h)+u)))*r+n*(n+2*c)+d;else a=-(r=Math.max(0,-(l*(n=-h)+u)))*r+n*(n+2*c)+d;else n<=-o?(n=(r=Math.max(0,-(-l*h+u)))>0?-h:Math.min(Math.max(-h,-c),h),a=-r*r+n*(n+2*c)+d):n<=o?(r=0,a=(n=Math.min(Math.max(-h,-c),h))*(n+2*c)+d):(n=(r=Math.max(0,-(l*h+u)))>0?h:Math.min(Math.max(-h,-c),h),a=-r*r+n*(n+2*c)+d);else n=l>0?-h:h,a=-(r=Math.max(0,-(l*n+u)))*r+n*(n+2*c)+d;return i&&i.copy(this.origin).addScaledVector(this.direction,r),s&&s.copy(i3).addScaledVector(i5,n),a}intersectSphere(t,e){i2.subVectors(t.center,this.origin);let i=i2.dot(this.direction),s=i2.dot(i2)-i*i,r=t.radius*t.radius;if(s>r)return null;let n=Math.sqrt(r-s),a=i-n,o=i+n;return o<0?null:a<0?this.at(o,e):this.at(a,e)}intersectsSphere(t){return this.distanceSqToPoint(t.center)<=t.radius*t.radius}distanceToPlane(t){let e=t.normal.dot(this.direction);if(0===e)return 0===t.distanceToPoint(this.origin)?0:null;let i=-(this.origin.dot(t.normal)+t.constant)/e;return i>=0?i:null}intersectPlane(t,e){let i=this.distanceToPlane(t);return null===i?null:this.at(i,e)}intersectsPlane(t){let e=t.distanceToPoint(this.origin);return!!(0===e||t.normal.dot(this.direction)*e<0)}intersectBox(t,e){let i,s,r,n,a,o,h=1/this.direction.x,l=1/this.direction.y,u=1/this.direction.z,c=this.origin;return(h>=0?(i=(t.min.x-c.x)*h,s=(t.max.x-c.x)*h):(i=(t.max.x-c.x)*h,s=(t.min.x-c.x)*h),l>=0?(r=(t.min.y-c.y)*l,n=(t.max.y-c.y)*l):(r=(t.max.y-c.y)*l,n=(t.min.y-c.y)*l),i>n||r>s||((r>i||isNaN(i))&&(i=r),(n<s||isNaN(s))&&(s=n),u>=0?(a=(t.min.z-c.z)*u,o=(t.max.z-c.z)*u):(a=(t.max.z-c.z)*u,o=(t.min.z-c.z)*u),i>o||a>s||((a>i||i!=i)&&(i=a),(o<s||s!=s)&&(s=o),s<0)))?null:this.at(i>=0?i:s,e)}intersectsBox(t){return null!==this.intersectBox(t,i2)}intersectTriangle(t,e,i,s,r){let n;i6.subVectors(e,t),i8.subVectors(i,t),i9.crossVectors(i6,i8);let a=this.direction.dot(i9);if(a>0){if(s)return null;n=1}else{if(!(a<0))return null;n=-1,a=-a}i4.subVectors(this.origin,t);let o=n*this.direction.dot(i8.crossVectors(i4,i8));if(o<0)return null;let h=n*this.direction.dot(i6.cross(i4));if(h<0||o+h>a)return null;let l=-n*i4.dot(i9);return l<0?null:this.at(l/a,r)}applyMatrix4(t){return this.origin.applyMatrix4(t),this.direction.transformDirection(t),this}equals(t){return t.origin.equals(this.origin)&&t.direction.equals(this.direction)}clone(){return new this.constructor().copy(this)}constructor(t=new iR,e=new iR(0,0,-1)){this.origin=t,this.direction=e}}class st{set(t,e,i,s,r,n,a,o,h,l,u,c,d,p,m,y){let f=this.elements;return f[0]=t,f[4]=e,f[8]=i,f[12]=s,f[1]=r,f[5]=n,f[9]=a,f[13]=o,f[2]=h,f[6]=l,f[10]=u,f[14]=c,f[3]=d,f[7]=p,f[11]=m,f[15]=y,this}identity(){return this.set(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1),this}clone(){return new st().fromArray(this.elements)}copy(t){let e=this.elements,i=t.elements;return e[0]=i[0],e[1]=i[1],e[2]=i[2],e[3]=i[3],e[4]=i[4],e[5]=i[5],e[6]=i[6],e[7]=i[7],e[8]=i[8],e[9]=i[9],e[10]=i[10],e[11]=i[11],e[12]=i[12],e[13]=i[13],e[14]=i[14],e[15]=i[15],this}copyPosition(t){let e=this.elements,i=t.elements;return e[12]=i[12],e[13]=i[13],e[14]=i[14],this}setFromMatrix3(t){let e=t.elements;return this.set(e[0],e[3],e[6],0,e[1],e[4],e[7],0,e[2],e[5],e[8],0,0,0,0,1),this}extractBasis(t,e,i){return t.setFromMatrixColumn(this,0),e.setFromMatrixColumn(this,1),i.setFromMatrixColumn(this,2),this}makeBasis(t,e,i){return this.set(t.x,e.x,i.x,0,t.y,e.y,i.y,0,t.z,e.z,i.z,0,0,0,0,1),this}extractRotation(t){let e=this.elements,i=t.elements,s=1/se.setFromMatrixColumn(t,0).length(),r=1/se.setFromMatrixColumn(t,1).length(),n=1/se.setFromMatrixColumn(t,2).length();return e[0]=i[0]*s,e[1]=i[1]*s,e[2]=i[2]*s,e[3]=0,e[4]=i[4]*r,e[5]=i[5]*r,e[6]=i[6]*r,e[7]=0,e[8]=i[8]*n,e[9]=i[9]*n,e[10]=i[10]*n,e[11]=0,e[12]=0,e[13]=0,e[14]=0,e[15]=1,this}makeRotationFromEuler(t){let e=this.elements,i=t.x,s=t.y,r=t.z,n=Math.cos(i),a=Math.sin(i),o=Math.cos(s),h=Math.sin(s),l=Math.cos(r),u=Math.sin(r);if("XYZ"===t.order){let t=n*l,i=n*u,s=a*l,r=a*u;e[0]=o*l,e[4]=-o*u,e[8]=h,e[1]=i+s*h,e[5]=t-r*h,e[9]=-a*o,e[2]=r-t*h,e[6]=s+i*h,e[10]=n*o}else if("YXZ"===t.order){let t=o*l,i=o*u,s=h*l,r=h*u;e[0]=t+r*a,e[4]=s*a-i,e[8]=n*h,e[1]=n*u,e[5]=n*l,e[9]=-a,e[2]=i*a-s,e[6]=r+t*a,e[10]=n*o}else if("ZXY"===t.order){let t=o*l,i=o*u,s=h*l,r=h*u;e[0]=t-r*a,e[4]=-n*u,e[8]=s+i*a,e[1]=i+s*a,e[5]=n*l,e[9]=r-t*a,e[2]=-n*h,e[6]=a,e[10]=n*o}else if("ZYX"===t.order){let t=n*l,i=n*u,s=a*l,r=a*u;e[0]=o*l,e[4]=s*h-i,e[8]=t*h+r,e[1]=o*u,e[5]=r*h+t,e[9]=i*h-s,e[2]=-h,e[6]=a*o,e[10]=n*o}else if("YZX"===t.order){let t=n*o,i=n*h,s=a*o,r=a*h;e[0]=o*l,e[4]=r-t*u,e[8]=s*u+i,e[1]=u,e[5]=n*l,e[9]=-a*l,e[2]=-h*l,e[6]=i*u+s,e[10]=t-r*u}else if("XZY"===t.order){let t=n*o,i=n*h,s=a*o,r=a*h;e[0]=o*l,e[4]=-u,e[8]=h*l,e[1]=t*u+r,e[5]=n*l,e[9]=i*u-s,e[2]=s*u-i,e[6]=a*l,e[10]=r*u+t}return e[3]=0,e[7]=0,e[11]=0,e[12]=0,e[13]=0,e[14]=0,e[15]=1,this}makeRotationFromQuaternion(t){return this.compose(ss,t,sr)}lookAt(t,e,i){let s=this.elements;return so.subVectors(t,e),0===so.lengthSq()&&(so.z=1),so.normalize(),sn.crossVectors(i,so),0===sn.lengthSq()&&(1===Math.abs(i.z)?so.x+=1e-4:so.z+=1e-4,so.normalize(),sn.crossVectors(i,so)),sn.normalize(),sa.crossVectors(so,sn),s[0]=sn.x,s[4]=sa.x,s[8]=so.x,s[1]=sn.y,s[5]=sa.y,s[9]=so.y,s[2]=sn.z,s[6]=sa.z,s[10]=so.z,this}multiply(t){return this.multiplyMatrices(this,t)}premultiply(t){return this.multiplyMatrices(t,this)}multiplyMatrices(t,e){let i=t.elements,s=e.elements,r=this.elements,n=i[0],a=i[4],o=i[8],h=i[12],l=i[1],u=i[5],c=i[9],d=i[13],p=i[2],m=i[6],y=i[10],f=i[14],g=i[3],x=i[7],v=i[11],b=i[15],M=s[0],w=s[4],S=s[8],_=s[12],A=s[1],z=s[5],T=s[9],I=s[13],C=s[2],B=s[6],k=s[10],O=s[14],E=s[3],R=s[7],P=s[11],N=s[15];return r[0]=n*M+a*A+o*C+h*E,r[4]=n*w+a*z+o*B+h*R,r[8]=n*S+a*T+o*k+h*P,r[12]=n*_+a*I+o*O+h*N,r[1]=l*M+u*A+c*C+d*E,r[5]=l*w+u*z+c*B+d*R,r[9]=l*S+u*T+c*k+d*P,r[13]=l*_+u*I+c*O+d*N,r[2]=p*M+m*A+y*C+f*E,r[6]=p*w+m*z+y*B+f*R,r[10]=p*S+m*T+y*k+f*P,r[14]=p*_+m*I+y*O+f*N,r[3]=g*M+x*A+v*C+b*E,r[7]=g*w+x*z+v*B+b*R,r[11]=g*S+x*T+v*k+b*P,r[15]=g*_+x*I+v*O+b*N,this}multiplyScalar(t){let e=this.elements;return e[0]*=t,e[4]*=t,e[8]*=t,e[12]*=t,e[1]*=t,e[5]*=t,e[9]*=t,e[13]*=t,e[2]*=t,e[6]*=t,e[10]*=t,e[14]*=t,e[3]*=t,e[7]*=t,e[11]*=t,e[15]*=t,this}determinant(){let t=this.elements,e=t[0],i=t[4],s=t[8],r=t[12],n=t[1],a=t[5],o=t[9],h=t[13],l=t[2],u=t[6],c=t[10],d=t[14],p=t[3],m=t[7];return p*(r*o*u-s*h*u-r*a*c+i*h*c+s*a*d-i*o*d)+m*(e*o*d-e*h*c+r*n*c-s*n*d+s*h*l-r*o*l)+t[11]*(e*h*u-e*a*d-r*n*u+i*n*d+r*a*l-i*h*l)+t[15]*(-s*a*l-e*o*u+e*a*c+s*n*u-i*n*c+i*o*l)}transpose(){let t,e=this.elements;return t=e[1],e[1]=e[4],e[4]=t,t=e[2],e[2]=e[8],e[8]=t,t=e[6],e[6]=e[9],e[9]=t,t=e[3],e[3]=e[12],e[12]=t,t=e[7],e[7]=e[13],e[13]=t,t=e[11],e[11]=e[14],e[14]=t,this}setPosition(t,e,i){let s=this.elements;return t.isVector3?(s[12]=t.x,s[13]=t.y,s[14]=t.z):(s[12]=t,s[13]=e,s[14]=i),this}invert(){let t=this.elements,e=t[0],i=t[1],s=t[2],r=t[3],n=t[4],a=t[5],o=t[6],h=t[7],l=t[8],u=t[9],c=t[10],d=t[11],p=t[12],m=t[13],y=t[14],f=t[15],g=u*y*h-m*c*h+m*o*d-a*y*d-u*o*f+a*c*f,x=p*c*h-l*y*h-p*o*d+n*y*d+l*o*f-n*c*f,v=l*m*h-p*u*h+p*a*d-n*m*d-l*a*f+n*u*f,b=p*u*o-l*m*o-p*a*c+n*m*c+l*a*y-n*u*y,M=e*g+i*x+s*v+r*b;if(0===M)return this.set(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0);let w=1/M;return t[0]=g*w,t[1]=(m*c*r-u*y*r-m*s*d+i*y*d+u*s*f-i*c*f)*w,t[2]=(a*y*r-m*o*r+m*s*h-i*y*h-a*s*f+i*o*f)*w,t[3]=(u*o*r-a*c*r-u*s*h+i*c*h+a*s*d-i*o*d)*w,t[4]=x*w,t[5]=(l*y*r-p*c*r+p*s*d-e*y*d-l*s*f+e*c*f)*w,t[6]=(p*o*r-n*y*r-p*s*h+e*y*h+n*s*f-e*o*f)*w,t[7]=(n*c*r-l*o*r+l*s*h-e*c*h-n*s*d+e*o*d)*w,t[8]=v*w,t[9]=(p*u*r-l*m*r-p*i*d+e*m*d+l*i*f-e*u*f)*w,t[10]=(n*m*r-p*a*r+p*i*h-e*m*h-n*i*f+e*a*f)*w,t[11]=(l*a*r-n*u*r-l*i*h+e*u*h+n*i*d-e*a*d)*w,t[12]=b*w,t[13]=(l*m*s-p*u*s+p*i*c-e*m*c-l*i*y+e*u*y)*w,t[14]=(p*a*s-n*m*s-p*i*o+e*m*o+n*i*y-e*a*y)*w,t[15]=(n*u*s-l*a*s+l*i*o-e*u*o-n*i*c+e*a*c)*w,this}scale(t){let e=this.elements,i=t.x,s=t.y,r=t.z;return e[0]*=i,e[4]*=s,e[8]*=r,e[1]*=i,e[5]*=s,e[9]*=r,e[2]*=i,e[6]*=s,e[10]*=r,e[3]*=i,e[7]*=s,e[11]*=r,this}getMaxScaleOnAxis(){let t=this.elements,e=t[0]*t[0]+t[1]*t[1]+t[2]*t[2];return Math.sqrt(Math.max(e,t[4]*t[4]+t[5]*t[5]+t[6]*t[6],t[8]*t[8]+t[9]*t[9]+t[10]*t[10]))}makeTranslation(t,e,i){return t.isVector3?this.set(1,0,0,t.x,0,1,0,t.y,0,0,1,t.z,0,0,0,1):this.set(1,0,0,t,0,1,0,e,0,0,1,i,0,0,0,1),this}makeRotationX(t){let e=Math.cos(t),i=Math.sin(t);return this.set(1,0,0,0,0,e,-i,0,0,i,e,0,0,0,0,1),this}makeRotationY(t){let e=Math.cos(t),i=Math.sin(t);return this.set(e,0,i,0,0,1,0,0,-i,0,e,0,0,0,0,1),this}makeRotationZ(t){let e=Math.cos(t),i=Math.sin(t);return this.set(e,-i,0,0,i,e,0,0,0,0,1,0,0,0,0,1),this}makeRotationAxis(t,e){let i=Math.cos(e),s=Math.sin(e),r=1-i,n=t.x,a=t.y,o=t.z,h=r*n,l=r*a;return this.set(h*n+i,h*a-s*o,h*o+s*a,0,h*a+s*o,l*a+i,l*o-s*n,0,h*o-s*a,l*o+s*n,r*o*o+i,0,0,0,0,1),this}makeScale(t,e,i){return this.set(t,0,0,0,0,e,0,0,0,0,i,0,0,0,0,1),this}makeShear(t,e,i,s,r,n){return this.set(1,i,r,0,t,1,n,0,e,s,1,0,0,0,0,1),this}compose(t,e,i){let s=this.elements,r=e._x,n=e._y,a=e._z,o=e._w,h=r+r,l=n+n,u=a+a,c=r*h,d=r*l,p=r*u,m=n*l,y=n*u,f=a*u,g=o*h,x=o*l,v=o*u,b=i.x,M=i.y,w=i.z;return s[0]=(1-(m+f))*b,s[1]=(d+v)*b,s[2]=(p-x)*b,s[3]=0,s[4]=(d-v)*M,s[5]=(1-(c+f))*M,s[6]=(y+g)*M,s[7]=0,s[8]=(p+x)*w,s[9]=(y-g)*w,s[10]=(1-(c+m))*w,s[11]=0,s[12]=t.x,s[13]=t.y,s[14]=t.z,s[15]=1,this}decompose(t,e,i){let s=this.elements,r=se.set(s[0],s[1],s[2]).length(),n=se.set(s[4],s[5],s[6]).length(),a=se.set(s[8],s[9],s[10]).length();0>this.determinant()&&(r=-r),t.x=s[12],t.y=s[13],t.z=s[14],si.copy(this);let o=1/r,h=1/n,l=1/a;return si.elements[0]*=o,si.elements[1]*=o,si.elements[2]*=o,si.elements[4]*=h,si.elements[5]*=h,si.elements[6]*=h,si.elements[8]*=l,si.elements[9]*=l,si.elements[10]*=l,e.setFromRotationMatrix(si),i.x=r,i.y=n,i.z=a,this}makePerspective(t,e,i,s,r,n){let a,o,h=arguments.length>6&&void 0!==arguments[6]?arguments[6]:eK,l=this.elements;if(h===eK)a=-(n+r)/(n-r),o=-2*n*r/(n-r);else if(h===e$)a=-n/(n-r),o=-n*r/(n-r);else throw Error("THREE.Matrix4.makePerspective(): Invalid coordinate system: "+h);return l[0]=2*r/(e-t),l[4]=0,l[8]=(e+t)/(e-t),l[12]=0,l[1]=0,l[5]=2*r/(i-s),l[9]=(i+s)/(i-s),l[13]=0,l[2]=0,l[6]=0,l[10]=a,l[14]=o,l[3]=0,l[7]=0,l[11]=-1,l[15]=0,this}makeOrthographic(t,e,i,s,r,n){let a,o,h=arguments.length>6&&void 0!==arguments[6]?arguments[6]:eK,l=this.elements,u=1/(e-t),c=1/(i-s),d=1/(n-r);if(h===eK)a=(n+r)*d,o=-2*d;else if(h===e$)a=r*d,o=-1*d;else throw Error("THREE.Matrix4.makeOrthographic(): Invalid coordinate system: "+h);return l[0]=2*u,l[4]=0,l[8]=0,l[12]=-((e+t)*u),l[1]=0,l[5]=2*c,l[9]=0,l[13]=-((i+s)*c),l[2]=0,l[6]=0,l[10]=o,l[14]=-a,l[3]=0,l[7]=0,l[11]=0,l[15]=1,this}equals(t){let e=this.elements,i=t.elements;for(let t=0;t<16;t++)if(e[t]!==i[t])return!1;return!0}fromArray(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;for(let i=0;i<16;i++)this.elements[i]=t[i+e];return this}toArray(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=this.elements;return t[e]=i[0],t[e+1]=i[1],t[e+2]=i[2],t[e+3]=i[3],t[e+4]=i[4],t[e+5]=i[5],t[e+6]=i[6],t[e+7]=i[7],t[e+8]=i[8],t[e+9]=i[9],t[e+10]=i[10],t[e+11]=i[11],t[e+12]=i[12],t[e+13]=i[13],t[e+14]=i[14],t[e+15]=i[15],t}constructor(t,e,i,s,r,n,a,o,h,l,u,c,d,p,m,y){st.prototype.isMatrix4=!0,this.elements=[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1],void 0!==t&&this.set(t,e,i,s,r,n,a,o,h,l,u,c,d,p,m,y)}}let se=new iR,si=new st,ss=new iR(0,0,0),sr=new iR(1,1,1),sn=new iR,sa=new iR,so=new iR,sh=new st,sl=new iE;class su{get x(){return this._x}set x(t){this._x=t,this._onChangeCallback()}get y(){return this._y}set y(t){this._y=t,this._onChangeCallback()}get z(){return this._z}set z(t){this._z=t,this._onChangeCallback()}get order(){return this._order}set order(t){this._order=t,this._onChangeCallback()}set(t,e,i){let s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:this._order;return this._x=t,this._y=e,this._z=i,this._order=s,this._onChangeCallback(),this}clone(){return new this.constructor(this._x,this._y,this._z,this._order)}copy(t){return this._x=t._x,this._y=t._y,this._z=t._z,this._order=t._order,this._onChangeCallback(),this}setFromRotationMatrix(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this._order,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],s=t.elements,r=s[0],n=s[4],a=s[8],o=s[1],h=s[5],l=s[9],u=s[2],c=s[6],d=s[10];switch(e){case"XYZ":this._y=Math.asin(e6(a,-1,1)),.9999999>Math.abs(a)?(this._x=Math.atan2(-l,d),this._z=Math.atan2(-n,r)):(this._x=Math.atan2(c,h),this._z=0);break;case"YXZ":this._x=Math.asin(-e6(l,-1,1)),.9999999>Math.abs(l)?(this._y=Math.atan2(a,d),this._z=Math.atan2(o,h)):(this._y=Math.atan2(-u,r),this._z=0);break;case"ZXY":this._x=Math.asin(e6(c,-1,1)),.9999999>Math.abs(c)?(this._y=Math.atan2(-u,d),this._z=Math.atan2(-n,h)):(this._y=0,this._z=Math.atan2(o,r));break;case"ZYX":this._y=Math.asin(-e6(u,-1,1)),.9999999>Math.abs(u)?(this._x=Math.atan2(c,d),this._z=Math.atan2(o,r)):(this._x=0,this._z=Math.atan2(-n,h));break;case"YZX":this._z=Math.asin(e6(o,-1,1)),.9999999>Math.abs(o)?(this._x=Math.atan2(-l,h),this._y=Math.atan2(-u,r)):(this._x=0,this._y=Math.atan2(a,d));break;case"XZY":this._z=Math.asin(-e6(n,-1,1)),.9999999>Math.abs(n)?(this._x=Math.atan2(c,h),this._y=Math.atan2(a,r)):(this._x=Math.atan2(-l,d),this._y=0);break;default:console.warn("THREE.Euler: .setFromRotationMatrix() encountered an unknown order: "+e)}return this._order=e,!0===i&&this._onChangeCallback(),this}setFromQuaternion(t,e,i){return sh.makeRotationFromQuaternion(t),this.setFromRotationMatrix(sh,e,i)}setFromVector3(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this._order;return this.set(t.x,t.y,t.z,e)}reorder(t){return sl.setFromEuler(this),this.setFromQuaternion(sl,t)}equals(t){return t._x===this._x&&t._y===this._y&&t._z===this._z&&t._order===this._order}fromArray(t){return this._x=t[0],this._y=t[1],this._z=t[2],void 0!==t[3]&&(this._order=t[3]),this._onChangeCallback(),this}toArray(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return t[e]=this._x,t[e+1]=this._y,t[e+2]=this._z,t[e+3]=this._order,t}_onChange(t){return this._onChangeCallback=t,this}_onChangeCallback(){}*[Symbol.iterator](){yield this._x,yield this._y,yield this._z,yield this._order}constructor(t=0,e=0,i=0,s=su.DEFAULT_ORDER){this.isEuler=!0,this._x=t,this._y=e,this._z=i,this._order=s}}su.DEFAULT_ORDER="XYZ";class sc{set(t){this.mask=1<<t>>>0}enable(t){this.mask|=1<<t}enableAll(){this.mask=-1}toggle(t){this.mask^=1<<t}disable(t){this.mask&=~(1<<t)}disableAll(){this.mask=0}test(t){return(this.mask&t.mask)!=0}isEnabled(t){return(this.mask&1<<t)!=0}constructor(){this.mask=1}}let sd=0,sp=new iR,sm=new iE,sy=new st,sf=new iR,sg=new iR,sx=new iR,sv=new iE,sb=new iR(1,0,0),sM=new iR(0,1,0),sw=new iR(0,0,1),sS={type:"added"},s_={type:"removed"},sA={type:"childadded",child:null},sz={type:"childremoved",child:null};class sT extends e0{onBeforeShadow(){}onAfterShadow(){}onBeforeRender(){}onAfterRender(){}applyMatrix4(t){this.matrixAutoUpdate&&this.updateMatrix(),this.matrix.premultiply(t),this.matrix.decompose(this.position,this.quaternion,this.scale)}applyQuaternion(t){return this.quaternion.premultiply(t),this}setRotationFromAxisAngle(t,e){this.quaternion.setFromAxisAngle(t,e)}setRotationFromEuler(t){this.quaternion.setFromEuler(t,!0)}setRotationFromMatrix(t){this.quaternion.setFromRotationMatrix(t)}setRotationFromQuaternion(t){this.quaternion.copy(t)}rotateOnAxis(t,e){return sm.setFromAxisAngle(t,e),this.quaternion.multiply(sm),this}rotateOnWorldAxis(t,e){return sm.setFromAxisAngle(t,e),this.quaternion.premultiply(sm),this}rotateX(t){return this.rotateOnAxis(sb,t)}rotateY(t){return this.rotateOnAxis(sM,t)}rotateZ(t){return this.rotateOnAxis(sw,t)}translateOnAxis(t,e){return sp.copy(t).applyQuaternion(this.quaternion),this.position.add(sp.multiplyScalar(e)),this}translateX(t){return this.translateOnAxis(sb,t)}translateY(t){return this.translateOnAxis(sM,t)}translateZ(t){return this.translateOnAxis(sw,t)}localToWorld(t){return this.updateWorldMatrix(!0,!1),t.applyMatrix4(this.matrixWorld)}worldToLocal(t){return this.updateWorldMatrix(!0,!1),t.applyMatrix4(sy.copy(this.matrixWorld).invert())}lookAt(t,e,i){t.isVector3?sf.copy(t):sf.set(t,e,i);let s=this.parent;this.updateWorldMatrix(!0,!1),sg.setFromMatrixPosition(this.matrixWorld),this.isCamera||this.isLight?sy.lookAt(sg,sf,this.up):sy.lookAt(sf,sg,this.up),this.quaternion.setFromRotationMatrix(sy),s&&(sy.extractRotation(s.matrixWorld),sm.setFromRotationMatrix(sy),this.quaternion.premultiply(sm.invert()))}add(t){if(arguments.length>1){for(let t=0;t<arguments.length;t++)this.add(arguments[t]);return this}return t===this?console.error("THREE.Object3D.add: object can't be added as a child of itself.",t):t&&t.isObject3D?(t.removeFromParent(),t.parent=this,this.children.push(t),t.dispatchEvent(sS),sA.child=t,this.dispatchEvent(sA),sA.child=null):console.error("THREE.Object3D.add: object not an instance of THREE.Object3D.",t),this}remove(t){if(arguments.length>1){for(let t=0;t<arguments.length;t++)this.remove(arguments[t]);return this}let e=this.children.indexOf(t);return -1!==e&&(t.parent=null,this.children.splice(e,1),t.dispatchEvent(s_),sz.child=t,this.dispatchEvent(sz),sz.child=null),this}removeFromParent(){let t=this.parent;return null!==t&&t.remove(this),this}clear(){return this.remove(...this.children)}attach(t){return this.updateWorldMatrix(!0,!1),sy.copy(this.matrixWorld).invert(),null!==t.parent&&(t.parent.updateWorldMatrix(!0,!1),sy.multiply(t.parent.matrixWorld)),t.applyMatrix4(sy),t.removeFromParent(),t.parent=this,this.children.push(t),t.updateWorldMatrix(!1,!0),t.dispatchEvent(sS),sA.child=t,this.dispatchEvent(sA),sA.child=null,this}getObjectById(t){return this.getObjectByProperty("id",t)}getObjectByName(t){return this.getObjectByProperty("name",t)}getObjectByProperty(t,e){if(this[t]===e)return this;for(let i=0,s=this.children.length;i<s;i++){let s=this.children[i].getObjectByProperty(t,e);if(void 0!==s)return s}}getObjectsByProperty(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];this[t]===e&&i.push(this);let s=this.children;for(let r=0,n=s.length;r<n;r++)s[r].getObjectsByProperty(t,e,i);return i}getWorldPosition(t){return this.updateWorldMatrix(!0,!1),t.setFromMatrixPosition(this.matrixWorld)}getWorldQuaternion(t){return this.updateWorldMatrix(!0,!1),this.matrixWorld.decompose(sg,t,sx),t}getWorldScale(t){return this.updateWorldMatrix(!0,!1),this.matrixWorld.decompose(sg,sv,t),t}getWorldDirection(t){this.updateWorldMatrix(!0,!1);let e=this.matrixWorld.elements;return t.set(e[8],e[9],e[10]).normalize()}raycast(){}traverse(t){t(this);let e=this.children;for(let i=0,s=e.length;i<s;i++)e[i].traverse(t)}traverseVisible(t){if(!1===this.visible)return;t(this);let e=this.children;for(let i=0,s=e.length;i<s;i++)e[i].traverseVisible(t)}traverseAncestors(t){let e=this.parent;null!==e&&(t(e),e.traverseAncestors(t))}updateMatrix(){this.matrix.compose(this.position,this.quaternion,this.scale),this.matrixWorldNeedsUpdate=!0}updateMatrixWorld(t){this.matrixAutoUpdate&&this.updateMatrix(),(this.matrixWorldNeedsUpdate||t)&&(!0===this.matrixWorldAutoUpdate&&(null===this.parent?this.matrixWorld.copy(this.matrix):this.matrixWorld.multiplyMatrices(this.parent.matrixWorld,this.matrix)),this.matrixWorldNeedsUpdate=!1,t=!0);let e=this.children;for(let i=0,s=e.length;i<s;i++)e[i].updateMatrixWorld(t)}updateWorldMatrix(t,e){let i=this.parent;if(!0===t&&null!==i&&i.updateWorldMatrix(!0,!1),this.matrixAutoUpdate&&this.updateMatrix(),!0===this.matrixWorldAutoUpdate&&(null===this.parent?this.matrixWorld.copy(this.matrix):this.matrixWorld.multiplyMatrices(this.parent.matrixWorld,this.matrix)),!0===e){let t=this.children;for(let e=0,i=t.length;e<i;e++)t[e].updateWorldMatrix(!1,!0)}}toJSON(t){let e=void 0===t||"string"==typeof t,i={};e&&(t={geometries:{},materials:{},textures:{},images:{},shapes:{},skeletons:{},animations:{},nodes:{}},i.metadata={version:4.6,type:"Object",generator:"Object3D.toJSON"});let s={};function r(e,i){return void 0===e[i.uuid]&&(e[i.uuid]=i.toJSON(t)),i.uuid}if(s.uuid=this.uuid,s.type=this.type,""!==this.name&&(s.name=this.name),!0===this.castShadow&&(s.castShadow=!0),!0===this.receiveShadow&&(s.receiveShadow=!0),!1===this.visible&&(s.visible=!1),!1===this.frustumCulled&&(s.frustumCulled=!1),0!==this.renderOrder&&(s.renderOrder=this.renderOrder),Object.keys(this.userData).length>0&&(s.userData=this.userData),s.layers=this.layers.mask,s.matrix=this.matrix.toArray(),s.up=this.up.toArray(),!1===this.matrixAutoUpdate&&(s.matrixAutoUpdate=!1),this.isInstancedMesh&&(s.type="InstancedMesh",s.count=this.count,s.instanceMatrix=this.instanceMatrix.toJSON(),null!==this.instanceColor&&(s.instanceColor=this.instanceColor.toJSON())),this.isBatchedMesh&&(s.type="BatchedMesh",s.perObjectFrustumCulled=this.perObjectFrustumCulled,s.sortObjects=this.sortObjects,s.drawRanges=this._drawRanges,s.reservedRanges=this._reservedRanges,s.geometryInfo=this._geometryInfo.map(t=>({...t,boundingBox:t.boundingBox?{min:t.boundingBox.min.toArray(),max:t.boundingBox.max.toArray()}:void 0,boundingSphere:t.boundingSphere?{radius:t.boundingSphere.radius,center:t.boundingSphere.center.toArray()}:void 0})),s.instanceInfo=this._instanceInfo.map(t=>({...t})),s.availableInstanceIds=this._availableInstanceIds.slice(),s.availableGeometryIds=this._availableGeometryIds.slice(),s.nextIndexStart=this._nextIndexStart,s.nextVertexStart=this._nextVertexStart,s.geometryCount=this._geometryCount,s.maxInstanceCount=this._maxInstanceCount,s.maxVertexCount=this._maxVertexCount,s.maxIndexCount=this._maxIndexCount,s.geometryInitialized=this._geometryInitialized,s.matricesTexture=this._matricesTexture.toJSON(t),s.indirectTexture=this._indirectTexture.toJSON(t),null!==this._colorsTexture&&(s.colorsTexture=this._colorsTexture.toJSON(t)),null!==this.boundingSphere&&(s.boundingSphere={center:this.boundingSphere.center.toArray(),radius:this.boundingSphere.radius}),null!==this.boundingBox&&(s.boundingBox={min:this.boundingBox.min.toArray(),max:this.boundingBox.max.toArray()})),this.isScene)this.background&&(this.background.isColor?s.background=this.background.toJSON():this.background.isTexture&&(s.background=this.background.toJSON(t).uuid)),this.environment&&this.environment.isTexture&&!0!==this.environment.isRenderTargetTexture&&(s.environment=this.environment.toJSON(t).uuid);else if(this.isMesh||this.isLine||this.isPoints){s.geometry=r(t.geometries,this.geometry);let e=this.geometry.parameters;if(void 0!==e&&void 0!==e.shapes){let i=e.shapes;if(Array.isArray(i))for(let e=0,s=i.length;e<s;e++){let s=i[e];r(t.shapes,s)}else r(t.shapes,i)}}if(this.isSkinnedMesh&&(s.bindMode=this.bindMode,s.bindMatrix=this.bindMatrix.toArray(),void 0!==this.skeleton&&(r(t.skeletons,this.skeleton),s.skeleton=this.skeleton.uuid)),void 0!==this.material)if(Array.isArray(this.material)){let e=[];for(let i=0,s=this.material.length;i<s;i++)e.push(r(t.materials,this.material[i]));s.material=e}else s.material=r(t.materials,this.material);if(this.children.length>0){s.children=[];for(let e=0;e<this.children.length;e++)s.children.push(this.children[e].toJSON(t).object)}if(this.animations.length>0){s.animations=[];for(let e=0;e<this.animations.length;e++){let i=this.animations[e];s.animations.push(r(t.animations,i))}}if(e){let e=n(t.geometries),s=n(t.materials),r=n(t.textures),a=n(t.images),o=n(t.shapes),h=n(t.skeletons),l=n(t.animations),u=n(t.nodes);e.length>0&&(i.geometries=e),s.length>0&&(i.materials=s),r.length>0&&(i.textures=r),a.length>0&&(i.images=a),o.length>0&&(i.shapes=o),h.length>0&&(i.skeletons=h),l.length>0&&(i.animations=l),u.length>0&&(i.nodes=u)}return i.object=s,i;function n(t){let e=[];for(let i in t){let s=t[i];delete s.metadata,e.push(s)}return e}}clone(t){return new this.constructor().copy(this,t)}copy(t){let e=!(arguments.length>1)||void 0===arguments[1]||arguments[1];if(this.name=t.name,this.up.copy(t.up),this.position.copy(t.position),this.rotation.order=t.rotation.order,this.quaternion.copy(t.quaternion),this.scale.copy(t.scale),this.matrix.copy(t.matrix),this.matrixWorld.copy(t.matrixWorld),this.matrixAutoUpdate=t.matrixAutoUpdate,this.matrixWorldAutoUpdate=t.matrixWorldAutoUpdate,this.matrixWorldNeedsUpdate=t.matrixWorldNeedsUpdate,this.layers.mask=t.layers.mask,this.visible=t.visible,this.castShadow=t.castShadow,this.receiveShadow=t.receiveShadow,this.frustumCulled=t.frustumCulled,this.renderOrder=t.renderOrder,this.animations=t.animations.slice(),this.userData=JSON.parse(JSON.stringify(t.userData)),!0===e)for(let e=0;e<t.children.length;e++){let i=t.children[e];this.add(i.clone())}return this}constructor(){super(),this.isObject3D=!0,Object.defineProperty(this,"id",{value:sd++}),this.uuid=e4(),this.name="",this.type="Object3D",this.parent=null,this.children=[],this.up=sT.DEFAULT_UP.clone();let t=new iR,e=new su,i=new iE,s=new iR(1,1,1);e._onChange(function(){i.setFromEuler(e,!1)}),i._onChange(function(){e.setFromQuaternion(i,void 0,!1)}),Object.defineProperties(this,{position:{configurable:!0,enumerable:!0,value:t},rotation:{configurable:!0,enumerable:!0,value:e},quaternion:{configurable:!0,enumerable:!0,value:i},scale:{configurable:!0,enumerable:!0,value:s},modelViewMatrix:{value:new st},normalMatrix:{value:new is}}),this.matrix=new st,this.matrixWorld=new st,this.matrixAutoUpdate=sT.DEFAULT_MATRIX_AUTO_UPDATE,this.matrixWorldAutoUpdate=sT.DEFAULT_MATRIX_WORLD_AUTO_UPDATE,this.matrixWorldNeedsUpdate=!1,this.layers=new sc,this.visible=!0,this.castShadow=!1,this.receiveShadow=!1,this.frustumCulled=!0,this.renderOrder=0,this.animations=[],this.customDepthMaterial=void 0,this.customDistanceMaterial=void 0,this.userData={}}}sT.DEFAULT_UP=new iR(0,1,0),sT.DEFAULT_MATRIX_AUTO_UPDATE=!0,sT.DEFAULT_MATRIX_WORLD_AUTO_UPDATE=!0;let sI=new iR,sC=new iR,sB=new iR,sk=new iR,sO=new iR,sE=new iR,sR=new iR,sP=new iR,sN=new iR,sV=new iR,sL=new iI,sF=new iI,sj=new iI;class sU{static getNormal(t,e,i,s){s.subVectors(i,e),sI.subVectors(t,e),s.cross(sI);let r=s.lengthSq();return r>0?s.multiplyScalar(1/Math.sqrt(r)):s.set(0,0,0)}static getBarycoord(t,e,i,s,r){sI.subVectors(s,e),sC.subVectors(i,e),sB.subVectors(t,e);let n=sI.dot(sI),a=sI.dot(sC),o=sI.dot(sB),h=sC.dot(sC),l=sC.dot(sB),u=n*h-a*a;if(0===u)return r.set(0,0,0),null;let c=1/u,d=(h*o-a*l)*c,p=(n*l-a*o)*c;return r.set(1-d-p,p,d)}static containsPoint(t,e,i,s){return null!==this.getBarycoord(t,e,i,s,sk)&&sk.x>=0&&sk.y>=0&&sk.x+sk.y<=1}static getInterpolation(t,e,i,s,r,n,a,o){return null===this.getBarycoord(t,e,i,s,sk)?(o.x=0,o.y=0,"z"in o&&(o.z=0),"w"in o&&(o.w=0),null):(o.setScalar(0),o.addScaledVector(r,sk.x),o.addScaledVector(n,sk.y),o.addScaledVector(a,sk.z),o)}static getInterpolatedAttribute(t,e,i,s,r,n){return sL.setScalar(0),sF.setScalar(0),sj.setScalar(0),sL.fromBufferAttribute(t,e),sF.fromBufferAttribute(t,i),sj.fromBufferAttribute(t,s),n.setScalar(0),n.addScaledVector(sL,r.x),n.addScaledVector(sF,r.y),n.addScaledVector(sj,r.z),n}static isFrontFacing(t,e,i,s){return sI.subVectors(i,e),sC.subVectors(t,e),0>sI.cross(sC).dot(s)}set(t,e,i){return this.a.copy(t),this.b.copy(e),this.c.copy(i),this}setFromPointsAndIndices(t,e,i,s){return this.a.copy(t[e]),this.b.copy(t[i]),this.c.copy(t[s]),this}setFromAttributeAndIndices(t,e,i,s){return this.a.fromBufferAttribute(t,e),this.b.fromBufferAttribute(t,i),this.c.fromBufferAttribute(t,s),this}clone(){return new this.constructor().copy(this)}copy(t){return this.a.copy(t.a),this.b.copy(t.b),this.c.copy(t.c),this}getArea(){return sI.subVectors(this.c,this.b),sC.subVectors(this.a,this.b),.5*sI.cross(sC).length()}getMidpoint(t){return t.addVectors(this.a,this.b).add(this.c).multiplyScalar(1/3)}getNormal(t){return sU.getNormal(this.a,this.b,this.c,t)}getPlane(t){return t.setFromCoplanarPoints(this.a,this.b,this.c)}getBarycoord(t,e){return sU.getBarycoord(t,this.a,this.b,this.c,e)}getInterpolation(t,e,i,s,r){return sU.getInterpolation(t,this.a,this.b,this.c,e,i,s,r)}containsPoint(t){return sU.containsPoint(t,this.a,this.b,this.c)}isFrontFacing(t){return sU.isFrontFacing(this.a,this.b,this.c,t)}intersectsBox(t){return t.intersectsTriangle(this)}closestPointToPoint(t,e){let i,s,r=this.a,n=this.b,a=this.c;sO.subVectors(n,r),sE.subVectors(a,r),sP.subVectors(t,r);let o=sO.dot(sP),h=sE.dot(sP);if(o<=0&&h<=0)return e.copy(r);sN.subVectors(t,n);let l=sO.dot(sN),u=sE.dot(sN);if(l>=0&&u<=l)return e.copy(n);let c=o*u-l*h;if(c<=0&&o>=0&&l<=0)return i=o/(o-l),e.copy(r).addScaledVector(sO,i);sV.subVectors(t,a);let d=sO.dot(sV),p=sE.dot(sV);if(p>=0&&d<=p)return e.copy(a);let m=d*h-o*p;if(m<=0&&h>=0&&p<=0)return s=h/(h-p),e.copy(r).addScaledVector(sE,s);let y=l*p-d*u;if(y<=0&&u-l>=0&&d-p>=0)return sR.subVectors(a,n),s=(u-l)/(u-l+(d-p)),e.copy(n).addScaledVector(sR,s);let f=1/(y+m+c);return i=m*f,s=c*f,e.copy(r).addScaledVector(sO,i).addScaledVector(sE,s)}equals(t){return t.a.equals(this.a)&&t.b.equals(this.b)&&t.c.equals(this.c)}constructor(t=new iR,e=new iR,i=new iR){this.a=t,this.b=e,this.c=i}}let sD={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32},sW={h:0,s:0,l:0},sH={h:0,s:0,l:0};function sJ(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*6*(2/3-i):t}class sq{set(t,e,i){return void 0===e&&void 0===i?t&&t.isColor?this.copy(t):"number"==typeof t?this.setHex(t):"string"==typeof t&&this.setStyle(t):this.setRGB(t,e,i),this}setScalar(t){return this.r=t,this.g=t,this.b=t,this}setHex(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:eM;return t=Math.floor(t),this.r=(t>>16&255)/255,this.g=(t>>8&255)/255,this.b=(255&t)/255,iv.toWorkingColorSpace(this,e),this}setRGB(t,e,i){let s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:iv.workingColorSpace;return this.r=t,this.g=e,this.b=i,iv.toWorkingColorSpace(this,s),this}setHSL(t,e,i){let s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:iv.workingColorSpace;if(t=e8(t,1),e=e6(e,0,1),i=e6(i,0,1),0===e)this.r=this.g=this.b=i;else{let s=i<=.5?i*(1+e):i+e-i*e,r=2*i-s;this.r=sJ(r,s,t+1/3),this.g=sJ(r,s,t),this.b=sJ(r,s,t-1/3)}return iv.toWorkingColorSpace(this,s),this}setStyle(t){let e,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:eM;function s(e){void 0!==e&&1>parseFloat(e)&&console.warn("THREE.Color: Alpha component of "+t+" will be ignored.")}if(e=/^(\w+)\(([^\)]*)\)/.exec(t)){let r,n=e[1],a=e[2];switch(n){case"rgb":case"rgba":if(r=/^\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*(?:,\s*(\d*\.?\d+)\s*)?$/.exec(a))return s(r[4]),this.setRGB(Math.min(255,parseInt(r[1],10))/255,Math.min(255,parseInt(r[2],10))/255,Math.min(255,parseInt(r[3],10))/255,i);if(r=/^\s*(\d+)\%\s*,\s*(\d+)\%\s*,\s*(\d+)\%\s*(?:,\s*(\d*\.?\d+)\s*)?$/.exec(a))return s(r[4]),this.setRGB(Math.min(100,parseInt(r[1],10))/100,Math.min(100,parseInt(r[2],10))/100,Math.min(100,parseInt(r[3],10))/100,i);break;case"hsl":case"hsla":if(r=/^\s*(\d*\.?\d+)\s*,\s*(\d*\.?\d+)\%\s*,\s*(\d*\.?\d+)\%\s*(?:,\s*(\d*\.?\d+)\s*)?$/.exec(a))return s(r[4]),this.setHSL(parseFloat(r[1])/360,parseFloat(r[2])/100,parseFloat(r[3])/100,i);break;default:console.warn("THREE.Color: Unknown color model "+t)}}else if(e=/^\#([A-Fa-f\d]+)$/.exec(t)){let s=e[1],r=s.length;if(3===r)return this.setRGB(parseInt(s.charAt(0),16)/15,parseInt(s.charAt(1),16)/15,parseInt(s.charAt(2),16)/15,i);if(6===r)return this.setHex(parseInt(s,16),i);console.warn("THREE.Color: Invalid hex color "+t)}else if(t&&t.length>0)return this.setColorName(t,i);return this}setColorName(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:eM,i=sD[t.toLowerCase()];return void 0!==i?this.setHex(i,e):console.warn("THREE.Color: Unknown color "+t),this}clone(){return new this.constructor(this.r,this.g,this.b)}copy(t){return this.r=t.r,this.g=t.g,this.b=t.b,this}copySRGBToLinear(t){return this.r=ib(t.r),this.g=ib(t.g),this.b=ib(t.b),this}copyLinearToSRGB(t){return this.r=iM(t.r),this.g=iM(t.g),this.b=iM(t.b),this}convertSRGBToLinear(){return this.copySRGBToLinear(this),this}convertLinearToSRGB(){return this.copyLinearToSRGB(this),this}getHex(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:eM;return iv.fromWorkingColorSpace(sG.copy(this),t),65536*Math.round(e6(255*sG.r,0,255))+256*Math.round(e6(255*sG.g,0,255))+Math.round(e6(255*sG.b,0,255))}getHexString(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:eM;return("000000"+this.getHex(t).toString(16)).slice(-6)}getHSL(t){let e,i,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:iv.workingColorSpace;iv.fromWorkingColorSpace(sG.copy(this),s);let r=sG.r,n=sG.g,a=sG.b,o=Math.max(r,n,a),h=Math.min(r,n,a),l=(h+o)/2;if(h===o)e=0,i=0;else{let t=o-h;switch(i=l<=.5?t/(o+h):t/(2-o-h),o){case r:e=(n-a)/t+6*(n<a);break;case n:e=(a-r)/t+2;break;case a:e=(r-n)/t+4}e/=6}return t.h=e,t.s=i,t.l=l,t}getRGB(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:iv.workingColorSpace;return iv.fromWorkingColorSpace(sG.copy(this),e),t.r=sG.r,t.g=sG.g,t.b=sG.b,t}getStyle(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:eM;iv.fromWorkingColorSpace(sG.copy(this),t);let e=sG.r,i=sG.g,s=sG.b;return t!==eM?"color(".concat(t," ").concat(e.toFixed(3)," ").concat(i.toFixed(3)," ").concat(s.toFixed(3),")"):"rgb(".concat(Math.round(255*e),",").concat(Math.round(255*i),",").concat(Math.round(255*s),")")}offsetHSL(t,e,i){return this.getHSL(sW),this.setHSL(sW.h+t,sW.s+e,sW.l+i)}add(t){return this.r+=t.r,this.g+=t.g,this.b+=t.b,this}addColors(t,e){return this.r=t.r+e.r,this.g=t.g+e.g,this.b=t.b+e.b,this}addScalar(t){return this.r+=t,this.g+=t,this.b+=t,this}sub(t){return this.r=Math.max(0,this.r-t.r),this.g=Math.max(0,this.g-t.g),this.b=Math.max(0,this.b-t.b),this}multiply(t){return this.r*=t.r,this.g*=t.g,this.b*=t.b,this}multiplyScalar(t){return this.r*=t,this.g*=t,this.b*=t,this}lerp(t,e){return this.r+=(t.r-this.r)*e,this.g+=(t.g-this.g)*e,this.b+=(t.b-this.b)*e,this}lerpColors(t,e,i){return this.r=t.r+(e.r-t.r)*i,this.g=t.g+(e.g-t.g)*i,this.b=t.b+(e.b-t.b)*i,this}lerpHSL(t,e){this.getHSL(sW),t.getHSL(sH);let i=e9(sW.h,sH.h,e),s=e9(sW.s,sH.s,e),r=e9(sW.l,sH.l,e);return this.setHSL(i,s,r),this}setFromVector3(t){return this.r=t.x,this.g=t.y,this.b=t.z,this}applyMatrix3(t){let e=this.r,i=this.g,s=this.b,r=t.elements;return this.r=r[0]*e+r[3]*i+r[6]*s,this.g=r[1]*e+r[4]*i+r[7]*s,this.b=r[2]*e+r[5]*i+r[8]*s,this}equals(t){return t.r===this.r&&t.g===this.g&&t.b===this.b}fromArray(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return this.r=t[e],this.g=t[e+1],this.b=t[e+2],this}toArray(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return t[e]=this.r,t[e+1]=this.g,t[e+2]=this.b,t}fromBufferAttribute(t,e){return this.r=t.getX(e),this.g=t.getY(e),this.b=t.getZ(e),this}toJSON(){return this.getHex()}*[Symbol.iterator](){yield this.r,yield this.g,yield this.b}constructor(t,e,i){return this.isColor=!0,this.r=1,this.g=1,this.b=1,this.set(t,e,i)}}let sG=new sq;sq.NAMES=sD;let sX=0;class sZ extends e0{get alphaTest(){return this._alphaTest}set alphaTest(t){this._alphaTest>0!=t>0&&this.version++,this._alphaTest=t}onBeforeRender(){}onBeforeCompile(){}customProgramCacheKey(){return this.onBeforeCompile.toString()}setValues(t){if(void 0!==t)for(let e in t){let i=t[e];if(void 0===i){console.warn("THREE.Material: parameter '".concat(e,"' has value of undefined."));continue}let s=this[e];if(void 0===s){console.warn("THREE.Material: '".concat(e,"' is not a property of THREE.").concat(this.type,"."));continue}s&&s.isColor?s.set(i):s&&s.isVector3&&i&&i.isVector3?s.copy(i):this[e]=i}}toJSON(t){let e=void 0===t||"string"==typeof t;e&&(t={textures:{},images:{}});let i={metadata:{version:4.6,type:"Material",generator:"Material.toJSON"}};function s(t){let e=[];for(let i in t){let s=t[i];delete s.metadata,e.push(s)}return e}if(i.uuid=this.uuid,i.type=this.type,""!==this.name&&(i.name=this.name),this.color&&this.color.isColor&&(i.color=this.color.getHex()),void 0!==this.roughness&&(i.roughness=this.roughness),void 0!==this.metalness&&(i.metalness=this.metalness),void 0!==this.sheen&&(i.sheen=this.sheen),this.sheenColor&&this.sheenColor.isColor&&(i.sheenColor=this.sheenColor.getHex()),void 0!==this.sheenRoughness&&(i.sheenRoughness=this.sheenRoughness),this.emissive&&this.emissive.isColor&&(i.emissive=this.emissive.getHex()),void 0!==this.emissiveIntensity&&1!==this.emissiveIntensity&&(i.emissiveIntensity=this.emissiveIntensity),this.specular&&this.specular.isColor&&(i.specular=this.specular.getHex()),void 0!==this.specularIntensity&&(i.specularIntensity=this.specularIntensity),this.specularColor&&this.specularColor.isColor&&(i.specularColor=this.specularColor.getHex()),void 0!==this.shininess&&(i.shininess=this.shininess),void 0!==this.clearcoat&&(i.clearcoat=this.clearcoat),void 0!==this.clearcoatRoughness&&(i.clearcoatRoughness=this.clearcoatRoughness),this.clearcoatMap&&this.clearcoatMap.isTexture&&(i.clearcoatMap=this.clearcoatMap.toJSON(t).uuid),this.clearcoatRoughnessMap&&this.clearcoatRoughnessMap.isTexture&&(i.clearcoatRoughnessMap=this.clearcoatRoughnessMap.toJSON(t).uuid),this.clearcoatNormalMap&&this.clearcoatNormalMap.isTexture&&(i.clearcoatNormalMap=this.clearcoatNormalMap.toJSON(t).uuid,i.clearcoatNormalScale=this.clearcoatNormalScale.toArray()),void 0!==this.dispersion&&(i.dispersion=this.dispersion),void 0!==this.iridescence&&(i.iridescence=this.iridescence),void 0!==this.iridescenceIOR&&(i.iridescenceIOR=this.iridescenceIOR),void 0!==this.iridescenceThicknessRange&&(i.iridescenceThicknessRange=this.iridescenceThicknessRange),this.iridescenceMap&&this.iridescenceMap.isTexture&&(i.iridescenceMap=this.iridescenceMap.toJSON(t).uuid),this.iridescenceThicknessMap&&this.iridescenceThicknessMap.isTexture&&(i.iridescenceThicknessMap=this.iridescenceThicknessMap.toJSON(t).uuid),void 0!==this.anisotropy&&(i.anisotropy=this.anisotropy),void 0!==this.anisotropyRotation&&(i.anisotropyRotation=this.anisotropyRotation),this.anisotropyMap&&this.anisotropyMap.isTexture&&(i.anisotropyMap=this.anisotropyMap.toJSON(t).uuid),this.map&&this.map.isTexture&&(i.map=this.map.toJSON(t).uuid),this.matcap&&this.matcap.isTexture&&(i.matcap=this.matcap.toJSON(t).uuid),this.alphaMap&&this.alphaMap.isTexture&&(i.alphaMap=this.alphaMap.toJSON(t).uuid),this.lightMap&&this.lightMap.isTexture&&(i.lightMap=this.lightMap.toJSON(t).uuid,i.lightMapIntensity=this.lightMapIntensity),this.aoMap&&this.aoMap.isTexture&&(i.aoMap=this.aoMap.toJSON(t).uuid,i.aoMapIntensity=this.aoMapIntensity),this.bumpMap&&this.bumpMap.isTexture&&(i.bumpMap=this.bumpMap.toJSON(t).uuid,i.bumpScale=this.bumpScale),this.normalMap&&this.normalMap.isTexture&&(i.normalMap=this.normalMap.toJSON(t).uuid,i.normalMapType=this.normalMapType,i.normalScale=this.normalScale.toArray()),this.displacementMap&&this.displacementMap.isTexture&&(i.displacementMap=this.displacementMap.toJSON(t).uuid,i.displacementScale=this.displacementScale,i.displacementBias=this.displacementBias),this.roughnessMap&&this.roughnessMap.isTexture&&(i.roughnessMap=this.roughnessMap.toJSON(t).uuid),this.metalnessMap&&this.metalnessMap.isTexture&&(i.metalnessMap=this.metalnessMap.toJSON(t).uuid),this.emissiveMap&&this.emissiveMap.isTexture&&(i.emissiveMap=this.emissiveMap.toJSON(t).uuid),this.specularMap&&this.specularMap.isTexture&&(i.specularMap=this.specularMap.toJSON(t).uuid),this.specularIntensityMap&&this.specularIntensityMap.isTexture&&(i.specularIntensityMap=this.specularIntensityMap.toJSON(t).uuid),this.specularColorMap&&this.specularColorMap.isTexture&&(i.specularColorMap=this.specularColorMap.toJSON(t).uuid),this.envMap&&this.envMap.isTexture&&(i.envMap=this.envMap.toJSON(t).uuid,void 0!==this.combine&&(i.combine=this.combine)),void 0!==this.envMapRotation&&(i.envMapRotation=this.envMapRotation.toArray()),void 0!==this.envMapIntensity&&(i.envMapIntensity=this.envMapIntensity),void 0!==this.reflectivity&&(i.reflectivity=this.reflectivity),void 0!==this.refractionRatio&&(i.refractionRatio=this.refractionRatio),this.gradientMap&&this.gradientMap.isTexture&&(i.gradientMap=this.gradientMap.toJSON(t).uuid),void 0!==this.transmission&&(i.transmission=this.transmission),this.transmissionMap&&this.transmissionMap.isTexture&&(i.transmissionMap=this.transmissionMap.toJSON(t).uuid),void 0!==this.thickness&&(i.thickness=this.thickness),this.thicknessMap&&this.thicknessMap.isTexture&&(i.thicknessMap=this.thicknessMap.toJSON(t).uuid),void 0!==this.attenuationDistance&&this.attenuationDistance!==1/0&&(i.attenuationDistance=this.attenuationDistance),void 0!==this.attenuationColor&&(i.attenuationColor=this.attenuationColor.getHex()),void 0!==this.size&&(i.size=this.size),null!==this.shadowSide&&(i.shadowSide=this.shadowSide),void 0!==this.sizeAttenuation&&(i.sizeAttenuation=this.sizeAttenuation),this.blending!==v&&(i.blending=this.blending),this.side!==y&&(i.side=this.side),!0===this.vertexColors&&(i.vertexColors=!0),this.opacity<1&&(i.opacity=this.opacity),!0===this.transparent&&(i.transparent=!0),this.blendSrc!==E&&(i.blendSrc=this.blendSrc),this.blendDst!==R&&(i.blendDst=this.blendDst),this.blendEquation!==_&&(i.blendEquation=this.blendEquation),null!==this.blendSrcAlpha&&(i.blendSrcAlpha=this.blendSrcAlpha),null!==this.blendDstAlpha&&(i.blendDstAlpha=this.blendDstAlpha),null!==this.blendEquationAlpha&&(i.blendEquationAlpha=this.blendEquationAlpha),this.blendColor&&this.blendColor.isColor&&(i.blendColor=this.blendColor.getHex()),0!==this.blendAlpha&&(i.blendAlpha=this.blendAlpha),this.depthFunc!==G&&(i.depthFunc=this.depthFunc),!1===this.depthTest&&(i.depthTest=this.depthTest),!1===this.depthWrite&&(i.depthWrite=this.depthWrite),!1===this.colorWrite&&(i.colorWrite=this.colorWrite),255!==this.stencilWriteMask&&(i.stencilWriteMask=this.stencilWriteMask),this.stencilFunc!==ej&&(i.stencilFunc=this.stencilFunc),0!==this.stencilRef&&(i.stencilRef=this.stencilRef),255!==this.stencilFuncMask&&(i.stencilFuncMask=this.stencilFuncMask),this.stencilFail!==ez&&(i.stencilFail=this.stencilFail),this.stencilZFail!==ez&&(i.stencilZFail=this.stencilZFail),this.stencilZPass!==ez&&(i.stencilZPass=this.stencilZPass),!0===this.stencilWrite&&(i.stencilWrite=this.stencilWrite),void 0!==this.rotation&&0!==this.rotation&&(i.rotation=this.rotation),!0===this.polygonOffset&&(i.polygonOffset=!0),0!==this.polygonOffsetFactor&&(i.polygonOffsetFactor=this.polygonOffsetFactor),0!==this.polygonOffsetUnits&&(i.polygonOffsetUnits=this.polygonOffsetUnits),void 0!==this.linewidth&&1!==this.linewidth&&(i.linewidth=this.linewidth),void 0!==this.dashSize&&(i.dashSize=this.dashSize),void 0!==this.gapSize&&(i.gapSize=this.gapSize),void 0!==this.scale&&(i.scale=this.scale),!0===this.dithering&&(i.dithering=!0),this.alphaTest>0&&(i.alphaTest=this.alphaTest),!0===this.alphaHash&&(i.alphaHash=!0),!0===this.alphaToCoverage&&(i.alphaToCoverage=!0),!0===this.premultipliedAlpha&&(i.premultipliedAlpha=!0),!0===this.forceSinglePass&&(i.forceSinglePass=!0),!0===this.wireframe&&(i.wireframe=!0),this.wireframeLinewidth>1&&(i.wireframeLinewidth=this.wireframeLinewidth),"round"!==this.wireframeLinecap&&(i.wireframeLinecap=this.wireframeLinecap),"round"!==this.wireframeLinejoin&&(i.wireframeLinejoin=this.wireframeLinejoin),!0===this.flatShading&&(i.flatShading=!0),!1===this.visible&&(i.visible=!1),!1===this.toneMapped&&(i.toneMapped=!1),!1===this.fog&&(i.fog=!1),Object.keys(this.userData).length>0&&(i.userData=this.userData),e){let e=s(t.textures),r=s(t.images);e.length>0&&(i.textures=e),r.length>0&&(i.images=r)}return i}clone(){return new this.constructor().copy(this)}copy(t){this.name=t.name,this.blending=t.blending,this.side=t.side,this.vertexColors=t.vertexColors,this.opacity=t.opacity,this.transparent=t.transparent,this.blendSrc=t.blendSrc,this.blendDst=t.blendDst,this.blendEquation=t.blendEquation,this.blendSrcAlpha=t.blendSrcAlpha,this.blendDstAlpha=t.blendDstAlpha,this.blendEquationAlpha=t.blendEquationAlpha,this.blendColor.copy(t.blendColor),this.blendAlpha=t.blendAlpha,this.depthFunc=t.depthFunc,this.depthTest=t.depthTest,this.depthWrite=t.depthWrite,this.stencilWriteMask=t.stencilWriteMask,this.stencilFunc=t.stencilFunc,this.stencilRef=t.stencilRef,this.stencilFuncMask=t.stencilFuncMask,this.stencilFail=t.stencilFail,this.stencilZFail=t.stencilZFail,this.stencilZPass=t.stencilZPass,this.stencilWrite=t.stencilWrite;let e=t.clippingPlanes,i=null;if(null!==e){let t=e.length;i=Array(t);for(let s=0;s!==t;++s)i[s]=e[s].clone()}return this.clippingPlanes=i,this.clipIntersection=t.clipIntersection,this.clipShadows=t.clipShadows,this.shadowSide=t.shadowSide,this.colorWrite=t.colorWrite,this.precision=t.precision,this.polygonOffset=t.polygonOffset,this.polygonOffsetFactor=t.polygonOffsetFactor,this.polygonOffsetUnits=t.polygonOffsetUnits,this.dithering=t.dithering,this.alphaTest=t.alphaTest,this.alphaHash=t.alphaHash,this.alphaToCoverage=t.alphaToCoverage,this.premultipliedAlpha=t.premultipliedAlpha,this.forceSinglePass=t.forceSinglePass,this.visible=t.visible,this.toneMapped=t.toneMapped,this.userData=JSON.parse(JSON.stringify(t.userData)),this}dispose(){this.dispatchEvent({type:"dispose"})}set needsUpdate(t){!0===t&&this.version++}constructor(){super(),this.isMaterial=!0,Object.defineProperty(this,"id",{value:sX++}),this.uuid=e4(),this.name="",this.type="Material",this.blending=v,this.side=y,this.vertexColors=!1,this.opacity=1,this.transparent=!1,this.alphaHash=!1,this.blendSrc=E,this.blendDst=R,this.blendEquation=_,this.blendSrcAlpha=null,this.blendDstAlpha=null,this.blendEquationAlpha=null,this.blendColor=new sq(0,0,0),this.blendAlpha=0,this.depthFunc=G,this.depthTest=!0,this.depthWrite=!0,this.stencilWriteMask=255,this.stencilFunc=ej,this.stencilRef=0,this.stencilFuncMask=255,this.stencilFail=ez,this.stencilZFail=ez,this.stencilZPass=ez,this.stencilWrite=!1,this.clippingPlanes=null,this.clipIntersection=!1,this.clipShadows=!1,this.shadowSide=null,this.colorWrite=!0,this.precision=null,this.polygonOffset=!1,this.polygonOffsetFactor=0,this.polygonOffsetUnits=0,this.dithering=!1,this.alphaToCoverage=!1,this.premultipliedAlpha=!1,this.forceSinglePass=!1,this.allowOverride=!0,this.visible=!0,this.toneMapped=!0,this.userData={},this.version=0,this._alphaTest=0}}class sY extends sZ{copy(t){return super.copy(t),this.color.copy(t.color),this.map=t.map,this.lightMap=t.lightMap,this.lightMapIntensity=t.lightMapIntensity,this.aoMap=t.aoMap,this.aoMapIntensity=t.aoMapIntensity,this.specularMap=t.specularMap,this.alphaMap=t.alphaMap,this.envMap=t.envMap,this.envMapRotation.copy(t.envMapRotation),this.combine=t.combine,this.reflectivity=t.reflectivity,this.refractionRatio=t.refractionRatio,this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this.wireframeLinecap=t.wireframeLinecap,this.wireframeLinejoin=t.wireframeLinejoin,this.fog=t.fog,this}constructor(t){super(),this.isMeshBasicMaterial=!0,this.type="MeshBasicMaterial",this.color=new sq(0xffffff),this.map=null,this.lightMap=null,this.lightMapIntensity=1,this.aoMap=null,this.aoMapIntensity=1,this.specularMap=null,this.alphaMap=null,this.envMap=null,this.envMapRotation=new su,this.combine=K,this.reflectivity=1,this.refractionRatio=.98,this.wireframe=!1,this.wireframeLinewidth=1,this.wireframeLinecap="round",this.wireframeLinejoin="round",this.fog=!0,this.setValues(t)}}let sQ=function(){let t=new ArrayBuffer(4),e=new Float32Array(t),i=new Uint32Array(t),s=new Uint32Array(512),r=new Uint32Array(512);for(let t=0;t<256;++t){let e=t-127;e<-27?(s[t]=0,s[256|t]=32768,r[t]=24,r[256|t]=24):e<-14?(s[t]=1024>>-e-14,s[256|t]=1024>>-e-14|32768,r[t]=-e-1,r[256|t]=-e-1):e<=15?(s[t]=e+15<<10,s[256|t]=e+15<<10|32768,r[t]=13,r[256|t]=13):e<128?(s[t]=31744,s[256|t]=64512,r[t]=24,r[256|t]=24):(s[t]=31744,s[256|t]=64512,r[t]=13,r[256|t]=13)}let n=new Uint32Array(2048),a=new Uint32Array(64),o=new Uint32Array(64);for(let t=1;t<1024;++t){let e=t<<13,i=0;for(;(8388608&e)==0;)e<<=1,i-=8388608;e&=-8388609,i+=0x38800000,n[t]=e|i}for(let t=1024;t<2048;++t)n[t]=0x38000000+(t-1024<<13);for(let t=1;t<31;++t)a[t]=t<<23;a[31]=0x47800000,a[32]=0x80000000;for(let t=33;t<63;++t)a[t]=0x80000000+(t-32<<23);a[63]=0xc7800000;for(let t=1;t<64;++t)32!==t&&(o[t]=1024);return{floatView:e,uint32View:i,baseTable:s,shiftTable:r,mantissaTable:n,exponentTable:a,offsetTable:o}}();function sK(t){Math.abs(t)>65504&&console.warn("THREE.DataUtils.toHalfFloat(): Value out of range."),t=e6(t,-65504,65504),sQ.floatView[0]=t;let e=sQ.uint32View[0],i=e>>23&511;return sQ.baseTable[i]+((8388607&e)>>sQ.shiftTable[i])}function s$(t){let e=t>>10;return sQ.uint32View[0]=sQ.mantissaTable[sQ.offsetTable[e]+(1023&t)]+sQ.exponentTable[e],sQ.floatView[0]}let s0=new iR,s1=new ii,s2=0;class s3{onUploadCallback(){}set needsUpdate(t){!0===t&&this.version++}setUsage(t){return this.usage=t,this}addUpdateRange(t,e){this.updateRanges.push({start:t,count:e})}clearUpdateRanges(){this.updateRanges.length=0}copy(t){return this.name=t.name,this.array=new t.array.constructor(t.array),this.itemSize=t.itemSize,this.count=t.count,this.normalized=t.normalized,this.usage=t.usage,this.gpuType=t.gpuType,this}copyAt(t,e,i){t*=this.itemSize,i*=e.itemSize;for(let s=0,r=this.itemSize;s<r;s++)this.array[t+s]=e.array[i+s];return this}copyArray(t){return this.array.set(t),this}applyMatrix3(t){if(2===this.itemSize)for(let e=0,i=this.count;e<i;e++)s1.fromBufferAttribute(this,e),s1.applyMatrix3(t),this.setXY(e,s1.x,s1.y);else if(3===this.itemSize)for(let e=0,i=this.count;e<i;e++)s0.fromBufferAttribute(this,e),s0.applyMatrix3(t),this.setXYZ(e,s0.x,s0.y,s0.z);return this}applyMatrix4(t){for(let e=0,i=this.count;e<i;e++)s0.fromBufferAttribute(this,e),s0.applyMatrix4(t),this.setXYZ(e,s0.x,s0.y,s0.z);return this}applyNormalMatrix(t){for(let e=0,i=this.count;e<i;e++)s0.fromBufferAttribute(this,e),s0.applyNormalMatrix(t),this.setXYZ(e,s0.x,s0.y,s0.z);return this}transformDirection(t){for(let e=0,i=this.count;e<i;e++)s0.fromBufferAttribute(this,e),s0.transformDirection(t),this.setXYZ(e,s0.x,s0.y,s0.z);return this}set(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return this.array.set(t,e),this}getComponent(t,e){let i=this.array[t*this.itemSize+e];return this.normalized&&(i=e7(i,this.array)),i}setComponent(t,e,i){return this.normalized&&(i=it(i,this.array)),this.array[t*this.itemSize+e]=i,this}getX(t){let e=this.array[t*this.itemSize];return this.normalized&&(e=e7(e,this.array)),e}setX(t,e){return this.normalized&&(e=it(e,this.array)),this.array[t*this.itemSize]=e,this}getY(t){let e=this.array[t*this.itemSize+1];return this.normalized&&(e=e7(e,this.array)),e}setY(t,e){return this.normalized&&(e=it(e,this.array)),this.array[t*this.itemSize+1]=e,this}getZ(t){let e=this.array[t*this.itemSize+2];return this.normalized&&(e=e7(e,this.array)),e}setZ(t,e){return this.normalized&&(e=it(e,this.array)),this.array[t*this.itemSize+2]=e,this}getW(t){let e=this.array[t*this.itemSize+3];return this.normalized&&(e=e7(e,this.array)),e}setW(t,e){return this.normalized&&(e=it(e,this.array)),this.array[t*this.itemSize+3]=e,this}setXY(t,e,i){return t*=this.itemSize,this.normalized&&(e=it(e,this.array),i=it(i,this.array)),this.array[t+0]=e,this.array[t+1]=i,this}setXYZ(t,e,i,s){return t*=this.itemSize,this.normalized&&(e=it(e,this.array),i=it(i,this.array),s=it(s,this.array)),this.array[t+0]=e,this.array[t+1]=i,this.array[t+2]=s,this}setXYZW(t,e,i,s,r){return t*=this.itemSize,this.normalized&&(e=it(e,this.array),i=it(i,this.array),s=it(s,this.array),r=it(r,this.array)),this.array[t+0]=e,this.array[t+1]=i,this.array[t+2]=s,this.array[t+3]=r,this}onUpload(t){return this.onUploadCallback=t,this}clone(){return new this.constructor(this.array,this.itemSize).copy(this)}toJSON(){let t={itemSize:this.itemSize,type:this.array.constructor.name,array:Array.from(this.array),normalized:this.normalized};return""!==this.name&&(t.name=this.name),this.usage!==eZ&&(t.usage=this.usage),t}constructor(t,e,i=!1){if(Array.isArray(t))throw TypeError("THREE.BufferAttribute: array should be a Typed Array.");this.isBufferAttribute=!0,Object.defineProperty(this,"id",{value:s2++}),this.name="",this.array=t,this.itemSize=e,this.count=void 0!==t?t.length/e:0,this.normalized=i,this.usage=eZ,this.updateRanges=[],this.gpuType=tO,this.version=0}}class s5 extends s3{constructor(t,e,i){super(new Uint16Array(t),e,i)}}class s4 extends s3{constructor(t,e,i){super(new Uint32Array(t),e,i)}}class s6 extends s3{getX(t){let e=s$(this.array[t*this.itemSize]);return this.normalized&&(e=e7(e,this.array)),e}setX(t,e){return this.normalized&&(e=it(e,this.array)),this.array[t*this.itemSize]=sK(e),this}getY(t){let e=s$(this.array[t*this.itemSize+1]);return this.normalized&&(e=e7(e,this.array)),e}setY(t,e){return this.normalized&&(e=it(e,this.array)),this.array[t*this.itemSize+1]=sK(e),this}getZ(t){let e=s$(this.array[t*this.itemSize+2]);return this.normalized&&(e=e7(e,this.array)),e}setZ(t,e){return this.normalized&&(e=it(e,this.array)),this.array[t*this.itemSize+2]=sK(e),this}getW(t){let e=s$(this.array[t*this.itemSize+3]);return this.normalized&&(e=e7(e,this.array)),e}setW(t,e){return this.normalized&&(e=it(e,this.array)),this.array[t*this.itemSize+3]=sK(e),this}setXY(t,e,i){return t*=this.itemSize,this.normalized&&(e=it(e,this.array),i=it(i,this.array)),this.array[t+0]=sK(e),this.array[t+1]=sK(i),this}setXYZ(t,e,i,s){return t*=this.itemSize,this.normalized&&(e=it(e,this.array),i=it(i,this.array),s=it(s,this.array)),this.array[t+0]=sK(e),this.array[t+1]=sK(i),this.array[t+2]=sK(s),this}setXYZW(t,e,i,s,r){return t*=this.itemSize,this.normalized&&(e=it(e,this.array),i=it(i,this.array),s=it(s,this.array),r=it(r,this.array)),this.array[t+0]=sK(e),this.array[t+1]=sK(i),this.array[t+2]=sK(s),this.array[t+3]=sK(r),this}constructor(t,e,i){super(new Uint16Array(t),e,i),this.isFloat16BufferAttribute=!0}}class s8 extends s3{constructor(t,e,i){super(new Float32Array(t),e,i)}}let s9=0,s7=new st,rt=new sT,re=new iR,ri=new iV,rs=new iV,rr=new iR;class rn extends e0{getIndex(){return this.index}setIndex(t){return Array.isArray(t)?this.index=new(ia(t)?s4:s5)(t,1):this.index=t,this}setIndirect(t){return this.indirect=t,this}getIndirect(){return this.indirect}getAttribute(t){return this.attributes[t]}setAttribute(t,e){return this.attributes[t]=e,this}deleteAttribute(t){return delete this.attributes[t],this}hasAttribute(t){return void 0!==this.attributes[t]}addGroup(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;this.groups.push({start:t,count:e,materialIndex:i})}clearGroups(){this.groups=[]}setDrawRange(t,e){this.drawRange.start=t,this.drawRange.count=e}applyMatrix4(t){let e=this.attributes.position;void 0!==e&&(e.applyMatrix4(t),e.needsUpdate=!0);let i=this.attributes.normal;if(void 0!==i){let e=new is().getNormalMatrix(t);i.applyNormalMatrix(e),i.needsUpdate=!0}let s=this.attributes.tangent;return void 0!==s&&(s.transformDirection(t),s.needsUpdate=!0),null!==this.boundingBox&&this.computeBoundingBox(),null!==this.boundingSphere&&this.computeBoundingSphere(),this}applyQuaternion(t){return s7.makeRotationFromQuaternion(t),this.applyMatrix4(s7),this}rotateX(t){return s7.makeRotationX(t),this.applyMatrix4(s7),this}rotateY(t){return s7.makeRotationY(t),this.applyMatrix4(s7),this}rotateZ(t){return s7.makeRotationZ(t),this.applyMatrix4(s7),this}translate(t,e,i){return s7.makeTranslation(t,e,i),this.applyMatrix4(s7),this}scale(t,e,i){return s7.makeScale(t,e,i),this.applyMatrix4(s7),this}lookAt(t){return rt.lookAt(t),rt.updateMatrix(),this.applyMatrix4(rt.matrix),this}center(){return this.computeBoundingBox(),this.boundingBox.getCenter(re).negate(),this.translate(re.x,re.y,re.z),this}setFromPoints(t){let e=this.getAttribute("position");if(void 0===e){let e=[];for(let i=0,s=t.length;i<s;i++){let s=t[i];e.push(s.x,s.y,s.z||0)}this.setAttribute("position",new s8(e,3))}else{let i=Math.min(t.length,e.count);for(let s=0;s<i;s++){let i=t[s];e.setXYZ(s,i.x,i.y,i.z||0)}t.length>e.count&&console.warn("THREE.BufferGeometry: Buffer size too small for points data. Use .dispose() and create a new geometry."),e.needsUpdate=!0}return this}computeBoundingBox(){null===this.boundingBox&&(this.boundingBox=new iV);let t=this.attributes.position,e=this.morphAttributes.position;if(t&&t.isGLBufferAttribute){console.error("THREE.BufferGeometry.computeBoundingBox(): GLBufferAttribute requires a manual bounding box.",this),this.boundingBox.set(new iR(-1/0,-1/0,-1/0),new iR(Infinity,Infinity,Infinity));return}if(void 0!==t){if(this.boundingBox.setFromBufferAttribute(t),e)for(let t=0,i=e.length;t<i;t++){let i=e[t];ri.setFromBufferAttribute(i),this.morphTargetsRelative?(rr.addVectors(this.boundingBox.min,ri.min),this.boundingBox.expandByPoint(rr),rr.addVectors(this.boundingBox.max,ri.max),this.boundingBox.expandByPoint(rr)):(this.boundingBox.expandByPoint(ri.min),this.boundingBox.expandByPoint(ri.max))}}else this.boundingBox.makeEmpty();(isNaN(this.boundingBox.min.x)||isNaN(this.boundingBox.min.y)||isNaN(this.boundingBox.min.z))&&console.error('THREE.BufferGeometry.computeBoundingBox(): Computed min/max have NaN values. The "position" attribute is likely to have NaN values.',this)}computeBoundingSphere(){null===this.boundingSphere&&(this.boundingSphere=new i1);let t=this.attributes.position,e=this.morphAttributes.position;if(t&&t.isGLBufferAttribute){console.error("THREE.BufferGeometry.computeBoundingSphere(): GLBufferAttribute requires a manual bounding sphere.",this),this.boundingSphere.set(new iR,1/0);return}if(t){let i=this.boundingSphere.center;if(ri.setFromBufferAttribute(t),e)for(let t=0,i=e.length;t<i;t++){let i=e[t];rs.setFromBufferAttribute(i),this.morphTargetsRelative?(rr.addVectors(ri.min,rs.min),ri.expandByPoint(rr),rr.addVectors(ri.max,rs.max),ri.expandByPoint(rr)):(ri.expandByPoint(rs.min),ri.expandByPoint(rs.max))}ri.getCenter(i);let s=0;for(let e=0,r=t.count;e<r;e++)rr.fromBufferAttribute(t,e),s=Math.max(s,i.distanceToSquared(rr));if(e)for(let r=0,n=e.length;r<n;r++){let n=e[r],a=this.morphTargetsRelative;for(let e=0,r=n.count;e<r;e++)rr.fromBufferAttribute(n,e),a&&(re.fromBufferAttribute(t,e),rr.add(re)),s=Math.max(s,i.distanceToSquared(rr))}this.boundingSphere.radius=Math.sqrt(s),isNaN(this.boundingSphere.radius)&&console.error('THREE.BufferGeometry.computeBoundingSphere(): Computed radius is NaN. The "position" attribute is likely to have NaN values.',this)}}computeTangents(){let t=this.index,e=this.attributes;if(null===t||void 0===e.position||void 0===e.normal||void 0===e.uv)return void console.error("THREE.BufferGeometry: .computeTangents() failed. Missing required attributes (index, position, normal or uv)");let i=e.position,s=e.normal,r=e.uv;!1===this.hasAttribute("tangent")&&this.setAttribute("tangent",new s3(new Float32Array(4*i.count),4));let n=this.getAttribute("tangent"),a=[],o=[];for(let t=0;t<i.count;t++)a[t]=new iR,o[t]=new iR;let h=new iR,l=new iR,u=new iR,c=new ii,d=new ii,p=new ii,m=new iR,y=new iR,f=this.groups;0===f.length&&(f=[{start:0,count:t.count}]);for(let e=0,s=f.length;e<s;++e){let s=f[e],n=s.start,g=s.count;for(let e=n,s=n+g;e<s;e+=3)!function(t,e,s){h.fromBufferAttribute(i,t),l.fromBufferAttribute(i,e),u.fromBufferAttribute(i,s),c.fromBufferAttribute(r,t),d.fromBufferAttribute(r,e),p.fromBufferAttribute(r,s),l.sub(h),u.sub(h),d.sub(c),p.sub(c);let n=1/(d.x*p.y-p.x*d.y);isFinite(n)&&(m.copy(l).multiplyScalar(p.y).addScaledVector(u,-d.y).multiplyScalar(n),y.copy(u).multiplyScalar(d.x).addScaledVector(l,-p.x).multiplyScalar(n),a[t].add(m),a[e].add(m),a[s].add(m),o[t].add(y),o[e].add(y),o[s].add(y))}(t.getX(e+0),t.getX(e+1),t.getX(e+2))}let g=new iR,x=new iR,v=new iR,b=new iR;function M(t){v.fromBufferAttribute(s,t),b.copy(v);let e=a[t];g.copy(e),g.sub(v.multiplyScalar(v.dot(e))).normalize(),x.crossVectors(b,e);let i=x.dot(o[t]);n.setXYZW(t,g.x,g.y,g.z,i<0?-1:1)}for(let e=0,i=f.length;e<i;++e){let i=f[e],s=i.start,r=i.count;for(let e=s,i=s+r;e<i;e+=3)M(t.getX(e+0)),M(t.getX(e+1)),M(t.getX(e+2))}}computeVertexNormals(){let t=this.index,e=this.getAttribute("position");if(void 0!==e){let i=this.getAttribute("normal");if(void 0===i)i=new s3(new Float32Array(3*e.count),3),this.setAttribute("normal",i);else for(let t=0,e=i.count;t<e;t++)i.setXYZ(t,0,0,0);let s=new iR,r=new iR,n=new iR,a=new iR,o=new iR,h=new iR,l=new iR,u=new iR;if(t)for(let c=0,d=t.count;c<d;c+=3){let d=t.getX(c+0),p=t.getX(c+1),m=t.getX(c+2);s.fromBufferAttribute(e,d),r.fromBufferAttribute(e,p),n.fromBufferAttribute(e,m),l.subVectors(n,r),u.subVectors(s,r),l.cross(u),a.fromBufferAttribute(i,d),o.fromBufferAttribute(i,p),h.fromBufferAttribute(i,m),a.add(l),o.add(l),h.add(l),i.setXYZ(d,a.x,a.y,a.z),i.setXYZ(p,o.x,o.y,o.z),i.setXYZ(m,h.x,h.y,h.z)}else for(let t=0,a=e.count;t<a;t+=3)s.fromBufferAttribute(e,t+0),r.fromBufferAttribute(e,t+1),n.fromBufferAttribute(e,t+2),l.subVectors(n,r),u.subVectors(s,r),l.cross(u),i.setXYZ(t+0,l.x,l.y,l.z),i.setXYZ(t+1,l.x,l.y,l.z),i.setXYZ(t+2,l.x,l.y,l.z);this.normalizeNormals(),i.needsUpdate=!0}}normalizeNormals(){let t=this.attributes.normal;for(let e=0,i=t.count;e<i;e++)rr.fromBufferAttribute(t,e),rr.normalize(),t.setXYZ(e,rr.x,rr.y,rr.z)}toNonIndexed(){function t(t,e){let i=t.array,s=t.itemSize,r=t.normalized,n=new i.constructor(e.length*s),a=0,o=0;for(let r=0,h=e.length;r<h;r++){a=t.isInterleavedBufferAttribute?e[r]*t.data.stride+t.offset:e[r]*s;for(let t=0;t<s;t++)n[o++]=i[a++]}return new s3(n,s,r)}if(null===this.index)return console.warn("THREE.BufferGeometry.toNonIndexed(): BufferGeometry is already non-indexed."),this;let e=new rn,i=this.index.array,s=this.attributes;for(let r in s){let n=t(s[r],i);e.setAttribute(r,n)}let r=this.morphAttributes;for(let s in r){let n=[],a=r[s];for(let e=0,s=a.length;e<s;e++){let s=t(a[e],i);n.push(s)}e.morphAttributes[s]=n}e.morphTargetsRelative=this.morphTargetsRelative;let n=this.groups;for(let t=0,i=n.length;t<i;t++){let i=n[t];e.addGroup(i.start,i.count,i.materialIndex)}return e}toJSON(){let t={metadata:{version:4.6,type:"BufferGeometry",generator:"BufferGeometry.toJSON"}};if(t.uuid=this.uuid,t.type=this.type,""!==this.name&&(t.name=this.name),Object.keys(this.userData).length>0&&(t.userData=this.userData),void 0!==this.parameters){let e=this.parameters;for(let i in e)void 0!==e[i]&&(t[i]=e[i]);return t}t.data={attributes:{}};let e=this.index;null!==e&&(t.data.index={type:e.array.constructor.name,array:Array.prototype.slice.call(e.array)});let i=this.attributes;for(let e in i){let s=i[e];t.data.attributes[e]=s.toJSON(t.data)}let s={},r=!1;for(let e in this.morphAttributes){let i=this.morphAttributes[e],n=[];for(let e=0,s=i.length;e<s;e++){let s=i[e];n.push(s.toJSON(t.data))}n.length>0&&(s[e]=n,r=!0)}r&&(t.data.morphAttributes=s,t.data.morphTargetsRelative=this.morphTargetsRelative);let n=this.groups;n.length>0&&(t.data.groups=JSON.parse(JSON.stringify(n)));let a=this.boundingSphere;return null!==a&&(t.data.boundingSphere={center:a.center.toArray(),radius:a.radius}),t}clone(){return new this.constructor().copy(this)}copy(t){this.index=null,this.attributes={},this.morphAttributes={},this.groups=[],this.boundingBox=null,this.boundingSphere=null;let e={};this.name=t.name;let i=t.index;null!==i&&this.setIndex(i.clone());let s=t.attributes;for(let t in s){let i=s[t];this.setAttribute(t,i.clone(e))}let r=t.morphAttributes;for(let t in r){let i=[],s=r[t];for(let t=0,r=s.length;t<r;t++)i.push(s[t].clone(e));this.morphAttributes[t]=i}this.morphTargetsRelative=t.morphTargetsRelative;let n=t.groups;for(let t=0,e=n.length;t<e;t++){let e=n[t];this.addGroup(e.start,e.count,e.materialIndex)}let a=t.boundingBox;null!==a&&(this.boundingBox=a.clone());let o=t.boundingSphere;return null!==o&&(this.boundingSphere=o.clone()),this.drawRange.start=t.drawRange.start,this.drawRange.count=t.drawRange.count,this.userData=t.userData,this}dispose(){this.dispatchEvent({type:"dispose"})}constructor(){super(),this.isBufferGeometry=!0,Object.defineProperty(this,"id",{value:s9++}),this.uuid=e4(),this.name="",this.type="BufferGeometry",this.index=null,this.indirect=null,this.attributes={},this.morphAttributes={},this.morphTargetsRelative=!1,this.groups=[],this.boundingBox=null,this.boundingSphere=null,this.drawRange={start:0,count:1/0},this.userData={}}}let ra=new st,ro=new i7,rh=new i1,rl=new iR,ru=new iR,rc=new iR,rd=new iR,rp=new iR,rm=new iR,ry=new iR,rf=new iR;class rg extends sT{copy(t,e){return super.copy(t,e),void 0!==t.morphTargetInfluences&&(this.morphTargetInfluences=t.morphTargetInfluences.slice()),void 0!==t.morphTargetDictionary&&(this.morphTargetDictionary=Object.assign({},t.morphTargetDictionary)),this.material=Array.isArray(t.material)?t.material.slice():t.material,this.geometry=t.geometry,this}updateMorphTargets(){let t=this.geometry.morphAttributes,e=Object.keys(t);if(e.length>0){let i=t[e[0]];if(void 0!==i){this.morphTargetInfluences=[],this.morphTargetDictionary={};for(let t=0,e=i.length;t<e;t++){let e=i[t].name||String(t);this.morphTargetInfluences.push(0),this.morphTargetDictionary[e]=t}}}}getVertexPosition(t,e){let i=this.geometry,s=i.attributes.position,r=i.morphAttributes.position,n=i.morphTargetsRelative;e.fromBufferAttribute(s,t);let a=this.morphTargetInfluences;if(r&&a){rm.set(0,0,0);for(let i=0,s=r.length;i<s;i++){let s=a[i],o=r[i];0!==s&&(rp.fromBufferAttribute(o,t),n?rm.addScaledVector(rp,s):rm.addScaledVector(rp.sub(e),s))}e.add(rm)}return e}raycast(t,e){let i=this.geometry,s=this.material,r=this.matrixWorld;if(void 0!==s)null===i.boundingSphere&&i.computeBoundingSphere(),rh.copy(i.boundingSphere),rh.applyMatrix4(r),ro.copy(t.ray).recast(t.near),!1===rh.containsPoint(ro.origin)&&(null===ro.intersectSphere(rh,rl)||ro.origin.distanceToSquared(rl)>(t.far-t.near)**2)||(ra.copy(r).invert(),ro.copy(t.ray).applyMatrix4(ra),(null===i.boundingBox||!1!==ro.intersectsBox(i.boundingBox))&&this._computeIntersections(t,e,ro))}_computeIntersections(t,e,i){let s,r=this.geometry,n=this.material,a=r.index,o=r.attributes.position,h=r.attributes.uv,l=r.attributes.uv1,u=r.attributes.normal,c=r.groups,d=r.drawRange;if(null!==a)if(Array.isArray(n))for(let r=0,o=c.length;r<o;r++){let o=c[r],p=n[o.materialIndex],m=Math.max(o.start,d.start),y=Math.min(a.count,Math.min(o.start+o.count,d.start+d.count));for(let r=m;r<y;r+=3){let n=a.getX(r);(s=rx(this,p,t,i,h,l,u,n,a.getX(r+1),a.getX(r+2)))&&(s.faceIndex=Math.floor(r/3),s.face.materialIndex=o.materialIndex,e.push(s))}}else{let r=Math.max(0,d.start),o=Math.min(a.count,d.start+d.count);for(let c=r;c<o;c+=3){let r=a.getX(c);(s=rx(this,n,t,i,h,l,u,r,a.getX(c+1),a.getX(c+2)))&&(s.faceIndex=Math.floor(c/3),e.push(s))}}else if(void 0!==o)if(Array.isArray(n))for(let r=0,a=c.length;r<a;r++){let a=c[r],p=n[a.materialIndex],m=Math.max(a.start,d.start),y=Math.min(o.count,Math.min(a.start+a.count,d.start+d.count));for(let r=m;r<y;r+=3)(s=rx(this,p,t,i,h,l,u,r,r+1,r+2))&&(s.faceIndex=Math.floor(r/3),s.face.materialIndex=a.materialIndex,e.push(s))}else{let r=Math.max(0,d.start),a=Math.min(o.count,d.start+d.count);for(let o=r;o<a;o+=3)(s=rx(this,n,t,i,h,l,u,o,o+1,o+2))&&(s.faceIndex=Math.floor(o/3),e.push(s))}}constructor(t=new rn,e=new sY){super(),this.isMesh=!0,this.type="Mesh",this.geometry=t,this.material=e,this.morphTargetDictionary=void 0,this.morphTargetInfluences=void 0,this.updateMorphTargets()}}function rx(t,e,i,s,r,n,a,o,h,l){t.getVertexPosition(o,ru),t.getVertexPosition(h,rc),t.getVertexPosition(l,rd);let u=function(t,e,i,s,r,n,a,o){let h;if(null===(e.side===f?s.intersectTriangle(a,n,r,!0,o):s.intersectTriangle(r,n,a,e.side===y,o)))return null;rf.copy(o),rf.applyMatrix4(t.matrixWorld);let l=i.ray.origin.distanceTo(rf);return l<i.near||l>i.far?null:{distance:l,point:rf.clone(),object:t}}(t,e,i,s,ru,rc,rd,ry);if(u){let t=new iR;sU.getBarycoord(ry,ru,rc,rd,t),r&&(u.uv=sU.getInterpolatedAttribute(r,o,h,l,t,new ii)),n&&(u.uv1=sU.getInterpolatedAttribute(n,o,h,l,t,new ii)),a&&(u.normal=sU.getInterpolatedAttribute(a,o,h,l,t,new iR),u.normal.dot(s.direction)>0&&u.normal.multiplyScalar(-1));let e={a:o,b:h,c:l,normal:new iR,materialIndex:0};sU.getNormal(ru,rc,rd,e.normal),u.face=e,u.barycoord=t}return u}class rv extends rn{copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new rv(t.width,t.height,t.depth,t.widthSegments,t.heightSegments,t.depthSegments)}constructor(t=1,e=1,i=1,s=1,r=1,n=1){super(),this.type="BoxGeometry",this.parameters={width:t,height:e,depth:i,widthSegments:s,heightSegments:r,depthSegments:n};let a=this;s=Math.floor(s),r=Math.floor(r);let o=[],h=[],l=[],u=[],c=0,d=0;function p(t,e,i,s,r,n,p,m,y,f,g){let x=n/y,v=p/f,b=n/2,M=p/2,w=m/2,S=y+1,_=f+1,A=0,z=0,T=new iR;for(let n=0;n<_;n++){let a=n*v-M;for(let o=0;o<S;o++){let c=o*x-b;T[t]=c*s,T[e]=a*r,T[i]=w,h.push(T.x,T.y,T.z),T[t]=0,T[e]=0,T[i]=m>0?1:-1,l.push(T.x,T.y,T.z),u.push(o/y),u.push(1-n/f),A+=1}}for(let t=0;t<f;t++)for(let e=0;e<y;e++){let i=c+e+S*t,s=c+e+S*(t+1),r=c+(e+1)+S*(t+1),n=c+(e+1)+S*t;o.push(i,s,n),o.push(s,r,n),z+=6}a.addGroup(d,z,g),d+=z,c+=A}p("z","y","x",-1,-1,i,e,t,n=Math.floor(n),r,0),p("z","y","x",1,-1,i,e,-t,n,r,1),p("x","z","y",1,1,t,i,e,s,n,2),p("x","z","y",1,-1,t,i,-e,s,n,3),p("x","y","z",1,-1,t,e,i,s,r,4),p("x","y","z",-1,-1,t,e,-i,s,r,5),this.setIndex(o),this.setAttribute("position",new s8(h,3)),this.setAttribute("normal",new s8(l,3)),this.setAttribute("uv",new s8(u,2))}}function rb(t){let e={};for(let i in t)for(let s in e[i]={},t[i]){let r=t[i][s];r&&(r.isColor||r.isMatrix3||r.isMatrix4||r.isVector2||r.isVector3||r.isVector4||r.isTexture||r.isQuaternion)?r.isRenderTargetTexture?(console.warn("UniformsUtils: Textures of render targets cannot be cloned via cloneUniforms() or mergeUniforms()."),e[i][s]=null):e[i][s]=r.clone():Array.isArray(r)?e[i][s]=r.slice():e[i][s]=r}return e}function rM(t){let e={};for(let i=0;i<t.length;i++){let s=rb(t[i]);for(let t in s)e[t]=s[t]}return e}function rw(t){let e=t.getRenderTarget();return null===e?t.outputColorSpace:!0===e.isXRRenderTarget?e.texture.colorSpace:iv.workingColorSpace}let rS={clone:rb,merge:rM};class r_ extends sZ{copy(t){return super.copy(t),this.fragmentShader=t.fragmentShader,this.vertexShader=t.vertexShader,this.uniforms=rb(t.uniforms),this.uniformsGroups=function(t){let e=[];for(let i=0;i<t.length;i++)e.push(t[i].clone());return e}(t.uniformsGroups),this.defines=Object.assign({},t.defines),this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this.fog=t.fog,this.lights=t.lights,this.clipping=t.clipping,this.extensions=Object.assign({},t.extensions),this.glslVersion=t.glslVersion,this}toJSON(t){let e=super.toJSON(t);for(let i in e.glslVersion=this.glslVersion,e.uniforms={},this.uniforms){let s=this.uniforms[i].value;s&&s.isTexture?e.uniforms[i]={type:"t",value:s.toJSON(t).uuid}:s&&s.isColor?e.uniforms[i]={type:"c",value:s.getHex()}:s&&s.isVector2?e.uniforms[i]={type:"v2",value:s.toArray()}:s&&s.isVector3?e.uniforms[i]={type:"v3",value:s.toArray()}:s&&s.isVector4?e.uniforms[i]={type:"v4",value:s.toArray()}:s&&s.isMatrix3?e.uniforms[i]={type:"m3",value:s.toArray()}:s&&s.isMatrix4?e.uniforms[i]={type:"m4",value:s.toArray()}:e.uniforms[i]={value:s}}Object.keys(this.defines).length>0&&(e.defines=this.defines),e.vertexShader=this.vertexShader,e.fragmentShader=this.fragmentShader,e.lights=this.lights,e.clipping=this.clipping;let i={};for(let t in this.extensions)!0===this.extensions[t]&&(i[t]=!0);return Object.keys(i).length>0&&(e.extensions=i),e}constructor(t){super(),this.isShaderMaterial=!0,this.type="ShaderMaterial",this.defines={},this.uniforms={},this.uniformsGroups=[],this.vertexShader="void main() {\n	gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n}",this.fragmentShader="void main() {\n	gl_FragColor = vec4( 1.0, 0.0, 0.0, 1.0 );\n}",this.linewidth=1,this.wireframe=!1,this.wireframeLinewidth=1,this.fog=!1,this.lights=!1,this.clipping=!1,this.forceSinglePass=!0,this.extensions={clipCullDistance:!1,multiDraw:!1},this.defaultAttributeValues={color:[1,1,1],uv:[0,0],uv1:[0,0]},this.index0AttributeName=void 0,this.uniformsNeedUpdate=!1,this.glslVersion=null,void 0!==t&&this.setValues(t)}}class rA extends sT{copy(t,e){return super.copy(t,e),this.matrixWorldInverse.copy(t.matrixWorldInverse),this.projectionMatrix.copy(t.projectionMatrix),this.projectionMatrixInverse.copy(t.projectionMatrixInverse),this.coordinateSystem=t.coordinateSystem,this}getWorldDirection(t){return super.getWorldDirection(t).negate()}updateMatrixWorld(t){super.updateMatrixWorld(t),this.matrixWorldInverse.copy(this.matrixWorld).invert()}updateWorldMatrix(t,e){super.updateWorldMatrix(t,e),this.matrixWorldInverse.copy(this.matrixWorld).invert()}clone(){return new this.constructor().copy(this)}constructor(){super(),this.isCamera=!0,this.type="Camera",this.matrixWorldInverse=new st,this.projectionMatrix=new st,this.projectionMatrixInverse=new st,this.coordinateSystem=eK}}let rz=new iR,rT=new ii,rI=new ii;class rC extends rA{copy(t,e){return super.copy(t,e),this.fov=t.fov,this.zoom=t.zoom,this.near=t.near,this.far=t.far,this.focus=t.focus,this.aspect=t.aspect,this.view=null===t.view?null:Object.assign({},t.view),this.filmGauge=t.filmGauge,this.filmOffset=t.filmOffset,this}setFocalLength(t){let e=.5*this.getFilmHeight()/t;this.fov=2*e5*Math.atan(e),this.updateProjectionMatrix()}getFocalLength(){let t=Math.tan(.5*e3*this.fov);return .5*this.getFilmHeight()/t}getEffectiveFOV(){return 2*e5*Math.atan(Math.tan(.5*e3*this.fov)/this.zoom)}getFilmWidth(){return this.filmGauge*Math.min(this.aspect,1)}getFilmHeight(){return this.filmGauge/Math.max(this.aspect,1)}getViewBounds(t,e,i){rz.set(-1,-1,.5).applyMatrix4(this.projectionMatrixInverse),e.set(rz.x,rz.y).multiplyScalar(-t/rz.z),rz.set(1,1,.5).applyMatrix4(this.projectionMatrixInverse),i.set(rz.x,rz.y).multiplyScalar(-t/rz.z)}getViewSize(t,e){return this.getViewBounds(t,rT,rI),e.subVectors(rI,rT)}setViewOffset(t,e,i,s,r,n){this.aspect=t/e,null===this.view&&(this.view={enabled:!0,fullWidth:1,fullHeight:1,offsetX:0,offsetY:0,width:1,height:1}),this.view.enabled=!0,this.view.fullWidth=t,this.view.fullHeight=e,this.view.offsetX=i,this.view.offsetY=s,this.view.width=r,this.view.height=n,this.updateProjectionMatrix()}clearViewOffset(){null!==this.view&&(this.view.enabled=!1),this.updateProjectionMatrix()}updateProjectionMatrix(){let t=this.near,e=t*Math.tan(.5*e3*this.fov)/this.zoom,i=2*e,s=this.aspect*i,r=-.5*s,n=this.view;if(null!==this.view&&this.view.enabled){let t=n.fullWidth,a=n.fullHeight;r+=n.offsetX*s/t,e-=n.offsetY*i/a,s*=n.width/t,i*=n.height/a}let a=this.filmOffset;0!==a&&(r+=t*a/this.getFilmWidth()),this.projectionMatrix.makePerspective(r,r+s,e,e-i,t,this.far,this.coordinateSystem),this.projectionMatrixInverse.copy(this.projectionMatrix).invert()}toJSON(t){let e=super.toJSON(t);return e.object.fov=this.fov,e.object.zoom=this.zoom,e.object.near=this.near,e.object.far=this.far,e.object.focus=this.focus,e.object.aspect=this.aspect,null!==this.view&&(e.object.view=Object.assign({},this.view)),e.object.filmGauge=this.filmGauge,e.object.filmOffset=this.filmOffset,e}constructor(t=50,e=1,i=.1,s=2e3){super(),this.isPerspectiveCamera=!0,this.type="PerspectiveCamera",this.fov=t,this.zoom=1,this.near=i,this.far=s,this.focus=10,this.aspect=e,this.view=null,this.filmGauge=35,this.filmOffset=0,this.updateProjectionMatrix()}}class rB extends sT{updateCoordinateSystem(){let t=this.coordinateSystem,e=this.children.concat(),[i,s,r,n,a,o]=e;for(let t of e)this.remove(t);if(t===eK)i.up.set(0,1,0),i.lookAt(1,0,0),s.up.set(0,1,0),s.lookAt(-1,0,0),r.up.set(0,0,-1),r.lookAt(0,1,0),n.up.set(0,0,1),n.lookAt(0,-1,0),a.up.set(0,1,0),a.lookAt(0,0,1),o.up.set(0,1,0),o.lookAt(0,0,-1);else if(t===e$)i.up.set(0,-1,0),i.lookAt(-1,0,0),s.up.set(0,-1,0),s.lookAt(1,0,0),r.up.set(0,0,1),r.lookAt(0,1,0),n.up.set(0,0,-1),n.lookAt(0,-1,0),a.up.set(0,-1,0),a.lookAt(0,0,1),o.up.set(0,-1,0),o.lookAt(0,0,-1);else throw Error("THREE.CubeCamera.updateCoordinateSystem(): Invalid coordinate system: "+t);for(let t of e)this.add(t),t.updateMatrixWorld()}update(t,e){null===this.parent&&this.updateMatrixWorld();let{renderTarget:i,activeMipmapLevel:s}=this;this.coordinateSystem!==t.coordinateSystem&&(this.coordinateSystem=t.coordinateSystem,this.updateCoordinateSystem());let[r,n,a,o,h,l]=this.children,u=t.getRenderTarget(),c=t.getActiveCubeFace(),d=t.getActiveMipmapLevel(),p=t.xr.enabled;t.xr.enabled=!1;let m=i.texture.generateMipmaps;i.texture.generateMipmaps=!1,t.setRenderTarget(i,0,s),t.render(e,r),t.setRenderTarget(i,1,s),t.render(e,n),t.setRenderTarget(i,2,s),t.render(e,a),t.setRenderTarget(i,3,s),t.render(e,o),t.setRenderTarget(i,4,s),t.render(e,h),i.texture.generateMipmaps=m,t.setRenderTarget(i,5,s),t.render(e,l),t.setRenderTarget(u,c,d),t.xr.enabled=p,i.texture.needsPMREMUpdate=!0}constructor(t,e,i){super(),this.type="CubeCamera",this.renderTarget=i,this.coordinateSystem=null,this.activeMipmapLevel=0;let s=new rC(-90,1,t,e);s.layers=this.layers,this.add(s);let r=new rC(-90,1,t,e);r.layers=this.layers,this.add(r);let n=new rC(-90,1,t,e);n.layers=this.layers,this.add(n);let a=new rC(-90,1,t,e);a.layers=this.layers,this.add(a);let o=new rC(-90,1,t,e);o.layers=this.layers,this.add(o);let h=new rC(-90,1,t,e);h.layers=this.layers,this.add(h)}}class rk extends iT{get images(){return this.image}set images(t){this.image=t}constructor(t=[],e=tc,i,s,r,n,a,o,h,l){super(t,e,i,s,r,n,a,o,h,l),this.isCubeTexture=!0,this.flipY=!1}}class rO extends iB{fromEquirectangularTexture(t,e){this.texture.type=e.type,this.texture.colorSpace=e.colorSpace,this.texture.generateMipmaps=e.generateMipmaps,this.texture.minFilter=e.minFilter,this.texture.magFilter=e.magFilter;let i=new rv(5,5,5),s=new r_({name:"CubemapFromEquirect",uniforms:rb({tEquirect:{value:null}}),vertexShader:"\n\n				varying vec3 vWorldDirection;\n\n				vec3 transformDirection( in vec3 dir, in mat4 matrix ) {\n\n					return normalize( ( matrix * vec4( dir, 0.0 ) ).xyz );\n\n				}\n\n				void main() {\n\n					vWorldDirection = transformDirection( position, modelMatrix );\n\n					#include <begin_vertex>\n					#include <project_vertex>\n\n				}\n			",fragmentShader:"\n\n				uniform sampler2D tEquirect;\n\n				varying vec3 vWorldDirection;\n\n				#include <common>\n\n				void main() {\n\n					vec3 direction = normalize( vWorldDirection );\n\n					vec2 sampleUV = equirectUv( direction );\n\n					gl_FragColor = texture2D( tEquirect, sampleUV );\n\n				}\n			",side:f,blending:x});s.uniforms.tEquirect.value=e;let r=new rg(i,s),n=e.minFilter;return e.minFilter===t_&&(e.minFilter=tw),new rB(1,10,this).update(t,r),e.minFilter=n,r.geometry.dispose(),r.material.dispose(),this}clear(t){let e=!(arguments.length>1)||void 0===arguments[1]||arguments[1],i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],s=!(arguments.length>3)||void 0===arguments[3]||arguments[3],r=t.getRenderTarget();for(let r=0;r<6;r++)t.setRenderTarget(this,r),t.clear(e,i,s);t.setRenderTarget(r)}constructor(t=1,e={}){super(t,t,e),this.isWebGLCubeRenderTarget=!0;let i={width:t,height:t,depth:1};this.texture=new rk([i,i,i,i,i,i],e.mapping,e.wrapS,e.wrapT,e.magFilter,e.minFilter,e.format,e.type,e.anisotropy,e.colorSpace),this.texture.isRenderTargetTexture=!0,this.texture.generateMipmaps=void 0!==e.generateMipmaps&&e.generateMipmaps,this.texture.minFilter=void 0!==e.minFilter?e.minFilter:tw}}class rE extends sT{constructor(){super(),this.isGroup=!0,this.type="Group"}}let rR={type:"move"};class rP{getHandSpace(){return null===this._hand&&(this._hand=new rE,this._hand.matrixAutoUpdate=!1,this._hand.visible=!1,this._hand.joints={},this._hand.inputState={pinching:!1}),this._hand}getTargetRaySpace(){return null===this._targetRay&&(this._targetRay=new rE,this._targetRay.matrixAutoUpdate=!1,this._targetRay.visible=!1,this._targetRay.hasLinearVelocity=!1,this._targetRay.linearVelocity=new iR,this._targetRay.hasAngularVelocity=!1,this._targetRay.angularVelocity=new iR),this._targetRay}getGripSpace(){return null===this._grip&&(this._grip=new rE,this._grip.matrixAutoUpdate=!1,this._grip.visible=!1,this._grip.hasLinearVelocity=!1,this._grip.linearVelocity=new iR,this._grip.hasAngularVelocity=!1,this._grip.angularVelocity=new iR),this._grip}dispatchEvent(t){return null!==this._targetRay&&this._targetRay.dispatchEvent(t),null!==this._grip&&this._grip.dispatchEvent(t),null!==this._hand&&this._hand.dispatchEvent(t),this}connect(t){if(t&&t.hand){let e=this._hand;if(e)for(let i of t.hand.values())this._getHandJoint(e,i)}return this.dispatchEvent({type:"connected",data:t}),this}disconnect(t){return this.dispatchEvent({type:"disconnected",data:t}),null!==this._targetRay&&(this._targetRay.visible=!1),null!==this._grip&&(this._grip.visible=!1),null!==this._hand&&(this._hand.visible=!1),this}update(t,e,i){let s=null,r=null,n=null,a=this._targetRay,o=this._grip,h=this._hand;if(t&&"visible-blurred"!==e.session.visibilityState){if(h&&t.hand){for(let s of(n=!0,t.hand.values())){let t=e.getJointPose(s,i),r=this._getHandJoint(h,s);null!==t&&(r.matrix.fromArray(t.transform.matrix),r.matrix.decompose(r.position,r.rotation,r.scale),r.matrixWorldNeedsUpdate=!0,r.jointRadius=t.radius),r.visible=null!==t}let s=h.joints["index-finger-tip"],r=h.joints["thumb-tip"],a=s.position.distanceTo(r.position);h.inputState.pinching&&a>.025?(h.inputState.pinching=!1,this.dispatchEvent({type:"pinchend",handedness:t.handedness,target:this})):!h.inputState.pinching&&a<=.015&&(h.inputState.pinching=!0,this.dispatchEvent({type:"pinchstart",handedness:t.handedness,target:this}))}else null!==o&&t.gripSpace&&null!==(r=e.getPose(t.gripSpace,i))&&(o.matrix.fromArray(r.transform.matrix),o.matrix.decompose(o.position,o.rotation,o.scale),o.matrixWorldNeedsUpdate=!0,r.linearVelocity?(o.hasLinearVelocity=!0,o.linearVelocity.copy(r.linearVelocity)):o.hasLinearVelocity=!1,r.angularVelocity?(o.hasAngularVelocity=!0,o.angularVelocity.copy(r.angularVelocity)):o.hasAngularVelocity=!1);null!==a&&(null===(s=e.getPose(t.targetRaySpace,i))&&null!==r&&(s=r),null!==s&&(a.matrix.fromArray(s.transform.matrix),a.matrix.decompose(a.position,a.rotation,a.scale),a.matrixWorldNeedsUpdate=!0,s.linearVelocity?(a.hasLinearVelocity=!0,a.linearVelocity.copy(s.linearVelocity)):a.hasLinearVelocity=!1,s.angularVelocity?(a.hasAngularVelocity=!0,a.angularVelocity.copy(s.angularVelocity)):a.hasAngularVelocity=!1,this.dispatchEvent(rR)))}return null!==a&&(a.visible=null!==s),null!==o&&(o.visible=null!==r),null!==h&&(h.visible=null!==n),this}_getHandJoint(t,e){if(void 0===t.joints[e.jointName]){let i=new rE;i.matrixAutoUpdate=!1,i.visible=!1,t.joints[e.jointName]=i,t.add(i)}return t.joints[e.jointName]}constructor(){this._targetRay=null,this._grip=null,this._hand=null}}class rN{clone(){return new rN(this.color,this.density)}toJSON(){return{type:"FogExp2",name:this.name,color:this.color.getHex(),density:this.density}}constructor(t,e=25e-5){this.isFogExp2=!0,this.name="",this.color=new sq(t),this.density=e}}class rV{clone(){return new rV(this.color,this.near,this.far)}toJSON(){return{type:"Fog",name:this.name,color:this.color.getHex(),near:this.near,far:this.far}}constructor(t,e=1,i=1e3){this.isFog=!0,this.name="",this.color=new sq(t),this.near=e,this.far=i}}class rL extends sT{copy(t,e){return super.copy(t,e),null!==t.background&&(this.background=t.background.clone()),null!==t.environment&&(this.environment=t.environment.clone()),null!==t.fog&&(this.fog=t.fog.clone()),this.backgroundBlurriness=t.backgroundBlurriness,this.backgroundIntensity=t.backgroundIntensity,this.backgroundRotation.copy(t.backgroundRotation),this.environmentIntensity=t.environmentIntensity,this.environmentRotation.copy(t.environmentRotation),null!==t.overrideMaterial&&(this.overrideMaterial=t.overrideMaterial.clone()),this.matrixAutoUpdate=t.matrixAutoUpdate,this}toJSON(t){let e=super.toJSON(t);return null!==this.fog&&(e.object.fog=this.fog.toJSON()),this.backgroundBlurriness>0&&(e.object.backgroundBlurriness=this.backgroundBlurriness),1!==this.backgroundIntensity&&(e.object.backgroundIntensity=this.backgroundIntensity),e.object.backgroundRotation=this.backgroundRotation.toArray(),1!==this.environmentIntensity&&(e.object.environmentIntensity=this.environmentIntensity),e.object.environmentRotation=this.environmentRotation.toArray(),e}constructor(){super(),this.isScene=!0,this.type="Scene",this.background=null,this.environment=null,this.fog=null,this.backgroundBlurriness=0,this.backgroundIntensity=1,this.backgroundRotation=new su,this.environmentIntensity=1,this.environmentRotation=new su,this.overrideMaterial=null,"undefined"!=typeof __THREE_DEVTOOLS__&&__THREE_DEVTOOLS__.dispatchEvent(new CustomEvent("observe",{detail:this}))}}class rF{onUploadCallback(){}set needsUpdate(t){!0===t&&this.version++}setUsage(t){return this.usage=t,this}addUpdateRange(t,e){this.updateRanges.push({start:t,count:e})}clearUpdateRanges(){this.updateRanges.length=0}copy(t){return this.array=new t.array.constructor(t.array),this.count=t.count,this.stride=t.stride,this.usage=t.usage,this}copyAt(t,e,i){t*=this.stride,i*=e.stride;for(let s=0,r=this.stride;s<r;s++)this.array[t+s]=e.array[i+s];return this}set(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return this.array.set(t,e),this}clone(t){void 0===t.arrayBuffers&&(t.arrayBuffers={}),void 0===this.array.buffer._uuid&&(this.array.buffer._uuid=e4()),void 0===t.arrayBuffers[this.array.buffer._uuid]&&(t.arrayBuffers[this.array.buffer._uuid]=this.array.slice(0).buffer);let e=new this.array.constructor(t.arrayBuffers[this.array.buffer._uuid]),i=new this.constructor(e,this.stride);return i.setUsage(this.usage),i}onUpload(t){return this.onUploadCallback=t,this}toJSON(t){return void 0===t.arrayBuffers&&(t.arrayBuffers={}),void 0===this.array.buffer._uuid&&(this.array.buffer._uuid=e4()),void 0===t.arrayBuffers[this.array.buffer._uuid]&&(t.arrayBuffers[this.array.buffer._uuid]=Array.from(new Uint32Array(this.array.buffer))),{uuid:this.uuid,buffer:this.array.buffer._uuid,type:this.array.constructor.name,stride:this.stride}}constructor(t,e){this.isInterleavedBuffer=!0,this.array=t,this.stride=e,this.count=void 0!==t?t.length/e:0,this.usage=eZ,this.updateRanges=[],this.version=0,this.uuid=e4()}}let rj=new iR;class rU{get count(){return this.data.count}get array(){return this.data.array}set needsUpdate(t){this.data.needsUpdate=t}applyMatrix4(t){for(let e=0,i=this.data.count;e<i;e++)rj.fromBufferAttribute(this,e),rj.applyMatrix4(t),this.setXYZ(e,rj.x,rj.y,rj.z);return this}applyNormalMatrix(t){for(let e=0,i=this.count;e<i;e++)rj.fromBufferAttribute(this,e),rj.applyNormalMatrix(t),this.setXYZ(e,rj.x,rj.y,rj.z);return this}transformDirection(t){for(let e=0,i=this.count;e<i;e++)rj.fromBufferAttribute(this,e),rj.transformDirection(t),this.setXYZ(e,rj.x,rj.y,rj.z);return this}getComponent(t,e){let i=this.array[t*this.data.stride+this.offset+e];return this.normalized&&(i=e7(i,this.array)),i}setComponent(t,e,i){return this.normalized&&(i=it(i,this.array)),this.data.array[t*this.data.stride+this.offset+e]=i,this}setX(t,e){return this.normalized&&(e=it(e,this.array)),this.data.array[t*this.data.stride+this.offset]=e,this}setY(t,e){return this.normalized&&(e=it(e,this.array)),this.data.array[t*this.data.stride+this.offset+1]=e,this}setZ(t,e){return this.normalized&&(e=it(e,this.array)),this.data.array[t*this.data.stride+this.offset+2]=e,this}setW(t,e){return this.normalized&&(e=it(e,this.array)),this.data.array[t*this.data.stride+this.offset+3]=e,this}getX(t){let e=this.data.array[t*this.data.stride+this.offset];return this.normalized&&(e=e7(e,this.array)),e}getY(t){let e=this.data.array[t*this.data.stride+this.offset+1];return this.normalized&&(e=e7(e,this.array)),e}getZ(t){let e=this.data.array[t*this.data.stride+this.offset+2];return this.normalized&&(e=e7(e,this.array)),e}getW(t){let e=this.data.array[t*this.data.stride+this.offset+3];return this.normalized&&(e=e7(e,this.array)),e}setXY(t,e,i){return t=t*this.data.stride+this.offset,this.normalized&&(e=it(e,this.array),i=it(i,this.array)),this.data.array[t+0]=e,this.data.array[t+1]=i,this}setXYZ(t,e,i,s){return t=t*this.data.stride+this.offset,this.normalized&&(e=it(e,this.array),i=it(i,this.array),s=it(s,this.array)),this.data.array[t+0]=e,this.data.array[t+1]=i,this.data.array[t+2]=s,this}setXYZW(t,e,i,s,r){return t=t*this.data.stride+this.offset,this.normalized&&(e=it(e,this.array),i=it(i,this.array),s=it(s,this.array),r=it(r,this.array)),this.data.array[t+0]=e,this.data.array[t+1]=i,this.data.array[t+2]=s,this.data.array[t+3]=r,this}clone(t){if(void 0!==t)return void 0===t.interleavedBuffers&&(t.interleavedBuffers={}),void 0===t.interleavedBuffers[this.data.uuid]&&(t.interleavedBuffers[this.data.uuid]=this.data.clone(t)),new rU(t.interleavedBuffers[this.data.uuid],this.itemSize,this.offset,this.normalized);{console.log("THREE.InterleavedBufferAttribute.clone(): Cloning an interleaved buffer attribute will de-interleave buffer data.");let t=[];for(let e=0;e<this.count;e++){let i=e*this.data.stride+this.offset;for(let e=0;e<this.itemSize;e++)t.push(this.data.array[i+e])}return new s3(new this.array.constructor(t),this.itemSize,this.normalized)}}toJSON(t){if(void 0!==t)return void 0===t.interleavedBuffers&&(t.interleavedBuffers={}),void 0===t.interleavedBuffers[this.data.uuid]&&(t.interleavedBuffers[this.data.uuid]=this.data.toJSON(t)),{isInterleavedBufferAttribute:!0,itemSize:this.itemSize,data:this.data.uuid,offset:this.offset,normalized:this.normalized};{console.log("THREE.InterleavedBufferAttribute.toJSON(): Serializing an interleaved buffer attribute will de-interleave buffer data.");let t=[];for(let e=0;e<this.count;e++){let i=e*this.data.stride+this.offset;for(let e=0;e<this.itemSize;e++)t.push(this.data.array[i+e])}return{itemSize:this.itemSize,type:this.array.constructor.name,array:t,normalized:this.normalized}}}constructor(t,e,i,s=!1){this.isInterleavedBufferAttribute=!0,this.name="",this.data=t,this.itemSize=e,this.offset=i,this.normalized=s}}class rD extends sZ{copy(t){return super.copy(t),this.color.copy(t.color),this.map=t.map,this.alphaMap=t.alphaMap,this.rotation=t.rotation,this.sizeAttenuation=t.sizeAttenuation,this.fog=t.fog,this}constructor(t){super(),this.isSpriteMaterial=!0,this.type="SpriteMaterial",this.color=new sq(0xffffff),this.map=null,this.alphaMap=null,this.rotation=0,this.sizeAttenuation=!0,this.transparent=!0,this.fog=!0,this.setValues(t)}}let rW=new iR,rH=new iR,rJ=new iR,rq=new ii,rG=new ii,rX=new st,rZ=new iR,rY=new iR,rQ=new iR,rK=new ii,r$=new ii,r0=new ii;class r1 extends sT{raycast(t,e){let i,s;null===t.camera&&console.error('THREE.Sprite: "Raycaster.camera" needs to be set in order to raycast against sprites.'),rH.setFromMatrixScale(this.matrixWorld),rX.copy(t.camera.matrixWorld),this.modelViewMatrix.multiplyMatrices(t.camera.matrixWorldInverse,this.matrixWorld),rJ.setFromMatrixPosition(this.modelViewMatrix),t.camera.isPerspectiveCamera&&!1===this.material.sizeAttenuation&&rH.multiplyScalar(-rJ.z);let r=this.material.rotation;0!==r&&(s=Math.cos(r),i=Math.sin(r));let n=this.center;r2(rZ.set(-.5,-.5,0),rJ,n,rH,i,s),r2(rY.set(.5,-.5,0),rJ,n,rH,i,s),r2(rQ.set(.5,.5,0),rJ,n,rH,i,s),rK.set(0,0),r$.set(1,0),r0.set(1,1);let a=t.ray.intersectTriangle(rZ,rY,rQ,!1,rW);if(null===a&&(r2(rY.set(-.5,.5,0),rJ,n,rH,i,s),r$.set(0,1),null===(a=t.ray.intersectTriangle(rZ,rQ,rY,!1,rW))))return;let o=t.ray.origin.distanceTo(rW);o<t.near||o>t.far||e.push({distance:o,point:rW.clone(),uv:sU.getInterpolation(rW,rZ,rY,rQ,rK,r$,r0,new ii),face:null,object:this})}copy(t,e){return super.copy(t,e),void 0!==t.center&&this.center.copy(t.center),this.material=t.material,this}constructor(t=new rD){if(super(),this.isSprite=!0,this.type="Sprite",void 0===r){r=new rn;let t=new rF(new Float32Array([-.5,-.5,0,0,0,.5,-.5,0,1,0,.5,.5,0,1,1,-.5,.5,0,0,1]),5);r.setIndex([0,1,2,0,2,3]),r.setAttribute("position",new rU(t,3,0,!1)),r.setAttribute("uv",new rU(t,2,3,!1))}this.geometry=r,this.material=t,this.center=new ii(.5,.5)}}function r2(t,e,i,s,r,n){rq.subVectors(t,i).addScalar(.5).multiply(s),void 0!==r?(rG.x=n*rq.x-r*rq.y,rG.y=r*rq.x+n*rq.y):rG.copy(rq),t.copy(e),t.x+=rG.x,t.y+=rG.y,t.applyMatrix4(rX)}let r3=new iR,r5=new iR;class r4 extends sT{copy(t){super.copy(t,!1);let e=t.levels;for(let t=0,i=e.length;t<i;t++){let i=e[t];this.addLevel(i.object.clone(),i.distance,i.hysteresis)}return this.autoUpdate=t.autoUpdate,this}addLevel(t){let e,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;i=Math.abs(i);let r=this.levels;for(e=0;e<r.length&&!(i<r[e].distance);e++);return r.splice(e,0,{distance:i,hysteresis:s,object:t}),this.add(t),this}removeLevel(t){let e=this.levels;for(let i=0;i<e.length;i++)if(e[i].distance===t){let t=e.splice(i,1);return this.remove(t[0].object),!0}return!1}getCurrentLevel(){return this._currentLevel}getObjectForDistance(t){let e=this.levels;if(e.length>0){let i,s;for(i=1,s=e.length;i<s;i++){let s=e[i].distance;if(e[i].object.visible&&(s-=s*e[i].hysteresis),t<s)break}return e[i-1].object}return null}raycast(t,e){if(this.levels.length>0){r3.setFromMatrixPosition(this.matrixWorld);let i=t.ray.origin.distanceTo(r3);this.getObjectForDistance(i).raycast(t,e)}}update(t){let e=this.levels;if(e.length>1){let i,s;r3.setFromMatrixPosition(t.matrixWorld),r5.setFromMatrixPosition(this.matrixWorld);let r=r3.distanceTo(r5)/t.zoom;for(i=1,e[0].object.visible=!0,s=e.length;i<s;i++){let t=e[i].distance;if(e[i].object.visible&&(t-=t*e[i].hysteresis),r>=t)e[i-1].object.visible=!1,e[i].object.visible=!0;else break}for(this._currentLevel=i-1;i<s;i++)e[i].object.visible=!1}}toJSON(t){let e=super.toJSON(t);!1===this.autoUpdate&&(e.object.autoUpdate=!1),e.object.levels=[];let i=this.levels;for(let t=0,s=i.length;t<s;t++){let s=i[t];e.object.levels.push({object:s.object.uuid,distance:s.distance,hysteresis:s.hysteresis})}return e}constructor(){super(),this.isLOD=!0,this._currentLevel=0,this.type="LOD",Object.defineProperties(this,{levels:{enumerable:!0,value:[]}}),this.autoUpdate=!0}}let r6=new iR,r8=new iI,r9=new iI,r7=new iR,nt=new st,ne=new iR,ni=new i1,ns=new st,nr=new i7;class nn extends rg{computeBoundingBox(){let t=this.geometry;null===this.boundingBox&&(this.boundingBox=new iV),this.boundingBox.makeEmpty();let e=t.getAttribute("position");for(let t=0;t<e.count;t++)this.getVertexPosition(t,ne),this.boundingBox.expandByPoint(ne)}computeBoundingSphere(){let t=this.geometry;null===this.boundingSphere&&(this.boundingSphere=new i1),this.boundingSphere.makeEmpty();let e=t.getAttribute("position");for(let t=0;t<e.count;t++)this.getVertexPosition(t,ne),this.boundingSphere.expandByPoint(ne)}copy(t,e){return super.copy(t,e),this.bindMode=t.bindMode,this.bindMatrix.copy(t.bindMatrix),this.bindMatrixInverse.copy(t.bindMatrixInverse),this.skeleton=t.skeleton,null!==t.boundingBox&&(this.boundingBox=t.boundingBox.clone()),null!==t.boundingSphere&&(this.boundingSphere=t.boundingSphere.clone()),this}raycast(t,e){let i=this.material,s=this.matrixWorld;if(void 0!==i)null===this.boundingSphere&&this.computeBoundingSphere(),ni.copy(this.boundingSphere),ni.applyMatrix4(s),!1!==t.ray.intersectsSphere(ni)&&(ns.copy(s).invert(),nr.copy(t.ray).applyMatrix4(ns),(null===this.boundingBox||!1!==nr.intersectsBox(this.boundingBox))&&this._computeIntersections(t,e,nr))}getVertexPosition(t,e){return super.getVertexPosition(t,e),this.applyBoneTransform(t,e),e}bind(t,e){this.skeleton=t,void 0===e&&(this.updateMatrixWorld(!0),this.skeleton.calculateInverses(),e=this.matrixWorld),this.bindMatrix.copy(e),this.bindMatrixInverse.copy(e).invert()}pose(){this.skeleton.pose()}normalizeSkinWeights(){let t=new iI,e=this.geometry.attributes.skinWeight;for(let i=0,s=e.count;i<s;i++){t.fromBufferAttribute(e,i);let s=1/t.manhattanLength();s!==1/0?t.multiplyScalar(s):t.set(1,0,0,0),e.setXYZW(i,t.x,t.y,t.z,t.w)}}updateMatrixWorld(t){super.updateMatrixWorld(t),this.bindMode===tl?this.bindMatrixInverse.copy(this.matrixWorld).invert():"detached"===this.bindMode?this.bindMatrixInverse.copy(this.bindMatrix).invert():console.warn("THREE.SkinnedMesh: Unrecognized bindMode: "+this.bindMode)}applyBoneTransform(t,e){let i=this.skeleton,s=this.geometry;r8.fromBufferAttribute(s.attributes.skinIndex,t),r9.fromBufferAttribute(s.attributes.skinWeight,t),r6.copy(e).applyMatrix4(this.bindMatrix),e.set(0,0,0);for(let t=0;t<4;t++){let s=r9.getComponent(t);if(0!==s){let r=r8.getComponent(t);nt.multiplyMatrices(i.bones[r].matrixWorld,i.boneInverses[r]),e.addScaledVector(r7.copy(r6).applyMatrix4(nt),s)}}return e.applyMatrix4(this.bindMatrixInverse)}constructor(t,e){super(t,e),this.isSkinnedMesh=!0,this.type="SkinnedMesh",this.bindMode=tl,this.bindMatrix=new st,this.bindMatrixInverse=new st,this.boundingBox=null,this.boundingSphere=null}}class na extends sT{constructor(){super(),this.isBone=!0,this.type="Bone"}}class no extends iT{constructor(t=null,e=1,i=1,s,r,n,a,o,h=tv,l=tv,u,c){super(null,n,a,o,h,l,s,r,u,c),this.isDataTexture=!0,this.image={data:t,width:e,height:i},this.generateMipmaps=!1,this.flipY=!1,this.unpackAlignment=1}}let nh=new st,nl=new st;class nu{init(){let t=this.bones,e=this.boneInverses;if(this.boneMatrices=new Float32Array(16*t.length),0===e.length)this.calculateInverses();else if(t.length!==e.length){console.warn("THREE.Skeleton: Number of inverse bone matrices does not match amount of bones."),this.boneInverses=[];for(let t=0,e=this.bones.length;t<e;t++)this.boneInverses.push(new st)}}calculateInverses(){this.boneInverses.length=0;for(let t=0,e=this.bones.length;t<e;t++){let e=new st;this.bones[t]&&e.copy(this.bones[t].matrixWorld).invert(),this.boneInverses.push(e)}}pose(){for(let t=0,e=this.bones.length;t<e;t++){let e=this.bones[t];e&&e.matrixWorld.copy(this.boneInverses[t]).invert()}for(let t=0,e=this.bones.length;t<e;t++){let e=this.bones[t];e&&(e.parent&&e.parent.isBone?(e.matrix.copy(e.parent.matrixWorld).invert(),e.matrix.multiply(e.matrixWorld)):e.matrix.copy(e.matrixWorld),e.matrix.decompose(e.position,e.quaternion,e.scale))}}update(){let t=this.bones,e=this.boneInverses,i=this.boneMatrices,s=this.boneTexture;for(let s=0,r=t.length;s<r;s++){let r=t[s]?t[s].matrixWorld:nl;nh.multiplyMatrices(r,e[s]),nh.toArray(i,16*s)}null!==s&&(s.needsUpdate=!0)}clone(){return new nu(this.bones,this.boneInverses)}computeBoneTexture(){let t=Math.sqrt(4*this.bones.length),e=new Float32Array((t=Math.max(t=4*Math.ceil(t/4),4))*t*4);e.set(this.boneMatrices);let i=new no(e,t,t,tj,tO);return i.needsUpdate=!0,this.boneMatrices=e,this.boneTexture=i,this}getBoneByName(t){for(let e=0,i=this.bones.length;e<i;e++){let i=this.bones[e];if(i.name===t)return i}}dispose(){null!==this.boneTexture&&(this.boneTexture.dispose(),this.boneTexture=null)}fromJSON(t,e){this.uuid=t.uuid;for(let i=0,s=t.bones.length;i<s;i++){let s=t.bones[i],r=e[s];void 0===r&&(console.warn("THREE.Skeleton: No bone found with UUID:",s),r=new na),this.bones.push(r),this.boneInverses.push(new st().fromArray(t.boneInverses[i]))}return this.init(),this}toJSON(){let t={metadata:{version:4.6,type:"Skeleton",generator:"Skeleton.toJSON"},bones:[],boneInverses:[]};t.uuid=this.uuid;let e=this.bones,i=this.boneInverses;for(let s=0,r=e.length;s<r;s++){let r=e[s];t.bones.push(r.uuid);let n=i[s];t.boneInverses.push(n.toArray())}return t}constructor(t=[],e=[]){this.uuid=e4(),this.bones=t.slice(0),this.boneInverses=e,this.boneMatrices=null,this.boneTexture=null,this.init()}}class nc extends s3{copy(t){return super.copy(t),this.meshPerAttribute=t.meshPerAttribute,this}toJSON(){let t=super.toJSON();return t.meshPerAttribute=this.meshPerAttribute,t.isInstancedBufferAttribute=!0,t}constructor(t,e,i,s=1){super(t,e,i),this.isInstancedBufferAttribute=!0,this.meshPerAttribute=s}}let nd=new st,np=new st,nm=[],ny=new iV,nf=new st,ng=new rg,nx=new i1;class nv extends rg{computeBoundingBox(){let t=this.geometry,e=this.count;null===this.boundingBox&&(this.boundingBox=new iV),null===t.boundingBox&&t.computeBoundingBox(),this.boundingBox.makeEmpty();for(let i=0;i<e;i++)this.getMatrixAt(i,nd),ny.copy(t.boundingBox).applyMatrix4(nd),this.boundingBox.union(ny)}computeBoundingSphere(){let t=this.geometry,e=this.count;null===this.boundingSphere&&(this.boundingSphere=new i1),null===t.boundingSphere&&t.computeBoundingSphere(),this.boundingSphere.makeEmpty();for(let i=0;i<e;i++)this.getMatrixAt(i,nd),nx.copy(t.boundingSphere).applyMatrix4(nd),this.boundingSphere.union(nx)}copy(t,e){return super.copy(t,e),this.instanceMatrix.copy(t.instanceMatrix),null!==t.morphTexture&&(this.morphTexture=t.morphTexture.clone()),null!==t.instanceColor&&(this.instanceColor=t.instanceColor.clone()),this.count=t.count,null!==t.boundingBox&&(this.boundingBox=t.boundingBox.clone()),null!==t.boundingSphere&&(this.boundingSphere=t.boundingSphere.clone()),this}getColorAt(t,e){e.fromArray(this.instanceColor.array,3*t)}getMatrixAt(t,e){e.fromArray(this.instanceMatrix.array,16*t)}getMorphAt(t,e){let i=e.morphTargetInfluences,s=this.morphTexture.source.data.data,r=t*(i.length+1)+1;for(let t=0;t<i.length;t++)i[t]=s[r+t]}raycast(t,e){let i=this.matrixWorld,s=this.count;if((ng.geometry=this.geometry,ng.material=this.material,void 0!==ng.material)&&(null===this.boundingSphere&&this.computeBoundingSphere(),nx.copy(this.boundingSphere),nx.applyMatrix4(i),!1!==t.ray.intersectsSphere(nx)))for(let r=0;r<s;r++){this.getMatrixAt(r,nd),np.multiplyMatrices(i,nd),ng.matrixWorld=np,ng.raycast(t,nm);for(let t=0,i=nm.length;t<i;t++){let i=nm[t];i.instanceId=r,i.object=this,e.push(i)}nm.length=0}}setColorAt(t,e){null===this.instanceColor&&(this.instanceColor=new nc(new Float32Array(3*this.instanceMatrix.count).fill(1),3)),e.toArray(this.instanceColor.array,3*t)}setMatrixAt(t,e){e.toArray(this.instanceMatrix.array,16*t)}setMorphAt(t,e){let i=e.morphTargetInfluences,s=i.length+1;null===this.morphTexture&&(this.morphTexture=new no(new Float32Array(s*this.count),s,this.count,tW,tO));let r=this.morphTexture.source.data.data,n=0;for(let t=0;t<i.length;t++)n+=i[t];let a=this.geometry.morphTargetsRelative?1:1-n,o=s*t;r[o]=a,r.set(i,o+1)}updateMorphTargets(){}dispose(){this.dispatchEvent({type:"dispose"}),null!==this.morphTexture&&(this.morphTexture.dispose(),this.morphTexture=null)}constructor(t,e,i){super(t,e),this.isInstancedMesh=!0,this.instanceMatrix=new nc(new Float32Array(16*i),16),this.instanceColor=null,this.morphTexture=null,this.count=i,this.boundingBox=null,this.boundingSphere=null;for(let t=0;t<i;t++)this.setMatrixAt(t,nf)}}let nb=new iR,nM=new iR,nw=new is;class nS{set(t,e){return this.normal.copy(t),this.constant=e,this}setComponents(t,e,i,s){return this.normal.set(t,e,i),this.constant=s,this}setFromNormalAndCoplanarPoint(t,e){return this.normal.copy(t),this.constant=-e.dot(this.normal),this}setFromCoplanarPoints(t,e,i){let s=nb.subVectors(i,e).cross(nM.subVectors(t,e)).normalize();return this.setFromNormalAndCoplanarPoint(s,t),this}copy(t){return this.normal.copy(t.normal),this.constant=t.constant,this}normalize(){let t=1/this.normal.length();return this.normal.multiplyScalar(t),this.constant*=t,this}negate(){return this.constant*=-1,this.normal.negate(),this}distanceToPoint(t){return this.normal.dot(t)+this.constant}distanceToSphere(t){return this.distanceToPoint(t.center)-t.radius}projectPoint(t,e){return e.copy(t).addScaledVector(this.normal,-this.distanceToPoint(t))}intersectLine(t,e){let i=t.delta(nb),s=this.normal.dot(i);if(0===s)return 0===this.distanceToPoint(t.start)?e.copy(t.start):null;let r=-(t.start.dot(this.normal)+this.constant)/s;return r<0||r>1?null:e.copy(t.start).addScaledVector(i,r)}intersectsLine(t){let e=this.distanceToPoint(t.start),i=this.distanceToPoint(t.end);return e<0&&i>0||i<0&&e>0}intersectsBox(t){return t.intersectsPlane(this)}intersectsSphere(t){return t.intersectsPlane(this)}coplanarPoint(t){return t.copy(this.normal).multiplyScalar(-this.constant)}applyMatrix4(t,e){let i=e||nw.getNormalMatrix(t),s=this.coplanarPoint(nb).applyMatrix4(t),r=this.normal.applyMatrix3(i).normalize();return this.constant=-s.dot(r),this}translate(t){return this.constant-=t.dot(this.normal),this}equals(t){return t.normal.equals(this.normal)&&t.constant===this.constant}clone(){return new this.constructor().copy(this)}constructor(t=new iR(1,0,0),e=0){this.isPlane=!0,this.normal=t,this.constant=e}}let n_=new i1,nA=new iR;class nz{set(t,e,i,s,r,n){let a=this.planes;return a[0].copy(t),a[1].copy(e),a[2].copy(i),a[3].copy(s),a[4].copy(r),a[5].copy(n),this}copy(t){let e=this.planes;for(let i=0;i<6;i++)e[i].copy(t.planes[i]);return this}setFromProjectionMatrix(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:eK,i=this.planes,s=t.elements,r=s[0],n=s[1],a=s[2],o=s[3],h=s[4],l=s[5],u=s[6],c=s[7],d=s[8],p=s[9],m=s[10],y=s[11],f=s[12],g=s[13],x=s[14],v=s[15];if(i[0].setComponents(o-r,c-h,y-d,v-f).normalize(),i[1].setComponents(o+r,c+h,y+d,v+f).normalize(),i[2].setComponents(o+n,c+l,y+p,v+g).normalize(),i[3].setComponents(o-n,c-l,y-p,v-g).normalize(),i[4].setComponents(o-a,c-u,y-m,v-x).normalize(),e===eK)i[5].setComponents(o+a,c+u,y+m,v+x).normalize();else if(e===e$)i[5].setComponents(a,u,m,x).normalize();else throw Error("THREE.Frustum.setFromProjectionMatrix(): Invalid coordinate system: "+e);return this}intersectsObject(t){if(void 0!==t.boundingSphere)null===t.boundingSphere&&t.computeBoundingSphere(),n_.copy(t.boundingSphere).applyMatrix4(t.matrixWorld);else{let e=t.geometry;null===e.boundingSphere&&e.computeBoundingSphere(),n_.copy(e.boundingSphere).applyMatrix4(t.matrixWorld)}return this.intersectsSphere(n_)}intersectsSprite(t){return n_.center.set(0,0,0),n_.radius=.*********1865476,n_.applyMatrix4(t.matrixWorld),this.intersectsSphere(n_)}intersectsSphere(t){let e=this.planes,i=t.center,s=-t.radius;for(let t=0;t<6;t++)if(e[t].distanceToPoint(i)<s)return!1;return!0}intersectsBox(t){let e=this.planes;for(let i=0;i<6;i++){let s=e[i];if(nA.x=s.normal.x>0?t.max.x:t.min.x,nA.y=s.normal.y>0?t.max.y:t.min.y,nA.z=s.normal.z>0?t.max.z:t.min.z,0>s.distanceToPoint(nA))return!1}return!0}containsPoint(t){let e=this.planes;for(let i=0;i<6;i++)if(0>e[i].distanceToPoint(t))return!1;return!0}clone(){return new this.constructor().copy(this)}constructor(t=new nS,e=new nS,i=new nS,s=new nS,r=new nS,n=new nS){this.planes=[t,e,i,s,r,n]}}let nT=new st,nI=new nz;class nC{intersectsObject(t,e){if(!e.isArrayCamera||0===e.cameras.length)return!1;for(let i=0;i<e.cameras.length;i++){let s=e.cameras[i];if(nT.multiplyMatrices(s.projectionMatrix,s.matrixWorldInverse),nI.setFromProjectionMatrix(nT,this.coordinateSystem),nI.intersectsObject(t))return!0}return!1}intersectsSprite(t,e){if(!e||!e.cameras||0===e.cameras.length)return!1;for(let i=0;i<e.cameras.length;i++){let s=e.cameras[i];if(nT.multiplyMatrices(s.projectionMatrix,s.matrixWorldInverse),nI.setFromProjectionMatrix(nT,this.coordinateSystem),nI.intersectsSprite(t))return!0}return!1}intersectsSphere(t,e){if(!e||!e.cameras||0===e.cameras.length)return!1;for(let i=0;i<e.cameras.length;i++){let s=e.cameras[i];if(nT.multiplyMatrices(s.projectionMatrix,s.matrixWorldInverse),nI.setFromProjectionMatrix(nT,this.coordinateSystem),nI.intersectsSphere(t))return!0}return!1}intersectsBox(t,e){if(!e||!e.cameras||0===e.cameras.length)return!1;for(let i=0;i<e.cameras.length;i++){let s=e.cameras[i];if(nT.multiplyMatrices(s.projectionMatrix,s.matrixWorldInverse),nI.setFromProjectionMatrix(nT,this.coordinateSystem),nI.intersectsBox(t))return!0}return!1}containsPoint(t,e){if(!e||!e.cameras||0===e.cameras.length)return!1;for(let i=0;i<e.cameras.length;i++){let s=e.cameras[i];if(nT.multiplyMatrices(s.projectionMatrix,s.matrixWorldInverse),nI.setFromProjectionMatrix(nT,this.coordinateSystem),nI.containsPoint(t))return!0}return!1}clone(){return new nC}constructor(){this.coordinateSystem=eK}}function nB(t,e){return t-e}function nk(t,e){return t.z-e.z}function nO(t,e){return e.z-t.z}class nE{push(t,e,i,s){let r=this.pool,n=this.list;this.index>=r.length&&r.push({start:-1,count:-1,z:-1,index:-1});let a=r[this.index];n.push(a),this.index++,a.start=t,a.count=e,a.z=i,a.index=s}reset(){this.list.length=0,this.index=0}constructor(){this.index=0,this.pool=[],this.list=[]}}let nR=new st,nP=new sq(1,1,1),nN=new nz,nV=new nC,nL=new iV,nF=new i1,nj=new iR,nU=new iR,nD=new iR,nW=new nE,nH=new rg,nJ=[];function nq(t,e){if(t.constructor!==e.constructor){let i=Math.min(t.length,e.length);for(let s=0;s<i;s++)e[s]=t[s]}else{let i=Math.min(t.length,e.length);e.set(new t.constructor(t.buffer,0,i))}}class nG extends rg{get maxInstanceCount(){return this._maxInstanceCount}get instanceCount(){return this._instanceInfo.length-this._availableInstanceIds.length}get unusedVertexCount(){return this._maxVertexCount-this._nextVertexStart}get unusedIndexCount(){return this._maxIndexCount-this._nextIndexStart}_initMatricesTexture(){let t=Math.sqrt(4*this._maxInstanceCount),e=new no(new Float32Array((t=Math.max(t=4*Math.ceil(t/4),4))*t*4),t,t,tj,tO);this._matricesTexture=e}_initIndirectTexture(){let t=Math.sqrt(this._maxInstanceCount),e=new no(new Uint32Array((t=Math.ceil(t))*t),t,t,tH,tk);this._indirectTexture=e}_initColorsTexture(){let t=Math.sqrt(this._maxInstanceCount),e=new no(new Float32Array((t=Math.ceil(t))*t*4).fill(1),t,t,tj,tO);e.colorSpace=iv.workingColorSpace,this._colorsTexture=e}_initializeGeometry(t){let e=this.geometry,i=this._maxVertexCount,s=this._maxIndexCount;if(!1===this._geometryInitialized){for(let s in t.attributes){let{array:r,itemSize:n,normalized:a}=t.getAttribute(s),o=new s3(new r.constructor(i*n),n,a);e.setAttribute(s,o)}if(null!==t.getIndex()){let t=i>65535?new Uint32Array(s):new Uint16Array(s);e.setIndex(new s3(t,1))}this._geometryInitialized=!0}}_validateGeometry(t){let e=this.geometry;if(!!t.getIndex()!=!!e.getIndex())throw Error('THREE.BatchedMesh: All geometries must consistently have "index".');for(let i in e.attributes){if(!t.hasAttribute(i))throw Error('THREE.BatchedMesh: Added geometry missing "'.concat(i,'". All geometries must have consistent attributes.'));let s=t.getAttribute(i),r=e.getAttribute(i);if(s.itemSize!==r.itemSize||s.normalized!==r.normalized)throw Error("THREE.BatchedMesh: All attributes must have a consistent itemSize and normalized value.")}}validateInstanceId(t){let e=this._instanceInfo;if(t<0||t>=e.length||!1===e[t].active)throw Error("THREE.BatchedMesh: Invalid instanceId ".concat(t,". Instance is either out of range or has been deleted."))}validateGeometryId(t){let e=this._geometryInfo;if(t<0||t>=e.length||!1===e[t].active)throw Error("THREE.BatchedMesh: Invalid geometryId ".concat(t,". Geometry is either out of range or has been deleted."))}setCustomSort(t){return this.customSort=t,this}computeBoundingBox(){null===this.boundingBox&&(this.boundingBox=new iV);let t=this.boundingBox,e=this._instanceInfo;t.makeEmpty();for(let i=0,s=e.length;i<s;i++){if(!1===e[i].active)continue;let s=e[i].geometryIndex;this.getMatrixAt(i,nR),this.getBoundingBoxAt(s,nL).applyMatrix4(nR),t.union(nL)}}computeBoundingSphere(){null===this.boundingSphere&&(this.boundingSphere=new i1);let t=this.boundingSphere,e=this._instanceInfo;t.makeEmpty();for(let i=0,s=e.length;i<s;i++){if(!1===e[i].active)continue;let s=e[i].geometryIndex;this.getMatrixAt(i,nR),this.getBoundingSphereAt(s,nF).applyMatrix4(nR),t.union(nF)}}addInstance(t){if(this._instanceInfo.length>=this.maxInstanceCount&&0===this._availableInstanceIds.length)throw Error("THREE.BatchedMesh: Maximum item count reached.");let e={visible:!0,active:!0,geometryIndex:t},i=null;this._availableInstanceIds.length>0?(this._availableInstanceIds.sort(nB),i=this._availableInstanceIds.shift(),this._instanceInfo[i]=e):(i=this._instanceInfo.length,this._instanceInfo.push(e));let s=this._matricesTexture;nR.identity().toArray(s.image.data,16*i),s.needsUpdate=!0;let r=this._colorsTexture;return r&&(nP.toArray(r.image.data,4*i),r.needsUpdate=!0),this._visibilityChanged=!0,i}addGeometry(t){let e,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:-1,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:-1;this._initializeGeometry(t),this._validateGeometry(t);let r={vertexStart:-1,vertexCount:-1,reservedVertexCount:-1,indexStart:-1,indexCount:-1,reservedIndexCount:-1,start:-1,count:-1,boundingBox:null,boundingSphere:null,active:!0},n=this._geometryInfo;r.vertexStart=this._nextVertexStart,r.reservedVertexCount=-1===i?t.getAttribute("position").count:i;let a=t.getIndex();if(null!==a&&(r.indexStart=this._nextIndexStart,r.reservedIndexCount=-1===s?a.count:s),-1!==r.indexStart&&r.indexStart+r.reservedIndexCount>this._maxIndexCount||r.vertexStart+r.reservedVertexCount>this._maxVertexCount)throw Error("THREE.BatchedMesh: Reserved space request exceeds the maximum buffer size.");return this._availableGeometryIds.length>0?(this._availableGeometryIds.sort(nB),n[e=this._availableGeometryIds.shift()]=r):(e=this._geometryCount,this._geometryCount++,n.push(r)),this.setGeometryAt(e,t),this._nextIndexStart=r.indexStart+r.reservedIndexCount,this._nextVertexStart=r.vertexStart+r.reservedVertexCount,e}setGeometryAt(t,e){if(t>=this._geometryCount)throw Error("THREE.BatchedMesh: Maximum geometry count reached.");this._validateGeometry(e);let i=this.geometry,s=null!==i.getIndex(),r=i.getIndex(),n=e.getIndex(),a=this._geometryInfo[t];if(s&&n.count>a.reservedIndexCount||e.attributes.position.count>a.reservedVertexCount)throw Error("THREE.BatchedMesh: Reserved space not large enough for provided geometry.");let o=a.vertexStart,h=a.reservedVertexCount;for(let t in a.vertexCount=e.getAttribute("position").count,i.attributes){let s=e.getAttribute(t),r=i.getAttribute(t);!function(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,s=e.itemSize;if(t.isInterleavedBufferAttribute||t.array.constructor!==e.array.constructor){let r=t.count;for(let n=0;n<r;n++)for(let r=0;r<s;r++)e.setComponent(n+i,r,t.getComponent(n,r))}else e.array.set(t.array,i*s);e.needsUpdate=!0}(s,r,o);let n=s.itemSize;for(let t=s.count;t<h;t++){let e=o+t;for(let t=0;t<n;t++)r.setComponent(e,t,0)}r.needsUpdate=!0,r.addUpdateRange(o*n,h*n)}if(s){let t=a.indexStart,i=a.reservedIndexCount;a.indexCount=e.getIndex().count;for(let e=0;e<n.count;e++)r.setX(t+e,o+n.getX(e));for(let e=n.count;e<i;e++)r.setX(t+e,o);r.needsUpdate=!0,r.addUpdateRange(t,a.reservedIndexCount)}return a.start=s?a.indexStart:a.vertexStart,a.count=s?a.indexCount:a.vertexCount,a.boundingBox=null,null!==e.boundingBox&&(a.boundingBox=e.boundingBox.clone()),a.boundingSphere=null,null!==e.boundingSphere&&(a.boundingSphere=e.boundingSphere.clone()),this._visibilityChanged=!0,t}deleteGeometry(t){let e=this._geometryInfo;if(t>=e.length||!1===e[t].active)return this;let i=this._instanceInfo;for(let e=0,s=i.length;e<s;e++)i[e].active&&i[e].geometryIndex===t&&this.deleteInstance(e);return e[t].active=!1,this._availableGeometryIds.push(t),this._visibilityChanged=!0,this}deleteInstance(t){return this.validateInstanceId(t),this._instanceInfo[t].active=!1,this._availableInstanceIds.push(t),this._visibilityChanged=!0,this}optimize(){let t=0,e=0,i=this._geometryInfo,s=i.map((t,e)=>e).sort((t,e)=>i[t].vertexStart-i[e].vertexStart),r=this.geometry;for(let n=0,a=i.length;n<a;n++){let a=i[s[n]];if(!1!==a.active){if(null!==r.index){if(a.indexStart!==e){let{indexStart:i,vertexStart:s,reservedIndexCount:n}=a,o=r.index,h=o.array,l=t-s;for(let t=i;t<i+n;t++)h[t]=h[t]+l;o.array.copyWithin(e,i,i+n),o.addUpdateRange(e,n),a.indexStart=e}e+=a.reservedIndexCount}if(a.vertexStart!==t){let{vertexStart:e,reservedVertexCount:i}=a,s=r.attributes;for(let r in s){let n=s[r],{array:a,itemSize:o}=n;a.copyWithin(t*o,e*o,(e+i)*o),n.addUpdateRange(t*o,i*o)}a.vertexStart=t}t+=a.reservedVertexCount,a.start=r.index?a.indexStart:a.vertexStart,this._nextIndexStart=r.index?a.indexStart+a.reservedIndexCount:0,this._nextVertexStart=a.vertexStart+a.reservedVertexCount}}return this}getBoundingBoxAt(t,e){if(t>=this._geometryCount)return null;let i=this.geometry,s=this._geometryInfo[t];if(null===s.boundingBox){let t=new iV,e=i.index,r=i.attributes.position;for(let i=s.start,n=s.start+s.count;i<n;i++){let s=i;e&&(s=e.getX(s)),t.expandByPoint(nj.fromBufferAttribute(r,s))}s.boundingBox=t}return e.copy(s.boundingBox),e}getBoundingSphereAt(t,e){if(t>=this._geometryCount)return null;let i=this.geometry,s=this._geometryInfo[t];if(null===s.boundingSphere){let e=new i1;this.getBoundingBoxAt(t,nL),nL.getCenter(e.center);let r=i.index,n=i.attributes.position,a=0;for(let t=s.start,i=s.start+s.count;t<i;t++){let i=t;r&&(i=r.getX(i)),nj.fromBufferAttribute(n,i),a=Math.max(a,e.center.distanceToSquared(nj))}e.radius=Math.sqrt(a),s.boundingSphere=e}return e.copy(s.boundingSphere),e}setMatrixAt(t,e){this.validateInstanceId(t);let i=this._matricesTexture,s=this._matricesTexture.image.data;return e.toArray(s,16*t),i.needsUpdate=!0,this}getMatrixAt(t,e){return this.validateInstanceId(t),e.fromArray(this._matricesTexture.image.data,16*t)}setColorAt(t,e){return this.validateInstanceId(t),null===this._colorsTexture&&this._initColorsTexture(),e.toArray(this._colorsTexture.image.data,4*t),this._colorsTexture.needsUpdate=!0,this}getColorAt(t,e){return this.validateInstanceId(t),e.fromArray(this._colorsTexture.image.data,4*t)}setVisibleAt(t,e){return this.validateInstanceId(t),this._instanceInfo[t].visible===e||(this._instanceInfo[t].visible=e,this._visibilityChanged=!0),this}getVisibleAt(t){return this.validateInstanceId(t),this._instanceInfo[t].visible}setGeometryIdAt(t,e){return this.validateInstanceId(t),this.validateGeometryId(e),this._instanceInfo[t].geometryIndex=e,this}getGeometryIdAt(t){return this.validateInstanceId(t),this._instanceInfo[t].geometryIndex}getGeometryRangeAt(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.validateGeometryId(t);let i=this._geometryInfo[t];return e.vertexStart=i.vertexStart,e.vertexCount=i.vertexCount,e.reservedVertexCount=i.reservedVertexCount,e.indexStart=i.indexStart,e.indexCount=i.indexCount,e.reservedIndexCount=i.reservedIndexCount,e.start=i.start,e.count=i.count,e}setInstanceCount(t){let e=this._availableInstanceIds,i=this._instanceInfo;for(e.sort(nB);e[e.length-1]===i.length;)i.pop(),e.pop();if(t<i.length)throw Error("BatchedMesh: Instance ids outside the range ".concat(t," are being used. Cannot shrink instance count."));let s=new Int32Array(t),r=new Int32Array(t);nq(this._multiDrawCounts,s),nq(this._multiDrawStarts,r),this._multiDrawCounts=s,this._multiDrawStarts=r,this._maxInstanceCount=t;let n=this._indirectTexture,a=this._matricesTexture,o=this._colorsTexture;n.dispose(),this._initIndirectTexture(),nq(n.image.data,this._indirectTexture.image.data),a.dispose(),this._initMatricesTexture(),nq(a.image.data,this._matricesTexture.image.data),o&&(o.dispose(),this._initColorsTexture(),nq(o.image.data,this._colorsTexture.image.data))}setGeometrySize(t,e){let i=[...this._geometryInfo].filter(t=>t.active);if(Math.max(...i.map(t=>t.vertexStart+t.reservedVertexCount))>t)throw Error("BatchedMesh: Geometry vertex values are being used outside the range ".concat(e,". Cannot shrink further."));if(this.geometry.index&&Math.max(...i.map(t=>t.indexStart+t.reservedIndexCount))>e)throw Error("BatchedMesh: Geometry index values are being used outside the range ".concat(e,". Cannot shrink further."));let s=this.geometry;s.dispose(),this._maxVertexCount=t,this._maxIndexCount=e,this._geometryInitialized&&(this._geometryInitialized=!1,this.geometry=new rn,this._initializeGeometry(s));let r=this.geometry;for(let t in s.index&&nq(s.index.array,r.index.array),s.attributes)nq(s.attributes[t].array,r.attributes[t].array)}raycast(t,e){let i=this._instanceInfo,s=this._geometryInfo,r=this.matrixWorld,n=this.geometry;nH.material=this.material,nH.geometry.index=n.index,nH.geometry.attributes=n.attributes,null===nH.geometry.boundingBox&&(nH.geometry.boundingBox=new iV),null===nH.geometry.boundingSphere&&(nH.geometry.boundingSphere=new i1);for(let n=0,a=i.length;n<a;n++){if(!i[n].visible||!i[n].active)continue;let a=i[n].geometryIndex,o=s[a];nH.geometry.setDrawRange(o.start,o.count),this.getMatrixAt(n,nH.matrixWorld).premultiply(r),this.getBoundingBoxAt(a,nH.geometry.boundingBox),this.getBoundingSphereAt(a,nH.geometry.boundingSphere),nH.raycast(t,nJ);for(let t=0,i=nJ.length;t<i;t++){let i=nJ[t];i.object=this,i.batchId=n,e.push(i)}nJ.length=0}nH.material=null,nH.geometry.index=null,nH.geometry.attributes={},nH.geometry.setDrawRange(0,1/0)}copy(t){return super.copy(t),this.geometry=t.geometry.clone(),this.perObjectFrustumCulled=t.perObjectFrustumCulled,this.sortObjects=t.sortObjects,this.boundingBox=null!==t.boundingBox?t.boundingBox.clone():null,this.boundingSphere=null!==t.boundingSphere?t.boundingSphere.clone():null,this._geometryInfo=t._geometryInfo.map(t=>({...t,boundingBox:null!==t.boundingBox?t.boundingBox.clone():null,boundingSphere:null!==t.boundingSphere?t.boundingSphere.clone():null})),this._instanceInfo=t._instanceInfo.map(t=>({...t})),this._availableInstanceIds=t._availableInstanceIds.slice(),this._availableGeometryIds=t._availableGeometryIds.slice(),this._nextIndexStart=t._nextIndexStart,this._nextVertexStart=t._nextVertexStart,this._geometryCount=t._geometryCount,this._maxInstanceCount=t._maxInstanceCount,this._maxVertexCount=t._maxVertexCount,this._maxIndexCount=t._maxIndexCount,this._geometryInitialized=t._geometryInitialized,this._multiDrawCounts=t._multiDrawCounts.slice(),this._multiDrawStarts=t._multiDrawStarts.slice(),this._indirectTexture=t._indirectTexture.clone(),this._indirectTexture.image.data=this._indirectTexture.image.data.slice(),this._matricesTexture=t._matricesTexture.clone(),this._matricesTexture.image.data=this._matricesTexture.image.data.slice(),null!==this._colorsTexture&&(this._colorsTexture=t._colorsTexture.clone(),this._colorsTexture.image.data=this._colorsTexture.image.data.slice()),this}dispose(){this.geometry.dispose(),this._matricesTexture.dispose(),this._matricesTexture=null,this._indirectTexture.dispose(),this._indirectTexture=null,null!==this._colorsTexture&&(this._colorsTexture.dispose(),this._colorsTexture=null)}onBeforeRender(t,e,i,s,r){if(!this._visibilityChanged&&!this.perObjectFrustumCulled&&!this.sortObjects)return;let n=s.getIndex(),a=null===n?1:n.array.BYTES_PER_ELEMENT,o=this._instanceInfo,h=this._multiDrawStarts,l=this._multiDrawCounts,u=this._geometryInfo,c=this.perObjectFrustumCulled,d=this._indirectTexture,p=d.image.data,m=i.isArrayCamera?nV:nN;c&&!i.isArrayCamera&&(nR.multiplyMatrices(i.projectionMatrix,i.matrixWorldInverse).multiply(this.matrixWorld),nN.setFromProjectionMatrix(nR,t.coordinateSystem));let y=0;if(this.sortObjects){nR.copy(this.matrixWorld).invert(),nj.setFromMatrixPosition(i.matrixWorld).applyMatrix4(nR),nU.set(0,0,-1).transformDirection(i.matrixWorld).transformDirection(nR);for(let t=0,e=o.length;t<e;t++)if(o[t].visible&&o[t].active){let e=o[t].geometryIndex;this.getMatrixAt(t,nR),this.getBoundingSphereAt(e,nF).applyMatrix4(nR);let s=!1;if(c&&(s=!m.intersectsSphere(nF,i)),!s){let i=u[e],s=nD.subVectors(nF.center,nj).dot(nU);nW.push(i.start,i.count,s,t)}}let t=nW.list,e=this.customSort;null===e?t.sort(r.transparent?nO:nk):e.call(this,t,i);for(let e=0,i=t.length;e<i;e++){let i=t[e];h[y]=i.start*a,l[y]=i.count,p[y]=i.index,y++}nW.reset()}else for(let t=0,e=o.length;t<e;t++)if(o[t].visible&&o[t].active){let e=o[t].geometryIndex,s=!1;if(c&&(this.getMatrixAt(t,nR),this.getBoundingSphereAt(e,nF).applyMatrix4(nR),s=!m.intersectsSphere(nF,i)),!s){let i=u[e];h[y]=i.start*a,l[y]=i.count,p[y]=t,y++}}d.needsUpdate=!0,this._multiDrawCount=y,this._visibilityChanged=!1}onBeforeShadow(t,e,i,s,r,n){this.onBeforeRender(t,null,s,r,n)}constructor(t,e,i=2*e,s){super(new rn,s),this.isBatchedMesh=!0,this.perObjectFrustumCulled=!0,this.sortObjects=!0,this.boundingBox=null,this.boundingSphere=null,this.customSort=null,this._instanceInfo=[],this._geometryInfo=[],this._availableInstanceIds=[],this._availableGeometryIds=[],this._nextIndexStart=0,this._nextVertexStart=0,this._geometryCount=0,this._visibilityChanged=!0,this._geometryInitialized=!1,this._maxInstanceCount=t,this._maxVertexCount=e,this._maxIndexCount=i,this._multiDrawCounts=new Int32Array(t),this._multiDrawStarts=new Int32Array(t),this._multiDrawCount=0,this._multiDrawInstances=null,this._matricesTexture=null,this._indirectTexture=null,this._colorsTexture=null,this._initMatricesTexture(),this._initIndirectTexture()}}class nX extends sZ{copy(t){return super.copy(t),this.color.copy(t.color),this.map=t.map,this.linewidth=t.linewidth,this.linecap=t.linecap,this.linejoin=t.linejoin,this.fog=t.fog,this}constructor(t){super(),this.isLineBasicMaterial=!0,this.type="LineBasicMaterial",this.color=new sq(0xffffff),this.map=null,this.linewidth=1,this.linecap="round",this.linejoin="round",this.fog=!0,this.setValues(t)}}let nZ=new iR,nY=new iR,nQ=new st,nK=new i7,n$=new i1,n0=new iR,n1=new iR;class n2 extends sT{copy(t,e){return super.copy(t,e),this.material=Array.isArray(t.material)?t.material.slice():t.material,this.geometry=t.geometry,this}computeLineDistances(){let t=this.geometry;if(null===t.index){let e=t.attributes.position,i=[0];for(let t=1,s=e.count;t<s;t++)nZ.fromBufferAttribute(e,t-1),nY.fromBufferAttribute(e,t),i[t]=i[t-1],i[t]+=nZ.distanceTo(nY);t.setAttribute("lineDistance",new s8(i,1))}else console.warn("THREE.Line.computeLineDistances(): Computation only possible with non-indexed BufferGeometry.");return this}raycast(t,e){let i=this.geometry,s=this.matrixWorld,r=t.params.Line.threshold,n=i.drawRange;if(null===i.boundingSphere&&i.computeBoundingSphere(),n$.copy(i.boundingSphere),n$.applyMatrix4(s),n$.radius+=r,!1===t.ray.intersectsSphere(n$))return;nQ.copy(s).invert(),nK.copy(t.ray).applyMatrix4(nQ);let a=r/((this.scale.x+this.scale.y+this.scale.z)/3),o=a*a,h=this.isLineSegments?2:1,l=i.index,u=i.attributes.position;if(null!==l){let i=Math.max(0,n.start),s=Math.min(l.count,n.start+n.count);for(let r=i,n=s-1;r<n;r+=h){let i=n3(this,t,nK,o,l.getX(r),l.getX(r+1),r);i&&e.push(i)}if(this.isLineLoop){let r=n3(this,t,nK,o,l.getX(s-1),l.getX(i),s-1);r&&e.push(r)}}else{let i=Math.max(0,n.start),s=Math.min(u.count,n.start+n.count);for(let r=i,n=s-1;r<n;r+=h){let i=n3(this,t,nK,o,r,r+1,r);i&&e.push(i)}if(this.isLineLoop){let r=n3(this,t,nK,o,s-1,i,s-1);r&&e.push(r)}}}updateMorphTargets(){let t=this.geometry.morphAttributes,e=Object.keys(t);if(e.length>0){let i=t[e[0]];if(void 0!==i){this.morphTargetInfluences=[],this.morphTargetDictionary={};for(let t=0,e=i.length;t<e;t++){let e=i[t].name||String(t);this.morphTargetInfluences.push(0),this.morphTargetDictionary[e]=t}}}}constructor(t=new rn,e=new nX){super(),this.isLine=!0,this.type="Line",this.geometry=t,this.material=e,this.morphTargetDictionary=void 0,this.morphTargetInfluences=void 0,this.updateMorphTargets()}}function n3(t,e,i,s,r,n,a){let o=t.geometry.attributes.position;if(nZ.fromBufferAttribute(o,r),nY.fromBufferAttribute(o,n),i.distanceSqToSegment(nZ,nY,n0,n1)>s)return;n0.applyMatrix4(t.matrixWorld);let h=e.ray.origin.distanceTo(n0);if(!(h<e.near)&&!(h>e.far))return{distance:h,point:n1.clone().applyMatrix4(t.matrixWorld),index:a,face:null,faceIndex:null,barycoord:null,object:t}}let n5=new iR,n4=new iR;class n6 extends n2{computeLineDistances(){let t=this.geometry;if(null===t.index){let e=t.attributes.position,i=[];for(let t=0,s=e.count;t<s;t+=2)n5.fromBufferAttribute(e,t),n4.fromBufferAttribute(e,t+1),i[t]=0===t?0:i[t-1],i[t+1]=i[t]+n5.distanceTo(n4);t.setAttribute("lineDistance",new s8(i,1))}else console.warn("THREE.LineSegments.computeLineDistances(): Computation only possible with non-indexed BufferGeometry.");return this}constructor(t,e){super(t,e),this.isLineSegments=!0,this.type="LineSegments"}}class n8 extends n2{constructor(t,e){super(t,e),this.isLineLoop=!0,this.type="LineLoop"}}class n9 extends sZ{copy(t){return super.copy(t),this.color.copy(t.color),this.map=t.map,this.alphaMap=t.alphaMap,this.size=t.size,this.sizeAttenuation=t.sizeAttenuation,this.fog=t.fog,this}constructor(t){super(),this.isPointsMaterial=!0,this.type="PointsMaterial",this.color=new sq(0xffffff),this.map=null,this.alphaMap=null,this.size=1,this.sizeAttenuation=!0,this.fog=!0,this.setValues(t)}}let n7=new st,at=new i7,ae=new i1,ai=new iR;class as extends sT{copy(t,e){return super.copy(t,e),this.material=Array.isArray(t.material)?t.material.slice():t.material,this.geometry=t.geometry,this}raycast(t,e){let i=this.geometry,s=this.matrixWorld,r=t.params.Points.threshold,n=i.drawRange;if(null===i.boundingSphere&&i.computeBoundingSphere(),ae.copy(i.boundingSphere),ae.applyMatrix4(s),ae.radius+=r,!1===t.ray.intersectsSphere(ae))return;n7.copy(s).invert(),at.copy(t.ray).applyMatrix4(n7);let a=r/((this.scale.x+this.scale.y+this.scale.z)/3),o=a*a,h=i.index,l=i.attributes.position;if(null!==h){let i=Math.max(0,n.start),r=Math.min(h.count,n.start+n.count);for(let n=i;n<r;n++){let i=h.getX(n);ai.fromBufferAttribute(l,i),ar(ai,i,o,s,t,e,this)}}else{let i=Math.max(0,n.start),r=Math.min(l.count,n.start+n.count);for(let n=i;n<r;n++)ai.fromBufferAttribute(l,n),ar(ai,n,o,s,t,e,this)}}updateMorphTargets(){let t=this.geometry.morphAttributes,e=Object.keys(t);if(e.length>0){let i=t[e[0]];if(void 0!==i){this.morphTargetInfluences=[],this.morphTargetDictionary={};for(let t=0,e=i.length;t<e;t++){let e=i[t].name||String(t);this.morphTargetInfluences.push(0),this.morphTargetDictionary[e]=t}}}}constructor(t=new rn,e=new n9){super(),this.isPoints=!0,this.type="Points",this.geometry=t,this.material=e,this.morphTargetDictionary=void 0,this.morphTargetInfluences=void 0,this.updateMorphTargets()}}function ar(t,e,i,s,r,n,a){let o=at.distanceSqToPoint(t);if(o<i){let i=new iR;at.closestPointToPoint(t,i),i.applyMatrix4(s);let h=r.ray.origin.distanceTo(i);if(h<r.near||h>r.far)return;n.push({distance:h,distanceToRay:Math.sqrt(o),point:i,index:e,face:null,faceIndex:null,barycoord:null,object:a})}}class an extends iT{constructor(t,e){super({width:t,height:e}),this.isFramebufferTexture=!0,this.magFilter=tv,this.minFilter=tv,this.generateMipmaps=!1,this.needsUpdate=!0}}class aa extends iT{copy(t){return super.copy(t),this.source=new i_(Object.assign({},t.image)),this.compareFunction=t.compareFunction,this}toJSON(t){let e=super.toJSON(t);return null!==this.compareFunction&&(e.compareFunction=this.compareFunction),e}constructor(t,e,i=tk,s,r,n,a=tv,o=tv,h,l=tU){if(l!==tU&&l!==tD)throw Error("DepthTexture format must be either THREE.DepthFormat or THREE.DepthStencilFormat");super(null,s,r,n,a,o,l,i,h),this.isDepthTexture=!0,this.image={width:t,height:e},this.flipY=!1,this.generateMipmaps=!1,this.compareFunction=null}}class ao extends aa{addLayerUpdate(t){this.layerUpdates.add(t)}clearLayerUpdates(){this.layerUpdates.clear()}constructor(t=1,e=1,i=1){super(t,e),this.isDepthArrayTexture=!0,this.image={width:t,height:e,depth:i},this.flipY=!1,this.generateMipmaps=!1,this.compareFunction=null,this.layerUpdates=new Set}}class ah extends rn{copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new ah(t.radius,t.height,t.capSegments,t.radialSegments,t.heightSegments)}constructor(t=1,e=1,i=4,s=8,r=1){super(),this.type="CapsuleGeometry",this.parameters={radius:t,height:e,capSegments:i,radialSegments:s,heightSegments:r},e=Math.max(0,e),i=Math.max(1,Math.floor(i)),s=Math.max(3,Math.floor(s));let n=[],a=[],o=[],h=[],l=e/2,u=Math.PI/2*t,c=e,d=2*u+c,p=2*i+(r=Math.max(1,Math.floor(r))),m=s+1,y=new iR,f=new iR;for(let g=0;g<=p;g++){let x=0,v=0,b=0,M=0;if(g<=i){let e=g/i,s=e*Math.PI/2;v=-l-t*Math.cos(s),b=t*Math.sin(s),M=-t*Math.cos(s),x=e*u}else if(g<=i+r){let s=(g-i)/r;v=-l+s*e,b=t,M=0,x=u+s*c}else{let e=(g-i-r)/i,s=e*Math.PI/2;v=l+t*Math.sin(s),b=t*Math.cos(s),M=t*Math.sin(s),x=u+c+e*u}let w=Math.max(0,Math.min(1,x/d)),S=0;0===g?S=.5/s:g===p&&(S=-.5/s);for(let t=0;t<=s;t++){let e=t/s,i=e*Math.PI*2,r=Math.sin(i),n=Math.cos(i);f.x=-b*n,f.y=v,f.z=b*r,a.push(f.x,f.y,f.z),y.set(-b*n,M,b*r),y.normalize(),o.push(y.x,y.y,y.z),h.push(e+S,w)}if(g>0){let t=(g-1)*m;for(let e=0;e<s;e++){let i=t+e,s=t+e+1,r=g*m+e,a=g*m+e+1;n.push(i,s,r),n.push(s,a,r)}}}this.setIndex(n),this.setAttribute("position",new s8(a,3)),this.setAttribute("normal",new s8(o,3)),this.setAttribute("uv",new s8(h,2))}}class al extends rn{copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new al(t.radius,t.segments,t.thetaStart,t.thetaLength)}constructor(t=1,e=32,i=0,s=2*Math.PI){super(),this.type="CircleGeometry",this.parameters={radius:t,segments:e,thetaStart:i,thetaLength:s},e=Math.max(3,e);let r=[],n=[],a=[],o=[],h=new iR,l=new ii;n.push(0,0,0),a.push(0,0,1),o.push(.5,.5);for(let r=0,u=3;r<=e;r++,u+=3){let c=i+r/e*s;h.x=t*Math.cos(c),h.y=t*Math.sin(c),n.push(h.x,h.y,h.z),a.push(0,0,1),l.x=(n[u]/t+1)/2,l.y=(n[u+1]/t+1)/2,o.push(l.x,l.y)}for(let t=1;t<=e;t++)r.push(t,t+1,0);this.setIndex(r),this.setAttribute("position",new s8(n,3)),this.setAttribute("normal",new s8(a,3)),this.setAttribute("uv",new s8(o,2))}}class au extends rn{copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new au(t.radiusTop,t.radiusBottom,t.height,t.radialSegments,t.heightSegments,t.openEnded,t.thetaStart,t.thetaLength)}constructor(t=1,e=1,i=1,s=32,r=1,n=!1,a=0,o=2*Math.PI){super(),this.type="CylinderGeometry",this.parameters={radiusTop:t,radiusBottom:e,height:i,radialSegments:s,heightSegments:r,openEnded:n,thetaStart:a,thetaLength:o};let h=this;s=Math.floor(s),r=Math.floor(r);let l=[],u=[],c=[],d=[],p=0,m=[],y=i/2,f=0;function g(i){let r=p,n=new ii,m=new iR,g=0,x=!0===i?t:e,v=!0===i?1:-1;for(let t=1;t<=s;t++)u.push(0,y*v,0),c.push(0,v,0),d.push(.5,.5),p++;let b=p;for(let t=0;t<=s;t++){let e=t/s*o+a,i=Math.cos(e),r=Math.sin(e);m.x=x*r,m.y=y*v,m.z=x*i,u.push(m.x,m.y,m.z),c.push(0,v,0),n.x=.5*i+.5,n.y=.5*r*v+.5,d.push(n.x,n.y),p++}for(let t=0;t<s;t++){let e=r+t,s=b+t;!0===i?l.push(s,s+1,e):l.push(s+1,s,e),g+=3}h.addGroup(f,g,!0===i?1:2),f+=g}(function(){let n=new iR,g=new iR,x=0,v=(e-t)/i;for(let h=0;h<=r;h++){let l=[],f=h/r,x=f*(e-t)+t;for(let t=0;t<=s;t++){let e=t/s,r=e*o+a,h=Math.sin(r),m=Math.cos(r);g.x=x*h,g.y=-f*i+y,g.z=x*m,u.push(g.x,g.y,g.z),n.set(h,v,m).normalize(),c.push(n.x,n.y,n.z),d.push(e,1-f),l.push(p++)}m.push(l)}for(let i=0;i<s;i++)for(let s=0;s<r;s++){let n=m[s][i],a=m[s+1][i],o=m[s+1][i+1],h=m[s][i+1];(t>0||0!==s)&&(l.push(n,a,h),x+=3),(e>0||s!==r-1)&&(l.push(a,o,h),x+=3)}h.addGroup(f,x,0),f+=x})(),!1===n&&(t>0&&g(!0),e>0&&g(!1)),this.setIndex(l),this.setAttribute("position",new s8(u,3)),this.setAttribute("normal",new s8(c,3)),this.setAttribute("uv",new s8(d,2))}}class ac extends au{static fromJSON(t){return new ac(t.radius,t.height,t.radialSegments,t.heightSegments,t.openEnded,t.thetaStart,t.thetaLength)}constructor(t=1,e=1,i=32,s=1,r=!1,n=0,a=2*Math.PI){super(0,t,e,i,s,r,n,a),this.type="ConeGeometry",this.parameters={radius:t,height:e,radialSegments:i,heightSegments:s,openEnded:r,thetaStart:n,thetaLength:a}}}class ad extends rn{copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new ad(t.vertices,t.indices,t.radius,t.details)}constructor(t=[],e=[],i=1,s=0){super(),this.type="PolyhedronGeometry",this.parameters={vertices:t,indices:e,radius:i,detail:s};let r=[],n=[];function a(t){r.push(t.x,t.y,t.z)}function o(e,i){let s=3*e;i.x=t[s+0],i.y=t[s+1],i.z=t[s+2]}function h(t,e,i,s){s<0&&1===t.x&&(n[e]=t.x-1),0===i.x&&0===i.z&&(n[e]=s/2/Math.PI+.5)}function l(t){return Math.atan2(t.z,-t.x)}(function(t){let i=new iR,s=new iR,r=new iR;for(let n=0;n<e.length;n+=3)o(e[n+0],i),o(e[n+1],s),o(e[n+2],r),function(t,e,i,s){let r=s+1,n=[];for(let s=0;s<=r;s++){n[s]=[];let a=t.clone().lerp(i,s/r),o=e.clone().lerp(i,s/r),h=r-s;for(let t=0;t<=h;t++)0===t&&s===r?n[s][t]=a:n[s][t]=a.clone().lerp(o,t/h)}for(let t=0;t<r;t++)for(let e=0;e<2*(r-t)-1;e++){let i=Math.floor(e/2);e%2==0?(a(n[t][i+1]),a(n[t+1][i]),a(n[t][i])):(a(n[t][i+1]),a(n[t+1][i+1]),a(n[t+1][i]))}}(i,s,r,t)})(s),function(t){let e=new iR;for(let i=0;i<r.length;i+=3)e.x=r[i+0],e.y=r[i+1],e.z=r[i+2],e.normalize().multiplyScalar(t),r[i+0]=e.x,r[i+1]=e.y,r[i+2]=e.z}(i),function(){let t=new iR;for(let i=0;i<r.length;i+=3){var e;t.x=r[i+0],t.y=r[i+1],t.z=r[i+2];let s=l(t)/2/Math.PI+.5,a=Math.atan2(-(e=t).y,Math.sqrt(e.x*e.x+e.z*e.z))/Math.PI+.5;n.push(s,1-a)}(function(){let t=new iR,e=new iR,i=new iR,s=new iR,a=new ii,o=new ii,u=new ii;for(let c=0,d=0;c<r.length;c+=9,d+=6){t.set(r[c+0],r[c+1],r[c+2]),e.set(r[c+3],r[c+4],r[c+5]),i.set(r[c+6],r[c+7],r[c+8]),a.set(n[d+0],n[d+1]),o.set(n[d+2],n[d+3]),u.set(n[d+4],n[d+5]),s.copy(t).add(e).add(i).divideScalar(3);let p=l(s);h(a,d+0,t,p),h(o,d+2,e,p),h(u,d+4,i,p)}})(),function(){for(let t=0;t<n.length;t+=6){let e=n[t+0],i=n[t+2],s=n[t+4],r=Math.max(e,i,s),a=Math.min(e,i,s);r>.9&&a<.1&&(e<.2&&(n[t+0]+=1),i<.2&&(n[t+2]+=1),s<.2&&(n[t+4]+=1))}}()}(),this.setAttribute("position",new s8(r,3)),this.setAttribute("normal",new s8(r.slice(),3)),this.setAttribute("uv",new s8(n,2)),0===s?this.computeVertexNormals():this.normalizeNormals()}}class ap extends ad{static fromJSON(t){return new ap(t.radius,t.detail)}constructor(t=1,e=0){let i=(1+Math.sqrt(5))/2,s=1/i;super([-1,-1,-1,-1,-1,1,-1,1,-1,-1,1,1,1,-1,-1,1,-1,1,1,1,-1,1,1,1,0,-s,-i,0,-s,i,0,s,-i,0,s,i,-s,-i,0,-s,i,0,s,-i,0,s,i,0,-i,0,-s,i,0,-s,-i,0,s,i,0,s],[3,11,7,3,7,15,3,15,13,7,19,17,7,17,6,7,6,15,17,4,8,17,8,10,17,10,6,8,0,16,8,16,2,8,2,10,0,12,1,0,1,18,0,18,16,6,10,2,6,2,13,6,13,15,2,16,18,2,18,3,2,3,13,18,1,9,18,9,11,18,11,3,4,14,12,4,12,0,4,0,8,11,9,5,11,5,19,11,19,7,19,5,14,19,14,4,19,4,17,1,12,14,1,14,5,1,5,9],t,e),this.type="DodecahedronGeometry",this.parameters={radius:t,detail:e}}}let am=new iR,ay=new iR,af=new iR,ag=new sU;class ax extends rn{copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}constructor(t=null,e=1){if(super(),this.type="EdgesGeometry",this.parameters={geometry:t,thresholdAngle:e},null!==t){let i=Math.cos(e3*e),s=t.getIndex(),r=t.getAttribute("position"),n=s?s.count:r.count,a=[0,0,0],o=["a","b","c"],h=[,,,],l={},u=[];for(let t=0;t<n;t+=3){s?(a[0]=s.getX(t),a[1]=s.getX(t+1),a[2]=s.getX(t+2)):(a[0]=t,a[1]=t+1,a[2]=t+2);let{a:e,b:n,c}=ag;if(e.fromBufferAttribute(r,a[0]),n.fromBufferAttribute(r,a[1]),c.fromBufferAttribute(r,a[2]),ag.getNormal(af),h[0]="".concat(Math.round(1e4*e.x),",").concat(Math.round(1e4*e.y),",").concat(Math.round(1e4*e.z)),h[1]="".concat(Math.round(1e4*n.x),",").concat(Math.round(1e4*n.y),",").concat(Math.round(1e4*n.z)),h[2]="".concat(Math.round(1e4*c.x),",").concat(Math.round(1e4*c.y),",").concat(Math.round(1e4*c.z)),h[0]!==h[1]&&h[1]!==h[2]&&h[2]!==h[0])for(let t=0;t<3;t++){let e=(t+1)%3,s=h[t],r=h[e],n=ag[o[t]],c=ag[o[e]],d="".concat(s,"_").concat(r),p="".concat(r,"_").concat(s);p in l&&l[p]?(af.dot(l[p].normal)<=i&&(u.push(n.x,n.y,n.z),u.push(c.x,c.y,c.z)),l[p]=null):d in l||(l[d]={index0:a[t],index1:a[e],normal:af.clone()})}}for(let t in l)if(l[t]){let{index0:e,index1:i}=l[t];am.fromBufferAttribute(r,e),ay.fromBufferAttribute(r,i),u.push(am.x,am.y,am.z),u.push(ay.x,ay.y,ay.z)}this.setAttribute("position",new s8(u,3))}}}class av{getPoint(){console.warn("THREE.Curve: .getPoint() not implemented.")}getPointAt(t,e){let i=this.getUtoTmapping(t);return this.getPoint(i,e)}getPoints(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:5,e=[];for(let i=0;i<=t;i++)e.push(this.getPoint(i/t));return e}getSpacedPoints(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:5,e=[];for(let i=0;i<=t;i++)e.push(this.getPointAt(i/t));return e}getLength(){let t=this.getLengths();return t[t.length-1]}getLengths(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.arcLengthDivisions;if(this.cacheArcLengths&&this.cacheArcLengths.length===t+1&&!this.needsUpdate)return this.cacheArcLengths;this.needsUpdate=!1;let e=[],i,s=this.getPoint(0),r=0;e.push(0);for(let n=1;n<=t;n++)e.push(r+=(i=this.getPoint(n/t)).distanceTo(s)),s=i;return this.cacheArcLengths=e,e}updateArcLengths(){this.needsUpdate=!0,this.getLengths()}getUtoTmapping(t){let e,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,s=this.getLengths(),r=0,n=s.length;e=i||t*s[n-1];let a=0,o=n-1,h;for(;a<=o;)if((h=s[r=Math.floor(a+(o-a)/2)]-e)<0)a=r+1;else if(h>0)o=r-1;else{o=r;break}if(s[r=o]===e)return r/(n-1);let l=s[r],u=s[r+1];return(r+(e-l)/(u-l))/(n-1)}getTangent(t,e){let i=t-1e-4,s=t+1e-4;i<0&&(i=0),s>1&&(s=1);let r=this.getPoint(i),n=this.getPoint(s),a=e||(r.isVector2?new ii:new iR);return a.copy(n).sub(r).normalize(),a}getTangentAt(t,e){let i=this.getUtoTmapping(t);return this.getTangent(i,e)}computeFrenetFrames(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=new iR,s=[],r=[],n=[],a=new iR,o=new st;for(let e=0;e<=t;e++){let i=e/t;s[e]=this.getTangentAt(i,new iR)}r[0]=new iR,n[0]=new iR;let h=Number.MAX_VALUE,l=Math.abs(s[0].x),u=Math.abs(s[0].y),c=Math.abs(s[0].z);l<=h&&(h=l,i.set(1,0,0)),u<=h&&(h=u,i.set(0,1,0)),c<=h&&i.set(0,0,1),a.crossVectors(s[0],i).normalize(),r[0].crossVectors(s[0],a),n[0].crossVectors(s[0],r[0]);for(let e=1;e<=t;e++){if(r[e]=r[e-1].clone(),n[e]=n[e-1].clone(),a.crossVectors(s[e-1],s[e]),a.length()>Number.EPSILON){a.normalize();let t=Math.acos(e6(s[e-1].dot(s[e]),-1,1));r[e].applyMatrix4(o.makeRotationAxis(a,t))}n[e].crossVectors(s[e],r[e])}if(!0===e){let e=Math.acos(e6(r[0].dot(r[t]),-1,1));e/=t,s[0].dot(a.crossVectors(r[0],r[t]))>0&&(e=-e);for(let i=1;i<=t;i++)r[i].applyMatrix4(o.makeRotationAxis(s[i],e*i)),n[i].crossVectors(s[i],r[i])}return{tangents:s,normals:r,binormals:n}}clone(){return new this.constructor().copy(this)}copy(t){return this.arcLengthDivisions=t.arcLengthDivisions,this}toJSON(){let t={metadata:{version:4.6,type:"Curve",generator:"Curve.toJSON"}};return t.arcLengthDivisions=this.arcLengthDivisions,t.type=this.type,t}fromJSON(t){return this.arcLengthDivisions=t.arcLengthDivisions,this}constructor(){this.type="Curve",this.arcLengthDivisions=200,this.needsUpdate=!1,this.cacheArcLengths=null}}class ab extends av{getPoint(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new ii,i=2*Math.PI,s=this.aEndAngle-this.aStartAngle,r=Math.abs(s)<Number.EPSILON;for(;s<0;)s+=i;for(;s>i;)s-=i;s<Number.EPSILON&&(s=r?0:i),!0!==this.aClockwise||r||(s===i?s=-i:s-=i);let n=this.aStartAngle+t*s,a=this.aX+this.xRadius*Math.cos(n),o=this.aY+this.yRadius*Math.sin(n);if(0!==this.aRotation){let t=Math.cos(this.aRotation),e=Math.sin(this.aRotation),i=a-this.aX,s=o-this.aY;a=i*t-s*e+this.aX,o=i*e+s*t+this.aY}return e.set(a,o)}copy(t){return super.copy(t),this.aX=t.aX,this.aY=t.aY,this.xRadius=t.xRadius,this.yRadius=t.yRadius,this.aStartAngle=t.aStartAngle,this.aEndAngle=t.aEndAngle,this.aClockwise=t.aClockwise,this.aRotation=t.aRotation,this}toJSON(){let t=super.toJSON();return t.aX=this.aX,t.aY=this.aY,t.xRadius=this.xRadius,t.yRadius=this.yRadius,t.aStartAngle=this.aStartAngle,t.aEndAngle=this.aEndAngle,t.aClockwise=this.aClockwise,t.aRotation=this.aRotation,t}fromJSON(t){return super.fromJSON(t),this.aX=t.aX,this.aY=t.aY,this.xRadius=t.xRadius,this.yRadius=t.yRadius,this.aStartAngle=t.aStartAngle,this.aEndAngle=t.aEndAngle,this.aClockwise=t.aClockwise,this.aRotation=t.aRotation,this}constructor(t=0,e=0,i=1,s=1,r=0,n=2*Math.PI,a=!1,o=0){super(),this.isEllipseCurve=!0,this.type="EllipseCurve",this.aX=t,this.aY=e,this.xRadius=i,this.yRadius=s,this.aStartAngle=r,this.aEndAngle=n,this.aClockwise=a,this.aRotation=o}}class aM extends ab{constructor(t,e,i,s,r,n){super(t,e,i,i,s,r,n),this.isArcCurve=!0,this.type="ArcCurve"}}function aw(){let t=0,e=0,i=0,s=0;function r(r,n,a,o){t=r,e=a,i=-3*r+3*n-2*a-o,s=2*r-2*n+a+o}return{initCatmullRom:function(t,e,i,s,n){r(e,i,n*(i-t),n*(s-e))},initNonuniformCatmullRom:function(t,e,i,s,n,a,o){let h=(e-t)/n-(i-t)/(n+a)+(i-e)/a,l=(i-e)/a-(s-e)/(a+o)+(s-i)/o;r(e,i,h*=a,l*=a)},calc:function(r){let n=r*r;return t+e*r+i*n+n*r*s}}}let aS=new iR,a_=new aw,aA=new aw,az=new aw;class aT extends av{getPoint(t){let e,i,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new iR,r=this.points,n=r.length,a=(n-!this.closed)*t,o=Math.floor(a),h=a-o;this.closed?o+=o>0?0:(Math.floor(Math.abs(o)/n)+1)*n:0===h&&o===n-1&&(o=n-2,h=1),this.closed||o>0?e=r[(o-1)%n]:(aS.subVectors(r[0],r[1]).add(r[0]),e=aS);let l=r[o%n],u=r[(o+1)%n];if(this.closed||o+2<n?i=r[(o+2)%n]:(aS.subVectors(r[n-1],r[n-2]).add(r[n-1]),i=aS),"centripetal"===this.curveType||"chordal"===this.curveType){let t="chordal"===this.curveType?.5:.25,s=Math.pow(e.distanceToSquared(l),t),r=Math.pow(l.distanceToSquared(u),t),n=Math.pow(u.distanceToSquared(i),t);r<1e-4&&(r=1),s<1e-4&&(s=r),n<1e-4&&(n=r),a_.initNonuniformCatmullRom(e.x,l.x,u.x,i.x,s,r,n),aA.initNonuniformCatmullRom(e.y,l.y,u.y,i.y,s,r,n),az.initNonuniformCatmullRom(e.z,l.z,u.z,i.z,s,r,n)}else"catmullrom"===this.curveType&&(a_.initCatmullRom(e.x,l.x,u.x,i.x,this.tension),aA.initCatmullRom(e.y,l.y,u.y,i.y,this.tension),az.initCatmullRom(e.z,l.z,u.z,i.z,this.tension));return s.set(a_.calc(h),aA.calc(h),az.calc(h)),s}copy(t){super.copy(t),this.points=[];for(let e=0,i=t.points.length;e<i;e++){let i=t.points[e];this.points.push(i.clone())}return this.closed=t.closed,this.curveType=t.curveType,this.tension=t.tension,this}toJSON(){let t=super.toJSON();t.points=[];for(let e=0,i=this.points.length;e<i;e++){let i=this.points[e];t.points.push(i.toArray())}return t.closed=this.closed,t.curveType=this.curveType,t.tension=this.tension,t}fromJSON(t){super.fromJSON(t),this.points=[];for(let e=0,i=t.points.length;e<i;e++){let i=t.points[e];this.points.push(new iR().fromArray(i))}return this.closed=t.closed,this.curveType=t.curveType,this.tension=t.tension,this}constructor(t=[],e=!1,i="centripetal",s=.5){super(),this.isCatmullRomCurve3=!0,this.type="CatmullRomCurve3",this.points=t,this.closed=e,this.curveType=i,this.tension=s}}function aI(t,e,i,s,r){let n=(s-e)*.5,a=(r-i)*.5,o=t*t;return t*o*(2*i-2*s+n+a)+(-3*i+3*s-2*n-a)*o+n*t+i}function aC(t,e,i,s){return function(t,e){let i=1-t;return i*i*e}(t,e)+2*(1-t)*t*i+t*t*s}function aB(t,e,i,s,r){return function(t,e){let i=1-t;return i*i*i*e}(t,e)+function(t,e){let i=1-t;return 3*i*i*t*e}(t,i)+3*(1-t)*t*t*s+t*t*t*r}class ak extends av{getPoint(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new ii,i=this.v0,s=this.v1,r=this.v2,n=this.v3;return e.set(aB(t,i.x,s.x,r.x,n.x),aB(t,i.y,s.y,r.y,n.y)),e}copy(t){return super.copy(t),this.v0.copy(t.v0),this.v1.copy(t.v1),this.v2.copy(t.v2),this.v3.copy(t.v3),this}toJSON(){let t=super.toJSON();return t.v0=this.v0.toArray(),t.v1=this.v1.toArray(),t.v2=this.v2.toArray(),t.v3=this.v3.toArray(),t}fromJSON(t){return super.fromJSON(t),this.v0.fromArray(t.v0),this.v1.fromArray(t.v1),this.v2.fromArray(t.v2),this.v3.fromArray(t.v3),this}constructor(t=new ii,e=new ii,i=new ii,s=new ii){super(),this.isCubicBezierCurve=!0,this.type="CubicBezierCurve",this.v0=t,this.v1=e,this.v2=i,this.v3=s}}class aO extends av{getPoint(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new iR,i=this.v0,s=this.v1,r=this.v2,n=this.v3;return e.set(aB(t,i.x,s.x,r.x,n.x),aB(t,i.y,s.y,r.y,n.y),aB(t,i.z,s.z,r.z,n.z)),e}copy(t){return super.copy(t),this.v0.copy(t.v0),this.v1.copy(t.v1),this.v2.copy(t.v2),this.v3.copy(t.v3),this}toJSON(){let t=super.toJSON();return t.v0=this.v0.toArray(),t.v1=this.v1.toArray(),t.v2=this.v2.toArray(),t.v3=this.v3.toArray(),t}fromJSON(t){return super.fromJSON(t),this.v0.fromArray(t.v0),this.v1.fromArray(t.v1),this.v2.fromArray(t.v2),this.v3.fromArray(t.v3),this}constructor(t=new iR,e=new iR,i=new iR,s=new iR){super(),this.isCubicBezierCurve3=!0,this.type="CubicBezierCurve3",this.v0=t,this.v1=e,this.v2=i,this.v3=s}}class aE extends av{getPoint(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new ii;return 1===t?e.copy(this.v2):(e.copy(this.v2).sub(this.v1),e.multiplyScalar(t).add(this.v1)),e}getPointAt(t,e){return this.getPoint(t,e)}getTangent(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new ii;return e.subVectors(this.v2,this.v1).normalize()}getTangentAt(t,e){return this.getTangent(t,e)}copy(t){return super.copy(t),this.v1.copy(t.v1),this.v2.copy(t.v2),this}toJSON(){let t=super.toJSON();return t.v1=this.v1.toArray(),t.v2=this.v2.toArray(),t}fromJSON(t){return super.fromJSON(t),this.v1.fromArray(t.v1),this.v2.fromArray(t.v2),this}constructor(t=new ii,e=new ii){super(),this.isLineCurve=!0,this.type="LineCurve",this.v1=t,this.v2=e}}class aR extends av{getPoint(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new iR;return 1===t?e.copy(this.v2):(e.copy(this.v2).sub(this.v1),e.multiplyScalar(t).add(this.v1)),e}getPointAt(t,e){return this.getPoint(t,e)}getTangent(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new iR;return e.subVectors(this.v2,this.v1).normalize()}getTangentAt(t,e){return this.getTangent(t,e)}copy(t){return super.copy(t),this.v1.copy(t.v1),this.v2.copy(t.v2),this}toJSON(){let t=super.toJSON();return t.v1=this.v1.toArray(),t.v2=this.v2.toArray(),t}fromJSON(t){return super.fromJSON(t),this.v1.fromArray(t.v1),this.v2.fromArray(t.v2),this}constructor(t=new iR,e=new iR){super(),this.isLineCurve3=!0,this.type="LineCurve3",this.v1=t,this.v2=e}}class aP extends av{getPoint(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new ii,i=this.v0,s=this.v1,r=this.v2;return e.set(aC(t,i.x,s.x,r.x),aC(t,i.y,s.y,r.y)),e}copy(t){return super.copy(t),this.v0.copy(t.v0),this.v1.copy(t.v1),this.v2.copy(t.v2),this}toJSON(){let t=super.toJSON();return t.v0=this.v0.toArray(),t.v1=this.v1.toArray(),t.v2=this.v2.toArray(),t}fromJSON(t){return super.fromJSON(t),this.v0.fromArray(t.v0),this.v1.fromArray(t.v1),this.v2.fromArray(t.v2),this}constructor(t=new ii,e=new ii,i=new ii){super(),this.isQuadraticBezierCurve=!0,this.type="QuadraticBezierCurve",this.v0=t,this.v1=e,this.v2=i}}class aN extends av{getPoint(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new iR,i=this.v0,s=this.v1,r=this.v2;return e.set(aC(t,i.x,s.x,r.x),aC(t,i.y,s.y,r.y),aC(t,i.z,s.z,r.z)),e}copy(t){return super.copy(t),this.v0.copy(t.v0),this.v1.copy(t.v1),this.v2.copy(t.v2),this}toJSON(){let t=super.toJSON();return t.v0=this.v0.toArray(),t.v1=this.v1.toArray(),t.v2=this.v2.toArray(),t}fromJSON(t){return super.fromJSON(t),this.v0.fromArray(t.v0),this.v1.fromArray(t.v1),this.v2.fromArray(t.v2),this}constructor(t=new iR,e=new iR,i=new iR){super(),this.isQuadraticBezierCurve3=!0,this.type="QuadraticBezierCurve3",this.v0=t,this.v1=e,this.v2=i}}class aV extends av{getPoint(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new ii,i=this.points,s=(i.length-1)*t,r=Math.floor(s),n=s-r,a=i[0===r?r:r-1],o=i[r],h=i[r>i.length-2?i.length-1:r+1],l=i[r>i.length-3?i.length-1:r+2];return e.set(aI(n,a.x,o.x,h.x,l.x),aI(n,a.y,o.y,h.y,l.y)),e}copy(t){super.copy(t),this.points=[];for(let e=0,i=t.points.length;e<i;e++){let i=t.points[e];this.points.push(i.clone())}return this}toJSON(){let t=super.toJSON();t.points=[];for(let e=0,i=this.points.length;e<i;e++){let i=this.points[e];t.points.push(i.toArray())}return t}fromJSON(t){super.fromJSON(t),this.points=[];for(let e=0,i=t.points.length;e<i;e++){let i=t.points[e];this.points.push(new ii().fromArray(i))}return this}constructor(t=[]){super(),this.isSplineCurve=!0,this.type="SplineCurve",this.points=t}}var aL=Object.freeze({__proto__:null,ArcCurve:aM,CatmullRomCurve3:aT,CubicBezierCurve:ak,CubicBezierCurve3:aO,EllipseCurve:ab,LineCurve:aE,LineCurve3:aR,QuadraticBezierCurve:aP,QuadraticBezierCurve3:aN,SplineCurve:aV});class aF extends av{add(t){this.curves.push(t)}closePath(){let t=this.curves[0].getPoint(0),e=this.curves[this.curves.length-1].getPoint(1);if(!t.equals(e)){let i=!0===t.isVector2?"LineCurve":"LineCurve3";this.curves.push(new aL[i](e,t))}return this}getPoint(t,e){let i=t*this.getLength(),s=this.getCurveLengths(),r=0;for(;r<s.length;){if(s[r]>=i){let t=s[r]-i,n=this.curves[r],a=n.getLength(),o=0===a?0:1-t/a;return n.getPointAt(o,e)}r++}return null}getLength(){let t=this.getCurveLengths();return t[t.length-1]}updateArcLengths(){this.needsUpdate=!0,this.cacheLengths=null,this.getCurveLengths()}getCurveLengths(){if(this.cacheLengths&&this.cacheLengths.length===this.curves.length)return this.cacheLengths;let t=[],e=0;for(let i=0,s=this.curves.length;i<s;i++)t.push(e+=this.curves[i].getLength());return this.cacheLengths=t,t}getSpacedPoints(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:40,e=[];for(let i=0;i<=t;i++)e.push(this.getPoint(i/t));return this.autoClose&&e.push(e[0]),e}getPoints(){let t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:12,i=[];for(let s=0,r=this.curves;s<r.length;s++){let n=r[s],a=n.isEllipseCurve?2*e:n.isLineCurve||n.isLineCurve3?1:n.isSplineCurve?e*n.points.length:e,o=n.getPoints(a);for(let e=0;e<o.length;e++){let s=o[e];t&&t.equals(s)||(i.push(s),t=s)}}return this.autoClose&&i.length>1&&!i[i.length-1].equals(i[0])&&i.push(i[0]),i}copy(t){super.copy(t),this.curves=[];for(let e=0,i=t.curves.length;e<i;e++){let i=t.curves[e];this.curves.push(i.clone())}return this.autoClose=t.autoClose,this}toJSON(){let t=super.toJSON();t.autoClose=this.autoClose,t.curves=[];for(let e=0,i=this.curves.length;e<i;e++){let i=this.curves[e];t.curves.push(i.toJSON())}return t}fromJSON(t){super.fromJSON(t),this.autoClose=t.autoClose,this.curves=[];for(let e=0,i=t.curves.length;e<i;e++){let i=t.curves[e];this.curves.push(new aL[i.type]().fromJSON(i))}return this}constructor(){super(),this.type="CurvePath",this.curves=[],this.autoClose=!1}}class aj extends aF{setFromPoints(t){this.moveTo(t[0].x,t[0].y);for(let e=1,i=t.length;e<i;e++)this.lineTo(t[e].x,t[e].y);return this}moveTo(t,e){return this.currentPoint.set(t,e),this}lineTo(t,e){let i=new aE(this.currentPoint.clone(),new ii(t,e));return this.curves.push(i),this.currentPoint.set(t,e),this}quadraticCurveTo(t,e,i,s){let r=new aP(this.currentPoint.clone(),new ii(t,e),new ii(i,s));return this.curves.push(r),this.currentPoint.set(i,s),this}bezierCurveTo(t,e,i,s,r,n){let a=new ak(this.currentPoint.clone(),new ii(t,e),new ii(i,s),new ii(r,n));return this.curves.push(a),this.currentPoint.set(r,n),this}splineThru(t){let e=new aV([this.currentPoint.clone()].concat(t));return this.curves.push(e),this.currentPoint.copy(t[t.length-1]),this}arc(t,e,i,s,r,n){let a=this.currentPoint.x,o=this.currentPoint.y;return this.absarc(t+a,e+o,i,s,r,n),this}absarc(t,e,i,s,r,n){return this.absellipse(t,e,i,i,s,r,n),this}ellipse(t,e,i,s,r,n,a,o){let h=this.currentPoint.x,l=this.currentPoint.y;return this.absellipse(t+h,e+l,i,s,r,n,a,o),this}absellipse(t,e,i,s,r,n,a,o){let h=new ab(t,e,i,s,r,n,a,o);if(this.curves.length>0){let t=h.getPoint(0);t.equals(this.currentPoint)||this.lineTo(t.x,t.y)}this.curves.push(h);let l=h.getPoint(1);return this.currentPoint.copy(l),this}copy(t){return super.copy(t),this.currentPoint.copy(t.currentPoint),this}toJSON(){let t=super.toJSON();return t.currentPoint=this.currentPoint.toArray(),t}fromJSON(t){return super.fromJSON(t),this.currentPoint.fromArray(t.currentPoint),this}constructor(t){super(),this.type="Path",this.currentPoint=new ii,t&&this.setFromPoints(t)}}class aU extends aj{getPointsHoles(t){let e=[];for(let i=0,s=this.holes.length;i<s;i++)e[i]=this.holes[i].getPoints(t);return e}extractPoints(t){return{shape:this.getPoints(t),holes:this.getPointsHoles(t)}}copy(t){super.copy(t),this.holes=[];for(let e=0,i=t.holes.length;e<i;e++){let i=t.holes[e];this.holes.push(i.clone())}return this}toJSON(){let t=super.toJSON();t.uuid=this.uuid,t.holes=[];for(let e=0,i=this.holes.length;e<i;e++){let i=this.holes[e];t.holes.push(i.toJSON())}return t}fromJSON(t){super.fromJSON(t),this.uuid=t.uuid,this.holes=[];for(let e=0,i=t.holes.length;e<i;e++){let i=t.holes[e];this.holes.push(new aj().fromJSON(i))}return this}constructor(t){super(t),this.uuid=e4(),this.type="Shape",this.holes=[]}}function aD(t,e,i,s,r){let n;if(r===function(t,e,i,s){let r=0;for(let n=e,a=i-s;n<i;n+=s)r+=(t[a]-t[n])*(t[n+1]+t[a+1]),a=n;return r}(t,e,i,s)>0)for(let r=e;r<i;r+=s)n=a1(r/s|0,t[r],t[r+1],n);else for(let r=i-s;r>=e;r-=s)n=a1(r/s|0,t[r],t[r+1],n);return n&&aZ(n,n.next)&&(a2(n),n=n.next),n}function aW(t,e){if(!t)return t;e||(e=t);let i=t,s;do if(s=!1,!i.steiner&&(aZ(i,i.next)||0===aX(i.prev,i,i.next))){if(a2(i),(i=e=i.prev)===i.next)break;s=!0}else i=i.next;while(s||i!==e);return e}function aH(t,e){let i=t.x-e.x;return 0===i&&0==(i=t.y-e.y)&&(i=(t.next.y-t.y)/(t.next.x-t.x)-(e.next.y-e.y)/(e.next.x-e.x)),i}function aJ(t,e,i,s,r){return(t=((t=((t=((t=((t=(t-i)*r|0)|t<<8)&0xff00ff)|t<<4)&0xf0f0f0f)|t<<2)&0x33333333)|t<<1)&0x55555555)|(e=((e=((e=((e=((e=(e-s)*r|0)|e<<8)&0xff00ff)|e<<4)&0xf0f0f0f)|e<<2)&0x33333333)|e<<1)&0x55555555)<<1}function aq(t,e,i,s,r,n,a,o){return(r-a)*(e-o)>=(t-a)*(n-o)&&(t-a)*(s-o)>=(i-a)*(e-o)&&(i-a)*(n-o)>=(r-a)*(s-o)}function aG(t,e,i,s,r,n,a,o){return(t!==a||e!==o)&&aq(t,e,i,s,r,n,a,o)}function aX(t,e,i){return(e.y-t.y)*(i.x-e.x)-(e.x-t.x)*(i.y-e.y)}function aZ(t,e){return t.x===e.x&&t.y===e.y}function aY(t,e,i,s){let r=aK(aX(t,e,i)),n=aK(aX(t,e,s)),a=aK(aX(i,s,t)),o=aK(aX(i,s,e));return!!(r!==n&&a!==o||0===r&&aQ(t,i,e)||0===n&&aQ(t,s,e)||0===a&&aQ(i,t,s)||0===o&&aQ(i,e,s))}function aQ(t,e,i){return e.x<=Math.max(t.x,i.x)&&e.x>=Math.min(t.x,i.x)&&e.y<=Math.max(t.y,i.y)&&e.y>=Math.min(t.y,i.y)}function aK(t){return t>0?1:t<0?-1:0}function a$(t,e){return 0>aX(t.prev,t,t.next)?aX(t,e,t.next)>=0&&aX(t,t.prev,e)>=0:0>aX(t,e,t.prev)||0>aX(t,t.next,e)}function a0(t,e){let i=a3(t.i,t.x,t.y),s=a3(e.i,e.x,e.y),r=t.next,n=e.prev;return t.next=e,e.prev=t,i.next=r,r.prev=i,s.next=i,i.prev=s,n.next=s,s.prev=n,s}function a1(t,e,i,s){let r=a3(t,e,i);return s?(r.next=s.next,r.prev=s,s.next.prev=r,s.next=r):(r.prev=r,r.next=r),r}function a2(t){t.next.prev=t.prev,t.prev.next=t.next,t.prevZ&&(t.prevZ.nextZ=t.nextZ),t.nextZ&&(t.nextZ.prevZ=t.prevZ)}function a3(t,e,i){return{i:t,x:e,y:i,prev:null,next:null,z:0,prevZ:null,nextZ:null,steiner:!1}}class a5{static triangulate(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:2;return function(t,e){let i,s,r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:2,a=e&&e.length,o=a?e[0]*n:t.length,h=aD(t,0,o,n,!0),l=[];if(!h||h.next===h.prev)return l;if(a&&(h=function(t,e,i,s){let r=[];for(let i=0,n=e.length;i<n;i++){let a=e[i]*s,o=i<n-1?e[i+1]*s:t.length,h=aD(t,a,o,s,!1);h===h.next&&(h.steiner=!0),r.push(function(t){let e=t,i=t;do(e.x<i.x||e.x===i.x&&e.y<i.y)&&(i=e),e=e.next;while(e!==t);return i}(h))}r.sort(aH);for(let t=0;t<r.length;t++)i=function(t,e){let i=function(t,e){let i,s=e,r=t.x,n=t.y,a=-1/0;if(aZ(t,s))return s;do{if(aZ(t,s.next))return s.next;if(n<=s.y&&n>=s.next.y&&s.next.y!==s.y){let t=s.x+(n-s.y)*(s.next.x-s.x)/(s.next.y-s.y);if(t<=r&&t>a&&(a=t,i=s.x<s.next.x?s:s.next,t===r))return i}s=s.next}while(s!==e);if(!i)return null;let o=i,h=i.x,l=i.y,u=1/0;s=i;do{if(r>=s.x&&s.x>=h&&r!==s.x&&aq(n<l?r:a,n,h,l,n<l?a:r,n,s.x,s.y)){var c,d;let e=Math.abs(n-s.y)/(r-s.x);a$(s,t)&&(e<u||e===u&&(s.x>i.x||s.x===i.x&&(c=i,d=s,0>aX(c.prev,c,d.prev)&&0>aX(d.next,c,c.next))))&&(i=s,u=e)}s=s.next}while(s!==o);return i}(t,e);if(!i)return e;let s=a0(i,t);return aW(s,s.next),aW(i,i.next)}(r[t],i);return i}(t,e,h,n)),t.length>80*n){i=1/0,s=1/0;let e=-1/0,a=-1/0;for(let r=n;r<o;r+=n){let n=t[r],o=t[r+1];n<i&&(i=n),o<s&&(s=o),n>e&&(e=n),o>a&&(a=o)}r=0!==(r=Math.max(e-i,a-s))?32767/r:0}return function t(e,i,s,r,n,a,o){if(!e)return;!o&&a&&function(t,e,i,s){let r=t;do 0===r.z&&(r.z=aJ(r.x,r.y,e,i,s)),r.prevZ=r.prev,r.nextZ=r.next,r=r.next;while(r!==t);r.prevZ.nextZ=null,r.prevZ=null,function(t){let e,i=1;do{let s,r=t;t=null;let n=null;for(e=0;r;){e++;let a=r,o=0;for(let t=0;t<i&&(o++,a=a.nextZ);t++);let h=i;for(;o>0||h>0&&a;)0!==o&&(0===h||!a||r.z<=a.z)?(s=r,r=r.nextZ,o--):(s=a,a=a.nextZ,h--),n?n.nextZ=s:t=s,s.prevZ=n,n=s;r=a}n.nextZ=null,i*=2}while(e>1)}(r)}(e,r,n,a);let h=e;for(;e.prev!==e.next;){let l=e.prev,u=e.next;if(a?function(t,e,i,s){let r=t.prev,n=t.next;if(aX(r,t,n)>=0)return!1;let a=r.x,o=t.x,h=n.x,l=r.y,u=t.y,c=n.y,d=Math.min(a,o,h),p=Math.min(l,u,c),m=Math.max(a,o,h),y=Math.max(l,u,c),f=aJ(d,p,e,i,s),g=aJ(m,y,e,i,s),x=t.prevZ,v=t.nextZ;for(;x&&x.z>=f&&v&&v.z<=g;){if(x.x>=d&&x.x<=m&&x.y>=p&&x.y<=y&&x!==r&&x!==n&&aG(a,l,o,u,h,c,x.x,x.y)&&aX(x.prev,x,x.next)>=0||(x=x.prevZ,v.x>=d&&v.x<=m&&v.y>=p&&v.y<=y&&v!==r&&v!==n&&aG(a,l,o,u,h,c,v.x,v.y)&&aX(v.prev,v,v.next)>=0))return!1;v=v.nextZ}for(;x&&x.z>=f;){if(x.x>=d&&x.x<=m&&x.y>=p&&x.y<=y&&x!==r&&x!==n&&aG(a,l,o,u,h,c,x.x,x.y)&&aX(x.prev,x,x.next)>=0)return!1;x=x.prevZ}for(;v&&v.z<=g;){if(v.x>=d&&v.x<=m&&v.y>=p&&v.y<=y&&v!==r&&v!==n&&aG(a,l,o,u,h,c,v.x,v.y)&&aX(v.prev,v,v.next)>=0)return!1;v=v.nextZ}return!0}(e,r,n,a):function(t){let e=t.prev,i=t.next;if(aX(e,t,i)>=0)return!1;let s=e.x,r=t.x,n=i.x,a=e.y,o=t.y,h=i.y,l=Math.min(s,r,n),u=Math.min(a,o,h),c=Math.max(s,r,n),d=Math.max(a,o,h),p=i.next;for(;p!==e;){if(p.x>=l&&p.x<=c&&p.y>=u&&p.y<=d&&aG(s,a,r,o,n,h,p.x,p.y)&&aX(p.prev,p,p.next)>=0)return!1;p=p.next}return!0}(e)){i.push(l.i,e.i,u.i),a2(e),e=u.next,h=u.next;continue}if((e=u)===h){o?1===o?t(e=function(t,e){let i=t;do{let s=i.prev,r=i.next.next;!aZ(s,r)&&aY(s,i,i.next,r)&&a$(s,r)&&a$(r,s)&&(e.push(s.i,i.i,r.i),a2(i),a2(i.next),i=t=r),i=i.next}while(i!==t);return aW(i)}(aW(e),i),i,s,r,n,a,2):2===o&&function(e,i,s,r,n,a){let o=e;do{let e=o.next.next;for(;e!==o.prev;){var h,l;if(o.i!==e.i&&(h=o,l=e,h.next.i!==l.i&&h.prev.i!==l.i&&!function(t,e){let i=t;do{if(i.i!==t.i&&i.next.i!==t.i&&i.i!==e.i&&i.next.i!==e.i&&aY(i,i.next,t,e))return!0;i=i.next}while(i!==t);return!1}(h,l)&&(a$(h,l)&&a$(l,h)&&function(t,e){let i=t,s=!1,r=(t.x+e.x)/2,n=(t.y+e.y)/2;do i.y>n!=i.next.y>n&&i.next.y!==i.y&&r<(i.next.x-i.x)*(n-i.y)/(i.next.y-i.y)+i.x&&(s=!s),i=i.next;while(i!==t);return s}(h,l)&&(aX(h.prev,h,l.prev)||aX(h,l.prev,l))||aZ(h,l)&&aX(h.prev,h,h.next)>0&&aX(l.prev,l,l.next)>0))){let h=a0(o,e);o=aW(o,o.next),h=aW(h,h.next),t(o,i,s,r,n,a,0),t(h,i,s,r,n,a,0);return}e=e.next}o=o.next}while(o!==e)}(e,i,s,r,n,a):t(aW(e),i,s,r,n,a,1);break}}}(h,l,n,i,s,r,0),l}(t,e,i)}}class a4{static area(t){let e=t.length,i=0;for(let s=e-1,r=0;r<e;s=r++)i+=t[s].x*t[r].y-t[r].x*t[s].y;return .5*i}static isClockWise(t){return 0>a4.area(t)}static triangulateShape(t,e){let i=[],s=[],r=[];a6(t),a8(i,t);let n=t.length;e.forEach(a6);for(let t=0;t<e.length;t++)s.push(n),n+=e[t].length,a8(i,e[t]);let a=a5.triangulate(i,s);for(let t=0;t<a.length;t+=3)r.push(a.slice(t,t+3));return r}}function a6(t){let e=t.length;e>2&&t[e-1].equals(t[0])&&t.pop()}function a8(t,e){for(let i=0;i<e.length;i++)t.push(e[i].x),t.push(e[i].y)}class a9 extends rn{copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}toJSON(){let t=super.toJSON();return function(t,e,i){if(i.shapes=[],Array.isArray(t))for(let e=0,s=t.length;e<s;e++){let s=t[e];i.shapes.push(s.uuid)}else i.shapes.push(t.uuid);return i.options=Object.assign({},e),void 0!==e.extrudePath&&(i.options.extrudePath=e.extrudePath.toJSON()),i}(this.parameters.shapes,this.parameters.options,t)}static fromJSON(t,e){let i=[];for(let s=0,r=t.shapes.length;s<r;s++){let r=e[t.shapes[s]];i.push(r)}let s=t.options.extrudePath;return void 0!==s&&(t.options.extrudePath=new aL[s.type]().fromJSON(s)),new a9(i,t.options)}constructor(t=new aU([new ii(.5,.5),new ii(-.5,.5),new ii(-.5,-.5),new ii(.5,-.5)]),e={}){super(),this.type="ExtrudeGeometry",this.parameters={shapes:t,options:e},t=Array.isArray(t)?t:[t];let i=this,s=[],r=[];for(let n=0,a=t.length;n<a;n++)!function(t){let n,a,o,h,l,u=[],c=void 0!==e.curveSegments?e.curveSegments:12,d=void 0!==e.steps?e.steps:1,p=void 0!==e.depth?e.depth:1,m=void 0===e.bevelEnabled||e.bevelEnabled,y=void 0!==e.bevelThickness?e.bevelThickness:.2,f=void 0!==e.bevelSize?e.bevelSize:y-.1,g=void 0!==e.bevelOffset?e.bevelOffset:0,x=void 0!==e.bevelSegments?e.bevelSegments:3,v=e.extrudePath,b=void 0!==e.UVGenerator?e.UVGenerator:a7,M,w=!1;v&&(M=v.getSpacedPoints(d),w=!0,m=!1,n=v.computeFrenetFrames(d,!1),a=new iR,o=new iR,h=new iR),m||(x=0,y=0,f=0,g=0);let S=t.extractPoints(c),_=S.shape,A=S.holes;if(!a4.isClockWise(_)){_=_.reverse();for(let t=0,e=A.length;t<e;t++){let e=A[t];a4.isClockWise(e)&&(A[t]=e.reverse())}}function z(t){let e=1e-10*1e-10,i=t[0];for(let s=1;s<=t.length;s++){let r=s%t.length,n=t[r],a=n.x-i.x,o=n.y-i.y,h=a*a+o*o,l=Math.max(Math.abs(n.x),Math.abs(n.y),Math.abs(i.x),Math.abs(i.y));if(h<=e*l*l){t.splice(r,1),s--;continue}i=n}}z(_),A.forEach(z);let T=A.length,I=_;for(let t=0;t<T;t++){let e=A[t];_=_.concat(e)}function C(t,e,i){return e||console.error("THREE.ExtrudeGeometry: vec does not exist"),t.clone().addScaledVector(e,i)}let B=_.length;function k(t,e,i){let s,r,n,a=t.x-e.x,o=t.y-e.y,h=i.x-t.x,l=i.y-t.y,u=a*a+o*o;if(Math.abs(a*l-o*h)>Number.EPSILON){let c=Math.sqrt(u),d=Math.sqrt(h*h+l*l),p=e.x-o/c,m=e.y+a/c,y=((i.x-l/d-p)*l-(i.y+h/d-m)*h)/(a*l-o*h),f=(s=p+a*y-t.x)*s+(r=m+o*y-t.y)*r;if(f<=2)return new ii(s,r);n=Math.sqrt(f/2)}else{let t=!1;a>Number.EPSILON?h>Number.EPSILON&&(t=!0):a<-Number.EPSILON?h<-Number.EPSILON&&(t=!0):Math.sign(o)===Math.sign(l)&&(t=!0),t?(s=-o,r=a,n=Math.sqrt(u)):(s=a,r=o,n=Math.sqrt(u/2))}return new ii(s/n,r/n)}let O=[];for(let t=0,e=I.length,i=e-1,s=t+1;t<e;t++,i++,s++)i===e&&(i=0),s===e&&(s=0),O[t]=k(I[t],I[i],I[s]);let E=[],R,P=O.concat();for(let t=0;t<T;t++){let e=A[t];R=[];for(let t=0,i=e.length,s=i-1,r=t+1;t<i;t++,s++,r++)s===i&&(s=0),r===i&&(r=0),R[t]=k(e[t],e[s],e[r]);E.push(R),P=P.concat(R)}if(0===x)l=a4.triangulateShape(I,A);else{let t=[],e=[];for(let i=0;i<x;i++){let s=i/x,r=y*Math.cos(s*Math.PI/2),n=f*Math.sin(s*Math.PI/2)+g;for(let e=0,i=I.length;e<i;e++){let i=C(I[e],O[e],n);F(i.x,i.y,-r),0===s&&t.push(i)}for(let t=0;t<T;t++){let i=A[t];R=E[t];let a=[];for(let t=0,e=i.length;t<e;t++){let e=C(i[t],R[t],n);F(e.x,e.y,-r),0===s&&a.push(e)}0===s&&e.push(a)}}l=a4.triangulateShape(t,e)}let N=l.length,V=f+g;for(let t=0;t<B;t++){let e=m?C(_[t],P[t],V):_[t];w?(o.copy(n.normals[0]).multiplyScalar(e.x),a.copy(n.binormals[0]).multiplyScalar(e.y),h.copy(M[0]).add(o).add(a),F(h.x,h.y,h.z)):F(e.x,e.y,0)}for(let t=1;t<=d;t++)for(let e=0;e<B;e++){let i=m?C(_[e],P[e],V):_[e];w?(o.copy(n.normals[t]).multiplyScalar(i.x),a.copy(n.binormals[t]).multiplyScalar(i.y),h.copy(M[t]).add(o).add(a),F(h.x,h.y,h.z)):F(i.x,i.y,p/d*t)}for(let t=x-1;t>=0;t--){let e=t/x,i=y*Math.cos(e*Math.PI/2),s=f*Math.sin(e*Math.PI/2)+g;for(let t=0,e=I.length;t<e;t++){let e=C(I[t],O[t],s);F(e.x,e.y,p+i)}for(let t=0,e=A.length;t<e;t++){let e=A[t];R=E[t];for(let t=0,r=e.length;t<r;t++){let r=C(e[t],R[t],s);w?F(r.x,r.y+M[d-1].y,M[d-1].x+i):F(r.x,r.y,p+i)}}}function L(t,e){let r=t.length;for(;--r>=0;){let n=r,a=r-1;a<0&&(a=t.length-1);for(let t=0,r=d+2*x;t<r;t++){let r=B*t,o=B*(t+1);!function(t,e,r,n){U(t),U(e),U(n),U(e),U(r),U(n);let a=s.length/3,o=b.generateSideWallUV(i,s,a-6,a-3,a-2,a-1);D(o[0]),D(o[1]),D(o[3]),D(o[1]),D(o[2]),D(o[3])}(e+n+r,e+a+r,e+a+o,e+n+o)}}}function F(t,e,i){u.push(t),u.push(e),u.push(i)}function j(t,e,r){U(t),U(e),U(r);let n=s.length/3,a=b.generateTopUV(i,s,n-3,n-2,n-1);D(a[0]),D(a[1]),D(a[2])}function U(t){s.push(u[3*t+0]),s.push(u[3*t+1]),s.push(u[3*t+2])}function D(t){r.push(t.x),r.push(t.y)}(function(){let t=s.length/3;if(m){let t=0,e=0*B;for(let t=0;t<N;t++){let i=l[t];j(i[2]+e,i[1]+e,i[0]+e)}e=B*(d+2*x);for(let t=0;t<N;t++){let i=l[t];j(i[0]+e,i[1]+e,i[2]+e)}}else{for(let t=0;t<N;t++){let e=l[t];j(e[2],e[1],e[0])}for(let t=0;t<N;t++){let e=l[t];j(e[0]+B*d,e[1]+B*d,e[2]+B*d)}}i.addGroup(t,s.length/3-t,0)})(),function(){let t=s.length/3,e=0;L(I,0),e+=I.length;for(let t=0,i=A.length;t<i;t++){let i=A[t];L(i,e),e+=i.length}i.addGroup(t,s.length/3-t,1)}()}(t[n]);this.setAttribute("position",new s8(s,3)),this.setAttribute("uv",new s8(r,2)),this.computeVertexNormals()}}let a7={generateTopUV:function(t,e,i,s,r){let n=e[3*i],a=e[3*i+1],o=e[3*s],h=e[3*s+1],l=e[3*r],u=e[3*r+1];return[new ii(n,a),new ii(o,h),new ii(l,u)]},generateSideWallUV:function(t,e,i,s,r,n){let a=e[3*i],o=e[3*i+1],h=e[3*i+2],l=e[3*s],u=e[3*s+1],c=e[3*s+2],d=e[3*r],p=e[3*r+1],m=e[3*r+2],y=e[3*n],f=e[3*n+1],g=e[3*n+2];return Math.abs(o-u)<Math.abs(a-l)?[new ii(a,1-h),new ii(l,1-c),new ii(d,1-m),new ii(y,1-g)]:[new ii(o,1-h),new ii(u,1-c),new ii(p,1-m),new ii(f,1-g)]}};class ot extends ad{static fromJSON(t){return new ot(t.radius,t.detail)}constructor(t=1,e=0){let i=(1+Math.sqrt(5))/2;super([-1,i,0,1,i,0,-1,-i,0,1,-i,0,0,-1,i,0,1,i,0,-1,-i,0,1,-i,i,0,-1,i,0,1,-i,0,-1,-i,0,1],[0,11,5,0,5,1,0,1,7,0,7,10,0,10,11,1,5,9,5,11,4,11,10,2,10,7,6,7,1,8,3,9,4,3,4,2,3,2,6,3,6,8,3,8,9,4,9,5,2,4,11,6,2,10,8,6,7,9,8,1],t,e),this.type="IcosahedronGeometry",this.parameters={radius:t,detail:e}}}class oe extends rn{copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new oe(t.points,t.segments,t.phiStart,t.phiLength)}constructor(t=[new ii(0,-.5),new ii(.5,0),new ii(0,.5)],e=12,i=0,s=2*Math.PI){super(),this.type="LatheGeometry",this.parameters={points:t,segments:e,phiStart:i,phiLength:s},e=Math.floor(e),s=e6(s,0,2*Math.PI);let r=[],n=[],a=[],o=[],h=[],l=1/e,u=new iR,c=new ii,d=new iR,p=new iR,m=new iR,y=0,f=0;for(let e=0;e<=t.length-1;e++)switch(e){case 0:y=t[e+1].x-t[e].x,d.x=+(f=t[e+1].y-t[e].y),d.y=-y,d.z=0*f,m.copy(d),d.normalize(),o.push(d.x,d.y,d.z);break;case t.length-1:o.push(m.x,m.y,m.z);break;default:y=t[e+1].x-t[e].x,d.x=+(f=t[e+1].y-t[e].y),d.y=-y,d.z=0*f,p.copy(d),d.x+=m.x,d.y+=m.y,d.z+=m.z,d.normalize(),o.push(d.x,d.y,d.z),m.copy(p)}for(let r=0;r<=e;r++){let d=i+r*l*s,p=Math.sin(d),m=Math.cos(d);for(let i=0;i<=t.length-1;i++){u.x=t[i].x*p,u.y=t[i].y,u.z=t[i].x*m,n.push(u.x,u.y,u.z),c.x=r/e,c.y=i/(t.length-1),a.push(c.x,c.y);let s=o[3*i+0]*p,l=o[3*i+1],d=o[3*i+0]*m;h.push(s,l,d)}}for(let i=0;i<e;i++)for(let e=0;e<t.length-1;e++){let s=e+i*t.length,n=s+t.length,a=s+t.length+1,o=s+1;r.push(s,n,o),r.push(a,o,n)}this.setIndex(r),this.setAttribute("position",new s8(n,3)),this.setAttribute("uv",new s8(a,2)),this.setAttribute("normal",new s8(h,3))}}class oi extends ad{static fromJSON(t){return new oi(t.radius,t.detail)}constructor(t=1,e=0){super([1,0,0,-1,0,0,0,1,0,0,-1,0,0,0,1,0,0,-1],[0,2,4,0,4,3,0,3,5,0,5,2,1,2,5,1,5,3,1,3,4,1,4,2],t,e),this.type="OctahedronGeometry",this.parameters={radius:t,detail:e}}}class os extends rn{copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new os(t.width,t.height,t.widthSegments,t.heightSegments)}constructor(t=1,e=1,i=1,s=1){super(),this.type="PlaneGeometry",this.parameters={width:t,height:e,widthSegments:i,heightSegments:s};let r=t/2,n=e/2,a=Math.floor(i),o=Math.floor(s),h=a+1,l=o+1,u=t/a,c=e/o,d=[],p=[],m=[],y=[];for(let t=0;t<l;t++){let e=t*c-n;for(let i=0;i<h;i++){let s=i*u-r;p.push(s,-e,0),m.push(0,0,1),y.push(i/a),y.push(1-t/o)}}for(let t=0;t<o;t++)for(let e=0;e<a;e++){let i=e+h*t,s=e+h*(t+1),r=e+1+h*(t+1),n=e+1+h*t;d.push(i,s,n),d.push(s,r,n)}this.setIndex(d),this.setAttribute("position",new s8(p,3)),this.setAttribute("normal",new s8(m,3)),this.setAttribute("uv",new s8(y,2))}}class or extends rn{copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new or(t.innerRadius,t.outerRadius,t.thetaSegments,t.phiSegments,t.thetaStart,t.thetaLength)}constructor(t=.5,e=1,i=32,s=1,r=0,n=2*Math.PI){super(),this.type="RingGeometry",this.parameters={innerRadius:t,outerRadius:e,thetaSegments:i,phiSegments:s,thetaStart:r,thetaLength:n},i=Math.max(3,i);let a=[],o=[],h=[],l=[],u=t,c=(e-t)/(s=Math.max(1,s)),d=new iR,p=new ii;for(let t=0;t<=s;t++){for(let t=0;t<=i;t++){let s=r+t/i*n;d.x=u*Math.cos(s),d.y=u*Math.sin(s),o.push(d.x,d.y,d.z),h.push(0,0,1),p.x=(d.x/e+1)/2,p.y=(d.y/e+1)/2,l.push(p.x,p.y)}u+=c}for(let t=0;t<s;t++){let e=t*(i+1);for(let t=0;t<i;t++){let s=t+e,r=s+i+1,n=s+i+2,o=s+1;a.push(s,r,o),a.push(r,n,o)}}this.setIndex(a),this.setAttribute("position",new s8(o,3)),this.setAttribute("normal",new s8(h,3)),this.setAttribute("uv",new s8(l,2))}}class on extends rn{copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}toJSON(){let t=super.toJSON();return function(t,e){if(e.shapes=[],Array.isArray(t))for(let i=0,s=t.length;i<s;i++){let s=t[i];e.shapes.push(s.uuid)}else e.shapes.push(t.uuid);return e}(this.parameters.shapes,t)}static fromJSON(t,e){let i=[];for(let s=0,r=t.shapes.length;s<r;s++){let r=e[t.shapes[s]];i.push(r)}return new on(i,t.curveSegments)}constructor(t=new aU([new ii(0,.5),new ii(-.5,-.5),new ii(.5,-.5)]),e=12){super(),this.type="ShapeGeometry",this.parameters={shapes:t,curveSegments:e};let i=[],s=[],r=[],n=[],a=0,o=0;if(!1===Array.isArray(t))h(t);else for(let e=0;e<t.length;e++)h(t[e]),this.addGroup(a,o,e),a+=o,o=0;function h(t){let a=s.length/3,h=t.extractPoints(e),l=h.shape,u=h.holes;!1===a4.isClockWise(l)&&(l=l.reverse());for(let t=0,e=u.length;t<e;t++){let e=u[t];!0===a4.isClockWise(e)&&(u[t]=e.reverse())}let c=a4.triangulateShape(l,u);for(let t=0,e=u.length;t<e;t++){let e=u[t];l=l.concat(e)}for(let t=0,e=l.length;t<e;t++){let e=l[t];s.push(e.x,e.y,0),r.push(0,0,1),n.push(e.x,e.y)}for(let t=0,e=c.length;t<e;t++){let e=c[t],s=e[0]+a,r=e[1]+a,n=e[2]+a;i.push(s,r,n),o+=3}}this.setIndex(i),this.setAttribute("position",new s8(s,3)),this.setAttribute("normal",new s8(r,3)),this.setAttribute("uv",new s8(n,2))}}class oa extends rn{copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new oa(t.radius,t.widthSegments,t.heightSegments,t.phiStart,t.phiLength,t.thetaStart,t.thetaLength)}constructor(t=1,e=32,i=16,s=0,r=2*Math.PI,n=0,a=Math.PI){super(),this.type="SphereGeometry",this.parameters={radius:t,widthSegments:e,heightSegments:i,phiStart:s,phiLength:r,thetaStart:n,thetaLength:a},e=Math.max(3,Math.floor(e)),i=Math.max(2,Math.floor(i));let o=Math.min(n+a,Math.PI),h=0,l=[],u=new iR,c=new iR,d=[],p=[],m=[],y=[];for(let d=0;d<=i;d++){let f=[],g=d/i,x=0;0===d&&0===n?x=.5/e:d===i&&o===Math.PI&&(x=-.5/e);for(let i=0;i<=e;i++){let o=i/e;u.x=-t*Math.cos(s+o*r)*Math.sin(n+g*a),u.y=t*Math.cos(n+g*a),u.z=t*Math.sin(s+o*r)*Math.sin(n+g*a),p.push(u.x,u.y,u.z),c.copy(u).normalize(),m.push(c.x,c.y,c.z),y.push(o+x,1-g),f.push(h++)}l.push(f)}for(let t=0;t<i;t++)for(let s=0;s<e;s++){let e=l[t][s+1],r=l[t][s],a=l[t+1][s],h=l[t+1][s+1];(0!==t||n>0)&&d.push(e,r,h),(t!==i-1||o<Math.PI)&&d.push(r,a,h)}this.setIndex(d),this.setAttribute("position",new s8(p,3)),this.setAttribute("normal",new s8(m,3)),this.setAttribute("uv",new s8(y,2))}}class oo extends ad{static fromJSON(t){return new oo(t.radius,t.detail)}constructor(t=1,e=0){super([1,1,1,-1,-1,1,-1,1,-1,1,-1,-1],[2,1,0,0,3,2,1,3,0,2,3,1],t,e),this.type="TetrahedronGeometry",this.parameters={radius:t,detail:e}}}class oh extends rn{copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new oh(t.radius,t.tube,t.radialSegments,t.tubularSegments,t.arc)}constructor(t=1,e=.4,i=12,s=48,r=2*Math.PI){super(),this.type="TorusGeometry",this.parameters={radius:t,tube:e,radialSegments:i,tubularSegments:s,arc:r},i=Math.floor(i),s=Math.floor(s);let n=[],a=[],o=[],h=[],l=new iR,u=new iR,c=new iR;for(let n=0;n<=i;n++)for(let d=0;d<=s;d++){let p=d/s*r,m=n/i*Math.PI*2;u.x=(t+e*Math.cos(m))*Math.cos(p),u.y=(t+e*Math.cos(m))*Math.sin(p),u.z=e*Math.sin(m),a.push(u.x,u.y,u.z),l.x=t*Math.cos(p),l.y=t*Math.sin(p),c.subVectors(u,l).normalize(),o.push(c.x,c.y,c.z),h.push(d/s),h.push(n/i)}for(let t=1;t<=i;t++)for(let e=1;e<=s;e++){let i=(s+1)*t+e-1,r=(s+1)*(t-1)+e-1,a=(s+1)*(t-1)+e,o=(s+1)*t+e;n.push(i,r,o),n.push(r,a,o)}this.setIndex(n),this.setAttribute("position",new s8(a,3)),this.setAttribute("normal",new s8(o,3)),this.setAttribute("uv",new s8(h,2))}}class ol extends rn{copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new ol(t.radius,t.tube,t.tubularSegments,t.radialSegments,t.p,t.q)}constructor(t=1,e=.4,i=64,s=8,r=2,n=3){super(),this.type="TorusKnotGeometry",this.parameters={radius:t,tube:e,tubularSegments:i,radialSegments:s,p:r,q:n},i=Math.floor(i),s=Math.floor(s);let a=[],o=[],h=[],l=[],u=new iR,c=new iR,d=new iR,p=new iR,m=new iR,y=new iR,f=new iR;for(let a=0;a<=i;++a){let x=a/i*r*Math.PI*2;g(x,r,n,t,d),g(x+.01,r,n,t,p),y.subVectors(p,d),f.addVectors(p,d),m.crossVectors(y,f),f.crossVectors(m,y),m.normalize(),f.normalize();for(let t=0;t<=s;++t){let r=t/s*Math.PI*2,n=-e*Math.cos(r),p=e*Math.sin(r);u.x=d.x+(n*f.x+p*m.x),u.y=d.y+(n*f.y+p*m.y),u.z=d.z+(n*f.z+p*m.z),o.push(u.x,u.y,u.z),c.subVectors(u,d).normalize(),h.push(c.x,c.y,c.z),l.push(a/i),l.push(t/s)}}for(let t=1;t<=i;t++)for(let e=1;e<=s;e++){let i=(s+1)*(t-1)+(e-1),r=(s+1)*t+(e-1),n=(s+1)*t+e,o=(s+1)*(t-1)+e;a.push(i,r,o),a.push(r,n,o)}function g(t,e,i,s,r){let n=Math.cos(t),a=Math.sin(t),o=i/e*t,h=Math.cos(o);r.x=s*(2+h)*.5*n,r.y=s*(2+h)*a*.5,r.z=s*Math.sin(o)*.5}this.setIndex(a),this.setAttribute("position",new s8(o,3)),this.setAttribute("normal",new s8(h,3)),this.setAttribute("uv",new s8(l,2))}}class ou extends rn{copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}toJSON(){let t=super.toJSON();return t.path=this.parameters.path.toJSON(),t}static fromJSON(t){return new ou(new aL[t.path.type]().fromJSON(t.path),t.tubularSegments,t.radius,t.radialSegments,t.closed)}constructor(t=new aN(new iR(-1,-1,0),new iR(-1,1,0),new iR(1,1,0)),e=64,i=1,s=8,r=!1){super(),this.type="TubeGeometry",this.parameters={path:t,tubularSegments:e,radius:i,radialSegments:s,closed:r};let n=t.computeFrenetFrames(e,r);this.tangents=n.tangents,this.normals=n.normals,this.binormals=n.binormals;let a=new iR,o=new iR,h=new ii,l=new iR,u=[],c=[],d=[],p=[];function m(r){l=t.getPointAt(r/e,l);let h=n.normals[r],d=n.binormals[r];for(let t=0;t<=s;t++){let e=t/s*Math.PI*2,r=Math.sin(e),n=-Math.cos(e);o.x=n*h.x+r*d.x,o.y=n*h.y+r*d.y,o.z=n*h.z+r*d.z,o.normalize(),c.push(o.x,o.y,o.z),a.x=l.x+i*o.x,a.y=l.y+i*o.y,a.z=l.z+i*o.z,u.push(a.x,a.y,a.z)}}(function(){for(let t=0;t<e;t++)m(t);m(!1===r?e:0),function(){for(let t=0;t<=e;t++)for(let i=0;i<=s;i++)h.x=t/e,h.y=i/s,d.push(h.x,h.y)}(),function(){for(let t=1;t<=e;t++)for(let e=1;e<=s;e++){let i=(s+1)*(t-1)+(e-1),r=(s+1)*t+(e-1),n=(s+1)*t+e,a=(s+1)*(t-1)+e;p.push(i,r,a),p.push(r,n,a)}}()})(),this.setIndex(p),this.setAttribute("position",new s8(u,3)),this.setAttribute("normal",new s8(c,3)),this.setAttribute("uv",new s8(d,2))}}class oc extends rn{copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}constructor(t=null){if(super(),this.type="WireframeGeometry",this.parameters={geometry:t},null!==t){let e=[],i=new Set,s=new iR,r=new iR;if(null!==t.index){let n=t.attributes.position,a=t.index,o=t.groups;0===o.length&&(o=[{start:0,count:a.count,materialIndex:0}]);for(let t=0,h=o.length;t<h;++t){let h=o[t],l=h.start,u=h.count;for(let t=l,o=l+u;t<o;t+=3)for(let o=0;o<3;o++){let h=a.getX(t+o),l=a.getX(t+(o+1)%3);s.fromBufferAttribute(n,h),r.fromBufferAttribute(n,l),!0===od(s,r,i)&&(e.push(s.x,s.y,s.z),e.push(r.x,r.y,r.z))}}}else{let n=t.attributes.position;for(let t=0,a=n.count/3;t<a;t++)for(let a=0;a<3;a++){let o=3*t+a,h=3*t+(a+1)%3;s.fromBufferAttribute(n,o),r.fromBufferAttribute(n,h),!0===od(s,r,i)&&(e.push(s.x,s.y,s.z),e.push(r.x,r.y,r.z))}}this.setAttribute("position",new s8(e,3))}}}function od(t,e,i){let s="".concat(t.x,",").concat(t.y,",").concat(t.z,"-").concat(e.x,",").concat(e.y,",").concat(e.z),r="".concat(e.x,",").concat(e.y,",").concat(e.z,"-").concat(t.x,",").concat(t.y,",").concat(t.z);return!0!==i.has(s)&&!0!==i.has(r)&&(i.add(s),i.add(r),!0)}var op=Object.freeze({__proto__:null,BoxGeometry:rv,CapsuleGeometry:ah,CircleGeometry:al,ConeGeometry:ac,CylinderGeometry:au,DodecahedronGeometry:ap,EdgesGeometry:ax,ExtrudeGeometry:a9,IcosahedronGeometry:ot,LatheGeometry:oe,OctahedronGeometry:oi,PlaneGeometry:os,PolyhedronGeometry:ad,RingGeometry:or,ShapeGeometry:on,SphereGeometry:oa,TetrahedronGeometry:oo,TorusGeometry:oh,TorusKnotGeometry:ol,TubeGeometry:ou,WireframeGeometry:oc});class om extends sZ{copy(t){return super.copy(t),this.color.copy(t.color),this.fog=t.fog,this}constructor(t){super(),this.isShadowMaterial=!0,this.type="ShadowMaterial",this.color=new sq(0),this.transparent=!0,this.fog=!0,this.setValues(t)}}class oy extends r_{constructor(t){super(t),this.isRawShaderMaterial=!0,this.type="RawShaderMaterial"}}class of extends sZ{copy(t){return super.copy(t),this.defines={STANDARD:""},this.color.copy(t.color),this.roughness=t.roughness,this.metalness=t.metalness,this.map=t.map,this.lightMap=t.lightMap,this.lightMapIntensity=t.lightMapIntensity,this.aoMap=t.aoMap,this.aoMapIntensity=t.aoMapIntensity,this.emissive.copy(t.emissive),this.emissiveMap=t.emissiveMap,this.emissiveIntensity=t.emissiveIntensity,this.bumpMap=t.bumpMap,this.bumpScale=t.bumpScale,this.normalMap=t.normalMap,this.normalMapType=t.normalMapType,this.normalScale.copy(t.normalScale),this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this.roughnessMap=t.roughnessMap,this.metalnessMap=t.metalnessMap,this.alphaMap=t.alphaMap,this.envMap=t.envMap,this.envMapRotation.copy(t.envMapRotation),this.envMapIntensity=t.envMapIntensity,this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this.wireframeLinecap=t.wireframeLinecap,this.wireframeLinejoin=t.wireframeLinejoin,this.flatShading=t.flatShading,this.fog=t.fog,this}constructor(t){super(),this.isMeshStandardMaterial=!0,this.type="MeshStandardMaterial",this.defines={STANDARD:""},this.color=new sq(0xffffff),this.roughness=1,this.metalness=0,this.map=null,this.lightMap=null,this.lightMapIntensity=1,this.aoMap=null,this.aoMapIntensity=1,this.emissive=new sq(0),this.emissiveIntensity=1,this.emissiveMap=null,this.bumpMap=null,this.bumpScale=1,this.normalMap=null,this.normalMapType=ex,this.normalScale=new ii(1,1),this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.roughnessMap=null,this.metalnessMap=null,this.alphaMap=null,this.envMap=null,this.envMapRotation=new su,this.envMapIntensity=1,this.wireframe=!1,this.wireframeLinewidth=1,this.wireframeLinecap="round",this.wireframeLinejoin="round",this.flatShading=!1,this.fog=!0,this.setValues(t)}}class og extends of{get anisotropy(){return this._anisotropy}set anisotropy(t){this._anisotropy>0!=t>0&&this.version++,this._anisotropy=t}get clearcoat(){return this._clearcoat}set clearcoat(t){this._clearcoat>0!=t>0&&this.version++,this._clearcoat=t}get iridescence(){return this._iridescence}set iridescence(t){this._iridescence>0!=t>0&&this.version++,this._iridescence=t}get dispersion(){return this._dispersion}set dispersion(t){this._dispersion>0!=t>0&&this.version++,this._dispersion=t}get sheen(){return this._sheen}set sheen(t){this._sheen>0!=t>0&&this.version++,this._sheen=t}get transmission(){return this._transmission}set transmission(t){this._transmission>0!=t>0&&this.version++,this._transmission=t}copy(t){return super.copy(t),this.defines={STANDARD:"",PHYSICAL:""},this.anisotropy=t.anisotropy,this.anisotropyRotation=t.anisotropyRotation,this.anisotropyMap=t.anisotropyMap,this.clearcoat=t.clearcoat,this.clearcoatMap=t.clearcoatMap,this.clearcoatRoughness=t.clearcoatRoughness,this.clearcoatRoughnessMap=t.clearcoatRoughnessMap,this.clearcoatNormalMap=t.clearcoatNormalMap,this.clearcoatNormalScale.copy(t.clearcoatNormalScale),this.dispersion=t.dispersion,this.ior=t.ior,this.iridescence=t.iridescence,this.iridescenceMap=t.iridescenceMap,this.iridescenceIOR=t.iridescenceIOR,this.iridescenceThicknessRange=[...t.iridescenceThicknessRange],this.iridescenceThicknessMap=t.iridescenceThicknessMap,this.sheen=t.sheen,this.sheenColor.copy(t.sheenColor),this.sheenColorMap=t.sheenColorMap,this.sheenRoughness=t.sheenRoughness,this.sheenRoughnessMap=t.sheenRoughnessMap,this.transmission=t.transmission,this.transmissionMap=t.transmissionMap,this.thickness=t.thickness,this.thicknessMap=t.thicknessMap,this.attenuationDistance=t.attenuationDistance,this.attenuationColor.copy(t.attenuationColor),this.specularIntensity=t.specularIntensity,this.specularIntensityMap=t.specularIntensityMap,this.specularColor.copy(t.specularColor),this.specularColorMap=t.specularColorMap,this}constructor(t){super(),this.isMeshPhysicalMaterial=!0,this.defines={STANDARD:"",PHYSICAL:""},this.type="MeshPhysicalMaterial",this.anisotropyRotation=0,this.anisotropyMap=null,this.clearcoatMap=null,this.clearcoatRoughness=0,this.clearcoatRoughnessMap=null,this.clearcoatNormalScale=new ii(1,1),this.clearcoatNormalMap=null,this.ior=1.5,Object.defineProperty(this,"reflectivity",{get:function(){return e6(2.5*(this.ior-1)/(this.ior+1),0,1)},set:function(t){this.ior=(1+.4*t)/(1-.4*t)}}),this.iridescenceMap=null,this.iridescenceIOR=1.3,this.iridescenceThicknessRange=[100,400],this.iridescenceThicknessMap=null,this.sheenColor=new sq(0),this.sheenColorMap=null,this.sheenRoughness=1,this.sheenRoughnessMap=null,this.transmissionMap=null,this.thickness=0,this.thicknessMap=null,this.attenuationDistance=1/0,this.attenuationColor=new sq(1,1,1),this.specularIntensity=1,this.specularIntensityMap=null,this.specularColor=new sq(1,1,1),this.specularColorMap=null,this._anisotropy=0,this._clearcoat=0,this._dispersion=0,this._iridescence=0,this._sheen=0,this._transmission=0,this.setValues(t)}}class ox extends sZ{copy(t){return super.copy(t),this.color.copy(t.color),this.specular.copy(t.specular),this.shininess=t.shininess,this.map=t.map,this.lightMap=t.lightMap,this.lightMapIntensity=t.lightMapIntensity,this.aoMap=t.aoMap,this.aoMapIntensity=t.aoMapIntensity,this.emissive.copy(t.emissive),this.emissiveMap=t.emissiveMap,this.emissiveIntensity=t.emissiveIntensity,this.bumpMap=t.bumpMap,this.bumpScale=t.bumpScale,this.normalMap=t.normalMap,this.normalMapType=t.normalMapType,this.normalScale.copy(t.normalScale),this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this.specularMap=t.specularMap,this.alphaMap=t.alphaMap,this.envMap=t.envMap,this.envMapRotation.copy(t.envMapRotation),this.combine=t.combine,this.reflectivity=t.reflectivity,this.refractionRatio=t.refractionRatio,this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this.wireframeLinecap=t.wireframeLinecap,this.wireframeLinejoin=t.wireframeLinejoin,this.flatShading=t.flatShading,this.fog=t.fog,this}constructor(t){super(),this.isMeshPhongMaterial=!0,this.type="MeshPhongMaterial",this.color=new sq(0xffffff),this.specular=new sq(1118481),this.shininess=30,this.map=null,this.lightMap=null,this.lightMapIntensity=1,this.aoMap=null,this.aoMapIntensity=1,this.emissive=new sq(0),this.emissiveIntensity=1,this.emissiveMap=null,this.bumpMap=null,this.bumpScale=1,this.normalMap=null,this.normalMapType=ex,this.normalScale=new ii(1,1),this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.specularMap=null,this.alphaMap=null,this.envMap=null,this.envMapRotation=new su,this.combine=K,this.reflectivity=1,this.refractionRatio=.98,this.wireframe=!1,this.wireframeLinewidth=1,this.wireframeLinecap="round",this.wireframeLinejoin="round",this.flatShading=!1,this.fog=!0,this.setValues(t)}}class ov extends sZ{copy(t){return super.copy(t),this.color.copy(t.color),this.map=t.map,this.gradientMap=t.gradientMap,this.lightMap=t.lightMap,this.lightMapIntensity=t.lightMapIntensity,this.aoMap=t.aoMap,this.aoMapIntensity=t.aoMapIntensity,this.emissive.copy(t.emissive),this.emissiveMap=t.emissiveMap,this.emissiveIntensity=t.emissiveIntensity,this.bumpMap=t.bumpMap,this.bumpScale=t.bumpScale,this.normalMap=t.normalMap,this.normalMapType=t.normalMapType,this.normalScale.copy(t.normalScale),this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this.alphaMap=t.alphaMap,this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this.wireframeLinecap=t.wireframeLinecap,this.wireframeLinejoin=t.wireframeLinejoin,this.fog=t.fog,this}constructor(t){super(),this.isMeshToonMaterial=!0,this.defines={TOON:""},this.type="MeshToonMaterial",this.color=new sq(0xffffff),this.map=null,this.gradientMap=null,this.lightMap=null,this.lightMapIntensity=1,this.aoMap=null,this.aoMapIntensity=1,this.emissive=new sq(0),this.emissiveIntensity=1,this.emissiveMap=null,this.bumpMap=null,this.bumpScale=1,this.normalMap=null,this.normalMapType=ex,this.normalScale=new ii(1,1),this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.alphaMap=null,this.wireframe=!1,this.wireframeLinewidth=1,this.wireframeLinecap="round",this.wireframeLinejoin="round",this.fog=!0,this.setValues(t)}}class ob extends sZ{copy(t){return super.copy(t),this.bumpMap=t.bumpMap,this.bumpScale=t.bumpScale,this.normalMap=t.normalMap,this.normalMapType=t.normalMapType,this.normalScale.copy(t.normalScale),this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this.flatShading=t.flatShading,this}constructor(t){super(),this.isMeshNormalMaterial=!0,this.type="MeshNormalMaterial",this.bumpMap=null,this.bumpScale=1,this.normalMap=null,this.normalMapType=ex,this.normalScale=new ii(1,1),this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.wireframe=!1,this.wireframeLinewidth=1,this.flatShading=!1,this.setValues(t)}}class oM extends sZ{copy(t){return super.copy(t),this.color.copy(t.color),this.map=t.map,this.lightMap=t.lightMap,this.lightMapIntensity=t.lightMapIntensity,this.aoMap=t.aoMap,this.aoMapIntensity=t.aoMapIntensity,this.emissive.copy(t.emissive),this.emissiveMap=t.emissiveMap,this.emissiveIntensity=t.emissiveIntensity,this.bumpMap=t.bumpMap,this.bumpScale=t.bumpScale,this.normalMap=t.normalMap,this.normalMapType=t.normalMapType,this.normalScale.copy(t.normalScale),this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this.specularMap=t.specularMap,this.alphaMap=t.alphaMap,this.envMap=t.envMap,this.envMapRotation.copy(t.envMapRotation),this.combine=t.combine,this.reflectivity=t.reflectivity,this.refractionRatio=t.refractionRatio,this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this.wireframeLinecap=t.wireframeLinecap,this.wireframeLinejoin=t.wireframeLinejoin,this.flatShading=t.flatShading,this.fog=t.fog,this}constructor(t){super(),this.isMeshLambertMaterial=!0,this.type="MeshLambertMaterial",this.color=new sq(0xffffff),this.map=null,this.lightMap=null,this.lightMapIntensity=1,this.aoMap=null,this.aoMapIntensity=1,this.emissive=new sq(0),this.emissiveIntensity=1,this.emissiveMap=null,this.bumpMap=null,this.bumpScale=1,this.normalMap=null,this.normalMapType=ex,this.normalScale=new ii(1,1),this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.specularMap=null,this.alphaMap=null,this.envMap=null,this.envMapRotation=new su,this.combine=K,this.reflectivity=1,this.refractionRatio=.98,this.wireframe=!1,this.wireframeLinewidth=1,this.wireframeLinecap="round",this.wireframeLinejoin="round",this.flatShading=!1,this.fog=!0,this.setValues(t)}}class ow extends sZ{copy(t){return super.copy(t),this.depthPacking=t.depthPacking,this.map=t.map,this.alphaMap=t.alphaMap,this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this}constructor(t){super(),this.isMeshDepthMaterial=!0,this.type="MeshDepthMaterial",this.depthPacking=3200,this.map=null,this.alphaMap=null,this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.wireframe=!1,this.wireframeLinewidth=1,this.setValues(t)}}class oS extends sZ{copy(t){return super.copy(t),this.map=t.map,this.alphaMap=t.alphaMap,this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this}constructor(t){super(),this.isMeshDistanceMaterial=!0,this.type="MeshDistanceMaterial",this.map=null,this.alphaMap=null,this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.setValues(t)}}class o_ extends sZ{copy(t){return super.copy(t),this.defines={MATCAP:""},this.color.copy(t.color),this.matcap=t.matcap,this.map=t.map,this.bumpMap=t.bumpMap,this.bumpScale=t.bumpScale,this.normalMap=t.normalMap,this.normalMapType=t.normalMapType,this.normalScale.copy(t.normalScale),this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this.alphaMap=t.alphaMap,this.flatShading=t.flatShading,this.fog=t.fog,this}constructor(t){super(),this.isMeshMatcapMaterial=!0,this.defines={MATCAP:""},this.type="MeshMatcapMaterial",this.color=new sq(0xffffff),this.matcap=null,this.map=null,this.bumpMap=null,this.bumpScale=1,this.normalMap=null,this.normalMapType=ex,this.normalScale=new ii(1,1),this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.alphaMap=null,this.flatShading=!1,this.fog=!0,this.setValues(t)}}class oA extends nX{copy(t){return super.copy(t),this.scale=t.scale,this.dashSize=t.dashSize,this.gapSize=t.gapSize,this}constructor(t){super(),this.isLineDashedMaterial=!0,this.type="LineDashedMaterial",this.scale=1,this.dashSize=3,this.gapSize=1,this.setValues(t)}}function oz(t,e){return t&&t.constructor!==e?"number"==typeof e.BYTES_PER_ELEMENT?new e(t):Array.prototype.slice.call(t):t}function oT(t,e,i){let s=t.length,r=new t.constructor(s);for(let n=0,a=0;a!==s;++n){let s=i[n]*e;for(let i=0;i!==e;++i)r[a++]=t[s+i]}return r}function oI(t,e,i,s){let r=1,n=t[0];for(;void 0!==n&&void 0===n[s];)n=t[r++];if(void 0===n)return;let a=n[s];if(void 0!==a)if(Array.isArray(a))do void 0!==(a=n[s])&&(e.push(n.time),i.push(...a)),n=t[r++];while(void 0!==n);else if(void 0!==a.toArray)do void 0!==(a=n[s])&&(e.push(n.time),a.toArray(i,i.length)),n=t[r++];while(void 0!==n);else do void 0!==(a=n[s])&&(e.push(n.time),i.push(a)),n=t[r++];while(void 0!==n)}class oC{evaluate(t){let e=this.parameterPositions,i=this._cachedIndex,s=e[i],r=e[i-1];t:{e:{let n;i:{s:if(!(t<s)){for(let n=i+2;;){if(void 0===s){if(t<r)break s;return i=e.length,this._cachedIndex=i,this.copySampleValue_(i-1)}if(i===n)break;if(r=s,t<(s=e[++i]))break e}n=e.length;break i}if(!(t>=r)){let a=e[1];t<a&&(i=2,r=a);for(let n=i-2;;){if(void 0===r)return this._cachedIndex=0,this.copySampleValue_(0);if(i===n)break;if(s=r,t>=(r=e[--i-1]))break e}n=i,i=0;break i}break t}for(;i<n;){let s=i+n>>>1;t<e[s]?n=s:i=s+1}if(s=e[i],void 0===(r=e[i-1]))return this._cachedIndex=0,this.copySampleValue_(0);if(void 0===s)return i=e.length,this._cachedIndex=i,this.copySampleValue_(i-1)}this._cachedIndex=i,this.intervalChanged_(i,r,s)}return this.interpolate_(i,r,t,s)}getSettings_(){return this.settings||this.DefaultSettings_}copySampleValue_(t){let e=this.resultBuffer,i=this.sampleValues,s=this.valueSize,r=t*s;for(let t=0;t!==s;++t)e[t]=i[r+t];return e}interpolate_(){throw Error("call to abstract method")}intervalChanged_(){}constructor(t,e,i,s){this.parameterPositions=t,this._cachedIndex=0,this.resultBuffer=void 0!==s?s:new e.constructor(i),this.sampleValues=e,this.valueSize=i,this.settings=null,this.DefaultSettings_={}}}class oB extends oC{intervalChanged_(t,e,i){let s=this.parameterPositions,r=t-2,n=t+1,a=s[r],o=s[n];if(void 0===a)switch(this.getSettings_().endingStart){case 2401:r=t,a=2*e-i;break;case 2402:r=s.length-2,a=e+s[r]-s[r+1];break;default:r=t,a=i}if(void 0===o)switch(this.getSettings_().endingEnd){case 2401:n=t,o=2*i-e;break;case 2402:n=1,o=i+s[1]-s[0];break;default:n=t-1,o=e}let h=(i-e)*.5,l=this.valueSize;this._weightPrev=h/(e-a),this._weightNext=h/(o-i),this._offsetPrev=r*l,this._offsetNext=n*l}interpolate_(t,e,i,s){let r=this.resultBuffer,n=this.sampleValues,a=this.valueSize,o=t*a,h=o-a,l=this._offsetPrev,u=this._offsetNext,c=this._weightPrev,d=this._weightNext,p=(i-e)/(s-e),m=p*p,y=m*p,f=-c*y+2*c*m-c*p,g=(1+c)*y+(-1.5-2*c)*m+(-.5+c)*p+1,x=(-1-d)*y+(1.5+d)*m+.5*p,v=d*y-d*m;for(let t=0;t!==a;++t)r[t]=f*n[l+t]+g*n[h+t]+x*n[o+t]+v*n[u+t];return r}constructor(t,e,i,s){super(t,e,i,s),this._weightPrev=-0,this._offsetPrev=-0,this._weightNext=-0,this._offsetNext=-0,this.DefaultSettings_={endingStart:2400,endingEnd:2400}}}class ok extends oC{interpolate_(t,e,i,s){let r=this.resultBuffer,n=this.sampleValues,a=this.valueSize,o=t*a,h=o-a,l=(i-e)/(s-e),u=1-l;for(let t=0;t!==a;++t)r[t]=n[h+t]*u+n[o+t]*l;return r}constructor(t,e,i,s){super(t,e,i,s)}}class oO extends oC{interpolate_(t){return this.copySampleValue_(t-1)}constructor(t,e,i,s){super(t,e,i,s)}}class oE{static toJSON(t){let e,i=t.constructor;if(i.toJSON!==this.toJSON)e=i.toJSON(t);else{e={name:t.name,times:oz(t.times,Array),values:oz(t.values,Array)};let i=t.getInterpolation();i!==t.DefaultInterpolation&&(e.interpolation=i)}return e.type=t.ValueTypeName,e}InterpolantFactoryMethodDiscrete(t){return new oO(this.times,this.values,this.getValueSize(),t)}InterpolantFactoryMethodLinear(t){return new ok(this.times,this.values,this.getValueSize(),t)}InterpolantFactoryMethodSmooth(t){return new oB(this.times,this.values,this.getValueSize(),t)}setInterpolation(t){let e;switch(t){case 2300:e=this.InterpolantFactoryMethodDiscrete;break;case 2301:e=this.InterpolantFactoryMethodLinear;break;case 2302:e=this.InterpolantFactoryMethodSmooth}if(void 0===e){let e="unsupported interpolation for "+this.ValueTypeName+" keyframe track named "+this.name;if(void 0===this.createInterpolant)if(t!==this.DefaultInterpolation)this.setInterpolation(this.DefaultInterpolation);else throw Error(e);return console.warn("THREE.KeyframeTrack:",e),this}return this.createInterpolant=e,this}getInterpolation(){switch(this.createInterpolant){case this.InterpolantFactoryMethodDiscrete:return 2300;case this.InterpolantFactoryMethodLinear:return 2301;case this.InterpolantFactoryMethodSmooth:return 2302}}getValueSize(){return this.values.length/this.times.length}shift(t){if(0!==t){let e=this.times;for(let i=0,s=e.length;i!==s;++i)e[i]+=t}return this}scale(t){if(1!==t){let e=this.times;for(let i=0,s=e.length;i!==s;++i)e[i]*=t}return this}trim(t,e){let i=this.times,s=i.length,r=0,n=s-1;for(;r!==s&&i[r]<t;)++r;for(;-1!==n&&i[n]>e;)--n;if(++n,0!==r||n!==s){r>=n&&(r=(n=Math.max(n,1))-1);let t=this.getValueSize();this.times=i.slice(r,n),this.values=this.values.slice(r*t,n*t)}return this}validate(){var t;let e=!0,i=this.getValueSize();i-Math.floor(i)!=0&&(console.error("THREE.KeyframeTrack: Invalid value size in track.",this),e=!1);let s=this.times,r=this.values,n=s.length;0===n&&(console.error("THREE.KeyframeTrack: Track is empty.",this),e=!1);let a=null;for(let t=0;t!==n;t++){let i=s[t];if("number"==typeof i&&isNaN(i)){console.error("THREE.KeyframeTrack: Time is not a valid number.",this,t,i),e=!1;break}if(null!==a&&a>i){console.error("THREE.KeyframeTrack: Out of order keys.",this,t,i,a),e=!1;break}a=i}if(void 0!==r&&ArrayBuffer.isView(t=r)&&!(t instanceof DataView))for(let t=0,i=r.length;t!==i;++t){let i=r[t];if(isNaN(i)){console.error("THREE.KeyframeTrack: Value is not a valid number.",this,t,i),e=!1;break}}return e}optimize(){let t=this.times.slice(),e=this.values.slice(),i=this.getValueSize(),s=2302===this.getInterpolation(),r=t.length-1,n=1;for(let a=1;a<r;++a){let r=!1,o=t[a];if(o!==t[a+1]&&(1!==a||o!==t[0]))if(s)r=!0;else{let t=a*i,s=t-i,n=t+i;for(let a=0;a!==i;++a){let i=e[t+a];if(i!==e[s+a]||i!==e[n+a]){r=!0;break}}}if(r){if(a!==n){t[n]=t[a];let s=a*i,r=n*i;for(let t=0;t!==i;++t)e[r+t]=e[s+t]}++n}}if(r>0){t[n]=t[r];for(let t=r*i,s=n*i,a=0;a!==i;++a)e[s+a]=e[t+a];++n}return n!==t.length?(this.times=t.slice(0,n),this.values=e.slice(0,n*i)):(this.times=t,this.values=e),this}clone(){let t=this.times.slice(),e=this.values.slice(),i=new this.constructor(this.name,t,e);return i.createInterpolant=this.createInterpolant,i}constructor(t,e,i,s){if(void 0===t)throw Error("THREE.KeyframeTrack: track name is undefined");if(void 0===e||0===e.length)throw Error("THREE.KeyframeTrack: no keyframes in track named "+t);this.name=t,this.times=oz(e,this.TimeBufferType),this.values=oz(i,this.ValueBufferType),this.setInterpolation(s||this.DefaultInterpolation)}}oE.prototype.ValueTypeName="",oE.prototype.TimeBufferType=Float32Array,oE.prototype.ValueBufferType=Float32Array,oE.prototype.DefaultInterpolation=2301;class oR extends oE{constructor(t,e,i){super(t,e,i)}}oR.prototype.ValueTypeName="bool",oR.prototype.ValueBufferType=Array,oR.prototype.DefaultInterpolation=2300,oR.prototype.InterpolantFactoryMethodLinear=void 0,oR.prototype.InterpolantFactoryMethodSmooth=void 0;class oP extends oE{constructor(t,e,i,s){super(t,e,i,s)}}oP.prototype.ValueTypeName="color";class oN extends oE{constructor(t,e,i,s){super(t,e,i,s)}}oN.prototype.ValueTypeName="number";class oV extends oC{interpolate_(t,e,i,s){let r=this.resultBuffer,n=this.sampleValues,a=this.valueSize,o=(i-e)/(s-e),h=t*a;for(let t=h+a;h!==t;h+=4)iE.slerpFlat(r,0,n,h-a,n,h,o);return r}constructor(t,e,i,s){super(t,e,i,s)}}class oL extends oE{InterpolantFactoryMethodLinear(t){return new oV(this.times,this.values,this.getValueSize(),t)}constructor(t,e,i,s){super(t,e,i,s)}}oL.prototype.ValueTypeName="quaternion",oL.prototype.InterpolantFactoryMethodSmooth=void 0;class oF extends oE{constructor(t,e,i){super(t,e,i)}}oF.prototype.ValueTypeName="string",oF.prototype.ValueBufferType=Array,oF.prototype.DefaultInterpolation=2300,oF.prototype.InterpolantFactoryMethodLinear=void 0,oF.prototype.InterpolantFactoryMethodSmooth=void 0;class oj extends oE{constructor(t,e,i,s){super(t,e,i,s)}}oj.prototype.ValueTypeName="vector";class oU{static parse(t){let e=[],i=t.tracks,s=1/(t.fps||1);for(let t=0,r=i.length;t!==r;++t)e.push((function(t){if(void 0===t.type)throw Error("THREE.KeyframeTrack: track type undefined, can not parse");let e=function(t){switch(t.toLowerCase()){case"scalar":case"double":case"float":case"number":case"integer":return oN;case"vector":case"vector2":case"vector3":case"vector4":return oj;case"color":return oP;case"quaternion":return oL;case"bool":case"boolean":return oR;case"string":return oF}throw Error("THREE.KeyframeTrack: Unsupported typeName: "+t)}(t.type);if(void 0===t.times){let e=[],i=[];oI(t.keys,e,i,"value"),t.times=e,t.values=i}return void 0!==e.parse?e.parse(t):new e(t.name,t.times,t.values,t.interpolation)})(i[t]).scale(s));let r=new this(t.name,t.duration,e,t.blendMode);return r.uuid=t.uuid,r}static toJSON(t){let e=[],i=t.tracks,s={name:t.name,duration:t.duration,tracks:e,uuid:t.uuid,blendMode:t.blendMode};for(let t=0,s=i.length;t!==s;++t)e.push(oE.toJSON(i[t]));return s}static CreateFromMorphTargetSequence(t,e,i,s){let r=e.length,n=[];for(let t=0;t<r;t++){let a=[],o=[];a.push((t+r-1)%r,t,(t+1)%r),o.push(0,1,0);let h=function(t){let e=t.length,i=Array(e);for(let t=0;t!==e;++t)i[t]=t;return i.sort(function(e,i){return t[e]-t[i]}),i}(a);a=oT(a,1,h),o=oT(o,1,h),s||0!==a[0]||(a.push(r),o.push(o[0])),n.push(new oN(".morphTargetInfluences["+e[t].name+"]",a,o).scale(1/i))}return new this(t,-1,n)}static findByName(t,e){let i=t;Array.isArray(t)||(i=t.geometry&&t.geometry.animations||t.animations);for(let t=0;t<i.length;t++)if(i[t].name===e)return i[t];return null}static CreateClipsFromMorphTargetSequences(t,e,i){let s={},r=/^([\w-]*?)([\d]+)$/;for(let e=0,i=t.length;e<i;e++){let i=t[e],n=i.name.match(r);if(n&&n.length>1){let t=n[1],e=s[t];e||(s[t]=e=[]),e.push(i)}}let n=[];for(let t in s)n.push(this.CreateFromMorphTargetSequence(t,s[t],e,i));return n}static parseAnimation(t,e){if(console.warn("THREE.AnimationClip: parseAnimation() is deprecated and will be removed with r185"),!t)return console.error("THREE.AnimationClip: No animation in JSONLoader data."),null;let i=function(t,e,i,s,r){if(0!==i.length){let n=[],a=[];oI(i,n,a,s),0!==n.length&&r.push(new t(e,n,a))}},s=[],r=t.name||"default",n=t.fps||30,a=t.blendMode,o=t.length||-1,h=t.hierarchy||[];for(let t=0;t<h.length;t++){let r=h[t].keys;if(r&&0!==r.length)if(r[0].morphTargets){let t,e={};for(t=0;t<r.length;t++)if(r[t].morphTargets)for(let i=0;i<r[t].morphTargets.length;i++)e[r[t].morphTargets[i]]=-1;for(let i in e){let e=[],n=[];for(let s=0;s!==r[t].morphTargets.length;++s){let s=r[t];e.push(s.time),n.push(+(s.morphTarget===i))}s.push(new oN(".morphTargetInfluence["+i+"]",e,n))}o=e.length*n}else{let n=".bones["+e[t].name+"]";i(oj,n+".position",r,"pos",s),i(oL,n+".quaternion",r,"rot",s),i(oj,n+".scale",r,"scl",s)}}return 0===s.length?null:new this(r,o,s,a)}resetDuration(){let t=this.tracks,e=0;for(let i=0,s=t.length;i!==s;++i){let t=this.tracks[i];e=Math.max(e,t.times[t.times.length-1])}return this.duration=e,this}trim(){for(let t=0;t<this.tracks.length;t++)this.tracks[t].trim(0,this.duration);return this}validate(){let t=!0;for(let e=0;e<this.tracks.length;e++)t=t&&this.tracks[e].validate();return t}optimize(){for(let t=0;t<this.tracks.length;t++)this.tracks[t].optimize();return this}clone(){let t=[];for(let e=0;e<this.tracks.length;e++)t.push(this.tracks[e].clone());return new this.constructor(this.name,this.duration,t,this.blendMode)}toJSON(){return this.constructor.toJSON(this)}constructor(t="",e=-1,i=[],s=2500){this.name=t,this.tracks=i,this.duration=e,this.blendMode=s,this.uuid=e4(),this.duration<0&&this.resetDuration()}}let oD={enabled:!1,files:{},add:function(t,e){!1!==this.enabled&&(this.files[t]=e)},get:function(t){if(!1!==this.enabled)return this.files[t]},remove:function(t){delete this.files[t]},clear:function(){this.files={}}};class oW{constructor(t,e,i){let s,r=this,n=!1,a=0,o=0,h=[];this.onStart=void 0,this.onLoad=t,this.onProgress=e,this.onError=i,this.itemStart=function(t){o++,!1===n&&void 0!==r.onStart&&r.onStart(t,a,o),n=!0},this.itemEnd=function(t){a++,void 0!==r.onProgress&&r.onProgress(t,a,o),a===o&&(n=!1,void 0!==r.onLoad&&r.onLoad())},this.itemError=function(t){void 0!==r.onError&&r.onError(t)},this.resolveURL=function(t){return s?s(t):t},this.setURLModifier=function(t){return s=t,this},this.addHandler=function(t,e){return h.push(t,e),this},this.removeHandler=function(t){let e=h.indexOf(t);return -1!==e&&h.splice(e,2),this},this.getHandler=function(t){for(let e=0,i=h.length;e<i;e+=2){let i=h[e],s=h[e+1];if(i.global&&(i.lastIndex=0),i.test(t))return s}return null}}}let oH=new oW;class oJ{load(){}loadAsync(t,e){let i=this;return new Promise(function(s,r){i.load(t,s,e,r)})}parse(){}setCrossOrigin(t){return this.crossOrigin=t,this}setWithCredentials(t){return this.withCredentials=t,this}setPath(t){return this.path=t,this}setResourcePath(t){return this.resourcePath=t,this}setRequestHeader(t){return this.requestHeader=t,this}constructor(t){this.manager=void 0!==t?t:oH,this.crossOrigin="anonymous",this.withCredentials=!1,this.path="",this.resourcePath="",this.requestHeader={}}}oJ.DEFAULT_MATERIAL_NAME="__DEFAULT";let oq={};class oG extends Error{constructor(t,e){super(t),this.response=e}}class oX extends oJ{load(t,e,i,s){void 0===t&&(t=""),void 0!==this.path&&(t=this.path+t),t=this.manager.resolveURL(t);let r=oD.get(t);if(void 0!==r)return this.manager.itemStart(t),setTimeout(()=>{e&&e(r),this.manager.itemEnd(t)},0),r;if(void 0!==oq[t])return void oq[t].push({onLoad:e,onProgress:i,onError:s});oq[t]=[],oq[t].push({onLoad:e,onProgress:i,onError:s});let n=new Request(t,{headers:new Headers(this.requestHeader),credentials:this.withCredentials?"include":"same-origin"}),a=this.mimeType,o=this.responseType;fetch(n).then(e=>{if(200===e.status||0===e.status){if(0===e.status&&console.warn("THREE.FileLoader: HTTP Status 0 received."),"undefined"==typeof ReadableStream||void 0===e.body||void 0===e.body.getReader)return e;let i=oq[t],s=e.body.getReader(),r=e.headers.get("X-File-Size")||e.headers.get("Content-Length"),n=r?parseInt(r):0,a=0!==n,o=0;return new Response(new ReadableStream({start(t){!function e(){s.read().then(s=>{let{done:r,value:h}=s;if(r)t.close();else{let s=new ProgressEvent("progress",{lengthComputable:a,loaded:o+=h.byteLength,total:n});for(let t=0,e=i.length;t<e;t++){let e=i[t];e.onProgress&&e.onProgress(s)}t.enqueue(h),e()}},e=>{t.error(e)})}()}}))}throw new oG('fetch for "'.concat(e.url,'" responded with ').concat(e.status,": ").concat(e.statusText),e)}).then(t=>{switch(o){case"arraybuffer":return t.arrayBuffer();case"blob":return t.blob();case"document":return t.text().then(t=>new DOMParser().parseFromString(t,a));case"json":return t.json();default:if(""===a)return t.text();{let e=/charset="?([^;"\s]*)"?/i.exec(a),i=new TextDecoder(e&&e[1]?e[1].toLowerCase():void 0);return t.arrayBuffer().then(t=>i.decode(t))}}}).then(e=>{oD.add(t,e);let i=oq[t];delete oq[t];for(let t=0,s=i.length;t<s;t++){let s=i[t];s.onLoad&&s.onLoad(e)}}).catch(e=>{let i=oq[t];if(void 0===i)throw this.manager.itemError(t),e;delete oq[t];for(let t=0,s=i.length;t<s;t++){let s=i[t];s.onError&&s.onError(e)}this.manager.itemError(t)}).finally(()=>{this.manager.itemEnd(t)}),this.manager.itemStart(t)}setResponseType(t){return this.responseType=t,this}setMimeType(t){return this.mimeType=t,this}constructor(t){super(t),this.mimeType="",this.responseType=""}}class oZ extends oJ{load(t,e,i,s){void 0!==this.path&&(t=this.path+t),t=this.manager.resolveURL(t);let r=this,n=oD.get(t);if(void 0!==n)return r.manager.itemStart(t),setTimeout(function(){e&&e(n),r.manager.itemEnd(t)},0),n;let a=il("img");function o(){l(),oD.add(t,this),e&&e(this),r.manager.itemEnd(t)}function h(e){l(),s&&s(e),r.manager.itemError(t),r.manager.itemEnd(t)}function l(){a.removeEventListener("load",o,!1),a.removeEventListener("error",h,!1)}return a.addEventListener("load",o,!1),a.addEventListener("error",h,!1),"data:"!==t.slice(0,5)&&void 0!==this.crossOrigin&&(a.crossOrigin=this.crossOrigin),r.manager.itemStart(t),a.src=t,a}constructor(t){super(t)}}class oY extends oJ{load(t,e,i,s){let r=new iT,n=new oZ(this.manager);return n.setCrossOrigin(this.crossOrigin),n.setPath(this.path),n.load(t,function(t){r.image=t,r.needsUpdate=!0,void 0!==e&&e(r)},i,s),r}constructor(t){super(t)}}class oQ extends sT{dispose(){}copy(t,e){return super.copy(t,e),this.color.copy(t.color),this.intensity=t.intensity,this}toJSON(t){let e=super.toJSON(t);return e.object.color=this.color.getHex(),e.object.intensity=this.intensity,void 0!==this.groundColor&&(e.object.groundColor=this.groundColor.getHex()),void 0!==this.distance&&(e.object.distance=this.distance),void 0!==this.angle&&(e.object.angle=this.angle),void 0!==this.decay&&(e.object.decay=this.decay),void 0!==this.penumbra&&(e.object.penumbra=this.penumbra),void 0!==this.shadow&&(e.object.shadow=this.shadow.toJSON()),void 0!==this.target&&(e.object.target=this.target.uuid),e}constructor(t,e=1){super(),this.isLight=!0,this.type="Light",this.color=new sq(t),this.intensity=e}}class oK extends oQ{copy(t,e){return super.copy(t,e),this.groundColor.copy(t.groundColor),this}constructor(t,e,i){super(t,i),this.isHemisphereLight=!0,this.type="HemisphereLight",this.position.copy(sT.DEFAULT_UP),this.updateMatrix(),this.groundColor=new sq(e)}}let o$=new st,o0=new iR,o1=new iR;class o2{getViewportCount(){return this._viewportCount}getFrustum(){return this._frustum}updateMatrices(t){let e=this.camera,i=this.matrix;o0.setFromMatrixPosition(t.matrixWorld),e.position.copy(o0),o1.setFromMatrixPosition(t.target.matrixWorld),e.lookAt(o1),e.updateMatrixWorld(),o$.multiplyMatrices(e.projectionMatrix,e.matrixWorldInverse),this._frustum.setFromProjectionMatrix(o$),i.set(.5,0,0,.5,0,.5,0,.5,0,0,.5,.5,0,0,0,1),i.multiply(o$)}getViewport(t){return this._viewports[t]}getFrameExtents(){return this._frameExtents}dispose(){this.map&&this.map.dispose(),this.mapPass&&this.mapPass.dispose()}copy(t){return this.camera=t.camera.clone(),this.intensity=t.intensity,this.bias=t.bias,this.radius=t.radius,this.autoUpdate=t.autoUpdate,this.needsUpdate=t.needsUpdate,this.normalBias=t.normalBias,this.blurSamples=t.blurSamples,this.mapSize.copy(t.mapSize),this}clone(){return new this.constructor().copy(this)}toJSON(){let t={};return 1!==this.intensity&&(t.intensity=this.intensity),0!==this.bias&&(t.bias=this.bias),0!==this.normalBias&&(t.normalBias=this.normalBias),1!==this.radius&&(t.radius=this.radius),(512!==this.mapSize.x||512!==this.mapSize.y)&&(t.mapSize=this.mapSize.toArray()),t.camera=this.camera.toJSON(!1).object,delete t.camera.matrix,t}constructor(t){this.camera=t,this.intensity=1,this.bias=0,this.normalBias=0,this.radius=1,this.blurSamples=8,this.mapSize=new ii(512,512),this.mapType=tz,this.map=null,this.mapPass=null,this.matrix=new st,this.autoUpdate=!0,this.needsUpdate=!1,this._frustum=new nz,this._frameExtents=new ii(1,1),this._viewportCount=1,this._viewports=[new iI(0,0,1,1)]}}class o3 extends o2{updateMatrices(t){let e=this.camera,i=2*e5*t.angle*this.focus,s=this.mapSize.width/this.mapSize.height,r=t.distance||e.far;(i!==e.fov||s!==e.aspect||r!==e.far)&&(e.fov=i,e.aspect=s,e.far=r,e.updateProjectionMatrix()),super.updateMatrices(t)}copy(t){return super.copy(t),this.focus=t.focus,this}constructor(){super(new rC(50,1,.5,500)),this.isSpotLightShadow=!0,this.focus=1}}class o5 extends oQ{get power(){return this.intensity*Math.PI}set power(t){this.intensity=t/Math.PI}dispose(){this.shadow.dispose()}copy(t,e){return super.copy(t,e),this.distance=t.distance,this.angle=t.angle,this.penumbra=t.penumbra,this.decay=t.decay,this.target=t.target.clone(),this.shadow=t.shadow.clone(),this}constructor(t,e,i=0,s=Math.PI/3,r=0,n=2){super(t,e),this.isSpotLight=!0,this.type="SpotLight",this.position.copy(sT.DEFAULT_UP),this.updateMatrix(),this.target=new sT,this.distance=i,this.angle=s,this.penumbra=r,this.decay=n,this.map=null,this.shadow=new o3}}let o4=new st,o6=new iR,o8=new iR;class o9 extends o2{updateMatrices(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=this.camera,s=this.matrix,r=t.distance||i.far;r!==i.far&&(i.far=r,i.updateProjectionMatrix()),o6.setFromMatrixPosition(t.matrixWorld),i.position.copy(o6),o8.copy(i.position),o8.add(this._cubeDirections[e]),i.up.copy(this._cubeUps[e]),i.lookAt(o8),i.updateMatrixWorld(),s.makeTranslation(-o6.x,-o6.y,-o6.z),o4.multiplyMatrices(i.projectionMatrix,i.matrixWorldInverse),this._frustum.setFromProjectionMatrix(o4)}constructor(){super(new rC(90,1,.5,500)),this.isPointLightShadow=!0,this._frameExtents=new ii(4,2),this._viewportCount=6,this._viewports=[new iI(2,1,1,1),new iI(0,1,1,1),new iI(3,1,1,1),new iI(1,1,1,1),new iI(3,0,1,1),new iI(1,0,1,1)],this._cubeDirections=[new iR(1,0,0),new iR(-1,0,0),new iR(0,0,1),new iR(0,0,-1),new iR(0,1,0),new iR(0,-1,0)],this._cubeUps=[new iR(0,1,0),new iR(0,1,0),new iR(0,1,0),new iR(0,1,0),new iR(0,0,1),new iR(0,0,-1)]}}class o7 extends oQ{get power(){return 4*this.intensity*Math.PI}set power(t){this.intensity=t/(4*Math.PI)}dispose(){this.shadow.dispose()}copy(t,e){return super.copy(t,e),this.distance=t.distance,this.decay=t.decay,this.shadow=t.shadow.clone(),this}constructor(t,e,i=0,s=2){super(t,e),this.isPointLight=!0,this.type="PointLight",this.distance=i,this.decay=s,this.shadow=new o9}}class ht extends rA{copy(t,e){return super.copy(t,e),this.left=t.left,this.right=t.right,this.top=t.top,this.bottom=t.bottom,this.near=t.near,this.far=t.far,this.zoom=t.zoom,this.view=null===t.view?null:Object.assign({},t.view),this}setViewOffset(t,e,i,s,r,n){null===this.view&&(this.view={enabled:!0,fullWidth:1,fullHeight:1,offsetX:0,offsetY:0,width:1,height:1}),this.view.enabled=!0,this.view.fullWidth=t,this.view.fullHeight=e,this.view.offsetX=i,this.view.offsetY=s,this.view.width=r,this.view.height=n,this.updateProjectionMatrix()}clearViewOffset(){null!==this.view&&(this.view.enabled=!1),this.updateProjectionMatrix()}updateProjectionMatrix(){let t=(this.right-this.left)/(2*this.zoom),e=(this.top-this.bottom)/(2*this.zoom),i=(this.right+this.left)/2,s=(this.top+this.bottom)/2,r=i-t,n=i+t,a=s+e,o=s-e;if(null!==this.view&&this.view.enabled){let t=(this.right-this.left)/this.view.fullWidth/this.zoom,e=(this.top-this.bottom)/this.view.fullHeight/this.zoom;r+=t*this.view.offsetX,n=r+t*this.view.width,a-=e*this.view.offsetY,o=a-e*this.view.height}this.projectionMatrix.makeOrthographic(r,n,a,o,this.near,this.far,this.coordinateSystem),this.projectionMatrixInverse.copy(this.projectionMatrix).invert()}toJSON(t){let e=super.toJSON(t);return e.object.zoom=this.zoom,e.object.left=this.left,e.object.right=this.right,e.object.top=this.top,e.object.bottom=this.bottom,e.object.near=this.near,e.object.far=this.far,null!==this.view&&(e.object.view=Object.assign({},this.view)),e}constructor(t=-1,e=1,i=1,s=-1,r=.1,n=2e3){super(),this.isOrthographicCamera=!0,this.type="OrthographicCamera",this.zoom=1,this.view=null,this.left=t,this.right=e,this.top=i,this.bottom=s,this.near=r,this.far=n,this.updateProjectionMatrix()}}class he extends o2{constructor(){super(new ht(-5,5,5,-5,.5,500)),this.isDirectionalLightShadow=!0}}class hi extends oQ{dispose(){this.shadow.dispose()}copy(t){return super.copy(t),this.target=t.target.clone(),this.shadow=t.shadow.clone(),this}constructor(t,e){super(t,e),this.isDirectionalLight=!0,this.type="DirectionalLight",this.position.copy(sT.DEFAULT_UP),this.updateMatrix(),this.target=new sT,this.shadow=new he}}class hs extends oQ{constructor(t,e){super(t,e),this.isAmbientLight=!0,this.type="AmbientLight"}}class hr extends oQ{get power(){return this.intensity*this.width*this.height*Math.PI}set power(t){this.intensity=t/(this.width*this.height*Math.PI)}copy(t){return super.copy(t),this.width=t.width,this.height=t.height,this}toJSON(t){let e=super.toJSON(t);return e.object.width=this.width,e.object.height=this.height,e}constructor(t,e,i=10,s=10){super(t,e),this.isRectAreaLight=!0,this.type="RectAreaLight",this.width=i,this.height=s}}class hn{set(t){for(let e=0;e<9;e++)this.coefficients[e].copy(t[e]);return this}zero(){for(let t=0;t<9;t++)this.coefficients[t].set(0,0,0);return this}getAt(t,e){let i=t.x,s=t.y,r=t.z,n=this.coefficients;return e.copy(n[0]).multiplyScalar(.282095),e.addScaledVector(n[1],.488603*s),e.addScaledVector(n[2],.488603*r),e.addScaledVector(n[3],.488603*i),e.addScaledVector(n[4],i*s*1.092548),e.addScaledVector(n[5],s*r*1.092548),e.addScaledVector(n[6],.315392*(3*r*r-1)),e.addScaledVector(n[7],i*r*1.092548),e.addScaledVector(n[8],.546274*(i*i-s*s)),e}getIrradianceAt(t,e){let i=t.x,s=t.y,r=t.z,n=this.coefficients;return e.copy(n[0]).multiplyScalar(.886227),e.addScaledVector(n[1],1.023328*s),e.addScaledVector(n[2],1.023328*r),e.addScaledVector(n[3],1.023328*i),e.addScaledVector(n[4],.858086*i*s),e.addScaledVector(n[5],.858086*s*r),e.addScaledVector(n[6],.743125*r*r-.247708),e.addScaledVector(n[7],.858086*i*r),e.addScaledVector(n[8],.429043*(i*i-s*s)),e}add(t){for(let e=0;e<9;e++)this.coefficients[e].add(t.coefficients[e]);return this}addScaledSH(t,e){for(let i=0;i<9;i++)this.coefficients[i].addScaledVector(t.coefficients[i],e);return this}scale(t){for(let e=0;e<9;e++)this.coefficients[e].multiplyScalar(t);return this}lerp(t,e){for(let i=0;i<9;i++)this.coefficients[i].lerp(t.coefficients[i],e);return this}equals(t){for(let e=0;e<9;e++)if(!this.coefficients[e].equals(t.coefficients[e]))return!1;return!0}copy(t){return this.set(t.coefficients)}clone(){return new this.constructor().copy(this)}fromArray(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=this.coefficients;for(let s=0;s<9;s++)i[s].fromArray(t,e+3*s);return this}toArray(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=this.coefficients;for(let s=0;s<9;s++)i[s].toArray(t,e+3*s);return t}static getBasisAt(t,e){let i=t.x,s=t.y,r=t.z;e[0]=.282095,e[1]=.488603*s,e[2]=.488603*r,e[3]=.488603*i,e[4]=1.092548*i*s,e[5]=1.092548*s*r,e[6]=.315392*(3*r*r-1),e[7]=1.092548*i*r,e[8]=.546274*(i*i-s*s)}constructor(){this.isSphericalHarmonics3=!0,this.coefficients=[];for(let t=0;t<9;t++)this.coefficients.push(new iR)}}class ha extends oQ{copy(t){return super.copy(t),this.sh.copy(t.sh),this}fromJSON(t){return this.intensity=t.intensity,this.sh.fromArray(t.sh),this}toJSON(t){let e=super.toJSON(t);return e.object.sh=this.sh.toArray(),e}constructor(t=new hn,e=1){super(void 0,e),this.isLightProbe=!0,this.sh=t}}class ho extends oJ{load(t,e,i,s){let r=this,n=new oX(r.manager);n.setPath(r.path),n.setRequestHeader(r.requestHeader),n.setWithCredentials(r.withCredentials),n.load(t,function(i){try{e(r.parse(JSON.parse(i)))}catch(e){s?s(e):console.error(e),r.manager.itemError(t)}},i,s)}parse(t){let e=this.textures;function i(t){return void 0===e[t]&&console.warn("THREE.MaterialLoader: Undefined texture",t),e[t]}let s=this.createMaterialFromType(t.type);if(void 0!==t.uuid&&(s.uuid=t.uuid),void 0!==t.name&&(s.name=t.name),void 0!==t.color&&void 0!==s.color&&s.color.setHex(t.color),void 0!==t.roughness&&(s.roughness=t.roughness),void 0!==t.metalness&&(s.metalness=t.metalness),void 0!==t.sheen&&(s.sheen=t.sheen),void 0!==t.sheenColor&&(s.sheenColor=new sq().setHex(t.sheenColor)),void 0!==t.sheenRoughness&&(s.sheenRoughness=t.sheenRoughness),void 0!==t.emissive&&void 0!==s.emissive&&s.emissive.setHex(t.emissive),void 0!==t.specular&&void 0!==s.specular&&s.specular.setHex(t.specular),void 0!==t.specularIntensity&&(s.specularIntensity=t.specularIntensity),void 0!==t.specularColor&&void 0!==s.specularColor&&s.specularColor.setHex(t.specularColor),void 0!==t.shininess&&(s.shininess=t.shininess),void 0!==t.clearcoat&&(s.clearcoat=t.clearcoat),void 0!==t.clearcoatRoughness&&(s.clearcoatRoughness=t.clearcoatRoughness),void 0!==t.dispersion&&(s.dispersion=t.dispersion),void 0!==t.iridescence&&(s.iridescence=t.iridescence),void 0!==t.iridescenceIOR&&(s.iridescenceIOR=t.iridescenceIOR),void 0!==t.iridescenceThicknessRange&&(s.iridescenceThicknessRange=t.iridescenceThicknessRange),void 0!==t.transmission&&(s.transmission=t.transmission),void 0!==t.thickness&&(s.thickness=t.thickness),void 0!==t.attenuationDistance&&(s.attenuationDistance=t.attenuationDistance),void 0!==t.attenuationColor&&void 0!==s.attenuationColor&&s.attenuationColor.setHex(t.attenuationColor),void 0!==t.anisotropy&&(s.anisotropy=t.anisotropy),void 0!==t.anisotropyRotation&&(s.anisotropyRotation=t.anisotropyRotation),void 0!==t.fog&&(s.fog=t.fog),void 0!==t.flatShading&&(s.flatShading=t.flatShading),void 0!==t.blending&&(s.blending=t.blending),void 0!==t.combine&&(s.combine=t.combine),void 0!==t.side&&(s.side=t.side),void 0!==t.shadowSide&&(s.shadowSide=t.shadowSide),void 0!==t.opacity&&(s.opacity=t.opacity),void 0!==t.transparent&&(s.transparent=t.transparent),void 0!==t.alphaTest&&(s.alphaTest=t.alphaTest),void 0!==t.alphaHash&&(s.alphaHash=t.alphaHash),void 0!==t.depthFunc&&(s.depthFunc=t.depthFunc),void 0!==t.depthTest&&(s.depthTest=t.depthTest),void 0!==t.depthWrite&&(s.depthWrite=t.depthWrite),void 0!==t.colorWrite&&(s.colorWrite=t.colorWrite),void 0!==t.blendSrc&&(s.blendSrc=t.blendSrc),void 0!==t.blendDst&&(s.blendDst=t.blendDst),void 0!==t.blendEquation&&(s.blendEquation=t.blendEquation),void 0!==t.blendSrcAlpha&&(s.blendSrcAlpha=t.blendSrcAlpha),void 0!==t.blendDstAlpha&&(s.blendDstAlpha=t.blendDstAlpha),void 0!==t.blendEquationAlpha&&(s.blendEquationAlpha=t.blendEquationAlpha),void 0!==t.blendColor&&void 0!==s.blendColor&&s.blendColor.setHex(t.blendColor),void 0!==t.blendAlpha&&(s.blendAlpha=t.blendAlpha),void 0!==t.stencilWriteMask&&(s.stencilWriteMask=t.stencilWriteMask),void 0!==t.stencilFunc&&(s.stencilFunc=t.stencilFunc),void 0!==t.stencilRef&&(s.stencilRef=t.stencilRef),void 0!==t.stencilFuncMask&&(s.stencilFuncMask=t.stencilFuncMask),void 0!==t.stencilFail&&(s.stencilFail=t.stencilFail),void 0!==t.stencilZFail&&(s.stencilZFail=t.stencilZFail),void 0!==t.stencilZPass&&(s.stencilZPass=t.stencilZPass),void 0!==t.stencilWrite&&(s.stencilWrite=t.stencilWrite),void 0!==t.wireframe&&(s.wireframe=t.wireframe),void 0!==t.wireframeLinewidth&&(s.wireframeLinewidth=t.wireframeLinewidth),void 0!==t.wireframeLinecap&&(s.wireframeLinecap=t.wireframeLinecap),void 0!==t.wireframeLinejoin&&(s.wireframeLinejoin=t.wireframeLinejoin),void 0!==t.rotation&&(s.rotation=t.rotation),void 0!==t.linewidth&&(s.linewidth=t.linewidth),void 0!==t.dashSize&&(s.dashSize=t.dashSize),void 0!==t.gapSize&&(s.gapSize=t.gapSize),void 0!==t.scale&&(s.scale=t.scale),void 0!==t.polygonOffset&&(s.polygonOffset=t.polygonOffset),void 0!==t.polygonOffsetFactor&&(s.polygonOffsetFactor=t.polygonOffsetFactor),void 0!==t.polygonOffsetUnits&&(s.polygonOffsetUnits=t.polygonOffsetUnits),void 0!==t.dithering&&(s.dithering=t.dithering),void 0!==t.alphaToCoverage&&(s.alphaToCoverage=t.alphaToCoverage),void 0!==t.premultipliedAlpha&&(s.premultipliedAlpha=t.premultipliedAlpha),void 0!==t.forceSinglePass&&(s.forceSinglePass=t.forceSinglePass),void 0!==t.visible&&(s.visible=t.visible),void 0!==t.toneMapped&&(s.toneMapped=t.toneMapped),void 0!==t.userData&&(s.userData=t.userData),void 0!==t.vertexColors&&("number"==typeof t.vertexColors?s.vertexColors=t.vertexColors>0:s.vertexColors=t.vertexColors),void 0!==t.uniforms)for(let e in t.uniforms){let r=t.uniforms[e];switch(s.uniforms[e]={},r.type){case"t":s.uniforms[e].value=i(r.value);break;case"c":s.uniforms[e].value=new sq().setHex(r.value);break;case"v2":s.uniforms[e].value=new ii().fromArray(r.value);break;case"v3":s.uniforms[e].value=new iR().fromArray(r.value);break;case"v4":s.uniforms[e].value=new iI().fromArray(r.value);break;case"m3":s.uniforms[e].value=new is().fromArray(r.value);break;case"m4":s.uniforms[e].value=new st().fromArray(r.value);break;default:s.uniforms[e].value=r.value}}if(void 0!==t.defines&&(s.defines=t.defines),void 0!==t.vertexShader&&(s.vertexShader=t.vertexShader),void 0!==t.fragmentShader&&(s.fragmentShader=t.fragmentShader),void 0!==t.glslVersion&&(s.glslVersion=t.glslVersion),void 0!==t.extensions)for(let e in t.extensions)s.extensions[e]=t.extensions[e];if(void 0!==t.lights&&(s.lights=t.lights),void 0!==t.clipping&&(s.clipping=t.clipping),void 0!==t.size&&(s.size=t.size),void 0!==t.sizeAttenuation&&(s.sizeAttenuation=t.sizeAttenuation),void 0!==t.map&&(s.map=i(t.map)),void 0!==t.matcap&&(s.matcap=i(t.matcap)),void 0!==t.alphaMap&&(s.alphaMap=i(t.alphaMap)),void 0!==t.bumpMap&&(s.bumpMap=i(t.bumpMap)),void 0!==t.bumpScale&&(s.bumpScale=t.bumpScale),void 0!==t.normalMap&&(s.normalMap=i(t.normalMap)),void 0!==t.normalMapType&&(s.normalMapType=t.normalMapType),void 0!==t.normalScale){let e=t.normalScale;!1===Array.isArray(e)&&(e=[e,e]),s.normalScale=new ii().fromArray(e)}return void 0!==t.displacementMap&&(s.displacementMap=i(t.displacementMap)),void 0!==t.displacementScale&&(s.displacementScale=t.displacementScale),void 0!==t.displacementBias&&(s.displacementBias=t.displacementBias),void 0!==t.roughnessMap&&(s.roughnessMap=i(t.roughnessMap)),void 0!==t.metalnessMap&&(s.metalnessMap=i(t.metalnessMap)),void 0!==t.emissiveMap&&(s.emissiveMap=i(t.emissiveMap)),void 0!==t.emissiveIntensity&&(s.emissiveIntensity=t.emissiveIntensity),void 0!==t.specularMap&&(s.specularMap=i(t.specularMap)),void 0!==t.specularIntensityMap&&(s.specularIntensityMap=i(t.specularIntensityMap)),void 0!==t.specularColorMap&&(s.specularColorMap=i(t.specularColorMap)),void 0!==t.envMap&&(s.envMap=i(t.envMap)),void 0!==t.envMapRotation&&s.envMapRotation.fromArray(t.envMapRotation),void 0!==t.envMapIntensity&&(s.envMapIntensity=t.envMapIntensity),void 0!==t.reflectivity&&(s.reflectivity=t.reflectivity),void 0!==t.refractionRatio&&(s.refractionRatio=t.refractionRatio),void 0!==t.lightMap&&(s.lightMap=i(t.lightMap)),void 0!==t.lightMapIntensity&&(s.lightMapIntensity=t.lightMapIntensity),void 0!==t.aoMap&&(s.aoMap=i(t.aoMap)),void 0!==t.aoMapIntensity&&(s.aoMapIntensity=t.aoMapIntensity),void 0!==t.gradientMap&&(s.gradientMap=i(t.gradientMap)),void 0!==t.clearcoatMap&&(s.clearcoatMap=i(t.clearcoatMap)),void 0!==t.clearcoatRoughnessMap&&(s.clearcoatRoughnessMap=i(t.clearcoatRoughnessMap)),void 0!==t.clearcoatNormalMap&&(s.clearcoatNormalMap=i(t.clearcoatNormalMap)),void 0!==t.clearcoatNormalScale&&(s.clearcoatNormalScale=new ii().fromArray(t.clearcoatNormalScale)),void 0!==t.iridescenceMap&&(s.iridescenceMap=i(t.iridescenceMap)),void 0!==t.iridescenceThicknessMap&&(s.iridescenceThicknessMap=i(t.iridescenceThicknessMap)),void 0!==t.transmissionMap&&(s.transmissionMap=i(t.transmissionMap)),void 0!==t.thicknessMap&&(s.thicknessMap=i(t.thicknessMap)),void 0!==t.anisotropyMap&&(s.anisotropyMap=i(t.anisotropyMap)),void 0!==t.sheenColorMap&&(s.sheenColorMap=i(t.sheenColorMap)),void 0!==t.sheenRoughnessMap&&(s.sheenRoughnessMap=i(t.sheenRoughnessMap)),s}setTextures(t){return this.textures=t,this}createMaterialFromType(t){return ho.createMaterialFromType(t)}static createMaterialFromType(t){return new({ShadowMaterial:om,SpriteMaterial:rD,RawShaderMaterial:oy,ShaderMaterial:r_,PointsMaterial:n9,MeshPhysicalMaterial:og,MeshStandardMaterial:of,MeshPhongMaterial:ox,MeshToonMaterial:ov,MeshNormalMaterial:ob,MeshLambertMaterial:oM,MeshDepthMaterial:ow,MeshDistanceMaterial:oS,MeshBasicMaterial:sY,MeshMatcapMaterial:o_,LineDashedMaterial:oA,LineBasicMaterial:nX,Material:sZ})[t]}constructor(t){super(t),this.textures={}}}class hh{static extractUrlBase(t){let e=t.lastIndexOf("/");return -1===e?"./":t.slice(0,e+1)}static resolveURL(t,e){return"string"!=typeof t||""===t?"":(/^https?:\/\//i.test(e)&&/^\//.test(t)&&(e=e.replace(/(^https?:\/\/[^\/]+).*/i,"$1")),/^(https?:)?\/\//i.test(t)||/^data:.*,.*$/i.test(t)||/^blob:.*$/i.test(t))?t:e+t}}class hl extends rn{copy(t){return super.copy(t),this.instanceCount=t.instanceCount,this}toJSON(){let t=super.toJSON();return t.instanceCount=this.instanceCount,t.isInstancedBufferGeometry=!0,t}constructor(){super(),this.isInstancedBufferGeometry=!0,this.type="InstancedBufferGeometry",this.instanceCount=1/0}}class hu extends oJ{load(t,e,i,s){let r=this,n=new oX(r.manager);n.setPath(r.path),n.setRequestHeader(r.requestHeader),n.setWithCredentials(r.withCredentials),n.load(t,function(i){try{e(r.parse(JSON.parse(i)))}catch(e){s?s(e):console.error(e),r.manager.itemError(t)}},i,s)}parse(t){let e={},i={};function s(t,s){if(void 0!==e[s])return e[s];let r=t.interleavedBuffers[s],n=function(t,e){if(void 0!==i[e])return i[e];let s=new Uint32Array(t.arrayBuffers[e]).buffer;return i[e]=s,s}(t,r.buffer),a=new rF(ih(r.type,n),r.stride);return a.uuid=r.uuid,e[s]=a,a}let r=t.isInstancedBufferGeometry?new hl:new rn,n=t.data.index;if(void 0!==n){let t=ih(n.type,n.array);r.setIndex(new s3(t,1))}let a=t.data.attributes;for(let e in a){let i,n=a[e];if(n.isInterleavedBufferAttribute)i=new rU(s(t.data,n.data),n.itemSize,n.offset,n.normalized);else{let t=ih(n.type,n.array);i=new(n.isInstancedBufferAttribute?nc:s3)(t,n.itemSize,n.normalized)}void 0!==n.name&&(i.name=n.name),void 0!==n.usage&&i.setUsage(n.usage),r.setAttribute(e,i)}let o=t.data.morphAttributes;if(o)for(let e in o){let i=o[e],n=[];for(let e=0,r=i.length;e<r;e++){let r,a=i[e];r=a.isInterleavedBufferAttribute?new rU(s(t.data,a.data),a.itemSize,a.offset,a.normalized):new s3(ih(a.type,a.array),a.itemSize,a.normalized),void 0!==a.name&&(r.name=a.name),n.push(r)}r.morphAttributes[e]=n}t.data.morphTargetsRelative&&(r.morphTargetsRelative=!0);let h=t.data.groups||t.data.drawcalls||t.data.offsets;if(void 0!==h)for(let t=0,e=h.length;t!==e;++t){let e=h[t];r.addGroup(e.start,e.count,e.materialIndex)}let l=t.data.boundingSphere;if(void 0!==l){let t=new iR;void 0!==l.center&&t.fromArray(l.center),r.boundingSphere=new i1(t,l.radius)}return t.name&&(r.name=t.name),t.userData&&(r.userData=t.userData),r}constructor(t){super(t)}}class hc extends oJ{load(t,e,i,s){let r=this,n=""===this.path?hh.extractUrlBase(t):this.path;this.resourcePath=this.resourcePath||n;let a=new oX(this.manager);a.setPath(this.path),a.setRequestHeader(this.requestHeader),a.setWithCredentials(this.withCredentials),a.load(t,function(i){let n=null;try{n=JSON.parse(i)}catch(e){void 0!==s&&s(e),console.error("THREE:ObjectLoader: Can't parse "+t+".",e.message);return}let a=n.metadata;if(void 0===a||void 0===a.type||"geometry"===a.type.toLowerCase()){void 0!==s&&s(Error("THREE.ObjectLoader: Can't load "+t)),console.error("THREE.ObjectLoader: Can't load "+t);return}r.parse(n,e)},i,s)}async loadAsync(t,e){let i=""===this.path?hh.extractUrlBase(t):this.path;this.resourcePath=this.resourcePath||i;let s=new oX(this.manager);s.setPath(this.path),s.setRequestHeader(this.requestHeader),s.setWithCredentials(this.withCredentials);let r=JSON.parse(await s.loadAsync(t,e)),n=r.metadata;if(void 0===n||void 0===n.type||"geometry"===n.type.toLowerCase())throw Error("THREE.ObjectLoader: Can't load "+t);return await this.parseAsync(r)}parse(t,e){let i=this.parseAnimations(t.animations),s=this.parseShapes(t.shapes),r=this.parseGeometries(t.geometries,s),n=this.parseImages(t.images,function(){void 0!==e&&e(h)}),a=this.parseTextures(t.textures,n),o=this.parseMaterials(t.materials,a),h=this.parseObject(t.object,r,o,a,i),l=this.parseSkeletons(t.skeletons,h);if(this.bindSkeletons(h,l),this.bindLightTargets(h),void 0!==e){let t=!1;for(let e in n)if(n[e].data instanceof HTMLImageElement){t=!0;break}!1===t&&e(h)}return h}async parseAsync(t){let e=this.parseAnimations(t.animations),i=this.parseShapes(t.shapes),s=this.parseGeometries(t.geometries,i),r=await this.parseImagesAsync(t.images),n=this.parseTextures(t.textures,r),a=this.parseMaterials(t.materials,n),o=this.parseObject(t.object,s,a,n,e),h=this.parseSkeletons(t.skeletons,o);return this.bindSkeletons(o,h),this.bindLightTargets(o),o}parseShapes(t){let e={};if(void 0!==t)for(let i=0,s=t.length;i<s;i++){let s=new aU().fromJSON(t[i]);e[s.uuid]=s}return e}parseSkeletons(t,e){let i={},s={};if(e.traverse(function(t){t.isBone&&(s[t.uuid]=t)}),void 0!==t)for(let e=0,r=t.length;e<r;e++){let r=new nu().fromJSON(t[e],s);i[r.uuid]=r}return i}parseGeometries(t,e){let i={};if(void 0!==t){let s=new hu;for(let r=0,n=t.length;r<n;r++){let n,a=t[r];switch(a.type){case"BufferGeometry":case"InstancedBufferGeometry":n=s.parse(a);break;default:a.type in op?n=op[a.type].fromJSON(a,e):console.warn('THREE.ObjectLoader: Unsupported geometry type "'.concat(a.type,'"'))}n.uuid=a.uuid,void 0!==a.name&&(n.name=a.name),void 0!==a.userData&&(n.userData=a.userData),i[a.uuid]=n}}return i}parseMaterials(t,e){let i={},s={};if(void 0!==t){let r=new ho;r.setTextures(e);for(let e=0,n=t.length;e<n;e++){let n=t[e];void 0===i[n.uuid]&&(i[n.uuid]=r.parse(n)),s[n.uuid]=i[n.uuid]}}return s}parseAnimations(t){let e={};if(void 0!==t)for(let i=0;i<t.length;i++){let s=t[i],r=oU.parse(s);e[r.uuid]=r}return e}parseImages(t,e){let i,s=this,r={};function n(t){if("string"==typeof t){var e;return e=/^(\/\/)|([a-z]+:(\/\/)?)/i.test(t)?t:s.resourcePath+t,s.manager.itemStart(e),i.load(e,function(){s.manager.itemEnd(e)},void 0,function(){s.manager.itemError(e),s.manager.itemEnd(e)})}return t.data?{data:ih(t.type,t.data),width:t.width,height:t.height}:null}if(void 0!==t&&t.length>0){(i=new oZ(new oW(e))).setCrossOrigin(this.crossOrigin);for(let e=0,i=t.length;e<i;e++){let i=t[e],s=i.url;if(Array.isArray(s)){let t=[];for(let e=0,i=s.length;e<i;e++){let i=n(s[e]);null!==i&&(i instanceof HTMLImageElement?t.push(i):t.push(new no(i.data,i.width,i.height)))}r[i.uuid]=new i_(t)}else{let t=n(i.url);r[i.uuid]=new i_(t)}}}return r}async parseImagesAsync(t){let e,i=this,s={};async function r(t){if("string"==typeof t){let s=/^(\/\/)|([a-z]+:(\/\/)?)/i.test(t)?t:i.resourcePath+t;return await e.loadAsync(s)}return t.data?{data:ih(t.type,t.data),width:t.width,height:t.height}:null}if(void 0!==t&&t.length>0){(e=new oZ(this.manager)).setCrossOrigin(this.crossOrigin);for(let e=0,i=t.length;e<i;e++){let i=t[e],n=i.url;if(Array.isArray(n)){let t=[];for(let e=0,i=n.length;e<i;e++){let i=n[e],s=await r(i);null!==s&&(s instanceof HTMLImageElement?t.push(s):t.push(new no(s.data,s.width,s.height)))}s[i.uuid]=new i_(t)}else{let t=await r(i.url);s[i.uuid]=new i_(t)}}}return s}parseTextures(t,e){function i(t,e){return"number"==typeof t?t:(console.warn("THREE.ObjectLoader.parseTexture: Constant should be in numeric form.",t),e[t])}let s={};if(void 0!==t)for(let r=0,n=t.length;r<n;r++){let n,a=t[r];void 0===a.image&&console.warn('THREE.ObjectLoader: No "image" specified for',a.uuid),void 0===e[a.image]&&console.warn("THREE.ObjectLoader: Undefined image",a.image);let o=e[a.image],h=o.data;Array.isArray(h)?(n=new rk,6===h.length&&(n.needsUpdate=!0)):(n=h&&h.data?new no:new iT,h&&(n.needsUpdate=!0)),n.source=o,n.uuid=a.uuid,void 0!==a.name&&(n.name=a.name),void 0!==a.mapping&&(n.mapping=i(a.mapping,hd)),void 0!==a.channel&&(n.channel=a.channel),void 0!==a.offset&&n.offset.fromArray(a.offset),void 0!==a.repeat&&n.repeat.fromArray(a.repeat),void 0!==a.center&&n.center.fromArray(a.center),void 0!==a.rotation&&(n.rotation=a.rotation),void 0!==a.wrap&&(n.wrapS=i(a.wrap[0],hp),n.wrapT=i(a.wrap[1],hp)),void 0!==a.format&&(n.format=a.format),void 0!==a.internalFormat&&(n.internalFormat=a.internalFormat),void 0!==a.type&&(n.type=a.type),void 0!==a.colorSpace&&(n.colorSpace=a.colorSpace),void 0!==a.minFilter&&(n.minFilter=i(a.minFilter,hm)),void 0!==a.magFilter&&(n.magFilter=i(a.magFilter,hm)),void 0!==a.anisotropy&&(n.anisotropy=a.anisotropy),void 0!==a.flipY&&(n.flipY=a.flipY),void 0!==a.generateMipmaps&&(n.generateMipmaps=a.generateMipmaps),void 0!==a.premultiplyAlpha&&(n.premultiplyAlpha=a.premultiplyAlpha),void 0!==a.unpackAlignment&&(n.unpackAlignment=a.unpackAlignment),void 0!==a.compareFunction&&(n.compareFunction=a.compareFunction),void 0!==a.userData&&(n.userData=a.userData),s[a.uuid]=n}return s}parseObject(t,e,i,s,r){let n,a,o;function h(t){return void 0===e[t]&&console.warn("THREE.ObjectLoader: Undefined geometry",t),e[t]}function l(t){if(void 0!==t){if(Array.isArray(t)){let e=[];for(let s=0,r=t.length;s<r;s++){let r=t[s];void 0===i[r]&&console.warn("THREE.ObjectLoader: Undefined material",r),e.push(i[r])}return e}return void 0===i[t]&&console.warn("THREE.ObjectLoader: Undefined material",t),i[t]}}function u(t){return void 0===s[t]&&console.warn("THREE.ObjectLoader: Undefined texture",t),s[t]}switch(t.type){case"Scene":n=new rL,void 0!==t.background&&(Number.isInteger(t.background)?n.background=new sq(t.background):n.background=u(t.background)),void 0!==t.environment&&(n.environment=u(t.environment)),void 0!==t.fog&&("Fog"===t.fog.type?n.fog=new rV(t.fog.color,t.fog.near,t.fog.far):"FogExp2"===t.fog.type&&(n.fog=new rN(t.fog.color,t.fog.density)),""!==t.fog.name&&(n.fog.name=t.fog.name)),void 0!==t.backgroundBlurriness&&(n.backgroundBlurriness=t.backgroundBlurriness),void 0!==t.backgroundIntensity&&(n.backgroundIntensity=t.backgroundIntensity),void 0!==t.backgroundRotation&&n.backgroundRotation.fromArray(t.backgroundRotation),void 0!==t.environmentIntensity&&(n.environmentIntensity=t.environmentIntensity),void 0!==t.environmentRotation&&n.environmentRotation.fromArray(t.environmentRotation);break;case"PerspectiveCamera":n=new rC(t.fov,t.aspect,t.near,t.far),void 0!==t.focus&&(n.focus=t.focus),void 0!==t.zoom&&(n.zoom=t.zoom),void 0!==t.filmGauge&&(n.filmGauge=t.filmGauge),void 0!==t.filmOffset&&(n.filmOffset=t.filmOffset),void 0!==t.view&&(n.view=Object.assign({},t.view));break;case"OrthographicCamera":n=new ht(t.left,t.right,t.top,t.bottom,t.near,t.far),void 0!==t.zoom&&(n.zoom=t.zoom),void 0!==t.view&&(n.view=Object.assign({},t.view));break;case"AmbientLight":n=new hs(t.color,t.intensity);break;case"DirectionalLight":(n=new hi(t.color,t.intensity)).target=t.target||"";break;case"PointLight":n=new o7(t.color,t.intensity,t.distance,t.decay);break;case"RectAreaLight":n=new hr(t.color,t.intensity,t.width,t.height);break;case"SpotLight":(n=new o5(t.color,t.intensity,t.distance,t.angle,t.penumbra,t.decay)).target=t.target||"";break;case"HemisphereLight":n=new oK(t.color,t.groundColor,t.intensity);break;case"LightProbe":n=new ha().fromJSON(t);break;case"SkinnedMesh":n=new nn(a=h(t.geometry),o=l(t.material)),void 0!==t.bindMode&&(n.bindMode=t.bindMode),void 0!==t.bindMatrix&&n.bindMatrix.fromArray(t.bindMatrix),void 0!==t.skeleton&&(n.skeleton=t.skeleton);break;case"Mesh":n=new rg(a=h(t.geometry),o=l(t.material));break;case"InstancedMesh":a=h(t.geometry),o=l(t.material);let c=t.count,d=t.instanceMatrix,p=t.instanceColor;(n=new nv(a,o,c)).instanceMatrix=new nc(new Float32Array(d.array),16),void 0!==p&&(n.instanceColor=new nc(new Float32Array(p.array),p.itemSize));break;case"BatchedMesh":a=h(t.geometry),o=l(t.material),(n=new nG(t.maxInstanceCount,t.maxVertexCount,t.maxIndexCount,o)).geometry=a,n.perObjectFrustumCulled=t.perObjectFrustumCulled,n.sortObjects=t.sortObjects,n._drawRanges=t.drawRanges,n._reservedRanges=t.reservedRanges,n._geometryInfo=t.geometryInfo.map(t=>{let e=null,i=null;return void 0!==t.boundingBox&&((e=new iV).min.fromArray(t.boundingBox.min),e.max.fromArray(t.boundingBox.max)),void 0!==t.boundingSphere&&((i=new i1).radius=t.boundingSphere.radius,i.center.fromArray(t.boundingSphere.center)),{...t,boundingBox:e,boundingSphere:i}}),n._instanceInfo=t.instanceInfo,n._availableInstanceIds=t._availableInstanceIds,n._availableGeometryIds=t._availableGeometryIds,n._nextIndexStart=t.nextIndexStart,n._nextVertexStart=t.nextVertexStart,n._geometryCount=t.geometryCount,n._maxInstanceCount=t.maxInstanceCount,n._maxVertexCount=t.maxVertexCount,n._maxIndexCount=t.maxIndexCount,n._geometryInitialized=t.geometryInitialized,n._matricesTexture=u(t.matricesTexture.uuid),n._indirectTexture=u(t.indirectTexture.uuid),void 0!==t.colorsTexture&&(n._colorsTexture=u(t.colorsTexture.uuid)),void 0!==t.boundingSphere&&(n.boundingSphere=new i1,n.boundingSphere.center.fromArray(t.boundingSphere.center),n.boundingSphere.radius=t.boundingSphere.radius),void 0!==t.boundingBox&&(n.boundingBox=new iV,n.boundingBox.min.fromArray(t.boundingBox.min),n.boundingBox.max.fromArray(t.boundingBox.max));break;case"LOD":n=new r4;break;case"Line":n=new n2(h(t.geometry),l(t.material));break;case"LineLoop":n=new n8(h(t.geometry),l(t.material));break;case"LineSegments":n=new n6(h(t.geometry),l(t.material));break;case"PointCloud":case"Points":n=new as(h(t.geometry),l(t.material));break;case"Sprite":n=new r1(l(t.material));break;case"Group":n=new rE;break;case"Bone":n=new na;break;default:n=new sT}if(n.uuid=t.uuid,void 0!==t.name&&(n.name=t.name),void 0!==t.matrix?(n.matrix.fromArray(t.matrix),void 0!==t.matrixAutoUpdate&&(n.matrixAutoUpdate=t.matrixAutoUpdate),n.matrixAutoUpdate&&n.matrix.decompose(n.position,n.quaternion,n.scale)):(void 0!==t.position&&n.position.fromArray(t.position),void 0!==t.rotation&&n.rotation.fromArray(t.rotation),void 0!==t.quaternion&&n.quaternion.fromArray(t.quaternion),void 0!==t.scale&&n.scale.fromArray(t.scale)),void 0!==t.up&&n.up.fromArray(t.up),void 0!==t.castShadow&&(n.castShadow=t.castShadow),void 0!==t.receiveShadow&&(n.receiveShadow=t.receiveShadow),t.shadow&&(void 0!==t.shadow.intensity&&(n.shadow.intensity=t.shadow.intensity),void 0!==t.shadow.bias&&(n.shadow.bias=t.shadow.bias),void 0!==t.shadow.normalBias&&(n.shadow.normalBias=t.shadow.normalBias),void 0!==t.shadow.radius&&(n.shadow.radius=t.shadow.radius),void 0!==t.shadow.mapSize&&n.shadow.mapSize.fromArray(t.shadow.mapSize),void 0!==t.shadow.camera&&(n.shadow.camera=this.parseObject(t.shadow.camera))),void 0!==t.visible&&(n.visible=t.visible),void 0!==t.frustumCulled&&(n.frustumCulled=t.frustumCulled),void 0!==t.renderOrder&&(n.renderOrder=t.renderOrder),void 0!==t.userData&&(n.userData=t.userData),void 0!==t.layers&&(n.layers.mask=t.layers),void 0!==t.children){let a=t.children;for(let t=0;t<a.length;t++)n.add(this.parseObject(a[t],e,i,s,r))}if(void 0!==t.animations){let e=t.animations;for(let t=0;t<e.length;t++){let i=e[t];n.animations.push(r[i])}}if("LOD"===t.type){void 0!==t.autoUpdate&&(n.autoUpdate=t.autoUpdate);let e=t.levels;for(let t=0;t<e.length;t++){let i=e[t],s=n.getObjectByProperty("uuid",i.object);void 0!==s&&n.addLevel(s,i.distance,i.hysteresis)}}return n}bindSkeletons(t,e){0!==Object.keys(e).length&&t.traverse(function(t){if(!0===t.isSkinnedMesh&&void 0!==t.skeleton){let i=e[t.skeleton];void 0===i?console.warn("THREE.ObjectLoader: No skeleton found with UUID:",t.skeleton):t.bind(i,t.bindMatrix)}})}bindLightTargets(t){t.traverse(function(e){if(e.isDirectionalLight||e.isSpotLight){let i=e.target,s=t.getObjectByProperty("uuid",i);void 0!==s?e.target=s:e.target=new sT}})}constructor(t){super(t)}}let hd={UVMapping:tu,CubeReflectionMapping:tc,CubeRefractionMapping:td,EquirectangularReflectionMapping:tp,EquirectangularRefractionMapping:tm,CubeUVReflectionMapping:ty},hp={RepeatWrapping:tf,ClampToEdgeWrapping:tg,MirroredRepeatWrapping:tx},hm={NearestFilter:tv,NearestMipmapNearestFilter:tb,NearestMipmapLinearFilter:tM,LinearFilter:tw,LinearMipmapNearestFilter:tS,LinearMipmapLinearFilter:t_};class hy extends rC{constructor(t=[]){super(),this.isArrayCamera=!0,this.isMultiViewCamera=!1,this.cameras=t}}class hf{start(){this.startTime=hg(),this.oldTime=this.startTime,this.elapsedTime=0,this.running=!0}stop(){this.getElapsedTime(),this.running=!1,this.autoStart=!1}getElapsedTime(){return this.getDelta(),this.elapsedTime}getDelta(){let t=0;if(this.autoStart&&!this.running)return this.start(),0;if(this.running){let e=hg();t=(e-this.oldTime)/1e3,this.oldTime=e,this.elapsedTime+=t}return t}constructor(t=!0){this.autoStart=t,this.startTime=0,this.oldTime=0,this.elapsedTime=0,this.running=!1}}function hg(){return performance.now()}let hx="\\[\\]\\.:\\/",hv=RegExp("["+hx+"]","g"),hb="[^"+hx+"]",hM="[^"+hx.replace("\\.","")+"]",hw=/((?:WC+[\/:])*)/.source.replace("WC",hb),hS=/(WCOD+)?/.source.replace("WCOD",hM),h_=RegExp("^"+hw+hS+/(?:\.(WC+)(?:\[(.+)\])?)?/.source.replace("WC",hb)+/\.(WC+)(?:\[(.+)\])?/.source.replace("WC",hb)+"$"),hA=["material","materials","bones","map"];class hz{getValue(t,e){this.bind();let i=this._targetGroup.nCachedObjects_,s=this._bindings[i];void 0!==s&&s.getValue(t,e)}setValue(t,e){let i=this._bindings;for(let s=this._targetGroup.nCachedObjects_,r=i.length;s!==r;++s)i[s].setValue(t,e)}bind(){let t=this._bindings;for(let e=this._targetGroup.nCachedObjects_,i=t.length;e!==i;++e)t[e].bind()}unbind(){let t=this._bindings;for(let e=this._targetGroup.nCachedObjects_,i=t.length;e!==i;++e)t[e].unbind()}constructor(t,e,i){let s=i||hT.parseTrackName(e);this._targetGroup=t,this._bindings=t.subscribe_(e,s)}}class hT{static create(t,e,i){return t&&t.isAnimationObjectGroup?new hT.Composite(t,e,i):new hT(t,e,i)}static sanitizeNodeName(t){return t.replace(/\s/g,"_").replace(hv,"")}static parseTrackName(t){let e=h_.exec(t);if(null===e)throw Error("PropertyBinding: Cannot parse trackName: "+t);let i={nodeName:e[2],objectName:e[3],objectIndex:e[4],propertyName:e[5],propertyIndex:e[6]},s=i.nodeName&&i.nodeName.lastIndexOf(".");if(void 0!==s&&-1!==s){let t=i.nodeName.substring(s+1);-1!==hA.indexOf(t)&&(i.nodeName=i.nodeName.substring(0,s),i.objectName=t)}if(null===i.propertyName||0===i.propertyName.length)throw Error("PropertyBinding: can not parse propertyName from trackName: "+t);return i}static findNode(t,e){if(void 0===e||""===e||"."===e||-1===e||e===t.name||e===t.uuid)return t;if(t.skeleton){let i=t.skeleton.getBoneByName(e);if(void 0!==i)return i}if(t.children){let i=function(t){for(let s=0;s<t.length;s++){let r=t[s];if(r.name===e||r.uuid===e)return r;let n=i(r.children);if(n)return n}return null},s=i(t.children);if(s)return s}return null}_getValue_unavailable(){}_setValue_unavailable(){}_getValue_direct(t,e){t[e]=this.targetObject[this.propertyName]}_getValue_array(t,e){let i=this.resolvedProperty;for(let s=0,r=i.length;s!==r;++s)t[e++]=i[s]}_getValue_arrayElement(t,e){t[e]=this.resolvedProperty[this.propertyIndex]}_getValue_toArray(t,e){this.resolvedProperty.toArray(t,e)}_setValue_direct(t,e){this.targetObject[this.propertyName]=t[e]}_setValue_direct_setNeedsUpdate(t,e){this.targetObject[this.propertyName]=t[e],this.targetObject.needsUpdate=!0}_setValue_direct_setMatrixWorldNeedsUpdate(t,e){this.targetObject[this.propertyName]=t[e],this.targetObject.matrixWorldNeedsUpdate=!0}_setValue_array(t,e){let i=this.resolvedProperty;for(let s=0,r=i.length;s!==r;++s)i[s]=t[e++]}_setValue_array_setNeedsUpdate(t,e){let i=this.resolvedProperty;for(let s=0,r=i.length;s!==r;++s)i[s]=t[e++];this.targetObject.needsUpdate=!0}_setValue_array_setMatrixWorldNeedsUpdate(t,e){let i=this.resolvedProperty;for(let s=0,r=i.length;s!==r;++s)i[s]=t[e++];this.targetObject.matrixWorldNeedsUpdate=!0}_setValue_arrayElement(t,e){this.resolvedProperty[this.propertyIndex]=t[e]}_setValue_arrayElement_setNeedsUpdate(t,e){this.resolvedProperty[this.propertyIndex]=t[e],this.targetObject.needsUpdate=!0}_setValue_arrayElement_setMatrixWorldNeedsUpdate(t,e){this.resolvedProperty[this.propertyIndex]=t[e],this.targetObject.matrixWorldNeedsUpdate=!0}_setValue_fromArray(t,e){this.resolvedProperty.fromArray(t,e)}_setValue_fromArray_setNeedsUpdate(t,e){this.resolvedProperty.fromArray(t,e),this.targetObject.needsUpdate=!0}_setValue_fromArray_setMatrixWorldNeedsUpdate(t,e){this.resolvedProperty.fromArray(t,e),this.targetObject.matrixWorldNeedsUpdate=!0}_getValue_unbound(t,e){this.bind(),this.getValue(t,e)}_setValue_unbound(t,e){this.bind(),this.setValue(t,e)}bind(){let t=this.node,e=this.parsedPath,i=e.objectName,s=e.propertyName,r=e.propertyIndex;if(t||(t=hT.findNode(this.rootNode,e.nodeName),this.node=t),this.getValue=this._getValue_unavailable,this.setValue=this._setValue_unavailable,!t)return void console.warn("THREE.PropertyBinding: No target node found for track: "+this.path+".");if(i){let s=e.objectIndex;switch(i){case"materials":if(!t.material)return void console.error("THREE.PropertyBinding: Can not bind to material as node does not have a material.",this);if(!t.material.materials)return void console.error("THREE.PropertyBinding: Can not bind to material.materials as node.material does not have a materials array.",this);t=t.material.materials;break;case"bones":if(!t.skeleton)return void console.error("THREE.PropertyBinding: Can not bind to bones as node does not have a skeleton.",this);t=t.skeleton.bones;for(let e=0;e<t.length;e++)if(t[e].name===s){s=e;break}break;case"map":if("map"in t){t=t.map;break}if(!t.material)return void console.error("THREE.PropertyBinding: Can not bind to material as node does not have a material.",this);if(!t.material.map)return void console.error("THREE.PropertyBinding: Can not bind to material.map as node.material does not have a map.",this);t=t.material.map;break;default:if(void 0===t[i])return void console.error("THREE.PropertyBinding: Can not bind to objectName of node undefined.",this);t=t[i]}if(void 0!==s){if(void 0===t[s])return void console.error("THREE.PropertyBinding: Trying to bind to objectIndex of objectName, but is undefined.",this,t);t=t[s]}}let n=t[s];if(void 0===n)return void console.error("THREE.PropertyBinding: Trying to update property for track: "+e.nodeName+"."+s+" but it wasn't found.",t);let a=this.Versioning.None;this.targetObject=t,!0===t.isMaterial?a=this.Versioning.NeedsUpdate:!0===t.isObject3D&&(a=this.Versioning.MatrixWorldNeedsUpdate);let o=this.BindingType.Direct;if(void 0!==r){if("morphTargetInfluences"===s){if(!t.geometry)return void console.error("THREE.PropertyBinding: Can not bind to morphTargetInfluences because node does not have a geometry.",this);if(!t.geometry.morphAttributes)return void console.error("THREE.PropertyBinding: Can not bind to morphTargetInfluences because node does not have a geometry.morphAttributes.",this);void 0!==t.morphTargetDictionary[r]&&(r=t.morphTargetDictionary[r])}o=this.BindingType.ArrayElement,this.resolvedProperty=n,this.propertyIndex=r}else void 0!==n.fromArray&&void 0!==n.toArray?(o=this.BindingType.HasFromToArray,this.resolvedProperty=n):Array.isArray(n)?(o=this.BindingType.EntireArray,this.resolvedProperty=n):this.propertyName=s;this.getValue=this.GetterByBindingType[o],this.setValue=this.SetterByBindingTypeAndVersioning[o][a]}unbind(){this.node=null,this.getValue=this._getValue_unbound,this.setValue=this._setValue_unbound}constructor(t,e,i){this.path=e,this.parsedPath=i||hT.parseTrackName(e),this.node=hT.findNode(t,this.parsedPath.nodeName),this.rootNode=t,this.getValue=this._getValue_unbound,this.setValue=this._setValue_unbound}}hT.Composite=hz,hT.prototype.BindingType={Direct:0,EntireArray:1,ArrayElement:2,HasFromToArray:3},hT.prototype.Versioning={None:0,NeedsUpdate:1,MatrixWorldNeedsUpdate:2},hT.prototype.GetterByBindingType=[hT.prototype._getValue_direct,hT.prototype._getValue_array,hT.prototype._getValue_arrayElement,hT.prototype._getValue_toArray],hT.prototype.SetterByBindingTypeAndVersioning=[[hT.prototype._setValue_direct,hT.prototype._setValue_direct_setNeedsUpdate,hT.prototype._setValue_direct_setMatrixWorldNeedsUpdate],[hT.prototype._setValue_array,hT.prototype._setValue_array_setNeedsUpdate,hT.prototype._setValue_array_setMatrixWorldNeedsUpdate],[hT.prototype._setValue_arrayElement,hT.prototype._setValue_arrayElement_setNeedsUpdate,hT.prototype._setValue_arrayElement_setMatrixWorldNeedsUpdate],[hT.prototype._setValue_fromArray,hT.prototype._setValue_fromArray_setNeedsUpdate,hT.prototype._setValue_fromArray_setMatrixWorldNeedsUpdate]],new Float32Array(1);class hI extends iC{constructor(t=1,e=1,i=1,s={}){super(t,e,s),this.isRenderTargetArray=!0,this.depth=i,this.texture=new ik(null,t,e,i),this.texture.isRenderTargetTexture=!0}}class hC extends rF{copy(t){return super.copy(t),this.meshPerAttribute=t.meshPerAttribute,this}clone(t){let e=super.clone(t);return e.meshPerAttribute=this.meshPerAttribute,e}toJSON(t){let e=super.toJSON(t);return e.isInstancedInterleavedBuffer=!0,e.meshPerAttribute=this.meshPerAttribute,e}constructor(t,e,i=1){super(t,e),this.isInstancedInterleavedBuffer=!0,this.meshPerAttribute=i}}let hB=new st;class hk{set(t,e){this.ray.set(t,e)}setFromCamera(t,e){e.isPerspectiveCamera?(this.ray.origin.setFromMatrixPosition(e.matrixWorld),this.ray.direction.set(t.x,t.y,.5).unproject(e).sub(this.ray.origin).normalize(),this.camera=e):e.isOrthographicCamera?(this.ray.origin.set(t.x,t.y,(e.near+e.far)/(e.near-e.far)).unproject(e),this.ray.direction.set(0,0,-1).transformDirection(e.matrixWorld),this.camera=e):console.error("THREE.Raycaster: Unsupported camera type: "+e.type)}setFromXRController(t){return hB.identity().extractRotation(t.matrixWorld),this.ray.origin.setFromMatrixPosition(t.matrixWorld),this.ray.direction.set(0,0,-1).applyMatrix4(hB),this}intersectObject(t){let e=!(arguments.length>1)||void 0===arguments[1]||arguments[1],i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return hE(t,this,i,e),i.sort(hO),i}intersectObjects(t){let e=!(arguments.length>1)||void 0===arguments[1]||arguments[1],i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];for(let s=0,r=t.length;s<r;s++)hE(t[s],this,i,e);return i.sort(hO),i}constructor(t,e,i=0,s=1/0){this.ray=new i7(t,e),this.near=i,this.far=s,this.camera=null,this.layers=new sc,this.params={Mesh:{},Line:{threshold:1},LOD:{},Points:{threshold:1},Sprite:{}}}}function hO(t,e){return t.distance-e.distance}function hE(t,e,i,s){let r=!0;if(t.layers.test(e.layers)&&!1===t.raycast(e,i)&&(r=!1),!0===r&&!0===s){let s=t.children;for(let t=0,r=s.length;t<r;t++)hE(s[t],e,i,!0)}}class hR{set(t,e,i){return this.radius=t,this.phi=e,this.theta=i,this}copy(t){return this.radius=t.radius,this.phi=t.phi,this.theta=t.theta,this}makeSafe(){return this.phi=e6(this.phi,1e-6,Math.PI-1e-6),this}setFromVector3(t){return this.setFromCartesianCoords(t.x,t.y,t.z)}setFromCartesianCoords(t,e,i){return this.radius=Math.sqrt(t*t+e*e+i*i),0===this.radius?(this.theta=0,this.phi=0):(this.theta=Math.atan2(t,i),this.phi=Math.acos(e6(e/this.radius,-1,1))),this}clone(){return new this.constructor().copy(this)}constructor(t=1,e=0,i=0){this.radius=t,this.phi=e,this.theta=i}}class hP{identity(){return this.set(1,0,0,1),this}fromArray(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;for(let i=0;i<4;i++)this.elements[i]=t[i+e];return this}set(t,e,i,s){let r=this.elements;return r[0]=t,r[2]=e,r[1]=i,r[3]=s,this}constructor(t,e,i,s){hP.prototype.isMatrix2=!0,this.elements=[1,0,0,1],void 0!==t&&this.set(t,e,i,s)}}class hN extends e0{connect(t){if(void 0===t)return void console.warn("THREE.Controls: connect() now requires an element.");null!==this.domElement&&this.disconnect(),this.domElement=t}disconnect(){}dispose(){}update(){}constructor(t,e=null){super(),this.object=t,this.domElement=e,this.enabled=!0,this.state=-1,this.keys={},this.mouseButtons={LEFT:null,MIDDLE:null,RIGHT:null},this.touches={ONE:null,TWO:null}}}function hV(t,e,i,s){let r=function(t){switch(t){case tz:case tT:return{byteLength:1,components:1};case tC:case tI:case tE:return{byteLength:2,components:1};case tR:case tP:return{byteLength:2,components:4};case tk:case tB:case tO:return{byteLength:4,components:1};case tV:return{byteLength:4,components:3}}throw Error("Unknown texture type ".concat(t,"."))}(s);switch(i){case tL:return t*e;case tW:case tH:return t*e/r.components*r.byteLength;case tJ:case tq:return t*e*2/r.components*r.byteLength;case tF:return t*e*3/r.components*r.byteLength;case tj:case tX:return t*e*4/r.components*r.byteLength;case tZ:case tY:return Math.floor((t+3)/4)*Math.floor((e+3)/4)*8;case tQ:case tK:return Math.floor((t+3)/4)*Math.floor((e+3)/4)*16;case t0:case t2:return Math.max(t,16)*Math.max(e,8)/4;case t$:case t1:return Math.max(t,8)*Math.max(e,8)/2;case t3:case t5:return Math.floor((t+3)/4)*Math.floor((e+3)/4)*8;case t4:case t6:return Math.floor((t+3)/4)*Math.floor((e+3)/4)*16;case t8:return Math.floor((t+4)/5)*Math.floor((e+3)/4)*16;case t9:return Math.floor((t+4)/5)*Math.floor((e+4)/5)*16;case t7:return Math.floor((t+5)/6)*Math.floor((e+4)/5)*16;case et:return Math.floor((t+5)/6)*Math.floor((e+5)/6)*16;case ee:return Math.floor((t+7)/8)*Math.floor((e+4)/5)*16;case ei:return Math.floor((t+7)/8)*Math.floor((e+5)/6)*16;case es:return Math.floor((t+7)/8)*Math.floor((e+7)/8)*16;case er:return Math.floor((t+9)/10)*Math.floor((e+4)/5)*16;case en:return Math.floor((t+9)/10)*Math.floor((e+5)/6)*16;case ea:return Math.floor((t+9)/10)*Math.floor((e+7)/8)*16;case eo:return Math.floor((t+9)/10)*Math.floor((e+9)/10)*16;case eh:return Math.floor((t+11)/12)*Math.floor((e+9)/10)*16;case el:return Math.floor((t+11)/12)*Math.floor((e+11)/12)*16;case eu:case ec:case ed:return Math.ceil(t/4)*Math.ceil(e/4)*16;case ep:case em:return Math.ceil(t/4)*Math.ceil(e/4)*8;case ey:case ef:return Math.ceil(t/4)*Math.ceil(e/4)*16}throw Error("Unable to determine texture byte length for ".concat(i," format."))}"undefined"!=typeof __THREE_DEVTOOLS__&&__THREE_DEVTOOLS__.dispatchEvent(new CustomEvent("register",{detail:{revision:n}})),window.__THREE__?console.warn("WARNING: Multiple instances of Three.js being imported."):window.__THREE__=n}}]);