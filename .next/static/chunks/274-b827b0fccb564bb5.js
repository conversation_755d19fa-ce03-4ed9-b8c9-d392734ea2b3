"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[274],{76:(t,n,e)=>{function r(t){return function(){return this.matches(t)}}function i(t){return function(n){return n.matches(t)}}e.d(n,{A:()=>r,j:()=>i})},351:(t,n,e)=>{function r(){}function i(t){return null==t?r:function(){return this.querySelector(t)}}e.d(n,{A:()=>i})},872:(t,n,e)=>{e.d(n,{A:()=>u});var r={value:()=>{}};function i(){for(var t,n=0,e=arguments.length,r={};n<e;++n){if(!(t=arguments[n]+"")||t in r||/[\s.]/.test(t))throw Error("illegal type: "+t);r[t]=[]}return new o(r)}function o(t){this._=t}function a(t,n,e){for(var i=0,o=t.length;i<o;++i)if(t[i].name===n){t[i]=r,t=t.slice(0,i).concat(t.slice(i+1));break}return null!=e&&t.push({name:n,value:e}),t}o.prototype=i.prototype={constructor:o,on:function(t,n){var e,r=this._,i=(t+"").trim().split(/^|\s+/).map(function(t){var n="",e=t.indexOf(".");if(e>=0&&(n=t.slice(e+1),t=t.slice(0,e)),t&&!r.hasOwnProperty(t))throw Error("unknown type: "+t);return{type:t,name:n}}),o=-1,u=i.length;if(arguments.length<2){for(;++o<u;)if((e=(t=i[o]).type)&&(e=function(t,n){for(var e,r=0,i=t.length;r<i;++r)if((e=t[r]).name===n)return e.value}(r[e],t.name)))return e;return}if(null!=n&&"function"!=typeof n)throw Error("invalid callback: "+n);for(;++o<u;)if(e=(t=i[o]).type)r[e]=a(r[e],t.name,n);else if(null==n)for(e in r)r[e]=a(r[e],t.name,null);return this},copy:function(){var t={},n=this._;for(var e in n)t[e]=n[e].slice();return new o(t)},call:function(t,n){if((e=arguments.length-2)>0)for(var e,r,i=Array(e),o=0;o<e;++o)i[o]=arguments[o+2];if(!this._.hasOwnProperty(t))throw Error("unknown type: "+t);for(r=this._[t],o=0,e=r.length;o<e;++o)r[o].value.apply(n,i)},apply:function(t,n,e){if(!this._.hasOwnProperty(t))throw Error("unknown type: "+t);for(var r=this._[t],i=0,o=r.length;i<o;++i)r[i].value.apply(n,e)}};let u=i},1614:(t,n,e)=>{e.d(n,{A:()=>r});function r(t){return t.ownerDocument&&t.ownerDocument.defaultView||t.document&&t||t.defaultView}},2662:(t,n,e)=>{e.d(n,{A:()=>i,j:()=>o});var r=e(1614);function i(t,n,e){return arguments.length>1?this.each((null==n?function(t){return function(){this.style.removeProperty(t)}}:"function"==typeof n?function(t,n,e){return function(){var r=n.apply(this,arguments);null==r?this.style.removeProperty(t):this.style.setProperty(t,r,e)}}:function(t,n,e){return function(){this.style.setProperty(t,n,e)}})(t,n,null==e?"":e)):o(this.node(),t)}function o(t,n){return t.style.getPropertyValue(n)||(0,r.A)(t).getComputedStyle(t,null).getPropertyValue(n)}},3177:(t,n,e)=>{e.d(n,{LN:()=>Y,Ay:()=>B,zr:()=>O});var r=e(351),i=e(9438),o=e(76),a=Array.prototype.find;function u(){return this.firstElementChild}var s=Array.prototype.filter;function l(){return Array.from(this.children)}function c(t){return Array(t.length)}function h(t,n){this.ownerDocument=t.ownerDocument,this.namespaceURI=t.namespaceURI,this._next=null,this._parent=t,this.__data__=n}function f(t,n,e,r,i,o){for(var a,u=0,s=n.length,l=o.length;u<l;++u)(a=n[u])?(a.__data__=o[u],r[u]=a):e[u]=new h(t,o[u]);for(;u<s;++u)(a=n[u])&&(i[u]=a)}function p(t,n,e,r,i,o,a){var u,s,l,c=new Map,f=n.length,p=o.length,d=Array(f);for(u=0;u<f;++u)(s=n[u])&&(d[u]=l=a.call(s,s.__data__,u,n)+"",c.has(l)?i[u]=s:c.set(l,s));for(u=0;u<p;++u)l=a.call(t,o[u],u,o)+"",(s=c.get(l))?(r[u]=s,s.__data__=o[u],c.delete(l)):e[u]=new h(t,o[u]);for(u=0;u<f;++u)(s=n[u])&&c.get(d[u])===s&&(i[u]=s)}function d(t){return t.__data__}function v(t,n){return t<n?-1:t>n?1:t>=n?0:NaN}h.prototype={constructor:h,appendChild:function(t){return this._parent.insertBefore(t,this._next)},insertBefore:function(t,n){return this._parent.insertBefore(t,n)},querySelector:function(t){return this._parent.querySelector(t)},querySelectorAll:function(t){return this._parent.querySelectorAll(t)}};var y=e(7033),g=e(2662);function _(t){return t.trim().split(/^|\s+/)}function m(t){return t.classList||new x(t)}function x(t){this._node=t,this._names=_(t.getAttribute("class")||"")}function w(t,n){for(var e=m(t),r=-1,i=n.length;++r<i;)e.add(n[r])}function b(t,n){for(var e=m(t),r=-1,i=n.length;++r<i;)e.remove(n[r])}function A(){this.textContent=""}function k(){this.innerHTML=""}function N(){this.nextSibling&&this.parentNode.appendChild(this)}function M(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}x.prototype={add:function(t){0>this._names.indexOf(t)&&(this._names.push(t),this._node.setAttribute("class",this._names.join(" ")))},remove:function(t){var n=this._names.indexOf(t);n>=0&&(this._names.splice(n,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(t){return this._names.indexOf(t)>=0}};var z=e(6078);function E(t){var n=(0,y.A)(t);return(n.local?function(t){return function(){return this.ownerDocument.createElementNS(t.space,t.local)}}:function(t){return function(){var n=this.ownerDocument,e=this.namespaceURI;return e===z.g&&n.documentElement.namespaceURI===z.g?n.createElement(t):n.createElementNS(e,t)}})(n)}function $(){return null}function S(){var t=this.parentNode;t&&t.removeChild(this)}function T(){var t=this.cloneNode(!1),n=this.parentNode;return n?n.insertBefore(t,this.nextSibling):t}function q(){var t=this.cloneNode(!0),n=this.parentNode;return n?n.insertBefore(t,this.nextSibling):t}function C(t){return function(){var n=this.__on;if(n){for(var e,r=0,i=-1,o=n.length;r<o;++r)(e=n[r],t.type&&e.type!==t.type||e.name!==t.name)?n[++i]=e:this.removeEventListener(e.type,e.listener,e.options);++i?n.length=i:delete this.__on}}}function P(t,n,e){return function(){var r,i=this.__on,o=function(t){n.call(this,t,this.__data__)};if(i){for(var a=0,u=i.length;a<u;++a)if((r=i[a]).type===t.type&&r.name===t.name){this.removeEventListener(r.type,r.listener,r.options),this.addEventListener(r.type,r.listener=o,r.options=e),r.value=n;return}}this.addEventListener(t.type,o,e),r={type:t.type,name:t.name,value:n,listener:o,options:e},i?i.push(r):this.__on=[r]}}var X=e(1614);function j(t,n,e){var r=(0,X.A)(t),i=r.CustomEvent;"function"==typeof i?i=new i(n,e):(i=r.document.createEvent("Event"),e?(i.initEvent(n,e.bubbles,e.cancelable),i.detail=e.detail):i.initEvent(n,!1,!1)),t.dispatchEvent(i)}var O=[null];function Y(t,n){this._groups=t,this._parents=n}function R(){return new Y([[document.documentElement]],O)}Y.prototype=R.prototype={constructor:Y,select:function(t){"function"!=typeof t&&(t=(0,r.A)(t));for(var n=this._groups,e=n.length,i=Array(e),o=0;o<e;++o)for(var a,u,s=n[o],l=s.length,c=i[o]=Array(l),h=0;h<l;++h)(a=s[h])&&(u=t.call(a,a.__data__,h,s))&&("__data__"in a&&(u.__data__=a.__data__),c[h]=u);return new Y(i,this._parents)},selectAll:function(t){if("function"==typeof t){var n;n=t,t=function(){var t;return t=n.apply(this,arguments),null==t?[]:Array.isArray(t)?t:Array.from(t)}}else t=(0,i.A)(t);for(var e=this._groups,r=e.length,o=[],a=[],u=0;u<r;++u)for(var s,l=e[u],c=l.length,h=0;h<c;++h)(s=l[h])&&(o.push(t.call(s,s.__data__,h,l)),a.push(s));return new Y(o,a)},selectChild:function(t){var n;return this.select(null==t?u:(n="function"==typeof t?t:(0,o.j)(t),function(){return a.call(this.children,n)}))},selectChildren:function(t){var n;return this.selectAll(null==t?l:(n="function"==typeof t?t:(0,o.j)(t),function(){return s.call(this.children,n)}))},filter:function(t){"function"!=typeof t&&(t=(0,o.A)(t));for(var n=this._groups,e=n.length,r=Array(e),i=0;i<e;++i)for(var a,u=n[i],s=u.length,l=r[i]=[],c=0;c<s;++c)(a=u[c])&&t.call(a,a.__data__,c,u)&&l.push(a);return new Y(r,this._parents)},data:function(t,n){if(!arguments.length)return Array.from(this,d);var e=n?p:f,r=this._parents,i=this._groups;"function"!=typeof t&&(w=t,t=function(){return w});for(var o=i.length,a=Array(o),u=Array(o),s=Array(o),l=0;l<o;++l){var c=r[l],h=i[l],v=h.length,y="object"==typeof(x=t.call(c,c&&c.__data__,l,r))&&"length"in x?x:Array.from(x),g=y.length,_=u[l]=Array(g),m=a[l]=Array(g);e(c,h,_,m,s[l]=Array(v),y,n);for(var x,w,b,A,k=0,N=0;k<g;++k)if(b=_[k]){for(k>=N&&(N=k+1);!(A=m[N])&&++N<g;);b._next=A||null}}return(a=new Y(a,r))._enter=u,a._exit=s,a},enter:function(){return new Y(this._enter||this._groups.map(c),this._parents)},exit:function(){return new Y(this._exit||this._groups.map(c),this._parents)},join:function(t,n,e){var r=this.enter(),i=this,o=this.exit();return"function"==typeof t?(r=t(r))&&(r=r.selection()):r=r.append(t+""),null!=n&&(i=n(i))&&(i=i.selection()),null==e?o.remove():e(o),r&&i?r.merge(i).order():i},merge:function(t){for(var n=t.selection?t.selection():t,e=this._groups,r=n._groups,i=e.length,o=r.length,a=Math.min(i,o),u=Array(i),s=0;s<a;++s)for(var l,c=e[s],h=r[s],f=c.length,p=u[s]=Array(f),d=0;d<f;++d)(l=c[d]||h[d])&&(p[d]=l);for(;s<i;++s)u[s]=e[s];return new Y(u,this._parents)},selection:function(){return this},order:function(){for(var t=this._groups,n=-1,e=t.length;++n<e;)for(var r,i=t[n],o=i.length-1,a=i[o];--o>=0;)(r=i[o])&&(a&&4^r.compareDocumentPosition(a)&&a.parentNode.insertBefore(r,a),a=r);return this},sort:function(t){function n(n,e){return n&&e?t(n.__data__,e.__data__):!n-!e}t||(t=v);for(var e=this._groups,r=e.length,i=Array(r),o=0;o<r;++o){for(var a,u=e[o],s=u.length,l=i[o]=Array(s),c=0;c<s;++c)(a=u[c])&&(l[c]=a);l.sort(n)}return new Y(i,this._parents).order()},call:function(){var t=arguments[0];return arguments[0]=this,t.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var t=this._groups,n=0,e=t.length;n<e;++n)for(var r=t[n],i=0,o=r.length;i<o;++i){var a=r[i];if(a)return a}return null},size:function(){let t=0;for(let n of this)++t;return t},empty:function(){return!this.node()},each:function(t){for(var n=this._groups,e=0,r=n.length;e<r;++e)for(var i,o=n[e],a=0,u=o.length;a<u;++a)(i=o[a])&&t.call(i,i.__data__,a,o);return this},attr:function(t,n){var e=(0,y.A)(t);if(arguments.length<2){var r=this.node();return e.local?r.getAttributeNS(e.space,e.local):r.getAttribute(e)}return this.each((null==n?e.local?function(t){return function(){this.removeAttributeNS(t.space,t.local)}}:function(t){return function(){this.removeAttribute(t)}}:"function"==typeof n?e.local?function(t,n){return function(){var e=n.apply(this,arguments);null==e?this.removeAttributeNS(t.space,t.local):this.setAttributeNS(t.space,t.local,e)}}:function(t,n){return function(){var e=n.apply(this,arguments);null==e?this.removeAttribute(t):this.setAttribute(t,e)}}:e.local?function(t,n){return function(){this.setAttributeNS(t.space,t.local,n)}}:function(t,n){return function(){this.setAttribute(t,n)}})(e,n))},style:g.A,property:function(t,n){return arguments.length>1?this.each((null==n?function(t){return function(){delete this[t]}}:"function"==typeof n?function(t,n){return function(){var e=n.apply(this,arguments);null==e?delete this[t]:this[t]=e}}:function(t,n){return function(){this[t]=n}})(t,n)):this.node()[t]},classed:function(t,n){var e=_(t+"");if(arguments.length<2){for(var r=m(this.node()),i=-1,o=e.length;++i<o;)if(!r.contains(e[i]))return!1;return!0}return this.each(("function"==typeof n?function(t,n){return function(){(n.apply(this,arguments)?w:b)(this,t)}}:n?function(t){return function(){w(this,t)}}:function(t){return function(){b(this,t)}})(e,n))},text:function(t){return arguments.length?this.each(null==t?A:("function"==typeof t?function(t){return function(){var n=t.apply(this,arguments);this.textContent=null==n?"":n}}:function(t){return function(){this.textContent=t}})(t)):this.node().textContent},html:function(t){return arguments.length?this.each(null==t?k:("function"==typeof t?function(t){return function(){var n=t.apply(this,arguments);this.innerHTML=null==n?"":n}}:function(t){return function(){this.innerHTML=t}})(t)):this.node().innerHTML},raise:function(){return this.each(N)},lower:function(){return this.each(M)},append:function(t){var n="function"==typeof t?t:E(t);return this.select(function(){return this.appendChild(n.apply(this,arguments))})},insert:function(t,n){var e="function"==typeof t?t:E(t),i=null==n?$:"function"==typeof n?n:(0,r.A)(n);return this.select(function(){return this.insertBefore(e.apply(this,arguments),i.apply(this,arguments)||null)})},remove:function(){return this.each(S)},clone:function(t){return this.select(t?q:T)},datum:function(t){return arguments.length?this.property("__data__",t):this.node().__data__},on:function(t,n,e){var r,i,o=(t+"").trim().split(/^|\s+/).map(function(t){var n="",e=t.indexOf(".");return e>=0&&(n=t.slice(e+1),t=t.slice(0,e)),{type:t,name:n}}),a=o.length;if(arguments.length<2){var u=this.node().__on;if(u){for(var s,l=0,c=u.length;l<c;++l)for(r=0,s=u[l];r<a;++r)if((i=o[r]).type===s.type&&i.name===s.name)return s.value}return}for(r=0,u=n?P:C;r<a;++r)this.each(u(o[r],n,e));return this},dispatch:function(t,n){return this.each(("function"==typeof n?function(t,n){return function(){return j(this,t,n.apply(this,arguments))}}:function(t,n){return function(){return j(this,t,n)}})(t,n))},[Symbol.iterator]:function*(){for(var t=this._groups,n=0,e=t.length;n<e;++n)for(var r,i=t[n],o=0,a=i.length;o<a;++o)(r=i[o])&&(yield r)}};let B=R},4274:(t,n,e)=>{e.d(n,{$Er:()=>t1,jTM:()=>t2,eRw:()=>t7,kJC:()=>nn,xJS:()=>na,tXi:()=>no,Ltv:()=>tD.A,s_O:()=>nw});var r,i=e(3177),o=e(872),a=e(4604);function u(t,n,e){var r=new a.M4;return n=null==n?0:+n,r.restart(e=>{r.stop(),t(e+n)},n,e),r}var s=(0,o.A)("start","end","cancel","interrupt"),l=[];function c(t,n,e,r,i,o){var c=t.__transition;if(c){if(e in c)return}else t.__transition={};!function(t,n,e){var r,i=t.__transition;function o(a){var c,h,f,p;if(1!==e.state)return l();for(c in i)if((p=i[c]).name===e.name){if(3===p.state)return u(o);4===p.state?(p.state=6,p.timer.stop(),p.on.call("interrupt",t,t.__data__,p.index,p.group),delete i[c]):+c<n&&(p.state=6,p.timer.stop(),p.on.call("cancel",t,t.__data__,p.index,p.group),delete i[c])}if(u(function(){3===e.state&&(e.state=4,e.timer.restart(s,e.delay,e.time),s(a))}),e.state=2,e.on.call("start",t,t.__data__,e.index,e.group),2===e.state){for(c=0,e.state=3,r=Array(f=e.tween.length),h=-1;c<f;++c)(p=e.tween[c].value.call(t,t.__data__,e.index,e.group))&&(r[++h]=p);r.length=h+1}}function s(n){for(var i=n<e.duration?e.ease.call(null,n/e.duration):(e.timer.restart(l),e.state=5,1),o=-1,a=r.length;++o<a;)r[o].call(t,i);5===e.state&&(e.on.call("end",t,t.__data__,e.index,e.group),l())}function l(){for(var r in e.state=6,e.timer.stop(),delete i[n],i)return;delete t.__transition}i[n]=e,e.timer=(0,a.O1)(function(t){e.state=1,e.timer.restart(o,e.delay,e.time),e.delay<=t&&o(t-e.delay)},0,e.time)}(t,e,{name:n,index:r,group:i,on:s,tween:l,time:o.time,delay:o.delay,duration:o.duration,ease:o.ease,timer:null,state:0})}function h(t,n){var e=p(t,n);if(e.state>0)throw Error("too late; already scheduled");return e}function f(t,n){var e=p(t,n);if(e.state>3)throw Error("too late; already running");return e}function p(t,n){var e=t.__transition;if(!e||!(e=e[n]))throw Error("transition not found");return e}function d(t,n){var e,r,i,o=t.__transition,a=!0;if(o){for(i in n=null==n?null:n+"",o){if((e=o[i]).name!==n){a=!1;continue}r=e.state>2&&e.state<5,e.state=6,e.timer.stop(),e.on.call(r?"interrupt":"cancel",t,t.__data__,e.index,e.group),delete o[i]}a&&delete t.__transition}}function v(t,n){return t*=1,n*=1,function(e){return t*(1-e)+n*e}}var y=180/Math.PI,g={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function _(t,n,e,r,i,o){var a,u,s;return(a=Math.sqrt(t*t+n*n))&&(t/=a,n/=a),(s=t*e+n*r)&&(e-=t*s,r-=n*s),(u=Math.sqrt(e*e+r*r))&&(e/=u,r/=u,s/=u),t*r<n*e&&(t=-t,n=-n,s=-s,a=-a),{translateX:i,translateY:o,rotate:Math.atan2(n,t)*y,skewX:Math.atan(s)*y,scaleX:a,scaleY:u}}function m(t,n,e,r){function i(t){return t.length?t.pop()+" ":""}return function(o,a){var u,s,l,c,h=[],f=[];return o=t(o),a=t(a),!function(t,r,i,o,a,u){if(t!==i||r!==o){var s=a.push("translate(",null,n,null,e);u.push({i:s-4,x:v(t,i)},{i:s-2,x:v(r,o)})}else(i||o)&&a.push("translate("+i+n+o+e)}(o.translateX,o.translateY,a.translateX,a.translateY,h,f),u=o.rotate,s=a.rotate,u!==s?(u-s>180?s+=360:s-u>180&&(u+=360),f.push({i:h.push(i(h)+"rotate(",null,r)-2,x:v(u,s)})):s&&h.push(i(h)+"rotate("+s+r),l=o.skewX,c=a.skewX,l!==c?f.push({i:h.push(i(h)+"skewX(",null,r)-2,x:v(l,c)}):c&&h.push(i(h)+"skewX("+c+r),!function(t,n,e,r,o,a){if(t!==e||n!==r){var u=o.push(i(o)+"scale(",null,",",null,")");a.push({i:u-4,x:v(t,e)},{i:u-2,x:v(n,r)})}else(1!==e||1!==r)&&o.push(i(o)+"scale("+e+","+r+")")}(o.scaleX,o.scaleY,a.scaleX,a.scaleY,h,f),o=a=null,function(t){for(var n,e=-1,r=f.length;++e<r;)h[(n=f[e]).i]=n.x(t);return h.join("")}}}var x=m(function(t){let n=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(t+"");return n.isIdentity?g:_(n.a,n.b,n.c,n.d,n.e,n.f)},"px, ","px)","deg)"),w=m(function(t){return null==t?g:(r||(r=document.createElementNS("http://www.w3.org/2000/svg","g")),r.setAttribute("transform",t),t=r.transform.baseVal.consolidate())?_((t=t.matrix).a,t.b,t.c,t.d,t.e,t.f):g},", ",")",")"),b=e(7033);function A(t,n,e){var r=t._id;return t.each(function(){var t=f(this,r);(t.value||(t.value={}))[n]=e.apply(this,arguments)}),function(t){return p(t,r).value[n]}}function k(t,n,e){t.prototype=n.prototype=e,e.constructor=t}function N(t,n){var e=Object.create(t.prototype);for(var r in n)e[r]=n[r];return e}function M(){}var z="\\s*([+-]?\\d+)\\s*",E="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",$="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",S=/^#([0-9a-f]{3,8})$/,T=RegExp(`^rgb\\(${z},${z},${z}\\)$`),q=RegExp(`^rgb\\(${$},${$},${$}\\)$`),C=RegExp(`^rgba\\(${z},${z},${z},${E}\\)$`),P=RegExp(`^rgba\\(${$},${$},${$},${E}\\)$`),X=RegExp(`^hsl\\(${E},${$},${$}\\)$`),j=RegExp(`^hsla\\(${E},${$},${$},${E}\\)$`),O={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function Y(){return this.rgb().formatHex()}function R(){return this.rgb().formatRgb()}function B(t){var n,e;return t=(t+"").trim().toLowerCase(),(n=S.exec(t))?(e=n[1].length,n=parseInt(n[1],16),6===e?D(n):3===e?new L(n>>8&15|n>>4&240,n>>4&15|240&n,(15&n)<<4|15&n,1):8===e?V(n>>24&255,n>>16&255,n>>8&255,(255&n)/255):4===e?V(n>>12&15|n>>8&240,n>>8&15|n>>4&240,n>>4&15|240&n,((15&n)<<4|15&n)/255):null):(n=T.exec(t))?new L(n[1],n[2],n[3],1):(n=q.exec(t))?new L(255*n[1]/100,255*n[2]/100,255*n[3]/100,1):(n=C.exec(t))?V(n[1],n[2],n[3],n[4]):(n=P.exec(t))?V(255*n[1]/100,255*n[2]/100,255*n[3]/100,n[4]):(n=X.exec(t))?J(n[1],n[2]/100,n[3]/100,1):(n=j.exec(t))?J(n[1],n[2]/100,n[3]/100,n[4]):O.hasOwnProperty(t)?D(O[t]):"transparent"===t?new L(NaN,NaN,NaN,0):null}function D(t){return new L(t>>16&255,t>>8&255,255&t,1)}function V(t,n,e,r){return r<=0&&(t=n=e=NaN),new L(t,n,e,r)}function I(t,n,e,r){var i;return 1==arguments.length?((i=t)instanceof M||(i=B(i)),i)?new L((i=i.rgb()).r,i.g,i.b,i.opacity):new L:new L(t,n,e,null==r?1:r)}function L(t,n,e,r){this.r=+t,this.g=+n,this.b=+e,this.opacity=+r}function H(){return`#${F(this.r)}${F(this.g)}${F(this.b)}`}function U(){let t=G(this.opacity);return`${1===t?"rgb(":"rgba("}${K(this.r)}, ${K(this.g)}, ${K(this.b)}${1===t?")":`, ${t})`}`}function G(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function K(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function F(t){return((t=K(t))<16?"0":"")+t.toString(16)}function J(t,n,e,r){return r<=0?t=n=e=NaN:e<=0||e>=1?t=n=NaN:n<=0&&(t=NaN),new W(t,n,e,r)}function Q(t){if(t instanceof W)return new W(t.h,t.s,t.l,t.opacity);if(t instanceof M||(t=B(t)),!t)return new W;if(t instanceof W)return t;var n=(t=t.rgb()).r/255,e=t.g/255,r=t.b/255,i=Math.min(n,e,r),o=Math.max(n,e,r),a=NaN,u=o-i,s=(o+i)/2;return u?(a=n===o?(e-r)/u+(e<r)*6:e===o?(r-n)/u+2:(n-e)/u+4,u/=s<.5?o+i:2-o-i,a*=60):u=s>0&&s<1?0:a,new W(a,u,s,t.opacity)}function W(t,n,e,r){this.h=+t,this.s=+n,this.l=+e,this.opacity=+r}function Z(t){return(t=(t||0)%360)<0?t+360:t}function tt(t){return Math.max(0,Math.min(1,t||0))}function tn(t,n,e){return(t<60?n+(e-n)*t/60:t<180?e:t<240?n+(e-n)*(240-t)/60:n)*255}function te(t,n,e,r,i){var o=t*t,a=o*t;return((1-3*t+3*o-a)*n+(4-6*o+3*a)*e+(1+3*t+3*o-3*a)*r+a*i)/6}k(M,B,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:Y,formatHex:Y,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return Q(this).formatHsl()},formatRgb:R,toString:R}),k(L,I,N(M,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new L(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new L(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new L(K(this.r),K(this.g),K(this.b),G(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:H,formatHex:H,formatHex8:function(){return`#${F(this.r)}${F(this.g)}${F(this.b)}${F((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:U,toString:U})),k(W,function(t,n,e,r){return 1==arguments.length?Q(t):new W(t,n,e,null==r?1:r)},N(M,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new W(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new W(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,n=isNaN(t)||isNaN(this.s)?0:this.s,e=this.l,r=e+(e<.5?e:1-e)*n,i=2*e-r;return new L(tn(t>=240?t-240:t+120,i,r),tn(t,i,r),tn(t<120?t+240:t-120,i,r),this.opacity)},clamp(){return new W(Z(this.h),tt(this.s),tt(this.l),G(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let t=G(this.opacity);return`${1===t?"hsl(":"hsla("}${Z(this.h)}, ${100*tt(this.s)}%, ${100*tt(this.l)}%${1===t?")":`, ${t})`}`}}));let tr=t=>()=>t;function ti(t,n){var e,r,i=n-t;return i?(e=t,r=i,function(t){return e+t*r}):tr(isNaN(t)?n:t)}let to=function t(n){var e,r=1==(e=+n)?ti:function(t,n){var r,i,o;return n-t?(r=t,i=n,r=Math.pow(r,o=e),i=Math.pow(i,o)-r,o=1/o,function(t){return Math.pow(r+t*i,o)}):tr(isNaN(t)?n:t)};function i(t,n){var e=r((t=I(t)).r,(n=I(n)).r),i=r(t.g,n.g),o=r(t.b,n.b),a=ti(t.opacity,n.opacity);return function(n){return t.r=e(n),t.g=i(n),t.b=o(n),t.opacity=a(n),t+""}}return i.gamma=t,i}(1);function ta(t){return function(n){var e,r,i=n.length,o=Array(i),a=Array(i),u=Array(i);for(e=0;e<i;++e)r=I(n[e]),o[e]=r.r||0,a[e]=r.g||0,u[e]=r.b||0;return o=t(o),a=t(a),u=t(u),r.opacity=1,function(t){return r.r=o(t),r.g=a(t),r.b=u(t),r+""}}}ta(function(t){var n=t.length-1;return function(e){var r=e<=0?e=0:e>=1?(e=1,n-1):Math.floor(e*n),i=t[r],o=t[r+1],a=r>0?t[r-1]:2*i-o,u=r<n-1?t[r+2]:2*o-i;return te((e-r/n)*n,a,i,o,u)}}),ta(function(t){var n=t.length;return function(e){var r=Math.floor(((e%=1)<0?++e:e)*n),i=t[(r+n-1)%n],o=t[r%n],a=t[(r+1)%n],u=t[(r+2)%n];return te((e-r/n)*n,i,o,a,u)}});var tu=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,ts=RegExp(tu.source,"g");function tl(t,n){var e;return("number"==typeof n?v:n instanceof B?to:(e=B(n))?(n=e,to):function(t,n){var e,r,i,o,a,u=tu.lastIndex=ts.lastIndex=0,s=-1,l=[],c=[];for(t+="",n+="";(i=tu.exec(t))&&(o=ts.exec(n));)(a=o.index)>u&&(a=n.slice(u,a),l[s]?l[s]+=a:l[++s]=a),(i=i[0])===(o=o[0])?l[s]?l[s]+=o:l[++s]=o:(l[++s]=null,c.push({i:s,x:v(i,o)})),u=ts.lastIndex;return u<n.length&&(a=n.slice(u),l[s]?l[s]+=a:l[++s]=a),l.length<2?c[0]?(e=c[0].x,function(t){return e(t)+""}):(r=n,function(){return r}):(n=c.length,function(t){for(var e,r=0;r<n;++r)l[(e=c[r]).i]=e.x(t);return l.join("")})})(t,n)}var tc=e(76),th=e(351),tf=e(9438),tp=i.Ay.prototype.constructor,td=e(2662);function tv(t){return function(){this.style.removeProperty(t)}}var ty=0;function tg(t,n,e,r){this._groups=t,this._parents=n,this._name=e,this._id=r}var t_=i.Ay.prototype;tg.prototype=(function(t){return(0,i.Ay)().transition(t)}).prototype={constructor:tg,select:function(t){var n=this._name,e=this._id;"function"!=typeof t&&(t=(0,th.A)(t));for(var r=this._groups,i=r.length,o=Array(i),a=0;a<i;++a)for(var u,s,l=r[a],h=l.length,f=o[a]=Array(h),d=0;d<h;++d)(u=l[d])&&(s=t.call(u,u.__data__,d,l))&&("__data__"in u&&(s.__data__=u.__data__),f[d]=s,c(f[d],n,e,d,f,p(u,e)));return new tg(o,this._parents,n,e)},selectAll:function(t){var n=this._name,e=this._id;"function"!=typeof t&&(t=(0,tf.A)(t));for(var r=this._groups,i=r.length,o=[],a=[],u=0;u<i;++u)for(var s,l=r[u],h=l.length,f=0;f<h;++f)if(s=l[f]){for(var d,v=t.call(s,s.__data__,f,l),y=p(s,e),g=0,_=v.length;g<_;++g)(d=v[g])&&c(d,n,e,g,v,y);o.push(v),a.push(s)}return new tg(o,a,n,e)},selectChild:t_.selectChild,selectChildren:t_.selectChildren,filter:function(t){"function"!=typeof t&&(t=(0,tc.A)(t));for(var n=this._groups,e=n.length,r=Array(e),i=0;i<e;++i)for(var o,a=n[i],u=a.length,s=r[i]=[],l=0;l<u;++l)(o=a[l])&&t.call(o,o.__data__,l,a)&&s.push(o);return new tg(r,this._parents,this._name,this._id)},merge:function(t){if(t._id!==this._id)throw Error();for(var n=this._groups,e=t._groups,r=n.length,i=e.length,o=Math.min(r,i),a=Array(r),u=0;u<o;++u)for(var s,l=n[u],c=e[u],h=l.length,f=a[u]=Array(h),p=0;p<h;++p)(s=l[p]||c[p])&&(f[p]=s);for(;u<r;++u)a[u]=n[u];return new tg(a,this._parents,this._name,this._id)},selection:function(){return new tp(this._groups,this._parents)},transition:function(){for(var t=this._name,n=this._id,e=++ty,r=this._groups,i=r.length,o=0;o<i;++o)for(var a,u=r[o],s=u.length,l=0;l<s;++l)if(a=u[l]){var h=p(a,n);c(a,t,e,l,u,{time:h.time+h.delay+h.duration,delay:0,duration:h.duration,ease:h.ease})}return new tg(r,this._parents,t,e)},call:t_.call,nodes:t_.nodes,node:t_.node,size:t_.size,empty:t_.empty,each:t_.each,on:function(t,n){var e,r,i,o,a,u,s=this._id;return arguments.length<2?p(this.node(),s).on.on(t):this.each((e=s,r=t,i=n,u=(r+"").trim().split(/^|\s+/).every(function(t){var n=t.indexOf(".");return n>=0&&(t=t.slice(0,n)),!t||"start"===t})?h:f,function(){var t=u(this,e),n=t.on;n!==o&&(a=(o=n).copy()).on(r,i),t.on=a}))},attr:function(t,n){var e=(0,b.A)(t),r="transform"===e?w:tl;return this.attrTween(t,"function"==typeof n?(e.local?function(t,n,e){var r,i,o;return function(){var a,u,s=e(this);return null==s?void this.removeAttributeNS(t.space,t.local):(a=this.getAttributeNS(t.space,t.local))===(u=s+"")?null:a===r&&u===i?o:(i=u,o=n(r=a,s))}}:function(t,n,e){var r,i,o;return function(){var a,u,s=e(this);return null==s?void this.removeAttribute(t):(a=this.getAttribute(t))===(u=s+"")?null:a===r&&u===i?o:(i=u,o=n(r=a,s))}})(e,r,A(this,"attr."+t,n)):null==n?(e.local?function(t){return function(){this.removeAttributeNS(t.space,t.local)}}:function(t){return function(){this.removeAttribute(t)}})(e):(e.local?function(t,n,e){var r,i,o=e+"";return function(){var a=this.getAttributeNS(t.space,t.local);return a===o?null:a===r?i:i=n(r=a,e)}}:function(t,n,e){var r,i,o=e+"";return function(){var a=this.getAttribute(t);return a===o?null:a===r?i:i=n(r=a,e)}})(e,r,n))},attrTween:function(t,n){var e="attr."+t;if(arguments.length<2)return(e=this.tween(e))&&e._value;if(null==n)return this.tween(e,null);if("function"!=typeof n)throw Error();var r=(0,b.A)(t);return this.tween(e,(r.local?function(t,n){var e,r;function i(){var i=n.apply(this,arguments);return i!==r&&(e=(r=i)&&function(n){this.setAttributeNS(t.space,t.local,i.call(this,n))}),e}return i._value=n,i}:function(t,n){var e,r;function i(){var i=n.apply(this,arguments);return i!==r&&(e=(r=i)&&function(n){this.setAttribute(t,i.call(this,n))}),e}return i._value=n,i})(r,n))},style:function(t,n,e){var r,i,o,a,u,s,l,c,h,p,d,v,y,g,_,m,w,b,k,N,M,z="transform"==(t+="")?x:tl;return null==n?this.styleTween(t,(r=t,function(){var t=(0,td.j)(this,r),n=(this.style.removeProperty(r),(0,td.j)(this,r));return t===n?null:t===i&&n===o?a:a=z(i=t,o=n)})).on("end.style."+t,tv(t)):"function"==typeof n?this.styleTween(t,(u=t,s=A(this,"style."+t,n),function(){var t=(0,td.j)(this,u),n=s(this),e=n+"";return null==n&&(this.style.removeProperty(u),e=n=(0,td.j)(this,u)),t===e?null:t===l&&e===c?h:(c=e,h=z(l=t,n))})).each((p=this._id,w="end."+(m="style."+(d=t)),function(){var t=f(this,p),n=t.on,e=null==t.value[m]?_||(_=tv(d)):void 0;(n!==v||g!==e)&&(y=(v=n).copy()).on(w,g=e),t.on=y})):this.styleTween(t,(b=t,M=n+"",function(){var t=(0,td.j)(this,b);return t===M?null:t===k?N:N=z(k=t,n)}),e).on("end.style."+t,null)},styleTween:function(t,n,e){var r="style."+(t+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(null==n)return this.tween(r,null);if("function"!=typeof n)throw Error();return this.tween(r,function(t,n,e){var r,i;function o(){var o=n.apply(this,arguments);return o!==i&&(r=(i=o)&&function(n){this.style.setProperty(t,o.call(this,n),e)}),r}return o._value=n,o}(t,n,null==e?"":e))},text:function(t){var n,e;return this.tween("text","function"==typeof t?(n=A(this,"text",t),function(){var t=n(this);this.textContent=null==t?"":t}):(e=null==t?"":t+"",function(){this.textContent=e}))},textTween:function(t){var n="text";if(arguments.length<1)return(n=this.tween(n))&&n._value;if(null==t)return this.tween(n,null);if("function"!=typeof t)throw Error();return this.tween(n,function(t){var n,e;function r(){var r=t.apply(this,arguments);return r!==e&&(n=(e=r)&&function(t){this.textContent=r.call(this,t)}),n}return r._value=t,r}(t))},remove:function(){var t;return this.on("end.remove",(t=this._id,function(){var n=this.parentNode;for(var e in this.__transition)if(+e!==t)return;n&&n.removeChild(this)}))},tween:function(t,n){var e=this._id;if(t+="",arguments.length<2){for(var r,i=p(this.node(),e).tween,o=0,a=i.length;o<a;++o)if((r=i[o]).name===t)return r.value;return null}return this.each((null==n?function(t,n){var e,r;return function(){var i=f(this,t),o=i.tween;if(o!==e){r=e=o;for(var a=0,u=r.length;a<u;++a)if(r[a].name===n){(r=r.slice()).splice(a,1);break}}i.tween=r}}:function(t,n,e){var r,i;if("function"!=typeof e)throw Error();return function(){var o=f(this,t),a=o.tween;if(a!==r){i=(r=a).slice();for(var u={name:n,value:e},s=0,l=i.length;s<l;++s)if(i[s].name===n){i[s]=u;break}s===l&&i.push(u)}o.tween=i}})(e,t,n))},delay:function(t){var n=this._id;return arguments.length?this.each(("function"==typeof t?function(t,n){return function(){h(this,t).delay=+n.apply(this,arguments)}}:function(t,n){return n*=1,function(){h(this,t).delay=n}})(n,t)):p(this.node(),n).delay},duration:function(t){var n=this._id;return arguments.length?this.each(("function"==typeof t?function(t,n){return function(){f(this,t).duration=+n.apply(this,arguments)}}:function(t,n){return n*=1,function(){f(this,t).duration=n}})(n,t)):p(this.node(),n).duration},ease:function(t){var n=this._id;return arguments.length?this.each(function(t,n){if("function"!=typeof n)throw Error();return function(){f(this,t).ease=n}}(n,t)):p(this.node(),n).ease},easeVarying:function(t){var n;if("function"!=typeof t)throw Error();return this.each((n=this._id,function(){var e=t.apply(this,arguments);if("function"!=typeof e)throw Error();f(this,n).ease=e}))},end:function(){var t,n,e=this,r=e._id,i=e.size();return new Promise(function(o,a){var u={value:a},s={value:function(){0==--i&&o()}};e.each(function(){var e=f(this,r),i=e.on;i!==t&&((n=(t=i).copy())._.cancel.push(u),n._.interrupt.push(u),n._.end.push(s)),e.on=n}),0===i&&o()})},[Symbol.iterator]:t_[Symbol.iterator]};var tm={time:null,delay:0,duration:250,ease:function(t){return((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2}};i.Ay.prototype.interrupt=function(t){return this.each(function(){d(this,t)})},i.Ay.prototype.transition=function(t){var n,e;t instanceof tg?(n=t._id,t=t._name):(n=++ty,(e=tm).time=(0,a.tB)(),t=null==t?null:t+"");for(var r=this._groups,i=r.length,o=0;o<i;++o)for(var u,s=r[o],l=s.length,h=0;h<l;++h)(u=s[h])&&c(u,t,n,h,s,e||function(t,n){for(var e;!(e=t.__transition)||!(e=e[n]);)if(!(t=t.parentNode))throw Error(`transition ${n} not found`);return e}(u,n));return new tg(r,this._parents,t,n)};var tx={name:"drag"},tw={name:"space"},tb={name:"handle"},tA={name:"center"};let{abs:tk,max:tN,min:tM}=Math;function tz(t){return[+t[0],+t[1]]}function tE(t){return[tz(t[0]),tz(t[1])]}var t$={name:"x",handles:["w","e"].map(tj),input:function(t,n){return null==t?null:[[+t[0],n[0][1]],[+t[1],n[1][1]]]},output:function(t){return t&&[t[0][0],t[1][0]]}},tS={name:"y",handles:["n","s"].map(tj),input:function(t,n){return null==t?null:[[n[0][0],+t[0]],[n[1][0],+t[1]]]},output:function(t){return t&&[t[0][1],t[1][1]]}},tT=(["n","w","e","s","nw","ne","sw","se"].map(tj),{overlay:"crosshair",selection:"move",n:"ns-resize",e:"ew-resize",s:"ns-resize",w:"ew-resize",nw:"nwse-resize",ne:"nesw-resize",se:"nwse-resize",sw:"nesw-resize"}),tq={e:"w",w:"e",nw:"ne",ne:"nw",se:"sw",sw:"se"},tC={n:"s",s:"n",nw:"sw",ne:"se",se:"ne",sw:"nw"},tP={overlay:1,selection:1,n:null,e:1,s:null,w:-1,nw:-1,ne:1,se:1,sw:-1},tX={overlay:1,selection:1,n:-1,e:null,s:1,w:null,nw:-1,ne:-1,se:1,sw:1};function tj(t){return{type:t}}function tO(t){return!t.ctrlKey&&!t.button}function tY(){var t=this.ownerSVGElement||this;return t.hasAttribute("viewBox")?[[(t=t.viewBox.baseVal).x,t.y],[t.x+t.width,t.y+t.height]]:[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]}function tR(){return navigator.maxTouchPoints||"ontouchstart"in this}function tB(t){for(;!t.__brush;)if(!(t=t.parentNode))return;return t.__brush}var tD=e(7394),tV=e(7267);let tI={passive:!1},tL={capture:!0,passive:!1};function tH(t){t.stopImmediatePropagation()}function tU(t){t.preventDefault(),t.stopImmediatePropagation()}function tG(t){var n=t.document.documentElement,e=(0,tD.A)(t).on("dragstart.drag",tU,tL);"onselectstart"in n?e.on("selectstart.drag",tU,tL):(n.__noselect=n.style.MozUserSelect,n.style.MozUserSelect="none")}function tK(t,n){var e=t.document.documentElement,r=(0,tD.A)(t).on("dragstart.drag",null);n&&(r.on("click.drag",tU,tL),setTimeout(function(){r.on("click.drag",null)},0)),"onselectstart"in e?r.on("selectstart.drag",null):(e.style.MozUserSelect=e.__noselect,delete e.__noselect)}let tF=t=>()=>t;function tJ(t,{sourceEvent:n,subject:e,target:r,identifier:i,active:o,x:a,y:u,dx:s,dy:l,dispatch:c}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:n,enumerable:!0,configurable:!0},subject:{value:e,enumerable:!0,configurable:!0},target:{value:r,enumerable:!0,configurable:!0},identifier:{value:i,enumerable:!0,configurable:!0},active:{value:o,enumerable:!0,configurable:!0},x:{value:a,enumerable:!0,configurable:!0},y:{value:u,enumerable:!0,configurable:!0},dx:{value:s,enumerable:!0,configurable:!0},dy:{value:l,enumerable:!0,configurable:!0},_:{value:c}})}function tQ(t){return!t.ctrlKey&&!t.button}function tW(){return this.parentNode}function tZ(t,n){return null==n?{x:t.x,y:t.y}:n}function t0(){return navigator.maxTouchPoints||"ontouchstart"in this}function t1(){var t,n,e,r,i=tQ,a=tW,u=tZ,s=t0,l={},c=(0,o.A)("start","drag","end"),h=0,f=0;function p(t){t.on("mousedown.drag",d).filter(s).on("touchstart.drag",g).on("touchmove.drag",_,tI).on("touchend.drag touchcancel.drag",m).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function d(o,u){if(!r&&i.call(this,o,u)){var s=x(this,a.call(this,o,u),o,u,"mouse");s&&((0,tD.A)(o.view).on("mousemove.drag",v,tL).on("mouseup.drag",y,tL),tG(o.view),tH(o),e=!1,t=o.clientX,n=o.clientY,s("start",o))}}function v(r){if(tU(r),!e){var i=r.clientX-t,o=r.clientY-n;e=i*i+o*o>f}l.mouse("drag",r)}function y(t){(0,tD.A)(t.view).on("mousemove.drag mouseup.drag",null),tK(t.view,e),tU(t),l.mouse("end",t)}function g(t,n){if(i.call(this,t,n)){var e,r,o=t.changedTouches,u=a.call(this,t,n),s=o.length;for(e=0;e<s;++e)(r=x(this,u,t,n,o[e].identifier,o[e]))&&(tH(t),r("start",t,o[e]))}}function _(t){var n,e,r=t.changedTouches,i=r.length;for(n=0;n<i;++n)(e=l[r[n].identifier])&&(tU(t),e("drag",t,r[n]))}function m(t){var n,e,i=t.changedTouches,o=i.length;for(r&&clearTimeout(r),r=setTimeout(function(){r=null},500),n=0;n<o;++n)(e=l[i[n].identifier])&&(tH(t),e("end",t,i[n]))}function x(t,n,e,r,i,o){var a,s,f,d=c.copy(),v=(0,tV.A)(o||e,n);if(null!=(f=u.call(t,new tJ("beforestart",{sourceEvent:e,target:p,identifier:i,active:h,x:v[0],y:v[1],dx:0,dy:0,dispatch:d}),r)))return a=f.x-v[0]||0,s=f.y-v[1]||0,function e(o,u,c){var y,g=v;switch(o){case"start":l[i]=e,y=h++;break;case"end":delete l[i],--h;case"drag":v=(0,tV.A)(c||u,n),y=h}d.call(o,t,new tJ(o,{sourceEvent:u,subject:f,target:p,identifier:i,active:y,x:v[0]+a,y:v[1]+s,dx:v[0]-g[0],dy:v[1]-g[1],dispatch:d}),r)}}return p.filter=function(t){return arguments.length?(i="function"==typeof t?t:tF(!!t),p):i},p.container=function(t){return arguments.length?(a="function"==typeof t?t:tF(t),p):a},p.subject=function(t){return arguments.length?(u="function"==typeof t?t:tF(t),p):u},p.touchable=function(t){return arguments.length?(s="function"==typeof t?t:tF(!!t),p):s},p.on=function(){var t=c.on.apply(c,arguments);return t===c?p:t},p.clickDistance=function(t){return arguments.length?(f=(t*=1)*t,p):Math.sqrt(f)},p}function t2(t,n){var e,r=1;function i(){var i,o,a=e.length,u=0,s=0;for(i=0;i<a;++i)u+=(o=e[i]).x,s+=o.y;for(u=(u/a-t)*r,s=(s/a-n)*r,i=0;i<a;++i)o=e[i],o.x-=u,o.y-=s}return null==t&&(t=0),null==n&&(n=0),i.initialize=function(t){e=t},i.x=function(n){return arguments.length?(t=+n,i):t},i.y=function(t){return arguments.length?(n=+t,i):n},i.strength=function(t){return arguments.length?(r=+t,i):r},i}tJ.prototype.on=function(){var t=this._.on.apply(this._,arguments);return t===this._?this:t};var t5=e(7046);function t3(t){return function(){return t}}function t4(t){return(t()-.5)*1e-6}function t6(t){return t.x+t.vx}function t8(t){return t.y+t.vy}function t7(t){var n,e,r,i=1,o=1;function a(){for(var t,a,s,l,c,h,f,p=n.length,d=0;d<o;++d)for(t=0,a=(0,t5.A)(n,t6,t8).visitAfter(u);t<p;++t)f=(h=e[(s=n[t]).index])*h,l=s.x+s.vx,c=s.y+s.vy,a.visit(v);function v(t,n,e,o,a){var u=t.data,p=t.r,d=h+p;if(u){if(u.index>s.index){var v=l-u.x-u.vx,y=c-u.y-u.vy,g=v*v+y*y;g<d*d&&(0===v&&(g+=(v=t4(r))*v),0===y&&(g+=(y=t4(r))*y),g=(d-(g=Math.sqrt(g)))/g*i,s.vx+=(v*=g)*(d=(p*=p)/(f+p)),s.vy+=(y*=g)*d,u.vx-=v*(d=1-d),u.vy-=y*d)}return}return n>l+d||o<l-d||e>c+d||a<c-d}}function u(t){if(t.data)return t.r=e[t.data.index];for(var n=t.r=0;n<4;++n)t[n]&&t[n].r>t.r&&(t.r=t[n].r)}function s(){if(n){var r,i,o=n.length;for(r=0,e=Array(o);r<o;++r)e[(i=n[r]).index]=+t(i,r,n)}}return"function"!=typeof t&&(t=t3(null==t?1:+t)),a.initialize=function(t,e){n=t,r=e,s()},a.iterations=function(t){return arguments.length?(o=+t,a):o},a.strength=function(t){return arguments.length?(i=+t,a):i},a.radius=function(n){return arguments.length?(t="function"==typeof n?n:t3(+n),s(),a):t},a}function t9(t){return t.index}function nt(t,n){var e=t.get(n);if(!e)throw Error("node not found: "+n);return e}function nn(t){var n,e,r,i,o,a,u=t9,s=function(t){return 1/Math.min(i[t.source.index],i[t.target.index])},l=t3(30),c=1;function h(r){for(var i=0,u=t.length;i<c;++i)for(var s,l,h,f,p,d,v,y=0;y<u;++y)l=(s=t[y]).source,d=((d=Math.sqrt((f=(h=s.target).x+h.vx-l.x-l.vx||t4(a))*f+(p=h.y+h.vy-l.y-l.vy||t4(a))*p))-e[y])/d*r*n[y],f*=d,p*=d,h.vx-=f*(v=o[y]),h.vy-=p*v,l.vx+=f*(v=1-v),l.vy+=p*v}function f(){if(r){var a,s,l=r.length,c=t.length,h=new Map(r.map((t,n)=>[u(t,n,r),t]));for(a=0,i=Array(l);a<c;++a)(s=t[a]).index=a,"object"!=typeof s.source&&(s.source=nt(h,s.source)),"object"!=typeof s.target&&(s.target=nt(h,s.target)),i[s.source.index]=(i[s.source.index]||0)+1,i[s.target.index]=(i[s.target.index]||0)+1;for(a=0,o=Array(c);a<c;++a)s=t[a],o[a]=i[s.source.index]/(i[s.source.index]+i[s.target.index]);n=Array(c),p(),e=Array(c),d()}}function p(){if(r)for(var e=0,i=t.length;e<i;++e)n[e]=+s(t[e],e,t)}function d(){if(r)for(var n=0,i=t.length;n<i;++n)e[n]=+l(t[n],n,t)}return null==t&&(t=[]),h.initialize=function(t,n){r=t,a=n,f()},h.links=function(n){return arguments.length?(t=n,f(),h):t},h.id=function(t){return arguments.length?(u=t,h):u},h.iterations=function(t){return arguments.length?(c=+t,h):c},h.strength=function(t){return arguments.length?(s="function"==typeof t?t:t3(+t),p(),h):s},h.distance=function(t){return arguments.length?(l="function"==typeof t?t:t3(+t),d(),h):l},h}function ne(t){return t.x}function nr(t){return t.y}var ni=Math.PI*(3-Math.sqrt(5));function no(t){let n;var e,r=1,i=.001,u=1-Math.pow(.001,1/300),s=0,l=.6,c=new Map,h=(0,a.O1)(d),f=(0,o.A)("tick","end"),p=(n=1,()=>(n=(1664525*n+0x3c6ef35f)%0x100000000)/0x100000000);function d(){v(),f.call("tick",e),r<i&&(h.stop(),f.call("end",e))}function v(n){var i,o,a=t.length;void 0===n&&(n=1);for(var h=0;h<n;++h)for(r+=(s-r)*u,c.forEach(function(t){t(r)}),i=0;i<a;++i)null==(o=t[i]).fx?o.x+=o.vx*=l:(o.x=o.fx,o.vx=0),null==o.fy?o.y+=o.vy*=l:(o.y=o.fy,o.vy=0);return e}function y(){for(var n,e=0,r=t.length;e<r;++e){if((n=t[e]).index=e,null!=n.fx&&(n.x=n.fx),null!=n.fy&&(n.y=n.fy),isNaN(n.x)||isNaN(n.y)){var i=10*Math.sqrt(.5+e),o=e*ni;n.x=i*Math.cos(o),n.y=i*Math.sin(o)}(isNaN(n.vx)||isNaN(n.vy))&&(n.vx=n.vy=0)}}function g(n){return n.initialize&&n.initialize(t,p),n}return null==t&&(t=[]),y(),e={tick:v,restart:function(){return h.restart(d),e},stop:function(){return h.stop(),e},nodes:function(n){return arguments.length?(t=n,y(),c.forEach(g),e):t},alpha:function(t){return arguments.length?(r=+t,e):r},alphaMin:function(t){return arguments.length?(i=+t,e):i},alphaDecay:function(t){return arguments.length?(u=+t,e):+u},alphaTarget:function(t){return arguments.length?(s=+t,e):s},velocityDecay:function(t){return arguments.length?(l=1-t,e):1-l},randomSource:function(t){return arguments.length?(p=t,c.forEach(g),e):p},force:function(t,n){return arguments.length>1?(null==n?c.delete(t):c.set(t,g(n)),e):c.get(t)},find:function(n,e,r){var i,o,a,u,s,l=0,c=t.length;for(null==r?r=1/0:r*=r,l=0;l<c;++l)(a=(i=n-(u=t[l]).x)*i+(o=e-u.y)*o)<r&&(s=u,r=a);return s},on:function(t,n){return arguments.length>1?(f.on(t,n),e):f.on(t)}}}function na(){var t,n,e,r,i,o=t3(-30),a=1,u=1/0,s=.81;function l(e){var i,o=t.length,a=(0,t5.A)(t,ne,nr).visitAfter(h);for(r=e,i=0;i<o;++i)n=t[i],a.visit(f)}function c(){if(t){var n,e,r=t.length;for(n=0,i=Array(r);n<r;++n)i[(e=t[n]).index]=+o(e,n,t)}}function h(t){var n,e,r,o,a,u=0,s=0;if(t.length){for(r=o=a=0;a<4;++a)(n=t[a])&&(e=Math.abs(n.value))&&(u+=n.value,s+=e,r+=e*n.x,o+=e*n.y);t.x=r/s,t.y=o/s}else{(n=t).x=n.data.x,n.y=n.data.y;do u+=i[n.data.index];while(n=n.next)}t.value=u}function f(t,o,l,c){if(!t.value)return!0;var h=t.x-n.x,f=t.y-n.y,p=c-o,d=h*h+f*f;if(p*p/s<d)return d<u&&(0===h&&(d+=(h=t4(e))*h),0===f&&(d+=(f=t4(e))*f),d<a&&(d=Math.sqrt(a*d)),n.vx+=h*t.value*r/d,n.vy+=f*t.value*r/d),!0;if(!t.length&&!(d>=u)){(t.data!==n||t.next)&&(0===h&&(d+=(h=t4(e))*h),0===f&&(d+=(f=t4(e))*f),d<a&&(d=Math.sqrt(a*d)));do t.data!==n&&(p=i[t.data.index]*r/d,n.vx+=h*p,n.vy+=f*p);while(t=t.next)}}return l.initialize=function(n,r){t=n,e=r,c()},l.strength=function(t){return arguments.length?(o="function"==typeof t?t:t3(+t),c(),l):o},l.distanceMin=function(t){return arguments.length?(a=t*t,l):Math.sqrt(a)},l.distanceMax=function(t){return arguments.length?(u=t*t,l):Math.sqrt(u)},l.theta=function(t){return arguments.length?(s=t*t,l):Math.sqrt(s)},l}function nu(t){return((t=Math.exp(t))+1/t)/2}let ns=function t(n,e,r){function i(t,i){var o,a,u=t[0],s=t[1],l=t[2],c=i[0],h=i[1],f=i[2],p=c-u,d=h-s,v=p*p+d*d;if(v<1e-12)a=Math.log(f/l)/n,o=function(t){return[u+t*p,s+t*d,l*Math.exp(n*t*a)]};else{var y=Math.sqrt(v),g=(f*f-l*l+r*v)/(2*l*e*y),_=(f*f-l*l-r*v)/(2*f*e*y),m=Math.log(Math.sqrt(g*g+1)-g);a=(Math.log(Math.sqrt(_*_+1)-_)-m)/n,o=function(t){var r,i,o=t*a,c=nu(m),h=l/(e*y)*(c*(((r=Math.exp(2*(r=n*o+m)))-1)/(r+1))-((i=Math.exp(i=m))-1/i)/2);return[u+h*p,s+h*d,l*c/nu(n*o+m)]}}return o.duration=1e3*a*n/Math.SQRT2,o}return i.rho=function(n){var e=Math.max(.001,+n),r=e*e;return t(e,r,r*r)},i}(Math.SQRT2,2,4),nl=t=>()=>t;function nc(t,{sourceEvent:n,target:e,transform:r,dispatch:i}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:n,enumerable:!0,configurable:!0},target:{value:e,enumerable:!0,configurable:!0},transform:{value:r,enumerable:!0,configurable:!0},_:{value:i}})}function nh(t,n,e){this.k=t,this.x=n,this.y=e}nh.prototype={constructor:nh,scale:function(t){return 1===t?this:new nh(this.k*t,this.x,this.y)},translate:function(t,n){return 0===t&0===n?this:new nh(this.k,this.x+this.k*t,this.y+this.k*n)},apply:function(t){return[t[0]*this.k+this.x,t[1]*this.k+this.y]},applyX:function(t){return t*this.k+this.x},applyY:function(t){return t*this.k+this.y},invert:function(t){return[(t[0]-this.x)/this.k,(t[1]-this.y)/this.k]},invertX:function(t){return(t-this.x)/this.k},invertY:function(t){return(t-this.y)/this.k},rescaleX:function(t){return t.copy().domain(t.range().map(this.invertX,this).map(t.invert,t))},rescaleY:function(t){return t.copy().domain(t.range().map(this.invertY,this).map(t.invert,t))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var nf=new nh(1,0,0);function np(t){t.stopImmediatePropagation()}function nd(t){t.preventDefault(),t.stopImmediatePropagation()}function nv(t){return(!t.ctrlKey||"wheel"===t.type)&&!t.button}function ny(){var t=this;return t instanceof SVGElement?(t=t.ownerSVGElement||t).hasAttribute("viewBox")?[[(t=t.viewBox.baseVal).x,t.y],[t.x+t.width,t.y+t.height]]:[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]:[[0,0],[t.clientWidth,t.clientHeight]]}function ng(){return this.__zoom||nf}function n_(t){return-t.deltaY*(1===t.deltaMode?.05:t.deltaMode?1:.002)*(t.ctrlKey?10:1)}function nm(){return navigator.maxTouchPoints||"ontouchstart"in this}function nx(t,n,e){var r=t.invertX(n[0][0])-e[0][0],i=t.invertX(n[1][0])-e[1][0],o=t.invertY(n[0][1])-e[0][1],a=t.invertY(n[1][1])-e[1][1];return t.translate(i>r?(r+i)/2:Math.min(0,r)||Math.max(0,i),a>o?(o+a)/2:Math.min(0,o)||Math.max(0,a))}function nw(){var t,n,e,r=nv,i=ny,a=nx,u=n_,s=nm,l=[0,1/0],c=[[-1/0,-1/0],[1/0,1/0]],h=250,f=ns,p=(0,o.A)("start","zoom","end"),v=0,y=10;function g(t){t.property("__zoom",ng).on("wheel.zoom",k,{passive:!1}).on("mousedown.zoom",N).on("dblclick.zoom",M).filter(s).on("touchstart.zoom",z).on("touchmove.zoom",E).on("touchend.zoom touchcancel.zoom",$).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function _(t,n){return(n=Math.max(l[0],Math.min(l[1],n)))===t.k?t:new nh(n,t.x,t.y)}function m(t,n,e){var r=n[0]-e[0]*t.k,i=n[1]-e[1]*t.k;return r===t.x&&i===t.y?t:new nh(t.k,r,i)}function x(t){return[(+t[0][0]+ +t[1][0])/2,(+t[0][1]+ +t[1][1])/2]}function w(t,n,e,r){t.on("start.zoom",function(){b(this,arguments).event(r).start()}).on("interrupt.zoom end.zoom",function(){b(this,arguments).event(r).end()}).tween("zoom",function(){var t=arguments,o=b(this,t).event(r),a=i.apply(this,t),u=null==e?x(a):"function"==typeof e?e.apply(this,t):e,s=Math.max(a[1][0]-a[0][0],a[1][1]-a[0][1]),l=this.__zoom,c="function"==typeof n?n.apply(this,t):n,h=f(l.invert(u).concat(s/l.k),c.invert(u).concat(s/c.k));return function(t){if(1===t)t=c;else{var n=h(t),e=s/n[2];t=new nh(e,u[0]-n[0]*e,u[1]-n[1]*e)}o.zoom(null,t)}})}function b(t,n,e){return!e&&t.__zooming||new A(t,n)}function A(t,n){this.that=t,this.args=n,this.active=0,this.sourceEvent=null,this.extent=i.apply(t,n),this.taps=0}function k(t,...n){if(r.apply(this,arguments)){var e=b(this,n).event(t),i=this.__zoom,o=Math.max(l[0],Math.min(l[1],i.k*Math.pow(2,u.apply(this,arguments)))),s=(0,tV.A)(t);if(e.wheel)(e.mouse[0][0]!==s[0]||e.mouse[0][1]!==s[1])&&(e.mouse[1]=i.invert(e.mouse[0]=s)),clearTimeout(e.wheel);else{if(i.k===o)return;e.mouse=[s,i.invert(s)],d(this),e.start()}nd(t),e.wheel=setTimeout(function(){e.wheel=null,e.end()},150),e.zoom("mouse",a(m(_(i,o),e.mouse[0],e.mouse[1]),e.extent,c))}}function N(t,...n){if(!e&&r.apply(this,arguments)){var i=t.currentTarget,o=b(this,n,!0).event(t),u=(0,tD.A)(t.view).on("mousemove.zoom",function(t){if(nd(t),!o.moved){var n=t.clientX-l,e=t.clientY-h;o.moved=n*n+e*e>v}o.event(t).zoom("mouse",a(m(o.that.__zoom,o.mouse[0]=(0,tV.A)(t,i),o.mouse[1]),o.extent,c))},!0).on("mouseup.zoom",function(t){u.on("mousemove.zoom mouseup.zoom",null),tK(t.view,o.moved),nd(t),o.event(t).end()},!0),s=(0,tV.A)(t,i),l=t.clientX,h=t.clientY;tG(t.view),np(t),o.mouse=[s,this.__zoom.invert(s)],d(this),o.start()}}function M(t,...n){if(r.apply(this,arguments)){var e=this.__zoom,o=(0,tV.A)(t.changedTouches?t.changedTouches[0]:t,this),u=e.invert(o),s=e.k*(t.shiftKey?.5:2),l=a(m(_(e,s),o,u),i.apply(this,n),c);nd(t),h>0?(0,tD.A)(this).transition().duration(h).call(w,l,o,t):(0,tD.A)(this).call(g.transform,l,o,t)}}function z(e,...i){if(r.apply(this,arguments)){var o,a,u,s,l=e.touches,c=l.length,h=b(this,i,e.changedTouches.length===c).event(e);for(np(e),a=0;a<c;++a)u=l[a],s=[s=(0,tV.A)(u,this),this.__zoom.invert(s),u.identifier],h.touch0?h.touch1||h.touch0[2]===s[2]||(h.touch1=s,h.taps=0):(h.touch0=s,o=!0,h.taps=1+!!t);t&&(t=clearTimeout(t)),o&&(h.taps<2&&(n=s[0],t=setTimeout(function(){t=null},500)),d(this),h.start())}}function E(t,...n){if(this.__zooming){var e,r,i,o,u=b(this,n).event(t),s=t.changedTouches,l=s.length;for(nd(t),e=0;e<l;++e)r=s[e],i=(0,tV.A)(r,this),u.touch0&&u.touch0[2]===r.identifier?u.touch0[0]=i:u.touch1&&u.touch1[2]===r.identifier&&(u.touch1[0]=i);if(r=u.that.__zoom,u.touch1){var h=u.touch0[0],f=u.touch0[1],p=u.touch1[0],d=u.touch1[1],v=(v=p[0]-h[0])*v+(v=p[1]-h[1])*v,y=(y=d[0]-f[0])*y+(y=d[1]-f[1])*y;r=_(r,Math.sqrt(v/y)),i=[(h[0]+p[0])/2,(h[1]+p[1])/2],o=[(f[0]+d[0])/2,(f[1]+d[1])/2]}else{if(!u.touch0)return;i=u.touch0[0],o=u.touch0[1]}u.zoom("touch",a(m(r,i,o),u.extent,c))}}function $(t,...r){if(this.__zooming){var i,o,a=b(this,r).event(t),u=t.changedTouches,s=u.length;for(np(t),e&&clearTimeout(e),e=setTimeout(function(){e=null},500),i=0;i<s;++i)o=u[i],a.touch0&&a.touch0[2]===o.identifier?delete a.touch0:a.touch1&&a.touch1[2]===o.identifier&&delete a.touch1;if(a.touch1&&!a.touch0&&(a.touch0=a.touch1,delete a.touch1),a.touch0)a.touch0[1]=this.__zoom.invert(a.touch0[0]);else if(a.end(),2===a.taps&&(o=(0,tV.A)(o,this),Math.hypot(n[0]-o[0],n[1]-o[1])<y)){var l=(0,tD.A)(this).on("dblclick.zoom");l&&l.apply(this,arguments)}}}return g.transform=function(t,n,e,r){var i=t.selection?t.selection():t;i.property("__zoom",ng),t!==i?w(t,n,e,r):i.interrupt().each(function(){b(this,arguments).event(r).start().zoom(null,"function"==typeof n?n.apply(this,arguments):n).end()})},g.scaleBy=function(t,n,e,r){g.scaleTo(t,function(){var t=this.__zoom.k,e="function"==typeof n?n.apply(this,arguments):n;return t*e},e,r)},g.scaleTo=function(t,n,e,r){g.transform(t,function(){var t=i.apply(this,arguments),r=this.__zoom,o=null==e?x(t):"function"==typeof e?e.apply(this,arguments):e,u=r.invert(o),s="function"==typeof n?n.apply(this,arguments):n;return a(m(_(r,s),o,u),t,c)},e,r)},g.translateBy=function(t,n,e,r){g.transform(t,function(){return a(this.__zoom.translate("function"==typeof n?n.apply(this,arguments):n,"function"==typeof e?e.apply(this,arguments):e),i.apply(this,arguments),c)},null,r)},g.translateTo=function(t,n,e,r,o){g.transform(t,function(){var t=i.apply(this,arguments),o=this.__zoom,u=null==r?x(t):"function"==typeof r?r.apply(this,arguments):r;return a(nf.translate(u[0],u[1]).scale(o.k).translate("function"==typeof n?-n.apply(this,arguments):-n,"function"==typeof e?-e.apply(this,arguments):-e),t,c)},r,o)},A.prototype={event:function(t){return t&&(this.sourceEvent=t),this},start:function(){return 1==++this.active&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(t,n){return this.mouse&&"mouse"!==t&&(this.mouse[1]=n.invert(this.mouse[0])),this.touch0&&"touch"!==t&&(this.touch0[1]=n.invert(this.touch0[0])),this.touch1&&"touch"!==t&&(this.touch1[1]=n.invert(this.touch1[0])),this.that.__zoom=n,this.emit("zoom"),this},end:function(){return 0==--this.active&&(delete this.that.__zooming,this.emit("end")),this},emit:function(t){var n=(0,tD.A)(this.that).datum();p.call(t,this.that,new nc(t,{sourceEvent:this.sourceEvent,target:g,type:t,transform:this.that.__zoom,dispatch:p}),n)}},g.wheelDelta=function(t){return arguments.length?(u="function"==typeof t?t:nl(+t),g):u},g.filter=function(t){return arguments.length?(r="function"==typeof t?t:nl(!!t),g):r},g.touchable=function(t){return arguments.length?(s="function"==typeof t?t:nl(!!t),g):s},g.extent=function(t){return arguments.length?(i="function"==typeof t?t:nl([[+t[0][0],+t[0][1]],[+t[1][0],+t[1][1]]]),g):i},g.scaleExtent=function(t){return arguments.length?(l[0]=+t[0],l[1]=+t[1],g):[l[0],l[1]]},g.translateExtent=function(t){return arguments.length?(c[0][0]=+t[0][0],c[1][0]=+t[1][0],c[0][1]=+t[0][1],c[1][1]=+t[1][1],g):[[c[0][0],c[0][1]],[c[1][0],c[1][1]]]},g.constrain=function(t){return arguments.length?(a=t,g):a},g.duration=function(t){return arguments.length?(h=+t,g):h},g.interpolate=function(t){return arguments.length?(f=t,g):f},g.on=function(){var t=p.on.apply(p,arguments);return t===p?g:t},g.clickDistance=function(t){return arguments.length?(v=(t*=1)*t,g):Math.sqrt(v)},g.tapDistance=function(t){return arguments.length?(y=+t,g):y},g}nh.prototype},4604:(t,n,e)=>{e.d(n,{M4:()=>v,O1:()=>y,tB:()=>p});var r,i,o=0,a=0,u=0,s=0,l=0,c=0,h="object"==typeof performance&&performance.now?performance:Date,f="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function p(){return l||(f(d),l=h.now()+c)}function d(){l=0}function v(){this._call=this._time=this._next=null}function y(t,n,e){var r=new v;return r.restart(t,n,e),r}function g(){l=(s=h.now())+c,o=a=0;try{p(),++o;for(var t,n=r;n;)(t=l-n._time)>=0&&n._call.call(void 0,t),n=n._next;--o}finally{o=0,function(){for(var t,n,e=r,o=1/0;e;)e._call?(o>e._time&&(o=e._time),t=e,e=e._next):(n=e._next,e._next=null,e=t?t._next=n:r=n);i=t,m(o)}(),l=0}}function _(){var t=h.now(),n=t-s;n>1e3&&(c-=n,s=t)}function m(t){!o&&(a&&(a=clearTimeout(a)),t-l>24?(t<1/0&&(a=setTimeout(g,t-h.now()-c)),u&&(u=clearInterval(u))):(u||(s=h.now(),u=setInterval(_,1e3)),o=1,f(g)))}v.prototype=y.prototype={constructor:v,restart:function(t,n,e){if("function"!=typeof t)throw TypeError("callback is not a function");e=(null==e?p():+e)+(null==n?0:+n),this._next||i===this||(i?i._next=this:r=this,i=this),this._call=t,this._time=e,m()},stop:function(){this._call&&(this._call=null,this._time=1/0,m())}}},6078:(t,n,e)=>{e.d(n,{A:()=>i,g:()=>r});var r="http://www.w3.org/1999/xhtml";let i={svg:"http://www.w3.org/2000/svg",xhtml:r,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"}},7033:(t,n,e)=>{e.d(n,{A:()=>i});var r=e(6078);function i(t){var n=t+="",e=n.indexOf(":");return e>=0&&"xmlns"!==(n=t.slice(0,e))&&(t=t.slice(e+1)),r.A.hasOwnProperty(n)?{space:r.A[n],local:t}:t}},7046:(t,n,e)=>{function r(t,n,e,r){if(isNaN(n)||isNaN(e))return t;var i,o,a,u,s,l,c,h,f,p=t._root,d={data:r},v=t._x0,y=t._y0,g=t._x1,_=t._y1;if(!p)return t._root=d,t;for(;p.length;)if((l=n>=(o=(v+g)/2))?v=o:g=o,(c=e>=(a=(y+_)/2))?y=a:_=a,i=p,!(p=p[h=c<<1|l]))return i[h]=d,t;if(u=+t._x.call(null,p.data),s=+t._y.call(null,p.data),n===u&&e===s)return d.next=p,i?i[h]=d:t._root=d,t;do i=i?i[h]=[,,,,]:t._root=[,,,,],(l=n>=(o=(v+g)/2))?v=o:g=o,(c=e>=(a=(y+_)/2))?y=a:_=a;while((h=c<<1|l)==(f=(s>=a)<<1|u>=o));return i[f]=p,i[h]=d,t}function i(t,n,e,r,i){this.node=t,this.x0=n,this.y0=e,this.x1=r,this.y1=i}function o(t){return t[0]}function a(t){return t[1]}function u(t,n,e){var r=new s(null==n?o:n,null==e?a:e,NaN,NaN,NaN,NaN);return null==t?r:r.addAll(t)}function s(t,n,e,r,i,o){this._x=t,this._y=n,this._x0=e,this._y0=r,this._x1=i,this._y1=o,this._root=void 0}function l(t){for(var n={data:t.data},e=n;t=t.next;)e=e.next={data:t.data};return n}e.d(n,{A:()=>u});var c=u.prototype=s.prototype;c.copy=function(){var t,n,e=new s(this._x,this._y,this._x0,this._y0,this._x1,this._y1),r=this._root;if(!r)return e;if(!r.length)return e._root=l(r),e;for(t=[{source:r,target:e._root=[,,,,]}];r=t.pop();)for(var i=0;i<4;++i)(n=r.source[i])&&(n.length?t.push({source:n,target:r.target[i]=[,,,,]}):r.target[i]=l(n));return e},c.add=function(t){let n=+this._x.call(null,t),e=+this._y.call(null,t);return r(this.cover(n,e),n,e,t)},c.addAll=function(t){var n,e,i,o,a=t.length,u=Array(a),s=Array(a),l=1/0,c=1/0,h=-1/0,f=-1/0;for(e=0;e<a;++e)!(isNaN(i=+this._x.call(null,n=t[e]))||isNaN(o=+this._y.call(null,n)))&&(u[e]=i,s[e]=o,i<l&&(l=i),i>h&&(h=i),o<c&&(c=o),o>f&&(f=o));if(l>h||c>f)return this;for(this.cover(l,c).cover(h,f),e=0;e<a;++e)r(this,u[e],s[e],t[e]);return this},c.cover=function(t,n){if(isNaN(t*=1)||isNaN(n*=1))return this;var e=this._x0,r=this._y0,i=this._x1,o=this._y1;if(isNaN(e))i=(e=Math.floor(t))+1,o=(r=Math.floor(n))+1;else{for(var a,u,s=i-e||1,l=this._root;e>t||t>=i||r>n||n>=o;)switch(u=(n<r)<<1|t<e,(a=[,,,,])[u]=l,l=a,s*=2,u){case 0:i=e+s,o=r+s;break;case 1:e=i-s,o=r+s;break;case 2:i=e+s,r=o-s;break;case 3:e=i-s,r=o-s}this._root&&this._root.length&&(this._root=l)}return this._x0=e,this._y0=r,this._x1=i,this._y1=o,this},c.data=function(){var t=[];return this.visit(function(n){if(!n.length)do t.push(n.data);while(n=n.next)}),t},c.extent=function(t){return arguments.length?this.cover(+t[0][0],+t[0][1]).cover(+t[1][0],+t[1][1]):isNaN(this._x0)?void 0:[[this._x0,this._y0],[this._x1,this._y1]]},c.find=function(t,n,e){var r,o,a,u,s,l,c,h=this._x0,f=this._y0,p=this._x1,d=this._y1,v=[],y=this._root;for(y&&v.push(new i(y,h,f,p,d)),null==e?e=1/0:(h=t-e,f=n-e,p=t+e,d=n+e,e*=e);l=v.pop();)if((y=l.node)&&!((o=l.x0)>p)&&!((a=l.y0)>d)&&!((u=l.x1)<h)&&!((s=l.y1)<f))if(y.length){var g=(o+u)/2,_=(a+s)/2;v.push(new i(y[3],g,_,u,s),new i(y[2],o,_,g,s),new i(y[1],g,a,u,_),new i(y[0],o,a,g,_)),(c=(n>=_)<<1|t>=g)&&(l=v[v.length-1],v[v.length-1]=v[v.length-1-c],v[v.length-1-c]=l)}else{var m=t-this._x.call(null,y.data),x=n-this._y.call(null,y.data),w=m*m+x*x;if(w<e){var b=Math.sqrt(e=w);h=t-b,f=n-b,p=t+b,d=n+b,r=y.data}}return r},c.remove=function(t){if(isNaN(o=+this._x.call(null,t))||isNaN(a=+this._y.call(null,t)))return this;var n,e,r,i,o,a,u,s,l,c,h,f,p=this._root,d=this._x0,v=this._y0,y=this._x1,g=this._y1;if(!p)return this;if(p.length)for(;;){if((l=o>=(u=(d+y)/2))?d=u:y=u,(c=a>=(s=(v+g)/2))?v=s:g=s,n=p,!(p=p[h=c<<1|l]))return this;if(!p.length)break;(n[h+1&3]||n[h+2&3]||n[h+3&3])&&(e=n,f=h)}for(;p.data!==t;)if(r=p,!(p=p.next))return this;return((i=p.next)&&delete p.next,r)?i?r.next=i:delete r.next:n?(i?n[h]=i:delete n[h],(p=n[0]||n[1]||n[2]||n[3])&&p===(n[3]||n[2]||n[1]||n[0])&&!p.length&&(e?e[f]=p:this._root=p)):this._root=i,this},c.removeAll=function(t){for(var n=0,e=t.length;n<e;++n)this.remove(t[n]);return this},c.root=function(){return this._root},c.size=function(){var t=0;return this.visit(function(n){if(!n.length)do++t;while(n=n.next)}),t},c.visit=function(t){var n,e,r,o,a,u,s=[],l=this._root;for(l&&s.push(new i(l,this._x0,this._y0,this._x1,this._y1));n=s.pop();)if(!t(l=n.node,r=n.x0,o=n.y0,a=n.x1,u=n.y1)&&l.length){var c=(r+a)/2,h=(o+u)/2;(e=l[3])&&s.push(new i(e,c,h,a,u)),(e=l[2])&&s.push(new i(e,r,h,c,u)),(e=l[1])&&s.push(new i(e,c,o,a,h)),(e=l[0])&&s.push(new i(e,r,o,c,h))}return this},c.visitAfter=function(t){var n,e=[],r=[];for(this._root&&e.push(new i(this._root,this._x0,this._y0,this._x1,this._y1));n=e.pop();){var o=n.node;if(o.length){var a,u=n.x0,s=n.y0,l=n.x1,c=n.y1,h=(u+l)/2,f=(s+c)/2;(a=o[0])&&e.push(new i(a,u,s,h,f)),(a=o[1])&&e.push(new i(a,h,s,l,f)),(a=o[2])&&e.push(new i(a,u,f,h,c)),(a=o[3])&&e.push(new i(a,h,f,l,c))}r.push(n)}for(;n=r.pop();)t(n.node,n.x0,n.y0,n.x1,n.y1);return this},c.x=function(t){return arguments.length?(this._x=t,this):this._x},c.y=function(t){return arguments.length?(this._y=t,this):this._y}},7267:(t,n,e)=>{e.d(n,{A:()=>r});function r(t,n){if(t=function(t){let n;for(;n=t.sourceEvent;)t=n;return t}(t),void 0===n&&(n=t.currentTarget),n){var e=n.ownerSVGElement||n;if(e.createSVGPoint){var r=e.createSVGPoint();return r.x=t.clientX,r.y=t.clientY,[(r=r.matrixTransform(n.getScreenCTM().inverse())).x,r.y]}if(n.getBoundingClientRect){var i=n.getBoundingClientRect();return[t.clientX-i.left-n.clientLeft,t.clientY-i.top-n.clientTop]}}return[t.pageX,t.pageY]}},7394:(t,n,e)=>{e.d(n,{A:()=>i});var r=e(3177);function i(t){return"string"==typeof t?new r.LN([[document.querySelector(t)]],[document.documentElement]):new r.LN([[t]],r.zr)}},9438:(t,n,e)=>{function r(){return[]}function i(t){return null==t?r:function(){return this.querySelectorAll(t)}}e.d(n,{A:()=>i})}}]);