"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[41],{1467:(e,t,a)=>{a.d(t,{A:()=>i});var r=a(7876),s=a(4232),n=a(4274);function i(e){let{data:t}=e,a=(0,s.useRef)(null);return(0,s.useEffect)(()=>{var e;if(!t||!a.current)return;n.Ltv(a.current).selectAll("*").remove();let r=n.Ltv(a.current).attr("width",800).attr("height",600).attr("viewBox",[0,0,800,600]),s=n.tXi(t.nodes).force("link",n.kJC(t.links).id(e=>e.id)).force("charge",n.xJS().strength(-400)).force("center",n.jTM(400,300)),i=r.append("g").selectAll("line").data(t.links).join("line").attr("stroke","#999").attr("stroke-opacity",.6).attr("stroke-width",e=>Math.sqrt(e.value||1)),c=r.append("g").selectAll("circle").data(t.nodes).join("circle").attr("r",e=>e.size||5).attr("fill",e=>{switch(e.type){case"jobSeeker":return"#3b82f6";case"company":return"#14b8a6";case"position":return"#10b981";case"skill":return"#f59e0b";default:return"#6366f1"}}).call((e=s,n.$Er().on("start",function(t){t.active||e.alphaTarget(.3).restart(),t.subject.fx=t.subject.x,t.subject.fy=t.subject.y}).on("drag",function(e){e.subject.fx=e.x,e.subject.fy=e.y}).on("end",function(t){t.active||e.alphaTarget(0),t.subject.fx=null,t.subject.fy=null})));return c.append("title").text(e=>e.name),s.on("tick",()=>{i.attr("x1",e=>e.source.x).attr("y1",e=>e.source.y).attr("x2",e=>e.target.x).attr("y2",e=>e.target.y),c.attr("cx",e=>e.x).attr("cy",e=>e.y)}),()=>{s.stop()}},[t]),(0,r.jsx)("div",{className:"border rounded-lg shadow-sm bg-white p-4",children:(0,r.jsx)("svg",{ref:a,className:"w-full h-full"})})}},6234:(e,t,a)=>{a.d(t,{A:()=>o});var r=a(7876),s=a(8230),n=a.n(s),i=a(9099),c=a(4232);function l(){let e=(0,i.useRouter)(),[t,a]=(0,c.useState)(!1),s=[{name:"Dashboard",path:"/",icon:"\uD83C\uDFE0"},{name:"Authority Matches",path:"/matches",icon:"\uD83C\uDFAF"},{name:"Job Seekers",path:"/job-seekers",icon:"\uD83D\uDC65"},{name:"Hiring Authorities",path:"/hiring-authorities",icon:"\uD83D\uDC54"},{name:"Companies",path:"/companies",icon:"\uD83C\uDFE2"},{name:"Positions",path:"/positions",icon:"\uD83D\uDCCB"},{name:"Skills",path:"/skills",icon:"\uD83D\uDEE0️"},{name:"Visualizations",path:"/visualizations",icon:"\uD83D\uDCCA"},{name:"Network View",path:"/global-view",icon:"\uD83C\uDF10"}];return(0,r.jsx)("nav",{className:"bg-white shadow-soft border-b border-candid-gray-200",children:(0,r.jsxs)("div",{className:"container-app",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center h-20",children:[(0,r.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,r.jsxs)(n(),{href:"/",className:"flex items-center space-x-3 group",children:[(0,r.jsx)("div",{className:"w-12 h-12 relative group-hover:scale-105 transition-transform duration-200",children:(0,r.jsxs)("svg",{viewBox:"0 0 48 48",className:"w-full h-full",children:[(0,r.jsx)("circle",{cx:"24",cy:"24",r:"22",fill:"none",stroke:"#1e3a8a",strokeWidth:"2"}),(0,r.jsx)("circle",{cx:"24",cy:"12",r:"3",fill:"#00d4ff"}),(0,r.jsx)("circle",{cx:"36",cy:"24",r:"3",fill:"#00d4ff"}),(0,r.jsx)("circle",{cx:"24",cy:"36",r:"3",fill:"#00d4ff"}),(0,r.jsx)("circle",{cx:"12",cy:"24",r:"3",fill:"#00d4ff"}),(0,r.jsx)("circle",{cx:"24",cy:"24",r:"4",fill:"#1e3a8a"}),(0,r.jsx)("line",{x1:"24",y1:"15",x2:"24",y2:"20",stroke:"#00d4ff",strokeWidth:"2"}),(0,r.jsx)("line",{x1:"33",y1:"24",x2:"28",y2:"24",stroke:"#00d4ff",strokeWidth:"2"}),(0,r.jsx)("line",{x1:"24",y1:"33",x2:"24",y2:"28",stroke:"#00d4ff",strokeWidth:"2"}),(0,r.jsx)("line",{x1:"15",y1:"24",x2:"20",y2:"24",stroke:"#00d4ff",strokeWidth:"2"})]})}),(0,r.jsxs)("div",{className:"hidden sm:block",children:[(0,r.jsx)("h1",{className:"text-xl font-bold text-secondary-800 group-hover:text-primary-500 transition-colors duration-200",children:"Candid Connections"}),(0,r.jsx)("p",{className:"text-sm text-candid-gray-600 -mt-1",children:"Katra Platform"})]})]})}),(0,r.jsx)("div",{className:"hidden lg:block",children:(0,r.jsx)("div",{className:"flex items-center space-x-1",children:s.map(t=>(0,r.jsxs)(n(),{href:t.path,className:"flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ".concat(e.pathname===t.path?"nav-link-active":"nav-link"),children:[(0,r.jsx)("span",{className:"text-base",children:t.icon}),(0,r.jsx)("span",{children:t.name})]},t.path))})}),(0,r.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[(0,r.jsx)(n(),{href:"/admin",className:"btn-outline text-sm py-2 px-4",children:"⚙️ Admin"}),(0,r.jsx)(n(),{href:"/global-view",className:"btn-outline text-sm py-2 px-4",children:"\uD83C\uDF10 Network View"}),(0,r.jsx)(n(),{href:"https://portal.candid-connections.com/user/login",target:"_blank",rel:"noopener noreferrer",className:"btn-primary text-sm py-2 px-4",children:"Portal Login"})]}),(0,r.jsx)("div",{className:"lg:hidden",children:(0,r.jsx)("button",{onClick:()=>a(!t),className:"p-2 rounded-lg text-candid-navy-600 hover:text-primary-600 hover:bg-primary-50 transition-colors duration-200","aria-label":"Toggle mobile menu",children:(0,r.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:t?(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})})]}),t&&(0,r.jsx)("div",{className:"lg:hidden border-t border-candid-gray-200 py-4",children:(0,r.jsxs)("div",{className:"space-y-2",children:[s.map(t=>(0,r.jsxs)(n(),{href:t.path,onClick:()=>a(!1),className:"flex items-center space-x-3 px-4 py-3 rounded-lg text-base font-medium transition-all duration-200 ".concat(e.pathname===t.path?"nav-link-active":"nav-link"),children:[(0,r.jsx)("span",{className:"text-lg",children:t.icon}),(0,r.jsx)("span",{children:t.name})]},t.path)),(0,r.jsxs)("div",{className:"pt-4 space-y-2",children:[(0,r.jsx)(n(),{href:"/global-view",onClick:()=>a(!1),className:"block w-full btn-outline text-center",children:"\uD83C\uDF10 Network View"}),(0,r.jsx)(n(),{href:"https://portal.candid-connections.com/user/login",target:"_blank",rel:"noopener noreferrer",className:"block w-full btn-primary text-center",children:"Portal Login"})]})]})})]})})}function o(e){let{children:t}=e;return(0,r.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,r.jsx)(l,{}),(0,r.jsx)("main",{className:"container-app section-padding",children:t})]})}},8512:(e,t,a)=>{a.d(t,{_:()=>r});let r=()=>{let e=Array.from({length:5},(e,t)=>({id:"jobSeekers/".concat(t),name:"Job Seeker ".concat(t+1),type:"jobSeeker",size:8})),t=Array.from({length:3},(e,t)=>({id:"companies/".concat(t),name:"Company ".concat(t+1),type:"company",size:10})),a=Array.from({length:4},(e,t)=>({id:"positions/".concat(t),name:"Position ".concat(t+1),type:"position",size:9})),r=Array.from({length:8},(e,t)=>({id:"skills/".concat(t),name:"Skill ".concat(t+1),type:"skill",size:6})),s=[...e,...t,...a,...r],n=[];return e.forEach((e,a)=>{n.push({source:e.id,target:t[a%t.length].id,type:"works_for",value:1})}),t.forEach((e,t)=>{a.forEach((a,r)=>{(t+r)%2==0&&n.push({source:e.id,target:a.id,type:"posts",value:1})})}),e.forEach(e=>{let t=2+Math.floor(3*Math.random());[...r].sort(()=>.5-Math.random()).slice(0,t).forEach(t=>{n.push({source:e.id,target:t.id,type:"has_skill",value:1})})}),a.forEach(e=>{let t=2+Math.floor(2*Math.random());[...r].sort(()=>.5-Math.random()).slice(0,t).forEach(t=>{n.push({source:e.id,target:t.id,type:"requires",value:1})})}),{nodes:s,links:n}}}}]);