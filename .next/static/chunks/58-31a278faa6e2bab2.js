"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[58],{1467:(e,t,a)=>{a.d(t,{A:()=>i});var n=a(7876),s=a(4232),r=a(7070);function i(e){let{data:t}=e,a=(0,s.useRef)(null);return(0,s.useEffect)(()=>{var e;if(!t||!a.current)return;r.Ltv(a.current).selectAll("*").remove();let n=r.Ltv(a.current).attr("width",800).attr("height",600).attr("viewBox",[0,0,800,600]),s=r.tXi(t.nodes).force("link",r.kJC(t.links).id(e=>e.id)).force("charge",r.xJS().strength(-400)).force("center",r.jTM(400,300)),i=n.append("g").selectAll("line").data(t.links).join("line").attr("stroke","#999").attr("stroke-opacity",.6).attr("stroke-width",e=>Math.sqrt(e.value||1)),l=n.append("g").selectAll("circle").data(t.nodes).join("circle").attr("r",e=>e.size||5).attr("fill",e=>{switch(e.type){case"jobSeeker":return"#3b82f6";case"company":return"#14b8a6";case"position":return"#10b981";case"skill":return"#f59e0b";default:return"#6366f1"}}).call((e=s,r.$Er().on("start",function(t){t.active||e.alphaTarget(.3).restart(),t.subject.fx=t.subject.x,t.subject.fy=t.subject.y}).on("drag",function(e){e.subject.fx=e.x,e.subject.fy=e.y}).on("end",function(t){t.active||e.alphaTarget(0),t.subject.fx=null,t.subject.fy=null})));return l.append("title").text(e=>e.name),s.on("tick",()=>{i.attr("x1",e=>e.source.x).attr("y1",e=>e.source.y).attr("x2",e=>e.target.x).attr("y2",e=>e.target.y),l.attr("cx",e=>e.x).attr("cy",e=>e.y)}),()=>{s.stop()}},[t]),(0,n.jsx)("div",{className:"border rounded-lg shadow-sm bg-white p-4",children:(0,n.jsx)("svg",{ref:a,className:"w-full h-full"})})}},1566:(e,t,a)=>{a.d(t,{A:()=>l});var n=a(7876),s=a(4232),r=a(1467);function i(e){let{data:t}=e,r=(0,s.useRef)(null);return(0,s.useEffect)(()=>{t&&r.current&&(r.current.innerHTML="",Promise.all([a.e(391),a.e(917),a.e(403),a.e(728),a.e(682)]).then(a.bind(a,4682)).then(e=>{let a=(0,e.default)().width(r.current.clientWidth).height(500).backgroundColor("#ffffff").nodeColor(e=>{switch(e.type){case"jobSeeker":return"#3b82f6";case"company":return"#14b8a6";case"position":return"#10b981";case"skill":return"#f59e0b";default:return"#6366f1"}}).nodeLabel(e=>e.name).nodeVal(e=>e.size||5).linkWidth(e=>e.value||1).linkDirectionalParticles(2).linkDirectionalParticleSpeed(.005).graphData(t)(r.current);window.addEventListener("resize",()=>{r.current&&a.width(r.current.clientWidth)})}).catch(e=>{console.error("Failed to load 3D visualization:",e),r.current&&(r.current.innerHTML='<div class="flex items-center justify-center h-full text-gray-500">3D visualization unavailable</div>')}))},[t]),(0,n.jsx)("div",{ref:r,className:"w-full h-[500px] border rounded-lg shadow-sm bg-white"})}function l(e){let{isOpen:t,onClose:a,data:l,title:c}=e,[o,d]=(0,s.useState)("2D");return t?(0,n.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,n.jsxs)("div",{className:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,n.jsx)("div",{className:"fixed inset-0 transition-opacity","aria-hidden":"true",children:(0,n.jsx)("div",{className:"absolute inset-0 bg-gray-500 opacity-75",onClick:a})}),(0,n.jsxs)("div",{className:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-5xl sm:w-full",children:[(0,n.jsx)("div",{className:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:(0,n.jsx)("div",{className:"sm:flex sm:items-start",children:(0,n.jsxs)("div",{className:"w-full",children:[(0,n.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,n.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:c||"Network Visualization"}),(0,n.jsxs)("div",{className:"flex space-x-2",children:[(0,n.jsx)("button",{type:"button",onClick:()=>d("2D"),className:"px-3 py-1 text-sm font-medium rounded-md ".concat("2D"===o?"bg-indigo-100 text-indigo-700":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:"2D View"}),(0,n.jsx)("button",{type:"button",onClick:()=>d("3D"),className:"px-3 py-1 text-sm font-medium rounded-md ".concat("3D"===o?"bg-indigo-100 text-indigo-700":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:"3D View"})]})]}),(0,n.jsx)("div",{className:"mt-2",children:"2D"===o?(0,n.jsx)(r.A,{data:l}):(0,n.jsx)(i,{data:l})})]})})}),(0,n.jsx)("div",{className:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:(0,n.jsx)("button",{type:"button",className:"btn-secondary",onClick:a,children:"Close"})})]})]})}):null}},6234:(e,t,a)=>{a.d(t,{A:()=>o});var n=a(7876),s=a(8230),r=a.n(s),i=a(9099),l=a(4232);function c(){let e=(0,i.useRouter)(),[t,a]=(0,l.useState)(!1),s=[{name:"Dashboard",path:"/",icon:"\uD83C\uDFE0"},{name:"Job Matches",path:"/matches",icon:"\uD83C\uDFAF"},{name:"Job Seekers",path:"/job-seekers",icon:"\uD83D\uDC65"},{name:"Companies",path:"/companies",icon:"\uD83C\uDFE2"},{name:"Positions",path:"/positions",icon:"\uD83D\uDCCB"},{name:"Skills",path:"/skills",icon:"\uD83D\uDEE0️"},{name:"Global View",path:"/global-view",icon:"\uD83C\uDF10"}];return(0,n.jsx)("nav",{className:"bg-white shadow-soft border-b border-candid-gray-200",children:(0,n.jsxs)("div",{className:"container-app",children:[(0,n.jsxs)("div",{className:"flex justify-between items-center h-20",children:[(0,n.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,n.jsxs)(r(),{href:"/",className:"flex items-center space-x-3 group",children:[(0,n.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-primary-500 to-accent-500 rounded-xl flex items-center justify-center shadow-soft group-hover:shadow-medium transition-all duration-200",children:(0,n.jsx)("span",{className:"text-white font-bold text-lg",children:"C"})}),(0,n.jsxs)("div",{className:"hidden sm:block",children:[(0,n.jsx)("h1",{className:"text-xl font-bold text-candid-navy-900 group-hover:text-primary-600 transition-colors duration-200",children:"Candid Connections"}),(0,n.jsx)("p",{className:"text-sm text-candid-gray-600 -mt-1",children:"Katra Platform"})]})]})}),(0,n.jsx)("div",{className:"hidden lg:block",children:(0,n.jsx)("div",{className:"flex items-center space-x-1",children:s.map(t=>(0,n.jsxs)(r(),{href:t.path,className:"flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ".concat(e.pathname===t.path?"nav-link-active":"nav-link"),children:[(0,n.jsx)("span",{className:"text-base",children:t.icon}),(0,n.jsx)("span",{children:t.name})]},t.path))})}),(0,n.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[(0,n.jsx)(r(),{href:"/global-view",className:"btn-outline text-sm py-2 px-4",children:"\uD83C\uDF10 Network View"}),(0,n.jsx)(r(),{href:"https://portal.candid-connections.com/user/login",target:"_blank",rel:"noopener noreferrer",className:"btn-primary text-sm py-2 px-4",children:"Portal Login"})]}),(0,n.jsx)("div",{className:"lg:hidden",children:(0,n.jsx)("button",{onClick:()=>a(!t),className:"p-2 rounded-lg text-candid-navy-600 hover:text-primary-600 hover:bg-primary-50 transition-colors duration-200","aria-label":"Toggle mobile menu",children:(0,n.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:t?(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})})]}),t&&(0,n.jsx)("div",{className:"lg:hidden border-t border-candid-gray-200 py-4",children:(0,n.jsxs)("div",{className:"space-y-2",children:[s.map(t=>(0,n.jsxs)(r(),{href:t.path,onClick:()=>a(!1),className:"flex items-center space-x-3 px-4 py-3 rounded-lg text-base font-medium transition-all duration-200 ".concat(e.pathname===t.path?"nav-link-active":"nav-link"),children:[(0,n.jsx)("span",{className:"text-lg",children:t.icon}),(0,n.jsx)("span",{children:t.name})]},t.path)),(0,n.jsxs)("div",{className:"pt-4 space-y-2",children:[(0,n.jsx)(r(),{href:"/global-view",onClick:()=>a(!1),className:"block w-full btn-outline text-center",children:"\uD83C\uDF10 Network View"}),(0,n.jsx)(r(),{href:"https://portal.candid-connections.com/user/login",target:"_blank",rel:"noopener noreferrer",className:"block w-full btn-primary text-center",children:"Portal Login"})]})]})})]})})}function o(e){let{children:t}=e;return(0,n.jsxs)("div",{className:"min-h-screen bg-candid-gray-50",children:[(0,n.jsx)(c,{}),(0,n.jsx)("main",{className:"container-app section-padding",children:t})]})}},8512:(e,t,a)=>{a.d(t,{_:()=>n});let n=()=>{let e=Array.from({length:5},(e,t)=>({id:"jobSeekers/".concat(t),name:"Job Seeker ".concat(t+1),type:"jobSeeker",size:8})),t=Array.from({length:3},(e,t)=>({id:"companies/".concat(t),name:"Company ".concat(t+1),type:"company",size:10})),a=Array.from({length:4},(e,t)=>({id:"positions/".concat(t),name:"Position ".concat(t+1),type:"position",size:9})),n=Array.from({length:8},(e,t)=>({id:"skills/".concat(t),name:"Skill ".concat(t+1),type:"skill",size:6})),s=[...e,...t,...a,...n],r=[];return e.forEach((e,a)=>{r.push({source:e.id,target:t[a%t.length].id,type:"works_for",value:1})}),t.forEach((e,t)=>{a.forEach((a,n)=>{(t+n)%2==0&&r.push({source:e.id,target:a.id,type:"posts",value:1})})}),e.forEach(e=>{let t=2+Math.floor(3*Math.random());[...n].sort(()=>.5-Math.random()).slice(0,t).forEach(t=>{r.push({source:e.id,target:t.id,type:"has_skill",value:1})})}),a.forEach(e=>{let t=2+Math.floor(2*Math.random());[...n].sort(()=>.5-Math.random()).slice(0,t).forEach(t=>{r.push({source:e.id,target:t.id,type:"requires",value:1})})}),{nodes:s,links:r}}}}]);