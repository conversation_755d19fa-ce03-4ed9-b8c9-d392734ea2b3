"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[582],{1467:(e,t,a)=>{a.d(t,{A:()=>i});var r=a(7876),s=a(4232),n=a(7070);function i(e){let{data:t}=e,a=(0,s.useRef)(null);return(0,s.useEffect)(()=>{var e;if(!t||!a.current)return;n.Ltv(a.current).selectAll("*").remove();let r=n.Ltv(a.current).attr("width",800).attr("height",600).attr("viewBox",[0,0,800,600]),s=n.tXi(t.nodes).force("link",n.kJC(t.links).id(e=>e.id)).force("charge",n.xJS().strength(-400)).force("center",n.jTM(400,300)),i=r.append("g").selectAll("line").data(t.links).join("line").attr("stroke","#999").attr("stroke-opacity",.6).attr("stroke-width",e=>Math.sqrt(e.value||1)),l=r.append("g").selectAll("circle").data(t.nodes).join("circle").attr("r",e=>e.size||5).attr("fill",e=>{switch(e.type){case"jobSeeker":return"#3b82f6";case"company":return"#14b8a6";case"position":return"#10b981";case"skill":return"#f59e0b";default:return"#6366f1"}}).call((e=s,n.$Er().on("start",function(t){t.active||e.alphaTarget(.3).restart(),t.subject.fx=t.subject.x,t.subject.fy=t.subject.y}).on("drag",function(e){e.subject.fx=e.x,e.subject.fy=e.y}).on("end",function(t){t.active||e.alphaTarget(0),t.subject.fx=null,t.subject.fy=null})));return l.append("title").text(e=>e.name),s.on("tick",()=>{i.attr("x1",e=>e.source.x).attr("y1",e=>e.source.y).attr("x2",e=>e.target.x).attr("y2",e=>e.target.y),l.attr("cx",e=>e.x).attr("cy",e=>e.y)}),()=>{s.stop()}},[t]),(0,r.jsx)("div",{className:"border rounded-lg shadow-sm bg-white p-4",children:(0,r.jsx)("svg",{ref:a,className:"w-full h-full"})})}},1566:(e,t,a)=>{a.d(t,{A:()=>l});var r=a(7876),s=a(4232),n=a(1467);function i(e){let{data:t}=e,n=(0,s.useRef)(null);return(0,s.useEffect)(()=>{t&&n.current&&(n.current.innerHTML="",Promise.all([a.e(391),a.e(917),a.e(403),a.e(728),a.e(682)]).then(a.bind(a,4682)).then(e=>{let a=(0,e.default)().width(n.current.clientWidth).height(500).backgroundColor("#ffffff").nodeColor(e=>{switch(e.type){case"jobSeeker":return"#3b82f6";case"company":return"#14b8a6";case"position":return"#10b981";case"skill":return"#f59e0b";default:return"#6366f1"}}).nodeLabel(e=>e.name).nodeVal(e=>e.size||5).linkWidth(e=>e.value||1).linkDirectionalParticles(2).linkDirectionalParticleSpeed(.005).graphData(t)(n.current);window.addEventListener("resize",()=>{n.current&&a.width(n.current.clientWidth)})}).catch(e=>{console.error("Failed to load 3D visualization:",e),n.current&&(n.current.innerHTML='<div class="flex items-center justify-center h-full text-gray-500">3D visualization unavailable</div>')}))},[t]),(0,r.jsx)("div",{ref:n,className:"w-full h-[500px] border rounded-lg shadow-sm bg-white"})}function l(e){let{isOpen:t,onClose:a,data:l,title:c}=e,[o,d]=(0,s.useState)("2D");return t?(0,r.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,r.jsxs)("div",{className:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,r.jsx)("div",{className:"fixed inset-0 transition-opacity","aria-hidden":"true",children:(0,r.jsx)("div",{className:"absolute inset-0 bg-gray-500 opacity-75",onClick:a})}),(0,r.jsxs)("div",{className:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-5xl sm:w-full",children:[(0,r.jsx)("div",{className:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:(0,r.jsx)("div",{className:"sm:flex sm:items-start",children:(0,r.jsxs)("div",{className:"w-full",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,r.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:c||"Network Visualization"}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{type:"button",onClick:()=>d("2D"),className:"px-3 py-1 text-sm font-medium rounded-md ".concat("2D"===o?"bg-indigo-100 text-indigo-700":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:"2D View"}),(0,r.jsx)("button",{type:"button",onClick:()=>d("3D"),className:"px-3 py-1 text-sm font-medium rounded-md ".concat("3D"===o?"bg-indigo-100 text-indigo-700":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:"3D View"})]})]}),(0,r.jsx)("div",{className:"mt-2",children:"2D"===o?(0,r.jsx)(n.A,{data:l}):(0,r.jsx)(i,{data:l})})]})})}),(0,r.jsx)("div",{className:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:(0,r.jsx)("button",{type:"button",className:"btn-secondary",onClick:a,children:"Close"})})]})]})}):null}},6234:(e,t,a)=>{a.d(t,{A:()=>c});var r=a(7876),s=a(8230),n=a.n(s),i=a(9099);function l(){let e=(0,i.useRouter)();return(0,r.jsx)("nav",{className:"bg-white shadow-md",children:(0,r.jsx)("div",{className:"container mx-auto px-4",children:(0,r.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,r.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,r.jsx)(n(),{href:"/",className:"font-bold text-xl text-indigo-600",children:"Candid Connections Katra"})}),(0,r.jsx)("div",{className:"hidden md:block",children:(0,r.jsx)("div",{className:"ml-10 flex items-center space-x-4",children:[{name:"Dashboard",path:"/"},{name:"Job Matches",path:"/matches"},{name:"Job Seekers",path:"/job-seekers"},{name:"Companies",path:"/companies"},{name:"Positions",path:"/positions"},{name:"Skills",path:"/skills"}].map(t=>(0,r.jsx)(n(),{href:t.path,className:"px-3 py-2 rounded-md text-sm font-medium ".concat(e.pathname===t.path?"bg-indigo-100 text-indigo-700":"text-gray-700 hover:bg-gray-100"),children:t.name},t.path))})})]})})})}function c(e){let{children:t}=e;return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(l,{}),(0,r.jsx)("main",{children:t})]})}},8512:(e,t,a)=>{a.d(t,{_:()=>r});let r=()=>{let e=Array.from({length:5},(e,t)=>({id:"jobSeekers/".concat(t),name:"Job Seeker ".concat(t+1),type:"jobSeeker",size:8})),t=Array.from({length:3},(e,t)=>({id:"companies/".concat(t),name:"Company ".concat(t+1),type:"company",size:10})),a=Array.from({length:4},(e,t)=>({id:"positions/".concat(t),name:"Position ".concat(t+1),type:"position",size:9})),r=Array.from({length:8},(e,t)=>({id:"skills/".concat(t),name:"Skill ".concat(t+1),type:"skill",size:6})),s=[...e,...t,...a,...r],n=[];return e.forEach((e,a)=>{n.push({source:e.id,target:t[a%t.length].id,type:"works_for",value:1})}),t.forEach((e,t)=>{a.forEach((a,r)=>{(t+r)%2==0&&n.push({source:e.id,target:a.id,type:"posts",value:1})})}),e.forEach(e=>{let t=2+Math.floor(3*Math.random());[...r].sort(()=>.5-Math.random()).slice(0,t).forEach(t=>{n.push({source:e.id,target:t.id,type:"has_skill",value:1})})}),a.forEach(e=>{let t=2+Math.floor(2*Math.random());[...r].sort(()=>.5-Math.random()).slice(0,t).forEach(t=>{n.push({source:e.id,target:t.id,type:"requires",value:1})})}),{nodes:s,links:n}}},8773:(e,t,a)=>{a.d(t,{Yq:()=>r,zk:()=>s});let r=e=>e?new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}):"",s=e=>e>=80?"bg-emerald-100 text-emerald-800":e>=60?"bg-green-100 text-green-800":e>=40?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}}]);