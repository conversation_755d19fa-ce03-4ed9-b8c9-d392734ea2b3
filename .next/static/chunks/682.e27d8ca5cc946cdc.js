(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[682],{455:(t,e,n)=>{let r=n(4525);t.exports=function(t){return function(e,n){let i=n&&n.indent||0,o=n&&void 0!==n.join?n.join:"\n",a=Array(i+1).join(" "),s=[];for(let n=0;n<t;++n){let t=r(n),i=0===n?"":a;s.push(i+e.replace(/{var}/g,t))}return s.join(o)}}},2223:t=>{t.exports=function(t,e,n,r){this.from=t,this.to=e,this.length=n,this.coefficient=r}},3027:(t,e,n)=>{let r=n(455);function i(t){let e=r(t);return`
  if (!Number.isFinite(options.springCoefficient)) throw new Error('Spring coefficient is not a number');
  if (!Number.isFinite(options.springLength)) throw new Error('Spring length is not a number');

  return {
    /**
     * Updates forces acting on a spring
     */
    update: function (spring) {
      var body1 = spring.from;
      var body2 = spring.to;
      var length = spring.length < 0 ? options.springLength : spring.length;
      ${e("var d{var} = body2.pos.{var} - body1.pos.{var};",{indent:6})}
      var r = Math.sqrt(${e("d{var} * d{var}",{join:" + "})});

      if (r === 0) {
        ${e("d{var} = (random.nextDouble() - 0.5) / 50;",{indent:8})}
        r = Math.sqrt(${e("d{var} * d{var}",{join:" + "})});
      }

      var d = r - length;
      var coefficient = ((spring.coefficient > 0) ? spring.coefficient : options.springCoefficient) * d / r;

      ${e("body1.force.{var} += coefficient * d{var}",{indent:6})};
      body1.springCount += 1;
      body1.springLength += r;

      ${e("body2.force.{var} -= coefficient * d{var}",{indent:6})};
      body2.springCount += 1;
      body2.springLength += r;
    }
  };
`}t.exports=function(t){return Function("options","random",i(t))},t.exports.generateCreateSpringForceFunctionBody=i},4339:(t,e,n)=>{t.exports=function(t){if("uniqueLinkId"in(t=t||{})&&(console.warn("ngraph.graph: Starting from version 0.14 `uniqueLinkId` is deprecated.\nUse `multigraph` option instead\n","\n","Note: there is also change in default behavior: From now on each graph\nis considered to be not a multigraph by default (each edge is unique)."),t.multigraph=t.uniqueLinkId),void 0===t.multigraph&&(t.multigraph=!1),"function"!=typeof Map)throw Error("ngraph.graph requires `Map` to be defined. Please polyfill it before using ngraph");var e,n=new Map,l=new Map,h={},u=0,c=t.multigraph?function(t,e,n){var r=s(t,e),i=h.hasOwnProperty(r);if(i||O(t,e)){i||(h[r]=0);var o="@"+ ++h[r];r=s(t+o,e+o)}return new a(t,e,n,r)}:function(t,e,n){var r=s(t,e),i=l.get(r);return i?(i.data=n,i):new a(t,e,n,r)},d=[],f=M,p=M,g=M,_=M,m={version:20,addNode:b,addLink:function(t,e,n){g();var r=w(t)||b(t),i=w(e)||b(e),a=c(t,e,n),s=l.has(a.id);return l.set(a.id,a),o(r,a),t!==e&&o(i,a),f(a,s?"update":"add"),_(),a},removeLink:function(t,e){return void 0!==e&&(t=O(t,e)),E(t)},removeNode:x,getNode:w,getNodeCount:k,getLinkCount:P,getEdgeCount:P,getLinksCount:P,getNodesCount:k,getLinks:function(t){var e=w(t);return e?e.links:null},forEachNode:C,forEachLinkedNode:function(t,e,r){var i=w(t);if(i&&i.links&&"function"==typeof e)if(r)return function(t,e,r){for(var i=t.values(),o=i.next();!o.done;){var a=o.value;if(a.fromId===e&&r(n.get(a.toId),a))return!0;o=i.next()}}(i.links,t,e);else return function(t,e,r){for(var i=t.values(),o=i.next();!o.done;){var a=o.value,s=a.fromId===e?a.toId:a.fromId;if(r(n.get(s),a))return!0;o=i.next()}}(i.links,t,e)},forEachLink:function(t){if("function"==typeof t)for(var e=l.values(),n=e.next();!n.done;){if(t(n.value))return!0;n=e.next()}},beginUpdate:g,endUpdate:_,clear:function(){g(),C(function(t){x(t.id)}),_()},hasLink:O,hasNode:w,getLink:O};return r(m),e=m.on,m.on=function(){return m.beginUpdate=g=S,m.endUpdate=_=j,f=v,p=y,m.on=e,e.apply(m,arguments)},m;function v(t,e){d.push({link:t,changeType:e})}function y(t,e){d.push({node:t,changeType:e})}function b(t,e){if(void 0===t)throw Error("Invalid node identifier");g();var r=w(t);return r?(r.data=e,p(r,"update")):(r=new i(t,e),p(r,"add")),n.set(t,r),_(),r}function w(t){return n.get(t)}function x(t){var e=w(t);if(!e)return!1;g();var r=e.links;return r&&(r.forEach(E),e.links=null),n.delete(t),p(e,"remove"),_(),!0}function k(){return n.size}function P(){return l.size}function E(t){if(!t||!l.get(t.id))return!1;g(),l.delete(t.id);var e=w(t.fromId),n=w(t.toId);return e&&e.links.delete(t),n&&n.links.delete(t),f(t,"remove"),_(),!0}function O(t,e){if(void 0!==t&&void 0!==e)return l.get(s(t,e))}function M(){}function S(){u+=1}function j(){0==(u-=1)&&d.length>0&&(m.fire("changed",d),d.length=0)}function C(t){if("function"!=typeof t)throw Error("Function is expected to iterate over graph nodes. You passed "+t);for(var e=n.values(),r=e.next();!r.done;){if(t(r.value))return!0;r=e.next()}}};var r=n(6106);function i(t,e){this.id=t,this.links=null,this.data=e}function o(t,e){t.links?t.links.add(e):t.links=new Set([e])}function a(t,e,n,r){this.fromId=t,this.toId=e,this.data=n,this.id=r}function s(t,e){return t.toString()+"\uD83D\uDC49 "+e.toString()}},4525:t=>{t.exports=function(t){return 0===t?"x":1===t?"y":2===t?"z":"c"+(t+1)}},4682:(t,e,n)=>{"use strict";n.r(e),n.d(e,{default:()=>iY});var r=n(9220);let i=new r.Zcv,o=new r.I9Y,a=new r.Pq0,s=new r.I9Y,l=new r.I9Y,h=new r.Pq0,u=new r.Pq0,c=new r.kn4,d=new r.Pq0,f=new r.Pq0,p=null,g=null,_=[],m={NONE:-1,PAN:0,ROTATE:1};class v extends r.H2z{connect(t){super.connect(t),this.domElement.addEventListener("pointermove",this._onPointerMove),this.domElement.addEventListener("pointerdown",this._onPointerDown),this.domElement.addEventListener("pointerup",this._onPointerCancel),this.domElement.addEventListener("pointerleave",this._onPointerCancel),this.domElement.addEventListener("contextmenu",this._onContextMenu),this.domElement.style.touchAction="none"}disconnect(){this.domElement.removeEventListener("pointermove",this._onPointerMove),this.domElement.removeEventListener("pointerdown",this._onPointerDown),this.domElement.removeEventListener("pointerup",this._onPointerCancel),this.domElement.removeEventListener("pointerleave",this._onPointerCancel),this.domElement.removeEventListener("contextmenu",this._onContextMenu),this.domElement.style.touchAction="auto",this.domElement.style.cursor=""}dispose(){this.disconnect()}_updatePointer(t){let e=this.domElement.getBoundingClientRect();o.x=(t.clientX-e.left)/e.width*2-1,o.y=-(t.clientY-e.top)/e.height*2+1}_updateState(t){let e;if("touch"===t.pointerType)e=this.touches.ONE;else switch(t.button){case 0:e=this.mouseButtons.LEFT;break;case 1:e=this.mouseButtons.MIDDLE;break;case 2:e=this.mouseButtons.RIGHT;break;default:e=null}switch(e){case r.kBv.PAN:case r.wtR.PAN:this.state=m.PAN;break;case r.kBv.ROTATE:case r.wtR.ROTATE:this.state=m.ROTATE;break;default:this.state=m.NONE}}getRaycaster(){return console.warn("THREE.DragControls: getRaycaster() has been deprecated. Use controls.raycaster instead."),this.raycaster}setObjects(t){console.warn("THREE.DragControls: setObjects() has been deprecated. Use controls.objects instead."),this.objects=t}getObjects(){return console.warn("THREE.DragControls: getObjects() has been deprecated. Use controls.objects instead."),this.objects}activate(){console.warn("THREE.DragControls: activate() has been renamed to connect()."),this.connect()}deactivate(){console.warn("THREE.DragControls: deactivate() has been renamed to disconnect()."),this.disconnect()}set mode(t){console.warn("THREE.DragControls: The .mode property has been removed. Define the type of transformation via the .mouseButtons or .touches properties.")}get mode(){console.warn("THREE.DragControls: The .mode property has been removed. Define the type of transformation via the .mouseButtons or .touches properties.")}constructor(t,e,n=null){super(e,n),this.objects=t,this.recursive=!0,this.transformGroup=!1,this.rotateSpeed=1,this.raycaster=new r.tBo,this.mouseButtons={LEFT:r.kBv.PAN,MIDDLE:r.kBv.PAN,RIGHT:r.kBv.ROTATE},this.touches={ONE:r.wtR.PAN},this._onPointerMove=y.bind(this),this._onPointerDown=b.bind(this),this._onPointerCancel=w.bind(this),this._onContextMenu=x.bind(this),null!==n&&this.connect(n)}}function y(t){let e=this.object,n=this.domElement,r=this.raycaster;if(!1!==this.enabled){if(this._updatePointer(t),r.setFromCamera(o,e),p)this.state===m.PAN?r.ray.intersectPlane(i,h)&&p.position.copy(h.sub(a).applyMatrix4(c)):this.state===m.ROTATE&&(s.subVectors(o,l).multiplyScalar(this.rotateSpeed),p.rotateOnWorldAxis(d,s.x),p.rotateOnWorldAxis(f.normalize(),-s.y)),this.dispatchEvent({type:"drag",object:p}),l.copy(o);else if("mouse"===t.pointerType||"pen"===t.pointerType)if(_.length=0,r.setFromCamera(o,e),r.intersectObjects(this.objects,this.recursive,_),_.length>0){let t=_[0].object;i.setFromNormalAndCoplanarPoint(e.getWorldDirection(i.normal),u.setFromMatrixPosition(t.matrixWorld)),g!==t&&null!==g&&(this.dispatchEvent({type:"hoveroff",object:g}),n.style.cursor="auto",g=null),g!==t&&(this.dispatchEvent({type:"hoveron",object:t}),n.style.cursor="pointer",g=t)}else null!==g&&(this.dispatchEvent({type:"hoveroff",object:g}),n.style.cursor="auto",g=null);l.copy(o)}}function b(t){let e=this.object,n=this.domElement,r=this.raycaster;!1!==this.enabled&&(this._updatePointer(t),this._updateState(t),_.length=0,r.setFromCamera(o,e),r.intersectObjects(this.objects,this.recursive,_),_.length>0&&(p=!0===this.transformGroup?function t(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return(e.isGroup&&(n=e),null===e.parent)?n:t(e.parent,n)}(_[0].object):_[0].object,i.setFromNormalAndCoplanarPoint(e.getWorldDirection(i.normal),u.setFromMatrixPosition(p.matrixWorld)),r.ray.intersectPlane(i,h)&&(this.state===m.PAN?(c.copy(p.parent.matrixWorld).invert(),a.copy(h).sub(u.setFromMatrixPosition(p.matrixWorld))):this.state===m.ROTATE&&(d.set(0,1,0).applyQuaternion(e.quaternion).normalize(),f.set(1,0,0).applyQuaternion(e.quaternion).normalize())),n.style.cursor="move",this.dispatchEvent({type:"dragstart",object:p})),l.copy(o))}function w(){!1!==this.enabled&&(p&&(this.dispatchEvent({type:"dragend",object:p}),p=null),this.domElement.style.cursor=g?"pointer":"auto",this.state=m.NONE)}function x(t){!1!==this.enabled&&t.preventDefault()}var k=n(5351),P=n(872),E=n(4604);function O(t){return t.x}function M(t){return t.y}function S(t){return t.z}var j=Math.PI*(3-Math.sqrt(5)),C=20*Math.PI/(9+Math.sqrt(221));function T(t){return function(){return t}}function A(t){return(t()-.5)*1e-6}function D(t){return t.index}function R(t,e){var n=t.get(e);if(!n)throw Error("node not found: "+e);return n}function L(t,e,n){if(isNaN(e))return t;var r,i,o,a,s,l,h=t._root,u={data:n},c=t._x0,d=t._x1;if(!h)return t._root=u,t;for(;h.length;)if((a=e>=(i=(c+d)/2))?c=i:d=i,r=h,!(h=h[s=+a]))return r[s]=u,t;if(e===(o=+t._x.call(null,h.data)))return u.next=h,r?r[s]=u:t._root=u,t;do r=r?r[s]=[,,]:t._root=[,,],(a=e>=(i=(c+d)/2))?c=i:d=i;while((s=+a)==(l=+(o>=i)));return r[l]=h,r[s]=u,t}function I(t,e,n){this.node=t,this.x0=e,this.x1=n}function N(t){return t[0]}function z(t,e){var n=new F(null==e?N:e,NaN,NaN);return null==t?n:n.addAll(t)}function F(t,e,n){this._x=t,this._x0=e,this._x1=n,this._root=void 0}function B(t){for(var e={data:t.data},n=e;t=t.next;)n=n.next={data:t.data};return e}var U=z.prototype=F.prototype;U.copy=function(){var t,e,n=new F(this._x,this._x0,this._x1),r=this._root;if(!r)return n;if(!r.length)return n._root=B(r),n;for(t=[{source:r,target:n._root=[,,]}];r=t.pop();)for(var i=0;i<2;++i)(e=r.source[i])&&(e.length?t.push({source:e,target:r.target[i]=[,,]}):r.target[i]=B(e));return n},U.add=function(t){let e=+this._x.call(null,t);return L(this.cover(e),e,t)},U.addAll=function(t){Array.isArray(t)||(t=Array.from(t));let e=t.length,n=new Float64Array(e),r=1/0,i=-1/0;for(let o=0,a;o<e;++o)!isNaN(a=+this._x.call(null,t[o]))&&(n[o]=a,a<r&&(r=a),a>i&&(i=a));if(r>i)return this;this.cover(r).cover(i);for(let r=0;r<e;++r)L(this,n[r],t[r]);return this},U.cover=function(t){if(isNaN(t*=1))return this;var e=this._x0,n=this._x1;if(isNaN(e))n=(e=Math.floor(t))+1;else{for(var r,i,o=n-e||1,a=this._root;e>t||t>=n;)switch(i=+(t<e),(r=[,,])[i]=a,a=r,o*=2,i){case 0:n=e+o;break;case 1:e=n-o}this._root&&this._root.length&&(this._root=a)}return this._x0=e,this._x1=n,this},U.data=function(){var t=[];return this.visit(function(e){if(!e.length)do t.push(e.data);while(e=e.next)}),t},U.extent=function(t){return arguments.length?this.cover(+t[0][0]).cover(+t[1][0]):isNaN(this._x0)?void 0:[[this._x0],[this._x1]]},U.find=function(t,e){var n,r,i,o,a,s=this._x0,l=this._x1,h=[],u=this._root;for(u&&h.push(new I(u,s,l)),null==e?e=1/0:(s=t-e,l=t+e);o=h.pop();)if((u=o.node)&&!((r=o.x0)>l)&&!((i=o.x1)<s))if(u.length){var c=(r+i)/2;h.push(new I(u[1],c,i),new I(u[0],r,c)),(a=+(t>=c))&&(o=h[h.length-1],h[h.length-1]=h[h.length-1-a],h[h.length-1-a]=o)}else{var d=Math.abs(t-this._x.call(null,u.data));d<e&&(e=d,s=t-d,l=t+d,n=u.data)}return n},U.remove=function(t){if(isNaN(o=+this._x.call(null,t)))return this;var e,n,r,i,o,a,s,l,h,u=this._root,c=this._x0,d=this._x1;if(!u)return this;if(u.length)for(;;){if((s=o>=(a=(c+d)/2))?c=a:d=a,e=u,!(u=u[l=+s]))return this;if(!u.length)break;e[l+1&1]&&(n=e,h=l)}for(;u.data!==t;)if(r=u,!(u=u.next))return this;return((i=u.next)&&delete u.next,r)?i?r.next=i:delete r.next:e?(i?e[l]=i:delete e[l],(u=e[0]||e[1])&&u===(e[1]||e[0])&&!u.length&&(n?n[h]=u:this._root=u)):this._root=i,this},U.removeAll=function(t){for(var e=0,n=t.length;e<n;++e)this.remove(t[e]);return this},U.root=function(){return this._root},U.size=function(){var t=0;return this.visit(function(e){if(!e.length)do++t;while(e=e.next)}),t},U.visit=function(t){var e,n,r,i,o=[],a=this._root;for(a&&o.push(new I(a,this._x0,this._x1));e=o.pop();)if(!t(a=e.node,r=e.x0,i=e.x1)&&a.length){var s=(r+i)/2;(n=a[1])&&o.push(new I(n,s,i)),(n=a[0])&&o.push(new I(n,r,s))}return this},U.visitAfter=function(t){var e,n=[],r=[];for(this._root&&n.push(new I(this._root,this._x0,this._x1));e=n.pop();){var i=e.node;if(i.length){var o,a=e.x0,s=e.x1,l=(a+s)/2;(o=i[0])&&n.push(new I(o,a,l)),(o=i[1])&&n.push(new I(o,l,s))}r.push(e)}for(;e=r.pop();)t(e.node,e.x0,e.x1);return this},U.x=function(t){return arguments.length?(this._x=t,this):this._x};var q=n(7046);function $(t,e,n,r,i){if(isNaN(e)||isNaN(n)||isNaN(r))return t;var o,a,s,l,h,u,c,d,f,p,g,_,m=t._root,v={data:i},y=t._x0,b=t._y0,w=t._z0,x=t._x1,k=t._y1,P=t._z1;if(!m)return t._root=v,t;for(;m.length;)if((d=e>=(a=(y+x)/2))?y=a:x=a,(f=n>=(s=(b+k)/2))?b=s:k=s,(p=r>=(l=(w+P)/2))?w=l:P=l,o=m,!(m=m[g=p<<2|f<<1|d]))return o[g]=v,t;if(h=+t._x.call(null,m.data),u=+t._y.call(null,m.data),c=+t._z.call(null,m.data),e===h&&n===u&&r===c)return v.next=m,o?o[g]=v:t._root=v,t;do o=o?o[g]=Array(8):t._root=Array(8),(d=e>=(a=(y+x)/2))?y=a:x=a,(f=n>=(s=(b+k)/2))?b=s:k=s,(p=r>=(l=(w+P)/2))?w=l:P=l;while((g=p<<2|f<<1|d)==(_=(c>=l)<<2|(u>=s)<<1|h>=a));return o[_]=m,o[g]=v,t}function H(t,e,n,r,i,o,a){this.node=t,this.x0=e,this.y0=n,this.z0=r,this.x1=i,this.y1=o,this.z1=a}let V=(t,e,n,r,i,o)=>Math.sqrt((t-r)**2+(e-i)**2+(n-o)**2);function Y(t){return t[0]}function W(t){return t[1]}function Z(t){return t[2]}function G(t,e,n,r){var i=new K(null==e?Y:e,null==n?W:n,null==r?Z:r,NaN,NaN,NaN,NaN,NaN,NaN);return null==t?i:i.addAll(t)}function K(t,e,n,r,i,o,a,s,l){this._x=t,this._y=e,this._z=n,this._x0=r,this._y0=i,this._z0=o,this._x1=a,this._y1=s,this._z1=l,this._root=void 0}function X(t){for(var e={data:t.data},n=e;t=t.next;)n=n.next={data:t.data};return e}var Q=G.prototype=K.prototype;Q.copy=function(){var t,e,n=new K(this._x,this._y,this._z,this._x0,this._y0,this._z0,this._x1,this._y1,this._z1),r=this._root;if(!r)return n;if(!r.length)return n._root=X(r),n;for(t=[{source:r,target:n._root=Array(8)}];r=t.pop();)for(var i=0;i<8;++i)(e=r.source[i])&&(e.length?t.push({source:e,target:r.target[i]=Array(8)}):r.target[i]=X(e));return n},Q.add=function(t){let e=+this._x.call(null,t),n=+this._y.call(null,t),r=+this._z.call(null,t);return $(this.cover(e,n,r),e,n,r,t)},Q.addAll=function(t){Array.isArray(t)||(t=Array.from(t));let e=t.length,n=new Float64Array(e),r=new Float64Array(e),i=new Float64Array(e),o=1/0,a=1/0,s=1/0,l=-1/0,h=-1/0,u=-1/0;for(let c=0,d,f,p,g;c<e;++c)!(isNaN(f=+this._x.call(null,d=t[c]))||isNaN(p=+this._y.call(null,d))||isNaN(g=+this._z.call(null,d)))&&(n[c]=f,r[c]=p,i[c]=g,f<o&&(o=f),f>l&&(l=f),p<a&&(a=p),p>h&&(h=p),g<s&&(s=g),g>u&&(u=g));if(o>l||a>h||s>u)return this;this.cover(o,a,s).cover(l,h,u);for(let o=0;o<e;++o)$(this,n[o],r[o],i[o],t[o]);return this},Q.cover=function(t,e,n){if(isNaN(t*=1)||isNaN(e*=1)||isNaN(n*=1))return this;var r=this._x0,i=this._y0,o=this._z0,a=this._x1,s=this._y1,l=this._z1;if(isNaN(r))a=(r=Math.floor(t))+1,s=(i=Math.floor(e))+1,l=(o=Math.floor(n))+1;else{for(var h,u,c=a-r||1,d=this._root;r>t||t>=a||i>e||e>=s||o>n||n>=l;)switch(u=(n<o)<<2|(e<i)<<1|t<r,(h=Array(8))[u]=d,d=h,c*=2,u){case 0:a=r+c,s=i+c,l=o+c;break;case 1:r=a-c,s=i+c,l=o+c;break;case 2:a=r+c,i=s-c,l=o+c;break;case 3:r=a-c,i=s-c,l=o+c;break;case 4:a=r+c,s=i+c,o=l-c;break;case 5:r=a-c,s=i+c,o=l-c;break;case 6:a=r+c,i=s-c,o=l-c;break;case 7:r=a-c,i=s-c,o=l-c}this._root&&this._root.length&&(this._root=d)}return this._x0=r,this._y0=i,this._z0=o,this._x1=a,this._y1=s,this._z1=l,this},Q.data=function(){var t=[];return this.visit(function(e){if(!e.length)do t.push(e.data);while(e=e.next)}),t},Q.extent=function(t){return arguments.length?this.cover(+t[0][0],+t[0][1],+t[0][2]).cover(+t[1][0],+t[1][1],+t[1][2]):isNaN(this._x0)?void 0:[[this._x0,this._y0,this._z0],[this._x1,this._y1,this._z1]]},Q.find=function(t,e,n,r){var i,o,a,s,l,h,u,c,d,f=this._x0,p=this._y0,g=this._z0,_=this._x1,m=this._y1,v=this._z1,y=[],b=this._root;for(b&&y.push(new H(b,f,p,g,_,m,v)),null==r?r=1/0:(f=t-r,p=e-r,g=n-r,_=t+r,m=e+r,v=n+r,r*=r);c=y.pop();)if((b=c.node)&&!((o=c.x0)>_)&&!((a=c.y0)>m)&&!((s=c.z0)>v)&&!((l=c.x1)<f)&&!((h=c.y1)<p)&&!((u=c.z1)<g))if(b.length){var w=(o+l)/2,x=(a+h)/2,k=(s+u)/2;y.push(new H(b[7],w,x,k,l,h,u),new H(b[6],o,x,k,w,h,u),new H(b[5],w,a,k,l,x,u),new H(b[4],o,a,k,w,x,u),new H(b[3],w,x,s,l,h,k),new H(b[2],o,x,s,w,h,k),new H(b[1],w,a,s,l,x,k),new H(b[0],o,a,s,w,x,k)),(d=(n>=k)<<2|(e>=x)<<1|t>=w)&&(c=y[y.length-1],y[y.length-1]=y[y.length-1-d],y[y.length-1-d]=c)}else{var P=t-this._x.call(null,b.data),E=e-this._y.call(null,b.data),O=n-this._z.call(null,b.data),M=P*P+E*E+O*O;if(M<r){var S=Math.sqrt(r=M);f=t-S,p=e-S,g=n-S,_=t+S,m=e+S,v=n+S,i=b.data}}return i},Q.findAllWithinRadius=function(t,e,n,r){let i=[],o=t-r,a=e-r,s=n-r,l=t+r,h=e+r,u=n+r;return this.visit((c,d,f,p,g,_,m)=>{if(!c.length)do{let o=c.data;V(t,e,n,this._x(o),this._y(o),this._z(o))<=r&&i.push(o)}while(c=c.next);return d>l||f>h||p>u||g<o||_<a||m<s}),i},Q.remove=function(t){if(isNaN(o=+this._x.call(null,t))||isNaN(a=+this._y.call(null,t))||isNaN(s=+this._z.call(null,t)))return this;var e,n,r,i,o,a,s,l,h,u,c,d,f,p,g,_=this._root,m=this._x0,v=this._y0,y=this._z0,b=this._x1,w=this._y1,x=this._z1;if(!_)return this;if(_.length)for(;;){if((c=o>=(l=(m+b)/2))?m=l:b=l,(d=a>=(h=(v+w)/2))?v=h:w=h,(f=s>=(u=(y+x)/2))?y=u:x=u,e=_,!(_=_[p=f<<2|d<<1|c]))return this;if(!_.length)break;(e[p+1&7]||e[p+2&7]||e[p+3&7]||e[p+4&7]||e[p+5&7]||e[p+6&7]||e[p+7&7])&&(n=e,g=p)}for(;_.data!==t;)if(r=_,!(_=_.next))return this;return((i=_.next)&&delete _.next,r)?i?r.next=i:delete r.next:e?(i?e[p]=i:delete e[p],(_=e[0]||e[1]||e[2]||e[3]||e[4]||e[5]||e[6]||e[7])&&_===(e[7]||e[6]||e[5]||e[4]||e[3]||e[2]||e[1]||e[0])&&!_.length&&(n?n[g]=_:this._root=_)):this._root=i,this},Q.removeAll=function(t){for(var e=0,n=t.length;e<n;++e)this.remove(t[e]);return this},Q.root=function(){return this._root},Q.size=function(){var t=0;return this.visit(function(e){if(!e.length)do++t;while(e=e.next)}),t},Q.visit=function(t){var e,n,r,i,o,a,s,l,h=[],u=this._root;for(u&&h.push(new H(u,this._x0,this._y0,this._z0,this._x1,this._y1,this._z1));e=h.pop();)if(!t(u=e.node,r=e.x0,i=e.y0,o=e.z0,a=e.x1,s=e.y1,l=e.z1)&&u.length){var c=(r+a)/2,d=(i+s)/2,f=(o+l)/2;(n=u[7])&&h.push(new H(n,c,d,f,a,s,l)),(n=u[6])&&h.push(new H(n,r,d,f,c,s,l)),(n=u[5])&&h.push(new H(n,c,i,f,a,d,l)),(n=u[4])&&h.push(new H(n,r,i,f,c,d,l)),(n=u[3])&&h.push(new H(n,c,d,o,a,s,f)),(n=u[2])&&h.push(new H(n,r,d,o,c,s,f)),(n=u[1])&&h.push(new H(n,c,i,o,a,d,f)),(n=u[0])&&h.push(new H(n,r,i,o,c,d,f))}return this},Q.visitAfter=function(t){var e,n=[],r=[];for(this._root&&n.push(new H(this._root,this._x0,this._y0,this._z0,this._x1,this._y1,this._z1));e=n.pop();){var i=e.node;if(i.length){var o,a=e.x0,s=e.y0,l=e.z0,h=e.x1,u=e.y1,c=e.z1,d=(a+h)/2,f=(s+u)/2,p=(l+c)/2;(o=i[0])&&n.push(new H(o,a,s,l,d,f,p)),(o=i[1])&&n.push(new H(o,d,s,l,h,f,p)),(o=i[2])&&n.push(new H(o,a,f,l,d,u,p)),(o=i[3])&&n.push(new H(o,d,f,l,h,u,p)),(o=i[4])&&n.push(new H(o,a,s,p,d,f,c)),(o=i[5])&&n.push(new H(o,d,s,p,h,f,c)),(o=i[6])&&n.push(new H(o,a,f,p,d,u,c)),(o=i[7])&&n.push(new H(o,d,f,p,h,u,c))}r.push(e)}for(;e=r.pop();)t(e.node,e.x0,e.y0,e.z0,e.x1,e.y1,e.z1);return this},Q.x=function(t){return arguments.length?(this._x=t,this):this._x},Q.y=function(t){return arguments.length?(this._y=t,this):this._y},Q.z=function(t){return arguments.length?(this._z=t,this):this._z};var J=n(4339),tt=n(9062);let te=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)};var tn="object"==typeof global&&global&&global.Object===Object&&global,tr="object"==typeof self&&self&&self.Object===Object&&self,ti=tn||tr||Function("return this")();let to=function(){return ti.Date.now()};var ta=/\s/;let ts=function(t){for(var e=t.length;e--&&ta.test(t.charAt(e)););return e};var tl=/^\s+/,th=ti.Symbol,tu=Object.prototype,tc=tu.hasOwnProperty,td=tu.toString,tf=th?th.toStringTag:void 0;let tp=function(t){var e=tc.call(t,tf),n=t[tf];try{t[tf]=void 0;var r=!0}catch(t){}var i=td.call(t);return r&&(e?t[tf]=n:delete t[tf]),i};var tg=Object.prototype.toString,t_=th?th.toStringTag:void 0;let tm=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":t_&&t_ in Object(t)?tp(t):tg.call(t)},tv=function(t){return"symbol"==typeof t||null!=t&&"object"==typeof t&&"[object Symbol]"==tm(t)};var ty=0/0,tb=/^[-+]0x[0-9a-f]+$/i,tw=/^0b[01]+$/i,tx=/^0o[0-7]+$/i,tk=parseInt;let tP=function(t){if("number"==typeof t)return t;if(tv(t))return ty;if(te(t)){var e,n="function"==typeof t.valueOf?t.valueOf():t;t=te(n)?n+"":n}if("string"!=typeof t)return 0===t?t:+t;t=(e=t)?e.slice(0,ts(e)+1).replace(tl,""):e;var r=tw.test(t);return r||tx.test(t)?tk(t.slice(2),r?2:8):tb.test(t)?ty:+t};var tE=Math.max,tO=Math.min;let tM=function(t,e,n){var r,i,o,a,s,l,h=0,u=!1,c=!1,d=!0;if("function"!=typeof t)throw TypeError("Expected a function");function f(e){var n=r,o=i;return r=i=void 0,h=e,a=t.apply(o,n)}function p(t){var n=t-l,r=t-h;return void 0===l||n>=e||n<0||c&&r>=o}function g(){var t,n,r,i=to();if(p(i))return _(i);s=setTimeout(g,(t=i-l,n=i-h,r=e-t,c?tO(r,o-n):r))}function _(t){return(s=void 0,d&&r)?f(t):(r=i=void 0,a)}function m(){var t,n=to(),o=p(n);if(r=arguments,i=this,l=n,o){if(void 0===s)return h=t=l,s=setTimeout(g,e),u?f(t):a;if(c)return clearTimeout(s),s=setTimeout(g,e),f(l)}return void 0===s&&(s=setTimeout(g,e)),a}return e=tP(e)||0,te(n)&&(u=!!n.leading,o=(c="maxWait"in n)?tE(tP(n.maxWait)||0,e):o,d="trailing"in n?!!n.trailing:d),m.cancel=function(){void 0!==s&&clearTimeout(s),h=0,r=l=i=s=void 0},m.flush=function(){return void 0===s?a:_(to())},m};function tS(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}var tj=function(t,e,n){return Object.defineProperty(t,"prototype",{writable:!1}),t}(function t(e,n){var r=n.default,i=n.triggerUpdate,o=n.onChange;if(!(this instanceof t))throw TypeError("Cannot call a class as a function");this.name=e,this.defaultVal=void 0===r?null:r,this.triggerUpdate=void 0===i||i,this.onChange=void 0===o?function(t,e){}:o});function tC(t){var e=t.stateInit,n=void 0===e?function(){return{}}:e,r=t.props,i=void 0===r?{}:r,o=t.methods,a=void 0===o?{}:o,s=t.aliases,l=void 0===s?{}:s,h=t.init,u=void 0===h?function(){}:h,c=t.update,d=void 0===c?function(){}:c,f=Object.keys(i).map(function(t){return new tj(t,i[t])});return function t(){for(var e=arguments.length,r=Array(e),i=0;i<e;i++)r[i]=arguments[i];var o=!!(this instanceof t?this.constructor:void 0),s=o?r.shift():void 0,h=r[0],c=void 0===h?{}:h,p=Object.assign({},n instanceof Function?n(c):n,{initialised:!1}),g={};function _(t){return m(t,c),v(),_}var m=function(t,e){u.call(_,t,p,e),p.initialised=!0},v=tM(function(){p.initialised&&(d.call(_,p,g),g={})},1);return f.forEach(function(t){var e,n,r,i,o,a,s,l;_[t.name]=(n=(e=t).name,i=void 0!==(r=e.triggerUpdate)&&r,a=void 0===(o=e.onChange)?function(t,e){}:o,l=void 0===(s=e.defaultVal)?null:s,function(t){var e=p[n];if(!arguments.length)return e;var r=void 0===t?l:t;return p[n]=r,a.call(_,r,p,e),g.hasOwnProperty(n)||(g[n]=e),i&&v(),_})}),Object.keys(a).forEach(function(t){_[t]=function(){for(var e,n=arguments.length,r=Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=a[t]).call.apply(e,[_,p].concat(r))}}),Object.entries(l).forEach(function(t){var e=function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o,a,s=[],l=!0,h=!1;try{o=(n=n.call(t)).next,!1;for(;!(l=(r=o.call(n)).done)&&(s.push(r.value),s.length!==e);l=!0);}catch(t){h=!0,i=t}finally{try{if(!l&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(h)throw i}}return s}}(t,2)||function(t,e){if(t){if("string"==typeof t)return tS(t,2);var n=({}).toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?tS(t,e):void 0}}(t,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),n=e[0],r=e[1];return _[n]=_[r]}),_.resetProps=function(){return f.forEach(function(t){_[t.name](t.defaultVal)}),_},_.resetProps(),p._rerender=v,o&&s&&_(s),_}}var tT=function(t){return"function"==typeof t?t:"string"==typeof t?function(e){return e[t]}:function(e){return t}};function tA(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function tD(t,e,n){if("function"==typeof t?t===e:t.has(e))return arguments.length<3?e:n;throw TypeError("Private element is not present on this object")}function tR(t,e){return t.get(tD(t,e))}function tL(t,e,n){(function(t,e){if(e.has(t))throw TypeError("Cannot initialize the same private elements twice on an object")})(t,e),e.set(t,n)}function tI(t,e,n){return t.set(tD(t,e),n),n}function tN(t,e){if(t){if("string"==typeof t)return tA(t,e);var n=({}).toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?tA(t,e):void 0}}var tz=new WeakMap,tF=new WeakMap,tB=new WeakMap,tU=new WeakMap,tq=new WeakMap,t$=new WeakMap,tH=function(){var t,e;return t=function t(){if(!(this instanceof t))throw TypeError("Cannot call a class as a function");tL(this,tz,new Map),tL(this,tF,new Map),tL(this,tB,function(t){return t}),tL(this,tU,function(){return{}}),tL(this,tq,function(){}),tL(this,t$,function(){})},e=[{key:"getObj",value:function(t){return tR(tz,this).get(tR(tB,this).call(this,t))}},{key:"getData",value:function(t){return tR(tF,this).get(t)}},{key:"entries",value:function(){var t;return((function(t){if(Array.isArray(t))return tA(t)})(t=tR(tF,this).entries())||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||tN(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).map(function(t){var e=function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o,a,s=[],l=!0,h=!1;try{o=(n=n.call(t)).next,!1;for(;!(l=(r=o.call(n)).done)&&(s.push(r.value),s.length!==e);l=!0);}catch(t){h=!0,i=t}finally{try{if(!l&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(h)throw i}}return s}}(t,2)||tN(t,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),n=e[0];return[e[1],n]})}},{key:"id",value:function(t){return tI(tB,this,tT(t)),this}},{key:"onCreateObj",value:function(t){return tI(tU,this,t),this}},{key:"onUpdateObj",value:function(t){return tI(tq,this,t),this}},{key:"onRemoveObj",value:function(t){return tI(t$,this,t),this}},{key:"digest",value:function(t){var e=this;t.filter(function(t){return!tR(tz,e).has(tR(tB,e).call(e,t))}).forEach(function(t){var n=tR(tU,e).call(e,t);tR(tz,e).set(tR(tB,e).call(e,t),n),tR(tF,e).set(n,t)});var n=new Map(t.map(function(t){return[tR(tB,e).call(e,t),t]}));return tR(tz,this).forEach(function(t,r){n.has(r)?tR(tq,e).call(e,t,n.get(r)):(tR(t$,e).call(e,t,r),tR(tz,e).delete(r),tR(tF,e).delete(t))}),this}},{key:"clear",value:function(){return this.digest([]),this}}],function(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e);if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==typeof e?e:e+""}(r.key),r)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}();class tV extends Map{constructor(t,e=tW){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(let[e,n]of t)this.set(e,n)}get(t){return super.get(tY(this,t))}has(t){return super.has(tY(this,t))}set(t,e){return super.set(function({_intern:t,_key:e},n){let r=e(n);return t.has(r)?t.get(r):(t.set(r,n),n)}(this,t),e)}delete(t){return super.delete(function({_intern:t,_key:e},n){let r=e(n);return t.has(r)&&(n=t.get(r),t.delete(r)),n}(this,t))}}function tY({_intern:t,_key:e},n){let r=e(n);return t.has(r)?t.get(r):n}function tW(t){return null!==t&&"object"==typeof t?t.valueOf():t}function tZ(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}let tG=Symbol("implicit"),tK=function(t){for(var e=t.length/6|0,n=Array(e),r=0;r<e;)n[r]="#"+t.slice(6*r,6*++r);return n}("a6cee31f78b4b2df8a33a02cfb9a99e31a1cfdbf6fff7f00cab2d66a3d9affff99b15928");function tX(t){return(tX="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var tQ=/^\s+/,tJ=/\s+$/;function t0(t,e){if(e=e||{},(t=t||"")instanceof t0)return t;if(!(this instanceof t0))return new t0(t,e);var n,r,i,o,a,s,l,h,u,c,d,f,p,g,_,m,v,y,b,w,x=(r={r:0,g:0,b:0},i=1,o=null,a=null,s=null,l=!1,h=!1,"string"==typeof(n=t)&&(n=function(t){t=t.replace(tQ,"").replace(tJ,"").toLowerCase();var e,n=!1;if(es[t])t=es[t],n=!0;else if("transparent"==t)return{r:0,g:0,b:0,a:0,format:"name"};return(e=e_.rgb.exec(t))?{r:e[1],g:e[2],b:e[3]}:(e=e_.rgba.exec(t))?{r:e[1],g:e[2],b:e[3],a:e[4]}:(e=e_.hsl.exec(t))?{h:e[1],s:e[2],l:e[3]}:(e=e_.hsla.exec(t))?{h:e[1],s:e[2],l:e[3],a:e[4]}:(e=e_.hsv.exec(t))?{h:e[1],s:e[2],v:e[3]}:(e=e_.hsva.exec(t))?{h:e[1],s:e[2],v:e[3],a:e[4]}:(e=e_.hex8.exec(t))?{r:ed(e[1]),g:ed(e[2]),b:ed(e[3]),a:ed(e[4])/255,format:n?"name":"hex8"}:(e=e_.hex6.exec(t))?{r:ed(e[1]),g:ed(e[2]),b:ed(e[3]),format:n?"name":"hex"}:(e=e_.hex4.exec(t))?{r:ed(e[1]+""+e[1]),g:ed(e[2]+""+e[2]),b:ed(e[3]+""+e[3]),a:ed(e[4]+""+e[4])/255,format:n?"name":"hex8"}:!!(e=e_.hex3.exec(t))&&{r:ed(e[1]+""+e[1]),g:ed(e[2]+""+e[2]),b:ed(e[3]+""+e[3]),format:n?"name":"hex"}}(n)),"object"==tX(n)&&(em(n.r)&&em(n.g)&&em(n.b)?(u=n.r,c=n.g,d=n.b,r={r:255*eu(u,255),g:255*eu(c,255),b:255*eu(d,255)},l=!0,h="%"===String(n.r).substr(-1)?"prgb":"rgb"):em(n.h)&&em(n.s)&&em(n.v)?(o=ep(n.s),a=ep(n.v),f=n.h,p=o,g=a,f=6*eu(f,360),p=eu(p,100),g=eu(g,100),_=Math.floor(f),m=f-_,v=g*(1-p),y=g*(1-m*p),b=g*(1-(1-m)*p),r={r:255*[g,y,v,v,b,g][w=_%6],g:255*[b,g,g,y,v,v][w],b:255*[v,v,b,g,g,y][w]},l=!0,h="hsv"):em(n.h)&&em(n.s)&&em(n.l)&&(o=ep(n.s),s=ep(n.l),r=function(t,e,n){var r,i,o;function a(t,e,n){return(n<0&&(n+=1),n>1&&(n-=1),n<1/6)?t+(e-t)*6*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}if(t=eu(t,360),e=eu(e,100),n=eu(n,100),0===e)r=i=o=n;else{var s=n<.5?n*(1+e):n+e-n*e,l=2*n-s;r=a(l,s,t+1/3),i=a(l,s,t),o=a(l,s,t-1/3)}return{r:255*r,g:255*i,b:255*o}}(n.h,o,s),l=!0,h="hsl"),n.hasOwnProperty("a")&&(i=n.a)),i=eh(i),{ok:l,format:n.format||h,r:Math.min(255,Math.max(r.r,0)),g:Math.min(255,Math.max(r.g,0)),b:Math.min(255,Math.max(r.b,0)),a:i});this._originalInput=t,this._r=x.r,this._g=x.g,this._b=x.b,this._a=x.a,this._roundA=Math.round(100*this._a)/100,this._format=e.format||x.format,this._gradientType=e.gradientType,this._r<1&&(this._r=Math.round(this._r)),this._g<1&&(this._g=Math.round(this._g)),this._b<1&&(this._b=Math.round(this._b)),this._ok=x.ok}function t1(t,e,n){t=eu(t,255);var r,i,o=Math.max(t,e=eu(e,255),n=eu(n,255)),a=Math.min(t,e,n),s=(o+a)/2;if(o==a)r=i=0;else{var l=o-a;switch(i=s>.5?l/(2-o-a):l/(o+a),o){case t:r=(e-n)/l+6*(e<n);break;case e:r=(n-t)/l+2;break;case n:r=(t-e)/l+4}r/=6}return{h:r,s:i,l:s}}function t2(t,e,n){t=eu(t,255);var r,i=Math.max(t,e=eu(e,255),n=eu(n,255)),o=Math.min(t,e,n),a=i-o;if(i==o)r=0;else{switch(i){case t:r=(e-n)/a+6*(e<n);break;case e:r=(n-t)/a+2;break;case n:r=(t-e)/a+4}r/=6}return{h:r,s:0===i?0:a/i,v:i}}function t5(t,e,n,r){var i=[ef(Math.round(t).toString(16)),ef(Math.round(e).toString(16)),ef(Math.round(n).toString(16))];return r&&i[0].charAt(0)==i[0].charAt(1)&&i[1].charAt(0)==i[1].charAt(1)&&i[2].charAt(0)==i[2].charAt(1)?i[0].charAt(0)+i[1].charAt(0)+i[2].charAt(0):i.join("")}function t3(t,e,n,r){return[ef(eg(r)),ef(Math.round(t).toString(16)),ef(Math.round(e).toString(16)),ef(Math.round(n).toString(16))].join("")}function t6(t,e){e=0===e?0:e||10;var n=t0(t).toHsl();return n.s-=e/100,n.s=ec(n.s),t0(n)}function t8(t,e){e=0===e?0:e||10;var n=t0(t).toHsl();return n.s+=e/100,n.s=ec(n.s),t0(n)}function t9(t){return t0(t).desaturate(100)}function t4(t,e){e=0===e?0:e||10;var n=t0(t).toHsl();return n.l+=e/100,n.l=ec(n.l),t0(n)}function t7(t,e){e=0===e?0:e||10;var n=t0(t).toRgb();return n.r=Math.max(0,Math.min(255,n.r-Math.round(-(e/100*255)))),n.g=Math.max(0,Math.min(255,n.g-Math.round(-(e/100*255)))),n.b=Math.max(0,Math.min(255,n.b-Math.round(-(e/100*255)))),t0(n)}function et(t,e){e=0===e?0:e||10;var n=t0(t).toHsl();return n.l-=e/100,n.l=ec(n.l),t0(n)}function ee(t,e){var n=t0(t).toHsl(),r=(n.h+e)%360;return n.h=r<0?360+r:r,t0(n)}function en(t){var e=t0(t).toHsl();return e.h=(e.h+180)%360,t0(e)}function er(t,e){if(isNaN(e)||e<=0)throw Error("Argument to polyad must be a positive number");for(var n=t0(t).toHsl(),r=[t0(t)],i=360/e,o=1;o<e;o++)r.push(t0({h:(n.h+o*i)%360,s:n.s,l:n.l}));return r}function ei(t){var e=t0(t).toHsl(),n=e.h;return[t0(t),t0({h:(n+72)%360,s:e.s,l:e.l}),t0({h:(n+216)%360,s:e.s,l:e.l})]}function eo(t,e,n){e=e||6,n=n||30;var r=t0(t).toHsl(),i=360/n,o=[t0(t)];for(r.h=(r.h-(i*e>>1)+720)%360;--e;)r.h=(r.h+i)%360,o.push(t0(r));return o}function ea(t,e){e=e||6;for(var n=t0(t).toHsv(),r=n.h,i=n.s,o=n.v,a=[],s=1/e;e--;)a.push(t0({h:r,s:i,v:o})),o=(o+s)%1;return a}t0.prototype={isDark:function(){return 128>this.getBrightness()},isLight:function(){return!this.isDark()},isValid:function(){return this._ok},getOriginalInput:function(){return this._originalInput},getFormat:function(){return this._format},getAlpha:function(){return this._a},getBrightness:function(){var t=this.toRgb();return(299*t.r+587*t.g+114*t.b)/1e3},getLuminance:function(){var t,e,n,r,i,o,a=this.toRgb();return t=a.r/255,e=a.g/255,.2126*(t<=.03928?t/12.92:Math.pow((t+.055)/1.055,2.4))+.7152*(e<=.03928?e/12.92:Math.pow((e+.055)/1.055,2.4))+.0722*((n=a.b/255)<=.03928?n/12.92:Math.pow((n+.055)/1.055,2.4))},setAlpha:function(t){return this._a=eh(t),this._roundA=Math.round(100*this._a)/100,this},toHsv:function(){var t=t2(this._r,this._g,this._b);return{h:360*t.h,s:t.s,v:t.v,a:this._a}},toHsvString:function(){var t=t2(this._r,this._g,this._b),e=Math.round(360*t.h),n=Math.round(100*t.s),r=Math.round(100*t.v);return 1==this._a?"hsv("+e+", "+n+"%, "+r+"%)":"hsva("+e+", "+n+"%, "+r+"%, "+this._roundA+")"},toHsl:function(){var t=t1(this._r,this._g,this._b);return{h:360*t.h,s:t.s,l:t.l,a:this._a}},toHslString:function(){var t=t1(this._r,this._g,this._b),e=Math.round(360*t.h),n=Math.round(100*t.s),r=Math.round(100*t.l);return 1==this._a?"hsl("+e+", "+n+"%, "+r+"%)":"hsla("+e+", "+n+"%, "+r+"%, "+this._roundA+")"},toHex:function(t){return t5(this._r,this._g,this._b,t)},toHexString:function(t){return"#"+this.toHex(t)},toHex8:function(t){var e,n,r,i,o,a;return e=this._r,n=this._g,r=this._b,i=this._a,o=t,a=[ef(Math.round(e).toString(16)),ef(Math.round(n).toString(16)),ef(Math.round(r).toString(16)),ef(eg(i))],o&&a[0].charAt(0)==a[0].charAt(1)&&a[1].charAt(0)==a[1].charAt(1)&&a[2].charAt(0)==a[2].charAt(1)&&a[3].charAt(0)==a[3].charAt(1)?a[0].charAt(0)+a[1].charAt(0)+a[2].charAt(0)+a[3].charAt(0):a.join("")},toHex8String:function(t){return"#"+this.toHex8(t)},toRgb:function(){return{r:Math.round(this._r),g:Math.round(this._g),b:Math.round(this._b),a:this._a}},toRgbString:function(){return 1==this._a?"rgb("+Math.round(this._r)+", "+Math.round(this._g)+", "+Math.round(this._b)+")":"rgba("+Math.round(this._r)+", "+Math.round(this._g)+", "+Math.round(this._b)+", "+this._roundA+")"},toPercentageRgb:function(){return{r:Math.round(100*eu(this._r,255))+"%",g:Math.round(100*eu(this._g,255))+"%",b:Math.round(100*eu(this._b,255))+"%",a:this._a}},toPercentageRgbString:function(){return 1==this._a?"rgb("+Math.round(100*eu(this._r,255))+"%, "+Math.round(100*eu(this._g,255))+"%, "+Math.round(100*eu(this._b,255))+"%)":"rgba("+Math.round(100*eu(this._r,255))+"%, "+Math.round(100*eu(this._g,255))+"%, "+Math.round(100*eu(this._b,255))+"%, "+this._roundA+")"},toName:function(){return 0===this._a?"transparent":!(this._a<1)&&(el[t5(this._r,this._g,this._b,!0)]||!1)},toFilter:function(t){var e="#"+t3(this._r,this._g,this._b,this._a),n=e,r=this._gradientType?"GradientType = 1, ":"";if(t){var i=t0(t);n="#"+t3(i._r,i._g,i._b,i._a)}return"progid:DXImageTransform.Microsoft.gradient("+r+"startColorstr="+e+",endColorstr="+n+")"},toString:function(t){var e=!!t;t=t||this._format;var n=!1,r=this._a<1&&this._a>=0;return!e&&r&&("hex"===t||"hex6"===t||"hex3"===t||"hex4"===t||"hex8"===t||"name"===t)?"name"===t&&0===this._a?this.toName():this.toRgbString():("rgb"===t&&(n=this.toRgbString()),"prgb"===t&&(n=this.toPercentageRgbString()),("hex"===t||"hex6"===t)&&(n=this.toHexString()),"hex3"===t&&(n=this.toHexString(!0)),"hex4"===t&&(n=this.toHex8String(!0)),"hex8"===t&&(n=this.toHex8String()),"name"===t&&(n=this.toName()),"hsl"===t&&(n=this.toHslString()),"hsv"===t&&(n=this.toHsvString()),n||this.toHexString())},clone:function(){return t0(this.toString())},_applyModification:function(t,e){var n=t.apply(null,[this].concat([].slice.call(e)));return this._r=n._r,this._g=n._g,this._b=n._b,this.setAlpha(n._a),this},lighten:function(){return this._applyModification(t4,arguments)},brighten:function(){return this._applyModification(t7,arguments)},darken:function(){return this._applyModification(et,arguments)},desaturate:function(){return this._applyModification(t6,arguments)},saturate:function(){return this._applyModification(t8,arguments)},greyscale:function(){return this._applyModification(t9,arguments)},spin:function(){return this._applyModification(ee,arguments)},_applyCombination:function(t,e){return t.apply(null,[this].concat([].slice.call(e)))},analogous:function(){return this._applyCombination(eo,arguments)},complement:function(){return this._applyCombination(en,arguments)},monochromatic:function(){return this._applyCombination(ea,arguments)},splitcomplement:function(){return this._applyCombination(ei,arguments)},triad:function(){return this._applyCombination(er,[3])},tetrad:function(){return this._applyCombination(er,[4])}},t0.fromRatio=function(t,e){if("object"==tX(t)){var n={};for(var r in t)t.hasOwnProperty(r)&&("a"===r?n[r]=t[r]:n[r]=ep(t[r]));t=n}return t0(t,e)},t0.equals=function(t,e){return!!t&&!!e&&t0(t).toRgbString()==t0(e).toRgbString()},t0.random=function(){return t0.fromRatio({r:Math.random(),g:Math.random(),b:Math.random()})},t0.mix=function(t,e,n){n=0===n?0:n||50;var r=t0(t).toRgb(),i=t0(e).toRgb(),o=n/100;return t0({r:(i.r-r.r)*o+r.r,g:(i.g-r.g)*o+r.g,b:(i.b-r.b)*o+r.b,a:(i.a-r.a)*o+r.a})},t0.readability=function(t,e){var n=t0(t),r=t0(e);return(Math.max(n.getLuminance(),r.getLuminance())+.05)/(Math.min(n.getLuminance(),r.getLuminance())+.05)},t0.isReadable=function(t,e,n){var r,i,o,a,s,l=t0.readability(t,e);switch(s=!1,(i=((r=(r=n)||{level:"AA",size:"small"}).level||"AA").toUpperCase(),o=(r.size||"small").toLowerCase(),"AA"!==i&&"AAA"!==i&&(i="AA"),"small"!==o&&"large"!==o&&(o="small"),a={level:i,size:o}).level+a.size){case"AAsmall":case"AAAlarge":s=l>=4.5;break;case"AAlarge":s=l>=3;break;case"AAAsmall":s=l>=7}return s},t0.mostReadable=function(t,e,n){var r,i,o,a,s=null,l=0;i=(n=n||{}).includeFallbackColors,o=n.level,a=n.size;for(var h=0;h<e.length;h++)(r=t0.readability(t,e[h]))>l&&(l=r,s=t0(e[h]));return t0.isReadable(t,s,{level:o,size:a})||!i?s:(n.includeFallbackColors=!1,t0.mostReadable(t,["#fff","#000"],n))};var es=t0.names={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"0ff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000",blanchedalmond:"ffebcd",blue:"00f",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",burntsienna:"ea7e5d",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"0ff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkgrey:"a9a9a9",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"f0f",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",grey:"808080",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgreen:"90ee90",lightgrey:"d3d3d3",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"789",lightslategrey:"789",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"0f0",limegreen:"32cd32",linen:"faf0e6",magenta:"f0f",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370db",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"db7093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"f00",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"fff",whitesmoke:"f5f5f5",yellow:"ff0",yellowgreen:"9acd32"},el=t0.hexNames=function(t){var e={};for(var n in t)t.hasOwnProperty(n)&&(e[t[n]]=n);return e}(es);function eh(t){return(isNaN(t=parseFloat(t))||t<0||t>1)&&(t=1),t}function eu(t,e){"string"==typeof(n=t)&&-1!=n.indexOf(".")&&1===parseFloat(n)&&(t="100%");var n,r,i="string"==typeof(r=t)&&-1!=r.indexOf("%");return(t=Math.min(e,Math.max(0,parseFloat(t))),i&&(t=parseInt(t*e,10)/100),1e-6>Math.abs(t-e))?1:t%e/parseFloat(e)}function ec(t){return Math.min(1,Math.max(0,t))}function ed(t){return parseInt(t,16)}function ef(t){return 1==t.length?"0"+t:""+t}function ep(t){return t<=1&&(t=100*t+"%"),t}function eg(t){return Math.round(255*parseFloat(t)).toString(16)}var e_=function(){var t="(?:[-\\+]?\\d*\\.\\d+%?)|(?:[-\\+]?\\d+%?)",e="[\\s|\\(]+("+t+")[,|\\s]+("+t+")[,|\\s]+("+t+")\\s*\\)?",n="[\\s|\\(]+("+t+")[,|\\s]+("+t+")[,|\\s]+("+t+")[,|\\s]+("+t+")\\s*\\)?";return{CSS_UNIT:new RegExp(t),rgb:RegExp("rgb"+e),rgba:RegExp("rgba"+n),hsl:RegExp("hsl"+e),hsla:RegExp("hsla"+n),hsv:RegExp("hsv"+e),hsva:RegExp("hsva"+n),hex3:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,hex4:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex8:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/}}();function em(t){return!!e_.CSS_UNIT.exec(t)}function ev(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function ey(t,e,n){if("function"==typeof t?t===e:t.has(e))return arguments.length<3?e:n;throw TypeError("Private element is not present on this object")}function eb(t,e,n){return e=ej(e),function(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t,eT()?Reflect.construct(e,n||[],ej(t).constructor):e.apply(t,n))}function ew(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}function ex(t,e){return t.get(ey(t,e))}function ek(t,e,n){(function(t,e){if(e.has(t))throw TypeError("Cannot initialize the same private elements twice on an object")})(t,e),e.set(t,n)}function eP(t,e,n){return t.set(ey(t,e),n),n}function eE(t,e,n){if(eT())return Reflect.construct.apply(null,arguments);var r=[null];return r.push.apply(r,e),new(t.bind.apply(t,r))}function eO(t,e,n){return e&&function(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,eN(r.key),r)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function eM(t,e,n){return(e=eN(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function eS(){return(eS="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,n){var r=function(t,e){for(;!({}).hasOwnProperty.call(t,e)&&null!==(t=ej(t)););return t}(t,e);if(r){var i=Object.getOwnPropertyDescriptor(r,e);return i.get?i.get.call(arguments.length<3?t:n):i.value}}).apply(null,arguments)}function ej(t){return(ej=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function eC(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&eD(t,e)}function eT(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(eT=function(){return!!t})()}function eA(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function eD(t,e){return(eD=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function eR(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o,a,s=[],l=!0,h=!1;try{if(o=(n=n.call(t)).next,0===e);else for(;!(l=(r=o.call(n)).done)&&(s.push(r.value),s.length!==e);l=!0);}catch(t){h=!0,i=t}finally{try{if(!l&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(h)throw i}}return s}}(t,e)||eF(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function eL(t,e,n,r){var i=eS(ej(t.prototype),e,n);return"function"==typeof i?function(t){return i.apply(n,t)}:i}function eI(t){return function(t){if(Array.isArray(t))return ev(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||eF(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function eN(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e);if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}function ez(t){return(ez="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function eF(t,e){if(t){if("string"==typeof t)return ev(t,e);var n=({}).toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?ev(t,e):void 0}}var eB=function(t){t instanceof Array?t.forEach(eB):(t.map&&t.map.dispose(),t.dispose())},eU=function(t){t.geometry&&t.geometry.dispose(),t.material&&eB(t.material),t.texture&&t.texture.dispose(),t.children&&t.children.forEach(eU)},eq=function(t){for(;t.children.length;){var e=t.children[0];t.remove(e),eU(e)}},e$=new WeakMap,eH=new WeakMap,eV=function(t){function e(t){var n,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=r.dataBindAttr,o=r.objBindAttr;return ew(this,e),eM(n=eb(this,e),"scene",void 0),ek(n,e$,void 0),ek(n,eH,void 0),n.scene=t,eP(e$,n,void 0===i?"__data":i),eP(eH,n,void 0===o?"__threeObj":o),n.onRemoveObj(function(){}),n}return eC(e,t),eO(e,[{key:"onCreateObj",value:function(t){var n=this;return eL(e,"onCreateObj",this)([function(e){var r=t(e);return e[ex(eH,n)]=r,r[ex(e$,n)]=e,n.scene.add(r),r}]),this}},{key:"onRemoveObj",value:function(t){var n=this;return eL(e,"onRemoveObj",this)([function(r,i){var o=eL(e,"getData",n)([r]);t(r,i),n.scene.remove(r),eq(r),delete o[ex(eH,n)]}]),this}}])}(tH),eY=function(t){return isNaN(t)?parseInt(t0(t).toHex(),16):t},eW=function(t){return isNaN(t)?t0(t).getAlpha():1},eZ=function t(){var e=new tV,n=[],r=[],i=tG;function o(t){let o=e.get(t);if(void 0===o){if(i!==tG)return i;e.set(t,o=n.push(t)-1)}return r[o%r.length]}return o.domain=function(t){if(!arguments.length)return n.slice();for(let r of(n=[],e=new tV,t))e.has(r)||e.set(r,n.push(r)-1);return o},o.range=function(t){return arguments.length?(r=Array.from(t),o):r.slice()},o.unknown=function(t){return arguments.length?(i=t,o):i},o.copy=function(){return t(n,r).unknown(i)},tZ.apply(o,arguments),o}(tK);function eG(t,e,n){e&&"string"==typeof n&&t.filter(function(t){return!t[n]}).forEach(function(t){t[n]=eZ(e(t))})}var eK=window.THREE?window.THREE:{Group:k.YJl,Mesh:k.eaF,MeshLambertMaterial:k.G_z,Color:k.Q1f,BufferGeometry:k.LoY,BufferAttribute:k.THS,Matrix4:k.kn4,Vector3:k.Pq0,SphereGeometry:k.Gu$,CylinderGeometry:k.Ho_,TubeGeometry:k.j6,ConeGeometry:k.qFE,Line:k.N1A,LineBasicMaterial:k.mrM,QuadraticBezierCurve3:k.CV9,CubicBezierCurve3:k.s0K,Box3:k.NRn},eX={graph:J,forcelayout:tt},eQ=new eK.BufferGeometry().setAttribute?"setAttribute":"addAttribute",eJ=new eK.BufferGeometry().applyMatrix4?"applyMatrix4":"applyMatrix",e0=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Object,n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=function(e){function r(){var e;ew(this,r);for(var i=arguments.length,o=Array(i),a=0;a<i;a++)o[a]=arguments[a];return(e=eb(this,r,[].concat(o))).__kapsuleInstance=eE(t,[].concat(eI(n?[e]:[]),o)),e}return eC(r,e),eO(r)}(e);return Object.keys(t()).forEach(function(t){return r.prototype[t]=function(){var e,n=(e=this.__kapsuleInstance)[t].apply(e,arguments);return n===this.__kapsuleInstance?this:n}}),r}(tC({props:{jsonUrl:{onChange:function(t,e){var n=this;t&&!e.fetchingJson&&(e.fetchingJson=!0,e.onLoading(),fetch(t).then(function(t){return t.json()}).then(function(t){e.fetchingJson=!1,e.onFinishLoading(t),n.graphData(t)}))},triggerUpdate:!1},graphData:{default:{nodes:[],links:[]},onChange:function(t,e){e.engineRunning=!1}},numDimensions:{default:3,onChange:function(t,e){var n=e.d3ForceLayout.force("charge");function r(t,e){t.forEach(function(t){delete t[e],delete t["v".concat(e)]})}n&&n.strength(t>2?-60:-30),t<3&&r(e.graphData.nodes,"z"),t<2&&r(e.graphData.nodes,"y")}},dagMode:{onChange:function(t,e){t||"d3"!==e.forceEngine||(e.graphData.nodes||[]).forEach(function(t){return t.fx=t.fy=t.fz=void 0})}},dagLevelDistance:{},dagNodeFilter:{default:function(t){return!0}},onDagError:{triggerUpdate:!1},nodeRelSize:{default:4},nodeId:{default:"id"},nodeVal:{default:"val"},nodeResolution:{default:8},nodeColor:{default:"color"},nodeAutoColorBy:{},nodeOpacity:{default:.75},nodeVisibility:{default:!0},nodeThreeObject:{},nodeThreeObjectExtend:{default:!1},nodePositionUpdate:{triggerUpdate:!1},linkSource:{default:"source"},linkTarget:{default:"target"},linkVisibility:{default:!0},linkColor:{default:"color"},linkAutoColorBy:{},linkOpacity:{default:.2},linkWidth:{},linkResolution:{default:6},linkCurvature:{default:0,triggerUpdate:!1},linkCurveRotation:{default:0,triggerUpdate:!1},linkMaterial:{},linkThreeObject:{},linkThreeObjectExtend:{default:!1},linkPositionUpdate:{triggerUpdate:!1},linkDirectionalArrowLength:{default:0},linkDirectionalArrowColor:{},linkDirectionalArrowRelPos:{default:.5,triggerUpdate:!1},linkDirectionalArrowResolution:{default:8},linkDirectionalParticles:{default:0},linkDirectionalParticleSpeed:{default:.01,triggerUpdate:!1},linkDirectionalParticleWidth:{default:.5},linkDirectionalParticleColor:{},linkDirectionalParticleResolution:{default:4},forceEngine:{default:"d3"},d3AlphaMin:{default:0},d3AlphaDecay:{default:.0228,triggerUpdate:!1,onChange:function(t,e){e.d3ForceLayout.alphaDecay(t)}},d3AlphaTarget:{default:0,triggerUpdate:!1,onChange:function(t,e){e.d3ForceLayout.alphaTarget(t)}},d3VelocityDecay:{default:.4,triggerUpdate:!1,onChange:function(t,e){e.d3ForceLayout.velocityDecay(t)}},ngraphPhysics:{default:{timeStep:20,gravity:-1.2,theta:.8,springLength:30,springCoefficient:8e-4,dragCoefficient:.02}},warmupTicks:{default:0,triggerUpdate:!1},cooldownTicks:{default:1/0,triggerUpdate:!1},cooldownTime:{default:15e3,triggerUpdate:!1},onLoading:{default:function(){},triggerUpdate:!1},onFinishLoading:{default:function(){},triggerUpdate:!1},onUpdate:{default:function(){},triggerUpdate:!1},onFinishUpdate:{default:function(){},triggerUpdate:!1},onEngineTick:{default:function(){},triggerUpdate:!1},onEngineStop:{default:function(){},triggerUpdate:!1}},methods:{refresh:function(t){return t._flushObjects=!0,t._rerender(),this},d3Force:function(t,e,n){return void 0===n?t.d3ForceLayout.force(e):(t.d3ForceLayout.force(e,n),this)},d3ReheatSimulation:function(t){return t.d3ForceLayout.alpha(1),this.resetCountdown(),this},resetCountdown:function(t){return t.cntTicks=0,t.startTickTime=new Date,t.engineRunning=!0,this},tickFrame:function(t){var e,n,r,i,o,a,s,l,h,u="ngraph"!==t.forceEngine;return t.engineRunning&&(++t.cntTicks>t.cooldownTicks||new Date-t.startTickTime>t.cooldownTime||u&&t.d3AlphaMin>0&&t.d3ForceLayout.alpha()<t.d3AlphaMin?(t.engineRunning=!1,t.onEngineStop()):(t.layout[u?"tick":"step"](),t.onEngineTick()),e=tT(t.nodeThreeObjectExtend),t.nodeDataMapper.entries().forEach(function(n){var r=eR(n,2),i=r[0],o=r[1];if(o){var a=u?i:t.layout.getNodePosition(i[t.nodeId]),s=e(i);t.nodePositionUpdate&&t.nodePositionUpdate(s?o.children[0]:o,{x:a.x,y:a.y,z:a.z},i)&&!s||(o.position.x=a.x,o.position.y=a.y||0,o.position.z=a.z||0)}}),n=tT(t.linkWidth),r=tT(t.linkCurvature),i=tT(t.linkCurveRotation),o=tT(t.linkThreeObjectExtend),t.linkDataMapper.entries().forEach(function(e){var a=eR(e,2),s=a[0],l=a[1];if(l){var h=u?s:t.layout.getLinkPosition(t.layout.graph.getLink(s.source,s.target).id),c=h[u?"source":"from"],d=h[u?"target":"to"];if(c&&d&&c.hasOwnProperty("x")&&d.hasOwnProperty("x")){!function(e){var n=u?e:t.layout.getLinkPosition(t.layout.graph.getLink(e.source,e.target).id),o=n[u?"source":"from"],a=n[u?"target":"to"];if(o&&a&&o.hasOwnProperty("x")&&a.hasOwnProperty("x")){var s=r(e);if(s){var l,h=new eK.Vector3(o.x,o.y||0,o.z||0),c=new eK.Vector3(a.x,a.y||0,a.z||0),d=h.distanceTo(c),f=i(e);if(d>0){var p=a.x-o.x,g=a.y-o.y||0,_=new eK.Vector3().subVectors(c,h),m=_.clone().multiplyScalar(s).cross(0!==p||0!==g?new eK.Vector3(0,0,1):new eK.Vector3(0,1,0)).applyAxisAngle(_.normalize(),f).add(new eK.Vector3().addVectors(h,c).divideScalar(2));l=new eK.QuadraticBezierCurve3(h,m,c)}else{var v=70*s,y=-f,b=y+Math.PI/2;l=new eK.CubicBezierCurve3(h,new eK.Vector3(v*Math.cos(b),v*Math.sin(b),0).add(h),new eK.Vector3(v*Math.cos(y),v*Math.sin(y),0).add(h),c)}e.__curve=l}else e.__curve=null}}(s);var f=o(s);if(!(t.linkPositionUpdate&&t.linkPositionUpdate(f?l.children[1]:l,{start:{x:c.x,y:c.y,z:c.z},end:{x:d.x,y:d.y,z:d.z}},s))||f){var p=s.__curve,g=l.children.length?l.children[0]:l;if("Line"===g.type){if(p){var _=p.getPoints(30);g.geometry.getAttribute("position").array.length!==3*_.length&&g.geometry[eQ]("position",new eK.BufferAttribute(new Float32Array(3*_.length),3)),g.geometry.setFromPoints(_)}else{var m=g.geometry.getAttribute("position");m&&m.array&&6===m.array.length||g.geometry[eQ]("position",m=new eK.BufferAttribute(new Float32Array(6),3)),m.array[0]=c.x,m.array[1]=c.y||0,m.array[2]=c.z||0,m.array[3]=d.x,m.array[4]=d.y||0,m.array[5]=d.z||0,m.needsUpdate=!0}g.geometry.computeBoundingSphere()}else if("Mesh"===g.type)if(p){g.geometry.type.match(/^Tube(Buffer)?Geometry$/)||(g.position.set(0,0,0),g.rotation.set(0,0,0),g.scale.set(1,1,1));var v=Math.ceil(10*n(s))/10,y=new eK.TubeGeometry(p,30,v/2,t.linkResolution,!1);g.geometry.dispose(),g.geometry=y}else{if(!g.geometry.type.match(/^Cylinder(Buffer)?Geometry$/)){var b=Math.ceil(10*n(s))/10/2,w=new eK.CylinderGeometry(b,b,1,t.linkResolution,1,!1);w[eJ](new eK.Matrix4().makeTranslation(0,.5,0)),w[eJ](new eK.Matrix4().makeRotationX(Math.PI/2)),g.geometry.dispose(),g.geometry=w}var x=new eK.Vector3(c.x,c.y||0,c.z||0),k=new eK.Vector3(d.x,d.y||0,d.z||0),P=x.distanceTo(k);g.position.x=x.x,g.position.y=x.y,g.position.z=x.z,g.scale.z=P,g.parent.localToWorld(k),g.lookAt(k)}}}}})),a=tT(t.linkDirectionalArrowRelPos),s=tT(t.linkDirectionalArrowLength),l=tT(t.nodeVal),t.arrowDataMapper.entries().forEach(function(e){var n=eR(e,2),r=n[0],i=n[1];if(i){var o=u?r:t.layout.getLinkPosition(t.layout.graph.getLink(r.source,r.target).id),h=o[u?"source":"from"],c=o[u?"target":"to"];if(h&&c&&h.hasOwnProperty("x")&&c.hasOwnProperty("x")){var d=Math.cbrt(Math.max(0,l(h)||1))*t.nodeRelSize,f=Math.cbrt(Math.max(0,l(c)||1))*t.nodeRelSize,p=s(r),g=a(r),_=r.__curve?function(t){return r.__curve.getPoint(t)}:function(t){var e=function(t,e,n,r){return e[t]+(n[t]-e[t])*r||0};return{x:e("x",h,c,t),y:e("y",h,c,t),z:e("z",h,c,t)}},m=r.__curve?r.__curve.getLength():Math.sqrt(["x","y","z"].map(function(t){return Math.pow((c[t]||0)-(h[t]||0),2)}).reduce(function(t,e){return t+e},0)),v=d+p+(m-d-f-p)*g,y=_(v/m),b=_((v-p)/m);["x","y","z"].forEach(function(t){return i.position[t]=b[t]});var w=eE(eK.Vector3,eI(["x","y","z"].map(function(t){return y[t]})));i.parent.localToWorld(w),i.lookAt(w)}}}),h=tT(t.linkDirectionalParticleSpeed),t.graphData.links.forEach(function(e){var n=t.particlesDataMapper.getObj(e),r=n&&n.children,i=e.__singleHopPhotonsObj&&e.__singleHopPhotonsObj.children;if(i&&i.length||r&&r.length){var o=u?e:t.layout.getLinkPosition(t.layout.graph.getLink(e.source,e.target).id),a=o[u?"source":"from"],s=o[u?"target":"to"];if(a&&s&&a.hasOwnProperty("x")&&s.hasOwnProperty("x")){var l=h(e),c=e.__curve?function(t){return e.__curve.getPoint(t)}:function(t){var e=function(t,e,n,r){return e[t]+(n[t]-e[t])*r||0};return{x:e("x",a,s,t),y:e("y",a,s,t),z:e("z",a,s,t)}};[].concat(eI(r||[]),eI(i||[])).forEach(function(t,e){var n="singleHopPhotons"===t.parent.__linkThreeObjType;if(t.hasOwnProperty("__progressRatio")||(t.__progressRatio=n?0:e/r.length),t.__progressRatio+=l,t.__progressRatio>=1)if(n){t.parent.remove(t),eq(t);return}else t.__progressRatio=t.__progressRatio%1;var i=c(t.__progressRatio);["x","y","z"].forEach(function(e){return t.position[e]=i[e]})})}}}),this},emitParticle:function(t,e){if(e&&t.graphData.links.includes(e)){if(!e.__singleHopPhotonsObj){var n=new eK.Group;n.__linkThreeObjType="singleHopPhotons",e.__singleHopPhotonsObj=n,t.graphScene.add(n)}var r=Math.ceil(10*tT(t.linkDirectionalParticleWidth)(e))/10/2,i=t.linkDirectionalParticleResolution,o=new eK.SphereGeometry(r,i,i),a=tT(t.linkColor),s=tT(t.linkDirectionalParticleColor)(e)||a(e)||"#f0f0f0",l=new eK.Color(eY(s)),h=3*t.linkOpacity,u=new eK.MeshLambertMaterial({color:l,transparent:!0,opacity:h});e.__singleHopPhotonsObj.add(new eK.Mesh(o,u))}return this},getGraphBbox:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(){return!0};if(!t.initialised)return null;var n=function t(n){var r=[];if(n.geometry){n.geometry.computeBoundingBox();var i=new eK.Box3;i.copy(n.geometry.boundingBox).applyMatrix4(n.matrixWorld),r.push(i)}return r.concat.apply(r,eI((n.children||[]).filter(function(t){return!t.hasOwnProperty("__graphObjType")||"node"===t.__graphObjType&&e(t.__data)}).map(t)))}(t.graphScene);return n.length?Object.assign.apply(Object,eI(["x","y","z"].map(function(t){return eM({},t,[function(t,e){let n;if(void 0===e)for(let e of t)null!=e&&(n>e||void 0===n&&e>=e)&&(n=e);else{let r=-1;for(let i of t)null!=(i=e(i,++r,t))&&(n>i||void 0===n&&i>=i)&&(n=i)}return n}(n,function(e){return e.min[t]}),function(t,e){let n;if(void 0===e)for(let e of t)null!=e&&(n<e||void 0===n&&e>=e)&&(n=e);else{let r=-1;for(let i of t)null!=(i=e(i,++r,t))&&(n<i||void 0===n&&i>=i)&&(n=i)}return n}(n,function(e){return e.max[t]})])}))):null}},stateInit:function(){return{d3ForceLayout:(function(t,e){let n;var r,i=Math.min(3,Math.max(1,Math.round(e=e||2))),o=1,a=.001,s=1-Math.pow(.001,1/300),l=0,h=.6,u=new Map,c=(0,E.O1)(p),d=(0,P.A)("tick","end"),f=(n=1,()=>(n=(1664525*n+0x3c6ef35f)%0x100000000)/0x100000000);function p(){g(),d.call("tick",r),o<a&&(c.stop(),d.call("end",r))}function g(e){var n,a,c=t.length;void 0===e&&(e=1);for(var d=0;d<e;++d)for(o+=(l-o)*s,u.forEach(function(t){t(o)}),n=0;n<c;++n)null==(a=t[n]).fx?a.x+=a.vx*=h:(a.x=a.fx,a.vx=0),i>1&&(null==a.fy?a.y+=a.vy*=h:(a.y=a.fy,a.vy=0)),i>2&&(null==a.fz?a.z+=a.vz*=h:(a.z=a.fz,a.vz=0));return r}function _(){for(var e,n=0,r=t.length;n<r;++n){if((e=t[n]).index=n,null!=e.fx&&(e.x=e.fx),null!=e.fy&&(e.y=e.fy),null!=e.fz&&(e.z=e.fz),isNaN(e.x)||i>1&&isNaN(e.y)||i>2&&isNaN(e.z)){var o=10*(i>2?Math.cbrt(.5+n):i>1?Math.sqrt(.5+n):n),a=n*j,s=n*C;1===i?e.x=o:2===i?(e.x=o*Math.cos(a),e.y=o*Math.sin(a)):(e.x=o*Math.sin(a)*Math.cos(s),e.y=o*Math.cos(a),e.z=o*Math.sin(a)*Math.sin(s))}(isNaN(e.vx)||i>1&&isNaN(e.vy)||i>2&&isNaN(e.vz))&&(e.vx=0,i>1&&(e.vy=0),i>2&&(e.vz=0))}}function m(e){return e.initialize&&e.initialize(t,f,i),e}return null==t&&(t=[]),_(),r={tick:g,restart:function(){return c.restart(p),r},stop:function(){return c.stop(),r},numDimensions:function(t){return arguments.length?(i=Math.min(3,Math.max(1,Math.round(t))),u.forEach(m),r):i},nodes:function(e){return arguments.length?(t=e,_(),u.forEach(m),r):t},alpha:function(t){return arguments.length?(o=+t,r):o},alphaMin:function(t){return arguments.length?(a=+t,r):a},alphaDecay:function(t){return arguments.length?(s=+t,r):+s},alphaTarget:function(t){return arguments.length?(l=+t,r):l},velocityDecay:function(t){return arguments.length?(h=1-t,r):1-h},randomSource:function(t){return arguments.length?(f=t,u.forEach(m),r):f},force:function(t,e){return arguments.length>1?(null==e?u.delete(t):u.set(t,m(e)),r):u.get(t)},find:function(){var e,n,r,o,a,s,l=Array.prototype.slice.call(arguments),h=l.shift()||0,u=(i>1?l.shift():null)||0,c=(i>2?l.shift():null)||0,d=l.shift()||1/0,f=0,p=t.length;for(d*=d,f=0;f<p;++f)e=h-(a=t[f]).x,(o=e*e+(n=u-(a.y||0))*n+(r=c-(a.z||0))*r)<d&&(s=a,d=o);return s},on:function(t,e){return arguments.length>1?(d.on(t,e),r):d.on(t)}}})().force("link",function(t){var e,n,r,i,o,a,s,l=D,h=function(t){return 1/Math.min(o[t.source.index],o[t.target.index])},u=T(30),c=1;function d(r){for(var o=0,l=t.length;o<c;++o)for(var h,u,d,f,p,g=0,_=0,m=0,v=0;g<l;++g)u=(h=t[g]).source,_=(d=h.target).x+d.vx-u.x-u.vx||A(s),i>1&&(m=d.y+d.vy-u.y-u.vy||A(s)),i>2&&(v=d.z+d.vz-u.z-u.vz||A(s)),f=((f=Math.sqrt(_*_+m*m+v*v))-n[g])/f*r*e[g],_*=f,m*=f,v*=f,d.vx-=_*(p=a[g]),i>1&&(d.vy-=m*p),i>2&&(d.vz-=v*p),u.vx+=_*(p=1-p),i>1&&(u.vy+=m*p),i>2&&(u.vz+=v*p)}function f(){if(r){var i,s,h=r.length,u=t.length,c=new Map(r.map((t,e)=>[l(t,e,r),t]));for(i=0,o=Array(h);i<u;++i)(s=t[i]).index=i,"object"!=typeof s.source&&(s.source=R(c,s.source)),"object"!=typeof s.target&&(s.target=R(c,s.target)),o[s.source.index]=(o[s.source.index]||0)+1,o[s.target.index]=(o[s.target.index]||0)+1;for(i=0,a=Array(u);i<u;++i)s=t[i],a[i]=o[s.source.index]/(o[s.source.index]+o[s.target.index]);e=Array(u),p(),n=Array(u),g()}}function p(){if(r)for(var n=0,i=t.length;n<i;++n)e[n]=+h(t[n],n,t)}function g(){if(r)for(var e=0,i=t.length;e<i;++e)n[e]=+u(t[e],e,t)}return null==t&&(t=[]),d.initialize=function(t,...e){r=t,s=e.find(t=>"function"==typeof t)||Math.random,i=e.find(t=>[1,2,3].includes(t))||2,f()},d.links=function(e){return arguments.length?(t=e,f(),d):t},d.id=function(t){return arguments.length?(l=t,d):l},d.iterations=function(t){return arguments.length?(c=+t,d):c},d.strength=function(t){return arguments.length?(h="function"==typeof t?t:T(+t),p(),d):h},d.distance=function(t){return arguments.length?(u="function"==typeof t?t:T(+t),g(),d):u},d}()).force("charge",function(){var t,e,n,r,i,o,a=T(-30),s=1,l=1/0,h=.81;function u(r){var o,a=t.length,s=(1===e?z(t,O):2===e?(0,q.A)(t,O,M):3===e?G(t,O,M,S):null).visitAfter(d);for(i=r,o=0;o<a;++o)n=t[o],s.visit(f)}function c(){if(t){var e,n,r=t.length;for(e=0,o=Array(r);e<r;++e)o[(n=t[e]).index]=+a(n,e,t)}}function d(t){var n,r,i,a,s,l,h=0,u=0,c=t.length;if(c){for(i=a=s=l=0;l<c;++l)(n=t[l])&&(r=Math.abs(n.value))&&(h+=n.value,u+=r,i+=r*(n.x||0),a+=r*(n.y||0),s+=r*(n.z||0));h*=Math.sqrt(4/c),t.x=i/u,e>1&&(t.y=a/u),e>2&&(t.z=s/u)}else{(n=t).x=n.data.x,e>1&&(n.y=n.data.y),e>2&&(n.z=n.data.z);do h+=o[n.data.index];while(n=n.next)}t.value=h}function f(t,a,u,c,d){if(!t.value)return!0;var f=[u,c,d][e-1],p=t.x-n.x,g=e>1?t.y-n.y:0,_=e>2?t.z-n.z:0,m=f-a,v=p*p+g*g+_*_;if(m*m/h<v)return v<l&&(0===p&&(v+=(p=A(r))*p),e>1&&0===g&&(v+=(g=A(r))*g),e>2&&0===_&&(v+=(_=A(r))*_),v<s&&(v=Math.sqrt(s*v)),n.vx+=p*t.value*i/v,e>1&&(n.vy+=g*t.value*i/v),e>2&&(n.vz+=_*t.value*i/v)),!0;if(!t.length&&!(v>=l)){(t.data!==n||t.next)&&(0===p&&(v+=(p=A(r))*p),e>1&&0===g&&(v+=(g=A(r))*g),e>2&&0===_&&(v+=(_=A(r))*_),v<s&&(v=Math.sqrt(s*v)));do t.data!==n&&(m=o[t.data.index]*i/v,n.vx+=p*m,e>1&&(n.vy+=g*m),e>2&&(n.vz+=_*m));while(t=t.next)}}return u.initialize=function(n,...i){t=n,r=i.find(t=>"function"==typeof t)||Math.random,e=i.find(t=>[1,2,3].includes(t))||2,c()},u.strength=function(t){return arguments.length?(a="function"==typeof t?t:T(+t),c(),u):a},u.distanceMin=function(t){return arguments.length?(s=t*t,u):Math.sqrt(s)},u.distanceMax=function(t){return arguments.length?(l=t*t,u):Math.sqrt(l)},u.theta=function(t){return arguments.length?(h=t*t,u):Math.sqrt(h)},u}()).force("center",function(t,e,n){var r,i=1;function o(){var o,a,s=r.length,l=0,h=0,u=0;for(o=0;o<s;++o)l+=(a=r[o]).x||0,h+=a.y||0,u+=a.z||0;for(l=(l/s-t)*i,h=(h/s-e)*i,u=(u/s-n)*i,o=0;o<s;++o)a=r[o],l&&(a.x-=l),h&&(a.y-=h),u&&(a.z-=u)}return null==t&&(t=0),null==e&&(e=0),null==n&&(n=0),o.initialize=function(t){r=t},o.x=function(e){return arguments.length?(t=+e,o):t},o.y=function(t){return arguments.length?(e=+t,o):e},o.z=function(t){return arguments.length?(n=+t,o):n},o.strength=function(t){return arguments.length?(i=+t,o):i},o}()).force("dagRadial",null).stop(),engineRunning:!1}},init:function(t,e){e.graphScene=t,e.nodeDataMapper=new eV(t,{objBindAttr:"__threeObj"}),e.linkDataMapper=new eV(t,{objBindAttr:"__lineObj"}),e.arrowDataMapper=new eV(t,{objBindAttr:"__arrowObj"}),e.particlesDataMapper=new eV(t,{objBindAttr:"__photonsObj"})},update:function(t,e){var n=function(t){return t.some(function(t){return e.hasOwnProperty(t)})};if(t.engineRunning=!1,"function"==typeof t.onUpdate&&t.onUpdate(),null!==t.nodeAutoColorBy&&n(["nodeAutoColorBy","graphData","nodeColor"])&&eG(t.graphData.nodes,tT(t.nodeAutoColorBy),t.nodeColor),null!==t.linkAutoColorBy&&n(["linkAutoColorBy","graphData","linkColor"])&&eG(t.graphData.links,tT(t.linkAutoColorBy),t.linkColor),t._flushObjects||n(["graphData","nodeThreeObject","nodeThreeObjectExtend","nodeVal","nodeColor","nodeVisibility","nodeRelSize","nodeResolution","nodeOpacity"])){var r=tT(t.nodeThreeObject),i=tT(t.nodeThreeObjectExtend),o=tT(t.nodeVal),a=tT(t.nodeColor),s=tT(t.nodeVisibility),l={},h={};(t._flushObjects||n(["nodeThreeObject","nodeThreeObjectExtend"]))&&t.nodeDataMapper.clear(),t.nodeDataMapper.onCreateObj(function(e){var n,o=r(e),a=i(e);return o&&t.nodeThreeObject===o&&(o=o.clone()),o&&!a?n=o:((n=new eK.Mesh).__graphDefaultObj=!0,o&&a&&n.add(o)),n.__graphObjType="node",n}).onUpdateObj(function(e,n){if(e.__graphDefaultObj){var r=o(n)||1,i=Math.cbrt(r)*t.nodeRelSize,s=t.nodeResolution;e.geometry.type.match(/^Sphere(Buffer)?Geometry$/)&&e.geometry.parameters.radius===i&&e.geometry.parameters.widthSegments===s||(l.hasOwnProperty(r)||(l[r]=new eK.SphereGeometry(i,s,s)),e.geometry.dispose(),e.geometry=l[r]);var u=a(n),c=new eK.Color(eY(u||"#ffffaa")),d=t.nodeOpacity*eW(u);"MeshLambertMaterial"===e.material.type&&e.material.color.equals(c)&&e.material.opacity===d||(h.hasOwnProperty(u)||(h[u]=new eK.MeshLambertMaterial({color:c,transparent:!0,opacity:d})),e.material.dispose(),e.material=h[u])}}).digest(t.graphData.nodes.filter(s))}if(t._flushObjects||n(["graphData","linkThreeObject","linkThreeObjectExtend","linkMaterial","linkColor","linkWidth","linkVisibility","linkResolution","linkOpacity","linkDirectionalArrowLength","linkDirectionalArrowColor","linkDirectionalArrowResolution","linkDirectionalParticles","linkDirectionalParticleWidth","linkDirectionalParticleColor","linkDirectionalParticleResolution"])){var u=tT(t.linkThreeObject),c=tT(t.linkThreeObjectExtend),d=tT(t.linkMaterial),f=tT(t.linkVisibility),p=tT(t.linkColor),g=tT(t.linkWidth),_={},m={},v={},y=t.graphData.links.filter(f);if((t._flushObjects||n(["linkThreeObject","linkThreeObjectExtend","linkWidth"]))&&t.linkDataMapper.clear(),t.linkDataMapper.onRemoveObj(function(t){var e=t.__data&&t.__data.__singleHopPhotonsObj;e&&(e.parent.remove(e),eq(e),delete t.__data.__singleHopPhotonsObj)}).onCreateObj(function(e){var n,r,i=u(e),o=c(e);if(i&&t.linkThreeObject===i&&(i=i.clone()),!i||o)if(g(e))n=new eK.Mesh;else{var a=new eK.BufferGeometry;a[eQ]("position",new eK.BufferAttribute(new Float32Array(6),3)),n=new eK.Line(a)}return i?o?((r=new eK.Group).__graphDefaultObj=!0,r.add(n),r.add(i)):r=i:(r=n).__graphDefaultObj=!0,r.renderOrder=10,r.__graphObjType="link",r}).onUpdateObj(function(e,n){if(e.__graphDefaultObj){var r=e.children.length?e.children[0]:e,i=Math.ceil(10*g(n))/10,o=!!i;if(o){var a=i/2,s=t.linkResolution;if(!r.geometry.type.match(/^Cylinder(Buffer)?Geometry$/)||r.geometry.parameters.radiusTop!==a||r.geometry.parameters.radialSegments!==s){if(!_.hasOwnProperty(i)){var l=new eK.CylinderGeometry(a,a,1,s,1,!1);l[eJ](new eK.Matrix4().makeTranslation(0,.5,0)),l[eJ](new eK.Matrix4().makeRotationX(Math.PI/2)),_[i]=l}r.geometry.dispose(),r.geometry=_[i]}}var h=d(n);if(h)r.material=h;else{var u=p(n),c=new eK.Color(eY(u||"#f0f0f0")),f=t.linkOpacity*eW(u),y=o?"MeshLambertMaterial":"LineBasicMaterial";if(r.material.type!==y||!r.material.color.equals(c)||r.material.opacity!==f){var b=o?m:v;b.hasOwnProperty(u)||(b[u]=new eK[y]({color:c,transparent:f<1,opacity:f,depthWrite:f>=1})),r.material.dispose(),r.material=b[u]}}}}).digest(y),t.linkDirectionalArrowLength||e.hasOwnProperty("linkDirectionalArrowLength")){var b=tT(t.linkDirectionalArrowLength),w=tT(t.linkDirectionalArrowColor);t.arrowDataMapper.onCreateObj(function(){var t=new eK.Mesh(void 0,new eK.MeshLambertMaterial({transparent:!0}));return t.__linkThreeObjType="arrow",t}).onUpdateObj(function(e,n){var r=b(n),i=t.linkDirectionalArrowResolution;if(!e.geometry.type.match(/^Cone(Buffer)?Geometry$/)||e.geometry.parameters.height!==r||e.geometry.parameters.radialSegments!==i){var o=new eK.ConeGeometry(.25*r,r,i);o.translate(0,r/2,0),o.rotateX(Math.PI/2),e.geometry.dispose(),e.geometry=o}var a=w(n)||p(n)||"#f0f0f0";e.material.color=new eK.Color(eY(a)),e.material.opacity=3*t.linkOpacity*eW(a)}).digest(y.filter(b))}if(t.linkDirectionalParticles||e.hasOwnProperty("linkDirectionalParticles")){var x=tT(t.linkDirectionalParticles),k=tT(t.linkDirectionalParticleWidth),P=tT(t.linkDirectionalParticleColor),E={},O={};t.particlesDataMapper.onCreateObj(function(){var t=new eK.Group;return t.__linkThreeObjType="photons",t.__photonDataMapper=new eV(t),t}).onUpdateObj(function(e,n){var r,i,o=Math.round(Math.abs(x(n))),a=!!e.children.length&&e.children[0],s=Math.ceil(10*k(n))/10/2,l=t.linkDirectionalParticleResolution;a&&a.geometry.parameters.radius===s&&a.geometry.parameters.widthSegments===l?r=a.geometry:(O.hasOwnProperty(s)||(O[s]=new eK.SphereGeometry(s,l,l)),r=O[s],a&&a.geometry.dispose());var h=P(n)||p(n)||"#f0f0f0",u=new eK.Color(eY(h)),c=3*t.linkOpacity;a&&a.material.color.equals(u)&&a.material.opacity===c?i=a.material:(E.hasOwnProperty(h)||(E[h]=new eK.MeshLambertMaterial({color:u,transparent:!0,opacity:c})),i=E[h],a&&a.material.dispose()),e.__photonDataMapper.id(function(t){return t.idx}).onCreateObj(function(){return new eK.Mesh(r,i)}).onUpdateObj(function(t){t.geometry=r,t.material=i}).digest(eI(Array(o)).map(function(t,e){return{idx:e}}))}).digest(y.filter(x))}}if(t._flushObjects=!1,n(["graphData","nodeId","linkSource","linkTarget","numDimensions","forceEngine","dagMode","dagNodeFilter","dagLevelDistance"])){t.engineRunning=!1,t.graphData.links.forEach(function(e){e.source=e[t.linkSource],e.target=e[t.linkTarget]});var M,S="ngraph"!==t.forceEngine;if(S){(M=t.d3ForceLayout).stop().alpha(1).numDimensions(t.numDimensions).nodes(t.graphData.nodes);var j=t.d3ForceLayout.force("link");j&&j.id(function(e){return e[t.nodeId]}).links(t.graphData.links);var C=t.dagMode&&function(t,e){var n=t.nodes,r=t.links,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=i.nodeFilter,a=void 0===o?function(){return!0}:o,s=i.onLoopError,l=void 0===s?function(t){throw"Invalid DAG structure! Found cycle in node path: ".concat(t.join(" -> "),".")}:s,h={};n.forEach(function(t){return h[e(t)]={data:t,out:[],depth:-1,skip:!a(t)}}),r.forEach(function(t){var n=t.source,r=t.target,i=l(n),o=l(r);if(!h.hasOwnProperty(i))throw"Missing source node with id: ".concat(i);if(!h.hasOwnProperty(o))throw"Missing target node with id: ".concat(o);var a=h[i],s=h[o];function l(t){return"object"===ez(t)?e(t):t}a.out.push(s)});var u=[];return function t(n){for(var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=0,a=n.length;o<a;o++)if(function(){var a=n[o];if(-1!==r.indexOf(a)){var s=[].concat(eI(r.slice(r.indexOf(a))),[a]).map(function(t){return e(t.data)});return u.some(function(t){return t.length===s.length&&t.every(function(t,e){return t===s[e]})})||(u.push(s),l(s)),1}i>a.depth&&(a.depth=i,t(a.out,[].concat(eI(r),[a]),i+ +!a.skip))}())continue}(Object.values(h)),Object.assign.apply(Object,[{}].concat(eI(Object.entries(h).filter(function(t){return!eR(t,2)[1].skip}).map(function(t){var e=eR(t,2);return eM({},e[0],e[1].depth)}))))}(t.graphData,function(e){return e[t.nodeId]},{nodeFilter:t.dagNodeFilter,onLoopError:t.onDagError||void 0}),A=Math.max.apply(Math,eI(Object.values(C||[]))),D=t.dagLevelDistance||t.graphData.nodes.length/(A||1)*2*(-1!==["radialin","radialout"].indexOf(t.dagMode)?.7:1);if(["lr","rl","td","bu","zin","zout"].includes(e.dagMode)){var R=["lr","rl"].includes(e.dagMode)?"fx":["td","bu"].includes(e.dagMode)?"fy":"fz";t.graphData.nodes.filter(t.dagNodeFilter).forEach(function(t){return delete t[R]})}if(["lr","rl","td","bu","zin","zout"].includes(t.dagMode)){var L=["rl","td","zout"].includes(t.dagMode),I=["lr","rl"].includes(t.dagMode)?"fx":["td","bu"].includes(t.dagMode)?"fy":"fz";t.graphData.nodes.filter(t.dagNodeFilter).forEach(function(e){return e[I]=(C[e[t.nodeId]]-A/2)*D*(L?-1:1)})}t.d3ForceLayout.force("dagRadial",-1!==["radialin","radialout"].indexOf(t.dagMode)?(function(t,e,n,r){var i,o,a,s,l=T(.1);function h(t){for(var l=0,h=i.length;l<h;++l){var u=i[l],c=u.x-e||1e-6,d=(u.y||0)-n||1e-6,f=(u.z||0)-r||1e-6,p=Math.sqrt(c*c+d*d+f*f),g=(s[l]-p)*a[l]*t/p;u.vx+=c*g,o>1&&(u.vy+=d*g),o>2&&(u.vz+=f*g)}}function u(){if(i){var e,n=i.length;for(e=0,a=Array(n),s=Array(n);e<n;++e)s[e]=+t(i[e],e,i),a[e]=isNaN(s[e])?0:+l(i[e],e,i)}}return"function"!=typeof t&&(t=T(+t)),null==e&&(e=0),null==n&&(n=0),null==r&&(r=0),h.initialize=function(t,...e){i=t,o=e.find(t=>[1,2,3].includes(t))||2,u()},h.strength=function(t){return arguments.length?(l="function"==typeof t?t:T(+t),u(),h):l},h.radius=function(e){return arguments.length?(t="function"==typeof e?e:T(+e),u(),h):t},h.x=function(t){return arguments.length?(e=+t,h):e},h.y=function(t){return arguments.length?(n=+t,h):n},h.z=function(t){return arguments.length?(r=+t,h):r},h})(function(e){var n=C[e[t.nodeId]]||-1;return("radialin"===t.dagMode?A-n:n)*D}).strength(function(e){return+!!t.dagNodeFilter(e)}):null)}else{var N=eX.graph();t.graphData.nodes.forEach(function(e){N.addNode(e[t.nodeId])}),t.graphData.links.forEach(function(t){N.addLink(t.source,t.target)}),(M=eX.forcelayout(N,function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?eA(Object(n),!0).forEach(function(e){eM(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):eA(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}({dimensions:t.numDimensions},t.ngraphPhysics))).graph=N}for(var z=0;z<t.warmupTicks&&!(S&&t.d3AlphaMin>0&&t.d3ForceLayout.alpha()<t.d3AlphaMin);z++)M[S?"tick":"step"]();t.layout=M,this.resetCountdown()}t.engineRunning=!0,t.onFinishUpdate()}}),(window.THREE?window.THREE:{Group:k.YJl}).Group,!0),e1=n(5267),e2=n(2367);let e5={type:"change"},e3={type:"start"},e6={type:"end"},e8={NONE:-1,ROTATE:0,ZOOM:1,PAN:2,TOUCH_ROTATE:3,TOUCH_ZOOM_PAN:4},e9=new r.I9Y,e4=new r.I9Y,e7=new r.Pq0,nt=new r.Pq0,ne=new r.Pq0,nn=new r.PTz,nr=new r.Pq0,ni=new r.Pq0,no=new r.Pq0,na=new r.Pq0;class ns extends r.H2z{connect(t){super.connect(t),window.addEventListener("keydown",this._onKeyDown),window.addEventListener("keyup",this._onKeyUp),this.domElement.addEventListener("pointerdown",this._onPointerDown),this.domElement.addEventListener("pointercancel",this._onPointerCancel),this.domElement.addEventListener("wheel",this._onMouseWheel,{passive:!1}),this.domElement.addEventListener("contextmenu",this._onContextMenu),this.domElement.style.touchAction="none"}disconnect(){window.removeEventListener("keydown",this._onKeyDown),window.removeEventListener("keyup",this._onKeyUp),this.domElement.removeEventListener("pointerdown",this._onPointerDown),this.domElement.removeEventListener("pointermove",this._onPointerMove),this.domElement.removeEventListener("pointerup",this._onPointerUp),this.domElement.removeEventListener("pointercancel",this._onPointerCancel),this.domElement.removeEventListener("wheel",this._onMouseWheel),this.domElement.removeEventListener("contextmenu",this._onContextMenu),this.domElement.style.touchAction="auto"}dispose(){this.disconnect()}handleResize(){let t=this.domElement.getBoundingClientRect(),e=this.domElement.ownerDocument.documentElement;this.screen.left=t.left+window.pageXOffset-e.clientLeft,this.screen.top=t.top+window.pageYOffset-e.clientTop,this.screen.width=t.width,this.screen.height=t.height}update(){this._eye.subVectors(this.object.position,this.target),this.noRotate||this._rotateCamera(),this.noZoom||this._zoomCamera(),this.noPan||this._panCamera(),this.object.position.addVectors(this.target,this._eye),this.object.isPerspectiveCamera?(this._checkDistances(),this.object.lookAt(this.target),this._lastPosition.distanceToSquared(this.object.position)>1e-6&&(this.dispatchEvent(e5),this._lastPosition.copy(this.object.position))):this.object.isOrthographicCamera?(this.object.lookAt(this.target),(this._lastPosition.distanceToSquared(this.object.position)>1e-6||this._lastZoom!==this.object.zoom)&&(this.dispatchEvent(e5),this._lastPosition.copy(this.object.position),this._lastZoom=this.object.zoom)):console.warn("THREE.TrackballControls: Unsupported camera type.")}reset(){this.state=e8.NONE,this.keyState=e8.NONE,this.target.copy(this._target0),this.object.position.copy(this._position0),this.object.up.copy(this._up0),this.object.zoom=this._zoom0,this.object.updateProjectionMatrix(),this._eye.subVectors(this.object.position,this.target),this.object.lookAt(this.target),this.dispatchEvent(e5),this._lastPosition.copy(this.object.position),this._lastZoom=this.object.zoom}_panCamera(){if(e4.copy(this._panEnd).sub(this._panStart),e4.lengthSq()){if(this.object.isOrthographicCamera){let t=(this.object.right-this.object.left)/this.object.zoom/this.domElement.clientWidth,e=(this.object.top-this.object.bottom)/this.object.zoom/this.domElement.clientWidth;e4.x*=t,e4.y*=e}e4.multiplyScalar(this._eye.length()*this.panSpeed),nt.copy(this._eye).cross(this.object.up).setLength(e4.x),nt.add(e7.copy(this.object.up).setLength(e4.y)),this.object.position.add(nt),this.target.add(nt),this.staticMoving?this._panStart.copy(this._panEnd):this._panStart.add(e4.subVectors(this._panEnd,this._panStart).multiplyScalar(this.dynamicDampingFactor))}}_rotateCamera(){na.set(this._moveCurr.x-this._movePrev.x,this._moveCurr.y-this._movePrev.y,0);let t=na.length();t?(this._eye.copy(this.object.position).sub(this.target),nr.copy(this._eye).normalize(),ni.copy(this.object.up).normalize(),no.crossVectors(ni,nr).normalize(),ni.setLength(this._moveCurr.y-this._movePrev.y),no.setLength(this._moveCurr.x-this._movePrev.x),na.copy(ni.add(no)),ne.crossVectors(na,this._eye).normalize(),t*=this.rotateSpeed,nn.setFromAxisAngle(ne,t),this._eye.applyQuaternion(nn),this.object.up.applyQuaternion(nn),this._lastAxis.copy(ne),this._lastAngle=t):!this.staticMoving&&this._lastAngle&&(this._lastAngle*=Math.sqrt(1-this.dynamicDampingFactor),this._eye.copy(this.object.position).sub(this.target),nn.setFromAxisAngle(this._lastAxis,this._lastAngle),this._eye.applyQuaternion(nn),this.object.up.applyQuaternion(nn)),this._movePrev.copy(this._moveCurr)}_zoomCamera(){let t;this.state===e8.TOUCH_ZOOM_PAN?(t=this._touchZoomDistanceStart/this._touchZoomDistanceEnd,this._touchZoomDistanceStart=this._touchZoomDistanceEnd,this.object.isPerspectiveCamera?this._eye.multiplyScalar(t):this.object.isOrthographicCamera?(this.object.zoom=r.cj9.clamp(this.object.zoom/t,this.minZoom,this.maxZoom),this._lastZoom!==this.object.zoom&&this.object.updateProjectionMatrix()):console.warn("THREE.TrackballControls: Unsupported camera type")):(1!=(t=1+(this._zoomEnd.y-this._zoomStart.y)*this.zoomSpeed)&&t>0&&(this.object.isPerspectiveCamera?this._eye.multiplyScalar(t):this.object.isOrthographicCamera?(this.object.zoom=r.cj9.clamp(this.object.zoom/t,this.minZoom,this.maxZoom),this._lastZoom!==this.object.zoom&&this.object.updateProjectionMatrix()):console.warn("THREE.TrackballControls: Unsupported camera type")),this.staticMoving?this._zoomStart.copy(this._zoomEnd):this._zoomStart.y+=(this._zoomEnd.y-this._zoomStart.y)*this.dynamicDampingFactor)}_getMouseOnScreen(t,e){return e9.set((t-this.screen.left)/this.screen.width,(e-this.screen.top)/this.screen.height),e9}_getMouseOnCircle(t,e){return e9.set((t-.5*this.screen.width-this.screen.left)/(.5*this.screen.width),(this.screen.height+2*(this.screen.top-e))/this.screen.width),e9}_addPointer(t){this._pointers.push(t)}_removePointer(t){delete this._pointerPositions[t.pointerId];for(let e=0;e<this._pointers.length;e++)if(this._pointers[e].pointerId==t.pointerId)return void this._pointers.splice(e,1)}_trackPointer(t){let e=this._pointerPositions[t.pointerId];void 0===e&&(e=new r.I9Y,this._pointerPositions[t.pointerId]=e),e.set(t.pageX,t.pageY)}_getSecondPointerPosition(t){let e=t.pointerId===this._pointers[0].pointerId?this._pointers[1]:this._pointers[0];return this._pointerPositions[e.pointerId]}_checkDistances(){this.noZoom&&this.noPan||(this._eye.lengthSq()>this.maxDistance*this.maxDistance&&(this.object.position.addVectors(this.target,this._eye.setLength(this.maxDistance)),this._zoomStart.copy(this._zoomEnd)),this._eye.lengthSq()<this.minDistance*this.minDistance&&(this.object.position.addVectors(this.target,this._eye.setLength(this.minDistance)),this._zoomStart.copy(this._zoomEnd)))}constructor(t,e=null){super(t,e),this.screen={left:0,top:0,width:0,height:0},this.rotateSpeed=1,this.zoomSpeed=1.2,this.panSpeed=.3,this.noRotate=!1,this.noZoom=!1,this.noPan=!1,this.staticMoving=!1,this.dynamicDampingFactor=.2,this.minDistance=0,this.maxDistance=1/0,this.minZoom=0,this.maxZoom=1/0,this.keys=["KeyA","KeyS","KeyD"],this.mouseButtons={LEFT:r.kBv.ROTATE,MIDDLE:r.kBv.DOLLY,RIGHT:r.kBv.PAN},this.target=new r.Pq0,this.state=e8.NONE,this.keyState=e8.NONE,this._lastPosition=new r.Pq0,this._lastZoom=1,this._touchZoomDistanceStart=0,this._touchZoomDistanceEnd=0,this._lastAngle=0,this._eye=new r.Pq0,this._movePrev=new r.I9Y,this._moveCurr=new r.I9Y,this._lastAxis=new r.Pq0,this._zoomStart=new r.I9Y,this._zoomEnd=new r.I9Y,this._panStart=new r.I9Y,this._panEnd=new r.I9Y,this._pointers=[],this._pointerPositions={},this._onPointerMove=nh.bind(this),this._onPointerDown=nl.bind(this),this._onPointerUp=nu.bind(this),this._onPointerCancel=nc.bind(this),this._onContextMenu=nv.bind(this),this._onMouseWheel=nm.bind(this),this._onKeyDown=nf.bind(this),this._onKeyUp=nd.bind(this),this._onTouchStart=ny.bind(this),this._onTouchMove=nb.bind(this),this._onTouchEnd=nw.bind(this),this._onMouseDown=np.bind(this),this._onMouseMove=ng.bind(this),this._onMouseUp=n_.bind(this),this._target0=this.target.clone(),this._position0=this.object.position.clone(),this._up0=this.object.up.clone(),this._zoom0=this.object.zoom,null!==e&&(this.connect(e),this.handleResize()),this.update()}}function nl(t){!1!==this.enabled&&(0===this._pointers.length&&(this.domElement.setPointerCapture(t.pointerId),this.domElement.addEventListener("pointermove",this._onPointerMove),this.domElement.addEventListener("pointerup",this._onPointerUp)),this._addPointer(t),"touch"===t.pointerType?this._onTouchStart(t):this._onMouseDown(t))}function nh(t){!1!==this.enabled&&("touch"===t.pointerType?this._onTouchMove(t):this._onMouseMove(t))}function nu(t){!1!==this.enabled&&("touch"===t.pointerType?this._onTouchEnd(t):this._onMouseUp(),this._removePointer(t),0===this._pointers.length&&(this.domElement.releasePointerCapture(t.pointerId),this.domElement.removeEventListener("pointermove",this._onPointerMove),this.domElement.removeEventListener("pointerup",this._onPointerUp)))}function nc(t){this._removePointer(t)}function nd(){!1!==this.enabled&&(this.keyState=e8.NONE,window.addEventListener("keydown",this._onKeyDown))}function nf(t){!1!==this.enabled&&(window.removeEventListener("keydown",this._onKeyDown),this.keyState===e8.NONE&&(t.code!==this.keys[e8.ROTATE]||this.noRotate?t.code!==this.keys[e8.ZOOM]||this.noZoom?t.code!==this.keys[e8.PAN]||this.noPan||(this.keyState=e8.PAN):this.keyState=e8.ZOOM:this.keyState=e8.ROTATE))}function np(t){let e;switch(t.button){case 0:e=this.mouseButtons.LEFT;break;case 1:e=this.mouseButtons.MIDDLE;break;case 2:e=this.mouseButtons.RIGHT;break;default:e=-1}switch(e){case r.kBv.DOLLY:this.state=e8.ZOOM;break;case r.kBv.ROTATE:this.state=e8.ROTATE;break;case r.kBv.PAN:this.state=e8.PAN;break;default:this.state=e8.NONE}let n=this.keyState!==e8.NONE?this.keyState:this.state;n!==e8.ROTATE||this.noRotate?n!==e8.ZOOM||this.noZoom?n!==e8.PAN||this.noPan||(this._panStart.copy(this._getMouseOnScreen(t.pageX,t.pageY)),this._panEnd.copy(this._panStart)):(this._zoomStart.copy(this._getMouseOnScreen(t.pageX,t.pageY)),this._zoomEnd.copy(this._zoomStart)):(this._moveCurr.copy(this._getMouseOnCircle(t.pageX,t.pageY)),this._movePrev.copy(this._moveCurr)),this.dispatchEvent(e3)}function ng(t){let e=this.keyState!==e8.NONE?this.keyState:this.state;e!==e8.ROTATE||this.noRotate?e!==e8.ZOOM||this.noZoom?e!==e8.PAN||this.noPan||this._panEnd.copy(this._getMouseOnScreen(t.pageX,t.pageY)):this._zoomEnd.copy(this._getMouseOnScreen(t.pageX,t.pageY)):(this._movePrev.copy(this._moveCurr),this._moveCurr.copy(this._getMouseOnCircle(t.pageX,t.pageY)))}function n_(){this.state=e8.NONE,this.dispatchEvent(e6)}function nm(t){if(!1!==this.enabled&&!0!==this.noZoom){switch(t.preventDefault(),t.deltaMode){case 2:this._zoomStart.y-=.025*t.deltaY;break;case 1:this._zoomStart.y-=.01*t.deltaY;break;default:this._zoomStart.y-=25e-5*t.deltaY}this.dispatchEvent(e3),this.dispatchEvent(e6)}}function nv(t){!1!==this.enabled&&t.preventDefault()}function ny(t){if(this._trackPointer(t),1===this._pointers.length)this.state=e8.TOUCH_ROTATE,this._moveCurr.copy(this._getMouseOnCircle(this._pointers[0].pageX,this._pointers[0].pageY)),this._movePrev.copy(this._moveCurr);else{this.state=e8.TOUCH_ZOOM_PAN;let t=this._pointers[0].pageX-this._pointers[1].pageX,e=this._pointers[0].pageY-this._pointers[1].pageY;this._touchZoomDistanceEnd=this._touchZoomDistanceStart=Math.sqrt(t*t+e*e);let n=(this._pointers[0].pageX+this._pointers[1].pageX)/2,r=(this._pointers[0].pageY+this._pointers[1].pageY)/2;this._panStart.copy(this._getMouseOnScreen(n,r)),this._panEnd.copy(this._panStart)}this.dispatchEvent(e3)}function nb(t){if(this._trackPointer(t),1===this._pointers.length)this._movePrev.copy(this._moveCurr),this._moveCurr.copy(this._getMouseOnCircle(t.pageX,t.pageY));else{let e=this._getSecondPointerPosition(t),n=t.pageX-e.x,r=t.pageY-e.y;this._touchZoomDistanceEnd=Math.sqrt(n*n+r*r);let i=(t.pageX+e.x)/2,o=(t.pageY+e.y)/2;this._panEnd.copy(this._getMouseOnScreen(i,o))}}function nw(t){switch(this._pointers.length){case 0:this.state=e8.NONE;break;case 1:this.state=e8.TOUCH_ROTATE,this._moveCurr.copy(this._getMouseOnCircle(t.pageX,t.pageY)),this._movePrev.copy(this._moveCurr);break;case 2:this.state=e8.TOUCH_ZOOM_PAN;for(let e=0;e<this._pointers.length;e++)if(this._pointers[e].pointerId!==t.pointerId){let t=this._pointerPositions[this._pointers[e].pointerId];this._moveCurr.copy(this._getMouseOnCircle(t.x,t.y)),this._movePrev.copy(this._moveCurr);break}}this.dispatchEvent(e6)}let nx={type:"change"},nk={type:"start"},nP={type:"end"},nE=new r.RlV,nO=new r.Zcv,nM=Math.cos(70*r.cj9.DEG2RAD),nS=new r.Pq0,nj=2*Math.PI,nC={NONE:-1,ROTATE:0,DOLLY:1,PAN:2,TOUCH_ROTATE:3,TOUCH_PAN:4,TOUCH_DOLLY_PAN:5,TOUCH_DOLLY_ROTATE:6};class nT extends r.H2z{connect(t){super.connect(t),this.domElement.addEventListener("pointerdown",this._onPointerDown),this.domElement.addEventListener("pointercancel",this._onPointerUp),this.domElement.addEventListener("contextmenu",this._onContextMenu),this.domElement.addEventListener("wheel",this._onMouseWheel,{passive:!1}),this.domElement.getRootNode().addEventListener("keydown",this._interceptControlDown,{passive:!0,capture:!0}),this.domElement.style.touchAction="none"}disconnect(){this.domElement.removeEventListener("pointerdown",this._onPointerDown),this.domElement.removeEventListener("pointermove",this._onPointerMove),this.domElement.removeEventListener("pointerup",this._onPointerUp),this.domElement.removeEventListener("pointercancel",this._onPointerUp),this.domElement.removeEventListener("wheel",this._onMouseWheel),this.domElement.removeEventListener("contextmenu",this._onContextMenu),this.stopListenToKeyEvents(),this.domElement.getRootNode().removeEventListener("keydown",this._interceptControlDown,{capture:!0}),this.domElement.style.touchAction="auto"}dispose(){this.disconnect()}getPolarAngle(){return this._spherical.phi}getAzimuthalAngle(){return this._spherical.theta}getDistance(){return this.object.position.distanceTo(this.target)}listenToKeyEvents(t){t.addEventListener("keydown",this._onKeyDown),this._domElementKeyEvents=t}stopListenToKeyEvents(){null!==this._domElementKeyEvents&&(this._domElementKeyEvents.removeEventListener("keydown",this._onKeyDown),this._domElementKeyEvents=null)}saveState(){this.target0.copy(this.target),this.position0.copy(this.object.position),this.zoom0=this.object.zoom}reset(){this.target.copy(this.target0),this.object.position.copy(this.position0),this.object.zoom=this.zoom0,this.object.updateProjectionMatrix(),this.dispatchEvent(nx),this.update(),this.state=nC.NONE}update(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,e=this.object.position;nS.copy(e).sub(this.target),nS.applyQuaternion(this._quat),this._spherical.setFromVector3(nS),this.autoRotate&&this.state===nC.NONE&&this._rotateLeft(this._getAutoRotationAngle(t)),this.enableDamping?(this._spherical.theta+=this._sphericalDelta.theta*this.dampingFactor,this._spherical.phi+=this._sphericalDelta.phi*this.dampingFactor):(this._spherical.theta+=this._sphericalDelta.theta,this._spherical.phi+=this._sphericalDelta.phi);let n=this.minAzimuthAngle,i=this.maxAzimuthAngle;isFinite(n)&&isFinite(i)&&(n<-Math.PI?n+=nj:n>Math.PI&&(n-=nj),i<-Math.PI?i+=nj:i>Math.PI&&(i-=nj),n<=i?this._spherical.theta=Math.max(n,Math.min(i,this._spherical.theta)):this._spherical.theta=this._spherical.theta>(n+i)/2?Math.max(n,this._spherical.theta):Math.min(i,this._spherical.theta)),this._spherical.phi=Math.max(this.minPolarAngle,Math.min(this.maxPolarAngle,this._spherical.phi)),this._spherical.makeSafe(),!0===this.enableDamping?this.target.addScaledVector(this._panOffset,this.dampingFactor):this.target.add(this._panOffset),this.target.sub(this.cursor),this.target.clampLength(this.minTargetRadius,this.maxTargetRadius),this.target.add(this.cursor);let o=!1;if(this.zoomToCursor&&this._performCursorZoom||this.object.isOrthographicCamera)this._spherical.radius=this._clampDistance(this._spherical.radius);else{let t=this._spherical.radius;this._spherical.radius=this._clampDistance(this._spherical.radius*this._scale),o=t!=this._spherical.radius}if(nS.setFromSpherical(this._spherical),nS.applyQuaternion(this._quatInverse),e.copy(this.target).add(nS),this.object.lookAt(this.target),!0===this.enableDamping?(this._sphericalDelta.theta*=1-this.dampingFactor,this._sphericalDelta.phi*=1-this.dampingFactor,this._panOffset.multiplyScalar(1-this.dampingFactor)):(this._sphericalDelta.set(0,0,0),this._panOffset.set(0,0,0)),this.zoomToCursor&&this._performCursorZoom){let t=null;if(this.object.isPerspectiveCamera){let e=nS.length();t=this._clampDistance(e*this._scale);let n=e-t;this.object.position.addScaledVector(this._dollyDirection,n),this.object.updateMatrixWorld(),o=!!n}else if(this.object.isOrthographicCamera){let e=new r.Pq0(this._mouse.x,this._mouse.y,0);e.unproject(this.object);let n=this.object.zoom;this.object.zoom=Math.max(this.minZoom,Math.min(this.maxZoom,this.object.zoom/this._scale)),this.object.updateProjectionMatrix(),o=n!==this.object.zoom;let i=new r.Pq0(this._mouse.x,this._mouse.y,0);i.unproject(this.object),this.object.position.sub(i).add(e),this.object.updateMatrixWorld(),t=nS.length()}else console.warn("WARNING: OrbitControls.js encountered an unknown camera type - zoom to cursor disabled."),this.zoomToCursor=!1;null!==t&&(this.screenSpacePanning?this.target.set(0,0,-1).transformDirection(this.object.matrix).multiplyScalar(t).add(this.object.position):(nE.origin.copy(this.object.position),nE.direction.set(0,0,-1).transformDirection(this.object.matrix),Math.abs(this.object.up.dot(nE.direction))<nM?this.object.lookAt(this.target):(nO.setFromNormalAndCoplanarPoint(this.object.up,this.target),nE.intersectPlane(nO,this.target))))}else if(this.object.isOrthographicCamera){let t=this.object.zoom;this.object.zoom=Math.max(this.minZoom,Math.min(this.maxZoom,this.object.zoom/this._scale)),t!==this.object.zoom&&(this.object.updateProjectionMatrix(),o=!0)}return this._scale=1,this._performCursorZoom=!1,!!(o||this._lastPosition.distanceToSquared(this.object.position)>1e-6||8*(1-this._lastQuaternion.dot(this.object.quaternion))>1e-6||this._lastTargetPosition.distanceToSquared(this.target)>1e-6)&&(this.dispatchEvent(nx),this._lastPosition.copy(this.object.position),this._lastQuaternion.copy(this.object.quaternion),this._lastTargetPosition.copy(this.target),!0)}_getAutoRotationAngle(t){return null!==t?nj/60*this.autoRotateSpeed*t:nj/60/60*this.autoRotateSpeed}_getZoomScale(t){let e=Math.abs(.01*t);return Math.pow(.95,this.zoomSpeed*e)}_rotateLeft(t){this._sphericalDelta.theta-=t}_rotateUp(t){this._sphericalDelta.phi-=t}_panLeft(t,e){nS.setFromMatrixColumn(e,0),nS.multiplyScalar(-t),this._panOffset.add(nS)}_panUp(t,e){!0===this.screenSpacePanning?nS.setFromMatrixColumn(e,1):(nS.setFromMatrixColumn(e,0),nS.crossVectors(this.object.up,nS)),nS.multiplyScalar(t),this._panOffset.add(nS)}_pan(t,e){let n=this.domElement;if(this.object.isPerspectiveCamera){let r=this.object.position;nS.copy(r).sub(this.target);let i=nS.length();i*=Math.tan(this.object.fov/2*Math.PI/180),this._panLeft(2*t*i/n.clientHeight,this.object.matrix),this._panUp(2*e*i/n.clientHeight,this.object.matrix)}else this.object.isOrthographicCamera?(this._panLeft(t*(this.object.right-this.object.left)/this.object.zoom/n.clientWidth,this.object.matrix),this._panUp(e*(this.object.top-this.object.bottom)/this.object.zoom/n.clientHeight,this.object.matrix)):(console.warn("WARNING: OrbitControls.js encountered an unknown camera type - pan disabled."),this.enablePan=!1)}_dollyOut(t){this.object.isPerspectiveCamera||this.object.isOrthographicCamera?this._scale/=t:(console.warn("WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled."),this.enableZoom=!1)}_dollyIn(t){this.object.isPerspectiveCamera||this.object.isOrthographicCamera?this._scale*=t:(console.warn("WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled."),this.enableZoom=!1)}_updateZoomParameters(t,e){if(!this.zoomToCursor)return;this._performCursorZoom=!0;let n=this.domElement.getBoundingClientRect(),r=t-n.left,i=e-n.top,o=n.width,a=n.height;this._mouse.x=r/o*2-1,this._mouse.y=-(i/a*2)+1,this._dollyDirection.set(this._mouse.x,this._mouse.y,1).unproject(this.object).sub(this.object.position).normalize()}_clampDistance(t){return Math.max(this.minDistance,Math.min(this.maxDistance,t))}_handleMouseDownRotate(t){this._rotateStart.set(t.clientX,t.clientY)}_handleMouseDownDolly(t){this._updateZoomParameters(t.clientX,t.clientX),this._dollyStart.set(t.clientX,t.clientY)}_handleMouseDownPan(t){this._panStart.set(t.clientX,t.clientY)}_handleMouseMoveRotate(t){this._rotateEnd.set(t.clientX,t.clientY),this._rotateDelta.subVectors(this._rotateEnd,this._rotateStart).multiplyScalar(this.rotateSpeed);let e=this.domElement;this._rotateLeft(nj*this._rotateDelta.x/e.clientHeight),this._rotateUp(nj*this._rotateDelta.y/e.clientHeight),this._rotateStart.copy(this._rotateEnd),this.update()}_handleMouseMoveDolly(t){this._dollyEnd.set(t.clientX,t.clientY),this._dollyDelta.subVectors(this._dollyEnd,this._dollyStart),this._dollyDelta.y>0?this._dollyOut(this._getZoomScale(this._dollyDelta.y)):this._dollyDelta.y<0&&this._dollyIn(this._getZoomScale(this._dollyDelta.y)),this._dollyStart.copy(this._dollyEnd),this.update()}_handleMouseMovePan(t){this._panEnd.set(t.clientX,t.clientY),this._panDelta.subVectors(this._panEnd,this._panStart).multiplyScalar(this.panSpeed),this._pan(this._panDelta.x,this._panDelta.y),this._panStart.copy(this._panEnd),this.update()}_handleMouseWheel(t){this._updateZoomParameters(t.clientX,t.clientY),t.deltaY<0?this._dollyIn(this._getZoomScale(t.deltaY)):t.deltaY>0&&this._dollyOut(this._getZoomScale(t.deltaY)),this.update()}_handleKeyDown(t){let e=!1;switch(t.code){case this.keys.UP:t.ctrlKey||t.metaKey||t.shiftKey?this.enableRotate&&this._rotateUp(nj*this.keyRotateSpeed/this.domElement.clientHeight):this.enablePan&&this._pan(0,this.keyPanSpeed),e=!0;break;case this.keys.BOTTOM:t.ctrlKey||t.metaKey||t.shiftKey?this.enableRotate&&this._rotateUp(-nj*this.keyRotateSpeed/this.domElement.clientHeight):this.enablePan&&this._pan(0,-this.keyPanSpeed),e=!0;break;case this.keys.LEFT:t.ctrlKey||t.metaKey||t.shiftKey?this.enableRotate&&this._rotateLeft(nj*this.keyRotateSpeed/this.domElement.clientHeight):this.enablePan&&this._pan(this.keyPanSpeed,0),e=!0;break;case this.keys.RIGHT:t.ctrlKey||t.metaKey||t.shiftKey?this.enableRotate&&this._rotateLeft(-nj*this.keyRotateSpeed/this.domElement.clientHeight):this.enablePan&&this._pan(-this.keyPanSpeed,0),e=!0}e&&(t.preventDefault(),this.update())}_handleTouchStartRotate(t){if(1===this._pointers.length)this._rotateStart.set(t.pageX,t.pageY);else{let e=this._getSecondPointerPosition(t),n=.5*(t.pageX+e.x),r=.5*(t.pageY+e.y);this._rotateStart.set(n,r)}}_handleTouchStartPan(t){if(1===this._pointers.length)this._panStart.set(t.pageX,t.pageY);else{let e=this._getSecondPointerPosition(t),n=.5*(t.pageX+e.x),r=.5*(t.pageY+e.y);this._panStart.set(n,r)}}_handleTouchStartDolly(t){let e=this._getSecondPointerPosition(t),n=t.pageX-e.x,r=t.pageY-e.y,i=Math.sqrt(n*n+r*r);this._dollyStart.set(0,i)}_handleTouchStartDollyPan(t){this.enableZoom&&this._handleTouchStartDolly(t),this.enablePan&&this._handleTouchStartPan(t)}_handleTouchStartDollyRotate(t){this.enableZoom&&this._handleTouchStartDolly(t),this.enableRotate&&this._handleTouchStartRotate(t)}_handleTouchMoveRotate(t){if(1==this._pointers.length)this._rotateEnd.set(t.pageX,t.pageY);else{let e=this._getSecondPointerPosition(t),n=.5*(t.pageX+e.x),r=.5*(t.pageY+e.y);this._rotateEnd.set(n,r)}this._rotateDelta.subVectors(this._rotateEnd,this._rotateStart).multiplyScalar(this.rotateSpeed);let e=this.domElement;this._rotateLeft(nj*this._rotateDelta.x/e.clientHeight),this._rotateUp(nj*this._rotateDelta.y/e.clientHeight),this._rotateStart.copy(this._rotateEnd)}_handleTouchMovePan(t){if(1===this._pointers.length)this._panEnd.set(t.pageX,t.pageY);else{let e=this._getSecondPointerPosition(t),n=.5*(t.pageX+e.x),r=.5*(t.pageY+e.y);this._panEnd.set(n,r)}this._panDelta.subVectors(this._panEnd,this._panStart).multiplyScalar(this.panSpeed),this._pan(this._panDelta.x,this._panDelta.y),this._panStart.copy(this._panEnd)}_handleTouchMoveDolly(t){let e=this._getSecondPointerPosition(t),n=t.pageX-e.x,r=t.pageY-e.y,i=Math.sqrt(n*n+r*r);this._dollyEnd.set(0,i),this._dollyDelta.set(0,Math.pow(this._dollyEnd.y/this._dollyStart.y,this.zoomSpeed)),this._dollyOut(this._dollyDelta.y),this._dollyStart.copy(this._dollyEnd);let o=(t.pageX+e.x)*.5,a=(t.pageY+e.y)*.5;this._updateZoomParameters(o,a)}_handleTouchMoveDollyPan(t){this.enableZoom&&this._handleTouchMoveDolly(t),this.enablePan&&this._handleTouchMovePan(t)}_handleTouchMoveDollyRotate(t){this.enableZoom&&this._handleTouchMoveDolly(t),this.enableRotate&&this._handleTouchMoveRotate(t)}_addPointer(t){this._pointers.push(t.pointerId)}_removePointer(t){delete this._pointerPositions[t.pointerId];for(let e=0;e<this._pointers.length;e++)if(this._pointers[e]==t.pointerId)return void this._pointers.splice(e,1)}_isTrackingPointer(t){for(let e=0;e<this._pointers.length;e++)if(this._pointers[e]==t.pointerId)return!0;return!1}_trackPointer(t){let e=this._pointerPositions[t.pointerId];void 0===e&&(e=new r.I9Y,this._pointerPositions[t.pointerId]=e),e.set(t.pageX,t.pageY)}_getSecondPointerPosition(t){let e=t.pointerId===this._pointers[0]?this._pointers[1]:this._pointers[0];return this._pointerPositions[e]}_customWheelEvent(t){let e=t.deltaMode,n={clientX:t.clientX,clientY:t.clientY,deltaY:t.deltaY};switch(e){case 1:n.deltaY*=16;break;case 2:n.deltaY*=100}return t.ctrlKey&&!this._controlActive&&(n.deltaY*=10),n}constructor(t,e=null){super(t,e),this.state=nC.NONE,this.target=new r.Pq0,this.cursor=new r.Pq0,this.minDistance=0,this.maxDistance=1/0,this.minZoom=0,this.maxZoom=1/0,this.minTargetRadius=0,this.maxTargetRadius=1/0,this.minPolarAngle=0,this.maxPolarAngle=Math.PI,this.minAzimuthAngle=-1/0,this.maxAzimuthAngle=1/0,this.enableDamping=!1,this.dampingFactor=.05,this.enableZoom=!0,this.zoomSpeed=1,this.enableRotate=!0,this.rotateSpeed=1,this.keyRotateSpeed=1,this.enablePan=!0,this.panSpeed=1,this.screenSpacePanning=!0,this.keyPanSpeed=7,this.zoomToCursor=!1,this.autoRotate=!1,this.autoRotateSpeed=2,this.keys={LEFT:"ArrowLeft",UP:"ArrowUp",RIGHT:"ArrowRight",BOTTOM:"ArrowDown"},this.mouseButtons={LEFT:r.kBv.ROTATE,MIDDLE:r.kBv.DOLLY,RIGHT:r.kBv.PAN},this.touches={ONE:r.wtR.ROTATE,TWO:r.wtR.DOLLY_PAN},this.target0=this.target.clone(),this.position0=this.object.position.clone(),this.zoom0=this.object.zoom,this._domElementKeyEvents=null,this._lastPosition=new r.Pq0,this._lastQuaternion=new r.PTz,this._lastTargetPosition=new r.Pq0,this._quat=new r.PTz().setFromUnitVectors(t.up,new r.Pq0(0,1,0)),this._quatInverse=this._quat.clone().invert(),this._spherical=new r.YHV,this._sphericalDelta=new r.YHV,this._scale=1,this._panOffset=new r.Pq0,this._rotateStart=new r.I9Y,this._rotateEnd=new r.I9Y,this._rotateDelta=new r.I9Y,this._panStart=new r.I9Y,this._panEnd=new r.I9Y,this._panDelta=new r.I9Y,this._dollyStart=new r.I9Y,this._dollyEnd=new r.I9Y,this._dollyDelta=new r.I9Y,this._dollyDirection=new r.Pq0,this._mouse=new r.I9Y,this._performCursorZoom=!1,this._pointers=[],this._pointerPositions={},this._controlActive=!1,this._onPointerMove=nD.bind(this),this._onPointerDown=nA.bind(this),this._onPointerUp=nR.bind(this),this._onContextMenu=nU.bind(this),this._onMouseWheel=nN.bind(this),this._onKeyDown=nz.bind(this),this._onTouchStart=nF.bind(this),this._onTouchMove=nB.bind(this),this._onMouseDown=nL.bind(this),this._onMouseMove=nI.bind(this),this._interceptControlDown=nq.bind(this),this._interceptControlUp=n$.bind(this),null!==this.domElement&&this.connect(this.domElement),this.update()}}function nA(t){!1!==this.enabled&&(0===this._pointers.length&&(this.domElement.setPointerCapture(t.pointerId),this.domElement.addEventListener("pointermove",this._onPointerMove),this.domElement.addEventListener("pointerup",this._onPointerUp)),this._isTrackingPointer(t)||(this._addPointer(t),"touch"===t.pointerType?this._onTouchStart(t):this._onMouseDown(t)))}function nD(t){!1!==this.enabled&&("touch"===t.pointerType?this._onTouchMove(t):this._onMouseMove(t))}function nR(t){switch(this._removePointer(t),this._pointers.length){case 0:this.domElement.releasePointerCapture(t.pointerId),this.domElement.removeEventListener("pointermove",this._onPointerMove),this.domElement.removeEventListener("pointerup",this._onPointerUp),this.dispatchEvent(nP),this.state=nC.NONE;break;case 1:let e=this._pointers[0],n=this._pointerPositions[e];this._onTouchStart({pointerId:e,pageX:n.x,pageY:n.y})}}function nL(t){let e;switch(t.button){case 0:e=this.mouseButtons.LEFT;break;case 1:e=this.mouseButtons.MIDDLE;break;case 2:e=this.mouseButtons.RIGHT;break;default:e=-1}switch(e){case r.kBv.DOLLY:if(!1===this.enableZoom)return;this._handleMouseDownDolly(t),this.state=nC.DOLLY;break;case r.kBv.ROTATE:if(t.ctrlKey||t.metaKey||t.shiftKey){if(!1===this.enablePan)return;this._handleMouseDownPan(t),this.state=nC.PAN}else{if(!1===this.enableRotate)return;this._handleMouseDownRotate(t),this.state=nC.ROTATE}break;case r.kBv.PAN:if(t.ctrlKey||t.metaKey||t.shiftKey){if(!1===this.enableRotate)return;this._handleMouseDownRotate(t),this.state=nC.ROTATE}else{if(!1===this.enablePan)return;this._handleMouseDownPan(t),this.state=nC.PAN}break;default:this.state=nC.NONE}this.state!==nC.NONE&&this.dispatchEvent(nk)}function nI(t){switch(this.state){case nC.ROTATE:if(!1===this.enableRotate)return;this._handleMouseMoveRotate(t);break;case nC.DOLLY:if(!1===this.enableZoom)return;this._handleMouseMoveDolly(t);break;case nC.PAN:if(!1===this.enablePan)return;this._handleMouseMovePan(t)}}function nN(t){!1!==this.enabled&&!1!==this.enableZoom&&this.state===nC.NONE&&(t.preventDefault(),this.dispatchEvent(nk),this._handleMouseWheel(this._customWheelEvent(t)),this.dispatchEvent(nP))}function nz(t){!1!==this.enabled&&this._handleKeyDown(t)}function nF(t){switch(this._trackPointer(t),this._pointers.length){case 1:switch(this.touches.ONE){case r.wtR.ROTATE:if(!1===this.enableRotate)return;this._handleTouchStartRotate(t),this.state=nC.TOUCH_ROTATE;break;case r.wtR.PAN:if(!1===this.enablePan)return;this._handleTouchStartPan(t),this.state=nC.TOUCH_PAN;break;default:this.state=nC.NONE}break;case 2:switch(this.touches.TWO){case r.wtR.DOLLY_PAN:if(!1===this.enableZoom&&!1===this.enablePan)return;this._handleTouchStartDollyPan(t),this.state=nC.TOUCH_DOLLY_PAN;break;case r.wtR.DOLLY_ROTATE:if(!1===this.enableZoom&&!1===this.enableRotate)return;this._handleTouchStartDollyRotate(t),this.state=nC.TOUCH_DOLLY_ROTATE;break;default:this.state=nC.NONE}break;default:this.state=nC.NONE}this.state!==nC.NONE&&this.dispatchEvent(nk)}function nB(t){switch(this._trackPointer(t),this.state){case nC.TOUCH_ROTATE:if(!1===this.enableRotate)return;this._handleTouchMoveRotate(t),this.update();break;case nC.TOUCH_PAN:if(!1===this.enablePan)return;this._handleTouchMovePan(t),this.update();break;case nC.TOUCH_DOLLY_PAN:if(!1===this.enableZoom&&!1===this.enablePan)return;this._handleTouchMoveDollyPan(t),this.update();break;case nC.TOUCH_DOLLY_ROTATE:if(!1===this.enableZoom&&!1===this.enableRotate)return;this._handleTouchMoveDollyRotate(t),this.update();break;default:this.state=nC.NONE}}function nU(t){!1!==this.enabled&&t.preventDefault()}function nq(t){"Control"===t.key&&(this._controlActive=!0,this.domElement.getRootNode().addEventListener("keyup",this._interceptControlUp,{passive:!0,capture:!0}))}function n$(t){"Control"===t.key&&(this._controlActive=!1,this.domElement.getRootNode().removeEventListener("keyup",this._interceptControlUp,{passive:!0,capture:!0}))}let nH={type:"change"},nV=new r.PTz;class nY extends r.H2z{connect(t){super.connect(t),window.addEventListener("keydown",this._onKeyDown),window.addEventListener("keyup",this._onKeyUp),this.domElement.addEventListener("pointermove",this._onPointerMove),this.domElement.addEventListener("pointerdown",this._onPointerDown),this.domElement.addEventListener("pointerup",this._onPointerUp),this.domElement.addEventListener("pointercancel",this._onPointerCancel),this.domElement.addEventListener("contextmenu",this._onContextMenu)}disconnect(){window.removeEventListener("keydown",this._onKeyDown),window.removeEventListener("keyup",this._onKeyUp),this.domElement.removeEventListener("pointermove",this._onPointerMove),this.domElement.removeEventListener("pointerdown",this._onPointerDown),this.domElement.removeEventListener("pointerup",this._onPointerUp),this.domElement.removeEventListener("pointercancel",this._onPointerCancel),this.domElement.removeEventListener("contextmenu",this._onContextMenu)}dispose(){this.disconnect()}update(t){if(!1===this.enabled)return;let e=this.object,n=t*this.movementSpeed,r=t*this.rollSpeed;e.translateX(this._moveVector.x*n),e.translateY(this._moveVector.y*n),e.translateZ(this._moveVector.z*n),nV.set(this._rotationVector.x*r,this._rotationVector.y*r,this._rotationVector.z*r,1).normalize(),e.quaternion.multiply(nV),(this._lastPosition.distanceToSquared(e.position)>1e-6||8*(1-this._lastQuaternion.dot(e.quaternion))>1e-6)&&(this.dispatchEvent(nH),this._lastQuaternion.copy(e.quaternion),this._lastPosition.copy(e.position))}_updateMovementVector(){let t=this._moveState.forward||this.autoForward&&!this._moveState.back?1:0;this._moveVector.x=-this._moveState.left+this._moveState.right,this._moveVector.y=-this._moveState.down+this._moveState.up,this._moveVector.z=-t+this._moveState.back}_updateRotationVector(){this._rotationVector.x=-this._moveState.pitchDown+this._moveState.pitchUp,this._rotationVector.y=-this._moveState.yawRight+this._moveState.yawLeft,this._rotationVector.z=-this._moveState.rollRight+this._moveState.rollLeft}_getContainerDimensions(){return this.domElement!=document?{size:[this.domElement.offsetWidth,this.domElement.offsetHeight],offset:[this.domElement.offsetLeft,this.domElement.offsetTop]}:{size:[window.innerWidth,window.innerHeight],offset:[0,0]}}constructor(t,e=null){super(t,e),this.movementSpeed=1,this.rollSpeed=.005,this.dragToLook=!1,this.autoForward=!1,this._moveState={up:0,down:0,left:0,right:0,forward:0,back:0,pitchUp:0,pitchDown:0,yawLeft:0,yawRight:0,rollLeft:0,rollRight:0},this._moveVector=new r.Pq0(0,0,0),this._rotationVector=new r.Pq0(0,0,0),this._lastQuaternion=new r.PTz,this._lastPosition=new r.Pq0,this._status=0,this._onKeyDown=nW.bind(this),this._onKeyUp=nZ.bind(this),this._onPointerMove=nK.bind(this),this._onPointerDown=nG.bind(this),this._onPointerUp=nX.bind(this),this._onPointerCancel=nQ.bind(this),this._onContextMenu=nJ.bind(this),null!==e&&this.connect(e)}}function nW(t){if(!t.altKey&&!1!==this.enabled){switch(t.code){case"ShiftLeft":case"ShiftRight":this.movementSpeedMultiplier=.1;break;case"KeyW":this._moveState.forward=1;break;case"KeyS":this._moveState.back=1;break;case"KeyA":this._moveState.left=1;break;case"KeyD":this._moveState.right=1;break;case"KeyR":this._moveState.up=1;break;case"KeyF":this._moveState.down=1;break;case"ArrowUp":this._moveState.pitchUp=1;break;case"ArrowDown":this._moveState.pitchDown=1;break;case"ArrowLeft":this._moveState.yawLeft=1;break;case"ArrowRight":this._moveState.yawRight=1;break;case"KeyQ":this._moveState.rollLeft=1;break;case"KeyE":this._moveState.rollRight=1}this._updateMovementVector(),this._updateRotationVector()}}function nZ(t){if(!1!==this.enabled){switch(t.code){case"ShiftLeft":case"ShiftRight":this.movementSpeedMultiplier=1;break;case"KeyW":this._moveState.forward=0;break;case"KeyS":this._moveState.back=0;break;case"KeyA":this._moveState.left=0;break;case"KeyD":this._moveState.right=0;break;case"KeyR":this._moveState.up=0;break;case"KeyF":this._moveState.down=0;break;case"ArrowUp":this._moveState.pitchUp=0;break;case"ArrowDown":this._moveState.pitchDown=0;break;case"ArrowLeft":this._moveState.yawLeft=0;break;case"ArrowRight":this._moveState.yawRight=0;break;case"KeyQ":this._moveState.rollLeft=0;break;case"KeyE":this._moveState.rollRight=0}this._updateMovementVector(),this._updateRotationVector()}}function nG(t){if(!1!==this.enabled)if(this.dragToLook)this._status++;else{switch(t.button){case 0:this._moveState.forward=1;break;case 2:this._moveState.back=1}this._updateMovementVector()}}function nK(t){if(!1!==this.enabled&&(!this.dragToLook||this._status>0)){let e=this._getContainerDimensions(),n=e.size[0]/2,r=e.size[1]/2;this._moveState.yawLeft=-(t.pageX-e.offset[0]-n)/n,this._moveState.pitchDown=(t.pageY-e.offset[1]-r)/r,this._updateRotationVector()}}function nX(t){if(!1!==this.enabled){if(this.dragToLook)this._status--,this._moveState.yawLeft=this._moveState.pitchDown=0;else{switch(t.button){case 0:this._moveState.forward=0;break;case 2:this._moveState.back=0}this._updateMovementVector()}this._updateRotationVector()}}function nQ(){!1!==this.enabled&&(this.dragToLook?(this._status=0,this._moveState.yawLeft=this._moveState.pitchDown=0):(this._moveState.forward=0,this._moveState.back=0,this._updateMovementVector()),this._updateRotationVector())}function nJ(t){!1!==this.enabled&&t.preventDefault()}let n0={name:"CopyShader",uniforms:{tDiffuse:{value:null},opacity:{value:1}},vertexShader:"\n\n		varying vec2 vUv;\n\n		void main() {\n\n			vUv = uv;\n			gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n		}",fragmentShader:"\n\n		uniform float opacity;\n\n		uniform sampler2D tDiffuse;\n\n		varying vec2 vUv;\n\n		void main() {\n\n			vec4 texel = texture2D( tDiffuse, vUv );\n			gl_FragColor = opacity * texel;\n\n\n		}"};class n1{setSize(){}render(){console.error("THREE.Pass: .render() must be implemented in derived pass.")}dispose(){}constructor(){this.isPass=!0,this.enabled=!0,this.needsSwap=!0,this.clear=!1,this.renderToScreen=!1}}let n2=new r.qUd(-1,1,1,-1,0,1);class n5 extends r.LoY{constructor(){super(),this.setAttribute("position",new r.qtW([-1,3,0,-1,-1,0,3,-1,0],3)),this.setAttribute("uv",new r.qtW([0,2,0,0,2,0],2))}}let n3=new n5;class n6{dispose(){this._mesh.geometry.dispose()}render(t){t.render(this._mesh,n2)}get material(){return this._mesh.material}set material(t){this._mesh.material=t}constructor(t){this._mesh=new r.eaF(n3,t)}}class n8 extends n1{render(t,e,n){this.uniforms[this.textureID]&&(this.uniforms[this.textureID].value=n.texture),this._fsQuad.material=this.material,this.renderToScreen?t.setRenderTarget(null):(t.setRenderTarget(e),this.clear&&t.clear(t.autoClearColor,t.autoClearDepth,t.autoClearStencil)),this._fsQuad.render(t)}dispose(){this.material.dispose(),this._fsQuad.dispose()}constructor(t,e="tDiffuse"){super(),this.textureID=e,this.uniforms=null,this.material=null,t instanceof r.BKk?(this.uniforms=t.uniforms,this.material=t):t&&(this.uniforms=r.LlO.clone(t.uniforms),this.material=new r.BKk({name:void 0!==t.name?t.name:"unspecified",defines:Object.assign({},t.defines),uniforms:this.uniforms,vertexShader:t.vertexShader,fragmentShader:t.fragmentShader})),this._fsQuad=new n6(this.material)}}class n9 extends n1{render(t,e,n){let r,i,o=t.getContext(),a=t.state;a.buffers.color.setMask(!1),a.buffers.depth.setMask(!1),a.buffers.color.setLocked(!0),a.buffers.depth.setLocked(!0),this.inverse?(r=0,i=1):(r=1,i=0),a.buffers.stencil.setTest(!0),a.buffers.stencil.setOp(o.REPLACE,o.REPLACE,o.REPLACE),a.buffers.stencil.setFunc(o.ALWAYS,r,0xffffffff),a.buffers.stencil.setClear(i),a.buffers.stencil.setLocked(!0),t.setRenderTarget(n),this.clear&&t.clear(),t.render(this.scene,this.camera),t.setRenderTarget(e),this.clear&&t.clear(),t.render(this.scene,this.camera),a.buffers.color.setLocked(!1),a.buffers.depth.setLocked(!1),a.buffers.color.setMask(!0),a.buffers.depth.setMask(!0),a.buffers.stencil.setLocked(!1),a.buffers.stencil.setFunc(o.EQUAL,1,0xffffffff),a.buffers.stencil.setOp(o.KEEP,o.KEEP,o.KEEP),a.buffers.stencil.setLocked(!0)}constructor(t,e){super(),this.scene=t,this.camera=e,this.clear=!0,this.needsSwap=!1,this.inverse=!1}}class n4 extends n1{render(t){t.state.buffers.stencil.setLocked(!1),t.state.buffers.stencil.setTest(!1)}constructor(){super(),this.needsSwap=!1}}class n7{swapBuffers(){let t=this.readBuffer;this.readBuffer=this.writeBuffer,this.writeBuffer=t}addPass(t){this.passes.push(t),t.setSize(this._width*this._pixelRatio,this._height*this._pixelRatio)}insertPass(t,e){this.passes.splice(e,0,t),t.setSize(this._width*this._pixelRatio,this._height*this._pixelRatio)}removePass(t){let e=this.passes.indexOf(t);-1!==e&&this.passes.splice(e,1)}isLastEnabledPass(t){for(let e=t+1;e<this.passes.length;e++)if(this.passes[e].enabled)return!1;return!0}render(t){void 0===t&&(t=this.clock.getDelta());let e=this.renderer.getRenderTarget(),n=!1;for(let e=0,r=this.passes.length;e<r;e++){let r=this.passes[e];if(!1!==r.enabled){if(r.renderToScreen=this.renderToScreen&&this.isLastEnabledPass(e),r.render(this.renderer,this.writeBuffer,this.readBuffer,t,n),r.needsSwap){if(n){let e=this.renderer.getContext(),n=this.renderer.state.buffers.stencil;n.setFunc(e.NOTEQUAL,1,0xffffffff),this.copyPass.render(this.renderer,this.writeBuffer,this.readBuffer,t),n.setFunc(e.EQUAL,1,0xffffffff)}this.swapBuffers()}void 0!==n9&&(r instanceof n9?n=!0:r instanceof n4&&(n=!1))}}this.renderer.setRenderTarget(e)}reset(t){if(void 0===t){let e=this.renderer.getSize(new r.I9Y);this._pixelRatio=this.renderer.getPixelRatio(),this._width=e.width,this._height=e.height,(t=this.renderTarget1.clone()).setSize(this._width*this._pixelRatio,this._height*this._pixelRatio)}this.renderTarget1.dispose(),this.renderTarget2.dispose(),this.renderTarget1=t,this.renderTarget2=t.clone(),this.writeBuffer=this.renderTarget1,this.readBuffer=this.renderTarget2}setSize(t,e){this._width=t,this._height=e;let n=this._width*this._pixelRatio,r=this._height*this._pixelRatio;this.renderTarget1.setSize(n,r),this.renderTarget2.setSize(n,r);for(let t=0;t<this.passes.length;t++)this.passes[t].setSize(n,r)}setPixelRatio(t){this._pixelRatio=t,this.setSize(this._width,this._height)}dispose(){this.renderTarget1.dispose(),this.renderTarget2.dispose(),this.copyPass.dispose()}constructor(t,e){if(this.renderer=t,this._pixelRatio=t.getPixelRatio(),void 0===e){let n=t.getSize(new r.I9Y);this._width=n.width,this._height=n.height,(e=new r.nWS(this._width*this._pixelRatio,this._height*this._pixelRatio,{type:r.ix0})).texture.name="EffectComposer.rt1"}else this._width=e.width,this._height=e.height;this.renderTarget1=e,this.renderTarget2=e.clone(),this.renderTarget2.texture.name="EffectComposer.rt2",this.writeBuffer=this.renderTarget1,this.readBuffer=this.renderTarget2,this.renderToScreen=!0,this.passes=[],this.copyPass=new n8(n0),this.copyPass.material.blending=r.XIg,this.clock=new r.zD7}}class rt extends n1{render(t,e,n){let r,i,o=t.autoClear;t.autoClear=!1,null!==this.overrideMaterial&&(i=this.scene.overrideMaterial,this.scene.overrideMaterial=this.overrideMaterial),null!==this.clearColor&&(t.getClearColor(this._oldClearColor),t.setClearColor(this.clearColor,t.getClearAlpha())),null!==this.clearAlpha&&(r=t.getClearAlpha(),t.setClearAlpha(this.clearAlpha)),!0==this.clearDepth&&t.clearDepth(),t.setRenderTarget(this.renderToScreen?null:n),!0===this.clear&&t.clear(t.autoClearColor,t.autoClearDepth,t.autoClearStencil),t.render(this.scene,this.camera),null!==this.clearColor&&t.setClearColor(this._oldClearColor),null!==this.clearAlpha&&t.setClearAlpha(r),null!==this.overrideMaterial&&(this.scene.overrideMaterial=i),t.autoClear=o}constructor(t,e,n=null,i=null,o=null){super(),this.scene=t,this.camera=e,this.overrideMaterial=n,this.clearColor=i,this.clearAlpha=o,this.clear=!0,this.clearDepth=!1,this.needsSwap=!1,this._oldClearColor=new r.Q1f}}function re(){return(re=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)({}).hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(null,arguments)}function rn(t,e){return(rn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function rr(t){return(rr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function ri(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(ri=function(){return!!t})()}function ro(t){var e="function"==typeof Map?new Map:void 0;return(ro=function(t){if(null===t||!function(t){try{return -1!==Function.toString.call(t).indexOf("[native code]")}catch(e){return"function"==typeof t}}(t))return t;if("function"!=typeof t)throw TypeError("Super expression must either be null or a function");if(void 0!==e){if(e.has(t))return e.get(t);e.set(t,n)}function n(){return function(t,e,n){if(ri())return Reflect.construct.apply(null,arguments);var r=[null];r.push.apply(r,e);var i=new(t.bind.apply(t,r));return n&&rn(i,n.prototype),i}(t,arguments,rr(this).constructor)}return n.prototype=Object.create(t.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),rn(n,t)})(t)}var ra=function(t){function e(e){var n=t.call(this,"An error occurred. See https://github.com/styled-components/polished/blob/main/src/internalHelpers/errors.md#"+e+" for more information.")||this;if(void 0===n)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return n}return e.prototype=Object.create(t.prototype),e.prototype.constructor=e,rn(e,t),e}(ro(Error));function rs(t,e){return t.substr(-e.length)===e}var rl=/^([+-]?(?:\d+|\d*\.\d+))([a-z]*|%)$/;function rh(t){return"string"!=typeof t?t:t.match(rl)?parseFloat(t):t}var ru=function(t){return function(e,n){void 0===n&&(n="16px");var r=e,i=n;if("string"==typeof e){if(!rs(e,"px"))throw new ra(69,t,e);r=rh(e)}if("string"==typeof n){if(!rs(n,"px"))throw new ra(70,t,n);i=rh(n)}if("string"==typeof r)throw new ra(71,e,t);if("string"==typeof i)throw new ra(72,n,t);return""+r/i+t}};ru("em");var rc=/^([+-]?(?:\d+|\d*\.\d+))([a-z]*|%)$/;function rd(t){if("string"!=typeof t)return[t,""];var e=t.match(rc);return e?[parseFloat(t),e[2]]:[t,void 0]}ru("rem");var rf={woff:"woff",woff2:"woff2",ttf:"truetype",otf:"opentype",eot:"embedded-opentype",svg:"svg",svgz:"svg"};function rp(t){return Math.round(255*t)}function rg(t,e,n){return rp(t)+","+rp(e)+","+rp(n)}function r_(t,e,n,r){if(void 0===r&&(r=rg),0===e)return r(n,n,n);var i=(t%360+360)%360/60,o=(1-Math.abs(2*n-1))*e,a=o*(1-Math.abs(i%2-1)),s=0,l=0,h=0;i>=0&&i<1?(s=o,l=a):i>=1&&i<2?(s=a,l=o):i>=2&&i<3?(l=o,h=a):i>=3&&i<4?(l=a,h=o):i>=4&&i<5?(s=a,h=o):i>=5&&i<6&&(s=o,h=a);var u=n-o/2;return r(s+u,l+u,h+u)}var rm={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkgrey:"a9a9a9",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",grey:"808080",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgreen:"90ee90",lightgrey:"d3d3d3",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"789",lightslategrey:"789",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"0f0",limegreen:"32cd32",linen:"faf0e6",magenta:"f0f",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370db",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"db7093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"639",red:"f00",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"fff",whitesmoke:"f5f5f5",yellow:"ff0",yellowgreen:"9acd32"},rv=/^#[a-fA-F0-9]{6}$/,ry=/^#[a-fA-F0-9]{8}$/,rb=/^#[a-fA-F0-9]{3}$/,rw=/^#[a-fA-F0-9]{4}$/,rx=/^rgb\(\s*(\d{1,3})\s*(?:,)?\s*(\d{1,3})\s*(?:,)?\s*(\d{1,3})\s*\)$/i,rk=/^rgb(?:a)?\(\s*(\d{1,3})\s*(?:,)?\s*(\d{1,3})\s*(?:,)?\s*(\d{1,3})\s*(?:,|\/)\s*([-+]?\d*[.]?\d+[%]?)\s*\)$/i,rP=/^hsl\(\s*(\d{0,3}[.]?[0-9]+(?:deg)?)\s*(?:,)?\s*(\d{1,3}[.]?[0-9]?)%\s*(?:,)?\s*(\d{1,3}[.]?[0-9]?)%\s*\)$/i,rE=/^hsl(?:a)?\(\s*(\d{0,3}[.]?[0-9]+(?:deg)?)\s*(?:,)?\s*(\d{1,3}[.]?[0-9]?)%\s*(?:,)?\s*(\d{1,3}[.]?[0-9]?)%\s*(?:,|\/)\s*([-+]?\d*[.]?\d+[%]?)\s*\)$/i;function rO(t){if("string"!=typeof t)throw new ra(3);var e=function(t){if("string"!=typeof t)return t;var e=t.toLowerCase();return rm[e]?"#"+rm[e]:t}(t);if(e.match(rv))return{red:parseInt(""+e[1]+e[2],16),green:parseInt(""+e[3]+e[4],16),blue:parseInt(""+e[5]+e[6],16)};if(e.match(ry)){var n=parseFloat((parseInt(""+e[7]+e[8],16)/255).toFixed(2));return{red:parseInt(""+e[1]+e[2],16),green:parseInt(""+e[3]+e[4],16),blue:parseInt(""+e[5]+e[6],16),alpha:n}}if(e.match(rb))return{red:parseInt(""+e[1]+e[1],16),green:parseInt(""+e[2]+e[2],16),blue:parseInt(""+e[3]+e[3],16)};if(e.match(rw)){var r=parseFloat((parseInt(""+e[4]+e[4],16)/255).toFixed(2));return{red:parseInt(""+e[1]+e[1],16),green:parseInt(""+e[2]+e[2],16),blue:parseInt(""+e[3]+e[3],16),alpha:r}}var i=rx.exec(e);if(i)return{red:parseInt(""+i[1],10),green:parseInt(""+i[2],10),blue:parseInt(""+i[3],10)};var o=rk.exec(e.substring(0,50));if(o)return{red:parseInt(""+o[1],10),green:parseInt(""+o[2],10),blue:parseInt(""+o[3],10),alpha:parseFloat(""+o[4])>1?parseFloat(""+o[4])/100:parseFloat(""+o[4])};var a=rP.exec(e);if(a){var s="rgb("+r_(parseInt(""+a[1],10),parseInt(""+a[2],10)/100,parseInt(""+a[3],10)/100)+")",l=rx.exec(s);if(!l)throw new ra(4,e,s);return{red:parseInt(""+l[1],10),green:parseInt(""+l[2],10),blue:parseInt(""+l[3],10)}}var h=rE.exec(e.substring(0,50));if(h){var u="rgb("+r_(parseInt(""+h[1],10),parseInt(""+h[2],10)/100,parseInt(""+h[3],10)/100)+")",c=rx.exec(u);if(!c)throw new ra(4,e,u);return{red:parseInt(""+c[1],10),green:parseInt(""+c[2],10),blue:parseInt(""+c[3],10),alpha:parseFloat(""+h[4])>1?parseFloat(""+h[4])/100:parseFloat(""+h[4])}}throw new ra(5)}function rM(t){return function(t){var e,n=t.red/255,r=t.green/255,i=t.blue/255,o=Math.max(n,r,i),a=Math.min(n,r,i),s=(o+a)/2;if(o===a)if(void 0!==t.alpha)return{hue:0,saturation:0,lightness:s,alpha:t.alpha};else return{hue:0,saturation:0,lightness:s};var l=o-a,h=s>.5?l/(2-o-a):l/(o+a);switch(o){case n:e=(r-i)/l+6*(r<i);break;case r:e=(i-n)/l+2;break;default:e=(n-r)/l+4}return(e*=60,void 0!==t.alpha)?{hue:e,saturation:h,lightness:s,alpha:t.alpha}:{hue:e,saturation:h,lightness:s}}(rO(t))}var rS=function(t){return 7===t.length&&t[1]===t[2]&&t[3]===t[4]&&t[5]===t[6]?"#"+t[1]+t[3]+t[5]:t};function rj(t){var e=t.toString(16);return 1===e.length?"0"+e:e}function rC(t){return rj(Math.round(255*t))}function rT(t,e,n){return rS("#"+rC(t)+rC(e)+rC(n))}function rA(t,e,n){if("number"==typeof t&&"number"==typeof e&&"number"==typeof n)return rS("#"+rj(t)+rj(e)+rj(n));if("object"==typeof t&&void 0===e&&void 0===n)return rS("#"+rj(t.red)+rj(t.green)+rj(t.blue));throw new ra(6)}function rD(t,e,n,r){if("string"==typeof t&&"number"==typeof e){var i=rO(t);return"rgba("+i.red+","+i.green+","+i.blue+","+e+")"}if("number"==typeof t&&"number"==typeof e&&"number"==typeof n&&"number"==typeof r)return r>=1?rA(t,e,n):"rgba("+t+","+e+","+n+","+r+")";if("object"==typeof t&&void 0===e&&void 0===n&&void 0===r)return t.alpha>=1?rA(t.red,t.green,t.blue):"rgba("+t.red+","+t.green+","+t.blue+","+t.alpha+")";throw new ra(7)}function rR(t){if("object"!=typeof t)throw new ra(8);if("number"==typeof t.red&&"number"==typeof t.green&&"number"==typeof t.blue&&"number"==typeof t.alpha)return rD(t);if("number"==typeof t.red&&"number"==typeof t.green&&"number"==typeof t.blue&&("number"!=typeof t.alpha||void 0===t.alpha))return rA(t);if("number"==typeof t.hue&&"number"==typeof t.saturation&&"number"==typeof t.lightness&&"number"==typeof t.alpha)return function(t,e,n,r){"number"==typeof t&&!1;if("object"==typeof t&&void 0===e&&void 0===n&&void 0===r){var i;return t.alpha>=1?(i=t.hue,r_(i,t.saturation,t.lightness,rT)):"rgba("+r_(t.hue,t.saturation,t.lightness)+","+t.alpha+")"}throw new ra(2)}(t);if("number"==typeof t.hue&&"number"==typeof t.saturation&&"number"==typeof t.lightness&&("number"!=typeof t.alpha||void 0===t.alpha))return function(t,e,n){"number"==typeof t&&!1;if("object"==typeof t&&void 0===e&&void 0===n){var r;return r=t.hue,r_(r,t.saturation,t.lightness,rT)}throw new ra(1)}(t);throw new ra(8)}function rL(t){return function t(e,n,r){return function(){var i=r.concat(Array.prototype.slice.call(arguments));return i.length>=n?e.apply(this,i):t(e,n,i)}}(t,t.length,[])}function rI(t,e,n){return Math.max(t,Math.min(e,n))}function rN(t){if("transparent"===t)return 0;var e=rO(t),n=Object.keys(e).map(function(t){var n=e[t]/255;return n<=.03928?n/12.92:Math.pow((n+.055)/1.055,2.4)});return parseFloat((.2126*n[0]+.7152*n[1]+.0722*n[2]).toFixed(3))}rL(function(t,e){if("transparent"===e)return e;var n=rM(e);return rR(re({},n,{hue:n.hue+parseFloat(t)}))}),rL(function(t,e){if("transparent"===e)return e;var n=rM(e);return rR(re({},n,{lightness:rI(0,1,n.lightness-parseFloat(t))}))}),rL(function(t,e){if("transparent"===e)return e;var n=rM(e);return rR(re({},n,{saturation:rI(0,1,n.saturation-parseFloat(t))}))});rL(function(t,e){if("transparent"===e)return e;var n=rM(e);return rR(re({},n,{lightness:rI(0,1,n.lightness+parseFloat(t))}))});var rz=rL(function(t,e,n){if("transparent"===e)return n;if("transparent"===n)return e;if(0===t)return n;var r=rO(e),i=re({},r,{alpha:"number"==typeof r.alpha?r.alpha:1}),o=rO(n),a=re({},o,{alpha:"number"==typeof o.alpha?o.alpha:1}),s=i.alpha-a.alpha,l=2*parseFloat(t)-1,h=((l*s==-1?l:l+s)/(1+l*s)+1)/2,u=1-h;return rD({red:Math.floor(i.red*h+a.red*u),green:Math.floor(i.green*h+a.green*u),blue:Math.floor(i.blue*h+a.blue*u),alpha:i.alpha*parseFloat(t)+a.alpha*(1-parseFloat(t))})}),rF=rL(function(t,e){if("transparent"===e)return e;var n=rO(e),r="number"==typeof n.alpha?n.alpha:1;return rD(re({},n,{alpha:rI(0,1,(100*r+100*parseFloat(t))/100)}))});rL(function(t,e){if("transparent"===e)return e;var n=rM(e);return rR(re({},n,{saturation:rI(0,1,n.saturation+parseFloat(t))}))}),rL(function(t,e){return"transparent"===e?e:rR(re({},rM(e),{hue:parseFloat(t)}))}),rL(function(t,e){return"transparent"===e?e:rR(re({},rM(e),{lightness:parseFloat(t)}))}),rL(function(t,e){return"transparent"===e?e:rR(re({},rM(e),{saturation:parseFloat(t)}))}),rL(function(t,e){return"transparent"===e?e:rz(parseFloat(t),"rgb(0, 0, 0)",e)}),rL(function(t,e){return"transparent"===e?e:rz(parseFloat(t),"rgb(255, 255, 255)",e)}),rL(function(t,e){if("transparent"===e)return e;var n=rO(e),r="number"==typeof n.alpha?n.alpha:1;return rD(re({},n,{alpha:rI(0,1,(100*r-100*parseFloat(t)).toFixed(2)/100)}))});var rB,rU,rq,r$,rH,rV,rY,rW,rZ,rG,rK,rX=Object.freeze({Linear:Object.freeze({None:function(t){return t},In:function(t){return t},Out:function(t){return t},InOut:function(t){return t}}),Quadratic:Object.freeze({In:function(t){return t*t},Out:function(t){return t*(2-t)},InOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)}}),Cubic:Object.freeze({In:function(t){return t*t*t},Out:function(t){return--t*t*t+1},InOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)}}),Quartic:Object.freeze({In:function(t){return t*t*t*t},Out:function(t){return 1- --t*t*t*t},InOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)}}),Quintic:Object.freeze({In:function(t){return t*t*t*t*t},Out:function(t){return--t*t*t*t*t+1},InOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)}}),Sinusoidal:Object.freeze({In:function(t){return 1-Math.sin((1-t)*Math.PI/2)},Out:function(t){return Math.sin(t*Math.PI/2)},InOut:function(t){return .5*(1-Math.sin(Math.PI*(.5-t)))}}),Exponential:Object.freeze({In:function(t){return 0===t?0:Math.pow(1024,t-1)},Out:function(t){return 1===t?1:1-Math.pow(2,-10*t)},InOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(-Math.pow(2,-10*(t-1))+2)}}),Circular:Object.freeze({In:function(t){return 1-Math.sqrt(1-t*t)},Out:function(t){return Math.sqrt(1- --t*t)},InOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)}}),Elastic:Object.freeze({In:function(t){return 0===t?0:1===t?1:-Math.pow(2,10*(t-1))*Math.sin((t-1.1)*5*Math.PI)},Out:function(t){return 0===t?0:1===t?1:Math.pow(2,-10*t)*Math.sin((t-.1)*5*Math.PI)+1},InOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?-.5*Math.pow(2,10*(t-1))*Math.sin((t-1.1)*5*Math.PI):.5*Math.pow(2,-10*(t-1))*Math.sin((t-1.1)*5*Math.PI)+1}}),Back:Object.freeze({In:function(t){return 1===t?1:t*t*(2.70158*t-1.70158)},Out:function(t){return 0===t?0:--t*t*(2.70158*t+1.70158)+1},InOut:function(t){return(t*=2)<1?t*t*(3.5949095*t-2.5949095)*.5:.5*((t-=2)*t*(3.5949095*t+2.5949095)+2)}}),Bounce:Object.freeze({In:function(t){return 1-rX.Bounce.Out(1-t)},Out:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},InOut:function(t){return t<.5?.5*rX.Bounce.In(2*t):.5*rX.Bounce.Out(2*t-1)+.5}}),generatePow:function(t){return void 0===t&&(t=4),t=(t=t<Number.EPSILON?Number.EPSILON:t)>1e4?1e4:t,{In:function(e){return Math.pow(e,t)},Out:function(e){return 1-Math.pow(1-e,t)},InOut:function(e){return e<.5?Math.pow(2*e,t)/2:(1-Math.pow(2-2*e,t))/2+.5}}}}),rQ=function(){return performance.now()},rJ=function(){function t(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];this._tweens={},this._tweensAddedDuringUpdate={},this.add.apply(this,t)}return t.prototype.getAll=function(){var t=this;return Object.keys(this._tweens).map(function(e){return t._tweens[e]})},t.prototype.removeAll=function(){this._tweens={}},t.prototype.add=function(){for(var t,e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];for(var r=0;r<e.length;r++){var i=e[r];null==(t=i._group)||t.remove(i),i._group=this,this._tweens[i.getId()]=i,this._tweensAddedDuringUpdate[i.getId()]=i}},t.prototype.remove=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var n=0;n<t.length;n++){var r=t[n];r._group=void 0,delete this._tweens[r.getId()],delete this._tweensAddedDuringUpdate[r.getId()]}},t.prototype.allStopped=function(){return this.getAll().every(function(t){return!t.isPlaying()})},t.prototype.update=function(t,e){void 0===t&&(t=rQ()),void 0===e&&(e=!0);var n=Object.keys(this._tweens);if(0!==n.length)for(;n.length>0;){this._tweensAddedDuringUpdate={};for(var r=0;r<n.length;r++){var i=this._tweens[n[r]],o=!e;i&&!1===i.update(t,o)&&!e&&this.remove(i)}n=Object.keys(this._tweensAddedDuringUpdate)}},t}(),r0={Linear:function(t,e){var n=t.length-1,r=n*e,i=Math.floor(r),o=r0.Utils.Linear;return e<0?o(t[0],t[1],r):e>1?o(t[n],t[n-1],n-r):o(t[i],t[i+1>n?n:i+1],r-i)},Bezier:function(t,e){for(var n=0,r=t.length-1,i=Math.pow,o=r0.Utils.Bernstein,a=0;a<=r;a++)n+=i(1-e,r-a)*i(e,a)*t[a]*o(r,a);return n},CatmullRom:function(t,e){var n=t.length-1,r=n*e,i=Math.floor(r),o=r0.Utils.CatmullRom;return t[0]===t[n]?(e<0&&(i=Math.floor(r=n*(1+e))),o(t[(i-1+n)%n],t[i],t[(i+1)%n],t[(i+2)%n],r-i)):e<0?t[0]-(o(t[0],t[0],t[1],t[1],-r)-t[0]):e>1?t[n]-(o(t[n],t[n],t[n-1],t[n-1],r-n)-t[n]):o(t[i?i-1:0],t[i],t[n<i+1?n:i+1],t[n<i+2?n:i+2],r-i)},Utils:{Linear:function(t,e,n){return(e-t)*n+t},Bernstein:function(t,e){var n=r0.Utils.Factorial;return n(t)/n(e)/n(t-e)},Factorial:function(){var t=[1];return function(e){var n=1;if(t[e])return t[e];for(var r=e;r>1;r--)n*=r;return t[e]=n,n}}(),CatmullRom:function(t,e,n,r,i){var o=(n-t)*.5,a=(r-e)*.5,s=i*i;return i*s*(2*e-2*n+o+a)+(-3*e+3*n-2*o-a)*s+o*i+e}}},r1=function(){function t(){}return t.nextId=function(){return t._nextId++},t._nextId=0,t}(),r2=new rJ,r5=function(){function t(t,e){this._isPaused=!1,this._pauseStart=0,this._valuesStart={},this._valuesEnd={},this._valuesStartRepeat={},this._duration=1e3,this._isDynamic=!1,this._initialRepeat=0,this._repeat=0,this._yoyo=!1,this._isPlaying=!1,this._reversed=!1,this._delayTime=0,this._startTime=0,this._easingFunction=rX.Linear.None,this._interpolationFunction=r0.Linear,this._chainedTweens=[],this._onStartCallbackFired=!1,this._onEveryStartCallbackFired=!1,this._id=r1.nextId(),this._isChainStopped=!1,this._propertiesAreSetUp=!1,this._goToEnd=!1,this._object=t,"object"==typeof e?(this._group=e,e.add(this)):!0===e&&(this._group=r2,r2.add(this))}return t.prototype.getId=function(){return this._id},t.prototype.isPlaying=function(){return this._isPlaying},t.prototype.isPaused=function(){return this._isPaused},t.prototype.getDuration=function(){return this._duration},t.prototype.to=function(t,e){if(void 0===e&&(e=1e3),this._isPlaying)throw Error("Can not call Tween.to() while Tween is already started or paused. Stop the Tween first.");return this._valuesEnd=t,this._propertiesAreSetUp=!1,this._duration=e<0?0:e,this},t.prototype.duration=function(t){return void 0===t&&(t=1e3),this._duration=t<0?0:t,this},t.prototype.dynamic=function(t){return void 0===t&&(t=!1),this._isDynamic=t,this},t.prototype.start=function(t,e){if(void 0===t&&(t=rQ()),void 0===e&&(e=!1),this._isPlaying)return this;if(this._repeat=this._initialRepeat,this._reversed)for(var n in this._reversed=!1,this._valuesStartRepeat)this._swapEndStartRepeatValues(n),this._valuesStart[n]=this._valuesStartRepeat[n];if(this._isPlaying=!0,this._isPaused=!1,this._onStartCallbackFired=!1,this._onEveryStartCallbackFired=!1,this._isChainStopped=!1,this._startTime=t,this._startTime+=this._delayTime,!this._propertiesAreSetUp||e){if(this._propertiesAreSetUp=!0,!this._isDynamic){var r={};for(var i in this._valuesEnd)r[i]=this._valuesEnd[i];this._valuesEnd=r}this._setupProperties(this._object,this._valuesStart,this._valuesEnd,this._valuesStartRepeat,e)}return this},t.prototype.startFromCurrentValues=function(t){return this.start(t,!0)},t.prototype._setupProperties=function(t,e,n,r,i){for(var o in n){var a=t[o],s=Array.isArray(a),l=s?"array":typeof a,h=!s&&Array.isArray(n[o]);if("undefined"!==l&&"function"!==l){if(h){var u=n[o];if(0===u.length)continue;for(var c=[a],d=0,f=u.length;d<f;d+=1){var p=this._handleRelativeValue(a,u[d]);if(isNaN(p)){h=!1,console.warn("Found invalid interpolation list. Skipping.");break}c.push(p)}h&&(n[o]=c)}if(("object"===l||s)&&a&&!h){for(var g in e[o]=s?[]:{},a)e[o][g]=a[g];r[o]=s?[]:{};var u=n[o];if(!this._isDynamic){var _={};for(var g in u)_[g]=u[g];n[o]=u=_}this._setupProperties(a,e[o],u,r[o],i)}else(void 0===e[o]||i)&&(e[o]=a),s||(e[o]*=1),h?r[o]=n[o].slice().reverse():r[o]=e[o]||0}}},t.prototype.stop=function(){return this._isChainStopped||(this._isChainStopped=!0,this.stopChainedTweens()),this._isPlaying&&(this._isPlaying=!1,this._isPaused=!1,this._onStopCallback&&this._onStopCallback(this._object)),this},t.prototype.end=function(){return this._goToEnd=!0,this.update(this._startTime+this._duration),this},t.prototype.pause=function(t){return void 0===t&&(t=rQ()),this._isPaused||!this._isPlaying||(this._isPaused=!0,this._pauseStart=t),this},t.prototype.resume=function(t){return void 0===t&&(t=rQ()),this._isPaused&&this._isPlaying&&(this._isPaused=!1,this._startTime+=t-this._pauseStart,this._pauseStart=0),this},t.prototype.stopChainedTweens=function(){for(var t=0,e=this._chainedTweens.length;t<e;t++)this._chainedTweens[t].stop();return this},t.prototype.group=function(t){return t?t.add(this):console.warn("tween.group() without args has been removed, use group.add(tween) instead."),this},t.prototype.remove=function(){var t;return null==(t=this._group)||t.remove(this),this},t.prototype.delay=function(t){return void 0===t&&(t=0),this._delayTime=t,this},t.prototype.repeat=function(t){return void 0===t&&(t=0),this._initialRepeat=t,this._repeat=t,this},t.prototype.repeatDelay=function(t){return this._repeatDelayTime=t,this},t.prototype.yoyo=function(t){return void 0===t&&(t=!1),this._yoyo=t,this},t.prototype.easing=function(t){return void 0===t&&(t=rX.Linear.None),this._easingFunction=t,this},t.prototype.interpolation=function(t){return void 0===t&&(t=r0.Linear),this._interpolationFunction=t,this},t.prototype.chain=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return this._chainedTweens=t,this},t.prototype.onStart=function(t){return this._onStartCallback=t,this},t.prototype.onEveryStart=function(t){return this._onEveryStartCallback=t,this},t.prototype.onUpdate=function(t){return this._onUpdateCallback=t,this},t.prototype.onRepeat=function(t){return this._onRepeatCallback=t,this},t.prototype.onComplete=function(t){return this._onCompleteCallback=t,this},t.prototype.onStop=function(t){return this._onStopCallback=t,this},t.prototype.update=function(e,n){var r,i,o=this;if(void 0===e&&(e=rQ()),void 0===n&&(n=t.autoStartOnUpdate),this._isPaused)return!0;if(!this._goToEnd&&!this._isPlaying)if(!n)return!1;else this.start(e,!0);if(this._goToEnd=!1,e<this._startTime)return!0;!1===this._onStartCallbackFired&&(this._onStartCallback&&this._onStartCallback(this._object),this._onStartCallbackFired=!0),!1===this._onEveryStartCallbackFired&&(this._onEveryStartCallback&&this._onEveryStartCallback(this._object),this._onEveryStartCallbackFired=!0);var a=e-this._startTime,s=this._duration+(null!=(r=this._repeatDelayTime)?r:this._delayTime),l=this._duration+this._repeat*s,h=function(){if(0===o._duration||a>l)return 1;var t=Math.trunc(a/s),e=Math.min((a-t*s)/o._duration,1);return 0===e&&a===o._duration?1:e}(),u=this._easingFunction(h);if(this._updateProperties(this._object,this._valuesStart,this._valuesEnd,u),this._onUpdateCallback&&this._onUpdateCallback(this._object,h),0===this._duration||a>=this._duration)if(this._repeat>0){var c=Math.min(Math.trunc((a-this._duration)/s)+1,this._repeat);for(i in isFinite(this._repeat)&&(this._repeat-=c),this._valuesStartRepeat)this._yoyo||"string"!=typeof this._valuesEnd[i]||(this._valuesStartRepeat[i]=this._valuesStartRepeat[i]+parseFloat(this._valuesEnd[i])),this._yoyo&&this._swapEndStartRepeatValues(i),this._valuesStart[i]=this._valuesStartRepeat[i];this._yoyo&&(this._reversed=!this._reversed),this._startTime+=s*c,this._onRepeatCallback&&this._onRepeatCallback(this._object),this._onEveryStartCallbackFired=!1}else{this._onCompleteCallback&&this._onCompleteCallback(this._object);for(var d=0,f=this._chainedTweens.length;d<f;d++)this._chainedTweens[d].start(this._startTime+this._duration,!1);return this._isPlaying=!1,!1}return!0},t.prototype._updateProperties=function(t,e,n,r){for(var i in n)if(void 0!==e[i]){var o=e[i]||0,a=n[i],s=Array.isArray(t[i]),l=Array.isArray(a);!s&&l?t[i]=this._interpolationFunction(a,r):"object"==typeof a&&a?this._updateProperties(t[i],o,a,r):"number"==typeof(a=this._handleRelativeValue(o,a))&&(t[i]=o+(a-o)*r)}},t.prototype._handleRelativeValue=function(t,e){return"string"!=typeof e?e:"+"===e.charAt(0)||"-"===e.charAt(0)?t+parseFloat(e):parseFloat(e)},t.prototype._swapEndStartRepeatValues=function(t){var e=this._valuesStartRepeat[t],n=this._valuesEnd[t];"string"==typeof n?this._valuesStartRepeat[t]=this._valuesStartRepeat[t]+parseFloat(n):this._valuesStartRepeat[t]=this._valuesEnd[t],this._valuesEnd[t]=e},t.autoStartOnUpdate=!1,t}(),r3=(r1.nextId,r2.getAll.bind(r2),r2.removeAll.bind(r2),r2.add.bind(r2),r2.remove.bind(r2),r2.update.bind(r2),n(7394)),r6=n(7267),r8={},r9=[],r4=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,r7=Array.isArray;function it(t,e){for(var n in e)t[n]=e[n];return t}function ie(t){t&&t.parentNode&&t.parentNode.removeChild(t)}function ir(t,e,n,r,i){var o={type:t,props:e,key:n,ref:r,__k:null,__:null,__b:0,__e:null,__c:null,constructor:void 0,__v:null==i?++rq:i,__i:-1,__u:0};return null==i&&null!=rU.vnode&&rU.vnode(o),o}function ii(t){return t.children}function io(t,e){this.props=t,this.context=e}function ia(t,e){if(null==e)return t.__?ia(t.__,t.__i+1):null;for(var n;e<t.__k.length;e++)if(null!=(n=t.__k[e])&&null!=n.__e)return n.__e;return"function"==typeof t.type?ia(t):null}function is(t){(!t.__d&&(t.__d=!0)&&r$.push(t)&&!il.__r++||rH!=rU.debounceRendering)&&((rH=rU.debounceRendering)||rV)(il)}function il(){for(var t,e,n,r,i,o,a=1;r$.length;)r$.length>a&&r$.sort(rY),t=r$.shift(),a=r$.length,t.__d&&(e=void 0,r=(n=t.__v).__e,i=[],o=[],t.__P&&((e=it({},n)).__v=n.__v+1,rU.vnode&&rU.vnode(e),ip(t.__P,e,n,t.__n,t.__P.namespaceURI,32&n.__u?[r]:null,i,null==r?ia(n):r,!!(32&n.__u),o),e.__v=n.__v,e.__.__k[e.__i]=e,ig(i,e,o),e.__e!=r&&function t(e){var n,r;if(null!=(e=e.__)&&null!=e.__c){for(e.__e=e.__c.base=null,n=0;n<e.__k.length;n++)if(null!=(r=e.__k[n])&&null!=r.__e){e.__e=e.__c.base=r.__e;break}return t(e)}}(e)));il.__r=0}function ih(t,e,n,r,i,o,a,s,l,h,u){var c,d,f,p,g,_,m=r&&r.__k||r9,v=e.length;for(l=function(t,e,n,r,i){var o,a,s,l,h,u=n.length,c=u,d=0;for(t.__k=Array(i),o=0;o<i;o++)null!=(a=e[o])&&"boolean"!=typeof a&&"function"!=typeof a?(l=o+d,(a=t.__k[o]="string"==typeof a||"number"==typeof a||"bigint"==typeof a||a.constructor==String?ir(null,a,null,null,null):r7(a)?ir(ii,{children:a},null,null,null):null==a.constructor&&a.__b>0?ir(a.type,a.props,a.key,a.ref?a.ref:null,a.__v):a).__=t,a.__b=t.__b+1,s=null,-1!=(h=a.__i=function(t,e,n,r){var i,o,a=t.key,s=t.type,l=e[n];if(null===l&&null==t.key||l&&a==l.key&&s==l.type&&0==(2&l.__u))return n;if(r>+(null!=l&&0==(2&l.__u)))for(i=n-1,o=n+1;i>=0||o<e.length;){if(i>=0){if((l=e[i])&&0==(2&l.__u)&&a==l.key&&s==l.type)return i;i--}if(o<e.length){if((l=e[o])&&0==(2&l.__u)&&a==l.key&&s==l.type)return o;o++}}return -1}(a,n,l,c))&&(c--,(s=n[h])&&(s.__u|=2)),null==s||null==s.__v?(-1==h&&(i>u?d--:i<u&&d++),"function"!=typeof a.type&&(a.__u|=4)):h!=l&&(h==l-1?d--:h==l+1?d++:(h>l?d--:d++,a.__u|=4))):t.__k[o]=null;if(c)for(o=0;o<u;o++)null!=(s=n[o])&&0==(2&s.__u)&&(s.__e==r&&(r=ia(s)),function t(e,n,r){var i,o;if(rU.unmount&&rU.unmount(e),(i=e.ref)&&(i.current&&i.current!=e.__e||i_(i,null,n)),null!=(i=e.__c)){if(i.componentWillUnmount)try{i.componentWillUnmount()}catch(t){rU.__e(t,n)}i.base=i.__P=null}if(i=e.__k)for(o=0;o<i.length;o++)i[o]&&t(i[o],n,r||"function"!=typeof e.type);r||ie(e.__e),e.__c=e.__=e.__e=void 0}(s,s));return r}(n,e,m,l,v),c=0;c<v;c++)null!=(f=n.__k[c])&&(d=-1==f.__i?r8:m[f.__i]||r8,f.__i=c,_=ip(t,f,d,i,o,a,s,l,h,u),p=f.__e,f.ref&&d.ref!=f.ref&&(d.ref&&i_(d.ref,null,f),u.push(f.ref,f.__c||p,f)),null==g&&null!=p&&(g=p),4&f.__u||d.__k===f.__k?l=function t(e,n,r){var i,o;if("function"==typeof e.type){for(i=e.__k,o=0;i&&o<i.length;o++)i[o]&&(i[o].__=e,n=t(i[o],n,r));return n}e.__e!=n&&(n&&e.type&&!r.contains(n)&&(n=ia(e)),r.insertBefore(e.__e,n||null),n=e.__e);do n=n&&n.nextSibling;while(null!=n&&8==n.nodeType);return n}(f,l,t):"function"==typeof f.type&&void 0!==_?l=_:p&&(l=p.nextSibling),f.__u&=-7);return n.__e=g,l}function iu(t,e,n){"-"==e[0]?t.setProperty(e,null==n?"":n):t[e]=null==n?"":"number"!=typeof n||r4.test(e)?n:n+"px"}function ic(t,e,n,r,i){var o,a;t:if("style"==e)if("string"==typeof n)t.style.cssText=n;else{if("string"==typeof r&&(t.style.cssText=r=""),r)for(e in r)n&&e in n||iu(t.style,e,"");if(n)for(e in n)r&&n[e]==r[e]||iu(t.style,e,n[e])}else if("o"==e[0]&&"n"==e[1])o=e!=(e=e.replace(rW,"$1")),e=(a=e.toLowerCase())in t||"onFocusOut"==e||"onFocusIn"==e?a.slice(2):e.slice(2),t.l||(t.l={}),t.l[e+o]=n,n?r?n.u=r.u:(n.u=rZ,t.addEventListener(e,o?rK:rG,o)):t.removeEventListener(e,o?rK:rG,o);else{if("http://www.w3.org/2000/svg"==i)e=e.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=e&&"height"!=e&&"href"!=e&&"list"!=e&&"form"!=e&&"tabIndex"!=e&&"download"!=e&&"rowSpan"!=e&&"colSpan"!=e&&"role"!=e&&"popover"!=e&&e in t)try{t[e]=null==n?"":n;break t}catch(t){}"function"==typeof n||(null==n||!1===n&&"-"!=e[4]?t.removeAttribute(e):t.setAttribute(e,"popover"==e&&1==n?"":n))}}function id(t){return function(e){if(this.l){var n=this.l[e.type+t];if(null==e.t)e.t=rZ++;else if(e.t<n.u)return;return n(rU.event?rU.event(e):e)}}}function ip(t,e,n,r,i,o,a,s,l,h){var u,c,d,f,p,g,_,m,v,y,b,w,x,k,P,E,O,M=e.type;if(null!=e.constructor)return null;128&n.__u&&(l=!!(32&n.__u),o=[s=e.__e=n.__e]),(u=rU.__b)&&u(e);t:if("function"==typeof M)try{if(m=e.props,v="prototype"in M&&M.prototype.render,y=(u=M.contextType)&&r[u.__c],b=u?y?y.props.value:u.__:r,n.__c?_=(c=e.__c=n.__c).__=c.__E:(v?e.__c=c=new M(m,b):(e.__c=c=new io(m,b),c.constructor=M,c.render=im),y&&y.sub(c),c.props=m,c.state||(c.state={}),c.context=b,c.__n=r,d=c.__d=!0,c.__h=[],c._sb=[]),v&&null==c.__s&&(c.__s=c.state),v&&null!=M.getDerivedStateFromProps&&(c.__s==c.state&&(c.__s=it({},c.__s)),it(c.__s,M.getDerivedStateFromProps(m,c.__s))),f=c.props,p=c.state,c.__v=e,d)v&&null==M.getDerivedStateFromProps&&null!=c.componentWillMount&&c.componentWillMount(),v&&null!=c.componentDidMount&&c.__h.push(c.componentDidMount);else{if(v&&null==M.getDerivedStateFromProps&&m!==f&&null!=c.componentWillReceiveProps&&c.componentWillReceiveProps(m,b),!c.__e&&null!=c.shouldComponentUpdate&&!1===c.shouldComponentUpdate(m,c.__s,b)||e.__v==n.__v){for(e.__v!=n.__v&&(c.props=m,c.state=c.__s,c.__d=!1),e.__e=n.__e,e.__k=n.__k,e.__k.some(function(t){t&&(t.__=e)}),w=0;w<c._sb.length;w++)c.__h.push(c._sb[w]);c._sb=[],c.__h.length&&a.push(c);break t}null!=c.componentWillUpdate&&c.componentWillUpdate(m,c.__s,b),v&&null!=c.componentDidUpdate&&c.__h.push(function(){c.componentDidUpdate(f,p,g)})}if(c.context=b,c.props=m,c.__P=t,c.__e=!1,x=rU.__r,k=0,v){for(c.state=c.__s,c.__d=!1,x&&x(e),u=c.render(c.props,c.state,c.context),P=0;P<c._sb.length;P++)c.__h.push(c._sb[P]);c._sb=[]}else do c.__d=!1,x&&x(e),u=c.render(c.props,c.state,c.context),c.state=c.__s;while(c.__d&&++k<25);c.state=c.__s,null!=c.getChildContext&&(r=it(it({},r),c.getChildContext())),v&&!d&&null!=c.getSnapshotBeforeUpdate&&(g=c.getSnapshotBeforeUpdate(f,p)),E=u,null!=u&&u.type===ii&&null==u.key&&(E=function t(e){return"object"!=typeof e||null==e||e.__b&&e.__b>0?e:r7(e)?e.map(t):it({},e)}(u.props.children)),s=ih(t,r7(E)?E:[E],e,n,r,i,o,a,s,l,h),c.base=e.__e,e.__u&=-161,c.__h.length&&a.push(c),_&&(c.__E=c.__=null)}catch(t){if(e.__v=null,l||null!=o)if(t.then){for(e.__u|=l?160:128;s&&8==s.nodeType&&s.nextSibling;)s=s.nextSibling;o[o.indexOf(s)]=null,e.__e=s}else for(O=o.length;O--;)ie(o[O]);else e.__e=n.__e,e.__k=n.__k;rU.__e(t,e,n)}else null==o&&e.__v==n.__v?(e.__k=n.__k,e.__e=n.__e):s=e.__e=function(t,e,n,r,i,o,a,s,l){var h,u,c,d,f,p,g,_=n.props,m=e.props,v=e.type;if("svg"==v?i="http://www.w3.org/2000/svg":"math"==v?i="http://www.w3.org/1998/Math/MathML":i||(i="http://www.w3.org/1999/xhtml"),null!=o){for(h=0;h<o.length;h++)if((f=o[h])&&"setAttribute"in f==!!v&&(v?f.localName==v:3==f.nodeType)){t=f,o[h]=null;break}}if(null==t){if(null==v)return document.createTextNode(m);t=document.createElementNS(i,v,m.is&&m),s&&(rU.__m&&rU.__m(e,o),s=!1),o=null}if(null==v)_===m||s&&t.data==m||(t.data=m);else{if(o=o&&rB.call(t.childNodes),_=n.props||r8,!s&&null!=o)for(_={},h=0;h<t.attributes.length;h++)_[(f=t.attributes[h]).name]=f.value;for(h in _)if(f=_[h],"children"==h);else if("dangerouslySetInnerHTML"==h)c=f;else if(!(h in m)){if("value"==h&&"defaultValue"in m||"checked"==h&&"defaultChecked"in m)continue;ic(t,h,null,f,i)}for(h in m)f=m[h],"children"==h?d=f:"dangerouslySetInnerHTML"==h?u=f:"value"==h?p=f:"checked"==h?g=f:s&&"function"!=typeof f||_[h]===f||ic(t,h,f,_[h],i);if(u)s||c&&(u.__html==c.__html||u.__html==t.innerHTML)||(t.innerHTML=u.__html),e.__k=[];else if(c&&(t.innerHTML=""),ih("template"==e.type?t.content:t,r7(d)?d:[d],e,n,r,"foreignObject"==v?"http://www.w3.org/1999/xhtml":i,o,a,o?o[0]:n.__k&&ia(n,0),s,l),null!=o)for(h=o.length;h--;)ie(o[h]);s||(h="value","progress"==v&&null==p?t.removeAttribute("value"):null==p||p===t[h]&&("progress"!=v||p)&&("option"!=v||p==_[h])||ic(t,h,p,_[h],i),h="checked",null!=g&&g!=t[h]&&ic(t,h,g,_[h],i))}return t}(n.__e,e,n,r,i,o,a,l,h);return(u=rU.diffed)&&u(e),128&e.__u?void 0:s}function ig(t,e,n){for(var r=0;r<n.length;r++)i_(n[r],n[++r],n[++r]);rU.__c&&rU.__c(e,t),t.some(function(e){try{t=e.__h,e.__h=[],t.some(function(t){t.call(e)})}catch(t){rU.__e(t,e.__v)}})}function i_(t,e,n){try{if("function"==typeof t){var r="function"==typeof t.__u;r&&t.__u(),r&&null==e||(t.__u=t(e))}else t.current=e}catch(t){rU.__e(t,n)}}function im(t,e,n){return this.constructor(t,n)}function iv(t,e,n){var r,i,o,a;e==document&&(e=document.documentElement),rU.__&&rU.__(t,e),i=(r="function"==typeof n)?null:n&&n.__k||e.__k,o=[],a=[],ip(e,t=(!r&&n||e).__k=function(t,e,n){var r,i,o,a={};for(o in e)"key"==o?r=e[o]:"ref"==o?i=e[o]:a[o]=e[o];if(arguments.length>2&&(a.children=arguments.length>3?rB.call(arguments,2):n),"function"==typeof t&&null!=t.defaultProps)for(o in t.defaultProps)null==a[o]&&(a[o]=t.defaultProps[o]);return ir(t,a,r,i,null)}(ii,null,[t]),i||r8,r8,e.namespaceURI,!r&&n?[n]:i?null:e.firstChild?rB.call(e.childNodes):null,o,!r&&n?n:i?i.__e:e.firstChild,r,a),ig(o,t,a)}function iy(t,e){iv(t,e,iy)}function ib(t,e,n){var r,i,o,a,s=it({},t.props);for(o in t.type&&t.type.defaultProps&&(a=t.type.defaultProps),e)"key"==o?r=e[o]:"ref"==o?i=e[o]:s[o]=null==e[o]&&null!=a?a[o]:e[o];return arguments.length>2&&(s.children=arguments.length>3?rB.call(arguments,2):n),ir(t.type,s,r||t.key,i||t.ref,null)}function iw(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function ix(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function ik(t){return(ik="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}rB=r9.slice,rU={__e:function(t,e,n,r){for(var i,o,a;e=e.__;)if((i=e.__c)&&!i.__)try{if((o=i.constructor)&&null!=o.getDerivedStateFromError&&(i.setState(o.getDerivedStateFromError(t)),a=i.__d),null!=i.componentDidCatch&&(i.componentDidCatch(t,r||{}),a=i.__d),a)return i.__E=i}catch(e){t=e}throw t}},rq=0,io.prototype.setState=function(t,e){var n;n=null!=this.__s&&this.__s!=this.state?this.__s:this.__s=it({},this.state),"function"==typeof t&&(t=t(it({},n),this.props)),t&&it(n,t),null!=t&&this.__v&&(e&&this._sb.push(e),is(this))},io.prototype.forceUpdate=function(t){this.__v&&(this.__e=!0,t&&this.__h.push(t),is(this))},io.prototype.render=ii,r$=[],rV="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,rY=function(t,e){return t.__v.__b-e.__v.__b},il.__r=0,rW=/(PointerCapture)$|Capture$/i,rZ=0,rG=id(!1),rK=id(!0);var iP=function(t){if("object"!==ik(t))return t;var e,n=ib(t);return n.props&&(n.props=function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ix(Object(n),!0).forEach(function(e){var r,i;r=e,i=n[e],(r=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e);if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(r))in t?Object.defineProperty(t,r,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[r]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ix(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}({},n.props),null!=n&&null!=(e=n.props)&&e.children&&(n.props.children=Array.isArray(n.props.children)?n.props.children.map(iP):iP(n.props.children))),n},iE=function(t){var e;return null!=(e=ib(t))&&null==e.constructor},iO=function(t,e){delete e.__k,iv(iP(t),e)};!function(t,e){void 0===e&&(e={});var n=e.insertAt;if("undefined"!=typeof document){var r=document.head||document.getElementsByTagName("head")[0],i=document.createElement("style");i.type="text/css","top"===n&&r.firstChild?r.insertBefore(i,r.firstChild):r.appendChild(i),i.styleSheet?i.styleSheet.cssText=t:i.appendChild(document.createTextNode(t))}}(".float-tooltip-kap {\n  position: absolute;\n  width: max-content; /* prevent shrinking near right edge */\n  max-width: max(50%, 150px);\n  padding: 3px 5px;\n  border-radius: 3px;\n  font: 12px sans-serif;\n  color: #eee;\n  background: rgba(0,0,0,0.6);\n  pointer-events: none;\n}\n");var iM=tC({props:{content:{default:!1},offsetX:{triggerUpdate:!1},offsetY:{triggerUpdate:!1}},init:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=n.style,i=!!t&&"object"===ik(t)&&!!t.node&&"function"==typeof t.node,o=(0,r3.A)(i?t.node():t);"static"===o.style("position")&&o.style("position","relative"),e.tooltipEl=o.append("div").attr("class","float-tooltip-kap"),Object.entries(void 0===r?{}:r).forEach(function(t){var n=function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o,a,s=[],l=!0,h=!1;try{o=(n=n.call(t)).next,!1;for(;!(l=(r=o.call(n)).done)&&(s.push(r.value),s.length!==e);l=!0);}catch(t){h=!0,i=t}finally{try{if(!l&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(h)throw i}}return s}}(t,2)||function(t,e){if(t){if("string"==typeof t)return iw(t,2);var n=({}).toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?iw(t,e):void 0}}(t,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),r=n[0],i=n[1];return e.tooltipEl.style(r,i)}),e.tooltipEl.style("left","-10000px").style("display","none");var a="tooltip-".concat(Math.round(1e12*Math.random()));e.mouseInside=!1,o.on("mousemove.".concat(a),function(t){e.mouseInside=!0;var n=(0,r6.A)(t),r=o.node(),i=r.offsetWidth,a=r.offsetHeight,s=[null===e.offsetX||void 0===e.offsetX?"-".concat(n[0]/i*100,"%"):"number"==typeof e.offsetX?"calc(-50% + ".concat(e.offsetX,"px)"):e.offsetX,null===e.offsetY||void 0===e.offsetY?a>130&&a-n[1]<100?"calc(-100% - 6px)":"21px":"number"==typeof e.offsetY?e.offsetY<0?"calc(-100% - ".concat(Math.abs(e.offsetY),"px)"):"".concat(e.offsetY,"px"):e.offsetY];e.tooltipEl.style("left",n[0]+"px").style("top",n[1]+"px").style("transform","translate(".concat(s.join(","),")")),e.content&&e.tooltipEl.style("display","inline")}),o.on("mouseover.".concat(a),function(){e.mouseInside=!0,e.content&&e.tooltipEl.style("display","inline")}),o.on("mouseout.".concat(a),function(){e.mouseInside=!1,e.tooltipEl.style("display","none")})},update:function(t){t.tooltipEl.style("display",t.content&&t.mouseInside?"inline":"none"),t.content?t.content instanceof HTMLElement?(t.tooltipEl.text(""),t.tooltipEl.append(function(){return t.content})):"string"==typeof t.content?t.tooltipEl.html(t.content):iE(t.content)?(t.tooltipEl.text(""),iO(t.content,t.tooltipEl.node())):(t.tooltipEl.style("display","none"),console.warn("Tooltip content is invalid, skipping.",t.content,t.content.toString())):t.tooltipEl.text("")}});function iS(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function ij(t){return function(t){if(Array.isArray(t))return iS(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||iC(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function iC(t,e){if(t){if("string"==typeof t)return iS(t,e);var n=({}).toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?iS(t,e):void 0}}!function(t,e){void 0===e&&(e={});var n=e.insertAt;if("undefined"!=typeof document){var r=document.head||document.getElementsByTagName("head")[0],i=document.createElement("style");i.type="text/css","top"===n&&r.firstChild?r.insertBefore(i,r.firstChild):r.appendChild(i),i.styleSheet?i.styleSheet.cssText=t:i.appendChild(document.createTextNode(t))}}(".scene-nav-info {\n  position: absolute;\n  bottom: 5px;\n  width: 100%;\n  text-align: center;\n  color: slategrey;\n  opacity: 0.7;\n  font-size: 10px;\n  font-family: sans-serif;\n  pointer-events: none;\n  user-select: none;\n}\n\n.scene-container canvas:focus {\n  outline: none;\n}");var iT=window.THREE?window.THREE:{WebGLRenderer:e1.JeP,Scene:r.Z58,PerspectiveCamera:r.ubm,Raycaster:r.tBo,SRGBColorSpace:r.er$,TextureLoader:r.Tap,Vector2:r.I9Y,Vector3:r.Pq0,Box3:r.NRn,Color:r.Q1f,Mesh:r.eaF,SphereGeometry:r.Gu$,MeshBasicMaterial:r.V9B,BackSide:r.hsX,Clock:r.zD7},iA=tC({props:{width:{default:window.innerWidth,onChange:function(t,e,n){isNaN(t)&&(e.width=n)}},height:{default:window.innerHeight,onChange:function(t,e,n){isNaN(t)&&(e.height=n)}},viewOffset:{default:[0,0]},backgroundColor:{default:"#000011"},backgroundImageUrl:{},onBackgroundImageLoaded:{},showNavInfo:{default:!0},skyRadius:{default:5e4},objects:{default:[]},lights:{default:[]},enablePointerInteraction:{default:!0,onChange:function(t,e){e.hoverObj=null,e.tooltip&&e.tooltip.content(null)},triggerUpdate:!1},pointerRaycasterThrottleMs:{default:50,triggerUpdate:!1},lineHoverPrecision:{default:1,triggerUpdate:!1},pointsHoverPrecision:{default:1,triggerUpdate:!1},hoverOrderComparator:{triggerUpdate:!1},hoverFilter:{default:function(){return!0},triggerUpdate:!1},tooltipContent:{triggerUpdate:!1},hoverDuringDrag:{default:!1,triggerUpdate:!1},clickAfterDrag:{default:!1,triggerUpdate:!1},onHover:{default:function(){},triggerUpdate:!1},onClick:{default:function(){},triggerUpdate:!1},onRightClick:{triggerUpdate:!1}},methods:{tick:function(t){if(t.initialised){t.controls.enabled&&t.controls.update&&t.controls.update(Math.min(1,t.clock.getDelta())),t.postProcessingComposer?t.postProcessingComposer.render():t.renderer.render(t.scene,t.camera),t.extraRenderers.forEach(function(e){return e.render(t.scene,t.camera)});var e=+new Date;if(t.enablePointerInteraction&&e-t.lastRaycasterCheck>=t.pointerRaycasterThrottleMs){t.lastRaycasterCheck=e;var n=null;if(t.hoverDuringDrag||!t.isPointerDragging){var r=this.intersectingObjects(t.pointerPos.x,t.pointerPos.y);t.hoverOrderComparator&&r.sort(function(e,n){return t.hoverOrderComparator(e.object,n.object)});var i=r.find(function(e){return t.hoverFilter(e.object)})||null;n=i?i.object:null,t.intersection=i||null}n!==t.hoverObj&&(t.onHover(n,t.hoverObj,t.intersection),t.tooltip.content(n&&tT(t.tooltipContent)(n,t.intersection)||null),t.hoverObj=n)}t.tweenGroup.update()}return this},getPointerPos:function(t){var e=t.pointerPos;return{x:e.x,y:e.y}},cameraPosition:function(t,e,n,r){var i=t.camera;if(e&&t.initialised){var o=n||{x:0,y:0,z:0};if(r){var a=Object.assign({},i.position),s=u();t.tweenGroup.add(new r5(a).to(e,r).easing(rX.Quadratic.Out).onUpdate(l).start()),t.tweenGroup.add(new r5(s).to(o,r/3).easing(rX.Quadratic.Out).onUpdate(h).start())}else l(e),h(o);return this}return Object.assign({},i.position,{lookAt:u()});function l(t){var e=t.x,n=t.y,r=t.z;void 0!==e&&(i.position.x=e),void 0!==n&&(i.position.y=n),void 0!==r&&(i.position.z=r)}function h(e){var n=new iT.Vector3(e.x,e.y,e.z);t.controls.target?t.controls.target=n:i.lookAt(n)}function u(){return Object.assign(new iT.Vector3(0,0,-1e3).applyQuaternion(i.quaternion).add(i.position))}},zoomToFit:function(t){for(var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10,r=arguments.length,i=Array(r>3?r-3:0),o=3;o<r;o++)i[o-3]=arguments[o];return this.fitToBbox(this.getBbox.apply(this,i),e,n)},fitToBbox:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:10,i=t.camera;if(e){var o=new iT.Vector3(0,0,0),a=2*Math.max.apply(Math,ij(Object.entries(e).map(function(t){var e=function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o,a,s=[],l=!0,h=!1;try{o=(n=n.call(t)).next,!1;for(;!(l=(r=o.call(n)).done)&&(s.push(r.value),s.length!==e);l=!0);}catch(t){h=!0,i=t}finally{try{if(!l&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(h)throw i}}return s}}(t,2)||iC(t,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),n=e[0],r=e[1];return Math.max.apply(Math,ij(r.map(function(t){return Math.abs(o[n]-t)})))})))/Math.atan((1-2*r/t.height)*i.fov*Math.PI/180),s=a/i.aspect,l=Math.max(a,s);if(l>0){var h=o.clone().sub(i.position).normalize().multiplyScalar(-l);this.cameraPosition(h,o,n)}}return this},getBbox:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(){return!0},n=new iT.Box3(new iT.Vector3(0,0,0),new iT.Vector3(0,0,0)),r=t.objects.filter(e);return r.length?(r.forEach(function(t){return n.expandByObject(t)}),Object.assign.apply(Object,ij(["x","y","z"].map(function(t){var e,r,i,o;return e={},r=t,i=[n.min[t],n.max[t]],(r="symbol"==typeof(o=function(t,e){if("object"!=typeof t||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e);if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(r,"string"))?o:o+"")in e?Object.defineProperty(e,r,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[r]=i,e})))):null},getScreenCoords:function(t,e,n,r){var i=new iT.Vector3(e,n,r);return i.project(this.camera()),{x:(i.x+1)*t.width/2,y:-(i.y-1)*t.height/2}},getSceneCoords:function(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=new iT.Vector2(e/t.width*2-1,-(2*(n/t.height))+1),o=new iT.Raycaster;return o.setFromCamera(i,t.camera),Object.assign({},o.ray.at(r,new iT.Vector3))},intersectingObjects:function(t,e,n){var r=new iT.Vector2(e/t.width*2-1,-(2*(n/t.height))+1),i=new iT.Raycaster;return i.params.Line.threshold=t.lineHoverPrecision,i.params.Points.threshold=t.pointsHoverPrecision,i.setFromCamera(r,t.camera),i.intersectObjects(t.objects,!0)},renderer:function(t){return t.renderer},scene:function(t){return t.scene},camera:function(t){return t.camera},postProcessingComposer:function(t){return t.postProcessingComposer},controls:function(t){return t.controls},tbControls:function(t){return t.controls}},stateInit:function(){return{scene:new iT.Scene,camera:new iT.PerspectiveCamera,clock:new iT.Clock,tweenGroup:new rJ,lastRaycasterCheck:0}},init:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=n.controlType,i=void 0===r?"trackball":r,o=n.useWebGPU,a=n.rendererConfig,s=n.extraRenderers,l=n.waitForLoadComplete;t.innerHTML="",t.appendChild(e.container=document.createElement("div")),e.container.className="scene-container",e.container.style.position="relative",e.container.appendChild(e.navInfo=document.createElement("div")),e.navInfo.className="scene-nav-info",e.navInfo.textContent=({orbit:"Left-click: rotate, Mouse-wheel/middle-click: zoom, Right-click: pan",trackball:"Left-click: rotate, Mouse-wheel/middle-click: zoom, Right-click: pan",fly:"WASD: move, R|F: up | down, Q|E: roll, up|down: pitch, left|right: yaw"})[i]||"",e.navInfo.style.display=e.showNavInfo?null:"none",e.tooltip=new iM(e.container),e.pointerPos=new iT.Vector2,e.pointerPos.x=-2,e.pointerPos.y=-2,["pointermove","pointerdown"].forEach(function(t){return e.container.addEventListener(t,function(n){if("pointerdown"===t&&(e.isPointerPressed=!0),!e.isPointerDragging&&"pointermove"===n.type&&(n.pressure>0||e.isPointerPressed)&&("touch"!==n.pointerType||void 0===n.movementX||[n.movementX,n.movementY].some(function(t){return Math.abs(t)>1}))&&(e.isPointerDragging=!0),e.enablePointerInteraction){var r,i,o,a=(r=e.container.getBoundingClientRect(),i=window.pageXOffset||document.documentElement.scrollLeft,o=window.pageYOffset||document.documentElement.scrollTop,{top:r.top+o,left:r.left+i});e.pointerPos.x=n.pageX-a.left,e.pointerPos.y=n.pageY-a.top}},{passive:!0})}),e.container.addEventListener("pointerup",function(t){e.isPointerPressed&&(e.isPointerPressed=!1,(!e.isPointerDragging||(e.isPointerDragging=!1,e.clickAfterDrag))&&requestAnimationFrame(function(){0===t.button&&e.onClick(e.hoverObj||null,t,e.intersection),2===t.button&&e.onRightClick&&e.onRightClick(e.hoverObj||null,t,e.intersection)}))},{passive:!0,capture:!0}),e.container.addEventListener("contextmenu",function(t){e.onRightClick&&t.preventDefault()}),e.renderer=new(void 0!==o&&o?e2.uV1:iT.WebGLRenderer)(Object.assign({antialias:!0,alpha:!0},void 0===a?{}:a)),e.renderer.setPixelRatio(Math.min(2,window.devicePixelRatio)),e.container.appendChild(e.renderer.domElement),e.extraRenderers=void 0===s?[]:s,e.extraRenderers.forEach(function(t){t.domElement.style.position="absolute",t.domElement.style.top="0px",t.domElement.style.pointerEvents="none",e.container.appendChild(t.domElement)}),e.postProcessingComposer=new n7(e.renderer),e.postProcessingComposer.addPass(new rt(e.scene,e.camera)),e.controls=new({trackball:ns,orbit:nT,fly:nY})[i](e.camera,e.renderer.domElement),"fly"===i&&(e.controls.movementSpeed=300,e.controls.rollSpeed=Math.PI/6,e.controls.dragToLook=!0),("trackball"===i||"orbit"===i)&&(e.controls.minDistance=.1,e.controls.maxDistance=e.skyRadius,e.controls.addEventListener("start",function(){e.controlsEngaged=!0}),e.controls.addEventListener("change",function(){e.controlsEngaged&&(e.controlsDragging=!0)}),e.controls.addEventListener("end",function(){e.controlsEngaged=!1,e.controlsDragging=!1})),[e.renderer,e.postProcessingComposer].concat(ij(e.extraRenderers)).forEach(function(t){return t.setSize(e.width,e.height)}),e.camera.aspect=e.width/e.height,e.camera.updateProjectionMatrix(),e.camera.position.z=1e3,e.scene.add(e.skysphere=new iT.Mesh),e.skysphere.visible=!1,e.loadComplete=e.scene.visible=!(void 0===l||l),window.scene=e.scene},update:function(t,e){if(t.width&&t.height&&(e.hasOwnProperty("width")||e.hasOwnProperty("height"))){var n,r=t.width,i=t.height;t.container.style.width="".concat(r,"px"),t.container.style.height="".concat(i,"px"),[t.renderer,t.postProcessingComposer].concat(ij(t.extraRenderers)).forEach(function(t){return t.setSize(r,i)}),t.camera.aspect=r/i;var o=t.viewOffset.slice(0,2);o.some(function(t){return t})&&(n=t.camera).setViewOffset.apply(n,[r,i].concat(ij(o),[r,i])),t.camera.updateProjectionMatrix()}if(e.hasOwnProperty("viewOffset")){var a,s=t.width,l=t.height,h=t.viewOffset.slice(0,2);h.some(function(t){return t})?(a=t.camera).setViewOffset.apply(a,[s,l].concat(ij(h),[s,l])):t.camera.clearViewOffset()}if(e.hasOwnProperty("skyRadius")&&t.skyRadius&&(t.controls.hasOwnProperty("maxDistance")&&e.skyRadius&&(t.controls.maxDistance=Math.min(t.controls.maxDistance,t.skyRadius)),t.camera.far=2.5*t.skyRadius,t.camera.updateProjectionMatrix(),t.skysphere.geometry=new iT.SphereGeometry(t.skyRadius)),e.hasOwnProperty("backgroundColor")){var u=rO(t.backgroundColor).alpha;void 0===u&&(u=1),t.renderer.setClearColor(new iT.Color(rF(1,t.backgroundColor)),u)}function c(){t.loadComplete=t.scene.visible=!0}e.hasOwnProperty("backgroundImageUrl")&&(t.backgroundImageUrl?new iT.TextureLoader().load(t.backgroundImageUrl,function(e){e.colorSpace=iT.SRGBColorSpace,t.skysphere.material=new iT.MeshBasicMaterial({map:e,side:iT.BackSide}),t.skysphere.visible=!0,t.onBackgroundImageLoaded&&setTimeout(t.onBackgroundImageLoaded),t.loadComplete||c()}):(t.skysphere.visible=!1,t.skysphere.material.map=null,t.loadComplete||c())),e.hasOwnProperty("showNavInfo")&&(t.navInfo.style.display=t.showNavInfo?null:"none"),e.hasOwnProperty("lights")&&((e.lights||[]).forEach(function(e){return t.scene.remove(e)}),t.lights.forEach(function(e){return t.scene.add(e)})),e.hasOwnProperty("objects")&&((e.objects||[]).forEach(function(e){return t.scene.remove(e)}),t.objects.forEach(function(e){return t.scene.add(e)}))}});function iD(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function iR(t,e,n){var r;return(e="symbol"==typeof(r=function(t,e){if("object"!=typeof t||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e);if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"))?r:r+"")in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function iL(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function iI(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?iL(Object(n),!0).forEach(function(e){iR(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):iL(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function iN(t){return function(t){if(Array.isArray(t))return iD(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return iD(t,void 0);var n=({}).toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?iD(t,e):void 0}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function iz(t,e){var n=new e;return n._destructor&&n._destructor(),{linkProp:function(e){return{default:n[e](),onChange:function(n,r){r[t][e](n)},triggerUpdate:!1}},linkMethod:function(e){return function(n){for(var r=n[t],i=arguments.length,o=Array(i>1?i-1:0),a=1;a<i;a++)o[a-1]=arguments[a];var s=r[e].apply(r,o);return s===r?this:s}}}}!function(t,e){void 0===e&&(e={});var n=e.insertAt;if("undefined"!=typeof document){var r=document.head||document.getElementsByTagName("head")[0],i=document.createElement("style");i.type="text/css","top"===n&&r.firstChild?r.insertBefore(i,r.firstChild):r.appendChild(i),i.styleSheet?i.styleSheet.cssText=t:i.appendChild(document.createTextNode(t))}}(".graph-info-msg {\n  top: 50%;\n  width: 100%;\n  text-align: center;\n  color: lavender;\n  opacity: 0.7;\n  font-size: 22px;\n  position: absolute;\n  font-family: Sans-serif;\n}\n\n.scene-container .clickable {\n  cursor: pointer;\n}\n\n.scene-container .grabbable {\n  cursor: move;\n  cursor: grab;\n  cursor: -moz-grab;\n  cursor: -webkit-grab;\n}\n\n.scene-container .grabbable:active {\n  cursor: grabbing;\n  cursor: -moz-grabbing;\n  cursor: -webkit-grabbing;\n}");var iF=window.THREE?window.THREE:{AmbientLight:r.$p8,DirectionalLight:r.ZyN,REVISION:r.sPf},iB=iz("forceGraph",e0),iU=Object.assign.apply(Object,iN(["jsonUrl","graphData","numDimensions","dagMode","dagLevelDistance","dagNodeFilter","onDagError","nodeRelSize","nodeId","nodeVal","nodeResolution","nodeColor","nodeAutoColorBy","nodeOpacity","nodeVisibility","nodeThreeObject","nodeThreeObjectExtend","nodePositionUpdate","linkSource","linkTarget","linkVisibility","linkColor","linkAutoColorBy","linkOpacity","linkWidth","linkResolution","linkCurvature","linkCurveRotation","linkMaterial","linkThreeObject","linkThreeObjectExtend","linkPositionUpdate","linkDirectionalArrowLength","linkDirectionalArrowColor","linkDirectionalArrowRelPos","linkDirectionalArrowResolution","linkDirectionalParticles","linkDirectionalParticleSpeed","linkDirectionalParticleWidth","linkDirectionalParticleColor","linkDirectionalParticleResolution","forceEngine","d3AlphaDecay","d3VelocityDecay","d3AlphaMin","ngraphPhysics","warmupTicks","cooldownTicks","cooldownTime","onEngineTick","onEngineStop"].map(function(t){return iR({},t,iB.linkProp(t))}))),iq=Object.assign.apply(Object,iN(["refresh","getGraphBbox","d3Force","d3ReheatSimulation","emitParticle"].map(function(t){return iR({},t,iB.linkMethod(t))}))),i$=iz("renderObjs",iA),iH=Object.assign.apply(Object,iN(["width","height","backgroundColor","showNavInfo","enablePointerInteraction"].map(function(t){return iR({},t,i$.linkProp(t))}))),iV=Object.assign.apply(Object,iN(["lights","cameraPosition","postProcessingComposer"].map(function(t){return iR({},t,i$.linkMethod(t))})).concat([{graph2ScreenCoords:i$.linkMethod("getScreenCoords"),screen2GraphCoords:i$.linkMethod("getSceneCoords")}])),iY=tC({props:iI(iI({nodeLabel:{default:"name",triggerUpdate:!1},linkLabel:{default:"name",triggerUpdate:!1},linkHoverPrecision:{default:1,onChange:function(t,e){return e.renderObjs.lineHoverPrecision(t)},triggerUpdate:!1},enableNavigationControls:{default:!0,onChange:function(t,e){var n=e.renderObjs.controls();n&&(n.enabled=t,t&&n.domElement&&n.domElement.dispatchEvent(new PointerEvent("pointerup")))},triggerUpdate:!1},enableNodeDrag:{default:!0,triggerUpdate:!1},onNodeDrag:{default:function(){},triggerUpdate:!1},onNodeDragEnd:{default:function(){},triggerUpdate:!1},onNodeClick:{triggerUpdate:!1},onNodeRightClick:{triggerUpdate:!1},onNodeHover:{triggerUpdate:!1},onLinkClick:{triggerUpdate:!1},onLinkRightClick:{triggerUpdate:!1},onLinkHover:{triggerUpdate:!1},onBackgroundClick:{triggerUpdate:!1},onBackgroundRightClick:{triggerUpdate:!1}},iU),iH),methods:iI(iI({zoomToFit:function(t,e,n){for(var r,i=arguments.length,o=Array(i>3?i-3:0),a=3;a<i;a++)o[a-3]=arguments[a];return t.renderObjs.fitToBbox((r=t.forceGraph).getGraphBbox.apply(r,o),e,n),this},pauseAnimation:function(t){return null!==t.animationFrameRequestId&&(cancelAnimationFrame(t.animationFrameRequestId),t.animationFrameRequestId=null),this},resumeAnimation:function(t){return null===t.animationFrameRequestId&&this._animationCycle(),this},_animationCycle:function(t){t.enablePointerInteraction&&(this.renderer().domElement.style.cursor=null),t.forceGraph.tickFrame(),t.renderObjs.tick(),t.animationFrameRequestId=requestAnimationFrame(this._animationCycle)},scene:function(t){return t.renderObjs.scene()},camera:function(t){return t.renderObjs.camera()},renderer:function(t){return t.renderObjs.renderer()},controls:function(t){return t.renderObjs.controls()},tbControls:function(t){return t.renderObjs.tbControls()},_destructor:function(){this.pauseAnimation(),this.graphData({nodes:[],links:[]})}},iq),iV),stateInit:function(t){var e=t.controlType,n=t.rendererConfig,r=t.extraRenderers,i=new e0;return{forceGraph:i,renderObjs:iA({controlType:e,rendererConfig:n,extraRenderers:r}).objects([i]).lights([new iF.AmbientLight(0xcccccc,Math.PI),new iF.DirectionalLight(0xffffff,.6*Math.PI)])}},init:function(t,e){t.innerHTML="",t.appendChild(e.container=document.createElement("div")),e.container.style.position="relative";var n,r=document.createElement("div");e.container.appendChild(r),e.renderObjs(r);var i=e.renderObjs.camera(),o=e.renderObjs.renderer(),a=e.renderObjs.controls();a.enabled=!!e.enableNavigationControls,e.lastSetCameraZ=i.position.z,e.container.appendChild(n=document.createElement("div")),n.className="graph-info-msg",n.textContent="",e.forceGraph.onLoading(function(){n.textContent="Loading..."}).onFinishLoading(function(){n.textContent=""}).onUpdate(function(){e.graphData=e.forceGraph.graphData(),0===i.position.x&&0===i.position.y&&i.position.z===e.lastSetCameraZ&&e.graphData.nodes.length&&(i.lookAt(e.forceGraph.position),e.lastSetCameraZ=i.position.z=170*Math.cbrt(e.graphData.nodes.length))}).onFinishUpdate(function(){if(e._dragControls){var t=e.graphData.nodes.find(function(t){return t.__initialFixedPos&&!t.__disposeControlsAfterDrag});t?t.__disposeControlsAfterDrag=!0:e._dragControls.dispose(),e._dragControls=void 0}if(e.enableNodeDrag&&e.enablePointerInteraction&&"d3"===e.forceEngine){var n=e._dragControls=new v(e.graphData.nodes.map(function(t){return t.__threeObj}).filter(function(t){return t}),i,o.domElement);n.addEventListener("dragstart",function(t){var e=iW(t.object);if(e){a.enabled=!1,t.object.__initialPos=t.object.position.clone(),t.object.__prevPos=t.object.position.clone();var n=e.__data;n.__initialFixedPos||(n.__initialFixedPos={fx:n.fx,fy:n.fy,fz:n.fz}),n.__initialPos||(n.__initialPos={x:n.x,y:n.y,z:n.z}),["x","y","z"].forEach(function(t){return n["f".concat(t)]=n[t]}),o.domElement.classList.add("grabbable")}}),n.addEventListener("drag",function(t){var n=iW(t.object);if(n){if(!t.object.hasOwnProperty("__graphObjType")){var r=t.object.__initialPos,i=t.object.__prevPos,o=t.object.position;n.position.add(o.clone().sub(i)),i.copy(o),o.copy(r)}var a=n.__data,s=n.position,l={x:s.x-a.x,y:s.y-a.y,z:s.z-a.z};["x","y","z"].forEach(function(t){return a["f".concat(t)]=a[t]=s[t]}),e.forceGraph.d3AlphaTarget(.3).resetCountdown(),a.__dragged=!0,e.onNodeDrag(a,l)}}),n.addEventListener("dragend",function(t){var r=iW(t.object);if(r){delete t.object.__initialPos,delete t.object.__prevPos;var i=r.__data;i.__disposeControlsAfterDrag&&(n.dispose(),delete i.__disposeControlsAfterDrag);var s=i.__initialFixedPos,l=i.__initialPos,h={x:l.x-i.x,y:l.y-i.y,z:l.z-i.z};s&&(["x","y","z"].forEach(function(t){var e="f".concat(t);void 0===s[e]&&delete i[e]}),delete i.__initialFixedPos,delete i.__initialPos,i.__dragged&&(delete i.__dragged,e.onNodeDragEnd(i,h))),e.forceGraph.d3AlphaTarget(0).resetCountdown(),e.enableNavigationControls&&(a.enabled=!0,a.domElement&&a.domElement.ownerDocument&&a.domElement.ownerDocument.dispatchEvent(new PointerEvent("pointerup",{pointerType:"touch"}))),o.domElement.classList.remove("grabbable")}})}}),iF.REVISION<155&&(e.renderObjs.renderer().useLegacyLights=!1),e.renderObjs.hoverOrderComparator(function(t,e){var n=iW(t);if(!n)return 1;var r=iW(e);if(!r)return -1;var i=function(t){return"node"===t.__graphObjType};return i(r)-i(n)}).tooltipContent(function(t){var n=iW(t);return n&&tT(e["".concat(n.__graphObjType,"Label")])(n.__data)||""}).hoverDuringDrag(!1).onHover(function(t){var n=iW(t);if(n!==e.hoverObj){var r=e.hoverObj?e.hoverObj.__graphObjType:null,i=e.hoverObj?e.hoverObj.__data:null,a=n?n.__graphObjType:null,s=n?n.__data:null;if(r&&r!==a){var l=e["on".concat("node"===r?"Node":"Link","Hover")];l&&l(null,i)}if(a){var h=e["on".concat("node"===a?"Node":"Link","Hover")];h&&h(s,r===a?i:null)}o.domElement.classList[n&&e["on".concat("node"===a?"Node":"Link","Click")]||!n&&e.onBackgroundClick?"add":"remove"]("clickable"),e.hoverObj=n}}).clickAfterDrag(!1).onClick(function(t,n){var r=iW(t);if(r){var i=e["on".concat("node"===r.__graphObjType?"Node":"Link","Click")];i&&i(r.__data,n)}else e.onBackgroundClick&&e.onBackgroundClick(n)}).onRightClick(function(t,n){var r=iW(t);if(r){var i=e["on".concat("node"===r.__graphObjType?"Node":"Link","RightClick")];i&&i(r.__data,n)}else e.onBackgroundRightClick&&e.onBackgroundRightClick(n)}),this._animationCycle()}});function iW(t){for(var e=t;e&&!e.hasOwnProperty("__graphObjType");)e=e.parent;return e}},5704:(t,e,n)=>{t.exports=function(t){return Function("bodies","settings","random",i(t))},t.exports.generateFunctionBody=i;let r=n(455);function i(t){let e=r(t);return`
  var boundingBox = {
    ${e("min_{var}: 0, max_{var}: 0,",{indent:4})}
  };

  return {
    box: boundingBox,

    update: updateBoundingBox,

    reset: resetBoundingBox,

    getBestNewPosition: function (neighbors) {
      var ${e("base_{var} = 0",{join:", "})};

      if (neighbors.length) {
        for (var i = 0; i < neighbors.length; ++i) {
          let neighborPos = neighbors[i].pos;
          ${e("base_{var} += neighborPos.{var};",{indent:10})}
        }

        ${e("base_{var} /= neighbors.length;",{indent:8})}
      } else {
        ${e("base_{var} = (boundingBox.min_{var} + boundingBox.max_{var}) / 2;",{indent:8})}
      }

      var springLength = settings.springLength;
      return {
        ${e("{var}: base_{var} + (random.nextDouble() - 0.5) * springLength,",{indent:8})}
      };
    }
  };

  function updateBoundingBox() {
    var i = bodies.length;
    if (i === 0) return; // No bodies - no borders.

    ${e("var max_{var} = -Infinity;",{indent:4})}
    ${e("var min_{var} = Infinity;",{indent:4})}

    while(i--) {
      // this is O(n), it could be done faster with quadtree, if we check the root node bounds
      var bodyPos = bodies[i].pos;
      ${e("if (bodyPos.{var} < min_{var}) min_{var} = bodyPos.{var};",{indent:6})}
      ${e("if (bodyPos.{var} > max_{var}) max_{var} = bodyPos.{var};",{indent:6})}
    }

    ${e("boundingBox.min_{var} = min_{var};",{indent:4})}
    ${e("boundingBox.max_{var} = max_{var};",{indent:4})}
  }

  function resetBoundingBox() {
    ${e("boundingBox.min_{var} = boundingBox.max_{var} = 0;",{indent:4})}
  }
`}},6106:t=>{t.exports=function(t){var e,n,r=t;if(!r)throw Error("Eventify cannot use falsy object as events subject");for(var i=["on","fire","off"],o=0;o<i.length;++o)if(r.hasOwnProperty(i[o]))throw Error("Subject cannot be eventified, since it already has property '"+i[o]+"'");var a=(e=t,n=Object.create(null),{on:function(t,r,i){if("function"!=typeof r)throw Error("callback is expected to be a function");var o=n[t];return o||(o=n[t]=[]),o.push({callback:r,ctx:i}),e},off:function(t,r){if(void 0===t)return n=Object.create(null),e;if(n[t])if("function"!=typeof r)delete n[t];else for(var i=n[t],o=0;o<i.length;++o)i[o].callback===r&&i.splice(o,1);return e},fire:function(t){var r,i=n[t];if(!i)return e;arguments.length>1&&(r=Array.prototype.splice.call(arguments,1));for(var o=0;o<i.length;++o){var a=i[o];a.callback.apply(a.ctx,r)}return e}});return t.on=a.on,t.off=a.off,t.fire=a.fire,t}},6568:(t,e,n)=>{let r=n(455);function i(t){let e=r(t);return`
  var length = bodies.length;
  if (length === 0) return 0;

  ${e("var d{var} = 0, t{var} = 0;",{indent:2})}

  for (var i = 0; i < length; ++i) {
    var body = bodies[i];
    if (body.isPinned) continue;

    if (adaptiveTimeStepWeight && body.springCount) {
      timeStep = (adaptiveTimeStepWeight * body.springLength/body.springCount);
    }

    var coeff = timeStep / body.mass;

    ${e("body.velocity.{var} += coeff * body.force.{var};",{indent:4})}
    ${e("var v{var} = body.velocity.{var};",{indent:4})}
    var v = Math.sqrt(${e("v{var} * v{var}",{join:" + "})});

    if (v > 1) {
      // We normalize it so that we move within timeStep range. 
      // for the case when v <= 1 - we let velocity to fade out.
      ${e("body.velocity.{var} = v{var} / v;",{indent:6})}
    }

    ${e("d{var} = timeStep * body.velocity.{var};",{indent:4})}

    ${e("body.pos.{var} += d{var};",{indent:4})}

    ${e("t{var} += Math.abs(d{var});",{indent:4})}
  }

  return (${e("t{var} * t{var}",{join:" + "})})/length;
`}t.exports=function(t){return Function("bodies","timeStep","adaptiveTimeStepWeight",i(t))},t.exports.generateIntegratorFunctionBody=i},7411:t=>{t.exports=function t(e,n){var r;if(e||(e={}),n){for(r in n)if(n.hasOwnProperty(r)){var i=e.hasOwnProperty(r),o=typeof n[r];i&&typeof e[r]===o?"object"===o&&(e[r]=t(e[r],n[r])):e[r]=n[r]}}return e}},7550:(t,e,n)=>{let r=n(455),i=n(4525);function o(t){let e=r(t),n=Math.pow(2,t);return`
${u()}
${h(t)}
${a(t)}
${l(t)}
${s(t)}

function createQuadTree(options, random) {
  options = options || {};
  options.gravity = typeof options.gravity === 'number' ? options.gravity : -1;
  options.theta = typeof options.theta === 'number' ? options.theta : 0.8;

  var gravity = options.gravity;
  var updateQueue = [];
  var insertStack = new InsertStack();
  var theta = options.theta;

  var nodesCache = [];
  var currentInCache = 0;
  var root = newNode();

  return {
    insertBodies: insertBodies,

    /**
     * Gets root node if it is present
     */
    getRoot: function() {
      return root;
    },

    updateBodyForce: update,

    options: function(newOptions) {
      if (newOptions) {
        if (typeof newOptions.gravity === 'number') {
          gravity = newOptions.gravity;
        }
        if (typeof newOptions.theta === 'number') {
          theta = newOptions.theta;
        }

        return this;
      }

      return {
        gravity: gravity,
        theta: theta
      };
    }
  };

  function newNode() {
    // To avoid pressure on GC we reuse nodes.
    var node = nodesCache[currentInCache];
    if (node) {
${function(t){let e=[];for(let r=0;r<n;++r)e.push(`${t}quad${r} = null;`);return e.join("\n")}("      node.")}
      node.body = null;
      node.mass = ${e("node.mass_{var} = ",{join:""})}0;
      ${e("node.min_{var} = node.max_{var} = ",{join:""})}0;
    } else {
      node = new QuadNode();
      nodesCache[currentInCache] = node;
    }

    ++currentInCache;
    return node;
  }

  function update(sourceBody) {
    var queue = updateQueue;
    var v;
    ${e("var d{var};",{indent:4})}
    var r; 
    ${e("var f{var} = 0;",{indent:4})}
    var queueLength = 1;
    var shiftIdx = 0;
    var pushIdx = 1;

    queue[0] = root;

    while (queueLength) {
      var node = queue[shiftIdx];
      var body = node.body;

      queueLength -= 1;
      shiftIdx += 1;
      var differentBody = (body !== sourceBody);
      if (body && differentBody) {
        // If the current node is a leaf node (and it is not source body),
        // calculate the force exerted by the current node on body, and add this
        // amount to body's net force.
        ${e("d{var} = body.pos.{var} - sourceBody.pos.{var};",{indent:8})}
        r = Math.sqrt(${e("d{var} * d{var}",{join:" + "})});

        if (r === 0) {
          // Poor man's protection against zero distance.
          ${e("d{var} = (random.nextDouble() - 0.5) / 50;",{indent:10})}
          r = Math.sqrt(${e("d{var} * d{var}",{join:" + "})});
        }

        // This is standard gravitation force calculation but we divide
        // by r^3 to save two operations when normalizing force vector.
        v = gravity * body.mass * sourceBody.mass / (r * r * r);
        ${e("f{var} += v * d{var};",{indent:8})}
      } else if (differentBody) {
        // Otherwise, calculate the ratio s / r,  where s is the width of the region
        // represented by the internal node, and r is the distance between the body
        // and the node's center-of-mass
        ${e("d{var} = node.mass_{var} / node.mass - sourceBody.pos.{var};",{indent:8})}
        r = Math.sqrt(${e("d{var} * d{var}",{join:" + "})});

        if (r === 0) {
          // Sorry about code duplication. I don't want to create many functions
          // right away. Just want to see performance first.
          ${e("d{var} = (random.nextDouble() - 0.5) / 50;",{indent:10})}
          r = Math.sqrt(${e("d{var} * d{var}",{join:" + "})});
        }
        // If s / r < θ, treat this internal node as a single body, and calculate the
        // force it exerts on sourceBody, and add this amount to sourceBody's net force.
        if ((node.max_${i(0)} - node.min_${i(0)}) / r < theta) {
          // in the if statement above we consider node's width only
          // because the region was made into square during tree creation.
          // Thus there is no difference between using width or height.
          v = gravity * node.mass * sourceBody.mass / (r * r * r);
          ${e("f{var} += v * d{var};",{indent:10})}
        } else {
          // Otherwise, run the procedure recursively on each of the current node's children.

          // I intentionally unfolded this loop, to save several CPU cycles.
${function(){let t=Array(11).join(" "),e=[];for(let r=0;r<n;++r)e.push(t+`if (node.quad${r}) {`),e.push(t+`  queue[pushIdx] = node.quad${r};`),e.push(t+"  queueLength += 1;"),e.push(t+"  pushIdx += 1;"),e.push(t+"}");return e.join("\n")}()}
        }
      }
    }

    ${e("sourceBody.force.{var} += f{var};",{indent:4})}
  }

  function insertBodies(bodies) {
    ${e("var {var}min = Number.MAX_VALUE;",{indent:4})}
    ${e("var {var}max = Number.MIN_VALUE;",{indent:4})}
    var i = bodies.length;

    // To reduce quad tree depth we are looking for exact bounding box of all particles.
    while (i--) {
      var pos = bodies[i].pos;
      ${e("if (pos.{var} < {var}min) {var}min = pos.{var};",{indent:6})}
      ${e("if (pos.{var} > {var}max) {var}max = pos.{var};",{indent:6})}
    }

    // Makes the bounds square.
    var maxSideLength = -Infinity;
    ${e("if ({var}max - {var}min > maxSideLength) maxSideLength = {var}max - {var}min ;",{indent:4})}

    currentInCache = 0;
    root = newNode();
    ${e("root.min_{var} = {var}min;",{indent:4})}
    ${e("root.max_{var} = {var}min + maxSideLength;",{indent:4})}

    i = bodies.length - 1;
    if (i >= 0) {
      root.body = bodies[i];
    }
    while (i--) {
      insert(bodies[i], root);
    }
  }

  function insert(newBody) {
    insertStack.reset();
    insertStack.push(root, newBody);

    while (!insertStack.isEmpty()) {
      var stackItem = insertStack.pop();
      var node = stackItem.node;
      var body = stackItem.body;

      if (!node.body) {
        // This is internal node. Update the total mass of the node and center-of-mass.
        ${e("var {var} = body.pos.{var};",{indent:8})}
        node.mass += body.mass;
        ${e("node.mass_{var} += body.mass * {var};",{indent:8})}

        // Recursively insert the body in the appropriate quadrant.
        // But first find the appropriate quadrant.
        var quadIdx = 0; // Assume we are in the 0's quad.
        ${e("var min_{var} = node.min_{var};",{indent:8})}
        ${e("var max_{var} = (min_{var} + node.max_{var}) / 2;",{indent:8})}

${function(e){let n=[],r=Array(9).join(" ");for(let e=0;e<t;++e)n.push(r+`if (${i(e)} > max_${i(e)}) {`),n.push(r+`  quadIdx = quadIdx + ${Math.pow(2,e)};`),n.push(r+`  min_${i(e)} = max_${i(e)};`),n.push(r+`  max_${i(e)} = node.max_${i(e)};`),n.push(r+"}");return n.join("\n")}(8)}

        var child = getChild(node, quadIdx);

        if (!child) {
          // The node is internal but this quadrant is not taken. Add
          // subnode to it.
          child = newNode();
          ${e("child.min_{var} = min_{var};",{indent:10})}
          ${e("child.max_{var} = max_{var};",{indent:10})}
          child.body = body;

          setChild(node, quadIdx, child);
        } else {
          // continue searching in this quadrant.
          insertStack.push(child, body);
        }
      } else {
        // We are trying to add to the leaf node.
        // We have to convert current leaf into internal node
        // and continue adding two nodes.
        var oldBody = node.body;
        node.body = null; // internal nodes do not cary bodies

        if (isSamePosition(oldBody.pos, body.pos)) {
          // Prevent infinite subdivision by bumping one node
          // anywhere in this quadrant
          var retriesCount = 3;
          do {
            var offset = random.nextDouble();
            ${e("var d{var} = (node.max_{var} - node.min_{var}) * offset;",{indent:12})}

            ${e("oldBody.pos.{var} = node.min_{var} + d{var};",{indent:12})}
            retriesCount -= 1;
            // Make sure we don't bump it out of the box. If we do, next iteration should fix it
          } while (retriesCount > 0 && isSamePosition(oldBody.pos, body.pos));

          if (retriesCount === 0 && isSamePosition(oldBody.pos, body.pos)) {
            // This is very bad, we ran out of precision.
            // if we do not return from the method we'll get into
            // infinite loop here. So we sacrifice correctness of layout, and keep the app running
            // Next layout iteration should get larger bounding box in the first step and fix this
            return;
          }
        }
        // Next iteration should subdivide node further.
        insertStack.push(node, oldBody);
        insertStack.push(node, body);
      }
    }
  }
}
return createQuadTree;

`}function a(t){let e=r(t);return`
  function isSamePosition(point1, point2) {
    ${e("var d{var} = Math.abs(point1.{var} - point2.{var});",{indent:2})}
  
    return ${e("d{var} < 1e-8",{join:" && "})};
  }  
`}function s(t){var e=Math.pow(2,t);return`
function setChild(node, idx, child) {
  ${function(){let t=[];for(let n=0;n<e;++n){let e=0===n?"  ":"  else ";t.push(`${e}if (idx === ${n}) node.quad${n} = child;`)}return t.join("\n")}()}
}`}function l(t){return`function getChild(node, idx) {
${function(){let e=[],n=Math.pow(2,t);for(let t=0;t<n;++t)e.push(`  if (idx === ${t}) return node.quad${t};`);return e.join("\n")}()}
  return null;
}`}function h(t){let e=r(t),n=Math.pow(2,t);return`
function QuadNode() {
  // body stored inside this node. In quad tree only leaf nodes (by construction)
  // contain bodies:
  this.body = null;

  // Child nodes are stored in quads. Each quad is presented by number:
  // 0 | 1
  // -----
  // 2 | 3
${function(t){let e=[];for(let r=0;r<n;++r)e.push(`${t}quad${r} = null;`);return e.join("\n")}("  this.")}

  // Total mass of current node
  this.mass = 0;

  // Center of mass coordinates
  ${e("this.mass_{var} = 0;",{indent:2})}

  // bounding box coordinates
  ${e("this.min_{var} = 0;",{indent:2})}
  ${e("this.max_{var} = 0;",{indent:2})}
}
`}function u(){return`
/**
 * Our implementation of QuadTree is non-recursive to avoid GC hit
 * This data structure represent stack of elements
 * which we are trying to insert into quad tree.
 */
function InsertStack () {
    this.stack = [];
    this.popIdx = 0;
}

InsertStack.prototype = {
    isEmpty: function() {
        return this.popIdx === 0;
    },
    push: function (node, body) {
        var item = this.stack[this.popIdx];
        if (!item) {
            // we are trying to avoid memory pressure: create new element
            // only when absolutely necessary
            this.stack[this.popIdx] = new InsertStackElement(node, body);
        } else {
            item.node = node;
            item.body = body;
        }
        ++this.popIdx;
    },
    pop: function () {
        if (this.popIdx > 0) {
            return this.stack[--this.popIdx];
        }
    },
    reset: function () {
        this.popIdx = 0;
    }
};

function InsertStackElement(node, body) {
    this.node = node; // QuadTree node
    this.body = body; // physical body which needs to be inserted to node
}
`}t.exports=function(t){return Function(o(t))()},t.exports.generateQuadTreeFunctionBody=o,t.exports.getInsertStackCode=u,t.exports.getQuadNodeCode=h,t.exports.isSamePosition=a,t.exports.getChildBodyCode=l,t.exports.setChildBodyCode=s},7895:(t,e,n)=>{t.exports=function(t){var e=n(2223),u=n(7411),c=n(6106);if(t){if(void 0!==t.springCoeff)throw Error("springCoeff was renamed to springCoefficient");if(void 0!==t.dragCoeff)throw Error("dragCoeff was renamed to dragCoefficient")}var d=h[(t=u(t,{springLength:10,springCoefficient:.8,gravity:-12,theta:.8,dragCoefficient:.9,timeStep:.5,adaptiveTimeStepWeight:0,dimensions:2,debug:!1})).dimensions];if(!d){var f=t.dimensions;d={Body:r(f,t.debug),createQuadTree:i(f),createBounds:o(f),createDragForce:a(f),createSpringForce:s(f),integrate:l(f)},h[f]=d}var p=d.Body,g=d.createQuadTree,_=d.createBounds,m=d.createDragForce,v=d.createSpringForce,y=d.integrate,b=t=>new p(t),w=n(9992).random(42),x=[],k=[],P=g(t,w),E=_(x,t,w),O=v(t,w),M=m(t),S=[],j=new Map,C=0;D("nbody",function(){if(0!==x.length){P.insertBodies(x);for(var t=x.length;t--;){var e=x[t];e.isPinned||(e.reset(),P.updateBodyForce(e),M.update(e))}}}),D("spring",function(){for(var t=k.length;t--;)O.update(k[t])});var T={bodies:x,quadTree:P,springs:k,settings:t,addForce:D,removeForce:function(t){var e=S.indexOf(j.get(t));e<0||(S.splice(e,1),j.delete(t))},getForces:function(){return j},step:function(){for(var e=0;e<S.length;++e)S[e](C);var n=y(x,t.timeStep,t.adaptiveTimeStepWeight);return C+=1,n},addBody:function(t){if(!t)throw Error("Body is required");return x.push(t),t},addBodyAt:function(t){if(!t)throw Error("Body position is required");var e=b(t);return x.push(e),e},removeBody:function(t){if(t){var e=x.indexOf(t);if(!(e<0))return x.splice(e,1),0===x.length&&E.reset(),!0}},addSpring:function(t,n,r,i){if(!t||!n)throw Error("Cannot add null spring to force simulator");"number"!=typeof r&&(r=-1);var o=new e(t,n,r,i>=0?i:-1);return k.push(o),o},getTotalMovement:function(){return 0},removeSpring:function(t){if(t){var e=k.indexOf(t);if(e>-1)return k.splice(e,1),!0}},getBestNewBodyPosition:function(t){return E.getBestNewPosition(t)},getBBox:A,getBoundingBox:A,invalidateBBox:function(){console.warn("invalidateBBox() is deprecated, bounds always recomputed on `getBBox()` call")},gravity:function(e){return void 0!==e?(t.gravity=e,P.options({gravity:e}),this):t.gravity},theta:function(e){return void 0!==e?(t.theta=e,P.options({theta:e}),this):t.theta},random:w};return function(t,e){for(var n in t)!function(t,e,n){t.hasOwnProperty(n)&&"function"!=typeof e[n]&&(Number.isFinite(t[n])?e[n]=function(r){if(void 0!==r){if(!Number.isFinite(r))throw Error("Value of "+n+" should be a valid number.");return t[n]=r,e}return t[n]}:e[n]=function(r){return void 0!==r?(t[n]=r,e):t[n]})}(t,e,n)}(t,T),c(T),T;function A(){return E.update(),E.box}function D(t,e){if(j.has(t))throw Error("Force "+t+" is already added");j.set(t,e),S.push(e)}};var r=n(9229),i=n(7550),o=n(5704),a=n(9514),s=n(3027),l=n(6568),h={}},9062:(t,e,n)=>{t.exports=function(t,e){if(!t)throw Error("Graph structure cannot be undefined");var o=(e&&e.createSimulator||n(7895))(e);if(Array.isArray(e))throw Error("Physics settings is expected to be an object");var a=t.version>19?function(e){var n=t.getLinks(e);return n?1+n.size/3:1}:function(e){var n=t.getLinks(e);return n?1+n.length/3:1};e&&"function"==typeof e.nodeMass&&(a=e.nodeMass);var s=new Map,l={},h=0,u=o.settings.springTransform||i;h=0,t.forEachNode(function(t){_(t.id),h+=1}),t.forEachLink(m),t.on("changed",g);var c=!1,d={step:function(){if(0===h)return f(!0),!0;var t=o.step();d.lastMove=t,d.fire("step");var e=t/h<=.01;return f(e),e},getNodePosition:function(t){return y(t).pos},setNodePosition:function(t){var e=y(t);e.setPosition.apply(e,Array.prototype.slice.call(arguments,1))},getLinkPosition:function(t){var e=l[t];if(e)return{from:e.from.pos,to:e.to.pos}},getGraphRect:function(){return o.getBBox()},forEachBody:p,pinNode:function(t,e){y(t.id).isPinned=!!e},isNodePinned:function(t){return y(t.id).isPinned},dispose:function(){t.off("changed",g),d.fire("disposed")},getBody:function(t){return s.get(t)},getSpring:function(e,n){var r;if(void 0===n)r="object"!=typeof e?e:e.id;else{var i=t.hasLink(e,n);if(!i)return;r=i.id}return l[r]},getForceVectorLength:function(){var t=0,e=0;return p(function(n){t+=Math.abs(n.force.x),e+=Math.abs(n.force.y)}),Math.sqrt(t*t+e*e)},simulator:o,graph:t,lastMove:0};return r(d),d;function f(t){var e;c!==t&&(c=t,e=t,d.fire("stable",e))}function p(t){s.forEach(t)}function g(e){for(var n=0;n<e.length;++n){var r=e[n];"add"===r.changeType?(r.node&&_(r.node.id),r.link&&m(r.link)):"remove"===r.changeType&&(r.node&&function(t){var e=t.id,n=s.get(e);n&&(s.delete(e),o.removeBody(n))}(r.node),r.link&&function(e){var n=l[e.id];if(n){var r=t.getNode(e.fromId),i=t.getNode(e.toId);r&&v(r.id),i&&v(i.id),delete l[e.id],o.removeSpring(n)}}(r.link))}h=t.getNodesCount()}function _(e){var n=s.get(e);if(!n){var r,i=t.getNode(e);if(!i)throw Error("initBody() was called with unknown node id");var a=i.position;if(!a){var l=function(t){var e=[];if(!t.links)return e;for(var n=Math.min(t.links.length,2),r=0;r<n;++r){var i=t.links[r],o=i.fromId!==t.id?s.get(i.fromId):s.get(i.toId);o&&o.pos&&e.push(o)}return e}(i);a=o.getBestNewBodyPosition(l)}(n=o.addBodyAt(a)).id=e,s.set(e,n),v(e),(r=i)&&(r.isPinned||r.data&&r.data.isPinned)&&(n.isPinned=!0)}}function m(t){v(t.fromId),v(t.toId);var e=s.get(t.fromId),n=s.get(t.toId),r=o.addSpring(e,n,t.length);u(t,r),l[t.id]=r}function v(t){var e=s.get(t);if(e.mass=a(t),Number.isNaN(e.mass))throw Error("Node mass should be a number")}function y(t){var e=s.get(t);return e||(_(t),e=s.get(t)),e}},t.exports.simulator=n(7895);var r=n(6106);function i(){}},9229:(t,e,n)=>{let r=n(455);function i(t,e){return`
${a(t,e)}
${o(t,e)}
return {Body: Body, Vector: Vector};
`}function o(t){let e=r(t),n=e("{var}",{join:", "});return`
function Body(${n}) {
  this.isPinned = false;
  this.pos = new Vector(${n});
  this.force = new Vector();
  this.velocity = new Vector();
  this.mass = 1;

  this.springCount = 0;
  this.springLength = 0;
}

Body.prototype.reset = function() {
  this.force.reset();
  this.springCount = 0;
  this.springLength = 0;
}

Body.prototype.setPosition = function (${n}) {
  ${e("this.pos.{var} = {var} || 0;",{indent:2})}
};`}function a(t,e){let n=r(t),i="";e&&(i=`${n("\n   var v{var};\nObject.defineProperty(this, '{var}', {\n  set: function(v) { \n    if (!Number.isFinite(v)) throw new Error('Cannot set non-numbers to {var}');\n    v{var} = v; \n  },\n  get: function() { return v{var}; }\n});")}`);let o=n("{var}",{join:", "});return`function Vector(${o}) {
  ${i}
    if (typeof arguments[0] === 'object') {
      // could be another vector
      let v = arguments[0];
      ${n('if (!Number.isFinite(v.{var})) throw new Error("Expected value is not a finite number at Vector constructor ({var})");',{indent:4})}
      ${n("this.{var} = v.{var};",{indent:4})}
    } else {
      ${n('this.{var} = typeof {var} === "number" ? {var} : 0;',{indent:4})}
    }
  }
  
  Vector.prototype.reset = function () {
    ${n("this.{var} = ",{join:""})}0;
  };`}t.exports=function(t,e){let{Body:n}=Function(i(t,e))();return n},t.exports.generateCreateBodyFunctionBody=i,t.exports.getVectorCode=a,t.exports.getBodyCode=o},9514:(t,e,n)=>{let r=n(455);function i(t){let e=r(t);return`
  if (!Number.isFinite(options.dragCoefficient)) throw new Error('dragCoefficient is not a finite number');

  return {
    update: function(body) {
      ${e("body.force.{var} -= options.dragCoefficient * body.velocity.{var};",{indent:6})}
    }
  };
`}t.exports=function(t){return Function("options",i(t))},t.exports.generateCreateDragForceFunctionBody=i},9992:t=>{function e(t){return new n("number"==typeof t?t:+new Date)}function n(t){this.seed=t}function r(t){return Math.sqrt(2*Math.PI/t)*Math.pow(1/Math.E*(t+1/(12*t-1/(10*t))),t)}function i(){var t=this.seed;return t=0xb55a4f09^(t=(t=(t=(t=0xc761c23c^(t=t+0x7ed55d16+(t<<12)|0)^t>>>19)+0x165667b1+(t<<5)|0)+0xd3a2646c^t<<9)+0xfd7046c5+(t<<3)|0)^t>>>16,this.seed=t,(0xfffffff&t)/0x10000000}t.exports=e,t.exports.random=e,t.exports.randomIterator=function(t,n){var r=n||e();if("function"!=typeof r.next)throw Error("customRandom does not match expected API: next() function is missing");return{forEach:function(e){var n,i,o;for(n=t.length-1;n>0;--n)o=t[i=r.next(n+1)],t[i]=t[n],t[n]=o,e(o);t.length&&e(t[0])},shuffle:function(){var e,n,i;for(e=t.length-1;e>0;--e)i=t[n=r.next(e+1)],t[n]=t[e],t[e]=i;return t}}},n.prototype.next=function(t){return Math.floor(this.nextDouble()*t)},n.prototype.nextDouble=i,n.prototype.uniform=i,n.prototype.gaussian=function(){var t,e,n;do t=(e=2*this.nextDouble()-1)*e+(n=2*this.nextDouble()-1)*n;while(t>=1||0===t);return e*Math.sqrt(-2*Math.log(t)/t)},n.prototype.random=i,n.prototype.levy=function(){var t=Math.pow(r(2.5)*Math.sin(1.5*Math.PI/2)/(1.5*r(1.25)*1.189207115002721),.6666666666666666);return this.gaussian()*t/Math.pow(Math.abs(this.gaussian()),.6666666666666666)}}}]);