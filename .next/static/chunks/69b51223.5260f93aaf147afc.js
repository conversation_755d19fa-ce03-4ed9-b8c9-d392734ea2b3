"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[391],{5351:(e,t,n)=>{let i;n.d(t,{CV9:()=>rN,G_z:()=>a_,Gu$:()=>au,Ho_:()=>rV,LoY:()=>ty,N1A:()=>rm,NRn:()=>K,Pq0:()=>Y,Q1f:()=>tn,THS:()=>th,YJl:()=>re,eaF:()=>tz,j6:()=>am,kn4:()=>eT,mrM:()=>rh,qFE:()=>rG,s0K:()=>rC});let r="srgb",a="srgb-linear",s="display-p3",o="display-p3-linear",l="linear",h="srgb",c="rec709",u="300 es";class d{addEventListener(e,t){void 0===this._listeners&&(this._listeners={});let n=this._listeners;void 0===n[e]&&(n[e]=[]),-1===n[e].indexOf(t)&&n[e].push(t)}hasEventListener(e,t){if(void 0===this._listeners)return!1;let n=this._listeners;return void 0!==n[e]&&-1!==n[e].indexOf(t)}removeEventListener(e,t){if(void 0===this._listeners)return;let n=this._listeners[e];if(void 0!==n){let e=n.indexOf(t);-1!==e&&n.splice(e,1)}}dispatchEvent(e){if(void 0===this._listeners)return;let t=this._listeners[e.type];if(void 0!==t){e.target=this;let n=t.slice(0);for(let t=0,i=n.length;t<i;t++)n[t].call(this,e);e.target=null}}}let p=["00","01","02","03","04","05","06","07","08","09","0a","0b","0c","0d","0e","0f","10","11","12","13","14","15","16","17","18","19","1a","1b","1c","1d","1e","1f","20","21","22","23","24","25","26","27","28","29","2a","2b","2c","2d","2e","2f","30","31","32","33","34","35","36","37","38","39","3a","3b","3c","3d","3e","3f","40","41","42","43","44","45","46","47","48","49","4a","4b","4c","4d","4e","4f","50","51","52","53","54","55","56","57","58","59","5a","5b","5c","5d","5e","5f","60","61","62","63","64","65","66","67","68","69","6a","6b","6c","6d","6e","6f","70","71","72","73","74","75","76","77","78","79","7a","7b","7c","7d","7e","7f","80","81","82","83","84","85","86","87","88","89","8a","8b","8c","8d","8e","8f","90","91","92","93","94","95","96","97","98","99","9a","9b","9c","9d","9e","9f","a0","a1","a2","a3","a4","a5","a6","a7","a8","a9","aa","ab","ac","ad","ae","af","b0","b1","b2","b3","b4","b5","b6","b7","b8","b9","ba","bb","bc","bd","be","bf","c0","c1","c2","c3","c4","c5","c6","c7","c8","c9","ca","cb","cc","cd","ce","cf","d0","d1","d2","d3","d4","d5","d6","d7","d8","d9","da","db","dc","dd","de","df","e0","e1","e2","e3","e4","e5","e6","e7","e8","e9","ea","eb","ec","ed","ee","ef","f0","f1","f2","f3","f4","f5","f6","f7","f8","f9","fa","fb","fc","fd","fe","ff"],f=Math.PI/180,m=180/Math.PI;function g(){let e=0xffffffff*Math.random()|0,t=0xffffffff*Math.random()|0,n=0xffffffff*Math.random()|0,i=0xffffffff*Math.random()|0;return(p[255&e]+p[e>>8&255]+p[e>>16&255]+p[e>>24&255]+"-"+p[255&t]+p[t>>8&255]+"-"+p[t>>16&15|64]+p[t>>24&255]+"-"+p[63&n|128]+p[n>>8&255]+"-"+p[n>>16&255]+p[n>>24&255]+p[255&i]+p[i>>8&255]+p[i>>16&255]+p[i>>24&255]).toLowerCase()}function _(e,t,n){return Math.max(t,Math.min(n,e))}function v(e){return(e&e-1)==0&&0!==e}function x(e){return Math.pow(2,Math.floor(Math.log(e)/Math.LN2))}function y(e,t){switch(t.constructor){case Float32Array:return e;case Uint32Array:return e/0xffffffff;case Uint16Array:return e/65535;case Uint8Array:return e/255;case Int32Array:return Math.max(e/0x7fffffff,-1);case Int16Array:return Math.max(e/32767,-1);case Int8Array:return Math.max(e/127,-1);default:throw Error("Invalid component type.")}}function M(e,t){switch(t.constructor){case Float32Array:return e;case Uint32Array:return Math.round(0xffffffff*e);case Uint16Array:return Math.round(65535*e);case Uint8Array:return Math.round(255*e);case Int32Array:return Math.round(0x7fffffff*e);case Int16Array:return Math.round(32767*e);case Int8Array:return Math.round(127*e);default:throw Error("Invalid component type.")}}class S{get width(){return this.x}set width(e){this.x=e}get height(){return this.y}set height(e){this.y=e}set(e,t){return this.x=e,this.y=t,this}setScalar(e){return this.x=e,this.y=e,this}setX(e){return this.x=e,this}setY(e){return this.y=e,this}setComponent(e,t){switch(e){case 0:this.x=t;break;case 1:this.y=t;break;default:throw Error("index is out of range: "+e)}return this}getComponent(e){switch(e){case 0:return this.x;case 1:return this.y;default:throw Error("index is out of range: "+e)}}clone(){return new this.constructor(this.x,this.y)}copy(e){return this.x=e.x,this.y=e.y,this}add(e){return this.x+=e.x,this.y+=e.y,this}addScalar(e){return this.x+=e,this.y+=e,this}addVectors(e,t){return this.x=e.x+t.x,this.y=e.y+t.y,this}addScaledVector(e,t){return this.x+=e.x*t,this.y+=e.y*t,this}sub(e){return this.x-=e.x,this.y-=e.y,this}subScalar(e){return this.x-=e,this.y-=e,this}subVectors(e,t){return this.x=e.x-t.x,this.y=e.y-t.y,this}multiply(e){return this.x*=e.x,this.y*=e.y,this}multiplyScalar(e){return this.x*=e,this.y*=e,this}divide(e){return this.x/=e.x,this.y/=e.y,this}divideScalar(e){return this.multiplyScalar(1/e)}applyMatrix3(e){let t=this.x,n=this.y,i=e.elements;return this.x=i[0]*t+i[3]*n+i[6],this.y=i[1]*t+i[4]*n+i[7],this}min(e){return this.x=Math.min(this.x,e.x),this.y=Math.min(this.y,e.y),this}max(e){return this.x=Math.max(this.x,e.x),this.y=Math.max(this.y,e.y),this}clamp(e,t){return this.x=Math.max(e.x,Math.min(t.x,this.x)),this.y=Math.max(e.y,Math.min(t.y,this.y)),this}clampScalar(e,t){return this.x=Math.max(e,Math.min(t,this.x)),this.y=Math.max(e,Math.min(t,this.y)),this}clampLength(e,t){let n=this.length();return this.divideScalar(n||1).multiplyScalar(Math.max(e,Math.min(t,n)))}floor(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this}ceil(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this}round(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this}roundToZero(){return this.x=Math.trunc(this.x),this.y=Math.trunc(this.y),this}negate(){return this.x=-this.x,this.y=-this.y,this}dot(e){return this.x*e.x+this.y*e.y}cross(e){return this.x*e.y-this.y*e.x}lengthSq(){return this.x*this.x+this.y*this.y}length(){return Math.sqrt(this.x*this.x+this.y*this.y)}manhattanLength(){return Math.abs(this.x)+Math.abs(this.y)}normalize(){return this.divideScalar(this.length()||1)}angle(){return Math.atan2(-this.y,-this.x)+Math.PI}angleTo(e){let t=Math.sqrt(this.lengthSq()*e.lengthSq());return 0===t?Math.PI/2:Math.acos(_(this.dot(e)/t,-1,1))}distanceTo(e){return Math.sqrt(this.distanceToSquared(e))}distanceToSquared(e){let t=this.x-e.x,n=this.y-e.y;return t*t+n*n}manhattanDistanceTo(e){return Math.abs(this.x-e.x)+Math.abs(this.y-e.y)}setLength(e){return this.normalize().multiplyScalar(e)}lerp(e,t){return this.x+=(e.x-this.x)*t,this.y+=(e.y-this.y)*t,this}lerpVectors(e,t,n){return this.x=e.x+(t.x-e.x)*n,this.y=e.y+(t.y-e.y)*n,this}equals(e){return e.x===this.x&&e.y===this.y}fromArray(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return this.x=e[t],this.y=e[t+1],this}toArray(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return e[t]=this.x,e[t+1]=this.y,e}fromBufferAttribute(e,t){return this.x=e.getX(t),this.y=e.getY(t),this}rotateAround(e,t){let n=Math.cos(t),i=Math.sin(t),r=this.x-e.x,a=this.y-e.y;return this.x=r*n-a*i+e.x,this.y=r*i+a*n+e.y,this}random(){return this.x=Math.random(),this.y=Math.random(),this}*[Symbol.iterator](){yield this.x,yield this.y}constructor(e=0,t=0){S.prototype.isVector2=!0,this.x=e,this.y=t}}class E{set(e,t,n,i,r,a,s,o,l){let h=this.elements;return h[0]=e,h[1]=i,h[2]=s,h[3]=t,h[4]=r,h[5]=o,h[6]=n,h[7]=a,h[8]=l,this}identity(){return this.set(1,0,0,0,1,0,0,0,1),this}copy(e){let t=this.elements,n=e.elements;return t[0]=n[0],t[1]=n[1],t[2]=n[2],t[3]=n[3],t[4]=n[4],t[5]=n[5],t[6]=n[6],t[7]=n[7],t[8]=n[8],this}extractBasis(e,t,n){return e.setFromMatrix3Column(this,0),t.setFromMatrix3Column(this,1),n.setFromMatrix3Column(this,2),this}setFromMatrix4(e){let t=e.elements;return this.set(t[0],t[4],t[8],t[1],t[5],t[9],t[2],t[6],t[10]),this}multiply(e){return this.multiplyMatrices(this,e)}premultiply(e){return this.multiplyMatrices(e,this)}multiplyMatrices(e,t){let n=e.elements,i=t.elements,r=this.elements,a=n[0],s=n[3],o=n[6],l=n[1],h=n[4],c=n[7],u=n[2],d=n[5],p=n[8],f=i[0],m=i[3],g=i[6],_=i[1],v=i[4],x=i[7],y=i[2],M=i[5],S=i[8];return r[0]=a*f+s*_+o*y,r[3]=a*m+s*v+o*M,r[6]=a*g+s*x+o*S,r[1]=l*f+h*_+c*y,r[4]=l*m+h*v+c*M,r[7]=l*g+h*x+c*S,r[2]=u*f+d*_+p*y,r[5]=u*m+d*v+p*M,r[8]=u*g+d*x+p*S,this}multiplyScalar(e){let t=this.elements;return t[0]*=e,t[3]*=e,t[6]*=e,t[1]*=e,t[4]*=e,t[7]*=e,t[2]*=e,t[5]*=e,t[8]*=e,this}determinant(){let e=this.elements,t=e[0],n=e[1],i=e[2],r=e[3],a=e[4],s=e[5],o=e[6],l=e[7],h=e[8];return t*a*h-t*s*l-n*r*h+n*s*o+i*r*l-i*a*o}invert(){let e=this.elements,t=e[0],n=e[1],i=e[2],r=e[3],a=e[4],s=e[5],o=e[6],l=e[7],h=e[8],c=h*a-s*l,u=s*o-h*r,d=l*r-a*o,p=t*c+n*u+i*d;if(0===p)return this.set(0,0,0,0,0,0,0,0,0);let f=1/p;return e[0]=c*f,e[1]=(i*l-h*n)*f,e[2]=(s*n-i*a)*f,e[3]=u*f,e[4]=(h*t-i*o)*f,e[5]=(i*r-s*t)*f,e[6]=d*f,e[7]=(n*o-l*t)*f,e[8]=(a*t-n*r)*f,this}transpose(){let e,t=this.elements;return e=t[1],t[1]=t[3],t[3]=e,e=t[2],t[2]=t[6],t[6]=e,e=t[5],t[5]=t[7],t[7]=e,this}getNormalMatrix(e){return this.setFromMatrix4(e).invert().transpose()}transposeIntoArray(e){let t=this.elements;return e[0]=t[0],e[1]=t[3],e[2]=t[6],e[3]=t[1],e[4]=t[4],e[5]=t[7],e[6]=t[2],e[7]=t[5],e[8]=t[8],this}setUvTransform(e,t,n,i,r,a,s){let o=Math.cos(r),l=Math.sin(r);return this.set(n*o,n*l,-n*(o*a+l*s)+a+e,-i*l,i*o,-i*(-l*a+o*s)+s+t,0,0,1),this}scale(e,t){return this.premultiply(T.makeScale(e,t)),this}rotate(e){return this.premultiply(T.makeRotation(-e)),this}translate(e,t){return this.premultiply(T.makeTranslation(e,t)),this}makeTranslation(e,t){return e.isVector2?this.set(1,0,e.x,0,1,e.y,0,0,1):this.set(1,0,e,0,1,t,0,0,1),this}makeRotation(e){let t=Math.cos(e),n=Math.sin(e);return this.set(t,-n,0,n,t,0,0,0,1),this}makeScale(e,t){return this.set(e,0,0,0,t,0,0,0,1),this}equals(e){let t=this.elements,n=e.elements;for(let e=0;e<9;e++)if(t[e]!==n[e])return!1;return!0}fromArray(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;for(let n=0;n<9;n++)this.elements[n]=e[n+t];return this}toArray(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=this.elements;return e[t]=n[0],e[t+1]=n[1],e[t+2]=n[2],e[t+3]=n[3],e[t+4]=n[4],e[t+5]=n[5],e[t+6]=n[6],e[t+7]=n[7],e[t+8]=n[8],e}clone(){return new this.constructor().fromArray(this.elements)}constructor(e,t,n,i,r,a,s,o,l){E.prototype.isMatrix3=!0,this.elements=[1,0,0,0,1,0,0,0,1],void 0!==e&&this.set(e,t,n,i,r,a,s,o,l)}}let T=new E;function b(e){for(let t=e.length-1;t>=0;--t)if(e[t]>=65535)return!0;return!1}function A(e){return document.createElementNS("http://www.w3.org/1999/xhtml",e)}Int8Array,Uint8Array,Uint8ClampedArray,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array;let w={};function R(e){e in w||(w[e]=!0,console.warn(e))}let C=new E().set(.8224621,.177538,0,.0331941,.9668058,0,.0170827,.0723974,.9105199),L=new E().set(1.2249401,-.2249404,0,-.0420569,1.0420571,0,-.0196376,-.0786361,1.0982735),P={[a]:{transfer:l,primaries:c,toReference:e=>e,fromReference:e=>e},[r]:{transfer:h,primaries:c,toReference:e=>e.convertSRGBToLinear(),fromReference:e=>e.convertLinearToSRGB()},[o]:{transfer:l,primaries:"p3",toReference:e=>e.applyMatrix3(L),fromReference:e=>e.applyMatrix3(C)},[s]:{transfer:h,primaries:"p3",toReference:e=>e.convertSRGBToLinear().applyMatrix3(L),fromReference:e=>e.applyMatrix3(C).convertLinearToSRGB()}},U=new Set([a,o]),N={enabled:!0,_workingColorSpace:a,get legacyMode(){return console.warn("THREE.ColorManagement: .legacyMode=false renamed to .enabled=true in r150."),!this.enabled},set legacyMode(legacyMode){console.warn("THREE.ColorManagement: .legacyMode=false renamed to .enabled=true in r150."),this.enabled=!legacyMode},get workingColorSpace(){return this._workingColorSpace},set workingColorSpace(colorSpace){if(!U.has(colorSpace))throw Error('Unsupported working color space, "'.concat(colorSpace,'".'));this._workingColorSpace=colorSpace},convert:function(e,t,n){if(!1===this.enabled||t===n||!t||!n)return e;let i=P[t].toReference;return(0,P[n].fromReference)(i(e))},fromWorkingColorSpace:function(e,t){return this.convert(e,this._workingColorSpace,t)},toWorkingColorSpace:function(e,t){return this.convert(e,t,this._workingColorSpace)},getPrimaries:function(e){return P[e].primaries},getTransfer:function(e){return""===e?l:P[e].transfer}};function D(e){return e<.04045?.0773993808*e:Math.pow(.9478672986*e+.0521327014,2.4)}function I(e){return e<.0031308?12.92*e:1.055*Math.pow(e,.41666)-.055}class O{static getDataURL(e){let t;if(/^data:/i.test(e.src)||"undefined"==typeof HTMLCanvasElement)return e.src;if(e instanceof HTMLCanvasElement)t=e;else{void 0===i&&(i=A("canvas")),i.width=e.width,i.height=e.height;let n=i.getContext("2d");e instanceof ImageData?n.putImageData(e,0,0):n.drawImage(e,0,0,e.width,e.height),t=i}return t.width>2048||t.height>2048?(console.warn("THREE.ImageUtils.getDataURL: Image converted to jpg for performance reasons",e),t.toDataURL("image/jpeg",.6)):t.toDataURL("image/png")}static sRGBToLinear(e){if("undefined"!=typeof HTMLImageElement&&e instanceof HTMLImageElement||"undefined"!=typeof HTMLCanvasElement&&e instanceof HTMLCanvasElement||"undefined"!=typeof ImageBitmap&&e instanceof ImageBitmap){let t=A("canvas");t.width=e.width,t.height=e.height;let n=t.getContext("2d");n.drawImage(e,0,0,e.width,e.height);let i=n.getImageData(0,0,e.width,e.height),r=i.data;for(let e=0;e<r.length;e++)r[e]=255*D(r[e]/255);return n.putImageData(i,0,0),t}if(!e.data)return console.warn("THREE.ImageUtils.sRGBToLinear(): Unsupported image type. No color space conversion applied."),e;{let t=e.data.slice(0);for(let e=0;e<t.length;e++)t instanceof Uint8Array||t instanceof Uint8ClampedArray?t[e]=Math.floor(255*D(t[e]/255)):t[e]=D(t[e]);return{data:t,width:e.width,height:e.height}}}}let F=0;class z{set needsUpdate(e){!0===e&&this.version++}toJSON(e){let t=void 0===e||"string"==typeof e;if(!t&&void 0!==e.images[this.uuid])return e.images[this.uuid];let n={uuid:this.uuid,url:""},i=this.data;if(null!==i){let e;if(Array.isArray(i)){e=[];for(let t=0,n=i.length;t<n;t++)i[t].isDataTexture?e.push(B(i[t].image)):e.push(B(i[t]))}else e=B(i);n.url=e}return t||(e.images[this.uuid]=n),n}constructor(e=null){this.isSource=!0,Object.defineProperty(this,"id",{value:F++}),this.uuid=g(),this.data=e,this.version=0}}function B(e){return"undefined"!=typeof HTMLImageElement&&e instanceof HTMLImageElement||"undefined"!=typeof HTMLCanvasElement&&e instanceof HTMLCanvasElement||"undefined"!=typeof ImageBitmap&&e instanceof ImageBitmap?O.getDataURL(e):e.data?{data:Array.from(e.data),width:e.width,height:e.height,type:e.data.constructor.name}:(console.warn("THREE.Texture: Unable to serialize Texture."),{})}let H=0;class V extends d{get image(){return this.source.data}set image(e){void 0===e&&(e=null),this.source.data=e}updateMatrix(){this.matrix.setUvTransform(this.offset.x,this.offset.y,this.repeat.x,this.repeat.y,this.rotation,this.center.x,this.center.y)}clone(){return new this.constructor().copy(this)}copy(e){return this.name=e.name,this.source=e.source,this.mipmaps=e.mipmaps.slice(0),this.mapping=e.mapping,this.channel=e.channel,this.wrapS=e.wrapS,this.wrapT=e.wrapT,this.magFilter=e.magFilter,this.minFilter=e.minFilter,this.anisotropy=e.anisotropy,this.format=e.format,this.internalFormat=e.internalFormat,this.type=e.type,this.offset.copy(e.offset),this.repeat.copy(e.repeat),this.center.copy(e.center),this.rotation=e.rotation,this.matrixAutoUpdate=e.matrixAutoUpdate,this.matrix.copy(e.matrix),this.generateMipmaps=e.generateMipmaps,this.premultiplyAlpha=e.premultiplyAlpha,this.flipY=e.flipY,this.unpackAlignment=e.unpackAlignment,this.colorSpace=e.colorSpace,this.userData=JSON.parse(JSON.stringify(e.userData)),this.needsUpdate=!0,this}toJSON(e){let t=void 0===e||"string"==typeof e;if(!t&&void 0!==e.textures[this.uuid])return e.textures[this.uuid];let n={metadata:{version:4.6,type:"Texture",generator:"Texture.toJSON"},uuid:this.uuid,name:this.name,image:this.source.toJSON(e).uuid,mapping:this.mapping,channel:this.channel,repeat:[this.repeat.x,this.repeat.y],offset:[this.offset.x,this.offset.y],center:[this.center.x,this.center.y],rotation:this.rotation,wrap:[this.wrapS,this.wrapT],format:this.format,internalFormat:this.internalFormat,type:this.type,colorSpace:this.colorSpace,minFilter:this.minFilter,magFilter:this.magFilter,anisotropy:this.anisotropy,flipY:this.flipY,generateMipmaps:this.generateMipmaps,premultiplyAlpha:this.premultiplyAlpha,unpackAlignment:this.unpackAlignment};return Object.keys(this.userData).length>0&&(n.userData=this.userData),t||(e.textures[this.uuid]=n),n}dispose(){this.dispatchEvent({type:"dispose"})}transformUv(e){if(300!==this.mapping)return e;if(e.applyMatrix3(this.matrix),e.x<0||e.x>1)switch(this.wrapS){case 1e3:e.x=e.x-Math.floor(e.x);break;case 1001:e.x=e.x<0?0:1;break;case 1002:1===Math.abs(Math.floor(e.x)%2)?e.x=Math.ceil(e.x)-e.x:e.x=e.x-Math.floor(e.x)}if(e.y<0||e.y>1)switch(this.wrapT){case 1e3:e.y=e.y-Math.floor(e.y);break;case 1001:e.y=e.y<0?0:1;break;case 1002:1===Math.abs(Math.floor(e.y)%2)?e.y=Math.ceil(e.y)-e.y:e.y=e.y-Math.floor(e.y)}return this.flipY&&(e.y=1-e.y),e}set needsUpdate(e){!0===e&&(this.version++,this.source.needsUpdate=!0)}get encoding(){return R("THREE.Texture: Property .encoding has been replaced by .colorSpace."),this.colorSpace===r?3001:3e3}set encoding(e){R("THREE.Texture: Property .encoding has been replaced by .colorSpace."),this.colorSpace=3001===e?r:""}constructor(e=V.DEFAULT_IMAGE,t=V.DEFAULT_MAPPING,n=1001,i=1001,a=1006,s=1008,o=1023,l=1009,h=V.DEFAULT_ANISOTROPY,c=""){super(),this.isTexture=!0,Object.defineProperty(this,"id",{value:H++}),this.uuid=g(),this.name="",this.source=new z(e),this.mipmaps=[],this.mapping=t,this.channel=0,this.wrapS=n,this.wrapT=i,this.magFilter=a,this.minFilter=s,this.anisotropy=h,this.format=o,this.internalFormat=null,this.type=l,this.offset=new S(0,0),this.repeat=new S(1,1),this.center=new S(0,0),this.rotation=0,this.matrixAutoUpdate=!0,this.matrix=new E,this.generateMipmaps=!0,this.premultiplyAlpha=!1,this.flipY=!0,this.unpackAlignment=4,"string"==typeof c?this.colorSpace=c:(R("THREE.Texture: Property .encoding has been replaced by .colorSpace."),this.colorSpace=3001===c?r:""),this.userData={},this.version=0,this.onUpdate=null,this.isRenderTargetTexture=!1,this.needsPMREMUpdate=!1}}V.DEFAULT_IMAGE=null,V.DEFAULT_MAPPING=300,V.DEFAULT_ANISOTROPY=1;class G{get width(){return this.z}set width(e){this.z=e}get height(){return this.w}set height(e){this.w=e}set(e,t,n,i){return this.x=e,this.y=t,this.z=n,this.w=i,this}setScalar(e){return this.x=e,this.y=e,this.z=e,this.w=e,this}setX(e){return this.x=e,this}setY(e){return this.y=e,this}setZ(e){return this.z=e,this}setW(e){return this.w=e,this}setComponent(e,t){switch(e){case 0:this.x=t;break;case 1:this.y=t;break;case 2:this.z=t;break;case 3:this.w=t;break;default:throw Error("index is out of range: "+e)}return this}getComponent(e){switch(e){case 0:return this.x;case 1:return this.y;case 2:return this.z;case 3:return this.w;default:throw Error("index is out of range: "+e)}}clone(){return new this.constructor(this.x,this.y,this.z,this.w)}copy(e){return this.x=e.x,this.y=e.y,this.z=e.z,this.w=void 0!==e.w?e.w:1,this}add(e){return this.x+=e.x,this.y+=e.y,this.z+=e.z,this.w+=e.w,this}addScalar(e){return this.x+=e,this.y+=e,this.z+=e,this.w+=e,this}addVectors(e,t){return this.x=e.x+t.x,this.y=e.y+t.y,this.z=e.z+t.z,this.w=e.w+t.w,this}addScaledVector(e,t){return this.x+=e.x*t,this.y+=e.y*t,this.z+=e.z*t,this.w+=e.w*t,this}sub(e){return this.x-=e.x,this.y-=e.y,this.z-=e.z,this.w-=e.w,this}subScalar(e){return this.x-=e,this.y-=e,this.z-=e,this.w-=e,this}subVectors(e,t){return this.x=e.x-t.x,this.y=e.y-t.y,this.z=e.z-t.z,this.w=e.w-t.w,this}multiply(e){return this.x*=e.x,this.y*=e.y,this.z*=e.z,this.w*=e.w,this}multiplyScalar(e){return this.x*=e,this.y*=e,this.z*=e,this.w*=e,this}applyMatrix4(e){let t=this.x,n=this.y,i=this.z,r=this.w,a=e.elements;return this.x=a[0]*t+a[4]*n+a[8]*i+a[12]*r,this.y=a[1]*t+a[5]*n+a[9]*i+a[13]*r,this.z=a[2]*t+a[6]*n+a[10]*i+a[14]*r,this.w=a[3]*t+a[7]*n+a[11]*i+a[15]*r,this}divideScalar(e){return this.multiplyScalar(1/e)}setAxisAngleFromQuaternion(e){this.w=2*Math.acos(e.w);let t=Math.sqrt(1-e.w*e.w);return t<1e-4?(this.x=1,this.y=0,this.z=0):(this.x=e.x/t,this.y=e.y/t,this.z=e.z/t),this}setAxisAngleFromRotationMatrix(e){let t,n,i,r,a=e.elements,s=a[0],o=a[4],l=a[8],h=a[1],c=a[5],u=a[9],d=a[2],p=a[6],f=a[10];if(.01>Math.abs(o-h)&&.01>Math.abs(l-d)&&.01>Math.abs(u-p)){if(.1>Math.abs(o+h)&&.1>Math.abs(l+d)&&.1>Math.abs(u+p)&&.1>Math.abs(s+c+f-3))return this.set(1,0,0,0),this;t=Math.PI;let e=(s+1)/2,a=(c+1)/2,m=(f+1)/2,g=(o+h)/4,_=(l+d)/4,v=(u+p)/4;return e>a&&e>m?e<.01?(n=0,i=.*********,r=.*********):(i=g/(n=Math.sqrt(e)),r=_/n):a>m?a<.01?(n=.*********,i=0,r=.*********):(n=g/(i=Math.sqrt(a)),r=v/i):m<.01?(n=.*********,i=.*********,r=0):(n=_/(r=Math.sqrt(m)),i=v/r),this.set(n,i,r,t),this}let m=Math.sqrt((p-u)*(p-u)+(l-d)*(l-d)+(h-o)*(h-o));return .001>Math.abs(m)&&(m=1),this.x=(p-u)/m,this.y=(l-d)/m,this.z=(h-o)/m,this.w=Math.acos((s+c+f-1)/2),this}min(e){return this.x=Math.min(this.x,e.x),this.y=Math.min(this.y,e.y),this.z=Math.min(this.z,e.z),this.w=Math.min(this.w,e.w),this}max(e){return this.x=Math.max(this.x,e.x),this.y=Math.max(this.y,e.y),this.z=Math.max(this.z,e.z),this.w=Math.max(this.w,e.w),this}clamp(e,t){return this.x=Math.max(e.x,Math.min(t.x,this.x)),this.y=Math.max(e.y,Math.min(t.y,this.y)),this.z=Math.max(e.z,Math.min(t.z,this.z)),this.w=Math.max(e.w,Math.min(t.w,this.w)),this}clampScalar(e,t){return this.x=Math.max(e,Math.min(t,this.x)),this.y=Math.max(e,Math.min(t,this.y)),this.z=Math.max(e,Math.min(t,this.z)),this.w=Math.max(e,Math.min(t,this.w)),this}clampLength(e,t){let n=this.length();return this.divideScalar(n||1).multiplyScalar(Math.max(e,Math.min(t,n)))}floor(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this.z=Math.floor(this.z),this.w=Math.floor(this.w),this}ceil(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this.z=Math.ceil(this.z),this.w=Math.ceil(this.w),this}round(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this.z=Math.round(this.z),this.w=Math.round(this.w),this}roundToZero(){return this.x=Math.trunc(this.x),this.y=Math.trunc(this.y),this.z=Math.trunc(this.z),this.w=Math.trunc(this.w),this}negate(){return this.x=-this.x,this.y=-this.y,this.z=-this.z,this.w=-this.w,this}dot(e){return this.x*e.x+this.y*e.y+this.z*e.z+this.w*e.w}lengthSq(){return this.x*this.x+this.y*this.y+this.z*this.z+this.w*this.w}length(){return Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z+this.w*this.w)}manhattanLength(){return Math.abs(this.x)+Math.abs(this.y)+Math.abs(this.z)+Math.abs(this.w)}normalize(){return this.divideScalar(this.length()||1)}setLength(e){return this.normalize().multiplyScalar(e)}lerp(e,t){return this.x+=(e.x-this.x)*t,this.y+=(e.y-this.y)*t,this.z+=(e.z-this.z)*t,this.w+=(e.w-this.w)*t,this}lerpVectors(e,t,n){return this.x=e.x+(t.x-e.x)*n,this.y=e.y+(t.y-e.y)*n,this.z=e.z+(t.z-e.z)*n,this.w=e.w+(t.w-e.w)*n,this}equals(e){return e.x===this.x&&e.y===this.y&&e.z===this.z&&e.w===this.w}fromArray(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return this.x=e[t],this.y=e[t+1],this.z=e[t+2],this.w=e[t+3],this}toArray(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return e[t]=this.x,e[t+1]=this.y,e[t+2]=this.z,e[t+3]=this.w,e}fromBufferAttribute(e,t){return this.x=e.getX(t),this.y=e.getY(t),this.z=e.getZ(t),this.w=e.getW(t),this}random(){return this.x=Math.random(),this.y=Math.random(),this.z=Math.random(),this.w=Math.random(),this}*[Symbol.iterator](){yield this.x,yield this.y,yield this.z,yield this.w}constructor(e=0,t=0,n=0,i=1){G.prototype.isVector4=!0,this.x=e,this.y=t,this.z=n,this.w=i}}class k extends d{setSize(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;(this.width!==e||this.height!==t||this.depth!==n)&&(this.width=e,this.height=t,this.depth=n,this.texture.image.width=e,this.texture.image.height=t,this.texture.image.depth=n,this.dispose()),this.viewport.set(0,0,e,t),this.scissor.set(0,0,e,t)}clone(){return new this.constructor().copy(this)}copy(e){this.width=e.width,this.height=e.height,this.depth=e.depth,this.scissor.copy(e.scissor),this.scissorTest=e.scissorTest,this.viewport.copy(e.viewport),this.texture=e.texture.clone(),this.texture.isRenderTargetTexture=!0;let t=Object.assign({},e.texture.image);return this.texture.source=new z(t),this.depthBuffer=e.depthBuffer,this.stencilBuffer=e.stencilBuffer,null!==e.depthTexture&&(this.depthTexture=e.depthTexture.clone()),this.samples=e.samples,this}dispose(){this.dispatchEvent({type:"dispose"})}constructor(e=1,t=1,n={}){super(),this.isRenderTarget=!0,this.width=e,this.height=t,this.depth=1,this.scissor=new G(0,0,e,t),this.scissorTest=!1,this.viewport=new G(0,0,e,t),void 0!==n.encoding&&(R("THREE.WebGLRenderTarget: option.encoding has been replaced by option.colorSpace."),n.colorSpace=3001===n.encoding?r:""),n=Object.assign({generateMipmaps:!1,internalFormat:null,minFilter:1006,depthBuffer:!0,stencilBuffer:!1,depthTexture:null,samples:0},n),this.texture=new V({width:e,height:t,depth:1},n.mapping,n.wrapS,n.wrapT,n.magFilter,n.minFilter,n.format,n.type,n.anisotropy,n.colorSpace),this.texture.isRenderTargetTexture=!0,this.texture.flipY=!1,this.texture.generateMipmaps=n.generateMipmaps,this.texture.internalFormat=n.internalFormat,this.depthBuffer=n.depthBuffer,this.stencilBuffer=n.stencilBuffer,this.depthTexture=n.depthTexture,this.samples=n.samples}}class W extends k{constructor(e=1,t=1,n={}){super(e,t,n),this.isWebGLRenderTarget=!0}}class X extends V{constructor(e=null,t=1,n=1,i=1){super(null),this.isDataArrayTexture=!0,this.image={data:e,width:t,height:n,depth:i},this.magFilter=1003,this.minFilter=1003,this.wrapR=1001,this.generateMipmaps=!1,this.flipY=!1,this.unpackAlignment=1}}class j extends V{constructor(e=null,t=1,n=1,i=1){super(null),this.isData3DTexture=!0,this.image={data:e,width:t,height:n,depth:i},this.magFilter=1003,this.minFilter=1003,this.wrapR=1001,this.generateMipmaps=!1,this.flipY=!1,this.unpackAlignment=1}}class q{static slerpFlat(e,t,n,i,r,a,s){let o=n[i+0],l=n[i+1],h=n[i+2],c=n[i+3],u=r[a+0],d=r[a+1],p=r[a+2],f=r[a+3];if(0===s){e[t+0]=o,e[t+1]=l,e[t+2]=h,e[t+3]=c;return}if(1===s){e[t+0]=u,e[t+1]=d,e[t+2]=p,e[t+3]=f;return}if(c!==f||o!==u||l!==d||h!==p){let e=1-s,t=o*u+l*d+h*p+c*f,n=t>=0?1:-1,i=1-t*t;if(i>Number.EPSILON){let r=Math.sqrt(i),a=Math.atan2(r,t*n);e=Math.sin(e*a)/r,s=Math.sin(s*a)/r}let r=s*n;if(o=o*e+u*r,l=l*e+d*r,h=h*e+p*r,c=c*e+f*r,e===1-s){let e=1/Math.sqrt(o*o+l*l+h*h+c*c);o*=e,l*=e,h*=e,c*=e}}e[t]=o,e[t+1]=l,e[t+2]=h,e[t+3]=c}static multiplyQuaternionsFlat(e,t,n,i,r,a){let s=n[i],o=n[i+1],l=n[i+2],h=n[i+3],c=r[a],u=r[a+1],d=r[a+2],p=r[a+3];return e[t]=s*p+h*c+o*d-l*u,e[t+1]=o*p+h*u+l*c-s*d,e[t+2]=l*p+h*d+s*u-o*c,e[t+3]=h*p-s*c-o*u-l*d,e}get x(){return this._x}set x(e){this._x=e,this._onChangeCallback()}get y(){return this._y}set y(e){this._y=e,this._onChangeCallback()}get z(){return this._z}set z(e){this._z=e,this._onChangeCallback()}get w(){return this._w}set w(e){this._w=e,this._onChangeCallback()}set(e,t,n,i){return this._x=e,this._y=t,this._z=n,this._w=i,this._onChangeCallback(),this}clone(){return new this.constructor(this._x,this._y,this._z,this._w)}copy(e){return this._x=e.x,this._y=e.y,this._z=e.z,this._w=e.w,this._onChangeCallback(),this}setFromEuler(e,t){let n=e._x,i=e._y,r=e._z,a=e._order,s=Math.cos,o=Math.sin,l=s(n/2),h=s(i/2),c=s(r/2),u=o(n/2),d=o(i/2),p=o(r/2);switch(a){case"XYZ":this._x=u*h*c+l*d*p,this._y=l*d*c-u*h*p,this._z=l*h*p+u*d*c,this._w=l*h*c-u*d*p;break;case"YXZ":this._x=u*h*c+l*d*p,this._y=l*d*c-u*h*p,this._z=l*h*p-u*d*c,this._w=l*h*c+u*d*p;break;case"ZXY":this._x=u*h*c-l*d*p,this._y=l*d*c+u*h*p,this._z=l*h*p+u*d*c,this._w=l*h*c-u*d*p;break;case"ZYX":this._x=u*h*c-l*d*p,this._y=l*d*c+u*h*p,this._z=l*h*p-u*d*c,this._w=l*h*c+u*d*p;break;case"YZX":this._x=u*h*c+l*d*p,this._y=l*d*c+u*h*p,this._z=l*h*p-u*d*c,this._w=l*h*c-u*d*p;break;case"XZY":this._x=u*h*c-l*d*p,this._y=l*d*c-u*h*p,this._z=l*h*p+u*d*c,this._w=l*h*c+u*d*p;break;default:console.warn("THREE.Quaternion: .setFromEuler() encountered an unknown order: "+a)}return!1!==t&&this._onChangeCallback(),this}setFromAxisAngle(e,t){let n=t/2,i=Math.sin(n);return this._x=e.x*i,this._y=e.y*i,this._z=e.z*i,this._w=Math.cos(n),this._onChangeCallback(),this}setFromRotationMatrix(e){let t=e.elements,n=t[0],i=t[4],r=t[8],a=t[1],s=t[5],o=t[9],l=t[2],h=t[6],c=t[10],u=n+s+c;if(u>0){let e=.5/Math.sqrt(u+1);this._w=.25/e,this._x=(h-o)*e,this._y=(r-l)*e,this._z=(a-i)*e}else if(n>s&&n>c){let e=2*Math.sqrt(1+n-s-c);this._w=(h-o)/e,this._x=.25*e,this._y=(i+a)/e,this._z=(r+l)/e}else if(s>c){let e=2*Math.sqrt(1+s-n-c);this._w=(r-l)/e,this._x=(i+a)/e,this._y=.25*e,this._z=(o+h)/e}else{let e=2*Math.sqrt(1+c-n-s);this._w=(a-i)/e,this._x=(r+l)/e,this._y=(o+h)/e,this._z=.25*e}return this._onChangeCallback(),this}setFromUnitVectors(e,t){let n=e.dot(t)+1;return n<Number.EPSILON?(n=0,Math.abs(e.x)>Math.abs(e.z)?(this._x=-e.y,this._y=e.x,this._z=0):(this._x=0,this._y=-e.z,this._z=e.y)):(this._x=e.y*t.z-e.z*t.y,this._y=e.z*t.x-e.x*t.z,this._z=e.x*t.y-e.y*t.x),this._w=n,this.normalize()}angleTo(e){return 2*Math.acos(Math.abs(_(this.dot(e),-1,1)))}rotateTowards(e,t){let n=this.angleTo(e);if(0===n)return this;let i=Math.min(1,t/n);return this.slerp(e,i),this}identity(){return this.set(0,0,0,1)}invert(){return this.conjugate()}conjugate(){return this._x*=-1,this._y*=-1,this._z*=-1,this._onChangeCallback(),this}dot(e){return this._x*e._x+this._y*e._y+this._z*e._z+this._w*e._w}lengthSq(){return this._x*this._x+this._y*this._y+this._z*this._z+this._w*this._w}length(){return Math.sqrt(this._x*this._x+this._y*this._y+this._z*this._z+this._w*this._w)}normalize(){let e=this.length();return 0===e?(this._x=0,this._y=0,this._z=0,this._w=1):(e=1/e,this._x=this._x*e,this._y=this._y*e,this._z=this._z*e,this._w=this._w*e),this._onChangeCallback(),this}multiply(e){return this.multiplyQuaternions(this,e)}premultiply(e){return this.multiplyQuaternions(e,this)}multiplyQuaternions(e,t){let n=e._x,i=e._y,r=e._z,a=e._w,s=t._x,o=t._y,l=t._z,h=t._w;return this._x=n*h+a*s+i*l-r*o,this._y=i*h+a*o+r*s-n*l,this._z=r*h+a*l+n*o-i*s,this._w=a*h-n*s-i*o-r*l,this._onChangeCallback(),this}slerp(e,t){if(0===t)return this;if(1===t)return this.copy(e);let n=this._x,i=this._y,r=this._z,a=this._w,s=a*e._w+n*e._x+i*e._y+r*e._z;if(s<0?(this._w=-e._w,this._x=-e._x,this._y=-e._y,this._z=-e._z,s=-s):this.copy(e),s>=1)return this._w=a,this._x=n,this._y=i,this._z=r,this;let o=1-s*s;if(o<=Number.EPSILON){let e=1-t;return this._w=e*a+t*this._w,this._x=e*n+t*this._x,this._y=e*i+t*this._y,this._z=e*r+t*this._z,this.normalize(),this._onChangeCallback(),this}let l=Math.sqrt(o),h=Math.atan2(l,s),c=Math.sin((1-t)*h)/l,u=Math.sin(t*h)/l;return this._w=a*c+this._w*u,this._x=n*c+this._x*u,this._y=i*c+this._y*u,this._z=r*c+this._z*u,this._onChangeCallback(),this}slerpQuaternions(e,t,n){return this.copy(e).slerp(t,n)}random(){let e=Math.random(),t=Math.sqrt(1-e),n=Math.sqrt(e),i=2*Math.PI*Math.random(),r=2*Math.PI*Math.random();return this.set(t*Math.cos(i),n*Math.sin(r),n*Math.cos(r),t*Math.sin(i))}equals(e){return e._x===this._x&&e._y===this._y&&e._z===this._z&&e._w===this._w}fromArray(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return this._x=e[t],this._y=e[t+1],this._z=e[t+2],this._w=e[t+3],this._onChangeCallback(),this}toArray(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return e[t]=this._x,e[t+1]=this._y,e[t+2]=this._z,e[t+3]=this._w,e}fromBufferAttribute(e,t){return this._x=e.getX(t),this._y=e.getY(t),this._z=e.getZ(t),this._w=e.getW(t),this}toJSON(){return this.toArray()}_onChange(e){return this._onChangeCallback=e,this}_onChangeCallback(){}*[Symbol.iterator](){yield this._x,yield this._y,yield this._z,yield this._w}constructor(e=0,t=0,n=0,i=1){this.isQuaternion=!0,this._x=e,this._y=t,this._z=n,this._w=i}}class Y{set(e,t,n){return void 0===n&&(n=this.z),this.x=e,this.y=t,this.z=n,this}setScalar(e){return this.x=e,this.y=e,this.z=e,this}setX(e){return this.x=e,this}setY(e){return this.y=e,this}setZ(e){return this.z=e,this}setComponent(e,t){switch(e){case 0:this.x=t;break;case 1:this.y=t;break;case 2:this.z=t;break;default:throw Error("index is out of range: "+e)}return this}getComponent(e){switch(e){case 0:return this.x;case 1:return this.y;case 2:return this.z;default:throw Error("index is out of range: "+e)}}clone(){return new this.constructor(this.x,this.y,this.z)}copy(e){return this.x=e.x,this.y=e.y,this.z=e.z,this}add(e){return this.x+=e.x,this.y+=e.y,this.z+=e.z,this}addScalar(e){return this.x+=e,this.y+=e,this.z+=e,this}addVectors(e,t){return this.x=e.x+t.x,this.y=e.y+t.y,this.z=e.z+t.z,this}addScaledVector(e,t){return this.x+=e.x*t,this.y+=e.y*t,this.z+=e.z*t,this}sub(e){return this.x-=e.x,this.y-=e.y,this.z-=e.z,this}subScalar(e){return this.x-=e,this.y-=e,this.z-=e,this}subVectors(e,t){return this.x=e.x-t.x,this.y=e.y-t.y,this.z=e.z-t.z,this}multiply(e){return this.x*=e.x,this.y*=e.y,this.z*=e.z,this}multiplyScalar(e){return this.x*=e,this.y*=e,this.z*=e,this}multiplyVectors(e,t){return this.x=e.x*t.x,this.y=e.y*t.y,this.z=e.z*t.z,this}applyEuler(e){return this.applyQuaternion(Z.setFromEuler(e))}applyAxisAngle(e,t){return this.applyQuaternion(Z.setFromAxisAngle(e,t))}applyMatrix3(e){let t=this.x,n=this.y,i=this.z,r=e.elements;return this.x=r[0]*t+r[3]*n+r[6]*i,this.y=r[1]*t+r[4]*n+r[7]*i,this.z=r[2]*t+r[5]*n+r[8]*i,this}applyNormalMatrix(e){return this.applyMatrix3(e).normalize()}applyMatrix4(e){let t=this.x,n=this.y,i=this.z,r=e.elements,a=1/(r[3]*t+r[7]*n+r[11]*i+r[15]);return this.x=(r[0]*t+r[4]*n+r[8]*i+r[12])*a,this.y=(r[1]*t+r[5]*n+r[9]*i+r[13])*a,this.z=(r[2]*t+r[6]*n+r[10]*i+r[14])*a,this}applyQuaternion(e){let t=this.x,n=this.y,i=this.z,r=e.x,a=e.y,s=e.z,o=e.w,l=2*(a*i-s*n),h=2*(s*t-r*i),c=2*(r*n-a*t);return this.x=t+o*l+a*c-s*h,this.y=n+o*h+s*l-r*c,this.z=i+o*c+r*h-a*l,this}project(e){return this.applyMatrix4(e.matrixWorldInverse).applyMatrix4(e.projectionMatrix)}unproject(e){return this.applyMatrix4(e.projectionMatrixInverse).applyMatrix4(e.matrixWorld)}transformDirection(e){let t=this.x,n=this.y,i=this.z,r=e.elements;return this.x=r[0]*t+r[4]*n+r[8]*i,this.y=r[1]*t+r[5]*n+r[9]*i,this.z=r[2]*t+r[6]*n+r[10]*i,this.normalize()}divide(e){return this.x/=e.x,this.y/=e.y,this.z/=e.z,this}divideScalar(e){return this.multiplyScalar(1/e)}min(e){return this.x=Math.min(this.x,e.x),this.y=Math.min(this.y,e.y),this.z=Math.min(this.z,e.z),this}max(e){return this.x=Math.max(this.x,e.x),this.y=Math.max(this.y,e.y),this.z=Math.max(this.z,e.z),this}clamp(e,t){return this.x=Math.max(e.x,Math.min(t.x,this.x)),this.y=Math.max(e.y,Math.min(t.y,this.y)),this.z=Math.max(e.z,Math.min(t.z,this.z)),this}clampScalar(e,t){return this.x=Math.max(e,Math.min(t,this.x)),this.y=Math.max(e,Math.min(t,this.y)),this.z=Math.max(e,Math.min(t,this.z)),this}clampLength(e,t){let n=this.length();return this.divideScalar(n||1).multiplyScalar(Math.max(e,Math.min(t,n)))}floor(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this.z=Math.floor(this.z),this}ceil(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this.z=Math.ceil(this.z),this}round(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this.z=Math.round(this.z),this}roundToZero(){return this.x=Math.trunc(this.x),this.y=Math.trunc(this.y),this.z=Math.trunc(this.z),this}negate(){return this.x=-this.x,this.y=-this.y,this.z=-this.z,this}dot(e){return this.x*e.x+this.y*e.y+this.z*e.z}lengthSq(){return this.x*this.x+this.y*this.y+this.z*this.z}length(){return Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z)}manhattanLength(){return Math.abs(this.x)+Math.abs(this.y)+Math.abs(this.z)}normalize(){return this.divideScalar(this.length()||1)}setLength(e){return this.normalize().multiplyScalar(e)}lerp(e,t){return this.x+=(e.x-this.x)*t,this.y+=(e.y-this.y)*t,this.z+=(e.z-this.z)*t,this}lerpVectors(e,t,n){return this.x=e.x+(t.x-e.x)*n,this.y=e.y+(t.y-e.y)*n,this.z=e.z+(t.z-e.z)*n,this}cross(e){return this.crossVectors(this,e)}crossVectors(e,t){let n=e.x,i=e.y,r=e.z,a=t.x,s=t.y,o=t.z;return this.x=i*o-r*s,this.y=r*a-n*o,this.z=n*s-i*a,this}projectOnVector(e){let t=e.lengthSq();if(0===t)return this.set(0,0,0);let n=e.dot(this)/t;return this.copy(e).multiplyScalar(n)}projectOnPlane(e){return J.copy(this).projectOnVector(e),this.sub(J)}reflect(e){return this.sub(J.copy(e).multiplyScalar(2*this.dot(e)))}angleTo(e){let t=Math.sqrt(this.lengthSq()*e.lengthSq());return 0===t?Math.PI/2:Math.acos(_(this.dot(e)/t,-1,1))}distanceTo(e){return Math.sqrt(this.distanceToSquared(e))}distanceToSquared(e){let t=this.x-e.x,n=this.y-e.y,i=this.z-e.z;return t*t+n*n+i*i}manhattanDistanceTo(e){return Math.abs(this.x-e.x)+Math.abs(this.y-e.y)+Math.abs(this.z-e.z)}setFromSpherical(e){return this.setFromSphericalCoords(e.radius,e.phi,e.theta)}setFromSphericalCoords(e,t,n){let i=Math.sin(t)*e;return this.x=i*Math.sin(n),this.y=Math.cos(t)*e,this.z=i*Math.cos(n),this}setFromCylindrical(e){return this.setFromCylindricalCoords(e.radius,e.theta,e.y)}setFromCylindricalCoords(e,t,n){return this.x=e*Math.sin(t),this.y=n,this.z=e*Math.cos(t),this}setFromMatrixPosition(e){let t=e.elements;return this.x=t[12],this.y=t[13],this.z=t[14],this}setFromMatrixScale(e){let t=this.setFromMatrixColumn(e,0).length(),n=this.setFromMatrixColumn(e,1).length(),i=this.setFromMatrixColumn(e,2).length();return this.x=t,this.y=n,this.z=i,this}setFromMatrixColumn(e,t){return this.fromArray(e.elements,4*t)}setFromMatrix3Column(e,t){return this.fromArray(e.elements,3*t)}setFromEuler(e){return this.x=e._x,this.y=e._y,this.z=e._z,this}setFromColor(e){return this.x=e.r,this.y=e.g,this.z=e.b,this}equals(e){return e.x===this.x&&e.y===this.y&&e.z===this.z}fromArray(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return this.x=e[t],this.y=e[t+1],this.z=e[t+2],this}toArray(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return e[t]=this.x,e[t+1]=this.y,e[t+2]=this.z,e}fromBufferAttribute(e,t){return this.x=e.getX(t),this.y=e.getY(t),this.z=e.getZ(t),this}random(){return this.x=Math.random(),this.y=Math.random(),this.z=Math.random(),this}randomDirection(){let e=(Math.random()-.5)*2,t=Math.random()*Math.PI*2,n=Math.sqrt(1-e**2);return this.x=n*Math.cos(t),this.y=n*Math.sin(t),this.z=e,this}*[Symbol.iterator](){yield this.x,yield this.y,yield this.z}constructor(e=0,t=0,n=0){Y.prototype.isVector3=!0,this.x=e,this.y=t,this.z=n}}let J=new Y,Z=new q;class K{set(e,t){return this.min.copy(e),this.max.copy(t),this}setFromArray(e){this.makeEmpty();for(let t=0,n=e.length;t<n;t+=3)this.expandByPoint($.fromArray(e,t));return this}setFromBufferAttribute(e){this.makeEmpty();for(let t=0,n=e.count;t<n;t++)this.expandByPoint($.fromBufferAttribute(e,t));return this}setFromPoints(e){this.makeEmpty();for(let t=0,n=e.length;t<n;t++)this.expandByPoint(e[t]);return this}setFromCenterAndSize(e,t){let n=$.copy(t).multiplyScalar(.5);return this.min.copy(e).sub(n),this.max.copy(e).add(n),this}setFromObject(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return this.makeEmpty(),this.expandByObject(e,t)}clone(){return new this.constructor().copy(this)}copy(e){return this.min.copy(e.min),this.max.copy(e.max),this}makeEmpty(){return this.min.x=this.min.y=this.min.z=Infinity,this.max.x=this.max.y=this.max.z=-1/0,this}isEmpty(){return this.max.x<this.min.x||this.max.y<this.min.y||this.max.z<this.min.z}getCenter(e){return this.isEmpty()?e.set(0,0,0):e.addVectors(this.min,this.max).multiplyScalar(.5)}getSize(e){return this.isEmpty()?e.set(0,0,0):e.subVectors(this.max,this.min)}expandByPoint(e){return this.min.min(e),this.max.max(e),this}expandByVector(e){return this.min.sub(e),this.max.add(e),this}expandByScalar(e){return this.min.addScalar(-e),this.max.addScalar(e),this}expandByObject(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];e.updateWorldMatrix(!1,!1);let n=e.geometry;if(void 0!==n){let i=n.getAttribute("position");if(!0===t&&void 0!==i&&!0!==e.isInstancedMesh)for(let t=0,n=i.count;t<n;t++)!0===e.isMesh?e.getVertexPosition(t,$):$.fromBufferAttribute(i,t),$.applyMatrix4(e.matrixWorld),this.expandByPoint($);else void 0!==e.boundingBox?(null===e.boundingBox&&e.computeBoundingBox(),ee.copy(e.boundingBox)):(null===n.boundingBox&&n.computeBoundingBox(),ee.copy(n.boundingBox)),ee.applyMatrix4(e.matrixWorld),this.union(ee)}let i=e.children;for(let e=0,n=i.length;e<n;e++)this.expandByObject(i[e],t);return this}containsPoint(e){return!(e.x<this.min.x)&&!(e.x>this.max.x)&&!(e.y<this.min.y)&&!(e.y>this.max.y)&&!(e.z<this.min.z)&&!(e.z>this.max.z)}containsBox(e){return this.min.x<=e.min.x&&e.max.x<=this.max.x&&this.min.y<=e.min.y&&e.max.y<=this.max.y&&this.min.z<=e.min.z&&e.max.z<=this.max.z}getParameter(e,t){return t.set((e.x-this.min.x)/(this.max.x-this.min.x),(e.y-this.min.y)/(this.max.y-this.min.y),(e.z-this.min.z)/(this.max.z-this.min.z))}intersectsBox(e){return!(e.max.x<this.min.x)&&!(e.min.x>this.max.x)&&!(e.max.y<this.min.y)&&!(e.min.y>this.max.y)&&!(e.max.z<this.min.z)&&!(e.min.z>this.max.z)}intersectsSphere(e){return this.clampPoint(e.center,$),$.distanceToSquared(e.center)<=e.radius*e.radius}intersectsPlane(e){let t,n;return e.normal.x>0?(t=e.normal.x*this.min.x,n=e.normal.x*this.max.x):(t=e.normal.x*this.max.x,n=e.normal.x*this.min.x),e.normal.y>0?(t+=e.normal.y*this.min.y,n+=e.normal.y*this.max.y):(t+=e.normal.y*this.max.y,n+=e.normal.y*this.min.y),e.normal.z>0?(t+=e.normal.z*this.min.z,n+=e.normal.z*this.max.z):(t+=e.normal.z*this.max.z,n+=e.normal.z*this.min.z),t<=-e.constant&&n>=-e.constant}intersectsTriangle(e){if(this.isEmpty())return!1;this.getCenter(eo),el.subVectors(this.max,eo),et.subVectors(e.a,eo),en.subVectors(e.b,eo),ei.subVectors(e.c,eo),er.subVectors(en,et),ea.subVectors(ei,en),es.subVectors(et,ei);let t=[0,-er.z,er.y,0,-ea.z,ea.y,0,-es.z,es.y,er.z,0,-er.x,ea.z,0,-ea.x,es.z,0,-es.x,-er.y,er.x,0,-ea.y,ea.x,0,-es.y,es.x,0];return!!eu(t,et,en,ei,el)&&!!eu(t=[1,0,0,0,1,0,0,0,1],et,en,ei,el)&&(eh.crossVectors(er,ea),eu(t=[eh.x,eh.y,eh.z],et,en,ei,el))}clampPoint(e,t){return t.copy(e).clamp(this.min,this.max)}distanceToPoint(e){return this.clampPoint(e,$).distanceTo(e)}getBoundingSphere(e){return this.isEmpty()?e.makeEmpty():(this.getCenter(e.center),e.radius=.5*this.getSize($).length()),e}intersect(e){return this.min.max(e.min),this.max.min(e.max),this.isEmpty()&&this.makeEmpty(),this}union(e){return this.min.min(e.min),this.max.max(e.max),this}applyMatrix4(e){return this.isEmpty()||(Q[0].set(this.min.x,this.min.y,this.min.z).applyMatrix4(e),Q[1].set(this.min.x,this.min.y,this.max.z).applyMatrix4(e),Q[2].set(this.min.x,this.max.y,this.min.z).applyMatrix4(e),Q[3].set(this.min.x,this.max.y,this.max.z).applyMatrix4(e),Q[4].set(this.max.x,this.min.y,this.min.z).applyMatrix4(e),Q[5].set(this.max.x,this.min.y,this.max.z).applyMatrix4(e),Q[6].set(this.max.x,this.max.y,this.min.z).applyMatrix4(e),Q[7].set(this.max.x,this.max.y,this.max.z).applyMatrix4(e),this.setFromPoints(Q)),this}translate(e){return this.min.add(e),this.max.add(e),this}equals(e){return e.min.equals(this.min)&&e.max.equals(this.max)}constructor(e=new Y(Infinity,Infinity,Infinity),t=new Y(-1/0,-1/0,-1/0)){this.isBox3=!0,this.min=e,this.max=t}}let Q=[new Y,new Y,new Y,new Y,new Y,new Y,new Y,new Y],$=new Y,ee=new K,et=new Y,en=new Y,ei=new Y,er=new Y,ea=new Y,es=new Y,eo=new Y,el=new Y,eh=new Y,ec=new Y;function eu(e,t,n,i,r){for(let a=0,s=e.length-3;a<=s;a+=3){ec.fromArray(e,a);let s=r.x*Math.abs(ec.x)+r.y*Math.abs(ec.y)+r.z*Math.abs(ec.z),o=t.dot(ec),l=n.dot(ec),h=i.dot(ec);if(Math.max(-Math.max(o,l,h),Math.min(o,l,h))>s)return!1}return!0}let ed=new K,ep=new Y,ef=new Y;class em{set(e,t){return this.center.copy(e),this.radius=t,this}setFromPoints(e,t){let n=this.center;void 0!==t?n.copy(t):ed.setFromPoints(e).getCenter(n);let i=0;for(let t=0,r=e.length;t<r;t++)i=Math.max(i,n.distanceToSquared(e[t]));return this.radius=Math.sqrt(i),this}copy(e){return this.center.copy(e.center),this.radius=e.radius,this}isEmpty(){return this.radius<0}makeEmpty(){return this.center.set(0,0,0),this.radius=-1,this}containsPoint(e){return e.distanceToSquared(this.center)<=this.radius*this.radius}distanceToPoint(e){return e.distanceTo(this.center)-this.radius}intersectsSphere(e){let t=this.radius+e.radius;return e.center.distanceToSquared(this.center)<=t*t}intersectsBox(e){return e.intersectsSphere(this)}intersectsPlane(e){return Math.abs(e.distanceToPoint(this.center))<=this.radius}clampPoint(e,t){let n=this.center.distanceToSquared(e);return t.copy(e),n>this.radius*this.radius&&(t.sub(this.center).normalize(),t.multiplyScalar(this.radius).add(this.center)),t}getBoundingBox(e){return this.isEmpty()?e.makeEmpty():(e.set(this.center,this.center),e.expandByScalar(this.radius)),e}applyMatrix4(e){return this.center.applyMatrix4(e),this.radius=this.radius*e.getMaxScaleOnAxis(),this}translate(e){return this.center.add(e),this}expandByPoint(e){if(this.isEmpty())return this.center.copy(e),this.radius=0,this;ep.subVectors(e,this.center);let t=ep.lengthSq();if(t>this.radius*this.radius){let e=Math.sqrt(t),n=(e-this.radius)*.5;this.center.addScaledVector(ep,n/e),this.radius+=n}return this}union(e){return e.isEmpty()||(this.isEmpty()?this.copy(e):!0===this.center.equals(e.center)?this.radius=Math.max(this.radius,e.radius):(ef.subVectors(e.center,this.center).setLength(e.radius),this.expandByPoint(ep.copy(e.center).add(ef)),this.expandByPoint(ep.copy(e.center).sub(ef)))),this}equals(e){return e.center.equals(this.center)&&e.radius===this.radius}clone(){return new this.constructor().copy(this)}constructor(e=new Y,t=-1){this.center=e,this.radius=t}}let eg=new Y,e_=new Y,ev=new Y,ex=new Y,ey=new Y,eM=new Y,eS=new Y;class eE{set(e,t){return this.origin.copy(e),this.direction.copy(t),this}copy(e){return this.origin.copy(e.origin),this.direction.copy(e.direction),this}at(e,t){return t.copy(this.origin).addScaledVector(this.direction,e)}lookAt(e){return this.direction.copy(e).sub(this.origin).normalize(),this}recast(e){return this.origin.copy(this.at(e,eg)),this}closestPointToPoint(e,t){t.subVectors(e,this.origin);let n=t.dot(this.direction);return n<0?t.copy(this.origin):t.copy(this.origin).addScaledVector(this.direction,n)}distanceToPoint(e){return Math.sqrt(this.distanceSqToPoint(e))}distanceSqToPoint(e){let t=eg.subVectors(e,this.origin).dot(this.direction);return t<0?this.origin.distanceToSquared(e):(eg.copy(this.origin).addScaledVector(this.direction,t),eg.distanceToSquared(e))}distanceSqToSegment(e,t,n,i){let r,a,s,o;e_.copy(e).add(t).multiplyScalar(.5),ev.copy(t).sub(e).normalize(),ex.copy(this.origin).sub(e_);let l=.5*e.distanceTo(t),h=-this.direction.dot(ev),c=ex.dot(this.direction),u=-ex.dot(ev),d=ex.lengthSq(),p=Math.abs(1-h*h);if(p>0)if(r=h*u-c,a=h*c-u,o=l*p,r>=0)if(a>=-o)if(a<=o){let e=1/p;r*=e,a*=e,s=r*(r+h*a+2*c)+a*(h*r+a+2*u)+d}else s=-(r=Math.max(0,-(h*(a=l)+c)))*r+a*(a+2*u)+d;else s=-(r=Math.max(0,-(h*(a=-l)+c)))*r+a*(a+2*u)+d;else a<=-o?(a=(r=Math.max(0,-(-h*l+c)))>0?-l:Math.min(Math.max(-l,-u),l),s=-r*r+a*(a+2*u)+d):a<=o?(r=0,s=(a=Math.min(Math.max(-l,-u),l))*(a+2*u)+d):(a=(r=Math.max(0,-(h*l+c)))>0?l:Math.min(Math.max(-l,-u),l),s=-r*r+a*(a+2*u)+d);else a=h>0?-l:l,s=-(r=Math.max(0,-(h*a+c)))*r+a*(a+2*u)+d;return n&&n.copy(this.origin).addScaledVector(this.direction,r),i&&i.copy(e_).addScaledVector(ev,a),s}intersectSphere(e,t){eg.subVectors(e.center,this.origin);let n=eg.dot(this.direction),i=eg.dot(eg)-n*n,r=e.radius*e.radius;if(i>r)return null;let a=Math.sqrt(r-i),s=n-a,o=n+a;return o<0?null:s<0?this.at(o,t):this.at(s,t)}intersectsSphere(e){return this.distanceSqToPoint(e.center)<=e.radius*e.radius}distanceToPlane(e){let t=e.normal.dot(this.direction);if(0===t)return 0===e.distanceToPoint(this.origin)?0:null;let n=-(this.origin.dot(e.normal)+e.constant)/t;return n>=0?n:null}intersectPlane(e,t){let n=this.distanceToPlane(e);return null===n?null:this.at(n,t)}intersectsPlane(e){let t=e.distanceToPoint(this.origin);return!!(0===t||e.normal.dot(this.direction)*t<0)}intersectBox(e,t){let n,i,r,a,s,o,l=1/this.direction.x,h=1/this.direction.y,c=1/this.direction.z,u=this.origin;return(l>=0?(n=(e.min.x-u.x)*l,i=(e.max.x-u.x)*l):(n=(e.max.x-u.x)*l,i=(e.min.x-u.x)*l),h>=0?(r=(e.min.y-u.y)*h,a=(e.max.y-u.y)*h):(r=(e.max.y-u.y)*h,a=(e.min.y-u.y)*h),n>a||r>i||((r>n||isNaN(n))&&(n=r),(a<i||isNaN(i))&&(i=a),c>=0?(s=(e.min.z-u.z)*c,o=(e.max.z-u.z)*c):(s=(e.max.z-u.z)*c,o=(e.min.z-u.z)*c),n>o||s>i||((s>n||n!=n)&&(n=s),(o<i||i!=i)&&(i=o),i<0)))?null:this.at(n>=0?n:i,t)}intersectsBox(e){return null!==this.intersectBox(e,eg)}intersectTriangle(e,t,n,i,r){let a;ey.subVectors(t,e),eM.subVectors(n,e),eS.crossVectors(ey,eM);let s=this.direction.dot(eS);if(s>0){if(i)return null;a=1}else{if(!(s<0))return null;a=-1,s=-s}ex.subVectors(this.origin,e);let o=a*this.direction.dot(eM.crossVectors(ex,eM));if(o<0)return null;let l=a*this.direction.dot(ey.cross(ex));if(l<0||o+l>s)return null;let h=-a*ex.dot(eS);return h<0?null:this.at(h/s,r)}applyMatrix4(e){return this.origin.applyMatrix4(e),this.direction.transformDirection(e),this}equals(e){return e.origin.equals(this.origin)&&e.direction.equals(this.direction)}clone(){return new this.constructor().copy(this)}constructor(e=new Y,t=new Y(0,0,-1)){this.origin=e,this.direction=t}}class eT{set(e,t,n,i,r,a,s,o,l,h,c,u,d,p,f,m){let g=this.elements;return g[0]=e,g[4]=t,g[8]=n,g[12]=i,g[1]=r,g[5]=a,g[9]=s,g[13]=o,g[2]=l,g[6]=h,g[10]=c,g[14]=u,g[3]=d,g[7]=p,g[11]=f,g[15]=m,this}identity(){return this.set(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1),this}clone(){return new eT().fromArray(this.elements)}copy(e){let t=this.elements,n=e.elements;return t[0]=n[0],t[1]=n[1],t[2]=n[2],t[3]=n[3],t[4]=n[4],t[5]=n[5],t[6]=n[6],t[7]=n[7],t[8]=n[8],t[9]=n[9],t[10]=n[10],t[11]=n[11],t[12]=n[12],t[13]=n[13],t[14]=n[14],t[15]=n[15],this}copyPosition(e){let t=this.elements,n=e.elements;return t[12]=n[12],t[13]=n[13],t[14]=n[14],this}setFromMatrix3(e){let t=e.elements;return this.set(t[0],t[3],t[6],0,t[1],t[4],t[7],0,t[2],t[5],t[8],0,0,0,0,1),this}extractBasis(e,t,n){return e.setFromMatrixColumn(this,0),t.setFromMatrixColumn(this,1),n.setFromMatrixColumn(this,2),this}makeBasis(e,t,n){return this.set(e.x,t.x,n.x,0,e.y,t.y,n.y,0,e.z,t.z,n.z,0,0,0,0,1),this}extractRotation(e){let t=this.elements,n=e.elements,i=1/eb.setFromMatrixColumn(e,0).length(),r=1/eb.setFromMatrixColumn(e,1).length(),a=1/eb.setFromMatrixColumn(e,2).length();return t[0]=n[0]*i,t[1]=n[1]*i,t[2]=n[2]*i,t[3]=0,t[4]=n[4]*r,t[5]=n[5]*r,t[6]=n[6]*r,t[7]=0,t[8]=n[8]*a,t[9]=n[9]*a,t[10]=n[10]*a,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,this}makeRotationFromEuler(e){let t=this.elements,n=e.x,i=e.y,r=e.z,a=Math.cos(n),s=Math.sin(n),o=Math.cos(i),l=Math.sin(i),h=Math.cos(r),c=Math.sin(r);if("XYZ"===e.order){let e=a*h,n=a*c,i=s*h,r=s*c;t[0]=o*h,t[4]=-o*c,t[8]=l,t[1]=n+i*l,t[5]=e-r*l,t[9]=-s*o,t[2]=r-e*l,t[6]=i+n*l,t[10]=a*o}else if("YXZ"===e.order){let e=o*h,n=o*c,i=l*h,r=l*c;t[0]=e+r*s,t[4]=i*s-n,t[8]=a*l,t[1]=a*c,t[5]=a*h,t[9]=-s,t[2]=n*s-i,t[6]=r+e*s,t[10]=a*o}else if("ZXY"===e.order){let e=o*h,n=o*c,i=l*h,r=l*c;t[0]=e-r*s,t[4]=-a*c,t[8]=i+n*s,t[1]=n+i*s,t[5]=a*h,t[9]=r-e*s,t[2]=-a*l,t[6]=s,t[10]=a*o}else if("ZYX"===e.order){let e=a*h,n=a*c,i=s*h,r=s*c;t[0]=o*h,t[4]=i*l-n,t[8]=e*l+r,t[1]=o*c,t[5]=r*l+e,t[9]=n*l-i,t[2]=-l,t[6]=s*o,t[10]=a*o}else if("YZX"===e.order){let e=a*o,n=a*l,i=s*o,r=s*l;t[0]=o*h,t[4]=r-e*c,t[8]=i*c+n,t[1]=c,t[5]=a*h,t[9]=-s*h,t[2]=-l*h,t[6]=n*c+i,t[10]=e-r*c}else if("XZY"===e.order){let e=a*o,n=a*l,i=s*o,r=s*l;t[0]=o*h,t[4]=-c,t[8]=l*h,t[1]=e*c+r,t[5]=a*h,t[9]=n*c-i,t[2]=i*c-n,t[6]=s*h,t[10]=r*c+e}return t[3]=0,t[7]=0,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,this}makeRotationFromQuaternion(e){return this.compose(ew,e,eR)}lookAt(e,t,n){let i=this.elements;return eP.subVectors(e,t),0===eP.lengthSq()&&(eP.z=1),eP.normalize(),eC.crossVectors(n,eP),0===eC.lengthSq()&&(1===Math.abs(n.z)?eP.x+=1e-4:eP.z+=1e-4,eP.normalize(),eC.crossVectors(n,eP)),eC.normalize(),eL.crossVectors(eP,eC),i[0]=eC.x,i[4]=eL.x,i[8]=eP.x,i[1]=eC.y,i[5]=eL.y,i[9]=eP.y,i[2]=eC.z,i[6]=eL.z,i[10]=eP.z,this}multiply(e){return this.multiplyMatrices(this,e)}premultiply(e){return this.multiplyMatrices(e,this)}multiplyMatrices(e,t){let n=e.elements,i=t.elements,r=this.elements,a=n[0],s=n[4],o=n[8],l=n[12],h=n[1],c=n[5],u=n[9],d=n[13],p=n[2],f=n[6],m=n[10],g=n[14],_=n[3],v=n[7],x=n[11],y=n[15],M=i[0],S=i[4],E=i[8],T=i[12],b=i[1],A=i[5],w=i[9],R=i[13],C=i[2],L=i[6],P=i[10],U=i[14],N=i[3],D=i[7],I=i[11],O=i[15];return r[0]=a*M+s*b+o*C+l*N,r[4]=a*S+s*A+o*L+l*D,r[8]=a*E+s*w+o*P+l*I,r[12]=a*T+s*R+o*U+l*O,r[1]=h*M+c*b+u*C+d*N,r[5]=h*S+c*A+u*L+d*D,r[9]=h*E+c*w+u*P+d*I,r[13]=h*T+c*R+u*U+d*O,r[2]=p*M+f*b+m*C+g*N,r[6]=p*S+f*A+m*L+g*D,r[10]=p*E+f*w+m*P+g*I,r[14]=p*T+f*R+m*U+g*O,r[3]=_*M+v*b+x*C+y*N,r[7]=_*S+v*A+x*L+y*D,r[11]=_*E+v*w+x*P+y*I,r[15]=_*T+v*R+x*U+y*O,this}multiplyScalar(e){let t=this.elements;return t[0]*=e,t[4]*=e,t[8]*=e,t[12]*=e,t[1]*=e,t[5]*=e,t[9]*=e,t[13]*=e,t[2]*=e,t[6]*=e,t[10]*=e,t[14]*=e,t[3]*=e,t[7]*=e,t[11]*=e,t[15]*=e,this}determinant(){let e=this.elements,t=e[0],n=e[4],i=e[8],r=e[12],a=e[1],s=e[5],o=e[9],l=e[13],h=e[2],c=e[6],u=e[10],d=e[14],p=e[3],f=e[7];return p*(r*o*c-i*l*c-r*s*u+n*l*u+i*s*d-n*o*d)+f*(t*o*d-t*l*u+r*a*u-i*a*d+i*l*h-r*o*h)+e[11]*(t*l*c-t*s*d-r*a*c+n*a*d+r*s*h-n*l*h)+e[15]*(-i*s*h-t*o*c+t*s*u+i*a*c-n*a*u+n*o*h)}transpose(){let e,t=this.elements;return e=t[1],t[1]=t[4],t[4]=e,e=t[2],t[2]=t[8],t[8]=e,e=t[6],t[6]=t[9],t[9]=e,e=t[3],t[3]=t[12],t[12]=e,e=t[7],t[7]=t[13],t[13]=e,e=t[11],t[11]=t[14],t[14]=e,this}setPosition(e,t,n){let i=this.elements;return e.isVector3?(i[12]=e.x,i[13]=e.y,i[14]=e.z):(i[12]=e,i[13]=t,i[14]=n),this}invert(){let e=this.elements,t=e[0],n=e[1],i=e[2],r=e[3],a=e[4],s=e[5],o=e[6],l=e[7],h=e[8],c=e[9],u=e[10],d=e[11],p=e[12],f=e[13],m=e[14],g=e[15],_=c*m*l-f*u*l+f*o*d-s*m*d-c*o*g+s*u*g,v=p*u*l-h*m*l-p*o*d+a*m*d+h*o*g-a*u*g,x=h*f*l-p*c*l+p*s*d-a*f*d-h*s*g+a*c*g,y=p*c*o-h*f*o-p*s*u+a*f*u+h*s*m-a*c*m,M=t*_+n*v+i*x+r*y;if(0===M)return this.set(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0);let S=1/M;return e[0]=_*S,e[1]=(f*u*r-c*m*r-f*i*d+n*m*d+c*i*g-n*u*g)*S,e[2]=(s*m*r-f*o*r+f*i*l-n*m*l-s*i*g+n*o*g)*S,e[3]=(c*o*r-s*u*r-c*i*l+n*u*l+s*i*d-n*o*d)*S,e[4]=v*S,e[5]=(h*m*r-p*u*r+p*i*d-t*m*d-h*i*g+t*u*g)*S,e[6]=(p*o*r-a*m*r-p*i*l+t*m*l+a*i*g-t*o*g)*S,e[7]=(a*u*r-h*o*r+h*i*l-t*u*l-a*i*d+t*o*d)*S,e[8]=x*S,e[9]=(p*c*r-h*f*r-p*n*d+t*f*d+h*n*g-t*c*g)*S,e[10]=(a*f*r-p*s*r+p*n*l-t*f*l-a*n*g+t*s*g)*S,e[11]=(h*s*r-a*c*r-h*n*l+t*c*l+a*n*d-t*s*d)*S,e[12]=y*S,e[13]=(h*f*i-p*c*i+p*n*u-t*f*u-h*n*m+t*c*m)*S,e[14]=(p*s*i-a*f*i-p*n*o+t*f*o+a*n*m-t*s*m)*S,e[15]=(a*c*i-h*s*i+h*n*o-t*c*o-a*n*u+t*s*u)*S,this}scale(e){let t=this.elements,n=e.x,i=e.y,r=e.z;return t[0]*=n,t[4]*=i,t[8]*=r,t[1]*=n,t[5]*=i,t[9]*=r,t[2]*=n,t[6]*=i,t[10]*=r,t[3]*=n,t[7]*=i,t[11]*=r,this}getMaxScaleOnAxis(){let e=this.elements,t=e[0]*e[0]+e[1]*e[1]+e[2]*e[2];return Math.sqrt(Math.max(t,e[4]*e[4]+e[5]*e[5]+e[6]*e[6],e[8]*e[8]+e[9]*e[9]+e[10]*e[10]))}makeTranslation(e,t,n){return e.isVector3?this.set(1,0,0,e.x,0,1,0,e.y,0,0,1,e.z,0,0,0,1):this.set(1,0,0,e,0,1,0,t,0,0,1,n,0,0,0,1),this}makeRotationX(e){let t=Math.cos(e),n=Math.sin(e);return this.set(1,0,0,0,0,t,-n,0,0,n,t,0,0,0,0,1),this}makeRotationY(e){let t=Math.cos(e),n=Math.sin(e);return this.set(t,0,n,0,0,1,0,0,-n,0,t,0,0,0,0,1),this}makeRotationZ(e){let t=Math.cos(e),n=Math.sin(e);return this.set(t,-n,0,0,n,t,0,0,0,0,1,0,0,0,0,1),this}makeRotationAxis(e,t){let n=Math.cos(t),i=Math.sin(t),r=1-n,a=e.x,s=e.y,o=e.z,l=r*a,h=r*s;return this.set(l*a+n,l*s-i*o,l*o+i*s,0,l*s+i*o,h*s+n,h*o-i*a,0,l*o-i*s,h*o+i*a,r*o*o+n,0,0,0,0,1),this}makeScale(e,t,n){return this.set(e,0,0,0,0,t,0,0,0,0,n,0,0,0,0,1),this}makeShear(e,t,n,i,r,a){return this.set(1,n,r,0,e,1,a,0,t,i,1,0,0,0,0,1),this}compose(e,t,n){let i=this.elements,r=t._x,a=t._y,s=t._z,o=t._w,l=r+r,h=a+a,c=s+s,u=r*l,d=r*h,p=r*c,f=a*h,m=a*c,g=s*c,_=o*l,v=o*h,x=o*c,y=n.x,M=n.y,S=n.z;return i[0]=(1-(f+g))*y,i[1]=(d+x)*y,i[2]=(p-v)*y,i[3]=0,i[4]=(d-x)*M,i[5]=(1-(u+g))*M,i[6]=(m+_)*M,i[7]=0,i[8]=(p+v)*S,i[9]=(m-_)*S,i[10]=(1-(u+f))*S,i[11]=0,i[12]=e.x,i[13]=e.y,i[14]=e.z,i[15]=1,this}decompose(e,t,n){let i=this.elements,r=eb.set(i[0],i[1],i[2]).length(),a=eb.set(i[4],i[5],i[6]).length(),s=eb.set(i[8],i[9],i[10]).length();0>this.determinant()&&(r=-r),e.x=i[12],e.y=i[13],e.z=i[14],eA.copy(this);let o=1/r,l=1/a,h=1/s;return eA.elements[0]*=o,eA.elements[1]*=o,eA.elements[2]*=o,eA.elements[4]*=l,eA.elements[5]*=l,eA.elements[6]*=l,eA.elements[8]*=h,eA.elements[9]*=h,eA.elements[10]*=h,t.setFromRotationMatrix(eA),n.x=r,n.y=a,n.z=s,this}makePerspective(e,t,n,i,r,a){let s,o,l=arguments.length>6&&void 0!==arguments[6]?arguments[6]:2e3,h=this.elements;if(2e3===l)s=-(a+r)/(a-r),o=-2*a*r/(a-r);else if(2001===l)s=-a/(a-r),o=-a*r/(a-r);else throw Error("THREE.Matrix4.makePerspective(): Invalid coordinate system: "+l);return h[0]=2*r/(t-e),h[4]=0,h[8]=(t+e)/(t-e),h[12]=0,h[1]=0,h[5]=2*r/(n-i),h[9]=(n+i)/(n-i),h[13]=0,h[2]=0,h[6]=0,h[10]=s,h[14]=o,h[3]=0,h[7]=0,h[11]=-1,h[15]=0,this}makeOrthographic(e,t,n,i,r,a){let s,o,l=arguments.length>6&&void 0!==arguments[6]?arguments[6]:2e3,h=this.elements,c=1/(t-e),u=1/(n-i),d=1/(a-r);if(2e3===l)s=(a+r)*d,o=-2*d;else if(2001===l)s=r*d,o=-1*d;else throw Error("THREE.Matrix4.makeOrthographic(): Invalid coordinate system: "+l);return h[0]=2*c,h[4]=0,h[8]=0,h[12]=-((t+e)*c),h[1]=0,h[5]=2*u,h[9]=0,h[13]=-((n+i)*u),h[2]=0,h[6]=0,h[10]=o,h[14]=-s,h[3]=0,h[7]=0,h[11]=0,h[15]=1,this}equals(e){let t=this.elements,n=e.elements;for(let e=0;e<16;e++)if(t[e]!==n[e])return!1;return!0}fromArray(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;for(let n=0;n<16;n++)this.elements[n]=e[n+t];return this}toArray(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=this.elements;return e[t]=n[0],e[t+1]=n[1],e[t+2]=n[2],e[t+3]=n[3],e[t+4]=n[4],e[t+5]=n[5],e[t+6]=n[6],e[t+7]=n[7],e[t+8]=n[8],e[t+9]=n[9],e[t+10]=n[10],e[t+11]=n[11],e[t+12]=n[12],e[t+13]=n[13],e[t+14]=n[14],e[t+15]=n[15],e}constructor(e,t,n,i,r,a,s,o,l,h,c,u,d,p,f,m){eT.prototype.isMatrix4=!0,this.elements=[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1],void 0!==e&&this.set(e,t,n,i,r,a,s,o,l,h,c,u,d,p,f,m)}}let eb=new Y,eA=new eT,ew=new Y(0,0,0),eR=new Y(1,1,1),eC=new Y,eL=new Y,eP=new Y,eU=new eT,eN=new q;class eD{get x(){return this._x}set x(e){this._x=e,this._onChangeCallback()}get y(){return this._y}set y(e){this._y=e,this._onChangeCallback()}get z(){return this._z}set z(e){this._z=e,this._onChangeCallback()}get order(){return this._order}set order(e){this._order=e,this._onChangeCallback()}set(e,t,n){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:this._order;return this._x=e,this._y=t,this._z=n,this._order=i,this._onChangeCallback(),this}clone(){return new this.constructor(this._x,this._y,this._z,this._order)}copy(e){return this._x=e._x,this._y=e._y,this._z=e._z,this._order=e._order,this._onChangeCallback(),this}setFromRotationMatrix(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this._order,n=!(arguments.length>2)||void 0===arguments[2]||arguments[2],i=e.elements,r=i[0],a=i[4],s=i[8],o=i[1],l=i[5],h=i[9],c=i[2],u=i[6],d=i[10];switch(t){case"XYZ":this._y=Math.asin(_(s,-1,1)),.9999999>Math.abs(s)?(this._x=Math.atan2(-h,d),this._z=Math.atan2(-a,r)):(this._x=Math.atan2(u,l),this._z=0);break;case"YXZ":this._x=Math.asin(-_(h,-1,1)),.9999999>Math.abs(h)?(this._y=Math.atan2(s,d),this._z=Math.atan2(o,l)):(this._y=Math.atan2(-c,r),this._z=0);break;case"ZXY":this._x=Math.asin(_(u,-1,1)),.9999999>Math.abs(u)?(this._y=Math.atan2(-c,d),this._z=Math.atan2(-a,l)):(this._y=0,this._z=Math.atan2(o,r));break;case"ZYX":this._y=Math.asin(-_(c,-1,1)),.9999999>Math.abs(c)?(this._x=Math.atan2(u,d),this._z=Math.atan2(o,r)):(this._x=0,this._z=Math.atan2(-a,l));break;case"YZX":this._z=Math.asin(_(o,-1,1)),.9999999>Math.abs(o)?(this._x=Math.atan2(-h,l),this._y=Math.atan2(-c,r)):(this._x=0,this._y=Math.atan2(s,d));break;case"XZY":this._z=Math.asin(-_(a,-1,1)),.9999999>Math.abs(a)?(this._x=Math.atan2(u,l),this._y=Math.atan2(s,r)):(this._x=Math.atan2(-h,d),this._y=0);break;default:console.warn("THREE.Euler: .setFromRotationMatrix() encountered an unknown order: "+t)}return this._order=t,!0===n&&this._onChangeCallback(),this}setFromQuaternion(e,t,n){return eU.makeRotationFromQuaternion(e),this.setFromRotationMatrix(eU,t,n)}setFromVector3(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this._order;return this.set(e.x,e.y,e.z,t)}reorder(e){return eN.setFromEuler(this),this.setFromQuaternion(eN,e)}equals(e){return e._x===this._x&&e._y===this._y&&e._z===this._z&&e._order===this._order}fromArray(e){return this._x=e[0],this._y=e[1],this._z=e[2],void 0!==e[3]&&(this._order=e[3]),this._onChangeCallback(),this}toArray(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return e[t]=this._x,e[t+1]=this._y,e[t+2]=this._z,e[t+3]=this._order,e}_onChange(e){return this._onChangeCallback=e,this}_onChangeCallback(){}*[Symbol.iterator](){yield this._x,yield this._y,yield this._z,yield this._order}constructor(e=0,t=0,n=0,i=eD.DEFAULT_ORDER){this.isEuler=!0,this._x=e,this._y=t,this._z=n,this._order=i}}eD.DEFAULT_ORDER="XYZ";class eI{set(e){this.mask=1<<e>>>0}enable(e){this.mask|=1<<e}enableAll(){this.mask=-1}toggle(e){this.mask^=1<<e}disable(e){this.mask&=~(1<<e)}disableAll(){this.mask=0}test(e){return(this.mask&e.mask)!=0}isEnabled(e){return(this.mask&1<<e)!=0}constructor(){this.mask=1}}let eO=0,eF=new Y,ez=new q,eB=new eT,eH=new Y,eV=new Y,eG=new Y,ek=new q,eW=new Y(1,0,0),eX=new Y(0,1,0),ej=new Y(0,0,1),eq={type:"added"},eY={type:"removed"};class eJ extends d{onBeforeRender(){}onAfterRender(){}applyMatrix4(e){this.matrixAutoUpdate&&this.updateMatrix(),this.matrix.premultiply(e),this.matrix.decompose(this.position,this.quaternion,this.scale)}applyQuaternion(e){return this.quaternion.premultiply(e),this}setRotationFromAxisAngle(e,t){this.quaternion.setFromAxisAngle(e,t)}setRotationFromEuler(e){this.quaternion.setFromEuler(e,!0)}setRotationFromMatrix(e){this.quaternion.setFromRotationMatrix(e)}setRotationFromQuaternion(e){this.quaternion.copy(e)}rotateOnAxis(e,t){return ez.setFromAxisAngle(e,t),this.quaternion.multiply(ez),this}rotateOnWorldAxis(e,t){return ez.setFromAxisAngle(e,t),this.quaternion.premultiply(ez),this}rotateX(e){return this.rotateOnAxis(eW,e)}rotateY(e){return this.rotateOnAxis(eX,e)}rotateZ(e){return this.rotateOnAxis(ej,e)}translateOnAxis(e,t){return eF.copy(e).applyQuaternion(this.quaternion),this.position.add(eF.multiplyScalar(t)),this}translateX(e){return this.translateOnAxis(eW,e)}translateY(e){return this.translateOnAxis(eX,e)}translateZ(e){return this.translateOnAxis(ej,e)}localToWorld(e){return this.updateWorldMatrix(!0,!1),e.applyMatrix4(this.matrixWorld)}worldToLocal(e){return this.updateWorldMatrix(!0,!1),e.applyMatrix4(eB.copy(this.matrixWorld).invert())}lookAt(e,t,n){e.isVector3?eH.copy(e):eH.set(e,t,n);let i=this.parent;this.updateWorldMatrix(!0,!1),eV.setFromMatrixPosition(this.matrixWorld),this.isCamera||this.isLight?eB.lookAt(eV,eH,this.up):eB.lookAt(eH,eV,this.up),this.quaternion.setFromRotationMatrix(eB),i&&(eB.extractRotation(i.matrixWorld),ez.setFromRotationMatrix(eB),this.quaternion.premultiply(ez.invert()))}add(e){if(arguments.length>1){for(let e=0;e<arguments.length;e++)this.add(arguments[e]);return this}return e===this?console.error("THREE.Object3D.add: object can't be added as a child of itself.",e):e&&e.isObject3D?(null!==e.parent&&e.parent.remove(e),e.parent=this,this.children.push(e),e.dispatchEvent(eq)):console.error("THREE.Object3D.add: object not an instance of THREE.Object3D.",e),this}remove(e){if(arguments.length>1){for(let e=0;e<arguments.length;e++)this.remove(arguments[e]);return this}let t=this.children.indexOf(e);return -1!==t&&(e.parent=null,this.children.splice(t,1),e.dispatchEvent(eY)),this}removeFromParent(){let e=this.parent;return null!==e&&e.remove(this),this}clear(){return this.remove(...this.children)}attach(e){return this.updateWorldMatrix(!0,!1),eB.copy(this.matrixWorld).invert(),null!==e.parent&&(e.parent.updateWorldMatrix(!0,!1),eB.multiply(e.parent.matrixWorld)),e.applyMatrix4(eB),this.add(e),e.updateWorldMatrix(!1,!0),this}getObjectById(e){return this.getObjectByProperty("id",e)}getObjectByName(e){return this.getObjectByProperty("name",e)}getObjectByProperty(e,t){if(this[e]===t)return this;for(let n=0,i=this.children.length;n<i;n++){let i=this.children[n].getObjectByProperty(e,t);if(void 0!==i)return i}}getObjectsByProperty(e,t){let n=[];this[e]===t&&n.push(this);for(let i=0,r=this.children.length;i<r;i++){let r=this.children[i].getObjectsByProperty(e,t);r.length>0&&(n=n.concat(r))}return n}getWorldPosition(e){return this.updateWorldMatrix(!0,!1),e.setFromMatrixPosition(this.matrixWorld)}getWorldQuaternion(e){return this.updateWorldMatrix(!0,!1),this.matrixWorld.decompose(eV,e,eG),e}getWorldScale(e){return this.updateWorldMatrix(!0,!1),this.matrixWorld.decompose(eV,ek,e),e}getWorldDirection(e){this.updateWorldMatrix(!0,!1);let t=this.matrixWorld.elements;return e.set(t[8],t[9],t[10]).normalize()}raycast(){}traverse(e){e(this);let t=this.children;for(let n=0,i=t.length;n<i;n++)t[n].traverse(e)}traverseVisible(e){if(!1===this.visible)return;e(this);let t=this.children;for(let n=0,i=t.length;n<i;n++)t[n].traverseVisible(e)}traverseAncestors(e){let t=this.parent;null!==t&&(e(t),t.traverseAncestors(e))}updateMatrix(){this.matrix.compose(this.position,this.quaternion,this.scale),this.matrixWorldNeedsUpdate=!0}updateMatrixWorld(e){this.matrixAutoUpdate&&this.updateMatrix(),(this.matrixWorldNeedsUpdate||e)&&(null===this.parent?this.matrixWorld.copy(this.matrix):this.matrixWorld.multiplyMatrices(this.parent.matrixWorld,this.matrix),this.matrixWorldNeedsUpdate=!1,e=!0);let t=this.children;for(let n=0,i=t.length;n<i;n++){let i=t[n];(!0===i.matrixWorldAutoUpdate||!0===e)&&i.updateMatrixWorld(e)}}updateWorldMatrix(e,t){let n=this.parent;if(!0===e&&null!==n&&!0===n.matrixWorldAutoUpdate&&n.updateWorldMatrix(!0,!1),this.matrixAutoUpdate&&this.updateMatrix(),null===this.parent?this.matrixWorld.copy(this.matrix):this.matrixWorld.multiplyMatrices(this.parent.matrixWorld,this.matrix),!0===t){let e=this.children;for(let t=0,n=e.length;t<n;t++){let n=e[t];!0===n.matrixWorldAutoUpdate&&n.updateWorldMatrix(!1,!0)}}}toJSON(e){let t=void 0===e||"string"==typeof e,n={};t&&(e={geometries:{},materials:{},textures:{},images:{},shapes:{},skeletons:{},animations:{},nodes:{}},n.metadata={version:4.6,type:"Object",generator:"Object3D.toJSON"});let i={};function r(t,n){return void 0===t[n.uuid]&&(t[n.uuid]=n.toJSON(e)),n.uuid}if(i.uuid=this.uuid,i.type=this.type,""!==this.name&&(i.name=this.name),!0===this.castShadow&&(i.castShadow=!0),!0===this.receiveShadow&&(i.receiveShadow=!0),!1===this.visible&&(i.visible=!1),!1===this.frustumCulled&&(i.frustumCulled=!1),0!==this.renderOrder&&(i.renderOrder=this.renderOrder),Object.keys(this.userData).length>0&&(i.userData=this.userData),i.layers=this.layers.mask,i.matrix=this.matrix.toArray(),i.up=this.up.toArray(),!1===this.matrixAutoUpdate&&(i.matrixAutoUpdate=!1),this.isInstancedMesh&&(i.type="InstancedMesh",i.count=this.count,i.instanceMatrix=this.instanceMatrix.toJSON(),null!==this.instanceColor&&(i.instanceColor=this.instanceColor.toJSON())),this.isScene)this.background&&(this.background.isColor?i.background=this.background.toJSON():this.background.isTexture&&(i.background=this.background.toJSON(e).uuid)),this.environment&&this.environment.isTexture&&!0!==this.environment.isRenderTargetTexture&&(i.environment=this.environment.toJSON(e).uuid);else if(this.isMesh||this.isLine||this.isPoints){i.geometry=r(e.geometries,this.geometry);let t=this.geometry.parameters;if(void 0!==t&&void 0!==t.shapes){let n=t.shapes;if(Array.isArray(n))for(let t=0,i=n.length;t<i;t++){let i=n[t];r(e.shapes,i)}else r(e.shapes,n)}}if(this.isSkinnedMesh&&(i.bindMode=this.bindMode,i.bindMatrix=this.bindMatrix.toArray(),void 0!==this.skeleton&&(r(e.skeletons,this.skeleton),i.skeleton=this.skeleton.uuid)),void 0!==this.material)if(Array.isArray(this.material)){let t=[];for(let n=0,i=this.material.length;n<i;n++)t.push(r(e.materials,this.material[n]));i.material=t}else i.material=r(e.materials,this.material);if(this.children.length>0){i.children=[];for(let t=0;t<this.children.length;t++)i.children.push(this.children[t].toJSON(e).object)}if(this.animations.length>0){i.animations=[];for(let t=0;t<this.animations.length;t++){let n=this.animations[t];i.animations.push(r(e.animations,n))}}if(t){let t=a(e.geometries),i=a(e.materials),r=a(e.textures),s=a(e.images),o=a(e.shapes),l=a(e.skeletons),h=a(e.animations),c=a(e.nodes);t.length>0&&(n.geometries=t),i.length>0&&(n.materials=i),r.length>0&&(n.textures=r),s.length>0&&(n.images=s),o.length>0&&(n.shapes=o),l.length>0&&(n.skeletons=l),h.length>0&&(n.animations=h),c.length>0&&(n.nodes=c)}return n.object=i,n;function a(e){let t=[];for(let n in e){let i=e[n];delete i.metadata,t.push(i)}return t}}clone(e){return new this.constructor().copy(this,e)}copy(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];if(this.name=e.name,this.up.copy(e.up),this.position.copy(e.position),this.rotation.order=e.rotation.order,this.quaternion.copy(e.quaternion),this.scale.copy(e.scale),this.matrix.copy(e.matrix),this.matrixWorld.copy(e.matrixWorld),this.matrixAutoUpdate=e.matrixAutoUpdate,this.matrixWorldNeedsUpdate=e.matrixWorldNeedsUpdate,this.matrixWorldAutoUpdate=e.matrixWorldAutoUpdate,this.layers.mask=e.layers.mask,this.visible=e.visible,this.castShadow=e.castShadow,this.receiveShadow=e.receiveShadow,this.frustumCulled=e.frustumCulled,this.renderOrder=e.renderOrder,this.animations=e.animations.slice(),this.userData=JSON.parse(JSON.stringify(e.userData)),!0===t)for(let t=0;t<e.children.length;t++){let n=e.children[t];this.add(n.clone())}return this}constructor(){super(),this.isObject3D=!0,Object.defineProperty(this,"id",{value:eO++}),this.uuid=g(),this.name="",this.type="Object3D",this.parent=null,this.children=[],this.up=eJ.DEFAULT_UP.clone();let e=new Y,t=new eD,n=new q,i=new Y(1,1,1);t._onChange(function(){n.setFromEuler(t,!1)}),n._onChange(function(){t.setFromQuaternion(n,void 0,!1)}),Object.defineProperties(this,{position:{configurable:!0,enumerable:!0,value:e},rotation:{configurable:!0,enumerable:!0,value:t},quaternion:{configurable:!0,enumerable:!0,value:n},scale:{configurable:!0,enumerable:!0,value:i},modelViewMatrix:{value:new eT},normalMatrix:{value:new E}}),this.matrix=new eT,this.matrixWorld=new eT,this.matrixAutoUpdate=eJ.DEFAULT_MATRIX_AUTO_UPDATE,this.matrixWorldNeedsUpdate=!1,this.matrixWorldAutoUpdate=eJ.DEFAULT_MATRIX_WORLD_AUTO_UPDATE,this.layers=new eI,this.visible=!0,this.castShadow=!1,this.receiveShadow=!1,this.frustumCulled=!0,this.renderOrder=0,this.animations=[],this.userData={}}}eJ.DEFAULT_UP=new Y(0,1,0),eJ.DEFAULT_MATRIX_AUTO_UPDATE=!0,eJ.DEFAULT_MATRIX_WORLD_AUTO_UPDATE=!0;let eZ=new Y,eK=new Y,eQ=new Y,e$=new Y,e0=new Y,e1=new Y,e2=new Y,e3=new Y,e4=new Y,e5=new Y,e6=!1;class e8{static getNormal(e,t,n,i){i.subVectors(n,t),eZ.subVectors(e,t),i.cross(eZ);let r=i.lengthSq();return r>0?i.multiplyScalar(1/Math.sqrt(r)):i.set(0,0,0)}static getBarycoord(e,t,n,i,r){eZ.subVectors(i,t),eK.subVectors(n,t),eQ.subVectors(e,t);let a=eZ.dot(eZ),s=eZ.dot(eK),o=eZ.dot(eQ),l=eK.dot(eK),h=eK.dot(eQ),c=a*l-s*s;if(0===c)return r.set(-2,-1,-1);let u=1/c,d=(l*o-s*h)*u,p=(a*h-s*o)*u;return r.set(1-d-p,p,d)}static containsPoint(e,t,n,i){return this.getBarycoord(e,t,n,i,e$),e$.x>=0&&e$.y>=0&&e$.x+e$.y<=1}static getUV(e,t,n,i,r,a,s,o){return!1===e6&&(console.warn("THREE.Triangle.getUV() has been renamed to THREE.Triangle.getInterpolation()."),e6=!0),this.getInterpolation(e,t,n,i,r,a,s,o)}static getInterpolation(e,t,n,i,r,a,s,o){return this.getBarycoord(e,t,n,i,e$),o.setScalar(0),o.addScaledVector(r,e$.x),o.addScaledVector(a,e$.y),o.addScaledVector(s,e$.z),o}static isFrontFacing(e,t,n,i){return eZ.subVectors(n,t),eK.subVectors(e,t),0>eZ.cross(eK).dot(i)}set(e,t,n){return this.a.copy(e),this.b.copy(t),this.c.copy(n),this}setFromPointsAndIndices(e,t,n,i){return this.a.copy(e[t]),this.b.copy(e[n]),this.c.copy(e[i]),this}setFromAttributeAndIndices(e,t,n,i){return this.a.fromBufferAttribute(e,t),this.b.fromBufferAttribute(e,n),this.c.fromBufferAttribute(e,i),this}clone(){return new this.constructor().copy(this)}copy(e){return this.a.copy(e.a),this.b.copy(e.b),this.c.copy(e.c),this}getArea(){return eZ.subVectors(this.c,this.b),eK.subVectors(this.a,this.b),.5*eZ.cross(eK).length()}getMidpoint(e){return e.addVectors(this.a,this.b).add(this.c).multiplyScalar(1/3)}getNormal(e){return e8.getNormal(this.a,this.b,this.c,e)}getPlane(e){return e.setFromCoplanarPoints(this.a,this.b,this.c)}getBarycoord(e,t){return e8.getBarycoord(e,this.a,this.b,this.c,t)}getUV(e,t,n,i,r){return!1===e6&&(console.warn("THREE.Triangle.getUV() has been renamed to THREE.Triangle.getInterpolation()."),e6=!0),e8.getInterpolation(e,this.a,this.b,this.c,t,n,i,r)}getInterpolation(e,t,n,i,r){return e8.getInterpolation(e,this.a,this.b,this.c,t,n,i,r)}containsPoint(e){return e8.containsPoint(e,this.a,this.b,this.c)}isFrontFacing(e){return e8.isFrontFacing(this.a,this.b,this.c,e)}intersectsBox(e){return e.intersectsTriangle(this)}closestPointToPoint(e,t){let n,i,r=this.a,a=this.b,s=this.c;e0.subVectors(a,r),e1.subVectors(s,r),e3.subVectors(e,r);let o=e0.dot(e3),l=e1.dot(e3);if(o<=0&&l<=0)return t.copy(r);e4.subVectors(e,a);let h=e0.dot(e4),c=e1.dot(e4);if(h>=0&&c<=h)return t.copy(a);let u=o*c-h*l;if(u<=0&&o>=0&&h<=0)return n=o/(o-h),t.copy(r).addScaledVector(e0,n);e5.subVectors(e,s);let d=e0.dot(e5),p=e1.dot(e5);if(p>=0&&d<=p)return t.copy(s);let f=d*l-o*p;if(f<=0&&l>=0&&p<=0)return i=l/(l-p),t.copy(r).addScaledVector(e1,i);let m=h*p-d*c;if(m<=0&&c-h>=0&&d-p>=0)return e2.subVectors(s,a),i=(c-h)/(c-h+(d-p)),t.copy(a).addScaledVector(e2,i);let g=1/(m+f+u);return n=f*g,i=u*g,t.copy(r).addScaledVector(e0,n).addScaledVector(e1,i)}equals(e){return e.a.equals(this.a)&&e.b.equals(this.b)&&e.c.equals(this.c)}constructor(e=new Y,t=new Y,n=new Y){this.a=e,this.b=t,this.c=n}}let e9={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32},e7={h:0,s:0,l:0},te={h:0,s:0,l:0};function tt(e,t,n){return(n<0&&(n+=1),n>1&&(n-=1),n<1/6)?e+(t-e)*6*n:n<.5?t:n<2/3?e+(t-e)*6*(2/3-n):e}class tn{set(e,t,n){return void 0===t&&void 0===n?e&&e.isColor?this.copy(e):"number"==typeof e?this.setHex(e):"string"==typeof e&&this.setStyle(e):this.setRGB(e,t,n),this}setScalar(e){return this.r=e,this.g=e,this.b=e,this}setHex(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:r;return e=Math.floor(e),this.r=(e>>16&255)/255,this.g=(e>>8&255)/255,this.b=(255&e)/255,N.toWorkingColorSpace(this,t),this}setRGB(e,t,n){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:N.workingColorSpace;return this.r=e,this.g=t,this.b=n,N.toWorkingColorSpace(this,i),this}setHSL(e,t,n){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:N.workingColorSpace;if(e=(e%1+1)%1,t=_(t,0,1),n=_(n,0,1),0===t)this.r=this.g=this.b=n;else{let i=n<=.5?n*(1+t):n+t-n*t,r=2*n-i;this.r=tt(r,i,e+1/3),this.g=tt(r,i,e),this.b=tt(r,i,e-1/3)}return N.toWorkingColorSpace(this,i),this}setStyle(e){let t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:r;function i(t){void 0!==t&&1>parseFloat(t)&&console.warn("THREE.Color: Alpha component of "+e+" will be ignored.")}if(t=/^(\w+)\(([^\)]*)\)/.exec(e)){let r,a=t[1],s=t[2];switch(a){case"rgb":case"rgba":if(r=/^\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*(?:,\s*(\d*\.?\d+)\s*)?$/.exec(s))return i(r[4]),this.setRGB(Math.min(255,parseInt(r[1],10))/255,Math.min(255,parseInt(r[2],10))/255,Math.min(255,parseInt(r[3],10))/255,n);if(r=/^\s*(\d+)\%\s*,\s*(\d+)\%\s*,\s*(\d+)\%\s*(?:,\s*(\d*\.?\d+)\s*)?$/.exec(s))return i(r[4]),this.setRGB(Math.min(100,parseInt(r[1],10))/100,Math.min(100,parseInt(r[2],10))/100,Math.min(100,parseInt(r[3],10))/100,n);break;case"hsl":case"hsla":if(r=/^\s*(\d*\.?\d+)\s*,\s*(\d*\.?\d+)\%\s*,\s*(\d*\.?\d+)\%\s*(?:,\s*(\d*\.?\d+)\s*)?$/.exec(s))return i(r[4]),this.setHSL(parseFloat(r[1])/360,parseFloat(r[2])/100,parseFloat(r[3])/100,n);break;default:console.warn("THREE.Color: Unknown color model "+e)}}else if(t=/^\#([A-Fa-f\d]+)$/.exec(e)){let i=t[1],r=i.length;if(3===r)return this.setRGB(parseInt(i.charAt(0),16)/15,parseInt(i.charAt(1),16)/15,parseInt(i.charAt(2),16)/15,n);if(6===r)return this.setHex(parseInt(i,16),n);console.warn("THREE.Color: Invalid hex color "+e)}else if(e&&e.length>0)return this.setColorName(e,n);return this}setColorName(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:r,n=e9[e.toLowerCase()];return void 0!==n?this.setHex(n,t):console.warn("THREE.Color: Unknown color "+e),this}clone(){return new this.constructor(this.r,this.g,this.b)}copy(e){return this.r=e.r,this.g=e.g,this.b=e.b,this}copySRGBToLinear(e){return this.r=D(e.r),this.g=D(e.g),this.b=D(e.b),this}copyLinearToSRGB(e){return this.r=I(e.r),this.g=I(e.g),this.b=I(e.b),this}convertSRGBToLinear(){return this.copySRGBToLinear(this),this}convertLinearToSRGB(){return this.copyLinearToSRGB(this),this}getHex(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:r;return N.fromWorkingColorSpace(ti.copy(this),e),65536*Math.round(_(255*ti.r,0,255))+256*Math.round(_(255*ti.g,0,255))+Math.round(_(255*ti.b,0,255))}getHexString(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:r;return("000000"+this.getHex(e).toString(16)).slice(-6)}getHSL(e){let t,n,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:N.workingColorSpace;N.fromWorkingColorSpace(ti.copy(this),i);let r=ti.r,a=ti.g,s=ti.b,o=Math.max(r,a,s),l=Math.min(r,a,s),h=(l+o)/2;if(l===o)t=0,n=0;else{let e=o-l;switch(n=h<=.5?e/(o+l):e/(2-o-l),o){case r:t=(a-s)/e+6*(a<s);break;case a:t=(s-r)/e+2;break;case s:t=(r-a)/e+4}t/=6}return e.h=t,e.s=n,e.l=h,e}getRGB(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:N.workingColorSpace;return N.fromWorkingColorSpace(ti.copy(this),t),e.r=ti.r,e.g=ti.g,e.b=ti.b,e}getStyle(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:r;N.fromWorkingColorSpace(ti.copy(this),e);let t=ti.r,n=ti.g,i=ti.b;return e!==r?"color(".concat(e," ").concat(t.toFixed(3)," ").concat(n.toFixed(3)," ").concat(i.toFixed(3),")"):"rgb(".concat(Math.round(255*t),",").concat(Math.round(255*n),",").concat(Math.round(255*i),")")}offsetHSL(e,t,n){return this.getHSL(e7),this.setHSL(e7.h+e,e7.s+t,e7.l+n)}add(e){return this.r+=e.r,this.g+=e.g,this.b+=e.b,this}addColors(e,t){return this.r=e.r+t.r,this.g=e.g+t.g,this.b=e.b+t.b,this}addScalar(e){return this.r+=e,this.g+=e,this.b+=e,this}sub(e){return this.r=Math.max(0,this.r-e.r),this.g=Math.max(0,this.g-e.g),this.b=Math.max(0,this.b-e.b),this}multiply(e){return this.r*=e.r,this.g*=e.g,this.b*=e.b,this}multiplyScalar(e){return this.r*=e,this.g*=e,this.b*=e,this}lerp(e,t){return this.r+=(e.r-this.r)*t,this.g+=(e.g-this.g)*t,this.b+=(e.b-this.b)*t,this}lerpColors(e,t,n){return this.r=e.r+(t.r-e.r)*n,this.g=e.g+(t.g-e.g)*n,this.b=e.b+(t.b-e.b)*n,this}lerpHSL(e,t){var n,i,r;this.getHSL(e7),e.getHSL(te);let a=(n=e7.h,(1-t)*n+t*te.h),s=(i=e7.s,(1-t)*i+t*te.s),o=(r=e7.l,(1-t)*r+t*te.l);return this.setHSL(a,s,o),this}setFromVector3(e){return this.r=e.x,this.g=e.y,this.b=e.z,this}applyMatrix3(e){let t=this.r,n=this.g,i=this.b,r=e.elements;return this.r=r[0]*t+r[3]*n+r[6]*i,this.g=r[1]*t+r[4]*n+r[7]*i,this.b=r[2]*t+r[5]*n+r[8]*i,this}equals(e){return e.r===this.r&&e.g===this.g&&e.b===this.b}fromArray(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return this.r=e[t],this.g=e[t+1],this.b=e[t+2],this}toArray(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return e[t]=this.r,e[t+1]=this.g,e[t+2]=this.b,e}fromBufferAttribute(e,t){return this.r=e.getX(t),this.g=e.getY(t),this.b=e.getZ(t),this}toJSON(){return this.getHex()}*[Symbol.iterator](){yield this.r,yield this.g,yield this.b}constructor(e,t,n){return this.isColor=!0,this.r=1,this.g=1,this.b=1,this.set(e,t,n)}}let ti=new tn;tn.NAMES=e9;let tr=0;class ta extends d{get alphaTest(){return this._alphaTest}set alphaTest(e){this._alphaTest>0!=e>0&&this.version++,this._alphaTest=e}onBuild(){}onBeforeRender(){}onBeforeCompile(){}customProgramCacheKey(){return this.onBeforeCompile.toString()}setValues(e){if(void 0!==e)for(let t in e){let n=e[t];if(void 0===n){console.warn("THREE.Material: parameter '".concat(t,"' has value of undefined."));continue}let i=this[t];if(void 0===i){console.warn("THREE.Material: '".concat(t,"' is not a property of THREE.").concat(this.type,"."));continue}i&&i.isColor?i.set(n):i&&i.isVector3&&n&&n.isVector3?i.copy(n):this[t]=n}}toJSON(e){let t=void 0===e||"string"==typeof e;t&&(e={textures:{},images:{}});let n={metadata:{version:4.6,type:"Material",generator:"Material.toJSON"}};function i(e){let t=[];for(let n in e){let i=e[n];delete i.metadata,t.push(i)}return t}if(n.uuid=this.uuid,n.type=this.type,""!==this.name&&(n.name=this.name),this.color&&this.color.isColor&&(n.color=this.color.getHex()),void 0!==this.roughness&&(n.roughness=this.roughness),void 0!==this.metalness&&(n.metalness=this.metalness),void 0!==this.sheen&&(n.sheen=this.sheen),this.sheenColor&&this.sheenColor.isColor&&(n.sheenColor=this.sheenColor.getHex()),void 0!==this.sheenRoughness&&(n.sheenRoughness=this.sheenRoughness),this.emissive&&this.emissive.isColor&&(n.emissive=this.emissive.getHex()),this.emissiveIntensity&&1!==this.emissiveIntensity&&(n.emissiveIntensity=this.emissiveIntensity),this.specular&&this.specular.isColor&&(n.specular=this.specular.getHex()),void 0!==this.specularIntensity&&(n.specularIntensity=this.specularIntensity),this.specularColor&&this.specularColor.isColor&&(n.specularColor=this.specularColor.getHex()),void 0!==this.shininess&&(n.shininess=this.shininess),void 0!==this.clearcoat&&(n.clearcoat=this.clearcoat),void 0!==this.clearcoatRoughness&&(n.clearcoatRoughness=this.clearcoatRoughness),this.clearcoatMap&&this.clearcoatMap.isTexture&&(n.clearcoatMap=this.clearcoatMap.toJSON(e).uuid),this.clearcoatRoughnessMap&&this.clearcoatRoughnessMap.isTexture&&(n.clearcoatRoughnessMap=this.clearcoatRoughnessMap.toJSON(e).uuid),this.clearcoatNormalMap&&this.clearcoatNormalMap.isTexture&&(n.clearcoatNormalMap=this.clearcoatNormalMap.toJSON(e).uuid,n.clearcoatNormalScale=this.clearcoatNormalScale.toArray()),void 0!==this.iridescence&&(n.iridescence=this.iridescence),void 0!==this.iridescenceIOR&&(n.iridescenceIOR=this.iridescenceIOR),void 0!==this.iridescenceThicknessRange&&(n.iridescenceThicknessRange=this.iridescenceThicknessRange),this.iridescenceMap&&this.iridescenceMap.isTexture&&(n.iridescenceMap=this.iridescenceMap.toJSON(e).uuid),this.iridescenceThicknessMap&&this.iridescenceThicknessMap.isTexture&&(n.iridescenceThicknessMap=this.iridescenceThicknessMap.toJSON(e).uuid),void 0!==this.anisotropy&&(n.anisotropy=this.anisotropy),void 0!==this.anisotropyRotation&&(n.anisotropyRotation=this.anisotropyRotation),this.anisotropyMap&&this.anisotropyMap.isTexture&&(n.anisotropyMap=this.anisotropyMap.toJSON(e).uuid),this.map&&this.map.isTexture&&(n.map=this.map.toJSON(e).uuid),this.matcap&&this.matcap.isTexture&&(n.matcap=this.matcap.toJSON(e).uuid),this.alphaMap&&this.alphaMap.isTexture&&(n.alphaMap=this.alphaMap.toJSON(e).uuid),this.lightMap&&this.lightMap.isTexture&&(n.lightMap=this.lightMap.toJSON(e).uuid,n.lightMapIntensity=this.lightMapIntensity),this.aoMap&&this.aoMap.isTexture&&(n.aoMap=this.aoMap.toJSON(e).uuid,n.aoMapIntensity=this.aoMapIntensity),this.bumpMap&&this.bumpMap.isTexture&&(n.bumpMap=this.bumpMap.toJSON(e).uuid,n.bumpScale=this.bumpScale),this.normalMap&&this.normalMap.isTexture&&(n.normalMap=this.normalMap.toJSON(e).uuid,n.normalMapType=this.normalMapType,n.normalScale=this.normalScale.toArray()),this.displacementMap&&this.displacementMap.isTexture&&(n.displacementMap=this.displacementMap.toJSON(e).uuid,n.displacementScale=this.displacementScale,n.displacementBias=this.displacementBias),this.roughnessMap&&this.roughnessMap.isTexture&&(n.roughnessMap=this.roughnessMap.toJSON(e).uuid),this.metalnessMap&&this.metalnessMap.isTexture&&(n.metalnessMap=this.metalnessMap.toJSON(e).uuid),this.emissiveMap&&this.emissiveMap.isTexture&&(n.emissiveMap=this.emissiveMap.toJSON(e).uuid),this.specularMap&&this.specularMap.isTexture&&(n.specularMap=this.specularMap.toJSON(e).uuid),this.specularIntensityMap&&this.specularIntensityMap.isTexture&&(n.specularIntensityMap=this.specularIntensityMap.toJSON(e).uuid),this.specularColorMap&&this.specularColorMap.isTexture&&(n.specularColorMap=this.specularColorMap.toJSON(e).uuid),this.envMap&&this.envMap.isTexture&&(n.envMap=this.envMap.toJSON(e).uuid,void 0!==this.combine&&(n.combine=this.combine)),void 0!==this.envMapIntensity&&(n.envMapIntensity=this.envMapIntensity),void 0!==this.reflectivity&&(n.reflectivity=this.reflectivity),void 0!==this.refractionRatio&&(n.refractionRatio=this.refractionRatio),this.gradientMap&&this.gradientMap.isTexture&&(n.gradientMap=this.gradientMap.toJSON(e).uuid),void 0!==this.transmission&&(n.transmission=this.transmission),this.transmissionMap&&this.transmissionMap.isTexture&&(n.transmissionMap=this.transmissionMap.toJSON(e).uuid),void 0!==this.thickness&&(n.thickness=this.thickness),this.thicknessMap&&this.thicknessMap.isTexture&&(n.thicknessMap=this.thicknessMap.toJSON(e).uuid),void 0!==this.attenuationDistance&&this.attenuationDistance!==1/0&&(n.attenuationDistance=this.attenuationDistance),void 0!==this.attenuationColor&&(n.attenuationColor=this.attenuationColor.getHex()),void 0!==this.size&&(n.size=this.size),null!==this.shadowSide&&(n.shadowSide=this.shadowSide),void 0!==this.sizeAttenuation&&(n.sizeAttenuation=this.sizeAttenuation),1!==this.blending&&(n.blending=this.blending),0!==this.side&&(n.side=this.side),!0===this.vertexColors&&(n.vertexColors=!0),this.opacity<1&&(n.opacity=this.opacity),!0===this.transparent&&(n.transparent=!0),204!==this.blendSrc&&(n.blendSrc=this.blendSrc),205!==this.blendDst&&(n.blendDst=this.blendDst),100!==this.blendEquation&&(n.blendEquation=this.blendEquation),null!==this.blendSrcAlpha&&(n.blendSrcAlpha=this.blendSrcAlpha),null!==this.blendDstAlpha&&(n.blendDstAlpha=this.blendDstAlpha),null!==this.blendEquationAlpha&&(n.blendEquationAlpha=this.blendEquationAlpha),this.blendColor&&this.blendColor.isColor&&(n.blendColor=this.blendColor.getHex()),0!==this.blendAlpha&&(n.blendAlpha=this.blendAlpha),3!==this.depthFunc&&(n.depthFunc=this.depthFunc),!1===this.depthTest&&(n.depthTest=this.depthTest),!1===this.depthWrite&&(n.depthWrite=this.depthWrite),!1===this.colorWrite&&(n.colorWrite=this.colorWrite),255!==this.stencilWriteMask&&(n.stencilWriteMask=this.stencilWriteMask),519!==this.stencilFunc&&(n.stencilFunc=this.stencilFunc),0!==this.stencilRef&&(n.stencilRef=this.stencilRef),255!==this.stencilFuncMask&&(n.stencilFuncMask=this.stencilFuncMask),7680!==this.stencilFail&&(n.stencilFail=this.stencilFail),7680!==this.stencilZFail&&(n.stencilZFail=this.stencilZFail),7680!==this.stencilZPass&&(n.stencilZPass=this.stencilZPass),!0===this.stencilWrite&&(n.stencilWrite=this.stencilWrite),void 0!==this.rotation&&0!==this.rotation&&(n.rotation=this.rotation),!0===this.polygonOffset&&(n.polygonOffset=!0),0!==this.polygonOffsetFactor&&(n.polygonOffsetFactor=this.polygonOffsetFactor),0!==this.polygonOffsetUnits&&(n.polygonOffsetUnits=this.polygonOffsetUnits),void 0!==this.linewidth&&1!==this.linewidth&&(n.linewidth=this.linewidth),void 0!==this.dashSize&&(n.dashSize=this.dashSize),void 0!==this.gapSize&&(n.gapSize=this.gapSize),void 0!==this.scale&&(n.scale=this.scale),!0===this.dithering&&(n.dithering=!0),this.alphaTest>0&&(n.alphaTest=this.alphaTest),!0===this.alphaHash&&(n.alphaHash=!0),!0===this.alphaToCoverage&&(n.alphaToCoverage=!0),!0===this.premultipliedAlpha&&(n.premultipliedAlpha=!0),!0===this.forceSinglePass&&(n.forceSinglePass=!0),!0===this.wireframe&&(n.wireframe=!0),this.wireframeLinewidth>1&&(n.wireframeLinewidth=this.wireframeLinewidth),"round"!==this.wireframeLinecap&&(n.wireframeLinecap=this.wireframeLinecap),"round"!==this.wireframeLinejoin&&(n.wireframeLinejoin=this.wireframeLinejoin),!0===this.flatShading&&(n.flatShading=!0),!1===this.visible&&(n.visible=!1),!1===this.toneMapped&&(n.toneMapped=!1),!1===this.fog&&(n.fog=!1),Object.keys(this.userData).length>0&&(n.userData=this.userData),t){let t=i(e.textures),r=i(e.images);t.length>0&&(n.textures=t),r.length>0&&(n.images=r)}return n}clone(){return new this.constructor().copy(this)}copy(e){this.name=e.name,this.blending=e.blending,this.side=e.side,this.vertexColors=e.vertexColors,this.opacity=e.opacity,this.transparent=e.transparent,this.blendSrc=e.blendSrc,this.blendDst=e.blendDst,this.blendEquation=e.blendEquation,this.blendSrcAlpha=e.blendSrcAlpha,this.blendDstAlpha=e.blendDstAlpha,this.blendEquationAlpha=e.blendEquationAlpha,this.blendColor.copy(e.blendColor),this.blendAlpha=e.blendAlpha,this.depthFunc=e.depthFunc,this.depthTest=e.depthTest,this.depthWrite=e.depthWrite,this.stencilWriteMask=e.stencilWriteMask,this.stencilFunc=e.stencilFunc,this.stencilRef=e.stencilRef,this.stencilFuncMask=e.stencilFuncMask,this.stencilFail=e.stencilFail,this.stencilZFail=e.stencilZFail,this.stencilZPass=e.stencilZPass,this.stencilWrite=e.stencilWrite;let t=e.clippingPlanes,n=null;if(null!==t){let e=t.length;n=Array(e);for(let i=0;i!==e;++i)n[i]=t[i].clone()}return this.clippingPlanes=n,this.clipIntersection=e.clipIntersection,this.clipShadows=e.clipShadows,this.shadowSide=e.shadowSide,this.colorWrite=e.colorWrite,this.precision=e.precision,this.polygonOffset=e.polygonOffset,this.polygonOffsetFactor=e.polygonOffsetFactor,this.polygonOffsetUnits=e.polygonOffsetUnits,this.dithering=e.dithering,this.alphaTest=e.alphaTest,this.alphaHash=e.alphaHash,this.alphaToCoverage=e.alphaToCoverage,this.premultipliedAlpha=e.premultipliedAlpha,this.forceSinglePass=e.forceSinglePass,this.visible=e.visible,this.toneMapped=e.toneMapped,this.userData=JSON.parse(JSON.stringify(e.userData)),this}dispose(){this.dispatchEvent({type:"dispose"})}set needsUpdate(e){!0===e&&this.version++}constructor(){super(),this.isMaterial=!0,Object.defineProperty(this,"id",{value:tr++}),this.uuid=g(),this.name="",this.type="Material",this.blending=1,this.side=0,this.vertexColors=!1,this.opacity=1,this.transparent=!1,this.alphaHash=!1,this.blendSrc=204,this.blendDst=205,this.blendEquation=100,this.blendSrcAlpha=null,this.blendDstAlpha=null,this.blendEquationAlpha=null,this.blendColor=new tn(0,0,0),this.blendAlpha=0,this.depthFunc=3,this.depthTest=!0,this.depthWrite=!0,this.stencilWriteMask=255,this.stencilFunc=519,this.stencilRef=0,this.stencilFuncMask=255,this.stencilFail=7680,this.stencilZFail=7680,this.stencilZPass=7680,this.stencilWrite=!1,this.clippingPlanes=null,this.clipIntersection=!1,this.clipShadows=!1,this.shadowSide=null,this.colorWrite=!0,this.precision=null,this.polygonOffset=!1,this.polygonOffsetFactor=0,this.polygonOffsetUnits=0,this.dithering=!1,this.alphaToCoverage=!1,this.premultipliedAlpha=!1,this.forceSinglePass=!1,this.visible=!0,this.toneMapped=!0,this.userData={},this.version=0,this._alphaTest=0}}class ts extends ta{copy(e){return super.copy(e),this.color.copy(e.color),this.map=e.map,this.lightMap=e.lightMap,this.lightMapIntensity=e.lightMapIntensity,this.aoMap=e.aoMap,this.aoMapIntensity=e.aoMapIntensity,this.specularMap=e.specularMap,this.alphaMap=e.alphaMap,this.envMap=e.envMap,this.combine=e.combine,this.reflectivity=e.reflectivity,this.refractionRatio=e.refractionRatio,this.wireframe=e.wireframe,this.wireframeLinewidth=e.wireframeLinewidth,this.wireframeLinecap=e.wireframeLinecap,this.wireframeLinejoin=e.wireframeLinejoin,this.fog=e.fog,this}constructor(e){super(),this.isMeshBasicMaterial=!0,this.type="MeshBasicMaterial",this.color=new tn(0xffffff),this.map=null,this.lightMap=null,this.lightMapIntensity=1,this.aoMap=null,this.aoMapIntensity=1,this.specularMap=null,this.alphaMap=null,this.envMap=null,this.combine=0,this.reflectivity=1,this.refractionRatio=.98,this.wireframe=!1,this.wireframeLinewidth=1,this.wireframeLinecap="round",this.wireframeLinejoin="round",this.fog=!0,this.setValues(e)}}let to=new Y,tl=new S;class th{onUploadCallback(){}set needsUpdate(e){!0===e&&this.version++}setUsage(e){return this.usage=e,this}copy(e){return this.name=e.name,this.array=new e.array.constructor(e.array),this.itemSize=e.itemSize,this.count=e.count,this.normalized=e.normalized,this.usage=e.usage,this.gpuType=e.gpuType,this}copyAt(e,t,n){e*=this.itemSize,n*=t.itemSize;for(let i=0,r=this.itemSize;i<r;i++)this.array[e+i]=t.array[n+i];return this}copyArray(e){return this.array.set(e),this}applyMatrix3(e){if(2===this.itemSize)for(let t=0,n=this.count;t<n;t++)tl.fromBufferAttribute(this,t),tl.applyMatrix3(e),this.setXY(t,tl.x,tl.y);else if(3===this.itemSize)for(let t=0,n=this.count;t<n;t++)to.fromBufferAttribute(this,t),to.applyMatrix3(e),this.setXYZ(t,to.x,to.y,to.z);return this}applyMatrix4(e){for(let t=0,n=this.count;t<n;t++)to.fromBufferAttribute(this,t),to.applyMatrix4(e),this.setXYZ(t,to.x,to.y,to.z);return this}applyNormalMatrix(e){for(let t=0,n=this.count;t<n;t++)to.fromBufferAttribute(this,t),to.applyNormalMatrix(e),this.setXYZ(t,to.x,to.y,to.z);return this}transformDirection(e){for(let t=0,n=this.count;t<n;t++)to.fromBufferAttribute(this,t),to.transformDirection(e),this.setXYZ(t,to.x,to.y,to.z);return this}set(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return this.array.set(e,t),this}getComponent(e,t){let n=this.array[e*this.itemSize+t];return this.normalized&&(n=y(n,this.array)),n}setComponent(e,t,n){return this.normalized&&(n=M(n,this.array)),this.array[e*this.itemSize+t]=n,this}getX(e){let t=this.array[e*this.itemSize];return this.normalized&&(t=y(t,this.array)),t}setX(e,t){return this.normalized&&(t=M(t,this.array)),this.array[e*this.itemSize]=t,this}getY(e){let t=this.array[e*this.itemSize+1];return this.normalized&&(t=y(t,this.array)),t}setY(e,t){return this.normalized&&(t=M(t,this.array)),this.array[e*this.itemSize+1]=t,this}getZ(e){let t=this.array[e*this.itemSize+2];return this.normalized&&(t=y(t,this.array)),t}setZ(e,t){return this.normalized&&(t=M(t,this.array)),this.array[e*this.itemSize+2]=t,this}getW(e){let t=this.array[e*this.itemSize+3];return this.normalized&&(t=y(t,this.array)),t}setW(e,t){return this.normalized&&(t=M(t,this.array)),this.array[e*this.itemSize+3]=t,this}setXY(e,t,n){return e*=this.itemSize,this.normalized&&(t=M(t,this.array),n=M(n,this.array)),this.array[e+0]=t,this.array[e+1]=n,this}setXYZ(e,t,n,i){return e*=this.itemSize,this.normalized&&(t=M(t,this.array),n=M(n,this.array),i=M(i,this.array)),this.array[e+0]=t,this.array[e+1]=n,this.array[e+2]=i,this}setXYZW(e,t,n,i,r){return e*=this.itemSize,this.normalized&&(t=M(t,this.array),n=M(n,this.array),i=M(i,this.array),r=M(r,this.array)),this.array[e+0]=t,this.array[e+1]=n,this.array[e+2]=i,this.array[e+3]=r,this}onUpload(e){return this.onUploadCallback=e,this}clone(){return new this.constructor(this.array,this.itemSize).copy(this)}toJSON(){let e={itemSize:this.itemSize,type:this.array.constructor.name,array:Array.from(this.array),normalized:this.normalized};return""!==this.name&&(e.name=this.name),35044!==this.usage&&(e.usage=this.usage),(0!==this.updateRange.offset||-1!==this.updateRange.count)&&(e.updateRange=this.updateRange),e}constructor(e,t,n=!1){if(Array.isArray(e))throw TypeError("THREE.BufferAttribute: array should be a Typed Array.");this.isBufferAttribute=!0,this.name="",this.array=e,this.itemSize=t,this.count=void 0!==e?e.length/t:0,this.normalized=n,this.usage=35044,this.updateRange={offset:0,count:-1},this.gpuType=1015,this.version=0}}class tc extends th{constructor(e,t,n){super(new Uint16Array(e),t,n)}}class tu extends th{constructor(e,t,n){super(new Uint32Array(e),t,n)}}class td extends th{constructor(e,t,n){super(new Float32Array(e),t,n)}}let tp=0,tf=new eT,tm=new eJ,tg=new Y,t_=new K,tv=new K,tx=new Y;class ty extends d{getIndex(){return this.index}setIndex(e){return Array.isArray(e)?this.index=new(b(e)?tu:tc)(e,1):this.index=e,this}getAttribute(e){return this.attributes[e]}setAttribute(e,t){return this.attributes[e]=t,this}deleteAttribute(e){return delete this.attributes[e],this}hasAttribute(e){return void 0!==this.attributes[e]}addGroup(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;this.groups.push({start:e,count:t,materialIndex:n})}clearGroups(){this.groups=[]}setDrawRange(e,t){this.drawRange.start=e,this.drawRange.count=t}applyMatrix4(e){let t=this.attributes.position;void 0!==t&&(t.applyMatrix4(e),t.needsUpdate=!0);let n=this.attributes.normal;if(void 0!==n){let t=new E().getNormalMatrix(e);n.applyNormalMatrix(t),n.needsUpdate=!0}let i=this.attributes.tangent;return void 0!==i&&(i.transformDirection(e),i.needsUpdate=!0),null!==this.boundingBox&&this.computeBoundingBox(),null!==this.boundingSphere&&this.computeBoundingSphere(),this}applyQuaternion(e){return tf.makeRotationFromQuaternion(e),this.applyMatrix4(tf),this}rotateX(e){return tf.makeRotationX(e),this.applyMatrix4(tf),this}rotateY(e){return tf.makeRotationY(e),this.applyMatrix4(tf),this}rotateZ(e){return tf.makeRotationZ(e),this.applyMatrix4(tf),this}translate(e,t,n){return tf.makeTranslation(e,t,n),this.applyMatrix4(tf),this}scale(e,t,n){return tf.makeScale(e,t,n),this.applyMatrix4(tf),this}lookAt(e){return tm.lookAt(e),tm.updateMatrix(),this.applyMatrix4(tm.matrix),this}center(){return this.computeBoundingBox(),this.boundingBox.getCenter(tg).negate(),this.translate(tg.x,tg.y,tg.z),this}setFromPoints(e){let t=[];for(let n=0,i=e.length;n<i;n++){let i=e[n];t.push(i.x,i.y,i.z||0)}return this.setAttribute("position",new td(t,3)),this}computeBoundingBox(){null===this.boundingBox&&(this.boundingBox=new K);let e=this.attributes.position,t=this.morphAttributes.position;if(e&&e.isGLBufferAttribute){console.error('THREE.BufferGeometry.computeBoundingBox(): GLBufferAttribute requires a manual bounding box. Alternatively set "mesh.frustumCulled" to "false".',this),this.boundingBox.set(new Y(-1/0,-1/0,-1/0),new Y(Infinity,Infinity,Infinity));return}if(void 0!==e){if(this.boundingBox.setFromBufferAttribute(e),t)for(let e=0,n=t.length;e<n;e++){let n=t[e];t_.setFromBufferAttribute(n),this.morphTargetsRelative?(tx.addVectors(this.boundingBox.min,t_.min),this.boundingBox.expandByPoint(tx),tx.addVectors(this.boundingBox.max,t_.max),this.boundingBox.expandByPoint(tx)):(this.boundingBox.expandByPoint(t_.min),this.boundingBox.expandByPoint(t_.max))}}else this.boundingBox.makeEmpty();(isNaN(this.boundingBox.min.x)||isNaN(this.boundingBox.min.y)||isNaN(this.boundingBox.min.z))&&console.error('THREE.BufferGeometry.computeBoundingBox(): Computed min/max have NaN values. The "position" attribute is likely to have NaN values.',this)}computeBoundingSphere(){null===this.boundingSphere&&(this.boundingSphere=new em);let e=this.attributes.position,t=this.morphAttributes.position;if(e&&e.isGLBufferAttribute){console.error('THREE.BufferGeometry.computeBoundingSphere(): GLBufferAttribute requires a manual bounding sphere. Alternatively set "mesh.frustumCulled" to "false".',this),this.boundingSphere.set(new Y,1/0);return}if(e){let n=this.boundingSphere.center;if(t_.setFromBufferAttribute(e),t)for(let e=0,n=t.length;e<n;e++){let n=t[e];tv.setFromBufferAttribute(n),this.morphTargetsRelative?(tx.addVectors(t_.min,tv.min),t_.expandByPoint(tx),tx.addVectors(t_.max,tv.max),t_.expandByPoint(tx)):(t_.expandByPoint(tv.min),t_.expandByPoint(tv.max))}t_.getCenter(n);let i=0;for(let t=0,r=e.count;t<r;t++)tx.fromBufferAttribute(e,t),i=Math.max(i,n.distanceToSquared(tx));if(t)for(let r=0,a=t.length;r<a;r++){let a=t[r],s=this.morphTargetsRelative;for(let t=0,r=a.count;t<r;t++)tx.fromBufferAttribute(a,t),s&&(tg.fromBufferAttribute(e,t),tx.add(tg)),i=Math.max(i,n.distanceToSquared(tx))}this.boundingSphere.radius=Math.sqrt(i),isNaN(this.boundingSphere.radius)&&console.error('THREE.BufferGeometry.computeBoundingSphere(): Computed radius is NaN. The "position" attribute is likely to have NaN values.',this)}}computeTangents(){let e=this.index,t=this.attributes;if(null===e||void 0===t.position||void 0===t.normal||void 0===t.uv)return void console.error("THREE.BufferGeometry: .computeTangents() failed. Missing required attributes (index, position, normal or uv)");let n=e.array,i=t.position.array,r=t.normal.array,a=t.uv.array,s=i.length/3;!1===this.hasAttribute("tangent")&&this.setAttribute("tangent",new th(new Float32Array(4*s),4));let o=this.getAttribute("tangent").array,l=[],h=[];for(let e=0;e<s;e++)l[e]=new Y,h[e]=new Y;let c=new Y,u=new Y,d=new Y,p=new S,f=new S,m=new S,g=new Y,_=new Y,v=this.groups;0===v.length&&(v=[{start:0,count:n.length}]);for(let e=0,t=v.length;e<t;++e){let t=v[e],r=t.start,s=t.count;for(let e=r,t=r+s;e<t;e+=3)!function(e,t,n){c.fromArray(i,3*e),u.fromArray(i,3*t),d.fromArray(i,3*n),p.fromArray(a,2*e),f.fromArray(a,2*t),m.fromArray(a,2*n),u.sub(c),d.sub(c),f.sub(p),m.sub(p);let r=1/(f.x*m.y-m.x*f.y);isFinite(r)&&(g.copy(u).multiplyScalar(m.y).addScaledVector(d,-f.y).multiplyScalar(r),_.copy(d).multiplyScalar(f.x).addScaledVector(u,-m.x).multiplyScalar(r),l[e].add(g),l[t].add(g),l[n].add(g),h[e].add(_),h[t].add(_),h[n].add(_))}(n[e+0],n[e+1],n[e+2])}let x=new Y,y=new Y,M=new Y,E=new Y;function T(e){M.fromArray(r,3*e),E.copy(M);let t=l[e];x.copy(t),x.sub(M.multiplyScalar(M.dot(t))).normalize(),y.crossVectors(E,t);let n=y.dot(h[e]);o[4*e]=x.x,o[4*e+1]=x.y,o[4*e+2]=x.z,o[4*e+3]=n<0?-1:1}for(let e=0,t=v.length;e<t;++e){let t=v[e],i=t.start,r=t.count;for(let e=i,t=i+r;e<t;e+=3)T(n[e+0]),T(n[e+1]),T(n[e+2])}}computeVertexNormals(){let e=this.index,t=this.getAttribute("position");if(void 0!==t){let n=this.getAttribute("normal");if(void 0===n)n=new th(new Float32Array(3*t.count),3),this.setAttribute("normal",n);else for(let e=0,t=n.count;e<t;e++)n.setXYZ(e,0,0,0);let i=new Y,r=new Y,a=new Y,s=new Y,o=new Y,l=new Y,h=new Y,c=new Y;if(e)for(let u=0,d=e.count;u<d;u+=3){let d=e.getX(u+0),p=e.getX(u+1),f=e.getX(u+2);i.fromBufferAttribute(t,d),r.fromBufferAttribute(t,p),a.fromBufferAttribute(t,f),h.subVectors(a,r),c.subVectors(i,r),h.cross(c),s.fromBufferAttribute(n,d),o.fromBufferAttribute(n,p),l.fromBufferAttribute(n,f),s.add(h),o.add(h),l.add(h),n.setXYZ(d,s.x,s.y,s.z),n.setXYZ(p,o.x,o.y,o.z),n.setXYZ(f,l.x,l.y,l.z)}else for(let e=0,s=t.count;e<s;e+=3)i.fromBufferAttribute(t,e+0),r.fromBufferAttribute(t,e+1),a.fromBufferAttribute(t,e+2),h.subVectors(a,r),c.subVectors(i,r),h.cross(c),n.setXYZ(e+0,h.x,h.y,h.z),n.setXYZ(e+1,h.x,h.y,h.z),n.setXYZ(e+2,h.x,h.y,h.z);this.normalizeNormals(),n.needsUpdate=!0}}normalizeNormals(){let e=this.attributes.normal;for(let t=0,n=e.count;t<n;t++)tx.fromBufferAttribute(e,t),tx.normalize(),e.setXYZ(t,tx.x,tx.y,tx.z)}toNonIndexed(){function e(e,t){let n=e.array,i=e.itemSize,r=e.normalized,a=new n.constructor(t.length*i),s=0,o=0;for(let r=0,l=t.length;r<l;r++){s=e.isInterleavedBufferAttribute?t[r]*e.data.stride+e.offset:t[r]*i;for(let e=0;e<i;e++)a[o++]=n[s++]}return new th(a,i,r)}if(null===this.index)return console.warn("THREE.BufferGeometry.toNonIndexed(): BufferGeometry is already non-indexed."),this;let t=new ty,n=this.index.array,i=this.attributes;for(let r in i){let a=e(i[r],n);t.setAttribute(r,a)}let r=this.morphAttributes;for(let i in r){let a=[],s=r[i];for(let t=0,i=s.length;t<i;t++){let i=e(s[t],n);a.push(i)}t.morphAttributes[i]=a}t.morphTargetsRelative=this.morphTargetsRelative;let a=this.groups;for(let e=0,n=a.length;e<n;e++){let n=a[e];t.addGroup(n.start,n.count,n.materialIndex)}return t}toJSON(){let e={metadata:{version:4.6,type:"BufferGeometry",generator:"BufferGeometry.toJSON"}};if(e.uuid=this.uuid,e.type=this.type,""!==this.name&&(e.name=this.name),Object.keys(this.userData).length>0&&(e.userData=this.userData),void 0!==this.parameters){let t=this.parameters;for(let n in t)void 0!==t[n]&&(e[n]=t[n]);return e}e.data={attributes:{}};let t=this.index;null!==t&&(e.data.index={type:t.array.constructor.name,array:Array.prototype.slice.call(t.array)});let n=this.attributes;for(let t in n){let i=n[t];e.data.attributes[t]=i.toJSON(e.data)}let i={},r=!1;for(let t in this.morphAttributes){let n=this.morphAttributes[t],a=[];for(let t=0,i=n.length;t<i;t++){let i=n[t];a.push(i.toJSON(e.data))}a.length>0&&(i[t]=a,r=!0)}r&&(e.data.morphAttributes=i,e.data.morphTargetsRelative=this.morphTargetsRelative);let a=this.groups;a.length>0&&(e.data.groups=JSON.parse(JSON.stringify(a)));let s=this.boundingSphere;return null!==s&&(e.data.boundingSphere={center:s.center.toArray(),radius:s.radius}),e}clone(){return new this.constructor().copy(this)}copy(e){this.index=null,this.attributes={},this.morphAttributes={},this.groups=[],this.boundingBox=null,this.boundingSphere=null;let t={};this.name=e.name;let n=e.index;null!==n&&this.setIndex(n.clone(t));let i=e.attributes;for(let e in i){let n=i[e];this.setAttribute(e,n.clone(t))}let r=e.morphAttributes;for(let e in r){let n=[],i=r[e];for(let e=0,r=i.length;e<r;e++)n.push(i[e].clone(t));this.morphAttributes[e]=n}this.morphTargetsRelative=e.morphTargetsRelative;let a=e.groups;for(let e=0,t=a.length;e<t;e++){let t=a[e];this.addGroup(t.start,t.count,t.materialIndex)}let s=e.boundingBox;null!==s&&(this.boundingBox=s.clone());let o=e.boundingSphere;return null!==o&&(this.boundingSphere=o.clone()),this.drawRange.start=e.drawRange.start,this.drawRange.count=e.drawRange.count,this.userData=e.userData,this}dispose(){this.dispatchEvent({type:"dispose"})}constructor(){super(),this.isBufferGeometry=!0,Object.defineProperty(this,"id",{value:tp++}),this.uuid=g(),this.name="",this.type="BufferGeometry",this.index=null,this.attributes={},this.morphAttributes={},this.morphTargetsRelative=!1,this.groups=[],this.boundingBox=null,this.boundingSphere=null,this.drawRange={start:0,count:1/0},this.userData={}}}let tM=new eT,tS=new eE,tE=new em,tT=new Y,tb=new Y,tA=new Y,tw=new Y,tR=new Y,tC=new Y,tL=new S,tP=new S,tU=new S,tN=new Y,tD=new Y,tI=new Y,tO=new Y,tF=new Y;class tz extends eJ{copy(e,t){return super.copy(e,t),void 0!==e.morphTargetInfluences&&(this.morphTargetInfluences=e.morphTargetInfluences.slice()),void 0!==e.morphTargetDictionary&&(this.morphTargetDictionary=Object.assign({},e.morphTargetDictionary)),this.material=Array.isArray(e.material)?e.material.slice():e.material,this.geometry=e.geometry,this}updateMorphTargets(){let e=this.geometry.morphAttributes,t=Object.keys(e);if(t.length>0){let n=e[t[0]];if(void 0!==n){this.morphTargetInfluences=[],this.morphTargetDictionary={};for(let e=0,t=n.length;e<t;e++){let t=n[e].name||String(e);this.morphTargetInfluences.push(0),this.morphTargetDictionary[t]=e}}}}getVertexPosition(e,t){let n=this.geometry,i=n.attributes.position,r=n.morphAttributes.position,a=n.morphTargetsRelative;t.fromBufferAttribute(i,e);let s=this.morphTargetInfluences;if(r&&s){tC.set(0,0,0);for(let n=0,i=r.length;n<i;n++){let i=s[n],o=r[n];0!==i&&(tR.fromBufferAttribute(o,e),a?tC.addScaledVector(tR,i):tC.addScaledVector(tR.sub(t),i))}t.add(tC)}return t}raycast(e,t){let n=this.geometry,i=this.material,r=this.matrixWorld;if(void 0!==i)null===n.boundingSphere&&n.computeBoundingSphere(),tE.copy(n.boundingSphere),tE.applyMatrix4(r),tS.copy(e.ray).recast(e.near),!1===tE.containsPoint(tS.origin)&&(null===tS.intersectSphere(tE,tT)||tS.origin.distanceToSquared(tT)>(e.far-e.near)**2)||(tM.copy(r).invert(),tS.copy(e.ray).applyMatrix4(tM),(null===n.boundingBox||!1!==tS.intersectsBox(n.boundingBox))&&this._computeIntersections(e,t,tS))}_computeIntersections(e,t,n){let i,r=this.geometry,a=this.material,s=r.index,o=r.attributes.position,l=r.attributes.uv,h=r.attributes.uv1,c=r.attributes.normal,u=r.groups,d=r.drawRange;if(null!==s)if(Array.isArray(a))for(let r=0,o=u.length;r<o;r++){let o=u[r],p=a[o.materialIndex],f=Math.max(o.start,d.start),m=Math.min(s.count,Math.min(o.start+o.count,d.start+d.count));for(let r=f;r<m;r+=3){let a=s.getX(r);(i=tB(this,p,e,n,l,h,c,a,s.getX(r+1),s.getX(r+2)))&&(i.faceIndex=Math.floor(r/3),i.face.materialIndex=o.materialIndex,t.push(i))}}else{let r=Math.max(0,d.start),o=Math.min(s.count,d.start+d.count);for(let u=r;u<o;u+=3){let r=s.getX(u);(i=tB(this,a,e,n,l,h,c,r,s.getX(u+1),s.getX(u+2)))&&(i.faceIndex=Math.floor(u/3),t.push(i))}}else if(void 0!==o)if(Array.isArray(a))for(let r=0,s=u.length;r<s;r++){let s=u[r],p=a[s.materialIndex],f=Math.max(s.start,d.start),m=Math.min(o.count,Math.min(s.start+s.count,d.start+d.count));for(let r=f;r<m;r+=3)(i=tB(this,p,e,n,l,h,c,r,r+1,r+2))&&(i.faceIndex=Math.floor(r/3),i.face.materialIndex=s.materialIndex,t.push(i))}else{let r=Math.max(0,d.start),s=Math.min(o.count,d.start+d.count);for(let o=r;o<s;o+=3)(i=tB(this,a,e,n,l,h,c,o,o+1,o+2))&&(i.faceIndex=Math.floor(o/3),t.push(i))}}constructor(e=new ty,t=new ts){super(),this.isMesh=!0,this.type="Mesh",this.geometry=e,this.material=t,this.updateMorphTargets()}}function tB(e,t,n,i,r,a,s,o,l,h){e.getVertexPosition(o,tb),e.getVertexPosition(l,tA),e.getVertexPosition(h,tw);let c=function(e,t,n,i,r,a,s,o){let l;if(null===(1===t.side?i.intersectTriangle(s,a,r,!0,o):i.intersectTriangle(r,a,s,0===t.side,o)))return null;tF.copy(o),tF.applyMatrix4(e.matrixWorld);let h=n.ray.origin.distanceTo(tF);return h<n.near||h>n.far?null:{distance:h,point:tF.clone(),object:e}}(e,t,n,i,tb,tA,tw,tO);if(c){r&&(tL.fromBufferAttribute(r,o),tP.fromBufferAttribute(r,l),tU.fromBufferAttribute(r,h),c.uv=e8.getInterpolation(tO,tb,tA,tw,tL,tP,tU,new S)),a&&(tL.fromBufferAttribute(a,o),tP.fromBufferAttribute(a,l),tU.fromBufferAttribute(a,h),c.uv1=e8.getInterpolation(tO,tb,tA,tw,tL,tP,tU,new S),c.uv2=c.uv1),s&&(tN.fromBufferAttribute(s,o),tD.fromBufferAttribute(s,l),tI.fromBufferAttribute(s,h),c.normal=e8.getInterpolation(tO,tb,tA,tw,tN,tD,tI,new Y),c.normal.dot(i.direction)>0&&c.normal.multiplyScalar(-1));let e={a:o,b:l,c:h,normal:new Y,materialIndex:0};e8.getNormal(tb,tA,tw,e.normal),c.face=e}return c}class tH extends ty{copy(e){return super.copy(e),this.parameters=Object.assign({},e.parameters),this}static fromJSON(e){return new tH(e.width,e.height,e.depth,e.widthSegments,e.heightSegments,e.depthSegments)}constructor(e=1,t=1,n=1,i=1,r=1,a=1){super(),this.type="BoxGeometry",this.parameters={width:e,height:t,depth:n,widthSegments:i,heightSegments:r,depthSegments:a};let s=this;i=Math.floor(i),r=Math.floor(r);let o=[],l=[],h=[],c=[],u=0,d=0;function p(e,t,n,i,r,a,p,f,m,g,_){let v=a/m,x=p/g,y=a/2,M=p/2,S=f/2,E=m+1,T=g+1,b=0,A=0,w=new Y;for(let a=0;a<T;a++){let s=a*x-M;for(let o=0;o<E;o++){let u=o*v-y;w[e]=u*i,w[t]=s*r,w[n]=S,l.push(w.x,w.y,w.z),w[e]=0,w[t]=0,w[n]=f>0?1:-1,h.push(w.x,w.y,w.z),c.push(o/m),c.push(1-a/g),b+=1}}for(let e=0;e<g;e++)for(let t=0;t<m;t++){let n=u+t+E*e,i=u+t+E*(e+1),r=u+(t+1)+E*(e+1),a=u+(t+1)+E*e;o.push(n,i,a),o.push(i,r,a),A+=6}s.addGroup(d,A,_),d+=A,u+=b}p("z","y","x",-1,-1,n,t,e,a=Math.floor(a),r,0),p("z","y","x",1,-1,n,t,-e,a,r,1),p("x","z","y",1,1,e,n,t,i,a,2),p("x","z","y",1,-1,e,n,-t,i,a,3),p("x","y","z",1,-1,e,t,n,i,r,4),p("x","y","z",-1,-1,e,t,-n,i,r,5),this.setIndex(o),this.setAttribute("position",new td(l,3)),this.setAttribute("normal",new td(h,3)),this.setAttribute("uv",new td(c,2))}}function tV(e){let t={};for(let n in e)for(let i in t[n]={},e[n]){let r=e[n][i];r&&(r.isColor||r.isMatrix3||r.isMatrix4||r.isVector2||r.isVector3||r.isVector4||r.isTexture||r.isQuaternion)?r.isRenderTargetTexture?(console.warn("UniformsUtils: Textures of render targets cannot be cloned via cloneUniforms() or mergeUniforms()."),t[n][i]=null):t[n][i]=r.clone():Array.isArray(r)?t[n][i]=r.slice():t[n][i]=r}return t}function tG(e){let t={};for(let n=0;n<e.length;n++){let i=tV(e[n]);for(let e in i)t[e]=i[e]}return t}function tk(e){return null===e.getRenderTarget()?e.outputColorSpace:N.workingColorSpace}let tW={clone:tV};class tX extends ta{copy(e){return super.copy(e),this.fragmentShader=e.fragmentShader,this.vertexShader=e.vertexShader,this.uniforms=tV(e.uniforms),this.uniformsGroups=function(e){let t=[];for(let n=0;n<e.length;n++)t.push(e[n].clone());return t}(e.uniformsGroups),this.defines=Object.assign({},e.defines),this.wireframe=e.wireframe,this.wireframeLinewidth=e.wireframeLinewidth,this.fog=e.fog,this.lights=e.lights,this.clipping=e.clipping,this.extensions=Object.assign({},e.extensions),this.glslVersion=e.glslVersion,this}toJSON(e){let t=super.toJSON(e);for(let n in t.glslVersion=this.glslVersion,t.uniforms={},this.uniforms){let i=this.uniforms[n].value;i&&i.isTexture?t.uniforms[n]={type:"t",value:i.toJSON(e).uuid}:i&&i.isColor?t.uniforms[n]={type:"c",value:i.getHex()}:i&&i.isVector2?t.uniforms[n]={type:"v2",value:i.toArray()}:i&&i.isVector3?t.uniforms[n]={type:"v3",value:i.toArray()}:i&&i.isVector4?t.uniforms[n]={type:"v4",value:i.toArray()}:i&&i.isMatrix3?t.uniforms[n]={type:"m3",value:i.toArray()}:i&&i.isMatrix4?t.uniforms[n]={type:"m4",value:i.toArray()}:t.uniforms[n]={value:i}}Object.keys(this.defines).length>0&&(t.defines=this.defines),t.vertexShader=this.vertexShader,t.fragmentShader=this.fragmentShader,t.lights=this.lights,t.clipping=this.clipping;let n={};for(let e in this.extensions)!0===this.extensions[e]&&(n[e]=!0);return Object.keys(n).length>0&&(t.extensions=n),t}constructor(e){super(),this.isShaderMaterial=!0,this.type="ShaderMaterial",this.defines={},this.uniforms={},this.uniformsGroups=[],this.vertexShader="void main() {\n	gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n}",this.fragmentShader="void main() {\n	gl_FragColor = vec4( 1.0, 0.0, 0.0, 1.0 );\n}",this.linewidth=1,this.wireframe=!1,this.wireframeLinewidth=1,this.fog=!1,this.lights=!1,this.clipping=!1,this.forceSinglePass=!0,this.extensions={derivatives:!1,fragDepth:!1,drawBuffers:!1,shaderTextureLOD:!1},this.defaultAttributeValues={color:[1,1,1],uv:[0,0],uv1:[0,0]},this.index0AttributeName=void 0,this.uniformsNeedUpdate=!1,this.glslVersion=null,void 0!==e&&this.setValues(e)}}class tj extends eJ{copy(e,t){return super.copy(e,t),this.matrixWorldInverse.copy(e.matrixWorldInverse),this.projectionMatrix.copy(e.projectionMatrix),this.projectionMatrixInverse.copy(e.projectionMatrixInverse),this.coordinateSystem=e.coordinateSystem,this}getWorldDirection(e){return super.getWorldDirection(e).negate()}updateMatrixWorld(e){super.updateMatrixWorld(e),this.matrixWorldInverse.copy(this.matrixWorld).invert()}updateWorldMatrix(e,t){super.updateWorldMatrix(e,t),this.matrixWorldInverse.copy(this.matrixWorld).invert()}clone(){return new this.constructor().copy(this)}constructor(){super(),this.isCamera=!0,this.type="Camera",this.matrixWorldInverse=new eT,this.projectionMatrix=new eT,this.projectionMatrixInverse=new eT,this.coordinateSystem=2e3}}class tq extends tj{copy(e,t){return super.copy(e,t),this.fov=e.fov,this.zoom=e.zoom,this.near=e.near,this.far=e.far,this.focus=e.focus,this.aspect=e.aspect,this.view=null===e.view?null:Object.assign({},e.view),this.filmGauge=e.filmGauge,this.filmOffset=e.filmOffset,this}setFocalLength(e){let t=.5*this.getFilmHeight()/e;this.fov=2*m*Math.atan(t),this.updateProjectionMatrix()}getFocalLength(){let e=Math.tan(.5*f*this.fov);return .5*this.getFilmHeight()/e}getEffectiveFOV(){return 2*m*Math.atan(Math.tan(.5*f*this.fov)/this.zoom)}getFilmWidth(){return this.filmGauge*Math.min(this.aspect,1)}getFilmHeight(){return this.filmGauge/Math.max(this.aspect,1)}setViewOffset(e,t,n,i,r,a){this.aspect=e/t,null===this.view&&(this.view={enabled:!0,fullWidth:1,fullHeight:1,offsetX:0,offsetY:0,width:1,height:1}),this.view.enabled=!0,this.view.fullWidth=e,this.view.fullHeight=t,this.view.offsetX=n,this.view.offsetY=i,this.view.width=r,this.view.height=a,this.updateProjectionMatrix()}clearViewOffset(){null!==this.view&&(this.view.enabled=!1),this.updateProjectionMatrix()}updateProjectionMatrix(){let e=this.near,t=e*Math.tan(.5*f*this.fov)/this.zoom,n=2*t,i=this.aspect*n,r=-.5*i,a=this.view;if(null!==this.view&&this.view.enabled){let e=a.fullWidth,s=a.fullHeight;r+=a.offsetX*i/e,t-=a.offsetY*n/s,i*=a.width/e,n*=a.height/s}let s=this.filmOffset;0!==s&&(r+=e*s/this.getFilmWidth()),this.projectionMatrix.makePerspective(r,r+i,t,t-n,e,this.far,this.coordinateSystem),this.projectionMatrixInverse.copy(this.projectionMatrix).invert()}toJSON(e){let t=super.toJSON(e);return t.object.fov=this.fov,t.object.zoom=this.zoom,t.object.near=this.near,t.object.far=this.far,t.object.focus=this.focus,t.object.aspect=this.aspect,null!==this.view&&(t.object.view=Object.assign({},this.view)),t.object.filmGauge=this.filmGauge,t.object.filmOffset=this.filmOffset,t}constructor(e=50,t=1,n=.1,i=2e3){super(),this.isPerspectiveCamera=!0,this.type="PerspectiveCamera",this.fov=e,this.zoom=1,this.near=n,this.far=i,this.focus=10,this.aspect=t,this.view=null,this.filmGauge=35,this.filmOffset=0,this.updateProjectionMatrix()}}class tY extends eJ{updateCoordinateSystem(){let e=this.coordinateSystem,t=this.children.concat(),[n,i,r,a,s,o]=t;for(let e of t)this.remove(e);if(2e3===e)n.up.set(0,1,0),n.lookAt(1,0,0),i.up.set(0,1,0),i.lookAt(-1,0,0),r.up.set(0,0,-1),r.lookAt(0,1,0),a.up.set(0,0,1),a.lookAt(0,-1,0),s.up.set(0,1,0),s.lookAt(0,0,1),o.up.set(0,1,0),o.lookAt(0,0,-1);else if(2001===e)n.up.set(0,-1,0),n.lookAt(-1,0,0),i.up.set(0,-1,0),i.lookAt(1,0,0),r.up.set(0,0,1),r.lookAt(0,1,0),a.up.set(0,0,-1),a.lookAt(0,-1,0),s.up.set(0,-1,0),s.lookAt(0,0,1),o.up.set(0,-1,0),o.lookAt(0,0,-1);else throw Error("THREE.CubeCamera.updateCoordinateSystem(): Invalid coordinate system: "+e);for(let e of t)this.add(e),e.updateMatrixWorld()}update(e,t){null===this.parent&&this.updateMatrixWorld();let{renderTarget:n,activeMipmapLevel:i}=this;this.coordinateSystem!==e.coordinateSystem&&(this.coordinateSystem=e.coordinateSystem,this.updateCoordinateSystem());let[r,a,s,o,l,h]=this.children,c=e.getRenderTarget(),u=e.getActiveCubeFace(),d=e.getActiveMipmapLevel(),p=e.xr.enabled;e.xr.enabled=!1;let f=n.texture.generateMipmaps;n.texture.generateMipmaps=!1,e.setRenderTarget(n,0,i),e.render(t,r),e.setRenderTarget(n,1,i),e.render(t,a),e.setRenderTarget(n,2,i),e.render(t,s),e.setRenderTarget(n,3,i),e.render(t,o),e.setRenderTarget(n,4,i),e.render(t,l),n.texture.generateMipmaps=f,e.setRenderTarget(n,5,i),e.render(t,h),e.setRenderTarget(c,u,d),e.xr.enabled=p,n.texture.needsPMREMUpdate=!0}constructor(e,t,n){super(),this.type="CubeCamera",this.renderTarget=n,this.coordinateSystem=null,this.activeMipmapLevel=0;let i=new tq(-90,1,e,t);i.layers=this.layers,this.add(i);let r=new tq(-90,1,e,t);r.layers=this.layers,this.add(r);let a=new tq(-90,1,e,t);a.layers=this.layers,this.add(a);let s=new tq(-90,1,e,t);s.layers=this.layers,this.add(s);let o=new tq(-90,1,e,t);o.layers=this.layers,this.add(o);let l=new tq(-90,1,e,t);l.layers=this.layers,this.add(l)}}class tJ extends V{get images(){return this.image}set images(e){this.image=e}constructor(e,t,n,i,r,a,s,o,l,h){super(e=void 0!==e?e:[],t=void 0!==t?t:301,n,i,r,a,s,o,l,h),this.isCubeTexture=!0,this.flipY=!1}}class tZ extends W{fromEquirectangularTexture(e,t){this.texture.type=t.type,this.texture.colorSpace=t.colorSpace,this.texture.generateMipmaps=t.generateMipmaps,this.texture.minFilter=t.minFilter,this.texture.magFilter=t.magFilter;let n=new tH(5,5,5),i=new tX({name:"CubemapFromEquirect",uniforms:tV({tEquirect:{value:null}}),vertexShader:"\n\n				varying vec3 vWorldDirection;\n\n				vec3 transformDirection( in vec3 dir, in mat4 matrix ) {\n\n					return normalize( ( matrix * vec4( dir, 0.0 ) ).xyz );\n\n				}\n\n				void main() {\n\n					vWorldDirection = transformDirection( position, modelMatrix );\n\n					#include <begin_vertex>\n					#include <project_vertex>\n\n				}\n			",fragmentShader:"\n\n				uniform sampler2D tEquirect;\n\n				varying vec3 vWorldDirection;\n\n				#include <common>\n\n				void main() {\n\n					vec3 direction = normalize( vWorldDirection );\n\n					vec2 sampleUV = equirectUv( direction );\n\n					gl_FragColor = texture2D( tEquirect, sampleUV );\n\n				}\n			",side:1,blending:0});i.uniforms.tEquirect.value=t;let r=new tz(n,i),a=t.minFilter;return 1008===t.minFilter&&(t.minFilter=1006),new tY(1,10,this).update(e,r),t.minFilter=a,r.geometry.dispose(),r.material.dispose(),this}clear(e,t,n,i){let r=e.getRenderTarget();for(let r=0;r<6;r++)e.setRenderTarget(this,r),e.clear(t,n,i);e.setRenderTarget(r)}constructor(e=1,t={}){super(e,e,t),this.isWebGLCubeRenderTarget=!0;let n={width:e,height:e,depth:1};void 0!==t.encoding&&(R("THREE.WebGLCubeRenderTarget: option.encoding has been replaced by option.colorSpace."),t.colorSpace=3001===t.encoding?r:""),this.texture=new tJ([n,n,n,n,n,n],t.mapping,t.wrapS,t.wrapT,t.magFilter,t.minFilter,t.format,t.type,t.anisotropy,t.colorSpace),this.texture.isRenderTargetTexture=!0,this.texture.generateMipmaps=void 0!==t.generateMipmaps&&t.generateMipmaps,this.texture.minFilter=void 0!==t.minFilter?t.minFilter:1006}}let tK=new Y,tQ=new Y,t$=new E;class t0{set(e,t){return this.normal.copy(e),this.constant=t,this}setComponents(e,t,n,i){return this.normal.set(e,t,n),this.constant=i,this}setFromNormalAndCoplanarPoint(e,t){return this.normal.copy(e),this.constant=-t.dot(this.normal),this}setFromCoplanarPoints(e,t,n){let i=tK.subVectors(n,t).cross(tQ.subVectors(e,t)).normalize();return this.setFromNormalAndCoplanarPoint(i,e),this}copy(e){return this.normal.copy(e.normal),this.constant=e.constant,this}normalize(){let e=1/this.normal.length();return this.normal.multiplyScalar(e),this.constant*=e,this}negate(){return this.constant*=-1,this.normal.negate(),this}distanceToPoint(e){return this.normal.dot(e)+this.constant}distanceToSphere(e){return this.distanceToPoint(e.center)-e.radius}projectPoint(e,t){return t.copy(e).addScaledVector(this.normal,-this.distanceToPoint(e))}intersectLine(e,t){let n=e.delta(tK),i=this.normal.dot(n);if(0===i)return 0===this.distanceToPoint(e.start)?t.copy(e.start):null;let r=-(e.start.dot(this.normal)+this.constant)/i;return r<0||r>1?null:t.copy(e.start).addScaledVector(n,r)}intersectsLine(e){let t=this.distanceToPoint(e.start),n=this.distanceToPoint(e.end);return t<0&&n>0||n<0&&t>0}intersectsBox(e){return e.intersectsPlane(this)}intersectsSphere(e){return e.intersectsPlane(this)}coplanarPoint(e){return e.copy(this.normal).multiplyScalar(-this.constant)}applyMatrix4(e,t){let n=t||t$.getNormalMatrix(e),i=this.coplanarPoint(tK).applyMatrix4(e),r=this.normal.applyMatrix3(n).normalize();return this.constant=-i.dot(r),this}translate(e){return this.constant-=e.dot(this.normal),this}equals(e){return e.normal.equals(this.normal)&&e.constant===this.constant}clone(){return new this.constructor().copy(this)}constructor(e=new Y(1,0,0),t=0){this.isPlane=!0,this.normal=e,this.constant=t}}let t1=new em,t2=new Y;class t3{set(e,t,n,i,r,a){let s=this.planes;return s[0].copy(e),s[1].copy(t),s[2].copy(n),s[3].copy(i),s[4].copy(r),s[5].copy(a),this}copy(e){let t=this.planes;for(let n=0;n<6;n++)t[n].copy(e.planes[n]);return this}setFromProjectionMatrix(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2e3,n=this.planes,i=e.elements,r=i[0],a=i[1],s=i[2],o=i[3],l=i[4],h=i[5],c=i[6],u=i[7],d=i[8],p=i[9],f=i[10],m=i[11],g=i[12],_=i[13],v=i[14],x=i[15];if(n[0].setComponents(o-r,u-l,m-d,x-g).normalize(),n[1].setComponents(o+r,u+l,m+d,x+g).normalize(),n[2].setComponents(o+a,u+h,m+p,x+_).normalize(),n[3].setComponents(o-a,u-h,m-p,x-_).normalize(),n[4].setComponents(o-s,u-c,m-f,x-v).normalize(),2e3===t)n[5].setComponents(o+s,u+c,m+f,x+v).normalize();else if(2001===t)n[5].setComponents(s,c,f,v).normalize();else throw Error("THREE.Frustum.setFromProjectionMatrix(): Invalid coordinate system: "+t);return this}intersectsObject(e){if(void 0!==e.boundingSphere)null===e.boundingSphere&&e.computeBoundingSphere(),t1.copy(e.boundingSphere).applyMatrix4(e.matrixWorld);else{let t=e.geometry;null===t.boundingSphere&&t.computeBoundingSphere(),t1.copy(t.boundingSphere).applyMatrix4(e.matrixWorld)}return this.intersectsSphere(t1)}intersectsSprite(e){return t1.center.set(0,0,0),t1.radius=.*********1865476,t1.applyMatrix4(e.matrixWorld),this.intersectsSphere(t1)}intersectsSphere(e){let t=this.planes,n=e.center,i=-e.radius;for(let e=0;e<6;e++)if(t[e].distanceToPoint(n)<i)return!1;return!0}intersectsBox(e){let t=this.planes;for(let n=0;n<6;n++){let i=t[n];if(t2.x=i.normal.x>0?e.max.x:e.min.x,t2.y=i.normal.y>0?e.max.y:e.min.y,t2.z=i.normal.z>0?e.max.z:e.min.z,0>i.distanceToPoint(t2))return!1}return!0}containsPoint(e){let t=this.planes;for(let n=0;n<6;n++)if(0>t[n].distanceToPoint(e))return!1;return!0}clone(){return new this.constructor().copy(this)}constructor(e=new t0,t=new t0,n=new t0,i=new t0,r=new t0,a=new t0){this.planes=[e,t,n,i,r,a]}}function t4(){let e=null,t=!1,n=null,i=null;function r(t,a){n(t,a),i=e.requestAnimationFrame(r)}return{start:function(){!0!==t&&null!==n&&(i=e.requestAnimationFrame(r),t=!0)},stop:function(){e.cancelAnimationFrame(i),t=!1},setAnimationLoop:function(e){n=e},setContext:function(t){e=t}}}function t5(e,t){let n=t.isWebGL2,i=new WeakMap;return{get:function(e){return e.isInterleavedBufferAttribute&&(e=e.data),i.get(e)},remove:function(t){t.isInterleavedBufferAttribute&&(t=t.data);let n=i.get(t);n&&(e.deleteBuffer(n.buffer),i.delete(t))},update:function(t,r){if(t.isGLBufferAttribute){let e=i.get(t);(!e||e.version<t.version)&&i.set(t,{buffer:t.buffer,type:t.type,bytesPerElement:t.elementSize,version:t.version});return}t.isInterleavedBufferAttribute&&(t=t.data);let a=i.get(t);if(void 0===a)i.set(t,function(t,i){let r,a=t.array,s=t.usage,o=e.createBuffer();if(e.bindBuffer(i,o),e.bufferData(i,a,s),t.onUploadCallback(),a instanceof Float32Array)r=e.FLOAT;else if(a instanceof Uint16Array)if(t.isFloat16BufferAttribute)if(n)r=e.HALF_FLOAT;else throw Error("THREE.WebGLAttributes: Usage of Float16BufferAttribute requires WebGL2.");else r=e.UNSIGNED_SHORT;else if(a instanceof Int16Array)r=e.SHORT;else if(a instanceof Uint32Array)r=e.UNSIGNED_INT;else if(a instanceof Int32Array)r=e.INT;else if(a instanceof Int8Array)r=e.BYTE;else if(a instanceof Uint8Array)r=e.UNSIGNED_BYTE;else if(a instanceof Uint8ClampedArray)r=e.UNSIGNED_BYTE;else throw Error("THREE.WebGLAttributes: Unsupported buffer data format: "+a);return{buffer:o,type:r,bytesPerElement:a.BYTES_PER_ELEMENT,version:t.version}}(t,r));else if(a.version<t.version){var s=a.buffer,o=t;let i=o.array,l=o.updateRange;e.bindBuffer(r,s),-1===l.count?e.bufferSubData(r,0,i):(n?e.bufferSubData(r,l.offset*i.BYTES_PER_ELEMENT,i,l.offset,l.count):e.bufferSubData(r,l.offset*i.BYTES_PER_ELEMENT,i.subarray(l.offset,l.offset+l.count)),l.count=-1),o.onUploadCallback(),a.version=t.version}}}}class t6 extends ty{copy(e){return super.copy(e),this.parameters=Object.assign({},e.parameters),this}static fromJSON(e){return new t6(e.width,e.height,e.widthSegments,e.heightSegments)}constructor(e=1,t=1,n=1,i=1){super(),this.type="PlaneGeometry",this.parameters={width:e,height:t,widthSegments:n,heightSegments:i};let r=e/2,a=t/2,s=Math.floor(n),o=Math.floor(i),l=s+1,h=o+1,c=e/s,u=t/o,d=[],p=[],f=[],m=[];for(let e=0;e<h;e++){let t=e*u-a;for(let n=0;n<l;n++){let i=n*c-r;p.push(i,-t,0),f.push(0,0,1),m.push(n/s),m.push(1-e/o)}}for(let e=0;e<o;e++)for(let t=0;t<s;t++){let n=t+l*e,i=t+l*(e+1),r=t+1+l*(e+1),a=t+1+l*e;d.push(n,i,a),d.push(i,r,a)}this.setIndex(d),this.setAttribute("position",new td(p,3)),this.setAttribute("normal",new td(f,3)),this.setAttribute("uv",new td(m,2))}}let t8={alphahash_fragment:"#ifdef USE_ALPHAHASH\n	if ( diffuseColor.a < getAlphaHashThreshold( vPosition ) ) discard;\n#endif",alphahash_pars_fragment:"#ifdef USE_ALPHAHASH\n	const float ALPHA_HASH_SCALE = 0.05;\n	float hash2D( vec2 value ) {\n		return fract( 1.0e4 * sin( 17.0 * value.x + 0.1 * value.y ) * ( 0.1 + abs( sin( 13.0 * value.y + value.x ) ) ) );\n	}\n	float hash3D( vec3 value ) {\n		return hash2D( vec2( hash2D( value.xy ), value.z ) );\n	}\n	float getAlphaHashThreshold( vec3 position ) {\n		float maxDeriv = max(\n			length( dFdx( position.xyz ) ),\n			length( dFdy( position.xyz ) )\n		);\n		float pixScale = 1.0 / ( ALPHA_HASH_SCALE * maxDeriv );\n		vec2 pixScales = vec2(\n			exp2( floor( log2( pixScale ) ) ),\n			exp2( ceil( log2( pixScale ) ) )\n		);\n		vec2 alpha = vec2(\n			hash3D( floor( pixScales.x * position.xyz ) ),\n			hash3D( floor( pixScales.y * position.xyz ) )\n		);\n		float lerpFactor = fract( log2( pixScale ) );\n		float x = ( 1.0 - lerpFactor ) * alpha.x + lerpFactor * alpha.y;\n		float a = min( lerpFactor, 1.0 - lerpFactor );\n		vec3 cases = vec3(\n			x * x / ( 2.0 * a * ( 1.0 - a ) ),\n			( x - 0.5 * a ) / ( 1.0 - a ),\n			1.0 - ( ( 1.0 - x ) * ( 1.0 - x ) / ( 2.0 * a * ( 1.0 - a ) ) )\n		);\n		float threshold = ( x < ( 1.0 - a ) )\n			? ( ( x < a ) ? cases.x : cases.y )\n			: cases.z;\n		return clamp( threshold , 1.0e-6, 1.0 );\n	}\n#endif",alphamap_fragment:"#ifdef USE_ALPHAMAP\n	diffuseColor.a *= texture2D( alphaMap, vAlphaMapUv ).g;\n#endif",alphamap_pars_fragment:"#ifdef USE_ALPHAMAP\n	uniform sampler2D alphaMap;\n#endif",alphatest_fragment:"#ifdef USE_ALPHATEST\n	if ( diffuseColor.a < alphaTest ) discard;\n#endif",alphatest_pars_fragment:"#ifdef USE_ALPHATEST\n	uniform float alphaTest;\n#endif",aomap_fragment:"#ifdef USE_AOMAP\n	float ambientOcclusion = ( texture2D( aoMap, vAoMapUv ).r - 1.0 ) * aoMapIntensity + 1.0;\n	reflectedLight.indirectDiffuse *= ambientOcclusion;\n	#if defined( USE_CLEARCOAT ) \n		clearcoatSpecularIndirect *= ambientOcclusion;\n	#endif\n	#if defined( USE_SHEEN ) \n		sheenSpecularIndirect *= ambientOcclusion;\n	#endif\n	#if defined( USE_ENVMAP ) && defined( STANDARD )\n		float dotNV = saturate( dot( geometryNormal, geometryViewDir ) );\n		reflectedLight.indirectSpecular *= computeSpecularOcclusion( dotNV, ambientOcclusion, material.roughness );\n	#endif\n#endif",aomap_pars_fragment:"#ifdef USE_AOMAP\n	uniform sampler2D aoMap;\n	uniform float aoMapIntensity;\n#endif",begin_vertex:"vec3 transformed = vec3( position );\n#ifdef USE_ALPHAHASH\n	vPosition = vec3( position );\n#endif",beginnormal_vertex:"vec3 objectNormal = vec3( normal );\n#ifdef USE_TANGENT\n	vec3 objectTangent = vec3( tangent.xyz );\n#endif",bsdfs:"float G_BlinnPhong_Implicit( ) {\n	return 0.25;\n}\nfloat D_BlinnPhong( const in float shininess, const in float dotNH ) {\n	return RECIPROCAL_PI * ( shininess * 0.5 + 1.0 ) * pow( dotNH, shininess );\n}\nvec3 BRDF_BlinnPhong( const in vec3 lightDir, const in vec3 viewDir, const in vec3 normal, const in vec3 specularColor, const in float shininess ) {\n	vec3 halfDir = normalize( lightDir + viewDir );\n	float dotNH = saturate( dot( normal, halfDir ) );\n	float dotVH = saturate( dot( viewDir, halfDir ) );\n	vec3 F = F_Schlick( specularColor, 1.0, dotVH );\n	float G = G_BlinnPhong_Implicit( );\n	float D = D_BlinnPhong( shininess, dotNH );\n	return F * ( G * D );\n} // validated",iridescence_fragment:"#ifdef USE_IRIDESCENCE\n	const mat3 XYZ_TO_REC709 = mat3(\n		 3.2404542, -0.9692660,  0.0556434,\n		-1.5371385,  1.8760108, -0.2040259,\n		-0.4985314,  0.0415560,  1.0572252\n	);\n	vec3 Fresnel0ToIor( vec3 fresnel0 ) {\n		vec3 sqrtF0 = sqrt( fresnel0 );\n		return ( vec3( 1.0 ) + sqrtF0 ) / ( vec3( 1.0 ) - sqrtF0 );\n	}\n	vec3 IorToFresnel0( vec3 transmittedIor, float incidentIor ) {\n		return pow2( ( transmittedIor - vec3( incidentIor ) ) / ( transmittedIor + vec3( incidentIor ) ) );\n	}\n	float IorToFresnel0( float transmittedIor, float incidentIor ) {\n		return pow2( ( transmittedIor - incidentIor ) / ( transmittedIor + incidentIor ));\n	}\n	vec3 evalSensitivity( float OPD, vec3 shift ) {\n		float phase = 2.0 * PI * OPD * 1.0e-9;\n		vec3 val = vec3( 5.4856e-13, 4.4201e-13, 5.2481e-13 );\n		vec3 pos = vec3( 1.6810e+06, 1.7953e+06, 2.2084e+06 );\n		vec3 var = vec3( 4.3278e+09, 9.3046e+09, 6.6121e+09 );\n		vec3 xyz = val * sqrt( 2.0 * PI * var ) * cos( pos * phase + shift ) * exp( - pow2( phase ) * var );\n		xyz.x += 9.7470e-14 * sqrt( 2.0 * PI * 4.5282e+09 ) * cos( 2.2399e+06 * phase + shift[ 0 ] ) * exp( - 4.5282e+09 * pow2( phase ) );\n		xyz /= 1.0685e-7;\n		vec3 rgb = XYZ_TO_REC709 * xyz;\n		return rgb;\n	}\n	vec3 evalIridescence( float outsideIOR, float eta2, float cosTheta1, float thinFilmThickness, vec3 baseF0 ) {\n		vec3 I;\n		float iridescenceIOR = mix( outsideIOR, eta2, smoothstep( 0.0, 0.03, thinFilmThickness ) );\n		float sinTheta2Sq = pow2( outsideIOR / iridescenceIOR ) * ( 1.0 - pow2( cosTheta1 ) );\n		float cosTheta2Sq = 1.0 - sinTheta2Sq;\n		if ( cosTheta2Sq < 0.0 ) {\n			return vec3( 1.0 );\n		}\n		float cosTheta2 = sqrt( cosTheta2Sq );\n		float R0 = IorToFresnel0( iridescenceIOR, outsideIOR );\n		float R12 = F_Schlick( R0, 1.0, cosTheta1 );\n		float T121 = 1.0 - R12;\n		float phi12 = 0.0;\n		if ( iridescenceIOR < outsideIOR ) phi12 = PI;\n		float phi21 = PI - phi12;\n		vec3 baseIOR = Fresnel0ToIor( clamp( baseF0, 0.0, 0.9999 ) );		vec3 R1 = IorToFresnel0( baseIOR, iridescenceIOR );\n		vec3 R23 = F_Schlick( R1, 1.0, cosTheta2 );\n		vec3 phi23 = vec3( 0.0 );\n		if ( baseIOR[ 0 ] < iridescenceIOR ) phi23[ 0 ] = PI;\n		if ( baseIOR[ 1 ] < iridescenceIOR ) phi23[ 1 ] = PI;\n		if ( baseIOR[ 2 ] < iridescenceIOR ) phi23[ 2 ] = PI;\n		float OPD = 2.0 * iridescenceIOR * thinFilmThickness * cosTheta2;\n		vec3 phi = vec3( phi21 ) + phi23;\n		vec3 R123 = clamp( R12 * R23, 1e-5, 0.9999 );\n		vec3 r123 = sqrt( R123 );\n		vec3 Rs = pow2( T121 ) * R23 / ( vec3( 1.0 ) - R123 );\n		vec3 C0 = R12 + Rs;\n		I = C0;\n		vec3 Cm = Rs - T121;\n		for ( int m = 1; m <= 2; ++ m ) {\n			Cm *= r123;\n			vec3 Sm = 2.0 * evalSensitivity( float( m ) * OPD, float( m ) * phi );\n			I += Cm * Sm;\n		}\n		return max( I, vec3( 0.0 ) );\n	}\n#endif",bumpmap_pars_fragment:"#ifdef USE_BUMPMAP\n	uniform sampler2D bumpMap;\n	uniform float bumpScale;\n	vec2 dHdxy_fwd() {\n		vec2 dSTdx = dFdx( vBumpMapUv );\n		vec2 dSTdy = dFdy( vBumpMapUv );\n		float Hll = bumpScale * texture2D( bumpMap, vBumpMapUv ).x;\n		float dBx = bumpScale * texture2D( bumpMap, vBumpMapUv + dSTdx ).x - Hll;\n		float dBy = bumpScale * texture2D( bumpMap, vBumpMapUv + dSTdy ).x - Hll;\n		return vec2( dBx, dBy );\n	}\n	vec3 perturbNormalArb( vec3 surf_pos, vec3 surf_norm, vec2 dHdxy, float faceDirection ) {\n		vec3 vSigmaX = normalize( dFdx( surf_pos.xyz ) );\n		vec3 vSigmaY = normalize( dFdy( surf_pos.xyz ) );\n		vec3 vN = surf_norm;\n		vec3 R1 = cross( vSigmaY, vN );\n		vec3 R2 = cross( vN, vSigmaX );\n		float fDet = dot( vSigmaX, R1 ) * faceDirection;\n		vec3 vGrad = sign( fDet ) * ( dHdxy.x * R1 + dHdxy.y * R2 );\n		return normalize( abs( fDet ) * surf_norm - vGrad );\n	}\n#endif",clipping_planes_fragment:"#if NUM_CLIPPING_PLANES > 0\n	vec4 plane;\n	#pragma unroll_loop_start\n	for ( int i = 0; i < UNION_CLIPPING_PLANES; i ++ ) {\n		plane = clippingPlanes[ i ];\n		if ( dot( vClipPosition, plane.xyz ) > plane.w ) discard;\n	}\n	#pragma unroll_loop_end\n	#if UNION_CLIPPING_PLANES < NUM_CLIPPING_PLANES\n		bool clipped = true;\n		#pragma unroll_loop_start\n		for ( int i = UNION_CLIPPING_PLANES; i < NUM_CLIPPING_PLANES; i ++ ) {\n			plane = clippingPlanes[ i ];\n			clipped = ( dot( vClipPosition, plane.xyz ) > plane.w ) && clipped;\n		}\n		#pragma unroll_loop_end\n		if ( clipped ) discard;\n	#endif\n#endif",clipping_planes_pars_fragment:"#if NUM_CLIPPING_PLANES > 0\n	varying vec3 vClipPosition;\n	uniform vec4 clippingPlanes[ NUM_CLIPPING_PLANES ];\n#endif",clipping_planes_pars_vertex:"#if NUM_CLIPPING_PLANES > 0\n	varying vec3 vClipPosition;\n#endif",clipping_planes_vertex:"#if NUM_CLIPPING_PLANES > 0\n	vClipPosition = - mvPosition.xyz;\n#endif",color_fragment:"#if defined( USE_COLOR_ALPHA )\n	diffuseColor *= vColor;\n#elif defined( USE_COLOR )\n	diffuseColor.rgb *= vColor;\n#endif",color_pars_fragment:"#if defined( USE_COLOR_ALPHA )\n	varying vec4 vColor;\n#elif defined( USE_COLOR )\n	varying vec3 vColor;\n#endif",color_pars_vertex:"#if defined( USE_COLOR_ALPHA )\n	varying vec4 vColor;\n#elif defined( USE_COLOR ) || defined( USE_INSTANCING_COLOR )\n	varying vec3 vColor;\n#endif",color_vertex:"#if defined( USE_COLOR_ALPHA )\n	vColor = vec4( 1.0 );\n#elif defined( USE_COLOR ) || defined( USE_INSTANCING_COLOR )\n	vColor = vec3( 1.0 );\n#endif\n#ifdef USE_COLOR\n	vColor *= color;\n#endif\n#ifdef USE_INSTANCING_COLOR\n	vColor.xyz *= instanceColor.xyz;\n#endif",common:"#define PI 3.141592653589793\n#define PI2 6.283185307179586\n#define PI_HALF 1.5707963267948966\n#define RECIPROCAL_PI 0.3183098861837907\n#define RECIPROCAL_PI2 0.15915494309189535\n#define EPSILON 1e-6\n#ifndef saturate\n#define saturate( a ) clamp( a, 0.0, 1.0 )\n#endif\n#define whiteComplement( a ) ( 1.0 - saturate( a ) )\nfloat pow2( const in float x ) { return x*x; }\nvec3 pow2( const in vec3 x ) { return x*x; }\nfloat pow3( const in float x ) { return x*x*x; }\nfloat pow4( const in float x ) { float x2 = x*x; return x2*x2; }\nfloat max3( const in vec3 v ) { return max( max( v.x, v.y ), v.z ); }\nfloat average( const in vec3 v ) { return dot( v, vec3( 0.3333333 ) ); }\nhighp float rand( const in vec2 uv ) {\n	const highp float a = 12.9898, b = 78.233, c = 43758.5453;\n	highp float dt = dot( uv.xy, vec2( a,b ) ), sn = mod( dt, PI );\n	return fract( sin( sn ) * c );\n}\n#ifdef HIGH_PRECISION\n	float precisionSafeLength( vec3 v ) { return length( v ); }\n#else\n	float precisionSafeLength( vec3 v ) {\n		float maxComponent = max3( abs( v ) );\n		return length( v / maxComponent ) * maxComponent;\n	}\n#endif\nstruct IncidentLight {\n	vec3 color;\n	vec3 direction;\n	bool visible;\n};\nstruct ReflectedLight {\n	vec3 directDiffuse;\n	vec3 directSpecular;\n	vec3 indirectDiffuse;\n	vec3 indirectSpecular;\n};\n#ifdef USE_ALPHAHASH\n	varying vec3 vPosition;\n#endif\nvec3 transformDirection( in vec3 dir, in mat4 matrix ) {\n	return normalize( ( matrix * vec4( dir, 0.0 ) ).xyz );\n}\nvec3 inverseTransformDirection( in vec3 dir, in mat4 matrix ) {\n	return normalize( ( vec4( dir, 0.0 ) * matrix ).xyz );\n}\nmat3 transposeMat3( const in mat3 m ) {\n	mat3 tmp;\n	tmp[ 0 ] = vec3( m[ 0 ].x, m[ 1 ].x, m[ 2 ].x );\n	tmp[ 1 ] = vec3( m[ 0 ].y, m[ 1 ].y, m[ 2 ].y );\n	tmp[ 2 ] = vec3( m[ 0 ].z, m[ 1 ].z, m[ 2 ].z );\n	return tmp;\n}\nfloat luminance( const in vec3 rgb ) {\n	const vec3 weights = vec3( 0.2126729, 0.7151522, 0.0721750 );\n	return dot( weights, rgb );\n}\nbool isPerspectiveMatrix( mat4 m ) {\n	return m[ 2 ][ 3 ] == - 1.0;\n}\nvec2 equirectUv( in vec3 dir ) {\n	float u = atan( dir.z, dir.x ) * RECIPROCAL_PI2 + 0.5;\n	float v = asin( clamp( dir.y, - 1.0, 1.0 ) ) * RECIPROCAL_PI + 0.5;\n	return vec2( u, v );\n}\nvec3 BRDF_Lambert( const in vec3 diffuseColor ) {\n	return RECIPROCAL_PI * diffuseColor;\n}\nvec3 F_Schlick( const in vec3 f0, const in float f90, const in float dotVH ) {\n	float fresnel = exp2( ( - 5.55473 * dotVH - 6.98316 ) * dotVH );\n	return f0 * ( 1.0 - fresnel ) + ( f90 * fresnel );\n}\nfloat F_Schlick( const in float f0, const in float f90, const in float dotVH ) {\n	float fresnel = exp2( ( - 5.55473 * dotVH - 6.98316 ) * dotVH );\n	return f0 * ( 1.0 - fresnel ) + ( f90 * fresnel );\n} // validated",cube_uv_reflection_fragment:"#ifdef ENVMAP_TYPE_CUBE_UV\n	#define cubeUV_minMipLevel 4.0\n	#define cubeUV_minTileSize 16.0\n	float getFace( vec3 direction ) {\n		vec3 absDirection = abs( direction );\n		float face = - 1.0;\n		if ( absDirection.x > absDirection.z ) {\n			if ( absDirection.x > absDirection.y )\n				face = direction.x > 0.0 ? 0.0 : 3.0;\n			else\n				face = direction.y > 0.0 ? 1.0 : 4.0;\n		} else {\n			if ( absDirection.z > absDirection.y )\n				face = direction.z > 0.0 ? 2.0 : 5.0;\n			else\n				face = direction.y > 0.0 ? 1.0 : 4.0;\n		}\n		return face;\n	}\n	vec2 getUV( vec3 direction, float face ) {\n		vec2 uv;\n		if ( face == 0.0 ) {\n			uv = vec2( direction.z, direction.y ) / abs( direction.x );\n		} else if ( face == 1.0 ) {\n			uv = vec2( - direction.x, - direction.z ) / abs( direction.y );\n		} else if ( face == 2.0 ) {\n			uv = vec2( - direction.x, direction.y ) / abs( direction.z );\n		} else if ( face == 3.0 ) {\n			uv = vec2( - direction.z, direction.y ) / abs( direction.x );\n		} else if ( face == 4.0 ) {\n			uv = vec2( - direction.x, direction.z ) / abs( direction.y );\n		} else {\n			uv = vec2( direction.x, direction.y ) / abs( direction.z );\n		}\n		return 0.5 * ( uv + 1.0 );\n	}\n	vec3 bilinearCubeUV( sampler2D envMap, vec3 direction, float mipInt ) {\n		float face = getFace( direction );\n		float filterInt = max( cubeUV_minMipLevel - mipInt, 0.0 );\n		mipInt = max( mipInt, cubeUV_minMipLevel );\n		float faceSize = exp2( mipInt );\n		highp vec2 uv = getUV( direction, face ) * ( faceSize - 2.0 ) + 1.0;\n		if ( face > 2.0 ) {\n			uv.y += faceSize;\n			face -= 3.0;\n		}\n		uv.x += face * faceSize;\n		uv.x += filterInt * 3.0 * cubeUV_minTileSize;\n		uv.y += 4.0 * ( exp2( CUBEUV_MAX_MIP ) - faceSize );\n		uv.x *= CUBEUV_TEXEL_WIDTH;\n		uv.y *= CUBEUV_TEXEL_HEIGHT;\n		#ifdef texture2DGradEXT\n			return texture2DGradEXT( envMap, uv, vec2( 0.0 ), vec2( 0.0 ) ).rgb;\n		#else\n			return texture2D( envMap, uv ).rgb;\n		#endif\n	}\n	#define cubeUV_r0 1.0\n	#define cubeUV_v0 0.339\n	#define cubeUV_m0 - 2.0\n	#define cubeUV_r1 0.8\n	#define cubeUV_v1 0.276\n	#define cubeUV_m1 - 1.0\n	#define cubeUV_r4 0.4\n	#define cubeUV_v4 0.046\n	#define cubeUV_m4 2.0\n	#define cubeUV_r5 0.305\n	#define cubeUV_v5 0.016\n	#define cubeUV_m5 3.0\n	#define cubeUV_r6 0.21\n	#define cubeUV_v6 0.0038\n	#define cubeUV_m6 4.0\n	float roughnessToMip( float roughness ) {\n		float mip = 0.0;\n		if ( roughness >= cubeUV_r1 ) {\n			mip = ( cubeUV_r0 - roughness ) * ( cubeUV_m1 - cubeUV_m0 ) / ( cubeUV_r0 - cubeUV_r1 ) + cubeUV_m0;\n		} else if ( roughness >= cubeUV_r4 ) {\n			mip = ( cubeUV_r1 - roughness ) * ( cubeUV_m4 - cubeUV_m1 ) / ( cubeUV_r1 - cubeUV_r4 ) + cubeUV_m1;\n		} else if ( roughness >= cubeUV_r5 ) {\n			mip = ( cubeUV_r4 - roughness ) * ( cubeUV_m5 - cubeUV_m4 ) / ( cubeUV_r4 - cubeUV_r5 ) + cubeUV_m4;\n		} else if ( roughness >= cubeUV_r6 ) {\n			mip = ( cubeUV_r5 - roughness ) * ( cubeUV_m6 - cubeUV_m5 ) / ( cubeUV_r5 - cubeUV_r6 ) + cubeUV_m5;\n		} else {\n			mip = - 2.0 * log2( 1.16 * roughness );		}\n		return mip;\n	}\n	vec4 textureCubeUV( sampler2D envMap, vec3 sampleDir, float roughness ) {\n		float mip = clamp( roughnessToMip( roughness ), cubeUV_m0, CUBEUV_MAX_MIP );\n		float mipF = fract( mip );\n		float mipInt = floor( mip );\n		vec3 color0 = bilinearCubeUV( envMap, sampleDir, mipInt );\n		if ( mipF == 0.0 ) {\n			return vec4( color0, 1.0 );\n		} else {\n			vec3 color1 = bilinearCubeUV( envMap, sampleDir, mipInt + 1.0 );\n			return vec4( mix( color0, color1, mipF ), 1.0 );\n		}\n	}\n#endif",defaultnormal_vertex:"vec3 transformedNormal = objectNormal;\n#ifdef USE_INSTANCING\n	mat3 m = mat3( instanceMatrix );\n	transformedNormal /= vec3( dot( m[ 0 ], m[ 0 ] ), dot( m[ 1 ], m[ 1 ] ), dot( m[ 2 ], m[ 2 ] ) );\n	transformedNormal = m * transformedNormal;\n#endif\ntransformedNormal = normalMatrix * transformedNormal;\n#ifdef FLIP_SIDED\n	transformedNormal = - transformedNormal;\n#endif\n#ifdef USE_TANGENT\n	vec3 transformedTangent = ( modelViewMatrix * vec4( objectTangent, 0.0 ) ).xyz;\n	#ifdef FLIP_SIDED\n		transformedTangent = - transformedTangent;\n	#endif\n#endif",displacementmap_pars_vertex:"#ifdef USE_DISPLACEMENTMAP\n	uniform sampler2D displacementMap;\n	uniform float displacementScale;\n	uniform float displacementBias;\n#endif",displacementmap_vertex:"#ifdef USE_DISPLACEMENTMAP\n	transformed += normalize( objectNormal ) * ( texture2D( displacementMap, vDisplacementMapUv ).x * displacementScale + displacementBias );\n#endif",emissivemap_fragment:"#ifdef USE_EMISSIVEMAP\n	vec4 emissiveColor = texture2D( emissiveMap, vEmissiveMapUv );\n	totalEmissiveRadiance *= emissiveColor.rgb;\n#endif",emissivemap_pars_fragment:"#ifdef USE_EMISSIVEMAP\n	uniform sampler2D emissiveMap;\n#endif",colorspace_fragment:"gl_FragColor = linearToOutputTexel( gl_FragColor );",colorspace_pars_fragment:"\nconst mat3 LINEAR_SRGB_TO_LINEAR_DISPLAY_P3 = mat3(\n	vec3( 0.8224621, 0.177538, 0.0 ),\n	vec3( 0.0331941, 0.9668058, 0.0 ),\n	vec3( 0.0170827, 0.0723974, 0.9105199 )\n);\nconst mat3 LINEAR_DISPLAY_P3_TO_LINEAR_SRGB = mat3(\n	vec3( 1.2249401, - 0.2249404, 0.0 ),\n	vec3( - 0.0420569, 1.0420571, 0.0 ),\n	vec3( - 0.0196376, - 0.0786361, 1.0982735 )\n);\nvec4 LinearSRGBToLinearDisplayP3( in vec4 value ) {\n	return vec4( value.rgb * LINEAR_SRGB_TO_LINEAR_DISPLAY_P3, value.a );\n}\nvec4 LinearDisplayP3ToLinearSRGB( in vec4 value ) {\n	return vec4( value.rgb * LINEAR_DISPLAY_P3_TO_LINEAR_SRGB, value.a );\n}\nvec4 LinearTransferOETF( in vec4 value ) {\n	return value;\n}\nvec4 sRGBTransferOETF( in vec4 value ) {\n	return vec4( mix( pow( value.rgb, vec3( 0.41666 ) ) * 1.055 - vec3( 0.055 ), value.rgb * 12.92, vec3( lessThanEqual( value.rgb, vec3( 0.0031308 ) ) ) ), value.a );\n}\nvec4 LinearToLinear( in vec4 value ) {\n	return value;\n}\nvec4 LinearTosRGB( in vec4 value ) {\n	return sRGBTransferOETF( value );\n}",envmap_fragment:"#ifdef USE_ENVMAP\n	#ifdef ENV_WORLDPOS\n		vec3 cameraToFrag;\n		if ( isOrthographic ) {\n			cameraToFrag = normalize( vec3( - viewMatrix[ 0 ][ 2 ], - viewMatrix[ 1 ][ 2 ], - viewMatrix[ 2 ][ 2 ] ) );\n		} else {\n			cameraToFrag = normalize( vWorldPosition - cameraPosition );\n		}\n		vec3 worldNormal = inverseTransformDirection( normal, viewMatrix );\n		#ifdef ENVMAP_MODE_REFLECTION\n			vec3 reflectVec = reflect( cameraToFrag, worldNormal );\n		#else\n			vec3 reflectVec = refract( cameraToFrag, worldNormal, refractionRatio );\n		#endif\n	#else\n		vec3 reflectVec = vReflect;\n	#endif\n	#ifdef ENVMAP_TYPE_CUBE\n		vec4 envColor = textureCube( envMap, vec3( flipEnvMap * reflectVec.x, reflectVec.yz ) );\n	#else\n		vec4 envColor = vec4( 0.0 );\n	#endif\n	#ifdef ENVMAP_BLENDING_MULTIPLY\n		outgoingLight = mix( outgoingLight, outgoingLight * envColor.xyz, specularStrength * reflectivity );\n	#elif defined( ENVMAP_BLENDING_MIX )\n		outgoingLight = mix( outgoingLight, envColor.xyz, specularStrength * reflectivity );\n	#elif defined( ENVMAP_BLENDING_ADD )\n		outgoingLight += envColor.xyz * specularStrength * reflectivity;\n	#endif\n#endif",envmap_common_pars_fragment:"#ifdef USE_ENVMAP\n	uniform float envMapIntensity;\n	uniform float flipEnvMap;\n	#ifdef ENVMAP_TYPE_CUBE\n		uniform samplerCube envMap;\n	#else\n		uniform sampler2D envMap;\n	#endif\n	\n#endif",envmap_pars_fragment:"#ifdef USE_ENVMAP\n	uniform float reflectivity;\n	#if defined( USE_BUMPMAP ) || defined( USE_NORMALMAP ) || defined( PHONG ) || defined( LAMBERT )\n		#define ENV_WORLDPOS\n	#endif\n	#ifdef ENV_WORLDPOS\n		varying vec3 vWorldPosition;\n		uniform float refractionRatio;\n	#else\n		varying vec3 vReflect;\n	#endif\n#endif",envmap_pars_vertex:"#ifdef USE_ENVMAP\n	#if defined( USE_BUMPMAP ) || defined( USE_NORMALMAP ) || defined( PHONG ) || defined( LAMBERT )\n		#define ENV_WORLDPOS\n	#endif\n	#ifdef ENV_WORLDPOS\n		\n		varying vec3 vWorldPosition;\n	#else\n		varying vec3 vReflect;\n		uniform float refractionRatio;\n	#endif\n#endif",envmap_physical_pars_fragment:"#ifdef USE_ENVMAP\n	vec3 getIBLIrradiance( const in vec3 normal ) {\n		#ifdef ENVMAP_TYPE_CUBE_UV\n			vec3 worldNormal = inverseTransformDirection( normal, viewMatrix );\n			vec4 envMapColor = textureCubeUV( envMap, worldNormal, 1.0 );\n			return PI * envMapColor.rgb * envMapIntensity;\n		#else\n			return vec3( 0.0 );\n		#endif\n	}\n	vec3 getIBLRadiance( const in vec3 viewDir, const in vec3 normal, const in float roughness ) {\n		#ifdef ENVMAP_TYPE_CUBE_UV\n			vec3 reflectVec = reflect( - viewDir, normal );\n			reflectVec = normalize( mix( reflectVec, normal, roughness * roughness) );\n			reflectVec = inverseTransformDirection( reflectVec, viewMatrix );\n			vec4 envMapColor = textureCubeUV( envMap, reflectVec, roughness );\n			return envMapColor.rgb * envMapIntensity;\n		#else\n			return vec3( 0.0 );\n		#endif\n	}\n	#ifdef USE_ANISOTROPY\n		vec3 getIBLAnisotropyRadiance( const in vec3 viewDir, const in vec3 normal, const in float roughness, const in vec3 bitangent, const in float anisotropy ) {\n			#ifdef ENVMAP_TYPE_CUBE_UV\n				vec3 bentNormal = cross( bitangent, viewDir );\n				bentNormal = normalize( cross( bentNormal, bitangent ) );\n				bentNormal = normalize( mix( bentNormal, normal, pow2( pow2( 1.0 - anisotropy * ( 1.0 - roughness ) ) ) ) );\n				return getIBLRadiance( viewDir, bentNormal, roughness );\n			#else\n				return vec3( 0.0 );\n			#endif\n		}\n	#endif\n#endif",envmap_vertex:"#ifdef USE_ENVMAP\n	#ifdef ENV_WORLDPOS\n		vWorldPosition = worldPosition.xyz;\n	#else\n		vec3 cameraToVertex;\n		if ( isOrthographic ) {\n			cameraToVertex = normalize( vec3( - viewMatrix[ 0 ][ 2 ], - viewMatrix[ 1 ][ 2 ], - viewMatrix[ 2 ][ 2 ] ) );\n		} else {\n			cameraToVertex = normalize( worldPosition.xyz - cameraPosition );\n		}\n		vec3 worldNormal = inverseTransformDirection( transformedNormal, viewMatrix );\n		#ifdef ENVMAP_MODE_REFLECTION\n			vReflect = reflect( cameraToVertex, worldNormal );\n		#else\n			vReflect = refract( cameraToVertex, worldNormal, refractionRatio );\n		#endif\n	#endif\n#endif",fog_vertex:"#ifdef USE_FOG\n	vFogDepth = - mvPosition.z;\n#endif",fog_pars_vertex:"#ifdef USE_FOG\n	varying float vFogDepth;\n#endif",fog_fragment:"#ifdef USE_FOG\n	#ifdef FOG_EXP2\n		float fogFactor = 1.0 - exp( - fogDensity * fogDensity * vFogDepth * vFogDepth );\n	#else\n		float fogFactor = smoothstep( fogNear, fogFar, vFogDepth );\n	#endif\n	gl_FragColor.rgb = mix( gl_FragColor.rgb, fogColor, fogFactor );\n#endif",fog_pars_fragment:"#ifdef USE_FOG\n	uniform vec3 fogColor;\n	varying float vFogDepth;\n	#ifdef FOG_EXP2\n		uniform float fogDensity;\n	#else\n		uniform float fogNear;\n		uniform float fogFar;\n	#endif\n#endif",gradientmap_pars_fragment:"#ifdef USE_GRADIENTMAP\n	uniform sampler2D gradientMap;\n#endif\nvec3 getGradientIrradiance( vec3 normal, vec3 lightDirection ) {\n	float dotNL = dot( normal, lightDirection );\n	vec2 coord = vec2( dotNL * 0.5 + 0.5, 0.0 );\n	#ifdef USE_GRADIENTMAP\n		return vec3( texture2D( gradientMap, coord ).r );\n	#else\n		vec2 fw = fwidth( coord ) * 0.5;\n		return mix( vec3( 0.7 ), vec3( 1.0 ), smoothstep( 0.7 - fw.x, 0.7 + fw.x, coord.x ) );\n	#endif\n}",lightmap_fragment:"#ifdef USE_LIGHTMAP\n	vec4 lightMapTexel = texture2D( lightMap, vLightMapUv );\n	vec3 lightMapIrradiance = lightMapTexel.rgb * lightMapIntensity;\n	reflectedLight.indirectDiffuse += lightMapIrradiance;\n#endif",lightmap_pars_fragment:"#ifdef USE_LIGHTMAP\n	uniform sampler2D lightMap;\n	uniform float lightMapIntensity;\n#endif",lights_lambert_fragment:"LambertMaterial material;\nmaterial.diffuseColor = diffuseColor.rgb;\nmaterial.specularStrength = specularStrength;",lights_lambert_pars_fragment:"varying vec3 vViewPosition;\nstruct LambertMaterial {\n	vec3 diffuseColor;\n	float specularStrength;\n};\nvoid RE_Direct_Lambert( const in IncidentLight directLight, const in vec3 geometryPosition, const in vec3 geometryNormal, const in vec3 geometryViewDir, const in vec3 geometryClearcoatNormal, const in LambertMaterial material, inout ReflectedLight reflectedLight ) {\n	float dotNL = saturate( dot( geometryNormal, directLight.direction ) );\n	vec3 irradiance = dotNL * directLight.color;\n	reflectedLight.directDiffuse += irradiance * BRDF_Lambert( material.diffuseColor );\n}\nvoid RE_IndirectDiffuse_Lambert( const in vec3 irradiance, const in vec3 geometryPosition, const in vec3 geometryNormal, const in vec3 geometryViewDir, const in vec3 geometryClearcoatNormal, const in LambertMaterial material, inout ReflectedLight reflectedLight ) {\n	reflectedLight.indirectDiffuse += irradiance * BRDF_Lambert( material.diffuseColor );\n}\n#define RE_Direct				RE_Direct_Lambert\n#define RE_IndirectDiffuse		RE_IndirectDiffuse_Lambert",lights_pars_begin:"uniform bool receiveShadow;\nuniform vec3 ambientLightColor;\n#if defined( USE_LIGHT_PROBES )\n	uniform vec3 lightProbe[ 9 ];\n#endif\nvec3 shGetIrradianceAt( in vec3 normal, in vec3 shCoefficients[ 9 ] ) {\n	float x = normal.x, y = normal.y, z = normal.z;\n	vec3 result = shCoefficients[ 0 ] * 0.886227;\n	result += shCoefficients[ 1 ] * 2.0 * 0.511664 * y;\n	result += shCoefficients[ 2 ] * 2.0 * 0.511664 * z;\n	result += shCoefficients[ 3 ] * 2.0 * 0.511664 * x;\n	result += shCoefficients[ 4 ] * 2.0 * 0.429043 * x * y;\n	result += shCoefficients[ 5 ] * 2.0 * 0.429043 * y * z;\n	result += shCoefficients[ 6 ] * ( 0.743125 * z * z - 0.247708 );\n	result += shCoefficients[ 7 ] * 2.0 * 0.429043 * x * z;\n	result += shCoefficients[ 8 ] * 0.429043 * ( x * x - y * y );\n	return result;\n}\nvec3 getLightProbeIrradiance( const in vec3 lightProbe[ 9 ], const in vec3 normal ) {\n	vec3 worldNormal = inverseTransformDirection( normal, viewMatrix );\n	vec3 irradiance = shGetIrradianceAt( worldNormal, lightProbe );\n	return irradiance;\n}\nvec3 getAmbientLightIrradiance( const in vec3 ambientLightColor ) {\n	vec3 irradiance = ambientLightColor;\n	return irradiance;\n}\nfloat getDistanceAttenuation( const in float lightDistance, const in float cutoffDistance, const in float decayExponent ) {\n	#if defined ( LEGACY_LIGHTS )\n		if ( cutoffDistance > 0.0 && decayExponent > 0.0 ) {\n			return pow( saturate( - lightDistance / cutoffDistance + 1.0 ), decayExponent );\n		}\n		return 1.0;\n	#else\n		float distanceFalloff = 1.0 / max( pow( lightDistance, decayExponent ), 0.01 );\n		if ( cutoffDistance > 0.0 ) {\n			distanceFalloff *= pow2( saturate( 1.0 - pow4( lightDistance / cutoffDistance ) ) );\n		}\n		return distanceFalloff;\n	#endif\n}\nfloat getSpotAttenuation( const in float coneCosine, const in float penumbraCosine, const in float angleCosine ) {\n	return smoothstep( coneCosine, penumbraCosine, angleCosine );\n}\n#if NUM_DIR_LIGHTS > 0\n	struct DirectionalLight {\n		vec3 direction;\n		vec3 color;\n	};\n	uniform DirectionalLight directionalLights[ NUM_DIR_LIGHTS ];\n	void getDirectionalLightInfo( const in DirectionalLight directionalLight, out IncidentLight light ) {\n		light.color = directionalLight.color;\n		light.direction = directionalLight.direction;\n		light.visible = true;\n	}\n#endif\n#if NUM_POINT_LIGHTS > 0\n	struct PointLight {\n		vec3 position;\n		vec3 color;\n		float distance;\n		float decay;\n	};\n	uniform PointLight pointLights[ NUM_POINT_LIGHTS ];\n	void getPointLightInfo( const in PointLight pointLight, const in vec3 geometryPosition, out IncidentLight light ) {\n		vec3 lVector = pointLight.position - geometryPosition;\n		light.direction = normalize( lVector );\n		float lightDistance = length( lVector );\n		light.color = pointLight.color;\n		light.color *= getDistanceAttenuation( lightDistance, pointLight.distance, pointLight.decay );\n		light.visible = ( light.color != vec3( 0.0 ) );\n	}\n#endif\n#if NUM_SPOT_LIGHTS > 0\n	struct SpotLight {\n		vec3 position;\n		vec3 direction;\n		vec3 color;\n		float distance;\n		float decay;\n		float coneCos;\n		float penumbraCos;\n	};\n	uniform SpotLight spotLights[ NUM_SPOT_LIGHTS ];\n	void getSpotLightInfo( const in SpotLight spotLight, const in vec3 geometryPosition, out IncidentLight light ) {\n		vec3 lVector = spotLight.position - geometryPosition;\n		light.direction = normalize( lVector );\n		float angleCos = dot( light.direction, spotLight.direction );\n		float spotAttenuation = getSpotAttenuation( spotLight.coneCos, spotLight.penumbraCos, angleCos );\n		if ( spotAttenuation > 0.0 ) {\n			float lightDistance = length( lVector );\n			light.color = spotLight.color * spotAttenuation;\n			light.color *= getDistanceAttenuation( lightDistance, spotLight.distance, spotLight.decay );\n			light.visible = ( light.color != vec3( 0.0 ) );\n		} else {\n			light.color = vec3( 0.0 );\n			light.visible = false;\n		}\n	}\n#endif\n#if NUM_RECT_AREA_LIGHTS > 0\n	struct RectAreaLight {\n		vec3 color;\n		vec3 position;\n		vec3 halfWidth;\n		vec3 halfHeight;\n	};\n	uniform sampler2D ltc_1;	uniform sampler2D ltc_2;\n	uniform RectAreaLight rectAreaLights[ NUM_RECT_AREA_LIGHTS ];\n#endif\n#if NUM_HEMI_LIGHTS > 0\n	struct HemisphereLight {\n		vec3 direction;\n		vec3 skyColor;\n		vec3 groundColor;\n	};\n	uniform HemisphereLight hemisphereLights[ NUM_HEMI_LIGHTS ];\n	vec3 getHemisphereLightIrradiance( const in HemisphereLight hemiLight, const in vec3 normal ) {\n		float dotNL = dot( normal, hemiLight.direction );\n		float hemiDiffuseWeight = 0.5 * dotNL + 0.5;\n		vec3 irradiance = mix( hemiLight.groundColor, hemiLight.skyColor, hemiDiffuseWeight );\n		return irradiance;\n	}\n#endif",lights_toon_fragment:"ToonMaterial material;\nmaterial.diffuseColor = diffuseColor.rgb;",lights_toon_pars_fragment:"varying vec3 vViewPosition;\nstruct ToonMaterial {\n	vec3 diffuseColor;\n};\nvoid RE_Direct_Toon( const in IncidentLight directLight, const in vec3 geometryPosition, const in vec3 geometryNormal, const in vec3 geometryViewDir, const in vec3 geometryClearcoatNormal, const in ToonMaterial material, inout ReflectedLight reflectedLight ) {\n	vec3 irradiance = getGradientIrradiance( geometryNormal, directLight.direction ) * directLight.color;\n	reflectedLight.directDiffuse += irradiance * BRDF_Lambert( material.diffuseColor );\n}\nvoid RE_IndirectDiffuse_Toon( const in vec3 irradiance, const in vec3 geometryPosition, const in vec3 geometryNormal, const in vec3 geometryViewDir, const in vec3 geometryClearcoatNormal, const in ToonMaterial material, inout ReflectedLight reflectedLight ) {\n	reflectedLight.indirectDiffuse += irradiance * BRDF_Lambert( material.diffuseColor );\n}\n#define RE_Direct				RE_Direct_Toon\n#define RE_IndirectDiffuse		RE_IndirectDiffuse_Toon",lights_phong_fragment:"BlinnPhongMaterial material;\nmaterial.diffuseColor = diffuseColor.rgb;\nmaterial.specularColor = specular;\nmaterial.specularShininess = shininess;\nmaterial.specularStrength = specularStrength;",lights_phong_pars_fragment:"varying vec3 vViewPosition;\nstruct BlinnPhongMaterial {\n	vec3 diffuseColor;\n	vec3 specularColor;\n	float specularShininess;\n	float specularStrength;\n};\nvoid RE_Direct_BlinnPhong( const in IncidentLight directLight, const in vec3 geometryPosition, const in vec3 geometryNormal, const in vec3 geometryViewDir, const in vec3 geometryClearcoatNormal, const in BlinnPhongMaterial material, inout ReflectedLight reflectedLight ) {\n	float dotNL = saturate( dot( geometryNormal, directLight.direction ) );\n	vec3 irradiance = dotNL * directLight.color;\n	reflectedLight.directDiffuse += irradiance * BRDF_Lambert( material.diffuseColor );\n	reflectedLight.directSpecular += irradiance * BRDF_BlinnPhong( directLight.direction, geometryViewDir, geometryNormal, material.specularColor, material.specularShininess ) * material.specularStrength;\n}\nvoid RE_IndirectDiffuse_BlinnPhong( const in vec3 irradiance, const in vec3 geometryPosition, const in vec3 geometryNormal, const in vec3 geometryViewDir, const in vec3 geometryClearcoatNormal, const in BlinnPhongMaterial material, inout ReflectedLight reflectedLight ) {\n	reflectedLight.indirectDiffuse += irradiance * BRDF_Lambert( material.diffuseColor );\n}\n#define RE_Direct				RE_Direct_BlinnPhong\n#define RE_IndirectDiffuse		RE_IndirectDiffuse_BlinnPhong",lights_physical_fragment:"PhysicalMaterial material;\nmaterial.diffuseColor = diffuseColor.rgb * ( 1.0 - metalnessFactor );\nvec3 dxy = max( abs( dFdx( nonPerturbedNormal ) ), abs( dFdy( nonPerturbedNormal ) ) );\nfloat geometryRoughness = max( max( dxy.x, dxy.y ), dxy.z );\nmaterial.roughness = max( roughnessFactor, 0.0525 );material.roughness += geometryRoughness;\nmaterial.roughness = min( material.roughness, 1.0 );\n#ifdef IOR\n	material.ior = ior;\n	#ifdef USE_SPECULAR\n		float specularIntensityFactor = specularIntensity;\n		vec3 specularColorFactor = specularColor;\n		#ifdef USE_SPECULAR_COLORMAP\n			specularColorFactor *= texture2D( specularColorMap, vSpecularColorMapUv ).rgb;\n		#endif\n		#ifdef USE_SPECULAR_INTENSITYMAP\n			specularIntensityFactor *= texture2D( specularIntensityMap, vSpecularIntensityMapUv ).a;\n		#endif\n		material.specularF90 = mix( specularIntensityFactor, 1.0, metalnessFactor );\n	#else\n		float specularIntensityFactor = 1.0;\n		vec3 specularColorFactor = vec3( 1.0 );\n		material.specularF90 = 1.0;\n	#endif\n	material.specularColor = mix( min( pow2( ( material.ior - 1.0 ) / ( material.ior + 1.0 ) ) * specularColorFactor, vec3( 1.0 ) ) * specularIntensityFactor, diffuseColor.rgb, metalnessFactor );\n#else\n	material.specularColor = mix( vec3( 0.04 ), diffuseColor.rgb, metalnessFactor );\n	material.specularF90 = 1.0;\n#endif\n#ifdef USE_CLEARCOAT\n	material.clearcoat = clearcoat;\n	material.clearcoatRoughness = clearcoatRoughness;\n	material.clearcoatF0 = vec3( 0.04 );\n	material.clearcoatF90 = 1.0;\n	#ifdef USE_CLEARCOATMAP\n		material.clearcoat *= texture2D( clearcoatMap, vClearcoatMapUv ).x;\n	#endif\n	#ifdef USE_CLEARCOAT_ROUGHNESSMAP\n		material.clearcoatRoughness *= texture2D( clearcoatRoughnessMap, vClearcoatRoughnessMapUv ).y;\n	#endif\n	material.clearcoat = saturate( material.clearcoat );	material.clearcoatRoughness = max( material.clearcoatRoughness, 0.0525 );\n	material.clearcoatRoughness += geometryRoughness;\n	material.clearcoatRoughness = min( material.clearcoatRoughness, 1.0 );\n#endif\n#ifdef USE_IRIDESCENCE\n	material.iridescence = iridescence;\n	material.iridescenceIOR = iridescenceIOR;\n	#ifdef USE_IRIDESCENCEMAP\n		material.iridescence *= texture2D( iridescenceMap, vIridescenceMapUv ).r;\n	#endif\n	#ifdef USE_IRIDESCENCE_THICKNESSMAP\n		material.iridescenceThickness = (iridescenceThicknessMaximum - iridescenceThicknessMinimum) * texture2D( iridescenceThicknessMap, vIridescenceThicknessMapUv ).g + iridescenceThicknessMinimum;\n	#else\n		material.iridescenceThickness = iridescenceThicknessMaximum;\n	#endif\n#endif\n#ifdef USE_SHEEN\n	material.sheenColor = sheenColor;\n	#ifdef USE_SHEEN_COLORMAP\n		material.sheenColor *= texture2D( sheenColorMap, vSheenColorMapUv ).rgb;\n	#endif\n	material.sheenRoughness = clamp( sheenRoughness, 0.07, 1.0 );\n	#ifdef USE_SHEEN_ROUGHNESSMAP\n		material.sheenRoughness *= texture2D( sheenRoughnessMap, vSheenRoughnessMapUv ).a;\n	#endif\n#endif\n#ifdef USE_ANISOTROPY\n	#ifdef USE_ANISOTROPYMAP\n		mat2 anisotropyMat = mat2( anisotropyVector.x, anisotropyVector.y, - anisotropyVector.y, anisotropyVector.x );\n		vec3 anisotropyPolar = texture2D( anisotropyMap, vAnisotropyMapUv ).rgb;\n		vec2 anisotropyV = anisotropyMat * normalize( 2.0 * anisotropyPolar.rg - vec2( 1.0 ) ) * anisotropyPolar.b;\n	#else\n		vec2 anisotropyV = anisotropyVector;\n	#endif\n	material.anisotropy = length( anisotropyV );\n	anisotropyV /= material.anisotropy;\n	material.anisotropy = saturate( material.anisotropy );\n	material.alphaT = mix( pow2( material.roughness ), 1.0, pow2( material.anisotropy ) );\n	material.anisotropyT = tbn[ 0 ] * anisotropyV.x - tbn[ 1 ] * anisotropyV.y;\n	material.anisotropyB = tbn[ 1 ] * anisotropyV.x + tbn[ 0 ] * anisotropyV.y;\n#endif",lights_physical_pars_fragment:"struct PhysicalMaterial {\n	vec3 diffuseColor;\n	float roughness;\n	vec3 specularColor;\n	float specularF90;\n	#ifdef USE_CLEARCOAT\n		float clearcoat;\n		float clearcoatRoughness;\n		vec3 clearcoatF0;\n		float clearcoatF90;\n	#endif\n	#ifdef USE_IRIDESCENCE\n		float iridescence;\n		float iridescenceIOR;\n		float iridescenceThickness;\n		vec3 iridescenceFresnel;\n		vec3 iridescenceF0;\n	#endif\n	#ifdef USE_SHEEN\n		vec3 sheenColor;\n		float sheenRoughness;\n	#endif\n	#ifdef IOR\n		float ior;\n	#endif\n	#ifdef USE_TRANSMISSION\n		float transmission;\n		float transmissionAlpha;\n		float thickness;\n		float attenuationDistance;\n		vec3 attenuationColor;\n	#endif\n	#ifdef USE_ANISOTROPY\n		float anisotropy;\n		float alphaT;\n		vec3 anisotropyT;\n		vec3 anisotropyB;\n	#endif\n};\nvec3 clearcoatSpecularDirect = vec3( 0.0 );\nvec3 clearcoatSpecularIndirect = vec3( 0.0 );\nvec3 sheenSpecularDirect = vec3( 0.0 );\nvec3 sheenSpecularIndirect = vec3(0.0 );\nvec3 Schlick_to_F0( const in vec3 f, const in float f90, const in float dotVH ) {\n    float x = clamp( 1.0 - dotVH, 0.0, 1.0 );\n    float x2 = x * x;\n    float x5 = clamp( x * x2 * x2, 0.0, 0.9999 );\n    return ( f - vec3( f90 ) * x5 ) / ( 1.0 - x5 );\n}\nfloat V_GGX_SmithCorrelated( const in float alpha, const in float dotNL, const in float dotNV ) {\n	float a2 = pow2( alpha );\n	float gv = dotNL * sqrt( a2 + ( 1.0 - a2 ) * pow2( dotNV ) );\n	float gl = dotNV * sqrt( a2 + ( 1.0 - a2 ) * pow2( dotNL ) );\n	return 0.5 / max( gv + gl, EPSILON );\n}\nfloat D_GGX( const in float alpha, const in float dotNH ) {\n	float a2 = pow2( alpha );\n	float denom = pow2( dotNH ) * ( a2 - 1.0 ) + 1.0;\n	return RECIPROCAL_PI * a2 / pow2( denom );\n}\n#ifdef USE_ANISOTROPY\n	float V_GGX_SmithCorrelated_Anisotropic( const in float alphaT, const in float alphaB, const in float dotTV, const in float dotBV, const in float dotTL, const in float dotBL, const in float dotNV, const in float dotNL ) {\n		float gv = dotNL * length( vec3( alphaT * dotTV, alphaB * dotBV, dotNV ) );\n		float gl = dotNV * length( vec3( alphaT * dotTL, alphaB * dotBL, dotNL ) );\n		float v = 0.5 / ( gv + gl );\n		return saturate(v);\n	}\n	float D_GGX_Anisotropic( const in float alphaT, const in float alphaB, const in float dotNH, const in float dotTH, const in float dotBH ) {\n		float a2 = alphaT * alphaB;\n		highp vec3 v = vec3( alphaB * dotTH, alphaT * dotBH, a2 * dotNH );\n		highp float v2 = dot( v, v );\n		float w2 = a2 / v2;\n		return RECIPROCAL_PI * a2 * pow2 ( w2 );\n	}\n#endif\n#ifdef USE_CLEARCOAT\n	vec3 BRDF_GGX_Clearcoat( const in vec3 lightDir, const in vec3 viewDir, const in vec3 normal, const in PhysicalMaterial material) {\n		vec3 f0 = material.clearcoatF0;\n		float f90 = material.clearcoatF90;\n		float roughness = material.clearcoatRoughness;\n		float alpha = pow2( roughness );\n		vec3 halfDir = normalize( lightDir + viewDir );\n		float dotNL = saturate( dot( normal, lightDir ) );\n		float dotNV = saturate( dot( normal, viewDir ) );\n		float dotNH = saturate( dot( normal, halfDir ) );\n		float dotVH = saturate( dot( viewDir, halfDir ) );\n		vec3 F = F_Schlick( f0, f90, dotVH );\n		float V = V_GGX_SmithCorrelated( alpha, dotNL, dotNV );\n		float D = D_GGX( alpha, dotNH );\n		return F * ( V * D );\n	}\n#endif\nvec3 BRDF_GGX( const in vec3 lightDir, const in vec3 viewDir, const in vec3 normal, const in PhysicalMaterial material ) {\n	vec3 f0 = material.specularColor;\n	float f90 = material.specularF90;\n	float roughness = material.roughness;\n	float alpha = pow2( roughness );\n	vec3 halfDir = normalize( lightDir + viewDir );\n	float dotNL = saturate( dot( normal, lightDir ) );\n	float dotNV = saturate( dot( normal, viewDir ) );\n	float dotNH = saturate( dot( normal, halfDir ) );\n	float dotVH = saturate( dot( viewDir, halfDir ) );\n	vec3 F = F_Schlick( f0, f90, dotVH );\n	#ifdef USE_IRIDESCENCE\n		F = mix( F, material.iridescenceFresnel, material.iridescence );\n	#endif\n	#ifdef USE_ANISOTROPY\n		float dotTL = dot( material.anisotropyT, lightDir );\n		float dotTV = dot( material.anisotropyT, viewDir );\n		float dotTH = dot( material.anisotropyT, halfDir );\n		float dotBL = dot( material.anisotropyB, lightDir );\n		float dotBV = dot( material.anisotropyB, viewDir );\n		float dotBH = dot( material.anisotropyB, halfDir );\n		float V = V_GGX_SmithCorrelated_Anisotropic( material.alphaT, alpha, dotTV, dotBV, dotTL, dotBL, dotNV, dotNL );\n		float D = D_GGX_Anisotropic( material.alphaT, alpha, dotNH, dotTH, dotBH );\n	#else\n		float V = V_GGX_SmithCorrelated( alpha, dotNL, dotNV );\n		float D = D_GGX( alpha, dotNH );\n	#endif\n	return F * ( V * D );\n}\nvec2 LTC_Uv( const in vec3 N, const in vec3 V, const in float roughness ) {\n	const float LUT_SIZE = 64.0;\n	const float LUT_SCALE = ( LUT_SIZE - 1.0 ) / LUT_SIZE;\n	const float LUT_BIAS = 0.5 / LUT_SIZE;\n	float dotNV = saturate( dot( N, V ) );\n	vec2 uv = vec2( roughness, sqrt( 1.0 - dotNV ) );\n	uv = uv * LUT_SCALE + LUT_BIAS;\n	return uv;\n}\nfloat LTC_ClippedSphereFormFactor( const in vec3 f ) {\n	float l = length( f );\n	return max( ( l * l + f.z ) / ( l + 1.0 ), 0.0 );\n}\nvec3 LTC_EdgeVectorFormFactor( const in vec3 v1, const in vec3 v2 ) {\n	float x = dot( v1, v2 );\n	float y = abs( x );\n	float a = 0.8543985 + ( 0.4965155 + 0.0145206 * y ) * y;\n	float b = 3.4175940 + ( 4.1616724 + y ) * y;\n	float v = a / b;\n	float theta_sintheta = ( x > 0.0 ) ? v : 0.5 * inversesqrt( max( 1.0 - x * x, 1e-7 ) ) - v;\n	return cross( v1, v2 ) * theta_sintheta;\n}\nvec3 LTC_Evaluate( const in vec3 N, const in vec3 V, const in vec3 P, const in mat3 mInv, const in vec3 rectCoords[ 4 ] ) {\n	vec3 v1 = rectCoords[ 1 ] - rectCoords[ 0 ];\n	vec3 v2 = rectCoords[ 3 ] - rectCoords[ 0 ];\n	vec3 lightNormal = cross( v1, v2 );\n	if( dot( lightNormal, P - rectCoords[ 0 ] ) < 0.0 ) return vec3( 0.0 );\n	vec3 T1, T2;\n	T1 = normalize( V - N * dot( V, N ) );\n	T2 = - cross( N, T1 );\n	mat3 mat = mInv * transposeMat3( mat3( T1, T2, N ) );\n	vec3 coords[ 4 ];\n	coords[ 0 ] = mat * ( rectCoords[ 0 ] - P );\n	coords[ 1 ] = mat * ( rectCoords[ 1 ] - P );\n	coords[ 2 ] = mat * ( rectCoords[ 2 ] - P );\n	coords[ 3 ] = mat * ( rectCoords[ 3 ] - P );\n	coords[ 0 ] = normalize( coords[ 0 ] );\n	coords[ 1 ] = normalize( coords[ 1 ] );\n	coords[ 2 ] = normalize( coords[ 2 ] );\n	coords[ 3 ] = normalize( coords[ 3 ] );\n	vec3 vectorFormFactor = vec3( 0.0 );\n	vectorFormFactor += LTC_EdgeVectorFormFactor( coords[ 0 ], coords[ 1 ] );\n	vectorFormFactor += LTC_EdgeVectorFormFactor( coords[ 1 ], coords[ 2 ] );\n	vectorFormFactor += LTC_EdgeVectorFormFactor( coords[ 2 ], coords[ 3 ] );\n	vectorFormFactor += LTC_EdgeVectorFormFactor( coords[ 3 ], coords[ 0 ] );\n	float result = LTC_ClippedSphereFormFactor( vectorFormFactor );\n	return vec3( result );\n}\n#if defined( USE_SHEEN )\nfloat D_Charlie( float roughness, float dotNH ) {\n	float alpha = pow2( roughness );\n	float invAlpha = 1.0 / alpha;\n	float cos2h = dotNH * dotNH;\n	float sin2h = max( 1.0 - cos2h, 0.0078125 );\n	return ( 2.0 + invAlpha ) * pow( sin2h, invAlpha * 0.5 ) / ( 2.0 * PI );\n}\nfloat V_Neubelt( float dotNV, float dotNL ) {\n	return saturate( 1.0 / ( 4.0 * ( dotNL + dotNV - dotNL * dotNV ) ) );\n}\nvec3 BRDF_Sheen( const in vec3 lightDir, const in vec3 viewDir, const in vec3 normal, vec3 sheenColor, const in float sheenRoughness ) {\n	vec3 halfDir = normalize( lightDir + viewDir );\n	float dotNL = saturate( dot( normal, lightDir ) );\n	float dotNV = saturate( dot( normal, viewDir ) );\n	float dotNH = saturate( dot( normal, halfDir ) );\n	float D = D_Charlie( sheenRoughness, dotNH );\n	float V = V_Neubelt( dotNV, dotNL );\n	return sheenColor * ( D * V );\n}\n#endif\nfloat IBLSheenBRDF( const in vec3 normal, const in vec3 viewDir, const in float roughness ) {\n	float dotNV = saturate( dot( normal, viewDir ) );\n	float r2 = roughness * roughness;\n	float a = roughness < 0.25 ? -339.2 * r2 + 161.4 * roughness - 25.9 : -8.48 * r2 + 14.3 * roughness - 9.95;\n	float b = roughness < 0.25 ? 44.0 * r2 - 23.7 * roughness + 3.26 : 1.97 * r2 - 3.27 * roughness + 0.72;\n	float DG = exp( a * dotNV + b ) + ( roughness < 0.25 ? 0.0 : 0.1 * ( roughness - 0.25 ) );\n	return saturate( DG * RECIPROCAL_PI );\n}\nvec2 DFGApprox( const in vec3 normal, const in vec3 viewDir, const in float roughness ) {\n	float dotNV = saturate( dot( normal, viewDir ) );\n	const vec4 c0 = vec4( - 1, - 0.0275, - 0.572, 0.022 );\n	const vec4 c1 = vec4( 1, 0.0425, 1.04, - 0.04 );\n	vec4 r = roughness * c0 + c1;\n	float a004 = min( r.x * r.x, exp2( - 9.28 * dotNV ) ) * r.x + r.y;\n	vec2 fab = vec2( - 1.04, 1.04 ) * a004 + r.zw;\n	return fab;\n}\nvec3 EnvironmentBRDF( const in vec3 normal, const in vec3 viewDir, const in vec3 specularColor, const in float specularF90, const in float roughness ) {\n	vec2 fab = DFGApprox( normal, viewDir, roughness );\n	return specularColor * fab.x + specularF90 * fab.y;\n}\n#ifdef USE_IRIDESCENCE\nvoid computeMultiscatteringIridescence( const in vec3 normal, const in vec3 viewDir, const in vec3 specularColor, const in float specularF90, const in float iridescence, const in vec3 iridescenceF0, const in float roughness, inout vec3 singleScatter, inout vec3 multiScatter ) {\n#else\nvoid computeMultiscattering( const in vec3 normal, const in vec3 viewDir, const in vec3 specularColor, const in float specularF90, const in float roughness, inout vec3 singleScatter, inout vec3 multiScatter ) {\n#endif\n	vec2 fab = DFGApprox( normal, viewDir, roughness );\n	#ifdef USE_IRIDESCENCE\n		vec3 Fr = mix( specularColor, iridescenceF0, iridescence );\n	#else\n		vec3 Fr = specularColor;\n	#endif\n	vec3 FssEss = Fr * fab.x + specularF90 * fab.y;\n	float Ess = fab.x + fab.y;\n	float Ems = 1.0 - Ess;\n	vec3 Favg = Fr + ( 1.0 - Fr ) * 0.047619;	vec3 Fms = FssEss * Favg / ( 1.0 - Ems * Favg );\n	singleScatter += FssEss;\n	multiScatter += Fms * Ems;\n}\n#if NUM_RECT_AREA_LIGHTS > 0\n	void RE_Direct_RectArea_Physical( const in RectAreaLight rectAreaLight, const in vec3 geometryPosition, const in vec3 geometryNormal, const in vec3 geometryViewDir, const in vec3 geometryClearcoatNormal, const in PhysicalMaterial material, inout ReflectedLight reflectedLight ) {\n		vec3 normal = geometryNormal;\n		vec3 viewDir = geometryViewDir;\n		vec3 position = geometryPosition;\n		vec3 lightPos = rectAreaLight.position;\n		vec3 halfWidth = rectAreaLight.halfWidth;\n		vec3 halfHeight = rectAreaLight.halfHeight;\n		vec3 lightColor = rectAreaLight.color;\n		float roughness = material.roughness;\n		vec3 rectCoords[ 4 ];\n		rectCoords[ 0 ] = lightPos + halfWidth - halfHeight;		rectCoords[ 1 ] = lightPos - halfWidth - halfHeight;\n		rectCoords[ 2 ] = lightPos - halfWidth + halfHeight;\n		rectCoords[ 3 ] = lightPos + halfWidth + halfHeight;\n		vec2 uv = LTC_Uv( normal, viewDir, roughness );\n		vec4 t1 = texture2D( ltc_1, uv );\n		vec4 t2 = texture2D( ltc_2, uv );\n		mat3 mInv = mat3(\n			vec3( t1.x, 0, t1.y ),\n			vec3(    0, 1,    0 ),\n			vec3( t1.z, 0, t1.w )\n		);\n		vec3 fresnel = ( material.specularColor * t2.x + ( vec3( 1.0 ) - material.specularColor ) * t2.y );\n		reflectedLight.directSpecular += lightColor * fresnel * LTC_Evaluate( normal, viewDir, position, mInv, rectCoords );\n		reflectedLight.directDiffuse += lightColor * material.diffuseColor * LTC_Evaluate( normal, viewDir, position, mat3( 1.0 ), rectCoords );\n	}\n#endif\nvoid RE_Direct_Physical( const in IncidentLight directLight, const in vec3 geometryPosition, const in vec3 geometryNormal, const in vec3 geometryViewDir, const in vec3 geometryClearcoatNormal, const in PhysicalMaterial material, inout ReflectedLight reflectedLight ) {\n	float dotNL = saturate( dot( geometryNormal, directLight.direction ) );\n	vec3 irradiance = dotNL * directLight.color;\n	#ifdef USE_CLEARCOAT\n		float dotNLcc = saturate( dot( geometryClearcoatNormal, directLight.direction ) );\n		vec3 ccIrradiance = dotNLcc * directLight.color;\n		clearcoatSpecularDirect += ccIrradiance * BRDF_GGX_Clearcoat( directLight.direction, geometryViewDir, geometryClearcoatNormal, material );\n	#endif\n	#ifdef USE_SHEEN\n		sheenSpecularDirect += irradiance * BRDF_Sheen( directLight.direction, geometryViewDir, geometryNormal, material.sheenColor, material.sheenRoughness );\n	#endif\n	reflectedLight.directSpecular += irradiance * BRDF_GGX( directLight.direction, geometryViewDir, geometryNormal, material );\n	reflectedLight.directDiffuse += irradiance * BRDF_Lambert( material.diffuseColor );\n}\nvoid RE_IndirectDiffuse_Physical( const in vec3 irradiance, const in vec3 geometryPosition, const in vec3 geometryNormal, const in vec3 geometryViewDir, const in vec3 geometryClearcoatNormal, const in PhysicalMaterial material, inout ReflectedLight reflectedLight ) {\n	reflectedLight.indirectDiffuse += irradiance * BRDF_Lambert( material.diffuseColor );\n}\nvoid RE_IndirectSpecular_Physical( const in vec3 radiance, const in vec3 irradiance, const in vec3 clearcoatRadiance, const in vec3 geometryPosition, const in vec3 geometryNormal, const in vec3 geometryViewDir, const in vec3 geometryClearcoatNormal, const in PhysicalMaterial material, inout ReflectedLight reflectedLight) {\n	#ifdef USE_CLEARCOAT\n		clearcoatSpecularIndirect += clearcoatRadiance * EnvironmentBRDF( geometryClearcoatNormal, geometryViewDir, material.clearcoatF0, material.clearcoatF90, material.clearcoatRoughness );\n	#endif\n	#ifdef USE_SHEEN\n		sheenSpecularIndirect += irradiance * material.sheenColor * IBLSheenBRDF( geometryNormal, geometryViewDir, material.sheenRoughness );\n	#endif\n	vec3 singleScattering = vec3( 0.0 );\n	vec3 multiScattering = vec3( 0.0 );\n	vec3 cosineWeightedIrradiance = irradiance * RECIPROCAL_PI;\n	#ifdef USE_IRIDESCENCE\n		computeMultiscatteringIridescence( geometryNormal, geometryViewDir, material.specularColor, material.specularF90, material.iridescence, material.iridescenceFresnel, material.roughness, singleScattering, multiScattering );\n	#else\n		computeMultiscattering( geometryNormal, geometryViewDir, material.specularColor, material.specularF90, material.roughness, singleScattering, multiScattering );\n	#endif\n	vec3 totalScattering = singleScattering + multiScattering;\n	vec3 diffuse = material.diffuseColor * ( 1.0 - max( max( totalScattering.r, totalScattering.g ), totalScattering.b ) );\n	reflectedLight.indirectSpecular += radiance * singleScattering;\n	reflectedLight.indirectSpecular += multiScattering * cosineWeightedIrradiance;\n	reflectedLight.indirectDiffuse += diffuse * cosineWeightedIrradiance;\n}\n#define RE_Direct				RE_Direct_Physical\n#define RE_Direct_RectArea		RE_Direct_RectArea_Physical\n#define RE_IndirectDiffuse		RE_IndirectDiffuse_Physical\n#define RE_IndirectSpecular		RE_IndirectSpecular_Physical\nfloat computeSpecularOcclusion( const in float dotNV, const in float ambientOcclusion, const in float roughness ) {\n	return saturate( pow( dotNV + ambientOcclusion, exp2( - 16.0 * roughness - 1.0 ) ) - 1.0 + ambientOcclusion );\n}",lights_fragment_begin:"\nvec3 geometryPosition = - vViewPosition;\nvec3 geometryNormal = normal;\nvec3 geometryViewDir = ( isOrthographic ) ? vec3( 0, 0, 1 ) : normalize( vViewPosition );\nvec3 geometryClearcoatNormal = vec3( 0.0 );\n#ifdef USE_CLEARCOAT\n	geometryClearcoatNormal = clearcoatNormal;\n#endif\n#ifdef USE_IRIDESCENCE\n	float dotNVi = saturate( dot( normal, geometryViewDir ) );\n	if ( material.iridescenceThickness == 0.0 ) {\n		material.iridescence = 0.0;\n	} else {\n		material.iridescence = saturate( material.iridescence );\n	}\n	if ( material.iridescence > 0.0 ) {\n		material.iridescenceFresnel = evalIridescence( 1.0, material.iridescenceIOR, dotNVi, material.iridescenceThickness, material.specularColor );\n		material.iridescenceF0 = Schlick_to_F0( material.iridescenceFresnel, 1.0, dotNVi );\n	}\n#endif\nIncidentLight directLight;\n#if ( NUM_POINT_LIGHTS > 0 ) && defined( RE_Direct )\n	PointLight pointLight;\n	#if defined( USE_SHADOWMAP ) && NUM_POINT_LIGHT_SHADOWS > 0\n	PointLightShadow pointLightShadow;\n	#endif\n	#pragma unroll_loop_start\n	for ( int i = 0; i < NUM_POINT_LIGHTS; i ++ ) {\n		pointLight = pointLights[ i ];\n		getPointLightInfo( pointLight, geometryPosition, directLight );\n		#if defined( USE_SHADOWMAP ) && ( UNROLLED_LOOP_INDEX < NUM_POINT_LIGHT_SHADOWS )\n		pointLightShadow = pointLightShadows[ i ];\n		directLight.color *= ( directLight.visible && receiveShadow ) ? getPointShadow( pointShadowMap[ i ], pointLightShadow.shadowMapSize, pointLightShadow.shadowBias, pointLightShadow.shadowRadius, vPointShadowCoord[ i ], pointLightShadow.shadowCameraNear, pointLightShadow.shadowCameraFar ) : 1.0;\n		#endif\n		RE_Direct( directLight, geometryPosition, geometryNormal, geometryViewDir, geometryClearcoatNormal, material, reflectedLight );\n	}\n	#pragma unroll_loop_end\n#endif\n#if ( NUM_SPOT_LIGHTS > 0 ) && defined( RE_Direct )\n	SpotLight spotLight;\n	vec4 spotColor;\n	vec3 spotLightCoord;\n	bool inSpotLightMap;\n	#if defined( USE_SHADOWMAP ) && NUM_SPOT_LIGHT_SHADOWS > 0\n	SpotLightShadow spotLightShadow;\n	#endif\n	#pragma unroll_loop_start\n	for ( int i = 0; i < NUM_SPOT_LIGHTS; i ++ ) {\n		spotLight = spotLights[ i ];\n		getSpotLightInfo( spotLight, geometryPosition, directLight );\n		#if ( UNROLLED_LOOP_INDEX < NUM_SPOT_LIGHT_SHADOWS_WITH_MAPS )\n		#define SPOT_LIGHT_MAP_INDEX UNROLLED_LOOP_INDEX\n		#elif ( UNROLLED_LOOP_INDEX < NUM_SPOT_LIGHT_SHADOWS )\n		#define SPOT_LIGHT_MAP_INDEX NUM_SPOT_LIGHT_MAPS\n		#else\n		#define SPOT_LIGHT_MAP_INDEX ( UNROLLED_LOOP_INDEX - NUM_SPOT_LIGHT_SHADOWS + NUM_SPOT_LIGHT_SHADOWS_WITH_MAPS )\n		#endif\n		#if ( SPOT_LIGHT_MAP_INDEX < NUM_SPOT_LIGHT_MAPS )\n			spotLightCoord = vSpotLightCoord[ i ].xyz / vSpotLightCoord[ i ].w;\n			inSpotLightMap = all( lessThan( abs( spotLightCoord * 2. - 1. ), vec3( 1.0 ) ) );\n			spotColor = texture2D( spotLightMap[ SPOT_LIGHT_MAP_INDEX ], spotLightCoord.xy );\n			directLight.color = inSpotLightMap ? directLight.color * spotColor.rgb : directLight.color;\n		#endif\n		#undef SPOT_LIGHT_MAP_INDEX\n		#if defined( USE_SHADOWMAP ) && ( UNROLLED_LOOP_INDEX < NUM_SPOT_LIGHT_SHADOWS )\n		spotLightShadow = spotLightShadows[ i ];\n		directLight.color *= ( directLight.visible && receiveShadow ) ? getShadow( spotShadowMap[ i ], spotLightShadow.shadowMapSize, spotLightShadow.shadowBias, spotLightShadow.shadowRadius, vSpotLightCoord[ i ] ) : 1.0;\n		#endif\n		RE_Direct( directLight, geometryPosition, geometryNormal, geometryViewDir, geometryClearcoatNormal, material, reflectedLight );\n	}\n	#pragma unroll_loop_end\n#endif\n#if ( NUM_DIR_LIGHTS > 0 ) && defined( RE_Direct )\n	DirectionalLight directionalLight;\n	#if defined( USE_SHADOWMAP ) && NUM_DIR_LIGHT_SHADOWS > 0\n	DirectionalLightShadow directionalLightShadow;\n	#endif\n	#pragma unroll_loop_start\n	for ( int i = 0; i < NUM_DIR_LIGHTS; i ++ ) {\n		directionalLight = directionalLights[ i ];\n		getDirectionalLightInfo( directionalLight, directLight );\n		#if defined( USE_SHADOWMAP ) && ( UNROLLED_LOOP_INDEX < NUM_DIR_LIGHT_SHADOWS )\n		directionalLightShadow = directionalLightShadows[ i ];\n		directLight.color *= ( directLight.visible && receiveShadow ) ? getShadow( directionalShadowMap[ i ], directionalLightShadow.shadowMapSize, directionalLightShadow.shadowBias, directionalLightShadow.shadowRadius, vDirectionalShadowCoord[ i ] ) : 1.0;\n		#endif\n		RE_Direct( directLight, geometryPosition, geometryNormal, geometryViewDir, geometryClearcoatNormal, material, reflectedLight );\n	}\n	#pragma unroll_loop_end\n#endif\n#if ( NUM_RECT_AREA_LIGHTS > 0 ) && defined( RE_Direct_RectArea )\n	RectAreaLight rectAreaLight;\n	#pragma unroll_loop_start\n	for ( int i = 0; i < NUM_RECT_AREA_LIGHTS; i ++ ) {\n		rectAreaLight = rectAreaLights[ i ];\n		RE_Direct_RectArea( rectAreaLight, geometryPosition, geometryNormal, geometryViewDir, geometryClearcoatNormal, material, reflectedLight );\n	}\n	#pragma unroll_loop_end\n#endif\n#if defined( RE_IndirectDiffuse )\n	vec3 iblIrradiance = vec3( 0.0 );\n	vec3 irradiance = getAmbientLightIrradiance( ambientLightColor );\n	#if defined( USE_LIGHT_PROBES )\n		irradiance += getLightProbeIrradiance( lightProbe, geometryNormal );\n	#endif\n	#if ( NUM_HEMI_LIGHTS > 0 )\n		#pragma unroll_loop_start\n		for ( int i = 0; i < NUM_HEMI_LIGHTS; i ++ ) {\n			irradiance += getHemisphereLightIrradiance( hemisphereLights[ i ], geometryNormal );\n		}\n		#pragma unroll_loop_end\n	#endif\n#endif\n#if defined( RE_IndirectSpecular )\n	vec3 radiance = vec3( 0.0 );\n	vec3 clearcoatRadiance = vec3( 0.0 );\n#endif",lights_fragment_maps:"#if defined( RE_IndirectDiffuse )\n	#ifdef USE_LIGHTMAP\n		vec4 lightMapTexel = texture2D( lightMap, vLightMapUv );\n		vec3 lightMapIrradiance = lightMapTexel.rgb * lightMapIntensity;\n		irradiance += lightMapIrradiance;\n	#endif\n	#if defined( USE_ENVMAP ) && defined( STANDARD ) && defined( ENVMAP_TYPE_CUBE_UV )\n		iblIrradiance += getIBLIrradiance( geometryNormal );\n	#endif\n#endif\n#if defined( USE_ENVMAP ) && defined( RE_IndirectSpecular )\n	#ifdef USE_ANISOTROPY\n		radiance += getIBLAnisotropyRadiance( geometryViewDir, geometryNormal, material.roughness, material.anisotropyB, material.anisotropy );\n	#else\n		radiance += getIBLRadiance( geometryViewDir, geometryNormal, material.roughness );\n	#endif\n	#ifdef USE_CLEARCOAT\n		clearcoatRadiance += getIBLRadiance( geometryViewDir, geometryClearcoatNormal, material.clearcoatRoughness );\n	#endif\n#endif",lights_fragment_end:"#if defined( RE_IndirectDiffuse )\n	RE_IndirectDiffuse( irradiance, geometryPosition, geometryNormal, geometryViewDir, geometryClearcoatNormal, material, reflectedLight );\n#endif\n#if defined( RE_IndirectSpecular )\n	RE_IndirectSpecular( radiance, iblIrradiance, clearcoatRadiance, geometryPosition, geometryNormal, geometryViewDir, geometryClearcoatNormal, material, reflectedLight );\n#endif",logdepthbuf_fragment:"#if defined( USE_LOGDEPTHBUF ) && defined( USE_LOGDEPTHBUF_EXT )\n	gl_FragDepthEXT = vIsPerspective == 0.0 ? gl_FragCoord.z : log2( vFragDepth ) * logDepthBufFC * 0.5;\n#endif",logdepthbuf_pars_fragment:"#if defined( USE_LOGDEPTHBUF ) && defined( USE_LOGDEPTHBUF_EXT )\n	uniform float logDepthBufFC;\n	varying float vFragDepth;\n	varying float vIsPerspective;\n#endif",logdepthbuf_pars_vertex:"#ifdef USE_LOGDEPTHBUF\n	#ifdef USE_LOGDEPTHBUF_EXT\n		varying float vFragDepth;\n		varying float vIsPerspective;\n	#else\n		uniform float logDepthBufFC;\n	#endif\n#endif",logdepthbuf_vertex:"#ifdef USE_LOGDEPTHBUF\n	#ifdef USE_LOGDEPTHBUF_EXT\n		vFragDepth = 1.0 + gl_Position.w;\n		vIsPerspective = float( isPerspectiveMatrix( projectionMatrix ) );\n	#else\n		if ( isPerspectiveMatrix( projectionMatrix ) ) {\n			gl_Position.z = log2( max( EPSILON, gl_Position.w + 1.0 ) ) * logDepthBufFC - 1.0;\n			gl_Position.z *= gl_Position.w;\n		}\n	#endif\n#endif",map_fragment:"#ifdef USE_MAP\n	vec4 sampledDiffuseColor = texture2D( map, vMapUv );\n	#ifdef DECODE_VIDEO_TEXTURE\n		sampledDiffuseColor = vec4( mix( pow( sampledDiffuseColor.rgb * 0.9478672986 + vec3( 0.0521327014 ), vec3( 2.4 ) ), sampledDiffuseColor.rgb * 0.0773993808, vec3( lessThanEqual( sampledDiffuseColor.rgb, vec3( 0.04045 ) ) ) ), sampledDiffuseColor.w );\n	\n	#endif\n	diffuseColor *= sampledDiffuseColor;\n#endif",map_pars_fragment:"#ifdef USE_MAP\n	uniform sampler2D map;\n#endif",map_particle_fragment:"#if defined( USE_MAP ) || defined( USE_ALPHAMAP )\n	#if defined( USE_POINTS_UV )\n		vec2 uv = vUv;\n	#else\n		vec2 uv = ( uvTransform * vec3( gl_PointCoord.x, 1.0 - gl_PointCoord.y, 1 ) ).xy;\n	#endif\n#endif\n#ifdef USE_MAP\n	diffuseColor *= texture2D( map, uv );\n#endif\n#ifdef USE_ALPHAMAP\n	diffuseColor.a *= texture2D( alphaMap, uv ).g;\n#endif",map_particle_pars_fragment:"#if defined( USE_POINTS_UV )\n	varying vec2 vUv;\n#else\n	#if defined( USE_MAP ) || defined( USE_ALPHAMAP )\n		uniform mat3 uvTransform;\n	#endif\n#endif\n#ifdef USE_MAP\n	uniform sampler2D map;\n#endif\n#ifdef USE_ALPHAMAP\n	uniform sampler2D alphaMap;\n#endif",metalnessmap_fragment:"float metalnessFactor = metalness;\n#ifdef USE_METALNESSMAP\n	vec4 texelMetalness = texture2D( metalnessMap, vMetalnessMapUv );\n	metalnessFactor *= texelMetalness.b;\n#endif",metalnessmap_pars_fragment:"#ifdef USE_METALNESSMAP\n	uniform sampler2D metalnessMap;\n#endif",morphcolor_vertex:"#if defined( USE_MORPHCOLORS ) && defined( MORPHTARGETS_TEXTURE )\n	vColor *= morphTargetBaseInfluence;\n	for ( int i = 0; i < MORPHTARGETS_COUNT; i ++ ) {\n		#if defined( USE_COLOR_ALPHA )\n			if ( morphTargetInfluences[ i ] != 0.0 ) vColor += getMorph( gl_VertexID, i, 2 ) * morphTargetInfluences[ i ];\n		#elif defined( USE_COLOR )\n			if ( morphTargetInfluences[ i ] != 0.0 ) vColor += getMorph( gl_VertexID, i, 2 ).rgb * morphTargetInfluences[ i ];\n		#endif\n	}\n#endif",morphnormal_vertex:"#ifdef USE_MORPHNORMALS\n	objectNormal *= morphTargetBaseInfluence;\n	#ifdef MORPHTARGETS_TEXTURE\n		for ( int i = 0; i < MORPHTARGETS_COUNT; i ++ ) {\n			if ( morphTargetInfluences[ i ] != 0.0 ) objectNormal += getMorph( gl_VertexID, i, 1 ).xyz * morphTargetInfluences[ i ];\n		}\n	#else\n		objectNormal += morphNormal0 * morphTargetInfluences[ 0 ];\n		objectNormal += morphNormal1 * morphTargetInfluences[ 1 ];\n		objectNormal += morphNormal2 * morphTargetInfluences[ 2 ];\n		objectNormal += morphNormal3 * morphTargetInfluences[ 3 ];\n	#endif\n#endif",morphtarget_pars_vertex:"#ifdef USE_MORPHTARGETS\n	uniform float morphTargetBaseInfluence;\n	#ifdef MORPHTARGETS_TEXTURE\n		uniform float morphTargetInfluences[ MORPHTARGETS_COUNT ];\n		uniform sampler2DArray morphTargetsTexture;\n		uniform ivec2 morphTargetsTextureSize;\n		vec4 getMorph( const in int vertexIndex, const in int morphTargetIndex, const in int offset ) {\n			int texelIndex = vertexIndex * MORPHTARGETS_TEXTURE_STRIDE + offset;\n			int y = texelIndex / morphTargetsTextureSize.x;\n			int x = texelIndex - y * morphTargetsTextureSize.x;\n			ivec3 morphUV = ivec3( x, y, morphTargetIndex );\n			return texelFetch( morphTargetsTexture, morphUV, 0 );\n		}\n	#else\n		#ifndef USE_MORPHNORMALS\n			uniform float morphTargetInfluences[ 8 ];\n		#else\n			uniform float morphTargetInfluences[ 4 ];\n		#endif\n	#endif\n#endif",morphtarget_vertex:"#ifdef USE_MORPHTARGETS\n	transformed *= morphTargetBaseInfluence;\n	#ifdef MORPHTARGETS_TEXTURE\n		for ( int i = 0; i < MORPHTARGETS_COUNT; i ++ ) {\n			if ( morphTargetInfluences[ i ] != 0.0 ) transformed += getMorph( gl_VertexID, i, 0 ).xyz * morphTargetInfluences[ i ];\n		}\n	#else\n		transformed += morphTarget0 * morphTargetInfluences[ 0 ];\n		transformed += morphTarget1 * morphTargetInfluences[ 1 ];\n		transformed += morphTarget2 * morphTargetInfluences[ 2 ];\n		transformed += morphTarget3 * morphTargetInfluences[ 3 ];\n		#ifndef USE_MORPHNORMALS\n			transformed += morphTarget4 * morphTargetInfluences[ 4 ];\n			transformed += morphTarget5 * morphTargetInfluences[ 5 ];\n			transformed += morphTarget6 * morphTargetInfluences[ 6 ];\n			transformed += morphTarget7 * morphTargetInfluences[ 7 ];\n		#endif\n	#endif\n#endif",normal_fragment_begin:"float faceDirection = gl_FrontFacing ? 1.0 : - 1.0;\n#ifdef FLAT_SHADED\n	vec3 fdx = dFdx( vViewPosition );\n	vec3 fdy = dFdy( vViewPosition );\n	vec3 normal = normalize( cross( fdx, fdy ) );\n#else\n	vec3 normal = normalize( vNormal );\n	#ifdef DOUBLE_SIDED\n		normal *= faceDirection;\n	#endif\n#endif\n#if defined( USE_NORMALMAP_TANGENTSPACE ) || defined( USE_CLEARCOAT_NORMALMAP ) || defined( USE_ANISOTROPY )\n	#ifdef USE_TANGENT\n		mat3 tbn = mat3( normalize( vTangent ), normalize( vBitangent ), normal );\n	#else\n		mat3 tbn = getTangentFrame( - vViewPosition, normal,\n		#if defined( USE_NORMALMAP )\n			vNormalMapUv\n		#elif defined( USE_CLEARCOAT_NORMALMAP )\n			vClearcoatNormalMapUv\n		#else\n			vUv\n		#endif\n		);\n	#endif\n	#if defined( DOUBLE_SIDED ) && ! defined( FLAT_SHADED )\n		tbn[0] *= faceDirection;\n		tbn[1] *= faceDirection;\n	#endif\n#endif\n#ifdef USE_CLEARCOAT_NORMALMAP\n	#ifdef USE_TANGENT\n		mat3 tbn2 = mat3( normalize( vTangent ), normalize( vBitangent ), normal );\n	#else\n		mat3 tbn2 = getTangentFrame( - vViewPosition, normal, vClearcoatNormalMapUv );\n	#endif\n	#if defined( DOUBLE_SIDED ) && ! defined( FLAT_SHADED )\n		tbn2[0] *= faceDirection;\n		tbn2[1] *= faceDirection;\n	#endif\n#endif\nvec3 nonPerturbedNormal = normal;",normal_fragment_maps:"#ifdef USE_NORMALMAP_OBJECTSPACE\n	normal = texture2D( normalMap, vNormalMapUv ).xyz * 2.0 - 1.0;\n	#ifdef FLIP_SIDED\n		normal = - normal;\n	#endif\n	#ifdef DOUBLE_SIDED\n		normal = normal * faceDirection;\n	#endif\n	normal = normalize( normalMatrix * normal );\n#elif defined( USE_NORMALMAP_TANGENTSPACE )\n	vec3 mapN = texture2D( normalMap, vNormalMapUv ).xyz * 2.0 - 1.0;\n	mapN.xy *= normalScale;\n	normal = normalize( tbn * mapN );\n#elif defined( USE_BUMPMAP )\n	normal = perturbNormalArb( - vViewPosition, normal, dHdxy_fwd(), faceDirection );\n#endif",normal_pars_fragment:"#ifndef FLAT_SHADED\n	varying vec3 vNormal;\n	#ifdef USE_TANGENT\n		varying vec3 vTangent;\n		varying vec3 vBitangent;\n	#endif\n#endif",normal_pars_vertex:"#ifndef FLAT_SHADED\n	varying vec3 vNormal;\n	#ifdef USE_TANGENT\n		varying vec3 vTangent;\n		varying vec3 vBitangent;\n	#endif\n#endif",normal_vertex:"#ifndef FLAT_SHADED\n	vNormal = normalize( transformedNormal );\n	#ifdef USE_TANGENT\n		vTangent = normalize( transformedTangent );\n		vBitangent = normalize( cross( vNormal, vTangent ) * tangent.w );\n	#endif\n#endif",normalmap_pars_fragment:"#ifdef USE_NORMALMAP\n	uniform sampler2D normalMap;\n	uniform vec2 normalScale;\n#endif\n#ifdef USE_NORMALMAP_OBJECTSPACE\n	uniform mat3 normalMatrix;\n#endif\n#if ! defined ( USE_TANGENT ) && ( defined ( USE_NORMALMAP_TANGENTSPACE ) || defined ( USE_CLEARCOAT_NORMALMAP ) || defined( USE_ANISOTROPY ) )\n	mat3 getTangentFrame( vec3 eye_pos, vec3 surf_norm, vec2 uv ) {\n		vec3 q0 = dFdx( eye_pos.xyz );\n		vec3 q1 = dFdy( eye_pos.xyz );\n		vec2 st0 = dFdx( uv.st );\n		vec2 st1 = dFdy( uv.st );\n		vec3 N = surf_norm;\n		vec3 q1perp = cross( q1, N );\n		vec3 q0perp = cross( N, q0 );\n		vec3 T = q1perp * st0.x + q0perp * st1.x;\n		vec3 B = q1perp * st0.y + q0perp * st1.y;\n		float det = max( dot( T, T ), dot( B, B ) );\n		float scale = ( det == 0.0 ) ? 0.0 : inversesqrt( det );\n		return mat3( T * scale, B * scale, N );\n	}\n#endif",clearcoat_normal_fragment_begin:"#ifdef USE_CLEARCOAT\n	vec3 clearcoatNormal = nonPerturbedNormal;\n#endif",clearcoat_normal_fragment_maps:"#ifdef USE_CLEARCOAT_NORMALMAP\n	vec3 clearcoatMapN = texture2D( clearcoatNormalMap, vClearcoatNormalMapUv ).xyz * 2.0 - 1.0;\n	clearcoatMapN.xy *= clearcoatNormalScale;\n	clearcoatNormal = normalize( tbn2 * clearcoatMapN );\n#endif",clearcoat_pars_fragment:"#ifdef USE_CLEARCOATMAP\n	uniform sampler2D clearcoatMap;\n#endif\n#ifdef USE_CLEARCOAT_NORMALMAP\n	uniform sampler2D clearcoatNormalMap;\n	uniform vec2 clearcoatNormalScale;\n#endif\n#ifdef USE_CLEARCOAT_ROUGHNESSMAP\n	uniform sampler2D clearcoatRoughnessMap;\n#endif",iridescence_pars_fragment:"#ifdef USE_IRIDESCENCEMAP\n	uniform sampler2D iridescenceMap;\n#endif\n#ifdef USE_IRIDESCENCE_THICKNESSMAP\n	uniform sampler2D iridescenceThicknessMap;\n#endif",opaque_fragment:"#ifdef OPAQUE\ndiffuseColor.a = 1.0;\n#endif\n#ifdef USE_TRANSMISSION\ndiffuseColor.a *= material.transmissionAlpha;\n#endif\ngl_FragColor = vec4( outgoingLight, diffuseColor.a );",packing:"vec3 packNormalToRGB( const in vec3 normal ) {\n	return normalize( normal ) * 0.5 + 0.5;\n}\nvec3 unpackRGBToNormal( const in vec3 rgb ) {\n	return 2.0 * rgb.xyz - 1.0;\n}\nconst float PackUpscale = 256. / 255.;const float UnpackDownscale = 255. / 256.;\nconst vec3 PackFactors = vec3( 256. * 256. * 256., 256. * 256., 256. );\nconst vec4 UnpackFactors = UnpackDownscale / vec4( PackFactors, 1. );\nconst float ShiftRight8 = 1. / 256.;\nvec4 packDepthToRGBA( const in float v ) {\n	vec4 r = vec4( fract( v * PackFactors ), v );\n	r.yzw -= r.xyz * ShiftRight8;	return r * PackUpscale;\n}\nfloat unpackRGBAToDepth( const in vec4 v ) {\n	return dot( v, UnpackFactors );\n}\nvec2 packDepthToRG( in highp float v ) {\n	return packDepthToRGBA( v ).yx;\n}\nfloat unpackRGToDepth( const in highp vec2 v ) {\n	return unpackRGBAToDepth( vec4( v.xy, 0.0, 0.0 ) );\n}\nvec4 pack2HalfToRGBA( vec2 v ) {\n	vec4 r = vec4( v.x, fract( v.x * 255.0 ), v.y, fract( v.y * 255.0 ) );\n	return vec4( r.x - r.y / 255.0, r.y, r.z - r.w / 255.0, r.w );\n}\nvec2 unpackRGBATo2Half( vec4 v ) {\n	return vec2( v.x + ( v.y / 255.0 ), v.z + ( v.w / 255.0 ) );\n}\nfloat viewZToOrthographicDepth( const in float viewZ, const in float near, const in float far ) {\n	return ( viewZ + near ) / ( near - far );\n}\nfloat orthographicDepthToViewZ( const in float depth, const in float near, const in float far ) {\n	return depth * ( near - far ) - near;\n}\nfloat viewZToPerspectiveDepth( const in float viewZ, const in float near, const in float far ) {\n	return ( ( near + viewZ ) * far ) / ( ( far - near ) * viewZ );\n}\nfloat perspectiveDepthToViewZ( const in float depth, const in float near, const in float far ) {\n	return ( near * far ) / ( ( far - near ) * depth - far );\n}",premultiplied_alpha_fragment:"#ifdef PREMULTIPLIED_ALPHA\n	gl_FragColor.rgb *= gl_FragColor.a;\n#endif",project_vertex:"vec4 mvPosition = vec4( transformed, 1.0 );\n#ifdef USE_INSTANCING\n	mvPosition = instanceMatrix * mvPosition;\n#endif\nmvPosition = modelViewMatrix * mvPosition;\ngl_Position = projectionMatrix * mvPosition;",dithering_fragment:"#ifdef DITHERING\n	gl_FragColor.rgb = dithering( gl_FragColor.rgb );\n#endif",dithering_pars_fragment:"#ifdef DITHERING\n	vec3 dithering( vec3 color ) {\n		float grid_position = rand( gl_FragCoord.xy );\n		vec3 dither_shift_RGB = vec3( 0.25 / 255.0, -0.25 / 255.0, 0.25 / 255.0 );\n		dither_shift_RGB = mix( 2.0 * dither_shift_RGB, -2.0 * dither_shift_RGB, grid_position );\n		return color + dither_shift_RGB;\n	}\n#endif",roughnessmap_fragment:"float roughnessFactor = roughness;\n#ifdef USE_ROUGHNESSMAP\n	vec4 texelRoughness = texture2D( roughnessMap, vRoughnessMapUv );\n	roughnessFactor *= texelRoughness.g;\n#endif",roughnessmap_pars_fragment:"#ifdef USE_ROUGHNESSMAP\n	uniform sampler2D roughnessMap;\n#endif",shadowmap_pars_fragment:"#if NUM_SPOT_LIGHT_COORDS > 0\n	varying vec4 vSpotLightCoord[ NUM_SPOT_LIGHT_COORDS ];\n#endif\n#if NUM_SPOT_LIGHT_MAPS > 0\n	uniform sampler2D spotLightMap[ NUM_SPOT_LIGHT_MAPS ];\n#endif\n#ifdef USE_SHADOWMAP\n	#if NUM_DIR_LIGHT_SHADOWS > 0\n		uniform sampler2D directionalShadowMap[ NUM_DIR_LIGHT_SHADOWS ];\n		varying vec4 vDirectionalShadowCoord[ NUM_DIR_LIGHT_SHADOWS ];\n		struct DirectionalLightShadow {\n			float shadowBias;\n			float shadowNormalBias;\n			float shadowRadius;\n			vec2 shadowMapSize;\n		};\n		uniform DirectionalLightShadow directionalLightShadows[ NUM_DIR_LIGHT_SHADOWS ];\n	#endif\n	#if NUM_SPOT_LIGHT_SHADOWS > 0\n		uniform sampler2D spotShadowMap[ NUM_SPOT_LIGHT_SHADOWS ];\n		struct SpotLightShadow {\n			float shadowBias;\n			float shadowNormalBias;\n			float shadowRadius;\n			vec2 shadowMapSize;\n		};\n		uniform SpotLightShadow spotLightShadows[ NUM_SPOT_LIGHT_SHADOWS ];\n	#endif\n	#if NUM_POINT_LIGHT_SHADOWS > 0\n		uniform sampler2D pointShadowMap[ NUM_POINT_LIGHT_SHADOWS ];\n		varying vec4 vPointShadowCoord[ NUM_POINT_LIGHT_SHADOWS ];\n		struct PointLightShadow {\n			float shadowBias;\n			float shadowNormalBias;\n			float shadowRadius;\n			vec2 shadowMapSize;\n			float shadowCameraNear;\n			float shadowCameraFar;\n		};\n		uniform PointLightShadow pointLightShadows[ NUM_POINT_LIGHT_SHADOWS ];\n	#endif\n	float texture2DCompare( sampler2D depths, vec2 uv, float compare ) {\n		return step( compare, unpackRGBAToDepth( texture2D( depths, uv ) ) );\n	}\n	vec2 texture2DDistribution( sampler2D shadow, vec2 uv ) {\n		return unpackRGBATo2Half( texture2D( shadow, uv ) );\n	}\n	float VSMShadow (sampler2D shadow, vec2 uv, float compare ){\n		float occlusion = 1.0;\n		vec2 distribution = texture2DDistribution( shadow, uv );\n		float hard_shadow = step( compare , distribution.x );\n		if (hard_shadow != 1.0 ) {\n			float distance = compare - distribution.x ;\n			float variance = max( 0.00000, distribution.y * distribution.y );\n			float softness_probability = variance / (variance + distance * distance );			softness_probability = clamp( ( softness_probability - 0.3 ) / ( 0.95 - 0.3 ), 0.0, 1.0 );			occlusion = clamp( max( hard_shadow, softness_probability ), 0.0, 1.0 );\n		}\n		return occlusion;\n	}\n	float getShadow( sampler2D shadowMap, vec2 shadowMapSize, float shadowBias, float shadowRadius, vec4 shadowCoord ) {\n		float shadow = 1.0;\n		shadowCoord.xyz /= shadowCoord.w;\n		shadowCoord.z += shadowBias;\n		bool inFrustum = shadowCoord.x >= 0.0 && shadowCoord.x <= 1.0 && shadowCoord.y >= 0.0 && shadowCoord.y <= 1.0;\n		bool frustumTest = inFrustum && shadowCoord.z <= 1.0;\n		if ( frustumTest ) {\n		#if defined( SHADOWMAP_TYPE_PCF )\n			vec2 texelSize = vec2( 1.0 ) / shadowMapSize;\n			float dx0 = - texelSize.x * shadowRadius;\n			float dy0 = - texelSize.y * shadowRadius;\n			float dx1 = + texelSize.x * shadowRadius;\n			float dy1 = + texelSize.y * shadowRadius;\n			float dx2 = dx0 / 2.0;\n			float dy2 = dy0 / 2.0;\n			float dx3 = dx1 / 2.0;\n			float dy3 = dy1 / 2.0;\n			shadow = (\n				texture2DCompare( shadowMap, shadowCoord.xy + vec2( dx0, dy0 ), shadowCoord.z ) +\n				texture2DCompare( shadowMap, shadowCoord.xy + vec2( 0.0, dy0 ), shadowCoord.z ) +\n				texture2DCompare( shadowMap, shadowCoord.xy + vec2( dx1, dy0 ), shadowCoord.z ) +\n				texture2DCompare( shadowMap, shadowCoord.xy + vec2( dx2, dy2 ), shadowCoord.z ) +\n				texture2DCompare( shadowMap, shadowCoord.xy + vec2( 0.0, dy2 ), shadowCoord.z ) +\n				texture2DCompare( shadowMap, shadowCoord.xy + vec2( dx3, dy2 ), shadowCoord.z ) +\n				texture2DCompare( shadowMap, shadowCoord.xy + vec2( dx0, 0.0 ), shadowCoord.z ) +\n				texture2DCompare( shadowMap, shadowCoord.xy + vec2( dx2, 0.0 ), shadowCoord.z ) +\n				texture2DCompare( shadowMap, shadowCoord.xy, shadowCoord.z ) +\n				texture2DCompare( shadowMap, shadowCoord.xy + vec2( dx3, 0.0 ), shadowCoord.z ) +\n				texture2DCompare( shadowMap, shadowCoord.xy + vec2( dx1, 0.0 ), shadowCoord.z ) +\n				texture2DCompare( shadowMap, shadowCoord.xy + vec2( dx2, dy3 ), shadowCoord.z ) +\n				texture2DCompare( shadowMap, shadowCoord.xy + vec2( 0.0, dy3 ), shadowCoord.z ) +\n				texture2DCompare( shadowMap, shadowCoord.xy + vec2( dx3, dy3 ), shadowCoord.z ) +\n				texture2DCompare( shadowMap, shadowCoord.xy + vec2( dx0, dy1 ), shadowCoord.z ) +\n				texture2DCompare( shadowMap, shadowCoord.xy + vec2( 0.0, dy1 ), shadowCoord.z ) +\n				texture2DCompare( shadowMap, shadowCoord.xy + vec2( dx1, dy1 ), shadowCoord.z )\n			) * ( 1.0 / 17.0 );\n		#elif defined( SHADOWMAP_TYPE_PCF_SOFT )\n			vec2 texelSize = vec2( 1.0 ) / shadowMapSize;\n			float dx = texelSize.x;\n			float dy = texelSize.y;\n			vec2 uv = shadowCoord.xy;\n			vec2 f = fract( uv * shadowMapSize + 0.5 );\n			uv -= f * texelSize;\n			shadow = (\n				texture2DCompare( shadowMap, uv, shadowCoord.z ) +\n				texture2DCompare( shadowMap, uv + vec2( dx, 0.0 ), shadowCoord.z ) +\n				texture2DCompare( shadowMap, uv + vec2( 0.0, dy ), shadowCoord.z ) +\n				texture2DCompare( shadowMap, uv + texelSize, shadowCoord.z ) +\n				mix( texture2DCompare( shadowMap, uv + vec2( -dx, 0.0 ), shadowCoord.z ),\n					 texture2DCompare( shadowMap, uv + vec2( 2.0 * dx, 0.0 ), shadowCoord.z ),\n					 f.x ) +\n				mix( texture2DCompare( shadowMap, uv + vec2( -dx, dy ), shadowCoord.z ),\n					 texture2DCompare( shadowMap, uv + vec2( 2.0 * dx, dy ), shadowCoord.z ),\n					 f.x ) +\n				mix( texture2DCompare( shadowMap, uv + vec2( 0.0, -dy ), shadowCoord.z ),\n					 texture2DCompare( shadowMap, uv + vec2( 0.0, 2.0 * dy ), shadowCoord.z ),\n					 f.y ) +\n				mix( texture2DCompare( shadowMap, uv + vec2( dx, -dy ), shadowCoord.z ),\n					 texture2DCompare( shadowMap, uv + vec2( dx, 2.0 * dy ), shadowCoord.z ),\n					 f.y ) +\n				mix( mix( texture2DCompare( shadowMap, uv + vec2( -dx, -dy ), shadowCoord.z ),\n						  texture2DCompare( shadowMap, uv + vec2( 2.0 * dx, -dy ), shadowCoord.z ),\n						  f.x ),\n					 mix( texture2DCompare( shadowMap, uv + vec2( -dx, 2.0 * dy ), shadowCoord.z ),\n						  texture2DCompare( shadowMap, uv + vec2( 2.0 * dx, 2.0 * dy ), shadowCoord.z ),\n						  f.x ),\n					 f.y )\n			) * ( 1.0 / 9.0 );\n		#elif defined( SHADOWMAP_TYPE_VSM )\n			shadow = VSMShadow( shadowMap, shadowCoord.xy, shadowCoord.z );\n		#else\n			shadow = texture2DCompare( shadowMap, shadowCoord.xy, shadowCoord.z );\n		#endif\n		}\n		return shadow;\n	}\n	vec2 cubeToUV( vec3 v, float texelSizeY ) {\n		vec3 absV = abs( v );\n		float scaleToCube = 1.0 / max( absV.x, max( absV.y, absV.z ) );\n		absV *= scaleToCube;\n		v *= scaleToCube * ( 1.0 - 2.0 * texelSizeY );\n		vec2 planar = v.xy;\n		float almostATexel = 1.5 * texelSizeY;\n		float almostOne = 1.0 - almostATexel;\n		if ( absV.z >= almostOne ) {\n			if ( v.z > 0.0 )\n				planar.x = 4.0 - v.x;\n		} else if ( absV.x >= almostOne ) {\n			float signX = sign( v.x );\n			planar.x = v.z * signX + 2.0 * signX;\n		} else if ( absV.y >= almostOne ) {\n			float signY = sign( v.y );\n			planar.x = v.x + 2.0 * signY + 2.0;\n			planar.y = v.z * signY - 2.0;\n		}\n		return vec2( 0.125, 0.25 ) * planar + vec2( 0.375, 0.75 );\n	}\n	float getPointShadow( sampler2D shadowMap, vec2 shadowMapSize, float shadowBias, float shadowRadius, vec4 shadowCoord, float shadowCameraNear, float shadowCameraFar ) {\n		vec2 texelSize = vec2( 1.0 ) / ( shadowMapSize * vec2( 4.0, 2.0 ) );\n		vec3 lightToPosition = shadowCoord.xyz;\n		float dp = ( length( lightToPosition ) - shadowCameraNear ) / ( shadowCameraFar - shadowCameraNear );		dp += shadowBias;\n		vec3 bd3D = normalize( lightToPosition );\n		#if defined( SHADOWMAP_TYPE_PCF ) || defined( SHADOWMAP_TYPE_PCF_SOFT ) || defined( SHADOWMAP_TYPE_VSM )\n			vec2 offset = vec2( - 1, 1 ) * shadowRadius * texelSize.y;\n			return (\n				texture2DCompare( shadowMap, cubeToUV( bd3D + offset.xyy, texelSize.y ), dp ) +\n				texture2DCompare( shadowMap, cubeToUV( bd3D + offset.yyy, texelSize.y ), dp ) +\n				texture2DCompare( shadowMap, cubeToUV( bd3D + offset.xyx, texelSize.y ), dp ) +\n				texture2DCompare( shadowMap, cubeToUV( bd3D + offset.yyx, texelSize.y ), dp ) +\n				texture2DCompare( shadowMap, cubeToUV( bd3D, texelSize.y ), dp ) +\n				texture2DCompare( shadowMap, cubeToUV( bd3D + offset.xxy, texelSize.y ), dp ) +\n				texture2DCompare( shadowMap, cubeToUV( bd3D + offset.yxy, texelSize.y ), dp ) +\n				texture2DCompare( shadowMap, cubeToUV( bd3D + offset.xxx, texelSize.y ), dp ) +\n				texture2DCompare( shadowMap, cubeToUV( bd3D + offset.yxx, texelSize.y ), dp )\n			) * ( 1.0 / 9.0 );\n		#else\n			return texture2DCompare( shadowMap, cubeToUV( bd3D, texelSize.y ), dp );\n		#endif\n	}\n#endif",shadowmap_pars_vertex:"#if NUM_SPOT_LIGHT_COORDS > 0\n	uniform mat4 spotLightMatrix[ NUM_SPOT_LIGHT_COORDS ];\n	varying vec4 vSpotLightCoord[ NUM_SPOT_LIGHT_COORDS ];\n#endif\n#ifdef USE_SHADOWMAP\n	#if NUM_DIR_LIGHT_SHADOWS > 0\n		uniform mat4 directionalShadowMatrix[ NUM_DIR_LIGHT_SHADOWS ];\n		varying vec4 vDirectionalShadowCoord[ NUM_DIR_LIGHT_SHADOWS ];\n		struct DirectionalLightShadow {\n			float shadowBias;\n			float shadowNormalBias;\n			float shadowRadius;\n			vec2 shadowMapSize;\n		};\n		uniform DirectionalLightShadow directionalLightShadows[ NUM_DIR_LIGHT_SHADOWS ];\n	#endif\n	#if NUM_SPOT_LIGHT_SHADOWS > 0\n		struct SpotLightShadow {\n			float shadowBias;\n			float shadowNormalBias;\n			float shadowRadius;\n			vec2 shadowMapSize;\n		};\n		uniform SpotLightShadow spotLightShadows[ NUM_SPOT_LIGHT_SHADOWS ];\n	#endif\n	#if NUM_POINT_LIGHT_SHADOWS > 0\n		uniform mat4 pointShadowMatrix[ NUM_POINT_LIGHT_SHADOWS ];\n		varying vec4 vPointShadowCoord[ NUM_POINT_LIGHT_SHADOWS ];\n		struct PointLightShadow {\n			float shadowBias;\n			float shadowNormalBias;\n			float shadowRadius;\n			vec2 shadowMapSize;\n			float shadowCameraNear;\n			float shadowCameraFar;\n		};\n		uniform PointLightShadow pointLightShadows[ NUM_POINT_LIGHT_SHADOWS ];\n	#endif\n#endif",shadowmap_vertex:"#if ( defined( USE_SHADOWMAP ) && ( NUM_DIR_LIGHT_SHADOWS > 0 || NUM_POINT_LIGHT_SHADOWS > 0 ) ) || ( NUM_SPOT_LIGHT_COORDS > 0 )\n	vec3 shadowWorldNormal = inverseTransformDirection( transformedNormal, viewMatrix );\n	vec4 shadowWorldPosition;\n#endif\n#if defined( USE_SHADOWMAP )\n	#if NUM_DIR_LIGHT_SHADOWS > 0\n		#pragma unroll_loop_start\n		for ( int i = 0; i < NUM_DIR_LIGHT_SHADOWS; i ++ ) {\n			shadowWorldPosition = worldPosition + vec4( shadowWorldNormal * directionalLightShadows[ i ].shadowNormalBias, 0 );\n			vDirectionalShadowCoord[ i ] = directionalShadowMatrix[ i ] * shadowWorldPosition;\n		}\n		#pragma unroll_loop_end\n	#endif\n	#if NUM_POINT_LIGHT_SHADOWS > 0\n		#pragma unroll_loop_start\n		for ( int i = 0; i < NUM_POINT_LIGHT_SHADOWS; i ++ ) {\n			shadowWorldPosition = worldPosition + vec4( shadowWorldNormal * pointLightShadows[ i ].shadowNormalBias, 0 );\n			vPointShadowCoord[ i ] = pointShadowMatrix[ i ] * shadowWorldPosition;\n		}\n		#pragma unroll_loop_end\n	#endif\n#endif\n#if NUM_SPOT_LIGHT_COORDS > 0\n	#pragma unroll_loop_start\n	for ( int i = 0; i < NUM_SPOT_LIGHT_COORDS; i ++ ) {\n		shadowWorldPosition = worldPosition;\n		#if ( defined( USE_SHADOWMAP ) && UNROLLED_LOOP_INDEX < NUM_SPOT_LIGHT_SHADOWS )\n			shadowWorldPosition.xyz += shadowWorldNormal * spotLightShadows[ i ].shadowNormalBias;\n		#endif\n		vSpotLightCoord[ i ] = spotLightMatrix[ i ] * shadowWorldPosition;\n	}\n	#pragma unroll_loop_end\n#endif",shadowmask_pars_fragment:"float getShadowMask() {\n	float shadow = 1.0;\n	#ifdef USE_SHADOWMAP\n	#if NUM_DIR_LIGHT_SHADOWS > 0\n	DirectionalLightShadow directionalLight;\n	#pragma unroll_loop_start\n	for ( int i = 0; i < NUM_DIR_LIGHT_SHADOWS; i ++ ) {\n		directionalLight = directionalLightShadows[ i ];\n		shadow *= receiveShadow ? getShadow( directionalShadowMap[ i ], directionalLight.shadowMapSize, directionalLight.shadowBias, directionalLight.shadowRadius, vDirectionalShadowCoord[ i ] ) : 1.0;\n	}\n	#pragma unroll_loop_end\n	#endif\n	#if NUM_SPOT_LIGHT_SHADOWS > 0\n	SpotLightShadow spotLight;\n	#pragma unroll_loop_start\n	for ( int i = 0; i < NUM_SPOT_LIGHT_SHADOWS; i ++ ) {\n		spotLight = spotLightShadows[ i ];\n		shadow *= receiveShadow ? getShadow( spotShadowMap[ i ], spotLight.shadowMapSize, spotLight.shadowBias, spotLight.shadowRadius, vSpotLightCoord[ i ] ) : 1.0;\n	}\n	#pragma unroll_loop_end\n	#endif\n	#if NUM_POINT_LIGHT_SHADOWS > 0\n	PointLightShadow pointLight;\n	#pragma unroll_loop_start\n	for ( int i = 0; i < NUM_POINT_LIGHT_SHADOWS; i ++ ) {\n		pointLight = pointLightShadows[ i ];\n		shadow *= receiveShadow ? getPointShadow( pointShadowMap[ i ], pointLight.shadowMapSize, pointLight.shadowBias, pointLight.shadowRadius, vPointShadowCoord[ i ], pointLight.shadowCameraNear, pointLight.shadowCameraFar ) : 1.0;\n	}\n	#pragma unroll_loop_end\n	#endif\n	#endif\n	return shadow;\n}",skinbase_vertex:"#ifdef USE_SKINNING\n	mat4 boneMatX = getBoneMatrix( skinIndex.x );\n	mat4 boneMatY = getBoneMatrix( skinIndex.y );\n	mat4 boneMatZ = getBoneMatrix( skinIndex.z );\n	mat4 boneMatW = getBoneMatrix( skinIndex.w );\n#endif",skinning_pars_vertex:"#ifdef USE_SKINNING\n	uniform mat4 bindMatrix;\n	uniform mat4 bindMatrixInverse;\n	uniform highp sampler2D boneTexture;\n	uniform int boneTextureSize;\n	mat4 getBoneMatrix( const in float i ) {\n		float j = i * 4.0;\n		float x = mod( j, float( boneTextureSize ) );\n		float y = floor( j / float( boneTextureSize ) );\n		float dx = 1.0 / float( boneTextureSize );\n		float dy = 1.0 / float( boneTextureSize );\n		y = dy * ( y + 0.5 );\n		vec4 v1 = texture2D( boneTexture, vec2( dx * ( x + 0.5 ), y ) );\n		vec4 v2 = texture2D( boneTexture, vec2( dx * ( x + 1.5 ), y ) );\n		vec4 v3 = texture2D( boneTexture, vec2( dx * ( x + 2.5 ), y ) );\n		vec4 v4 = texture2D( boneTexture, vec2( dx * ( x + 3.5 ), y ) );\n		mat4 bone = mat4( v1, v2, v3, v4 );\n		return bone;\n	}\n#endif",skinning_vertex:"#ifdef USE_SKINNING\n	vec4 skinVertex = bindMatrix * vec4( transformed, 1.0 );\n	vec4 skinned = vec4( 0.0 );\n	skinned += boneMatX * skinVertex * skinWeight.x;\n	skinned += boneMatY * skinVertex * skinWeight.y;\n	skinned += boneMatZ * skinVertex * skinWeight.z;\n	skinned += boneMatW * skinVertex * skinWeight.w;\n	transformed = ( bindMatrixInverse * skinned ).xyz;\n#endif",skinnormal_vertex:"#ifdef USE_SKINNING\n	mat4 skinMatrix = mat4( 0.0 );\n	skinMatrix += skinWeight.x * boneMatX;\n	skinMatrix += skinWeight.y * boneMatY;\n	skinMatrix += skinWeight.z * boneMatZ;\n	skinMatrix += skinWeight.w * boneMatW;\n	skinMatrix = bindMatrixInverse * skinMatrix * bindMatrix;\n	objectNormal = vec4( skinMatrix * vec4( objectNormal, 0.0 ) ).xyz;\n	#ifdef USE_TANGENT\n		objectTangent = vec4( skinMatrix * vec4( objectTangent, 0.0 ) ).xyz;\n	#endif\n#endif",specularmap_fragment:"float specularStrength;\n#ifdef USE_SPECULARMAP\n	vec4 texelSpecular = texture2D( specularMap, vSpecularMapUv );\n	specularStrength = texelSpecular.r;\n#else\n	specularStrength = 1.0;\n#endif",specularmap_pars_fragment:"#ifdef USE_SPECULARMAP\n	uniform sampler2D specularMap;\n#endif",tonemapping_fragment:"#if defined( TONE_MAPPING )\n	gl_FragColor.rgb = toneMapping( gl_FragColor.rgb );\n#endif",tonemapping_pars_fragment:"#ifndef saturate\n#define saturate( a ) clamp( a, 0.0, 1.0 )\n#endif\nuniform float toneMappingExposure;\nvec3 LinearToneMapping( vec3 color ) {\n	return saturate( toneMappingExposure * color );\n}\nvec3 ReinhardToneMapping( vec3 color ) {\n	color *= toneMappingExposure;\n	return saturate( color / ( vec3( 1.0 ) + color ) );\n}\nvec3 OptimizedCineonToneMapping( vec3 color ) {\n	color *= toneMappingExposure;\n	color = max( vec3( 0.0 ), color - 0.004 );\n	return pow( ( color * ( 6.2 * color + 0.5 ) ) / ( color * ( 6.2 * color + 1.7 ) + 0.06 ), vec3( 2.2 ) );\n}\nvec3 RRTAndODTFit( vec3 v ) {\n	vec3 a = v * ( v + 0.0245786 ) - 0.000090537;\n	vec3 b = v * ( 0.983729 * v + 0.4329510 ) + 0.238081;\n	return a / b;\n}\nvec3 ACESFilmicToneMapping( vec3 color ) {\n	const mat3 ACESInputMat = mat3(\n		vec3( 0.59719, 0.07600, 0.02840 ),		vec3( 0.35458, 0.90834, 0.13383 ),\n		vec3( 0.04823, 0.01566, 0.83777 )\n	);\n	const mat3 ACESOutputMat = mat3(\n		vec3(  1.60475, -0.10208, -0.00327 ),		vec3( -0.53108,  1.10813, -0.07276 ),\n		vec3( -0.07367, -0.00605,  1.07602 )\n	);\n	color *= toneMappingExposure / 0.6;\n	color = ACESInputMat * color;\n	color = RRTAndODTFit( color );\n	color = ACESOutputMat * color;\n	return saturate( color );\n}\nvec3 CustomToneMapping( vec3 color ) { return color; }",transmission_fragment:"#ifdef USE_TRANSMISSION\n	material.transmission = transmission;\n	material.transmissionAlpha = 1.0;\n	material.thickness = thickness;\n	material.attenuationDistance = attenuationDistance;\n	material.attenuationColor = attenuationColor;\n	#ifdef USE_TRANSMISSIONMAP\n		material.transmission *= texture2D( transmissionMap, vTransmissionMapUv ).r;\n	#endif\n	#ifdef USE_THICKNESSMAP\n		material.thickness *= texture2D( thicknessMap, vThicknessMapUv ).g;\n	#endif\n	vec3 pos = vWorldPosition;\n	vec3 v = normalize( cameraPosition - pos );\n	vec3 n = inverseTransformDirection( normal, viewMatrix );\n	vec4 transmitted = getIBLVolumeRefraction(\n		n, v, material.roughness, material.diffuseColor, material.specularColor, material.specularF90,\n		pos, modelMatrix, viewMatrix, projectionMatrix, material.ior, material.thickness,\n		material.attenuationColor, material.attenuationDistance );\n	material.transmissionAlpha = mix( material.transmissionAlpha, transmitted.a, material.transmission );\n	totalDiffuse = mix( totalDiffuse, transmitted.rgb, material.transmission );\n#endif",transmission_pars_fragment:"#ifdef USE_TRANSMISSION\n	uniform float transmission;\n	uniform float thickness;\n	uniform float attenuationDistance;\n	uniform vec3 attenuationColor;\n	#ifdef USE_TRANSMISSIONMAP\n		uniform sampler2D transmissionMap;\n	#endif\n	#ifdef USE_THICKNESSMAP\n		uniform sampler2D thicknessMap;\n	#endif\n	uniform vec2 transmissionSamplerSize;\n	uniform sampler2D transmissionSamplerMap;\n	uniform mat4 modelMatrix;\n	uniform mat4 projectionMatrix;\n	varying vec3 vWorldPosition;\n	float w0( float a ) {\n		return ( 1.0 / 6.0 ) * ( a * ( a * ( - a + 3.0 ) - 3.0 ) + 1.0 );\n	}\n	float w1( float a ) {\n		return ( 1.0 / 6.0 ) * ( a *  a * ( 3.0 * a - 6.0 ) + 4.0 );\n	}\n	float w2( float a ){\n		return ( 1.0 / 6.0 ) * ( a * ( a * ( - 3.0 * a + 3.0 ) + 3.0 ) + 1.0 );\n	}\n	float w3( float a ) {\n		return ( 1.0 / 6.0 ) * ( a * a * a );\n	}\n	float g0( float a ) {\n		return w0( a ) + w1( a );\n	}\n	float g1( float a ) {\n		return w2( a ) + w3( a );\n	}\n	float h0( float a ) {\n		return - 1.0 + w1( a ) / ( w0( a ) + w1( a ) );\n	}\n	float h1( float a ) {\n		return 1.0 + w3( a ) / ( w2( a ) + w3( a ) );\n	}\n	vec4 bicubic( sampler2D tex, vec2 uv, vec4 texelSize, float lod ) {\n		uv = uv * texelSize.zw + 0.5;\n		vec2 iuv = floor( uv );\n		vec2 fuv = fract( uv );\n		float g0x = g0( fuv.x );\n		float g1x = g1( fuv.x );\n		float h0x = h0( fuv.x );\n		float h1x = h1( fuv.x );\n		float h0y = h0( fuv.y );\n		float h1y = h1( fuv.y );\n		vec2 p0 = ( vec2( iuv.x + h0x, iuv.y + h0y ) - 0.5 ) * texelSize.xy;\n		vec2 p1 = ( vec2( iuv.x + h1x, iuv.y + h0y ) - 0.5 ) * texelSize.xy;\n		vec2 p2 = ( vec2( iuv.x + h0x, iuv.y + h1y ) - 0.5 ) * texelSize.xy;\n		vec2 p3 = ( vec2( iuv.x + h1x, iuv.y + h1y ) - 0.5 ) * texelSize.xy;\n		return g0( fuv.y ) * ( g0x * textureLod( tex, p0, lod ) + g1x * textureLod( tex, p1, lod ) ) +\n			g1( fuv.y ) * ( g0x * textureLod( tex, p2, lod ) + g1x * textureLod( tex, p3, lod ) );\n	}\n	vec4 textureBicubic( sampler2D sampler, vec2 uv, float lod ) {\n		vec2 fLodSize = vec2( textureSize( sampler, int( lod ) ) );\n		vec2 cLodSize = vec2( textureSize( sampler, int( lod + 1.0 ) ) );\n		vec2 fLodSizeInv = 1.0 / fLodSize;\n		vec2 cLodSizeInv = 1.0 / cLodSize;\n		vec4 fSample = bicubic( sampler, uv, vec4( fLodSizeInv, fLodSize ), floor( lod ) );\n		vec4 cSample = bicubic( sampler, uv, vec4( cLodSizeInv, cLodSize ), ceil( lod ) );\n		return mix( fSample, cSample, fract( lod ) );\n	}\n	vec3 getVolumeTransmissionRay( const in vec3 n, const in vec3 v, const in float thickness, const in float ior, const in mat4 modelMatrix ) {\n		vec3 refractionVector = refract( - v, normalize( n ), 1.0 / ior );\n		vec3 modelScale;\n		modelScale.x = length( vec3( modelMatrix[ 0 ].xyz ) );\n		modelScale.y = length( vec3( modelMatrix[ 1 ].xyz ) );\n		modelScale.z = length( vec3( modelMatrix[ 2 ].xyz ) );\n		return normalize( refractionVector ) * thickness * modelScale;\n	}\n	float applyIorToRoughness( const in float roughness, const in float ior ) {\n		return roughness * clamp( ior * 2.0 - 2.0, 0.0, 1.0 );\n	}\n	vec4 getTransmissionSample( const in vec2 fragCoord, const in float roughness, const in float ior ) {\n		float lod = log2( transmissionSamplerSize.x ) * applyIorToRoughness( roughness, ior );\n		return textureBicubic( transmissionSamplerMap, fragCoord.xy, lod );\n	}\n	vec3 volumeAttenuation( const in float transmissionDistance, const in vec3 attenuationColor, const in float attenuationDistance ) {\n		if ( isinf( attenuationDistance ) ) {\n			return vec3( 1.0 );\n		} else {\n			vec3 attenuationCoefficient = -log( attenuationColor ) / attenuationDistance;\n			vec3 transmittance = exp( - attenuationCoefficient * transmissionDistance );			return transmittance;\n		}\n	}\n	vec4 getIBLVolumeRefraction( const in vec3 n, const in vec3 v, const in float roughness, const in vec3 diffuseColor,\n		const in vec3 specularColor, const in float specularF90, const in vec3 position, const in mat4 modelMatrix,\n		const in mat4 viewMatrix, const in mat4 projMatrix, const in float ior, const in float thickness,\n		const in vec3 attenuationColor, const in float attenuationDistance ) {\n		vec3 transmissionRay = getVolumeTransmissionRay( n, v, thickness, ior, modelMatrix );\n		vec3 refractedRayExit = position + transmissionRay;\n		vec4 ndcPos = projMatrix * viewMatrix * vec4( refractedRayExit, 1.0 );\n		vec2 refractionCoords = ndcPos.xy / ndcPos.w;\n		refractionCoords += 1.0;\n		refractionCoords /= 2.0;\n		vec4 transmittedLight = getTransmissionSample( refractionCoords, roughness, ior );\n		vec3 transmittance = diffuseColor * volumeAttenuation( length( transmissionRay ), attenuationColor, attenuationDistance );\n		vec3 attenuatedColor = transmittance * transmittedLight.rgb;\n		vec3 F = EnvironmentBRDF( n, v, specularColor, specularF90, roughness );\n		float transmittanceFactor = ( transmittance.r + transmittance.g + transmittance.b ) / 3.0;\n		return vec4( ( 1.0 - F ) * attenuatedColor, 1.0 - ( 1.0 - transmittedLight.a ) * transmittanceFactor );\n	}\n#endif",uv_pars_fragment:"#if defined( USE_UV ) || defined( USE_ANISOTROPY )\n	varying vec2 vUv;\n#endif\n#ifdef USE_MAP\n	varying vec2 vMapUv;\n#endif\n#ifdef USE_ALPHAMAP\n	varying vec2 vAlphaMapUv;\n#endif\n#ifdef USE_LIGHTMAP\n	varying vec2 vLightMapUv;\n#endif\n#ifdef USE_AOMAP\n	varying vec2 vAoMapUv;\n#endif\n#ifdef USE_BUMPMAP\n	varying vec2 vBumpMapUv;\n#endif\n#ifdef USE_NORMALMAP\n	varying vec2 vNormalMapUv;\n#endif\n#ifdef USE_EMISSIVEMAP\n	varying vec2 vEmissiveMapUv;\n#endif\n#ifdef USE_METALNESSMAP\n	varying vec2 vMetalnessMapUv;\n#endif\n#ifdef USE_ROUGHNESSMAP\n	varying vec2 vRoughnessMapUv;\n#endif\n#ifdef USE_ANISOTROPYMAP\n	varying vec2 vAnisotropyMapUv;\n#endif\n#ifdef USE_CLEARCOATMAP\n	varying vec2 vClearcoatMapUv;\n#endif\n#ifdef USE_CLEARCOAT_NORMALMAP\n	varying vec2 vClearcoatNormalMapUv;\n#endif\n#ifdef USE_CLEARCOAT_ROUGHNESSMAP\n	varying vec2 vClearcoatRoughnessMapUv;\n#endif\n#ifdef USE_IRIDESCENCEMAP\n	varying vec2 vIridescenceMapUv;\n#endif\n#ifdef USE_IRIDESCENCE_THICKNESSMAP\n	varying vec2 vIridescenceThicknessMapUv;\n#endif\n#ifdef USE_SHEEN_COLORMAP\n	varying vec2 vSheenColorMapUv;\n#endif\n#ifdef USE_SHEEN_ROUGHNESSMAP\n	varying vec2 vSheenRoughnessMapUv;\n#endif\n#ifdef USE_SPECULARMAP\n	varying vec2 vSpecularMapUv;\n#endif\n#ifdef USE_SPECULAR_COLORMAP\n	varying vec2 vSpecularColorMapUv;\n#endif\n#ifdef USE_SPECULAR_INTENSITYMAP\n	varying vec2 vSpecularIntensityMapUv;\n#endif\n#ifdef USE_TRANSMISSIONMAP\n	uniform mat3 transmissionMapTransform;\n	varying vec2 vTransmissionMapUv;\n#endif\n#ifdef USE_THICKNESSMAP\n	uniform mat3 thicknessMapTransform;\n	varying vec2 vThicknessMapUv;\n#endif",uv_pars_vertex:"#if defined( USE_UV ) || defined( USE_ANISOTROPY )\n	varying vec2 vUv;\n#endif\n#ifdef USE_MAP\n	uniform mat3 mapTransform;\n	varying vec2 vMapUv;\n#endif\n#ifdef USE_ALPHAMAP\n	uniform mat3 alphaMapTransform;\n	varying vec2 vAlphaMapUv;\n#endif\n#ifdef USE_LIGHTMAP\n	uniform mat3 lightMapTransform;\n	varying vec2 vLightMapUv;\n#endif\n#ifdef USE_AOMAP\n	uniform mat3 aoMapTransform;\n	varying vec2 vAoMapUv;\n#endif\n#ifdef USE_BUMPMAP\n	uniform mat3 bumpMapTransform;\n	varying vec2 vBumpMapUv;\n#endif\n#ifdef USE_NORMALMAP\n	uniform mat3 normalMapTransform;\n	varying vec2 vNormalMapUv;\n#endif\n#ifdef USE_DISPLACEMENTMAP\n	uniform mat3 displacementMapTransform;\n	varying vec2 vDisplacementMapUv;\n#endif\n#ifdef USE_EMISSIVEMAP\n	uniform mat3 emissiveMapTransform;\n	varying vec2 vEmissiveMapUv;\n#endif\n#ifdef USE_METALNESSMAP\n	uniform mat3 metalnessMapTransform;\n	varying vec2 vMetalnessMapUv;\n#endif\n#ifdef USE_ROUGHNESSMAP\n	uniform mat3 roughnessMapTransform;\n	varying vec2 vRoughnessMapUv;\n#endif\n#ifdef USE_ANISOTROPYMAP\n	uniform mat3 anisotropyMapTransform;\n	varying vec2 vAnisotropyMapUv;\n#endif\n#ifdef USE_CLEARCOATMAP\n	uniform mat3 clearcoatMapTransform;\n	varying vec2 vClearcoatMapUv;\n#endif\n#ifdef USE_CLEARCOAT_NORMALMAP\n	uniform mat3 clearcoatNormalMapTransform;\n	varying vec2 vClearcoatNormalMapUv;\n#endif\n#ifdef USE_CLEARCOAT_ROUGHNESSMAP\n	uniform mat3 clearcoatRoughnessMapTransform;\n	varying vec2 vClearcoatRoughnessMapUv;\n#endif\n#ifdef USE_SHEEN_COLORMAP\n	uniform mat3 sheenColorMapTransform;\n	varying vec2 vSheenColorMapUv;\n#endif\n#ifdef USE_SHEEN_ROUGHNESSMAP\n	uniform mat3 sheenRoughnessMapTransform;\n	varying vec2 vSheenRoughnessMapUv;\n#endif\n#ifdef USE_IRIDESCENCEMAP\n	uniform mat3 iridescenceMapTransform;\n	varying vec2 vIridescenceMapUv;\n#endif\n#ifdef USE_IRIDESCENCE_THICKNESSMAP\n	uniform mat3 iridescenceThicknessMapTransform;\n	varying vec2 vIridescenceThicknessMapUv;\n#endif\n#ifdef USE_SPECULARMAP\n	uniform mat3 specularMapTransform;\n	varying vec2 vSpecularMapUv;\n#endif\n#ifdef USE_SPECULAR_COLORMAP\n	uniform mat3 specularColorMapTransform;\n	varying vec2 vSpecularColorMapUv;\n#endif\n#ifdef USE_SPECULAR_INTENSITYMAP\n	uniform mat3 specularIntensityMapTransform;\n	varying vec2 vSpecularIntensityMapUv;\n#endif\n#ifdef USE_TRANSMISSIONMAP\n	uniform mat3 transmissionMapTransform;\n	varying vec2 vTransmissionMapUv;\n#endif\n#ifdef USE_THICKNESSMAP\n	uniform mat3 thicknessMapTransform;\n	varying vec2 vThicknessMapUv;\n#endif",uv_vertex:"#if defined( USE_UV ) || defined( USE_ANISOTROPY )\n	vUv = vec3( uv, 1 ).xy;\n#endif\n#ifdef USE_MAP\n	vMapUv = ( mapTransform * vec3( MAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_ALPHAMAP\n	vAlphaMapUv = ( alphaMapTransform * vec3( ALPHAMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_LIGHTMAP\n	vLightMapUv = ( lightMapTransform * vec3( LIGHTMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_AOMAP\n	vAoMapUv = ( aoMapTransform * vec3( AOMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_BUMPMAP\n	vBumpMapUv = ( bumpMapTransform * vec3( BUMPMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_NORMALMAP\n	vNormalMapUv = ( normalMapTransform * vec3( NORMALMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_DISPLACEMENTMAP\n	vDisplacementMapUv = ( displacementMapTransform * vec3( DISPLACEMENTMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_EMISSIVEMAP\n	vEmissiveMapUv = ( emissiveMapTransform * vec3( EMISSIVEMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_METALNESSMAP\n	vMetalnessMapUv = ( metalnessMapTransform * vec3( METALNESSMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_ROUGHNESSMAP\n	vRoughnessMapUv = ( roughnessMapTransform * vec3( ROUGHNESSMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_ANISOTROPYMAP\n	vAnisotropyMapUv = ( anisotropyMapTransform * vec3( ANISOTROPYMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_CLEARCOATMAP\n	vClearcoatMapUv = ( clearcoatMapTransform * vec3( CLEARCOATMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_CLEARCOAT_NORMALMAP\n	vClearcoatNormalMapUv = ( clearcoatNormalMapTransform * vec3( CLEARCOAT_NORMALMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_CLEARCOAT_ROUGHNESSMAP\n	vClearcoatRoughnessMapUv = ( clearcoatRoughnessMapTransform * vec3( CLEARCOAT_ROUGHNESSMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_IRIDESCENCEMAP\n	vIridescenceMapUv = ( iridescenceMapTransform * vec3( IRIDESCENCEMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_IRIDESCENCE_THICKNESSMAP\n	vIridescenceThicknessMapUv = ( iridescenceThicknessMapTransform * vec3( IRIDESCENCE_THICKNESSMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_SHEEN_COLORMAP\n	vSheenColorMapUv = ( sheenColorMapTransform * vec3( SHEEN_COLORMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_SHEEN_ROUGHNESSMAP\n	vSheenRoughnessMapUv = ( sheenRoughnessMapTransform * vec3( SHEEN_ROUGHNESSMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_SPECULARMAP\n	vSpecularMapUv = ( specularMapTransform * vec3( SPECULARMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_SPECULAR_COLORMAP\n	vSpecularColorMapUv = ( specularColorMapTransform * vec3( SPECULAR_COLORMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_SPECULAR_INTENSITYMAP\n	vSpecularIntensityMapUv = ( specularIntensityMapTransform * vec3( SPECULAR_INTENSITYMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_TRANSMISSIONMAP\n	vTransmissionMapUv = ( transmissionMapTransform * vec3( TRANSMISSIONMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_THICKNESSMAP\n	vThicknessMapUv = ( thicknessMapTransform * vec3( THICKNESSMAP_UV, 1 ) ).xy;\n#endif",worldpos_vertex:"#if defined( USE_ENVMAP ) || defined( DISTANCE ) || defined ( USE_SHADOWMAP ) || defined ( USE_TRANSMISSION ) || NUM_SPOT_LIGHT_COORDS > 0\n	vec4 worldPosition = vec4( transformed, 1.0 );\n	#ifdef USE_INSTANCING\n		worldPosition = instanceMatrix * worldPosition;\n	#endif\n	worldPosition = modelMatrix * worldPosition;\n#endif",background_vert:"varying vec2 vUv;\nuniform mat3 uvTransform;\nvoid main() {\n	vUv = ( uvTransform * vec3( uv, 1 ) ).xy;\n	gl_Position = vec4( position.xy, 1.0, 1.0 );\n}",background_frag:"uniform sampler2D t2D;\nuniform float backgroundIntensity;\nvarying vec2 vUv;\nvoid main() {\n	vec4 texColor = texture2D( t2D, vUv );\n	#ifdef DECODE_VIDEO_TEXTURE\n		texColor = vec4( mix( pow( texColor.rgb * 0.9478672986 + vec3( 0.0521327014 ), vec3( 2.4 ) ), texColor.rgb * 0.0773993808, vec3( lessThanEqual( texColor.rgb, vec3( 0.04045 ) ) ) ), texColor.w );\n	#endif\n	texColor.rgb *= backgroundIntensity;\n	gl_FragColor = texColor;\n	#include <tonemapping_fragment>\n	#include <colorspace_fragment>\n}",backgroundCube_vert:"varying vec3 vWorldDirection;\n#include <common>\nvoid main() {\n	vWorldDirection = transformDirection( position, modelMatrix );\n	#include <begin_vertex>\n	#include <project_vertex>\n	gl_Position.z = gl_Position.w;\n}",backgroundCube_frag:"#ifdef ENVMAP_TYPE_CUBE\n	uniform samplerCube envMap;\n#elif defined( ENVMAP_TYPE_CUBE_UV )\n	uniform sampler2D envMap;\n#endif\nuniform float flipEnvMap;\nuniform float backgroundBlurriness;\nuniform float backgroundIntensity;\nvarying vec3 vWorldDirection;\n#include <cube_uv_reflection_fragment>\nvoid main() {\n	#ifdef ENVMAP_TYPE_CUBE\n		vec4 texColor = textureCube( envMap, vec3( flipEnvMap * vWorldDirection.x, vWorldDirection.yz ) );\n	#elif defined( ENVMAP_TYPE_CUBE_UV )\n		vec4 texColor = textureCubeUV( envMap, vWorldDirection, backgroundBlurriness );\n	#else\n		vec4 texColor = vec4( 0.0, 0.0, 0.0, 1.0 );\n	#endif\n	texColor.rgb *= backgroundIntensity;\n	gl_FragColor = texColor;\n	#include <tonemapping_fragment>\n	#include <colorspace_fragment>\n}",cube_vert:"varying vec3 vWorldDirection;\n#include <common>\nvoid main() {\n	vWorldDirection = transformDirection( position, modelMatrix );\n	#include <begin_vertex>\n	#include <project_vertex>\n	gl_Position.z = gl_Position.w;\n}",cube_frag:"uniform samplerCube tCube;\nuniform float tFlip;\nuniform float opacity;\nvarying vec3 vWorldDirection;\nvoid main() {\n	vec4 texColor = textureCube( tCube, vec3( tFlip * vWorldDirection.x, vWorldDirection.yz ) );\n	gl_FragColor = texColor;\n	gl_FragColor.a *= opacity;\n	#include <tonemapping_fragment>\n	#include <colorspace_fragment>\n}",depth_vert:"#include <common>\n#include <uv_pars_vertex>\n#include <displacementmap_pars_vertex>\n#include <morphtarget_pars_vertex>\n#include <skinning_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <clipping_planes_pars_vertex>\nvarying vec2 vHighPrecisionZW;\nvoid main() {\n	#include <uv_vertex>\n	#include <skinbase_vertex>\n	#ifdef USE_DISPLACEMENTMAP\n		#include <beginnormal_vertex>\n		#include <morphnormal_vertex>\n		#include <skinnormal_vertex>\n	#endif\n	#include <begin_vertex>\n	#include <morphtarget_vertex>\n	#include <skinning_vertex>\n	#include <displacementmap_vertex>\n	#include <project_vertex>\n	#include <logdepthbuf_vertex>\n	#include <clipping_planes_vertex>\n	vHighPrecisionZW = gl_Position.zw;\n}",depth_frag:"#if DEPTH_PACKING == 3200\n	uniform float opacity;\n#endif\n#include <common>\n#include <packing>\n#include <uv_pars_fragment>\n#include <map_pars_fragment>\n#include <alphamap_pars_fragment>\n#include <alphatest_pars_fragment>\n#include <alphahash_pars_fragment>\n#include <logdepthbuf_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvarying vec2 vHighPrecisionZW;\nvoid main() {\n	#include <clipping_planes_fragment>\n	vec4 diffuseColor = vec4( 1.0 );\n	#if DEPTH_PACKING == 3200\n		diffuseColor.a = opacity;\n	#endif\n	#include <map_fragment>\n	#include <alphamap_fragment>\n	#include <alphatest_fragment>\n	#include <alphahash_fragment>\n	#include <logdepthbuf_fragment>\n	float fragCoordZ = 0.5 * vHighPrecisionZW[0] / vHighPrecisionZW[1] + 0.5;\n	#if DEPTH_PACKING == 3200\n		gl_FragColor = vec4( vec3( 1.0 - fragCoordZ ), opacity );\n	#elif DEPTH_PACKING == 3201\n		gl_FragColor = packDepthToRGBA( fragCoordZ );\n	#endif\n}",distanceRGBA_vert:"#define DISTANCE\nvarying vec3 vWorldPosition;\n#include <common>\n#include <uv_pars_vertex>\n#include <displacementmap_pars_vertex>\n#include <morphtarget_pars_vertex>\n#include <skinning_pars_vertex>\n#include <clipping_planes_pars_vertex>\nvoid main() {\n	#include <uv_vertex>\n	#include <skinbase_vertex>\n	#ifdef USE_DISPLACEMENTMAP\n		#include <beginnormal_vertex>\n		#include <morphnormal_vertex>\n		#include <skinnormal_vertex>\n	#endif\n	#include <begin_vertex>\n	#include <morphtarget_vertex>\n	#include <skinning_vertex>\n	#include <displacementmap_vertex>\n	#include <project_vertex>\n	#include <worldpos_vertex>\n	#include <clipping_planes_vertex>\n	vWorldPosition = worldPosition.xyz;\n}",distanceRGBA_frag:"#define DISTANCE\nuniform vec3 referencePosition;\nuniform float nearDistance;\nuniform float farDistance;\nvarying vec3 vWorldPosition;\n#include <common>\n#include <packing>\n#include <uv_pars_fragment>\n#include <map_pars_fragment>\n#include <alphamap_pars_fragment>\n#include <alphatest_pars_fragment>\n#include <alphahash_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvoid main () {\n	#include <clipping_planes_fragment>\n	vec4 diffuseColor = vec4( 1.0 );\n	#include <map_fragment>\n	#include <alphamap_fragment>\n	#include <alphatest_fragment>\n	#include <alphahash_fragment>\n	float dist = length( vWorldPosition - referencePosition );\n	dist = ( dist - nearDistance ) / ( farDistance - nearDistance );\n	dist = saturate( dist );\n	gl_FragColor = packDepthToRGBA( dist );\n}",equirect_vert:"varying vec3 vWorldDirection;\n#include <common>\nvoid main() {\n	vWorldDirection = transformDirection( position, modelMatrix );\n	#include <begin_vertex>\n	#include <project_vertex>\n}",equirect_frag:"uniform sampler2D tEquirect;\nvarying vec3 vWorldDirection;\n#include <common>\nvoid main() {\n	vec3 direction = normalize( vWorldDirection );\n	vec2 sampleUV = equirectUv( direction );\n	gl_FragColor = texture2D( tEquirect, sampleUV );\n	#include <tonemapping_fragment>\n	#include <colorspace_fragment>\n}",linedashed_vert:"uniform float scale;\nattribute float lineDistance;\nvarying float vLineDistance;\n#include <common>\n#include <uv_pars_vertex>\n#include <color_pars_vertex>\n#include <fog_pars_vertex>\n#include <morphtarget_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <clipping_planes_pars_vertex>\nvoid main() {\n	vLineDistance = scale * lineDistance;\n	#include <uv_vertex>\n	#include <color_vertex>\n	#include <morphcolor_vertex>\n	#include <begin_vertex>\n	#include <morphtarget_vertex>\n	#include <project_vertex>\n	#include <logdepthbuf_vertex>\n	#include <clipping_planes_vertex>\n	#include <fog_vertex>\n}",linedashed_frag:"uniform vec3 diffuse;\nuniform float opacity;\nuniform float dashSize;\nuniform float totalSize;\nvarying float vLineDistance;\n#include <common>\n#include <color_pars_fragment>\n#include <uv_pars_fragment>\n#include <map_pars_fragment>\n#include <fog_pars_fragment>\n#include <logdepthbuf_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvoid main() {\n	#include <clipping_planes_fragment>\n	if ( mod( vLineDistance, totalSize ) > dashSize ) {\n		discard;\n	}\n	vec3 outgoingLight = vec3( 0.0 );\n	vec4 diffuseColor = vec4( diffuse, opacity );\n	#include <logdepthbuf_fragment>\n	#include <map_fragment>\n	#include <color_fragment>\n	outgoingLight = diffuseColor.rgb;\n	#include <opaque_fragment>\n	#include <tonemapping_fragment>\n	#include <colorspace_fragment>\n	#include <fog_fragment>\n	#include <premultiplied_alpha_fragment>\n}",meshbasic_vert:"#include <common>\n#include <uv_pars_vertex>\n#include <envmap_pars_vertex>\n#include <color_pars_vertex>\n#include <fog_pars_vertex>\n#include <morphtarget_pars_vertex>\n#include <skinning_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <clipping_planes_pars_vertex>\nvoid main() {\n	#include <uv_vertex>\n	#include <color_vertex>\n	#include <morphcolor_vertex>\n	#if defined ( USE_ENVMAP ) || defined ( USE_SKINNING )\n		#include <beginnormal_vertex>\n		#include <morphnormal_vertex>\n		#include <skinbase_vertex>\n		#include <skinnormal_vertex>\n		#include <defaultnormal_vertex>\n	#endif\n	#include <begin_vertex>\n	#include <morphtarget_vertex>\n	#include <skinning_vertex>\n	#include <project_vertex>\n	#include <logdepthbuf_vertex>\n	#include <clipping_planes_vertex>\n	#include <worldpos_vertex>\n	#include <envmap_vertex>\n	#include <fog_vertex>\n}",meshbasic_frag:"uniform vec3 diffuse;\nuniform float opacity;\n#ifndef FLAT_SHADED\n	varying vec3 vNormal;\n#endif\n#include <common>\n#include <dithering_pars_fragment>\n#include <color_pars_fragment>\n#include <uv_pars_fragment>\n#include <map_pars_fragment>\n#include <alphamap_pars_fragment>\n#include <alphatest_pars_fragment>\n#include <alphahash_pars_fragment>\n#include <aomap_pars_fragment>\n#include <lightmap_pars_fragment>\n#include <envmap_common_pars_fragment>\n#include <envmap_pars_fragment>\n#include <fog_pars_fragment>\n#include <specularmap_pars_fragment>\n#include <logdepthbuf_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvoid main() {\n	#include <clipping_planes_fragment>\n	vec4 diffuseColor = vec4( diffuse, opacity );\n	#include <logdepthbuf_fragment>\n	#include <map_fragment>\n	#include <color_fragment>\n	#include <alphamap_fragment>\n	#include <alphatest_fragment>\n	#include <alphahash_fragment>\n	#include <specularmap_fragment>\n	ReflectedLight reflectedLight = ReflectedLight( vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ) );\n	#ifdef USE_LIGHTMAP\n		vec4 lightMapTexel = texture2D( lightMap, vLightMapUv );\n		reflectedLight.indirectDiffuse += lightMapTexel.rgb * lightMapIntensity * RECIPROCAL_PI;\n	#else\n		reflectedLight.indirectDiffuse += vec3( 1.0 );\n	#endif\n	#include <aomap_fragment>\n	reflectedLight.indirectDiffuse *= diffuseColor.rgb;\n	vec3 outgoingLight = reflectedLight.indirectDiffuse;\n	#include <envmap_fragment>\n	#include <opaque_fragment>\n	#include <tonemapping_fragment>\n	#include <colorspace_fragment>\n	#include <fog_fragment>\n	#include <premultiplied_alpha_fragment>\n	#include <dithering_fragment>\n}",meshlambert_vert:"#define LAMBERT\nvarying vec3 vViewPosition;\n#include <common>\n#include <uv_pars_vertex>\n#include <displacementmap_pars_vertex>\n#include <envmap_pars_vertex>\n#include <color_pars_vertex>\n#include <fog_pars_vertex>\n#include <normal_pars_vertex>\n#include <morphtarget_pars_vertex>\n#include <skinning_pars_vertex>\n#include <shadowmap_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <clipping_planes_pars_vertex>\nvoid main() {\n	#include <uv_vertex>\n	#include <color_vertex>\n	#include <morphcolor_vertex>\n	#include <beginnormal_vertex>\n	#include <morphnormal_vertex>\n	#include <skinbase_vertex>\n	#include <skinnormal_vertex>\n	#include <defaultnormal_vertex>\n	#include <normal_vertex>\n	#include <begin_vertex>\n	#include <morphtarget_vertex>\n	#include <skinning_vertex>\n	#include <displacementmap_vertex>\n	#include <project_vertex>\n	#include <logdepthbuf_vertex>\n	#include <clipping_planes_vertex>\n	vViewPosition = - mvPosition.xyz;\n	#include <worldpos_vertex>\n	#include <envmap_vertex>\n	#include <shadowmap_vertex>\n	#include <fog_vertex>\n}",meshlambert_frag:"#define LAMBERT\nuniform vec3 diffuse;\nuniform vec3 emissive;\nuniform float opacity;\n#include <common>\n#include <packing>\n#include <dithering_pars_fragment>\n#include <color_pars_fragment>\n#include <uv_pars_fragment>\n#include <map_pars_fragment>\n#include <alphamap_pars_fragment>\n#include <alphatest_pars_fragment>\n#include <alphahash_pars_fragment>\n#include <aomap_pars_fragment>\n#include <lightmap_pars_fragment>\n#include <emissivemap_pars_fragment>\n#include <envmap_common_pars_fragment>\n#include <envmap_pars_fragment>\n#include <fog_pars_fragment>\n#include <bsdfs>\n#include <lights_pars_begin>\n#include <normal_pars_fragment>\n#include <lights_lambert_pars_fragment>\n#include <shadowmap_pars_fragment>\n#include <bumpmap_pars_fragment>\n#include <normalmap_pars_fragment>\n#include <specularmap_pars_fragment>\n#include <logdepthbuf_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvoid main() {\n	#include <clipping_planes_fragment>\n	vec4 diffuseColor = vec4( diffuse, opacity );\n	ReflectedLight reflectedLight = ReflectedLight( vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ) );\n	vec3 totalEmissiveRadiance = emissive;\n	#include <logdepthbuf_fragment>\n	#include <map_fragment>\n	#include <color_fragment>\n	#include <alphamap_fragment>\n	#include <alphatest_fragment>\n	#include <alphahash_fragment>\n	#include <specularmap_fragment>\n	#include <normal_fragment_begin>\n	#include <normal_fragment_maps>\n	#include <emissivemap_fragment>\n	#include <lights_lambert_fragment>\n	#include <lights_fragment_begin>\n	#include <lights_fragment_maps>\n	#include <lights_fragment_end>\n	#include <aomap_fragment>\n	vec3 outgoingLight = reflectedLight.directDiffuse + reflectedLight.indirectDiffuse + totalEmissiveRadiance;\n	#include <envmap_fragment>\n	#include <opaque_fragment>\n	#include <tonemapping_fragment>\n	#include <colorspace_fragment>\n	#include <fog_fragment>\n	#include <premultiplied_alpha_fragment>\n	#include <dithering_fragment>\n}",meshmatcap_vert:"#define MATCAP\nvarying vec3 vViewPosition;\n#include <common>\n#include <uv_pars_vertex>\n#include <color_pars_vertex>\n#include <displacementmap_pars_vertex>\n#include <fog_pars_vertex>\n#include <normal_pars_vertex>\n#include <morphtarget_pars_vertex>\n#include <skinning_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <clipping_planes_pars_vertex>\nvoid main() {\n	#include <uv_vertex>\n	#include <color_vertex>\n	#include <morphcolor_vertex>\n	#include <beginnormal_vertex>\n	#include <morphnormal_vertex>\n	#include <skinbase_vertex>\n	#include <skinnormal_vertex>\n	#include <defaultnormal_vertex>\n	#include <normal_vertex>\n	#include <begin_vertex>\n	#include <morphtarget_vertex>\n	#include <skinning_vertex>\n	#include <displacementmap_vertex>\n	#include <project_vertex>\n	#include <logdepthbuf_vertex>\n	#include <clipping_planes_vertex>\n	#include <fog_vertex>\n	vViewPosition = - mvPosition.xyz;\n}",meshmatcap_frag:"#define MATCAP\nuniform vec3 diffuse;\nuniform float opacity;\nuniform sampler2D matcap;\nvarying vec3 vViewPosition;\n#include <common>\n#include <dithering_pars_fragment>\n#include <color_pars_fragment>\n#include <uv_pars_fragment>\n#include <map_pars_fragment>\n#include <alphamap_pars_fragment>\n#include <alphatest_pars_fragment>\n#include <alphahash_pars_fragment>\n#include <fog_pars_fragment>\n#include <normal_pars_fragment>\n#include <bumpmap_pars_fragment>\n#include <normalmap_pars_fragment>\n#include <logdepthbuf_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvoid main() {\n	#include <clipping_planes_fragment>\n	vec4 diffuseColor = vec4( diffuse, opacity );\n	#include <logdepthbuf_fragment>\n	#include <map_fragment>\n	#include <color_fragment>\n	#include <alphamap_fragment>\n	#include <alphatest_fragment>\n	#include <alphahash_fragment>\n	#include <normal_fragment_begin>\n	#include <normal_fragment_maps>\n	vec3 viewDir = normalize( vViewPosition );\n	vec3 x = normalize( vec3( viewDir.z, 0.0, - viewDir.x ) );\n	vec3 y = cross( viewDir, x );\n	vec2 uv = vec2( dot( x, normal ), dot( y, normal ) ) * 0.495 + 0.5;\n	#ifdef USE_MATCAP\n		vec4 matcapColor = texture2D( matcap, uv );\n	#else\n		vec4 matcapColor = vec4( vec3( mix( 0.2, 0.8, uv.y ) ), 1.0 );\n	#endif\n	vec3 outgoingLight = diffuseColor.rgb * matcapColor.rgb;\n	#include <opaque_fragment>\n	#include <tonemapping_fragment>\n	#include <colorspace_fragment>\n	#include <fog_fragment>\n	#include <premultiplied_alpha_fragment>\n	#include <dithering_fragment>\n}",meshnormal_vert:"#define NORMAL\n#if defined( FLAT_SHADED ) || defined( USE_BUMPMAP ) || defined( USE_NORMALMAP_TANGENTSPACE )\n	varying vec3 vViewPosition;\n#endif\n#include <common>\n#include <uv_pars_vertex>\n#include <displacementmap_pars_vertex>\n#include <normal_pars_vertex>\n#include <morphtarget_pars_vertex>\n#include <skinning_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <clipping_planes_pars_vertex>\nvoid main() {\n	#include <uv_vertex>\n	#include <beginnormal_vertex>\n	#include <morphnormal_vertex>\n	#include <skinbase_vertex>\n	#include <skinnormal_vertex>\n	#include <defaultnormal_vertex>\n	#include <normal_vertex>\n	#include <begin_vertex>\n	#include <morphtarget_vertex>\n	#include <skinning_vertex>\n	#include <displacementmap_vertex>\n	#include <project_vertex>\n	#include <logdepthbuf_vertex>\n	#include <clipping_planes_vertex>\n#if defined( FLAT_SHADED ) || defined( USE_BUMPMAP ) || defined( USE_NORMALMAP_TANGENTSPACE )\n	vViewPosition = - mvPosition.xyz;\n#endif\n}",meshnormal_frag:"#define NORMAL\nuniform float opacity;\n#if defined( FLAT_SHADED ) || defined( USE_BUMPMAP ) || defined( USE_NORMALMAP_TANGENTSPACE )\n	varying vec3 vViewPosition;\n#endif\n#include <packing>\n#include <uv_pars_fragment>\n#include <normal_pars_fragment>\n#include <bumpmap_pars_fragment>\n#include <normalmap_pars_fragment>\n#include <logdepthbuf_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvoid main() {\n	#include <clipping_planes_fragment>\n	#include <logdepthbuf_fragment>\n	#include <normal_fragment_begin>\n	#include <normal_fragment_maps>\n	gl_FragColor = vec4( packNormalToRGB( normal ), opacity );\n	#ifdef OPAQUE\n		gl_FragColor.a = 1.0;\n	#endif\n}",meshphong_vert:"#define PHONG\nvarying vec3 vViewPosition;\n#include <common>\n#include <uv_pars_vertex>\n#include <displacementmap_pars_vertex>\n#include <envmap_pars_vertex>\n#include <color_pars_vertex>\n#include <fog_pars_vertex>\n#include <normal_pars_vertex>\n#include <morphtarget_pars_vertex>\n#include <skinning_pars_vertex>\n#include <shadowmap_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <clipping_planes_pars_vertex>\nvoid main() {\n	#include <uv_vertex>\n	#include <color_vertex>\n	#include <morphcolor_vertex>\n	#include <beginnormal_vertex>\n	#include <morphnormal_vertex>\n	#include <skinbase_vertex>\n	#include <skinnormal_vertex>\n	#include <defaultnormal_vertex>\n	#include <normal_vertex>\n	#include <begin_vertex>\n	#include <morphtarget_vertex>\n	#include <skinning_vertex>\n	#include <displacementmap_vertex>\n	#include <project_vertex>\n	#include <logdepthbuf_vertex>\n	#include <clipping_planes_vertex>\n	vViewPosition = - mvPosition.xyz;\n	#include <worldpos_vertex>\n	#include <envmap_vertex>\n	#include <shadowmap_vertex>\n	#include <fog_vertex>\n}",meshphong_frag:"#define PHONG\nuniform vec3 diffuse;\nuniform vec3 emissive;\nuniform vec3 specular;\nuniform float shininess;\nuniform float opacity;\n#include <common>\n#include <packing>\n#include <dithering_pars_fragment>\n#include <color_pars_fragment>\n#include <uv_pars_fragment>\n#include <map_pars_fragment>\n#include <alphamap_pars_fragment>\n#include <alphatest_pars_fragment>\n#include <alphahash_pars_fragment>\n#include <aomap_pars_fragment>\n#include <lightmap_pars_fragment>\n#include <emissivemap_pars_fragment>\n#include <envmap_common_pars_fragment>\n#include <envmap_pars_fragment>\n#include <fog_pars_fragment>\n#include <bsdfs>\n#include <lights_pars_begin>\n#include <normal_pars_fragment>\n#include <lights_phong_pars_fragment>\n#include <shadowmap_pars_fragment>\n#include <bumpmap_pars_fragment>\n#include <normalmap_pars_fragment>\n#include <specularmap_pars_fragment>\n#include <logdepthbuf_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvoid main() {\n	#include <clipping_planes_fragment>\n	vec4 diffuseColor = vec4( diffuse, opacity );\n	ReflectedLight reflectedLight = ReflectedLight( vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ) );\n	vec3 totalEmissiveRadiance = emissive;\n	#include <logdepthbuf_fragment>\n	#include <map_fragment>\n	#include <color_fragment>\n	#include <alphamap_fragment>\n	#include <alphatest_fragment>\n	#include <alphahash_fragment>\n	#include <specularmap_fragment>\n	#include <normal_fragment_begin>\n	#include <normal_fragment_maps>\n	#include <emissivemap_fragment>\n	#include <lights_phong_fragment>\n	#include <lights_fragment_begin>\n	#include <lights_fragment_maps>\n	#include <lights_fragment_end>\n	#include <aomap_fragment>\n	vec3 outgoingLight = reflectedLight.directDiffuse + reflectedLight.indirectDiffuse + reflectedLight.directSpecular + reflectedLight.indirectSpecular + totalEmissiveRadiance;\n	#include <envmap_fragment>\n	#include <opaque_fragment>\n	#include <tonemapping_fragment>\n	#include <colorspace_fragment>\n	#include <fog_fragment>\n	#include <premultiplied_alpha_fragment>\n	#include <dithering_fragment>\n}",meshphysical_vert:"#define STANDARD\nvarying vec3 vViewPosition;\n#ifdef USE_TRANSMISSION\n	varying vec3 vWorldPosition;\n#endif\n#include <common>\n#include <uv_pars_vertex>\n#include <displacementmap_pars_vertex>\n#include <color_pars_vertex>\n#include <fog_pars_vertex>\n#include <normal_pars_vertex>\n#include <morphtarget_pars_vertex>\n#include <skinning_pars_vertex>\n#include <shadowmap_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <clipping_planes_pars_vertex>\nvoid main() {\n	#include <uv_vertex>\n	#include <color_vertex>\n	#include <morphcolor_vertex>\n	#include <beginnormal_vertex>\n	#include <morphnormal_vertex>\n	#include <skinbase_vertex>\n	#include <skinnormal_vertex>\n	#include <defaultnormal_vertex>\n	#include <normal_vertex>\n	#include <begin_vertex>\n	#include <morphtarget_vertex>\n	#include <skinning_vertex>\n	#include <displacementmap_vertex>\n	#include <project_vertex>\n	#include <logdepthbuf_vertex>\n	#include <clipping_planes_vertex>\n	vViewPosition = - mvPosition.xyz;\n	#include <worldpos_vertex>\n	#include <shadowmap_vertex>\n	#include <fog_vertex>\n#ifdef USE_TRANSMISSION\n	vWorldPosition = worldPosition.xyz;\n#endif\n}",meshphysical_frag:"#define STANDARD\n#ifdef PHYSICAL\n	#define IOR\n	#define USE_SPECULAR\n#endif\nuniform vec3 diffuse;\nuniform vec3 emissive;\nuniform float roughness;\nuniform float metalness;\nuniform float opacity;\n#ifdef IOR\n	uniform float ior;\n#endif\n#ifdef USE_SPECULAR\n	uniform float specularIntensity;\n	uniform vec3 specularColor;\n	#ifdef USE_SPECULAR_COLORMAP\n		uniform sampler2D specularColorMap;\n	#endif\n	#ifdef USE_SPECULAR_INTENSITYMAP\n		uniform sampler2D specularIntensityMap;\n	#endif\n#endif\n#ifdef USE_CLEARCOAT\n	uniform float clearcoat;\n	uniform float clearcoatRoughness;\n#endif\n#ifdef USE_IRIDESCENCE\n	uniform float iridescence;\n	uniform float iridescenceIOR;\n	uniform float iridescenceThicknessMinimum;\n	uniform float iridescenceThicknessMaximum;\n#endif\n#ifdef USE_SHEEN\n	uniform vec3 sheenColor;\n	uniform float sheenRoughness;\n	#ifdef USE_SHEEN_COLORMAP\n		uniform sampler2D sheenColorMap;\n	#endif\n	#ifdef USE_SHEEN_ROUGHNESSMAP\n		uniform sampler2D sheenRoughnessMap;\n	#endif\n#endif\n#ifdef USE_ANISOTROPY\n	uniform vec2 anisotropyVector;\n	#ifdef USE_ANISOTROPYMAP\n		uniform sampler2D anisotropyMap;\n	#endif\n#endif\nvarying vec3 vViewPosition;\n#include <common>\n#include <packing>\n#include <dithering_pars_fragment>\n#include <color_pars_fragment>\n#include <uv_pars_fragment>\n#include <map_pars_fragment>\n#include <alphamap_pars_fragment>\n#include <alphatest_pars_fragment>\n#include <alphahash_pars_fragment>\n#include <aomap_pars_fragment>\n#include <lightmap_pars_fragment>\n#include <emissivemap_pars_fragment>\n#include <iridescence_fragment>\n#include <cube_uv_reflection_fragment>\n#include <envmap_common_pars_fragment>\n#include <envmap_physical_pars_fragment>\n#include <fog_pars_fragment>\n#include <lights_pars_begin>\n#include <normal_pars_fragment>\n#include <lights_physical_pars_fragment>\n#include <transmission_pars_fragment>\n#include <shadowmap_pars_fragment>\n#include <bumpmap_pars_fragment>\n#include <normalmap_pars_fragment>\n#include <clearcoat_pars_fragment>\n#include <iridescence_pars_fragment>\n#include <roughnessmap_pars_fragment>\n#include <metalnessmap_pars_fragment>\n#include <logdepthbuf_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvoid main() {\n	#include <clipping_planes_fragment>\n	vec4 diffuseColor = vec4( diffuse, opacity );\n	ReflectedLight reflectedLight = ReflectedLight( vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ) );\n	vec3 totalEmissiveRadiance = emissive;\n	#include <logdepthbuf_fragment>\n	#include <map_fragment>\n	#include <color_fragment>\n	#include <alphamap_fragment>\n	#include <alphatest_fragment>\n	#include <alphahash_fragment>\n	#include <roughnessmap_fragment>\n	#include <metalnessmap_fragment>\n	#include <normal_fragment_begin>\n	#include <normal_fragment_maps>\n	#include <clearcoat_normal_fragment_begin>\n	#include <clearcoat_normal_fragment_maps>\n	#include <emissivemap_fragment>\n	#include <lights_physical_fragment>\n	#include <lights_fragment_begin>\n	#include <lights_fragment_maps>\n	#include <lights_fragment_end>\n	#include <aomap_fragment>\n	vec3 totalDiffuse = reflectedLight.directDiffuse + reflectedLight.indirectDiffuse;\n	vec3 totalSpecular = reflectedLight.directSpecular + reflectedLight.indirectSpecular;\n	#include <transmission_fragment>\n	vec3 outgoingLight = totalDiffuse + totalSpecular + totalEmissiveRadiance;\n	#ifdef USE_SHEEN\n		float sheenEnergyComp = 1.0 - 0.157 * max3( material.sheenColor );\n		outgoingLight = outgoingLight * sheenEnergyComp + sheenSpecularDirect + sheenSpecularIndirect;\n	#endif\n	#ifdef USE_CLEARCOAT\n		float dotNVcc = saturate( dot( geometryClearcoatNormal, geometryViewDir ) );\n		vec3 Fcc = F_Schlick( material.clearcoatF0, material.clearcoatF90, dotNVcc );\n		outgoingLight = outgoingLight * ( 1.0 - material.clearcoat * Fcc ) + ( clearcoatSpecularDirect + clearcoatSpecularIndirect ) * material.clearcoat;\n	#endif\n	#include <opaque_fragment>\n	#include <tonemapping_fragment>\n	#include <colorspace_fragment>\n	#include <fog_fragment>\n	#include <premultiplied_alpha_fragment>\n	#include <dithering_fragment>\n}",meshtoon_vert:"#define TOON\nvarying vec3 vViewPosition;\n#include <common>\n#include <uv_pars_vertex>\n#include <displacementmap_pars_vertex>\n#include <color_pars_vertex>\n#include <fog_pars_vertex>\n#include <normal_pars_vertex>\n#include <morphtarget_pars_vertex>\n#include <skinning_pars_vertex>\n#include <shadowmap_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <clipping_planes_pars_vertex>\nvoid main() {\n	#include <uv_vertex>\n	#include <color_vertex>\n	#include <morphcolor_vertex>\n	#include <beginnormal_vertex>\n	#include <morphnormal_vertex>\n	#include <skinbase_vertex>\n	#include <skinnormal_vertex>\n	#include <defaultnormal_vertex>\n	#include <normal_vertex>\n	#include <begin_vertex>\n	#include <morphtarget_vertex>\n	#include <skinning_vertex>\n	#include <displacementmap_vertex>\n	#include <project_vertex>\n	#include <logdepthbuf_vertex>\n	#include <clipping_planes_vertex>\n	vViewPosition = - mvPosition.xyz;\n	#include <worldpos_vertex>\n	#include <shadowmap_vertex>\n	#include <fog_vertex>\n}",meshtoon_frag:"#define TOON\nuniform vec3 diffuse;\nuniform vec3 emissive;\nuniform float opacity;\n#include <common>\n#include <packing>\n#include <dithering_pars_fragment>\n#include <color_pars_fragment>\n#include <uv_pars_fragment>\n#include <map_pars_fragment>\n#include <alphamap_pars_fragment>\n#include <alphatest_pars_fragment>\n#include <alphahash_pars_fragment>\n#include <aomap_pars_fragment>\n#include <lightmap_pars_fragment>\n#include <emissivemap_pars_fragment>\n#include <gradientmap_pars_fragment>\n#include <fog_pars_fragment>\n#include <bsdfs>\n#include <lights_pars_begin>\n#include <normal_pars_fragment>\n#include <lights_toon_pars_fragment>\n#include <shadowmap_pars_fragment>\n#include <bumpmap_pars_fragment>\n#include <normalmap_pars_fragment>\n#include <logdepthbuf_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvoid main() {\n	#include <clipping_planes_fragment>\n	vec4 diffuseColor = vec4( diffuse, opacity );\n	ReflectedLight reflectedLight = ReflectedLight( vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ) );\n	vec3 totalEmissiveRadiance = emissive;\n	#include <logdepthbuf_fragment>\n	#include <map_fragment>\n	#include <color_fragment>\n	#include <alphamap_fragment>\n	#include <alphatest_fragment>\n	#include <alphahash_fragment>\n	#include <normal_fragment_begin>\n	#include <normal_fragment_maps>\n	#include <emissivemap_fragment>\n	#include <lights_toon_fragment>\n	#include <lights_fragment_begin>\n	#include <lights_fragment_maps>\n	#include <lights_fragment_end>\n	#include <aomap_fragment>\n	vec3 outgoingLight = reflectedLight.directDiffuse + reflectedLight.indirectDiffuse + totalEmissiveRadiance;\n	#include <opaque_fragment>\n	#include <tonemapping_fragment>\n	#include <colorspace_fragment>\n	#include <fog_fragment>\n	#include <premultiplied_alpha_fragment>\n	#include <dithering_fragment>\n}",points_vert:"uniform float size;\nuniform float scale;\n#include <common>\n#include <color_pars_vertex>\n#include <fog_pars_vertex>\n#include <morphtarget_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <clipping_planes_pars_vertex>\n#ifdef USE_POINTS_UV\n	varying vec2 vUv;\n	uniform mat3 uvTransform;\n#endif\nvoid main() {\n	#ifdef USE_POINTS_UV\n		vUv = ( uvTransform * vec3( uv, 1 ) ).xy;\n	#endif\n	#include <color_vertex>\n	#include <morphcolor_vertex>\n	#include <begin_vertex>\n	#include <morphtarget_vertex>\n	#include <project_vertex>\n	gl_PointSize = size;\n	#ifdef USE_SIZEATTENUATION\n		bool isPerspective = isPerspectiveMatrix( projectionMatrix );\n		if ( isPerspective ) gl_PointSize *= ( scale / - mvPosition.z );\n	#endif\n	#include <logdepthbuf_vertex>\n	#include <clipping_planes_vertex>\n	#include <worldpos_vertex>\n	#include <fog_vertex>\n}",points_frag:"uniform vec3 diffuse;\nuniform float opacity;\n#include <common>\n#include <color_pars_fragment>\n#include <map_particle_pars_fragment>\n#include <alphatest_pars_fragment>\n#include <alphahash_pars_fragment>\n#include <fog_pars_fragment>\n#include <logdepthbuf_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvoid main() {\n	#include <clipping_planes_fragment>\n	vec3 outgoingLight = vec3( 0.0 );\n	vec4 diffuseColor = vec4( diffuse, opacity );\n	#include <logdepthbuf_fragment>\n	#include <map_particle_fragment>\n	#include <color_fragment>\n	#include <alphatest_fragment>\n	#include <alphahash_fragment>\n	outgoingLight = diffuseColor.rgb;\n	#include <opaque_fragment>\n	#include <tonemapping_fragment>\n	#include <colorspace_fragment>\n	#include <fog_fragment>\n	#include <premultiplied_alpha_fragment>\n}",shadow_vert:"#include <common>\n#include <fog_pars_vertex>\n#include <morphtarget_pars_vertex>\n#include <skinning_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <shadowmap_pars_vertex>\nvoid main() {\n	#include <beginnormal_vertex>\n	#include <morphnormal_vertex>\n	#include <skinbase_vertex>\n	#include <skinnormal_vertex>\n	#include <defaultnormal_vertex>\n	#include <begin_vertex>\n	#include <morphtarget_vertex>\n	#include <skinning_vertex>\n	#include <project_vertex>\n	#include <logdepthbuf_vertex>\n	#include <worldpos_vertex>\n	#include <shadowmap_vertex>\n	#include <fog_vertex>\n}",shadow_frag:"uniform vec3 color;\nuniform float opacity;\n#include <common>\n#include <packing>\n#include <fog_pars_fragment>\n#include <bsdfs>\n#include <lights_pars_begin>\n#include <logdepthbuf_pars_fragment>\n#include <shadowmap_pars_fragment>\n#include <shadowmask_pars_fragment>\nvoid main() {\n	#include <logdepthbuf_fragment>\n	gl_FragColor = vec4( color, opacity * ( 1.0 - getShadowMask() ) );\n	#include <tonemapping_fragment>\n	#include <colorspace_fragment>\n	#include <fog_fragment>\n}",sprite_vert:"uniform float rotation;\nuniform vec2 center;\n#include <common>\n#include <uv_pars_vertex>\n#include <fog_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <clipping_planes_pars_vertex>\nvoid main() {\n	#include <uv_vertex>\n	vec4 mvPosition = modelViewMatrix * vec4( 0.0, 0.0, 0.0, 1.0 );\n	vec2 scale;\n	scale.x = length( vec3( modelMatrix[ 0 ].x, modelMatrix[ 0 ].y, modelMatrix[ 0 ].z ) );\n	scale.y = length( vec3( modelMatrix[ 1 ].x, modelMatrix[ 1 ].y, modelMatrix[ 1 ].z ) );\n	#ifndef USE_SIZEATTENUATION\n		bool isPerspective = isPerspectiveMatrix( projectionMatrix );\n		if ( isPerspective ) scale *= - mvPosition.z;\n	#endif\n	vec2 alignedPosition = ( position.xy - ( center - vec2( 0.5 ) ) ) * scale;\n	vec2 rotatedPosition;\n	rotatedPosition.x = cos( rotation ) * alignedPosition.x - sin( rotation ) * alignedPosition.y;\n	rotatedPosition.y = sin( rotation ) * alignedPosition.x + cos( rotation ) * alignedPosition.y;\n	mvPosition.xy += rotatedPosition;\n	gl_Position = projectionMatrix * mvPosition;\n	#include <logdepthbuf_vertex>\n	#include <clipping_planes_vertex>\n	#include <fog_vertex>\n}",sprite_frag:"uniform vec3 diffuse;\nuniform float opacity;\n#include <common>\n#include <uv_pars_fragment>\n#include <map_pars_fragment>\n#include <alphamap_pars_fragment>\n#include <alphatest_pars_fragment>\n#include <alphahash_pars_fragment>\n#include <fog_pars_fragment>\n#include <logdepthbuf_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvoid main() {\n	#include <clipping_planes_fragment>\n	vec3 outgoingLight = vec3( 0.0 );\n	vec4 diffuseColor = vec4( diffuse, opacity );\n	#include <logdepthbuf_fragment>\n	#include <map_fragment>\n	#include <alphamap_fragment>\n	#include <alphatest_fragment>\n	#include <alphahash_fragment>\n	outgoingLight = diffuseColor.rgb;\n	#include <opaque_fragment>\n	#include <tonemapping_fragment>\n	#include <colorspace_fragment>\n	#include <fog_fragment>\n}"},t9={common:{diffuse:{value:new tn(0xffffff)},opacity:{value:1},map:{value:null},mapTransform:{value:new E},alphaMap:{value:null},alphaMapTransform:{value:new E},alphaTest:{value:0}},specularmap:{specularMap:{value:null},specularMapTransform:{value:new E}},envmap:{envMap:{value:null},flipEnvMap:{value:-1},reflectivity:{value:1},ior:{value:1.5},refractionRatio:{value:.98}},aomap:{aoMap:{value:null},aoMapIntensity:{value:1},aoMapTransform:{value:new E}},lightmap:{lightMap:{value:null},lightMapIntensity:{value:1},lightMapTransform:{value:new E}},bumpmap:{bumpMap:{value:null},bumpMapTransform:{value:new E},bumpScale:{value:1}},normalmap:{normalMap:{value:null},normalMapTransform:{value:new E},normalScale:{value:new S(1,1)}},displacementmap:{displacementMap:{value:null},displacementMapTransform:{value:new E},displacementScale:{value:1},displacementBias:{value:0}},emissivemap:{emissiveMap:{value:null},emissiveMapTransform:{value:new E}},metalnessmap:{metalnessMap:{value:null},metalnessMapTransform:{value:new E}},roughnessmap:{roughnessMap:{value:null},roughnessMapTransform:{value:new E}},gradientmap:{gradientMap:{value:null}},fog:{fogDensity:{value:25e-5},fogNear:{value:1},fogFar:{value:2e3},fogColor:{value:new tn(0xffffff)}},lights:{ambientLightColor:{value:[]},lightProbe:{value:[]},directionalLights:{value:[],properties:{direction:{},color:{}}},directionalLightShadows:{value:[],properties:{shadowBias:{},shadowNormalBias:{},shadowRadius:{},shadowMapSize:{}}},directionalShadowMap:{value:[]},directionalShadowMatrix:{value:[]},spotLights:{value:[],properties:{color:{},position:{},direction:{},distance:{},coneCos:{},penumbraCos:{},decay:{}}},spotLightShadows:{value:[],properties:{shadowBias:{},shadowNormalBias:{},shadowRadius:{},shadowMapSize:{}}},spotLightMap:{value:[]},spotShadowMap:{value:[]},spotLightMatrix:{value:[]},pointLights:{value:[],properties:{color:{},position:{},decay:{},distance:{}}},pointLightShadows:{value:[],properties:{shadowBias:{},shadowNormalBias:{},shadowRadius:{},shadowMapSize:{},shadowCameraNear:{},shadowCameraFar:{}}},pointShadowMap:{value:[]},pointShadowMatrix:{value:[]},hemisphereLights:{value:[],properties:{direction:{},skyColor:{},groundColor:{}}},rectAreaLights:{value:[],properties:{color:{},position:{},width:{},height:{}}},ltc_1:{value:null},ltc_2:{value:null}},points:{diffuse:{value:new tn(0xffffff)},opacity:{value:1},size:{value:1},scale:{value:1},map:{value:null},alphaMap:{value:null},alphaMapTransform:{value:new E},alphaTest:{value:0},uvTransform:{value:new E}},sprite:{diffuse:{value:new tn(0xffffff)},opacity:{value:1},center:{value:new S(.5,.5)},rotation:{value:0},map:{value:null},mapTransform:{value:new E},alphaMap:{value:null},alphaMapTransform:{value:new E},alphaTest:{value:0}}},t7={basic:{uniforms:tG([t9.common,t9.specularmap,t9.envmap,t9.aomap,t9.lightmap,t9.fog]),vertexShader:t8.meshbasic_vert,fragmentShader:t8.meshbasic_frag},lambert:{uniforms:tG([t9.common,t9.specularmap,t9.envmap,t9.aomap,t9.lightmap,t9.emissivemap,t9.bumpmap,t9.normalmap,t9.displacementmap,t9.fog,t9.lights,{emissive:{value:new tn(0)}}]),vertexShader:t8.meshlambert_vert,fragmentShader:t8.meshlambert_frag},phong:{uniforms:tG([t9.common,t9.specularmap,t9.envmap,t9.aomap,t9.lightmap,t9.emissivemap,t9.bumpmap,t9.normalmap,t9.displacementmap,t9.fog,t9.lights,{emissive:{value:new tn(0)},specular:{value:new tn(1118481)},shininess:{value:30}}]),vertexShader:t8.meshphong_vert,fragmentShader:t8.meshphong_frag},standard:{uniforms:tG([t9.common,t9.envmap,t9.aomap,t9.lightmap,t9.emissivemap,t9.bumpmap,t9.normalmap,t9.displacementmap,t9.roughnessmap,t9.metalnessmap,t9.fog,t9.lights,{emissive:{value:new tn(0)},roughness:{value:1},metalness:{value:0},envMapIntensity:{value:1}}]),vertexShader:t8.meshphysical_vert,fragmentShader:t8.meshphysical_frag},toon:{uniforms:tG([t9.common,t9.aomap,t9.lightmap,t9.emissivemap,t9.bumpmap,t9.normalmap,t9.displacementmap,t9.gradientmap,t9.fog,t9.lights,{emissive:{value:new tn(0)}}]),vertexShader:t8.meshtoon_vert,fragmentShader:t8.meshtoon_frag},matcap:{uniforms:tG([t9.common,t9.bumpmap,t9.normalmap,t9.displacementmap,t9.fog,{matcap:{value:null}}]),vertexShader:t8.meshmatcap_vert,fragmentShader:t8.meshmatcap_frag},points:{uniforms:tG([t9.points,t9.fog]),vertexShader:t8.points_vert,fragmentShader:t8.points_frag},dashed:{uniforms:tG([t9.common,t9.fog,{scale:{value:1},dashSize:{value:1},totalSize:{value:2}}]),vertexShader:t8.linedashed_vert,fragmentShader:t8.linedashed_frag},depth:{uniforms:tG([t9.common,t9.displacementmap]),vertexShader:t8.depth_vert,fragmentShader:t8.depth_frag},normal:{uniforms:tG([t9.common,t9.bumpmap,t9.normalmap,t9.displacementmap,{opacity:{value:1}}]),vertexShader:t8.meshnormal_vert,fragmentShader:t8.meshnormal_frag},sprite:{uniforms:tG([t9.sprite,t9.fog]),vertexShader:t8.sprite_vert,fragmentShader:t8.sprite_frag},background:{uniforms:{uvTransform:{value:new E},t2D:{value:null},backgroundIntensity:{value:1}},vertexShader:t8.background_vert,fragmentShader:t8.background_frag},backgroundCube:{uniforms:{envMap:{value:null},flipEnvMap:{value:-1},backgroundBlurriness:{value:0},backgroundIntensity:{value:1}},vertexShader:t8.backgroundCube_vert,fragmentShader:t8.backgroundCube_frag},cube:{uniforms:{tCube:{value:null},tFlip:{value:-1},opacity:{value:1}},vertexShader:t8.cube_vert,fragmentShader:t8.cube_frag},equirect:{uniforms:{tEquirect:{value:null}},vertexShader:t8.equirect_vert,fragmentShader:t8.equirect_frag},distanceRGBA:{uniforms:tG([t9.common,t9.displacementmap,{referencePosition:{value:new Y},nearDistance:{value:1},farDistance:{value:1e3}}]),vertexShader:t8.distanceRGBA_vert,fragmentShader:t8.distanceRGBA_frag},shadow:{uniforms:tG([t9.lights,t9.fog,{color:{value:new tn(0)},opacity:{value:1}}]),vertexShader:t8.shadow_vert,fragmentShader:t8.shadow_frag}};t7.physical={uniforms:tG([t7.standard.uniforms,{clearcoat:{value:0},clearcoatMap:{value:null},clearcoatMapTransform:{value:new E},clearcoatNormalMap:{value:null},clearcoatNormalMapTransform:{value:new E},clearcoatNormalScale:{value:new S(1,1)},clearcoatRoughness:{value:0},clearcoatRoughnessMap:{value:null},clearcoatRoughnessMapTransform:{value:new E},iridescence:{value:0},iridescenceMap:{value:null},iridescenceMapTransform:{value:new E},iridescenceIOR:{value:1.3},iridescenceThicknessMinimum:{value:100},iridescenceThicknessMaximum:{value:400},iridescenceThicknessMap:{value:null},iridescenceThicknessMapTransform:{value:new E},sheen:{value:0},sheenColor:{value:new tn(0)},sheenColorMap:{value:null},sheenColorMapTransform:{value:new E},sheenRoughness:{value:1},sheenRoughnessMap:{value:null},sheenRoughnessMapTransform:{value:new E},transmission:{value:0},transmissionMap:{value:null},transmissionMapTransform:{value:new E},transmissionSamplerSize:{value:new S},transmissionSamplerMap:{value:null},thickness:{value:0},thicknessMap:{value:null},thicknessMapTransform:{value:new E},attenuationDistance:{value:0},attenuationColor:{value:new tn(0)},specularColor:{value:new tn(1,1,1)},specularColorMap:{value:null},specularColorMapTransform:{value:new E},specularIntensity:{value:1},specularIntensityMap:{value:null},specularIntensityMapTransform:{value:new E},anisotropyVector:{value:new S},anisotropyMap:{value:null},anisotropyMapTransform:{value:new E}}]),vertexShader:t8.meshphysical_vert,fragmentShader:t8.meshphysical_frag};let ne={r:0,b:0,g:0};function nt(e,t,n,i,r,a,s){let o,l,c=new tn(0),u=+(!0!==a),d=null,p=0,f=null;function m(t,n){t.getRGB(ne,tk(e)),i.buffers.color.setClear(ne.r,ne.g,ne.b,n,s)}return{getClearColor:function(){return c},setClearColor:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;c.set(e),m(c,u=t)},getClearAlpha:function(){return u},setClearAlpha:function(e){m(c,u=e)},render:function(a,g){let _=!1,v=!0===g.isScene?g.background:null;v&&v.isTexture&&(v=(g.backgroundBlurriness>0?n:t).get(v)),null===v?m(c,u):v&&v.isColor&&(m(v,1),_=!0);let x=e.xr.getEnvironmentBlendMode();"additive"===x?i.buffers.color.setClear(0,0,0,1,s):"alpha-blend"===x&&i.buffers.color.setClear(0,0,0,0,s),(e.autoClear||_)&&e.clear(e.autoClearColor,e.autoClearDepth,e.autoClearStencil),v&&(v.isCubeTexture||306===v.mapping)?(void 0===l&&((l=new tz(new tH(1,1,1),new tX({name:"BackgroundCubeMaterial",uniforms:tV(t7.backgroundCube.uniforms),vertexShader:t7.backgroundCube.vertexShader,fragmentShader:t7.backgroundCube.fragmentShader,side:1,depthTest:!1,depthWrite:!1,fog:!1}))).geometry.deleteAttribute("normal"),l.geometry.deleteAttribute("uv"),l.onBeforeRender=function(e,t,n){this.matrixWorld.copyPosition(n.matrixWorld)},Object.defineProperty(l.material,"envMap",{get:function(){return this.uniforms.envMap.value}}),r.update(l)),l.material.uniforms.envMap.value=v,l.material.uniforms.flipEnvMap.value=v.isCubeTexture&&!1===v.isRenderTargetTexture?-1:1,l.material.uniforms.backgroundBlurriness.value=g.backgroundBlurriness,l.material.uniforms.backgroundIntensity.value=g.backgroundIntensity,l.material.toneMapped=N.getTransfer(v.colorSpace)!==h,(d!==v||p!==v.version||f!==e.toneMapping)&&(l.material.needsUpdate=!0,d=v,p=v.version,f=e.toneMapping),l.layers.enableAll(),a.unshift(l,l.geometry,l.material,0,0,null)):v&&v.isTexture&&(void 0===o&&((o=new tz(new t6(2,2),new tX({name:"BackgroundMaterial",uniforms:tV(t7.background.uniforms),vertexShader:t7.background.vertexShader,fragmentShader:t7.background.fragmentShader,side:0,depthTest:!1,depthWrite:!1,fog:!1}))).geometry.deleteAttribute("normal"),Object.defineProperty(o.material,"map",{get:function(){return this.uniforms.t2D.value}}),r.update(o)),o.material.uniforms.t2D.value=v,o.material.uniforms.backgroundIntensity.value=g.backgroundIntensity,o.material.toneMapped=N.getTransfer(v.colorSpace)!==h,!0===v.matrixAutoUpdate&&v.updateMatrix(),o.material.uniforms.uvTransform.value.copy(v.matrix),(d!==v||p!==v.version||f!==e.toneMapping)&&(o.material.needsUpdate=!0,d=v,p=v.version,f=e.toneMapping),o.layers.enableAll(),a.unshift(o,o.geometry,o.material,0,0,null))}}}function nn(e,t,n,i){let r=e.getParameter(e.MAX_VERTEX_ATTRIBS),a=i.isWebGL2?null:t.get("OES_vertex_array_object"),s=i.isWebGL2||null!==a,o={},l=p(null),h=l,c=!1;function u(t){return i.isWebGL2?e.bindVertexArray(t):a.bindVertexArrayOES(t)}function d(t){return i.isWebGL2?e.deleteVertexArray(t):a.deleteVertexArrayOES(t)}function p(e){let t=[],n=[],i=[];for(let e=0;e<r;e++)t[e]=0,n[e]=0,i[e]=0;return{geometry:null,program:null,wireframe:!1,newAttributes:t,enabledAttributes:n,attributeDivisors:i,object:e,attributes:{},index:null}}function f(){let e=h.newAttributes;for(let t=0,n=e.length;t<n;t++)e[t]=0}function m(e){g(e,0)}function g(n,r){let a=h.newAttributes,s=h.enabledAttributes,o=h.attributeDivisors;a[n]=1,0===s[n]&&(e.enableVertexAttribArray(n),s[n]=1),o[n]!==r&&((i.isWebGL2?e:t.get("ANGLE_instanced_arrays"))[i.isWebGL2?"vertexAttribDivisor":"vertexAttribDivisorANGLE"](n,r),o[n]=r)}function _(){let t=h.newAttributes,n=h.enabledAttributes;for(let i=0,r=n.length;i<r;i++)n[i]!==t[i]&&(e.disableVertexAttribArray(i),n[i]=0)}function v(t,n,i,r,a,s,o){!0===o?e.vertexAttribIPointer(t,n,i,a,s):e.vertexAttribPointer(t,n,i,r,a,s)}function x(){y(),c=!0,h!==l&&u((h=l).object)}function y(){l.geometry=null,l.program=null,l.wireframe=!1}return{setup:function(r,l,d,x,y){let M=!1;if(s){let t=function(t,n,r){let s=!0===r.wireframe,l=o[t.id];void 0===l&&(l={},o[t.id]=l);let h=l[n.id];void 0===h&&(h={},l[n.id]=h);let c=h[s];return void 0===c&&(c=p(i.isWebGL2?e.createVertexArray():a.createVertexArrayOES()),h[s]=c),c}(x,d,l);h!==t&&u((h=t).object),(M=function(e,t,n,i){let r=h.attributes,a=t.attributes,s=0,o=n.getAttributes();for(let t in o)if(o[t].location>=0){let n=r[t],i=a[t];if(void 0===i&&("instanceMatrix"===t&&e.instanceMatrix&&(i=e.instanceMatrix),"instanceColor"===t&&e.instanceColor&&(i=e.instanceColor)),void 0===n||n.attribute!==i||i&&n.data!==i.data)return!0;s++}return h.attributesNum!==s||h.index!==i}(r,x,d,y))&&function(e,t,n,i){let r={},a=t.attributes,s=0,o=n.getAttributes();for(let t in o)if(o[t].location>=0){let n=a[t];void 0===n&&("instanceMatrix"===t&&e.instanceMatrix&&(n=e.instanceMatrix),"instanceColor"===t&&e.instanceColor&&(n=e.instanceColor));let i={};i.attribute=n,n&&n.data&&(i.data=n.data),r[t]=i,s++}h.attributes=r,h.attributesNum=s,h.index=i}(r,x,d,y)}else{let e=!0===l.wireframe;(h.geometry!==x.id||h.program!==d.id||h.wireframe!==e)&&(h.geometry=x.id,h.program=d.id,h.wireframe=e,M=!0)}null!==y&&n.update(y,e.ELEMENT_ARRAY_BUFFER),(M||c)&&(c=!1,function(r,a,s,o){if(!1===i.isWebGL2&&(r.isInstancedMesh||o.isInstancedBufferGeometry)&&null===t.get("ANGLE_instanced_arrays"))return;f();let l=o.attributes,h=s.getAttributes(),c=a.defaultAttributeValues;for(let t in h){let a=h[t];if(a.location>=0){let s=l[t];if(void 0===s&&("instanceMatrix"===t&&r.instanceMatrix&&(s=r.instanceMatrix),"instanceColor"===t&&r.instanceColor&&(s=r.instanceColor)),void 0!==s){let t=s.normalized,l=s.itemSize,h=n.get(s);if(void 0===h)continue;let c=h.buffer,u=h.type,d=h.bytesPerElement,p=!0===i.isWebGL2&&(u===e.INT||u===e.UNSIGNED_INT||1013===s.gpuType);if(s.isInterleavedBufferAttribute){let n=s.data,i=n.stride,h=s.offset;if(n.isInstancedInterleavedBuffer){for(let e=0;e<a.locationSize;e++)g(a.location+e,n.meshPerAttribute);!0!==r.isInstancedMesh&&void 0===o._maxInstanceCount&&(o._maxInstanceCount=n.meshPerAttribute*n.count)}else for(let e=0;e<a.locationSize;e++)m(a.location+e);e.bindBuffer(e.ARRAY_BUFFER,c);for(let e=0;e<a.locationSize;e++)v(a.location+e,l/a.locationSize,u,t,i*d,(h+l/a.locationSize*e)*d,p)}else{if(s.isInstancedBufferAttribute){for(let e=0;e<a.locationSize;e++)g(a.location+e,s.meshPerAttribute);!0!==r.isInstancedMesh&&void 0===o._maxInstanceCount&&(o._maxInstanceCount=s.meshPerAttribute*s.count)}else for(let e=0;e<a.locationSize;e++)m(a.location+e);e.bindBuffer(e.ARRAY_BUFFER,c);for(let e=0;e<a.locationSize;e++)v(a.location+e,l/a.locationSize,u,t,l*d,l/a.locationSize*e*d,p)}}else if(void 0!==c){let n=c[t];if(void 0!==n)switch(n.length){case 2:e.vertexAttrib2fv(a.location,n);break;case 3:e.vertexAttrib3fv(a.location,n);break;case 4:e.vertexAttrib4fv(a.location,n);break;default:e.vertexAttrib1fv(a.location,n)}}}}_()}(r,l,d,x),null!==y&&e.bindBuffer(e.ELEMENT_ARRAY_BUFFER,n.get(y).buffer))},reset:x,resetDefaultState:y,dispose:function(){for(let e in x(),o){let t=o[e];for(let e in t){let n=t[e];for(let e in n)d(n[e].object),delete n[e];delete t[e]}delete o[e]}},releaseStatesOfGeometry:function(e){if(void 0===o[e.id])return;let t=o[e.id];for(let e in t){let n=t[e];for(let e in n)d(n[e].object),delete n[e];delete t[e]}delete o[e.id]},releaseStatesOfProgram:function(e){for(let t in o){let n=o[t];if(void 0===n[e.id])continue;let i=n[e.id];for(let e in i)d(i[e].object),delete i[e];delete n[e.id]}},initAttributes:f,enableAttribute:m,disableUnusedAttributes:_}}function ni(e,t,n,i){let r,a=i.isWebGL2;this.setMode=function(e){r=e},this.render=function(t,i){e.drawArrays(r,t,i),n.update(i,r,1)},this.renderInstances=function(i,s,o){let l,h;if(0!==o){if(a)l=e,h="drawArraysInstanced";else if(l=t.get("ANGLE_instanced_arrays"),h="drawArraysInstancedANGLE",null===l)return void console.error("THREE.WebGLBufferRenderer: using THREE.InstancedBufferGeometry but hardware does not support extension ANGLE_instanced_arrays.");l[h](r,i,s,o),n.update(s,r,o)}}}function nr(e,t,n){let i;function r(t){if("highp"===t){if(e.getShaderPrecisionFormat(e.VERTEX_SHADER,e.HIGH_FLOAT).precision>0&&e.getShaderPrecisionFormat(e.FRAGMENT_SHADER,e.HIGH_FLOAT).precision>0)return"highp";t="mediump"}return"mediump"===t&&e.getShaderPrecisionFormat(e.VERTEX_SHADER,e.MEDIUM_FLOAT).precision>0&&e.getShaderPrecisionFormat(e.FRAGMENT_SHADER,e.MEDIUM_FLOAT).precision>0?"mediump":"lowp"}let a="undefined"!=typeof WebGL2RenderingContext&&"WebGL2RenderingContext"===e.constructor.name,s=void 0!==n.precision?n.precision:"highp",o=r(s);o!==s&&(console.warn("THREE.WebGLRenderer:",s,"not supported, using",o,"instead."),s=o);let l=a||t.has("WEBGL_draw_buffers"),h=!0===n.logarithmicDepthBuffer,c=e.getParameter(e.MAX_TEXTURE_IMAGE_UNITS),u=e.getParameter(e.MAX_VERTEX_TEXTURE_IMAGE_UNITS),d=e.getParameter(e.MAX_TEXTURE_SIZE),p=e.getParameter(e.MAX_CUBE_MAP_TEXTURE_SIZE),f=e.getParameter(e.MAX_VERTEX_ATTRIBS),m=e.getParameter(e.MAX_VERTEX_UNIFORM_VECTORS),g=e.getParameter(e.MAX_VARYING_VECTORS),_=e.getParameter(e.MAX_FRAGMENT_UNIFORM_VECTORS),v=u>0,x=a||t.has("OES_texture_float"),y=a?e.getParameter(e.MAX_SAMPLES):0;return{isWebGL2:a,drawBuffers:l,getMaxAnisotropy:function(){if(void 0!==i)return i;if(!0===t.has("EXT_texture_filter_anisotropic")){let n=t.get("EXT_texture_filter_anisotropic");i=e.getParameter(n.MAX_TEXTURE_MAX_ANISOTROPY_EXT)}else i=0;return i},getMaxPrecision:r,precision:s,logarithmicDepthBuffer:h,maxTextures:c,maxVertexTextures:u,maxTextureSize:d,maxCubemapSize:p,maxAttributes:f,maxVertexUniforms:m,maxVaryings:g,maxFragmentUniforms:_,vertexTextures:v,floatFragmentTextures:x,floatVertexTextures:v&&x,maxSamples:y}}function na(e){let t=this,n=null,i=0,r=!1,a=!1,s=new t0,o=new E,l={value:null,needsUpdate:!1};function h(e,n,i,r){let a=null!==e?e.length:0,h=null;if(0!==a){if(h=l.value,!0!==r||null===h){let t=i+4*a,r=n.matrixWorldInverse;o.getNormalMatrix(r),(null===h||h.length<t)&&(h=new Float32Array(t));for(let t=0,n=i;t!==a;++t,n+=4)s.copy(e[t]).applyMatrix4(r,o),s.normal.toArray(h,n),h[n+3]=s.constant}l.value=h,l.needsUpdate=!0}return t.numPlanes=a,t.numIntersection=0,h}this.uniform=l,this.numPlanes=0,this.numIntersection=0,this.init=function(e,t){let n=0!==e.length||t||0!==i||r;return r=t,i=e.length,n},this.beginShadows=function(){a=!0,h(null)},this.endShadows=function(){a=!1},this.setGlobalState=function(e,t){n=h(e,t,0)},this.setState=function(s,o,c){let u=s.clippingPlanes,d=s.clipIntersection,p=s.clipShadows,f=e.get(s);if(r&&null!==u&&0!==u.length&&(!a||p)){let e=a?0:i,t=4*e,r=f.clippingState||null;l.value=r,r=h(u,o,t,c);for(let e=0;e!==t;++e)r[e]=n[e];f.clippingState=r,this.numIntersection=d?this.numPlanes:0,this.numPlanes+=e}else a?h(null):(l.value!==n&&(l.value=n,l.needsUpdate=i>0),t.numPlanes=i,t.numIntersection=0)}}function ns(e){let t=new WeakMap;function n(e,t){return 303===t?e.mapping=301:304===t&&(e.mapping=302),e}function i(e){let n=e.target;n.removeEventListener("dispose",i);let r=t.get(n);void 0!==r&&(t.delete(n),r.dispose())}return{get:function(r){if(r&&r.isTexture&&!1===r.isRenderTargetTexture){let a=r.mapping;if(303===a||304===a)if(t.has(r))return n(t.get(r).texture,r.mapping);else{let a=r.image;if(!a||!(a.height>0))return null;{let s=new tZ(a.height/2);return s.fromEquirectangularTexture(e,r),t.set(r,s),r.addEventListener("dispose",i),n(s.texture,r.mapping)}}}return r},dispose:function(){t=new WeakMap}}}class no extends tj{copy(e,t){return super.copy(e,t),this.left=e.left,this.right=e.right,this.top=e.top,this.bottom=e.bottom,this.near=e.near,this.far=e.far,this.zoom=e.zoom,this.view=null===e.view?null:Object.assign({},e.view),this}setViewOffset(e,t,n,i,r,a){null===this.view&&(this.view={enabled:!0,fullWidth:1,fullHeight:1,offsetX:0,offsetY:0,width:1,height:1}),this.view.enabled=!0,this.view.fullWidth=e,this.view.fullHeight=t,this.view.offsetX=n,this.view.offsetY=i,this.view.width=r,this.view.height=a,this.updateProjectionMatrix()}clearViewOffset(){null!==this.view&&(this.view.enabled=!1),this.updateProjectionMatrix()}updateProjectionMatrix(){let e=(this.right-this.left)/(2*this.zoom),t=(this.top-this.bottom)/(2*this.zoom),n=(this.right+this.left)/2,i=(this.top+this.bottom)/2,r=n-e,a=n+e,s=i+t,o=i-t;if(null!==this.view&&this.view.enabled){let e=(this.right-this.left)/this.view.fullWidth/this.zoom,t=(this.top-this.bottom)/this.view.fullHeight/this.zoom;r+=e*this.view.offsetX,a=r+e*this.view.width,s-=t*this.view.offsetY,o=s-t*this.view.height}this.projectionMatrix.makeOrthographic(r,a,s,o,this.near,this.far,this.coordinateSystem),this.projectionMatrixInverse.copy(this.projectionMatrix).invert()}toJSON(e){let t=super.toJSON(e);return t.object.zoom=this.zoom,t.object.left=this.left,t.object.right=this.right,t.object.top=this.top,t.object.bottom=this.bottom,t.object.near=this.near,t.object.far=this.far,null!==this.view&&(t.object.view=Object.assign({},this.view)),t}constructor(e=-1,t=1,n=1,i=-1,r=.1,a=2e3){super(),this.isOrthographicCamera=!0,this.type="OrthographicCamera",this.zoom=1,this.view=null,this.left=e,this.right=t,this.top=n,this.bottom=i,this.near=r,this.far=a,this.updateProjectionMatrix()}}let nl=[.125,.215,.35,.446,.526,.582],nh=new no,nc=new tn,nu=null,nd=0,np=0,nf=(1+Math.sqrt(5))/2,nm=1/nf,ng=[new Y(1,1,1),new Y(-1,1,1),new Y(1,1,-1),new Y(-1,1,-1),new Y(0,nf,nm),new Y(0,nf,-nm),new Y(nm,0,nf),new Y(-nm,0,nf),new Y(nf,nm,0),new Y(-nf,nm,0)];class n_{fromScene(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:.1,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:100;nu=this._renderer.getRenderTarget(),nd=this._renderer.getActiveCubeFace(),np=this._renderer.getActiveMipmapLevel(),this._setSize(256);let r=this._allocateTargets();return r.depthBuffer=!0,this._sceneToCubeUV(e,n,i,r),t>0&&this._blur(r,0,0,t),this._applyPMREM(r),this._cleanup(r),r}fromEquirectangular(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return this._fromTexture(e,t)}fromCubemap(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return this._fromTexture(e,t)}compileCubemapShader(){null===this._cubemapMaterial&&(this._cubemapMaterial=nM(),this._compileMaterial(this._cubemapMaterial))}compileEquirectangularShader(){null===this._equirectMaterial&&(this._equirectMaterial=ny(),this._compileMaterial(this._equirectMaterial))}dispose(){this._dispose(),null!==this._cubemapMaterial&&this._cubemapMaterial.dispose(),null!==this._equirectMaterial&&this._equirectMaterial.dispose()}_setSize(e){this._lodMax=Math.floor(Math.log2(e)),this._cubeSize=Math.pow(2,this._lodMax)}_dispose(){null!==this._blurMaterial&&this._blurMaterial.dispose(),null!==this._pingPongRenderTarget&&this._pingPongRenderTarget.dispose();for(let e=0;e<this._lodPlanes.length;e++)this._lodPlanes[e].dispose()}_cleanup(e){this._renderer.setRenderTarget(nu,nd,np),e.scissorTest=!1,nx(e,0,0,e.width,e.height)}_fromTexture(e,t){301===e.mapping||302===e.mapping?this._setSize(0===e.image.length?16:e.image[0].width||e.image[0].image.width):this._setSize(e.image.width/4),nu=this._renderer.getRenderTarget(),nd=this._renderer.getActiveCubeFace(),np=this._renderer.getActiveMipmapLevel();let n=t||this._allocateTargets();return this._textureToCubeUV(e,n),this._applyPMREM(n),this._cleanup(n),n}_allocateTargets(){let e=3*Math.max(this._cubeSize,112),t=4*this._cubeSize,n={magFilter:1006,minFilter:1006,generateMipmaps:!1,type:1016,format:1023,colorSpace:a,depthBuffer:!1},i=nv(e,t,n);if(null===this._pingPongRenderTarget||this._pingPongRenderTarget.width!==e||this._pingPongRenderTarget.height!==t){null!==this._pingPongRenderTarget&&this._dispose(),this._pingPongRenderTarget=nv(e,t,n);let{_lodMax:i}=this;({sizeLods:this._sizeLods,lodPlanes:this._lodPlanes,sigmas:this._sigmas}=function(e){let t=[],n=[],i=[],r=e,a=e-4+1+nl.length;for(let s=0;s<a;s++){let a=Math.pow(2,r);n.push(a);let o=1/a;s>e-4?o=nl[s-e+4-1]:0===s&&(o=0),i.push(o);let l=1/(a-2),h=-l,c=1+l,u=[h,h,c,h,c,c,h,h,c,c,h,c],d=new Float32Array(108),p=new Float32Array(72),f=new Float32Array(36);for(let e=0;e<6;e++){let t=e%3*2/3-1,n=e>2?0:-1,i=[t,n,0,t+2/3,n,0,t+2/3,n+1,0,t,n,0,t+2/3,n+1,0,t,n+1,0];d.set(i,18*e),p.set(u,12*e);let r=[e,e,e,e,e,e];f.set(r,6*e)}let m=new ty;m.setAttribute("position",new th(d,3)),m.setAttribute("uv",new th(p,2)),m.setAttribute("faceIndex",new th(f,1)),t.push(m),r>4&&r--}return{lodPlanes:t,sizeLods:n,sigmas:i}}(i)),this._blurMaterial=function(e,t,n){let i=new Float32Array(20),r=new Y(0,1,0);return new tX({name:"SphericalGaussianBlur",defines:{n:20,CUBEUV_TEXEL_WIDTH:1/t,CUBEUV_TEXEL_HEIGHT:1/n,CUBEUV_MAX_MIP:"".concat(e,".0")},uniforms:{envMap:{value:null},samples:{value:1},weights:{value:i},latitudinal:{value:!1},dTheta:{value:0},mipInt:{value:0},poleAxis:{value:r}},vertexShader:nS(),fragmentShader:"\n\n			precision mediump float;\n			precision mediump int;\n\n			varying vec3 vOutputDirection;\n\n			uniform sampler2D envMap;\n			uniform int samples;\n			uniform float weights[ n ];\n			uniform bool latitudinal;\n			uniform float dTheta;\n			uniform float mipInt;\n			uniform vec3 poleAxis;\n\n			#define ENVMAP_TYPE_CUBE_UV\n			#include <cube_uv_reflection_fragment>\n\n			vec3 getSample( float theta, vec3 axis ) {\n\n				float cosTheta = cos( theta );\n				// Rodrigues' axis-angle rotation\n				vec3 sampleDirection = vOutputDirection * cosTheta\n					+ cross( axis, vOutputDirection ) * sin( theta )\n					+ axis * dot( axis, vOutputDirection ) * ( 1.0 - cosTheta );\n\n				return bilinearCubeUV( envMap, sampleDirection, mipInt );\n\n			}\n\n			void main() {\n\n				vec3 axis = latitudinal ? poleAxis : cross( poleAxis, vOutputDirection );\n\n				if ( all( equal( axis, vec3( 0.0 ) ) ) ) {\n\n					axis = vec3( vOutputDirection.z, 0.0, - vOutputDirection.x );\n\n				}\n\n				axis = normalize( axis );\n\n				gl_FragColor = vec4( 0.0, 0.0, 0.0, 1.0 );\n				gl_FragColor.rgb += weights[ 0 ] * getSample( 0.0, axis );\n\n				for ( int i = 1; i < n; i++ ) {\n\n					if ( i >= samples ) {\n\n						break;\n\n					}\n\n					float theta = dTheta * float( i );\n					gl_FragColor.rgb += weights[ i ] * getSample( -1.0 * theta, axis );\n					gl_FragColor.rgb += weights[ i ] * getSample( theta, axis );\n\n				}\n\n			}\n		",blending:0,depthTest:!1,depthWrite:!1})}(i,e,t)}return i}_compileMaterial(e){let t=new tz(this._lodPlanes[0],e);this._renderer.compile(t,nh)}_sceneToCubeUV(e,t,n,i){let r=new tq(90,1,t,n),a=[1,-1,1,1,1,1],s=[1,1,1,-1,-1,-1],o=this._renderer,l=o.autoClear,h=o.toneMapping;o.getClearColor(nc),o.toneMapping=0,o.autoClear=!1;let c=new ts({name:"PMREM.Background",side:1,depthWrite:!1,depthTest:!1}),u=new tz(new tH,c),d=!1,p=e.background;p?p.isColor&&(c.color.copy(p),e.background=null,d=!0):(c.color.copy(nc),d=!0);for(let t=0;t<6;t++){let n=t%3;0===n?(r.up.set(0,a[t],0),r.lookAt(s[t],0,0)):1===n?(r.up.set(0,0,a[t]),r.lookAt(0,s[t],0)):(r.up.set(0,a[t],0),r.lookAt(0,0,s[t]));let l=this._cubeSize;nx(i,n*l,t>2?l:0,l,l),o.setRenderTarget(i),d&&o.render(u,r),o.render(e,r)}u.geometry.dispose(),u.material.dispose(),o.toneMapping=h,o.autoClear=l,e.background=p}_textureToCubeUV(e,t){let n=this._renderer,i=301===e.mapping||302===e.mapping;i?(null===this._cubemapMaterial&&(this._cubemapMaterial=nM()),this._cubemapMaterial.uniforms.flipEnvMap.value=!1===e.isRenderTargetTexture?-1:1):null===this._equirectMaterial&&(this._equirectMaterial=ny());let r=i?this._cubemapMaterial:this._equirectMaterial,a=new tz(this._lodPlanes[0],r);r.uniforms.envMap.value=e;let s=this._cubeSize;nx(t,0,0,3*s,2*s),n.setRenderTarget(t),n.render(a,nh)}_applyPMREM(e){let t=this._renderer,n=t.autoClear;t.autoClear=!1;for(let t=1;t<this._lodPlanes.length;t++){let n=Math.sqrt(this._sigmas[t]*this._sigmas[t]-this._sigmas[t-1]*this._sigmas[t-1]),i=ng[(t-1)%ng.length];this._blur(e,t-1,t,n,i)}t.autoClear=n}_blur(e,t,n,i,r){let a=this._pingPongRenderTarget;this._halfBlur(e,a,t,n,i,"latitudinal",r),this._halfBlur(a,e,n,n,i,"longitudinal",r)}_halfBlur(e,t,n,i,r,a,s){let o=this._renderer,l=this._blurMaterial;"latitudinal"!==a&&"longitudinal"!==a&&console.error("blur direction must be either latitudinal or longitudinal!");let h=new tz(this._lodPlanes[i],l),c=l.uniforms,u=this._sizeLods[n]-1,d=isFinite(r)?Math.PI/(2*u):2*Math.PI/39,p=r/d,f=isFinite(r)?1+Math.floor(3*p):20;f>20&&console.warn("sigmaRadians, ".concat(r,", is too large and will clip, as it requested ").concat(f," samples when the maximum is set to ").concat(20));let m=[],g=0;for(let e=0;e<20;++e){let t=e/p,n=Math.exp(-t*t/2);m.push(n),0===e?g+=n:e<f&&(g+=2*n)}for(let e=0;e<m.length;e++)m[e]=m[e]/g;c.envMap.value=e.texture,c.samples.value=f,c.weights.value=m,c.latitudinal.value="latitudinal"===a,s&&(c.poleAxis.value=s);let{_lodMax:_}=this;c.dTheta.value=d,c.mipInt.value=_-n;let v=this._sizeLods[i],x=4*(this._cubeSize-v);nx(t,3*v*(i>_-4?i-_+4:0),x,3*v,2*v),o.setRenderTarget(t),o.render(h,nh)}constructor(e){this._renderer=e,this._pingPongRenderTarget=null,this._lodMax=0,this._cubeSize=0,this._lodPlanes=[],this._sizeLods=[],this._sigmas=[],this._blurMaterial=null,this._cubemapMaterial=null,this._equirectMaterial=null,this._compileMaterial(this._blurMaterial)}}function nv(e,t,n){let i=new W(e,t,n);return i.texture.mapping=306,i.texture.name="PMREM.cubeUv",i.scissorTest=!0,i}function nx(e,t,n,i,r){e.viewport.set(t,n,i,r),e.scissor.set(t,n,i,r)}function ny(){return new tX({name:"EquirectangularToCubeUV",uniforms:{envMap:{value:null}},vertexShader:nS(),fragmentShader:"\n\n			precision mediump float;\n			precision mediump int;\n\n			varying vec3 vOutputDirection;\n\n			uniform sampler2D envMap;\n\n			#include <common>\n\n			void main() {\n\n				vec3 outputDirection = normalize( vOutputDirection );\n				vec2 uv = equirectUv( outputDirection );\n\n				gl_FragColor = vec4( texture2D ( envMap, uv ).rgb, 1.0 );\n\n			}\n		",blending:0,depthTest:!1,depthWrite:!1})}function nM(){return new tX({name:"CubemapToCubeUV",uniforms:{envMap:{value:null},flipEnvMap:{value:-1}},vertexShader:nS(),fragmentShader:"\n\n			precision mediump float;\n			precision mediump int;\n\n			uniform float flipEnvMap;\n\n			varying vec3 vOutputDirection;\n\n			uniform samplerCube envMap;\n\n			void main() {\n\n				gl_FragColor = textureCube( envMap, vec3( flipEnvMap * vOutputDirection.x, vOutputDirection.yz ) );\n\n			}\n		",blending:0,depthTest:!1,depthWrite:!1})}function nS(){return"\n\n		precision mediump float;\n		precision mediump int;\n\n		attribute float faceIndex;\n\n		varying vec3 vOutputDirection;\n\n		// RH coordinate system; PMREM face-indexing convention\n		vec3 getDirection( vec2 uv, float face ) {\n\n			uv = 2.0 * uv - 1.0;\n\n			vec3 direction = vec3( uv, 1.0 );\n\n			if ( face == 0.0 ) {\n\n				direction = direction.zyx; // ( 1, v, u ) pos x\n\n			} else if ( face == 1.0 ) {\n\n				direction = direction.xzy;\n				direction.xz *= -1.0; // ( -u, 1, -v ) pos y\n\n			} else if ( face == 2.0 ) {\n\n				direction.x *= -1.0; // ( -u, v, 1 ) pos z\n\n			} else if ( face == 3.0 ) {\n\n				direction = direction.zyx;\n				direction.xz *= -1.0; // ( -1, v, -u ) neg x\n\n			} else if ( face == 4.0 ) {\n\n				direction = direction.xzy;\n				direction.xy *= -1.0; // ( -u, -1, v ) neg y\n\n			} else if ( face == 5.0 ) {\n\n				direction.z *= -1.0; // ( u, v, -1 ) neg z\n\n			}\n\n			return direction;\n\n		}\n\n		void main() {\n\n			vOutputDirection = getDirection( uv, faceIndex );\n			gl_Position = vec4( position, 1.0 );\n\n		}\n	"}function nE(e){let t=new WeakMap,n=null;function i(e){let n=e.target;n.removeEventListener("dispose",i);let r=t.get(n);void 0!==r&&(t.delete(n),r.dispose())}return{get:function(r){if(r&&r.isTexture){let a=r.mapping,s=303===a||304===a,o=301===a||302===a;if(s||o)if(r.isRenderTargetTexture&&!0===r.needsPMREMUpdate){r.needsPMREMUpdate=!1;let i=t.get(r);return null===n&&(n=new n_(e)),i=s?n.fromEquirectangular(r,i):n.fromCubemap(r,i),t.set(r,i),i.texture}else{if(t.has(r))return t.get(r).texture;let a=r.image;if(!(s&&a&&a.height>0||o&&a&&function(e){let t=0;for(let n=0;n<6;n++)void 0!==e[n]&&t++;return 6===t}(a)))return null;{null===n&&(n=new n_(e));let a=s?n.fromEquirectangular(r):n.fromCubemap(r);return t.set(r,a),r.addEventListener("dispose",i),a.texture}}}return r},dispose:function(){t=new WeakMap,null!==n&&(n.dispose(),n=null)}}}function nT(e){let t={};function n(n){let i;if(void 0!==t[n])return t[n];switch(n){case"WEBGL_depth_texture":i=e.getExtension("WEBGL_depth_texture")||e.getExtension("MOZ_WEBGL_depth_texture")||e.getExtension("WEBKIT_WEBGL_depth_texture");break;case"EXT_texture_filter_anisotropic":i=e.getExtension("EXT_texture_filter_anisotropic")||e.getExtension("MOZ_EXT_texture_filter_anisotropic")||e.getExtension("WEBKIT_EXT_texture_filter_anisotropic");break;case"WEBGL_compressed_texture_s3tc":i=e.getExtension("WEBGL_compressed_texture_s3tc")||e.getExtension("MOZ_WEBGL_compressed_texture_s3tc")||e.getExtension("WEBKIT_WEBGL_compressed_texture_s3tc");break;case"WEBGL_compressed_texture_pvrtc":i=e.getExtension("WEBGL_compressed_texture_pvrtc")||e.getExtension("WEBKIT_WEBGL_compressed_texture_pvrtc");break;default:i=e.getExtension(n)}return t[n]=i,i}return{has:function(e){return null!==n(e)},init:function(e){e.isWebGL2?n("EXT_color_buffer_float"):(n("WEBGL_depth_texture"),n("OES_texture_float"),n("OES_texture_half_float"),n("OES_texture_half_float_linear"),n("OES_standard_derivatives"),n("OES_element_index_uint"),n("OES_vertex_array_object"),n("ANGLE_instanced_arrays")),n("OES_texture_float_linear"),n("EXT_color_buffer_half_float"),n("WEBGL_multisampled_render_to_texture")},get:function(e){let t=n(e);return null===t&&console.warn("THREE.WebGLRenderer: "+e+" extension not supported."),t}}}function nb(e,t,n,i){let r={},a=new WeakMap;function s(e){let o=e.target;for(let e in null!==o.index&&t.remove(o.index),o.attributes)t.remove(o.attributes[e]);for(let e in o.morphAttributes){let n=o.morphAttributes[e];for(let e=0,i=n.length;e<i;e++)t.remove(n[e])}o.removeEventListener("dispose",s),delete r[o.id];let l=a.get(o);l&&(t.remove(l),a.delete(o)),i.releaseStatesOfGeometry(o),!0===o.isInstancedBufferGeometry&&delete o._maxInstanceCount,n.memory.geometries--}function o(e){let n=[],i=e.index,r=e.attributes.position,s=0;if(null!==i){let e=i.array;s=i.version;for(let t=0,i=e.length;t<i;t+=3){let i=e[t+0],r=e[t+1],a=e[t+2];n.push(i,r,r,a,a,i)}}else{if(void 0===r)return;let e=r.array;s=r.version;for(let t=0,i=e.length/3-1;t<i;t+=3){let e=t+0,i=t+1,r=t+2;n.push(e,i,i,r,r,e)}}let o=new(b(n)?tu:tc)(n,1);o.version=s;let l=a.get(e);l&&t.remove(l),a.set(e,o)}return{get:function(e,t){return!0===r[t.id]||(t.addEventListener("dispose",s),r[t.id]=!0,n.memory.geometries++),t},update:function(n){let i=n.attributes;for(let n in i)t.update(i[n],e.ARRAY_BUFFER);let r=n.morphAttributes;for(let n in r){let i=r[n];for(let n=0,r=i.length;n<r;n++)t.update(i[n],e.ARRAY_BUFFER)}},getWireframeAttribute:function(e){let t=a.get(e);if(t){let n=e.index;null!==n&&t.version<n.version&&o(e)}else o(e);return a.get(e)}}}function nA(e,t,n,i){let r,a,s,o=i.isWebGL2;this.setMode=function(e){r=e},this.setIndex=function(e){a=e.type,s=e.bytesPerElement},this.render=function(t,i){e.drawElements(r,i,a,t*s),n.update(i,r,1)},this.renderInstances=function(i,l,h){let c,u;if(0!==h){if(o)c=e,u="drawElementsInstanced";else if(c=t.get("ANGLE_instanced_arrays"),u="drawElementsInstancedANGLE",null===c)return void console.error("THREE.WebGLIndexedBufferRenderer: using THREE.InstancedBufferGeometry but hardware does not support extension ANGLE_instanced_arrays.");c[u](r,l,a,i*s,h),n.update(l,r,h)}}}function nw(e){let t={frame:0,calls:0,triangles:0,points:0,lines:0};return{memory:{geometries:0,textures:0},render:t,programs:null,autoReset:!0,reset:function(){t.calls=0,t.triangles=0,t.points=0,t.lines=0},update:function(n,i,r){switch(t.calls++,i){case e.TRIANGLES:t.triangles+=n/3*r;break;case e.LINES:t.lines+=n/2*r;break;case e.LINE_STRIP:t.lines+=r*(n-1);break;case e.LINE_LOOP:t.lines+=r*n;break;case e.POINTS:t.points+=r*n;break;default:console.error("THREE.WebGLInfo: Unknown draw mode:",i)}}}}function nR(e,t){return e[0]-t[0]}function nC(e,t){return Math.abs(t[1])-Math.abs(e[1])}function nL(e,t,n){let i={},r=new Float32Array(8),a=new WeakMap,s=new G,o=[];for(let e=0;e<8;e++)o[e]=[e,0];return{update:function(l,h,c){let u=l.morphTargetInfluences;if(!0===t.isWebGL2){let i=h.morphAttributes.position||h.morphAttributes.normal||h.morphAttributes.color,r=void 0!==i?i.length:0,o=a.get(h);if(void 0===o||o.count!==r){void 0!==o&&o.texture.dispose();let e=void 0!==h.morphAttributes.position,n=void 0!==h.morphAttributes.normal,i=void 0!==h.morphAttributes.color,l=h.morphAttributes.position||[],c=h.morphAttributes.normal||[],u=h.morphAttributes.color||[],d=0;!0===e&&(d=1),!0===n&&(d=2),!0===i&&(d=3);let p=h.attributes.position.count*d,f=1;p>t.maxTextureSize&&(f=Math.ceil(p/t.maxTextureSize),p=t.maxTextureSize);let m=new Float32Array(p*f*4*r),g=new X(m,p,f,r);g.type=1015,g.needsUpdate=!0;let _=4*d;for(let t=0;t<r;t++){let r=l[t],a=c[t],o=u[t],h=p*f*4*t;for(let t=0;t<r.count;t++){let l=t*_;!0===e&&(s.fromBufferAttribute(r,t),m[h+l+0]=s.x,m[h+l+1]=s.y,m[h+l+2]=s.z,m[h+l+3]=0),!0===n&&(s.fromBufferAttribute(a,t),m[h+l+4]=s.x,m[h+l+5]=s.y,m[h+l+6]=s.z,m[h+l+7]=0),!0===i&&(s.fromBufferAttribute(o,t),m[h+l+8]=s.x,m[h+l+9]=s.y,m[h+l+10]=s.z,m[h+l+11]=4===o.itemSize?s.w:1)}}o={count:r,texture:g,size:new S(p,f)},a.set(h,o),h.addEventListener("dispose",function e(){g.dispose(),a.delete(h),h.removeEventListener("dispose",e)})}let l=0;for(let e=0;e<u.length;e++)l+=u[e];let d=h.morphTargetsRelative?1:1-l;c.getUniforms().setValue(e,"morphTargetBaseInfluence",d),c.getUniforms().setValue(e,"morphTargetInfluences",u),c.getUniforms().setValue(e,"morphTargetsTexture",o.texture,n),c.getUniforms().setValue(e,"morphTargetsTextureSize",o.size)}else{let t=void 0===u?0:u.length,n=i[h.id];if(void 0===n||n.length!==t){n=[];for(let e=0;e<t;e++)n[e]=[e,0];i[h.id]=n}for(let e=0;e<t;e++){let t=n[e];t[0]=e,t[1]=u[e]}n.sort(nC);for(let e=0;e<8;e++)e<t&&n[e][1]?(o[e][0]=n[e][0],o[e][1]=n[e][1]):(o[e][0]=Number.MAX_SAFE_INTEGER,o[e][1]=0);o.sort(nR);let a=h.morphAttributes.position,s=h.morphAttributes.normal,l=0;for(let e=0;e<8;e++){let t=o[e],n=t[0],i=t[1];n!==Number.MAX_SAFE_INTEGER&&i?(a&&h.getAttribute("morphTarget"+e)!==a[n]&&h.setAttribute("morphTarget"+e,a[n]),s&&h.getAttribute("morphNormal"+e)!==s[n]&&h.setAttribute("morphNormal"+e,s[n]),r[e]=i,l+=i):(a&&!0===h.hasAttribute("morphTarget"+e)&&h.deleteAttribute("morphTarget"+e),s&&!0===h.hasAttribute("morphNormal"+e)&&h.deleteAttribute("morphNormal"+e),r[e]=0)}let d=h.morphTargetsRelative?1:1-l;c.getUniforms().setValue(e,"morphTargetBaseInfluence",d),c.getUniforms().setValue(e,"morphTargetInfluences",r)}}}}function nP(e,t,n,i){let r=new WeakMap;function a(e){let t=e.target;t.removeEventListener("dispose",a),n.remove(t.instanceMatrix),null!==t.instanceColor&&n.remove(t.instanceColor)}return{update:function(s){let o=i.render.frame,l=s.geometry,h=t.get(s,l);if(r.get(h)!==o&&(t.update(h),r.set(h,o)),s.isInstancedMesh&&(!1===s.hasEventListener("dispose",a)&&s.addEventListener("dispose",a),r.get(s)!==o&&(n.update(s.instanceMatrix,e.ARRAY_BUFFER),null!==s.instanceColor&&n.update(s.instanceColor,e.ARRAY_BUFFER),r.set(s,o))),s.isSkinnedMesh){let e=s.skeleton;r.get(e)!==o&&(e.update(),r.set(e,o))}return h},dispose:function(){r=new WeakMap}}}let nU=new V,nN=new X,nD=new j,nI=new tJ,nO=[],nF=[],nz=new Float32Array(16),nB=new Float32Array(9),nH=new Float32Array(4);function nV(e,t,n){let i=e[0];if(i<=0||i>0)return e;let r=t*n,a=nO[r];if(void 0===a&&(a=new Float32Array(r),nO[r]=a),0!==t){i.toArray(a,0);for(let i=1,r=0;i!==t;++i)r+=n,e[i].toArray(a,r)}return a}function nG(e,t){if(e.length!==t.length)return!1;for(let n=0,i=e.length;n<i;n++)if(e[n]!==t[n])return!1;return!0}function nk(e,t){for(let n=0,i=t.length;n<i;n++)e[n]=t[n]}function nW(e,t){let n=nF[t];void 0===n&&(n=new Int32Array(t),nF[t]=n);for(let i=0;i!==t;++i)n[i]=e.allocateTextureUnit();return n}function nX(e,t){let n=this.cache;n[0]!==t&&(e.uniform1f(this.addr,t),n[0]=t)}function nj(e,t){let n=this.cache;if(void 0!==t.x)(n[0]!==t.x||n[1]!==t.y)&&(e.uniform2f(this.addr,t.x,t.y),n[0]=t.x,n[1]=t.y);else{if(nG(n,t))return;e.uniform2fv(this.addr,t),nk(n,t)}}function nq(e,t){let n=this.cache;if(void 0!==t.x)(n[0]!==t.x||n[1]!==t.y||n[2]!==t.z)&&(e.uniform3f(this.addr,t.x,t.y,t.z),n[0]=t.x,n[1]=t.y,n[2]=t.z);else if(void 0!==t.r)(n[0]!==t.r||n[1]!==t.g||n[2]!==t.b)&&(e.uniform3f(this.addr,t.r,t.g,t.b),n[0]=t.r,n[1]=t.g,n[2]=t.b);else{if(nG(n,t))return;e.uniform3fv(this.addr,t),nk(n,t)}}function nY(e,t){let n=this.cache;if(void 0!==t.x)(n[0]!==t.x||n[1]!==t.y||n[2]!==t.z||n[3]!==t.w)&&(e.uniform4f(this.addr,t.x,t.y,t.z,t.w),n[0]=t.x,n[1]=t.y,n[2]=t.z,n[3]=t.w);else{if(nG(n,t))return;e.uniform4fv(this.addr,t),nk(n,t)}}function nJ(e,t){let n=this.cache,i=t.elements;if(void 0===i){if(nG(n,t))return;e.uniformMatrix2fv(this.addr,!1,t),nk(n,t)}else{if(nG(n,i))return;nH.set(i),e.uniformMatrix2fv(this.addr,!1,nH),nk(n,i)}}function nZ(e,t){let n=this.cache,i=t.elements;if(void 0===i){if(nG(n,t))return;e.uniformMatrix3fv(this.addr,!1,t),nk(n,t)}else{if(nG(n,i))return;nB.set(i),e.uniformMatrix3fv(this.addr,!1,nB),nk(n,i)}}function nK(e,t){let n=this.cache,i=t.elements;if(void 0===i){if(nG(n,t))return;e.uniformMatrix4fv(this.addr,!1,t),nk(n,t)}else{if(nG(n,i))return;nz.set(i),e.uniformMatrix4fv(this.addr,!1,nz),nk(n,i)}}function nQ(e,t){let n=this.cache;n[0]!==t&&(e.uniform1i(this.addr,t),n[0]=t)}function n$(e,t){let n=this.cache;if(void 0!==t.x)(n[0]!==t.x||n[1]!==t.y)&&(e.uniform2i(this.addr,t.x,t.y),n[0]=t.x,n[1]=t.y);else{if(nG(n,t))return;e.uniform2iv(this.addr,t),nk(n,t)}}function n0(e,t){let n=this.cache;if(void 0!==t.x)(n[0]!==t.x||n[1]!==t.y||n[2]!==t.z)&&(e.uniform3i(this.addr,t.x,t.y,t.z),n[0]=t.x,n[1]=t.y,n[2]=t.z);else{if(nG(n,t))return;e.uniform3iv(this.addr,t),nk(n,t)}}function n1(e,t){let n=this.cache;if(void 0!==t.x)(n[0]!==t.x||n[1]!==t.y||n[2]!==t.z||n[3]!==t.w)&&(e.uniform4i(this.addr,t.x,t.y,t.z,t.w),n[0]=t.x,n[1]=t.y,n[2]=t.z,n[3]=t.w);else{if(nG(n,t))return;e.uniform4iv(this.addr,t),nk(n,t)}}function n2(e,t){let n=this.cache;n[0]!==t&&(e.uniform1ui(this.addr,t),n[0]=t)}function n3(e,t){let n=this.cache;if(void 0!==t.x)(n[0]!==t.x||n[1]!==t.y)&&(e.uniform2ui(this.addr,t.x,t.y),n[0]=t.x,n[1]=t.y);else{if(nG(n,t))return;e.uniform2uiv(this.addr,t),nk(n,t)}}function n4(e,t){let n=this.cache;if(void 0!==t.x)(n[0]!==t.x||n[1]!==t.y||n[2]!==t.z)&&(e.uniform3ui(this.addr,t.x,t.y,t.z),n[0]=t.x,n[1]=t.y,n[2]=t.z);else{if(nG(n,t))return;e.uniform3uiv(this.addr,t),nk(n,t)}}function n5(e,t){let n=this.cache;if(void 0!==t.x)(n[0]!==t.x||n[1]!==t.y||n[2]!==t.z||n[3]!==t.w)&&(e.uniform4ui(this.addr,t.x,t.y,t.z,t.w),n[0]=t.x,n[1]=t.y,n[2]=t.z,n[3]=t.w);else{if(nG(n,t))return;e.uniform4uiv(this.addr,t),nk(n,t)}}function n6(e,t,n){let i=this.cache,r=n.allocateTextureUnit();i[0]!==r&&(e.uniform1i(this.addr,r),i[0]=r),n.setTexture2D(t||nU,r)}function n8(e,t,n){let i=this.cache,r=n.allocateTextureUnit();i[0]!==r&&(e.uniform1i(this.addr,r),i[0]=r),n.setTexture3D(t||nD,r)}function n9(e,t,n){let i=this.cache,r=n.allocateTextureUnit();i[0]!==r&&(e.uniform1i(this.addr,r),i[0]=r),n.setTextureCube(t||nI,r)}function n7(e,t,n){let i=this.cache,r=n.allocateTextureUnit();i[0]!==r&&(e.uniform1i(this.addr,r),i[0]=r),n.setTexture2DArray(t||nN,r)}function ie(e,t){e.uniform1fv(this.addr,t)}function it(e,t){let n=nV(t,this.size,2);e.uniform2fv(this.addr,n)}function ii(e,t){let n=nV(t,this.size,3);e.uniform3fv(this.addr,n)}function ir(e,t){let n=nV(t,this.size,4);e.uniform4fv(this.addr,n)}function ia(e,t){let n=nV(t,this.size,4);e.uniformMatrix2fv(this.addr,!1,n)}function is(e,t){let n=nV(t,this.size,9);e.uniformMatrix3fv(this.addr,!1,n)}function io(e,t){let n=nV(t,this.size,16);e.uniformMatrix4fv(this.addr,!1,n)}function il(e,t){e.uniform1iv(this.addr,t)}function ih(e,t){e.uniform2iv(this.addr,t)}function ic(e,t){e.uniform3iv(this.addr,t)}function iu(e,t){e.uniform4iv(this.addr,t)}function id(e,t){e.uniform1uiv(this.addr,t)}function ip(e,t){e.uniform2uiv(this.addr,t)}function im(e,t){e.uniform3uiv(this.addr,t)}function ig(e,t){e.uniform4uiv(this.addr,t)}function i_(e,t,n){let i=this.cache,r=t.length,a=nW(n,r);nG(i,a)||(e.uniform1iv(this.addr,a),nk(i,a));for(let e=0;e!==r;++e)n.setTexture2D(t[e]||nU,a[e])}function iv(e,t,n){let i=this.cache,r=t.length,a=nW(n,r);nG(i,a)||(e.uniform1iv(this.addr,a),nk(i,a));for(let e=0;e!==r;++e)n.setTexture3D(t[e]||nD,a[e])}function ix(e,t,n){let i=this.cache,r=t.length,a=nW(n,r);nG(i,a)||(e.uniform1iv(this.addr,a),nk(i,a));for(let e=0;e!==r;++e)n.setTextureCube(t[e]||nI,a[e])}function iy(e,t,n){let i=this.cache,r=t.length,a=nW(n,r);nG(i,a)||(e.uniform1iv(this.addr,a),nk(i,a));for(let e=0;e!==r;++e)n.setTexture2DArray(t[e]||nN,a[e])}class iM{constructor(e,t,n){this.id=e,this.addr=n,this.cache=[],this.setValue=function(e){switch(e){case 5126:return nX;case 35664:return nj;case 35665:return nq;case 35666:return nY;case 35674:return nJ;case 35675:return nZ;case 35676:return nK;case 5124:case 35670:return nQ;case 35667:case 35671:return n$;case 35668:case 35672:return n0;case 35669:case 35673:return n1;case 5125:return n2;case 36294:return n3;case 36295:return n4;case 36296:return n5;case 35678:case 36198:case 36298:case 36306:case 35682:return n6;case 35679:case 36299:case 36307:return n8;case 35680:case 36300:case 36308:case 36293:return n9;case 36289:case 36303:case 36311:case 36292:return n7}}(t.type)}}class iS{constructor(e,t,n){this.id=e,this.addr=n,this.cache=[],this.size=t.size,this.setValue=function(e){switch(e){case 5126:return ie;case 35664:return it;case 35665:return ii;case 35666:return ir;case 35674:return ia;case 35675:return is;case 35676:return io;case 5124:case 35670:return il;case 35667:case 35671:return ih;case 35668:case 35672:return ic;case 35669:case 35673:return iu;case 5125:return id;case 36294:return ip;case 36295:return im;case 36296:return ig;case 35678:case 36198:case 36298:case 36306:case 35682:return i_;case 35679:case 36299:case 36307:return iv;case 35680:case 36300:case 36308:case 36293:return ix;case 36289:case 36303:case 36311:case 36292:return iy}}(t.type)}}class iE{setValue(e,t,n){let i=this.seq;for(let r=0,a=i.length;r!==a;++r){let a=i[r];a.setValue(e,t[a.id],n)}}constructor(e){this.id=e,this.seq=[],this.map={}}}let iT=/(\w+)(\])?(\[|\.)?/g;function ib(e,t){e.seq.push(t),e.map[t.id]=t}class iA{setValue(e,t,n,i){let r=this.map[t];void 0!==r&&r.setValue(e,n,i)}setOptional(e,t,n){let i=t[n];void 0!==i&&this.setValue(e,n,i)}static upload(e,t,n,i){for(let r=0,a=t.length;r!==a;++r){let a=t[r],s=n[a.id];!1!==s.needsUpdate&&a.setValue(e,s.value,i)}}static seqWithValue(e,t){let n=[];for(let i=0,r=e.length;i!==r;++i){let r=e[i];r.id in t&&n.push(r)}return n}constructor(e,t){this.seq=[],this.map={};let n=e.getProgramParameter(t,e.ACTIVE_UNIFORMS);for(let i=0;i<n;++i){let n=e.getActiveUniform(t,i),r=e.getUniformLocation(t,n.name);!function(e,t,n){let i=e.name,r=i.length;for(iT.lastIndex=0;;){let a=iT.exec(i),s=iT.lastIndex,o=a[1],l="]"===a[2],h=a[3];if(l&&(o|=0),void 0===h||"["===h&&s+2===r){ib(n,void 0===h?new iM(o,e,t):new iS(o,e,t));break}{let e=n.map[o];void 0===e&&ib(n,e=new iE(o)),n=e}}}(n,r,this)}}}function iw(e,t,n){let i=e.createShader(t);return e.shaderSource(i,n),e.compileShader(i),i}let iR=0;function iC(e,t,n){let i=e.getShaderParameter(t,e.COMPILE_STATUS),r=e.getShaderInfoLog(t).trim();if(i&&""===r)return"";let a=/ERROR: 0:(\d+)/.exec(r);if(!a)return r;{let i=parseInt(a[1]);return n.toUpperCase()+"\n\n"+r+"\n\n"+function(e,t){let n=e.split("\n"),i=[],r=Math.max(t-6,0),a=Math.min(t+6,n.length);for(let e=r;e<a;e++){let r=e+1;i.push("".concat(r===t?">":" "," ").concat(r,": ").concat(n[e]))}return i.join("\n")}(e.getShaderSource(t),i)}}function iL(e){return""!==e}function iP(e,t){let n=t.numSpotLightShadows+t.numSpotLightMaps-t.numSpotLightShadowsWithMaps;return e.replace(/NUM_DIR_LIGHTS/g,t.numDirLights).replace(/NUM_SPOT_LIGHTS/g,t.numSpotLights).replace(/NUM_SPOT_LIGHT_MAPS/g,t.numSpotLightMaps).replace(/NUM_SPOT_LIGHT_COORDS/g,n).replace(/NUM_RECT_AREA_LIGHTS/g,t.numRectAreaLights).replace(/NUM_POINT_LIGHTS/g,t.numPointLights).replace(/NUM_HEMI_LIGHTS/g,t.numHemiLights).replace(/NUM_DIR_LIGHT_SHADOWS/g,t.numDirLightShadows).replace(/NUM_SPOT_LIGHT_SHADOWS_WITH_MAPS/g,t.numSpotLightShadowsWithMaps).replace(/NUM_SPOT_LIGHT_SHADOWS/g,t.numSpotLightShadows).replace(/NUM_POINT_LIGHT_SHADOWS/g,t.numPointLightShadows)}function iU(e,t){return e.replace(/NUM_CLIPPING_PLANES/g,t.numClippingPlanes).replace(/UNION_CLIPPING_PLANES/g,t.numClippingPlanes-t.numClipIntersection)}let iN=/^[ \t]*#include +<([\w\d./]+)>/gm;function iD(e){return e.replace(iN,iO)}let iI=new Map([["encodings_fragment","colorspace_fragment"],["encodings_pars_fragment","colorspace_pars_fragment"],["output_fragment","opaque_fragment"]]);function iO(e,t){let n=t8[t];if(void 0===n){let e=iI.get(t);if(void 0!==e)n=t8[e],console.warn('THREE.WebGLRenderer: Shader chunk "%s" has been deprecated. Use "%s" instead.',t,e);else throw Error("Can not resolve #include <"+t+">")}return iD(n)}let iF=/#pragma unroll_loop_start\s+for\s*\(\s*int\s+i\s*=\s*(\d+)\s*;\s*i\s*<\s*(\d+)\s*;\s*i\s*\+\+\s*\)\s*{([\s\S]+?)}\s+#pragma unroll_loop_end/g;function iz(e){return e.replace(iF,iB)}function iB(e,t,n,i){let r="";for(let e=parseInt(t);e<parseInt(n);e++)r+=i.replace(/\[\s*i\s*\]/g,"[ "+e+" ]").replace(/UNROLLED_LOOP_INDEX/g,e);return r}function iH(e){let t="precision "+e.precision+" float;\nprecision "+e.precision+" int;";return"highp"===e.precision?t+="\n#define HIGH_PRECISION":"mediump"===e.precision?t+="\n#define MEDIUM_PRECISION":"lowp"===e.precision&&(t+="\n#define LOW_PRECISION"),t}function iV(e,t,n,i){let l,h,d,p,f,m,g=e.getContext(),_=n.defines,v=n.vertexShader,x=n.fragmentShader,y=(f="SHADOWMAP_TYPE_BASIC",1===n.shadowMapType?f="SHADOWMAP_TYPE_PCF":2===n.shadowMapType?f="SHADOWMAP_TYPE_PCF_SOFT":3===n.shadowMapType&&(f="SHADOWMAP_TYPE_VSM"),f),M=function(e){let t="ENVMAP_TYPE_CUBE";if(e.envMap)switch(e.envMapMode){case 301:case 302:t="ENVMAP_TYPE_CUBE";break;case 306:t="ENVMAP_TYPE_CUBE_UV"}return t}(n),S=(m="ENVMAP_MODE_REFLECTION",n.envMap&&302===n.envMapMode&&(m="ENVMAP_MODE_REFRACTION"),m),E=function(e){let t="ENVMAP_BLENDING_NONE";if(e.envMap)switch(e.combine){case 0:t="ENVMAP_BLENDING_MULTIPLY";break;case 1:t="ENVMAP_BLENDING_MIX";break;case 2:t="ENVMAP_BLENDING_ADD"}return t}(n),T=function(e){let t=e.envMapCubeUVHeight;if(null===t)return null;let n=Math.log2(t)-2;return{texelWidth:1/(3*Math.max(Math.pow(2,n),112)),texelHeight:1/t,maxMip:n}}(n),b=n.isWebGL2?"":[n.extensionDerivatives||n.envMapCubeUVHeight||n.bumpMap||n.normalMapTangentSpace||n.clearcoatNormalMap||n.flatShading||"physical"===n.shaderID?"#extension GL_OES_standard_derivatives : enable":"",(n.extensionFragDepth||n.logarithmicDepthBuffer)&&n.rendererExtensionFragDepth?"#extension GL_EXT_frag_depth : enable":"",n.extensionDrawBuffers&&n.rendererExtensionDrawBuffers?"#extension GL_EXT_draw_buffers : require":"",(n.extensionShaderTextureLOD||n.envMap||n.transmission)&&n.rendererExtensionShaderTextureLod?"#extension GL_EXT_shader_texture_lod : enable":""].filter(iL).join("\n"),A=function(e){let t=[];for(let n in e){let i=e[n];!1!==i&&t.push("#define "+n+" "+i)}return t.join("\n")}(_),w=g.createProgram(),R=n.glslVersion?"#version "+n.glslVersion+"\n":"";n.isRawShaderMaterial?((l=["#define SHADER_TYPE "+n.shaderType,"#define SHADER_NAME "+n.shaderName,A].filter(iL).join("\n")).length>0&&(l+="\n"),(h=[b,"#define SHADER_TYPE "+n.shaderType,"#define SHADER_NAME "+n.shaderName,A].filter(iL).join("\n")).length>0&&(h+="\n")):(l=[iH(n),"#define SHADER_TYPE "+n.shaderType,"#define SHADER_NAME "+n.shaderName,A,n.instancing?"#define USE_INSTANCING":"",n.instancingColor?"#define USE_INSTANCING_COLOR":"",n.useFog&&n.fog?"#define USE_FOG":"",n.useFog&&n.fogExp2?"#define FOG_EXP2":"",n.map?"#define USE_MAP":"",n.envMap?"#define USE_ENVMAP":"",n.envMap?"#define "+S:"",n.lightMap?"#define USE_LIGHTMAP":"",n.aoMap?"#define USE_AOMAP":"",n.bumpMap?"#define USE_BUMPMAP":"",n.normalMap?"#define USE_NORMALMAP":"",n.normalMapObjectSpace?"#define USE_NORMALMAP_OBJECTSPACE":"",n.normalMapTangentSpace?"#define USE_NORMALMAP_TANGENTSPACE":"",n.displacementMap?"#define USE_DISPLACEMENTMAP":"",n.emissiveMap?"#define USE_EMISSIVEMAP":"",n.anisotropy?"#define USE_ANISOTROPY":"",n.anisotropyMap?"#define USE_ANISOTROPYMAP":"",n.clearcoatMap?"#define USE_CLEARCOATMAP":"",n.clearcoatRoughnessMap?"#define USE_CLEARCOAT_ROUGHNESSMAP":"",n.clearcoatNormalMap?"#define USE_CLEARCOAT_NORMALMAP":"",n.iridescenceMap?"#define USE_IRIDESCENCEMAP":"",n.iridescenceThicknessMap?"#define USE_IRIDESCENCE_THICKNESSMAP":"",n.specularMap?"#define USE_SPECULARMAP":"",n.specularColorMap?"#define USE_SPECULAR_COLORMAP":"",n.specularIntensityMap?"#define USE_SPECULAR_INTENSITYMAP":"",n.roughnessMap?"#define USE_ROUGHNESSMAP":"",n.metalnessMap?"#define USE_METALNESSMAP":"",n.alphaMap?"#define USE_ALPHAMAP":"",n.alphaHash?"#define USE_ALPHAHASH":"",n.transmission?"#define USE_TRANSMISSION":"",n.transmissionMap?"#define USE_TRANSMISSIONMAP":"",n.thicknessMap?"#define USE_THICKNESSMAP":"",n.sheenColorMap?"#define USE_SHEEN_COLORMAP":"",n.sheenRoughnessMap?"#define USE_SHEEN_ROUGHNESSMAP":"",n.mapUv?"#define MAP_UV "+n.mapUv:"",n.alphaMapUv?"#define ALPHAMAP_UV "+n.alphaMapUv:"",n.lightMapUv?"#define LIGHTMAP_UV "+n.lightMapUv:"",n.aoMapUv?"#define AOMAP_UV "+n.aoMapUv:"",n.emissiveMapUv?"#define EMISSIVEMAP_UV "+n.emissiveMapUv:"",n.bumpMapUv?"#define BUMPMAP_UV "+n.bumpMapUv:"",n.normalMapUv?"#define NORMALMAP_UV "+n.normalMapUv:"",n.displacementMapUv?"#define DISPLACEMENTMAP_UV "+n.displacementMapUv:"",n.metalnessMapUv?"#define METALNESSMAP_UV "+n.metalnessMapUv:"",n.roughnessMapUv?"#define ROUGHNESSMAP_UV "+n.roughnessMapUv:"",n.anisotropyMapUv?"#define ANISOTROPYMAP_UV "+n.anisotropyMapUv:"",n.clearcoatMapUv?"#define CLEARCOATMAP_UV "+n.clearcoatMapUv:"",n.clearcoatNormalMapUv?"#define CLEARCOAT_NORMALMAP_UV "+n.clearcoatNormalMapUv:"",n.clearcoatRoughnessMapUv?"#define CLEARCOAT_ROUGHNESSMAP_UV "+n.clearcoatRoughnessMapUv:"",n.iridescenceMapUv?"#define IRIDESCENCEMAP_UV "+n.iridescenceMapUv:"",n.iridescenceThicknessMapUv?"#define IRIDESCENCE_THICKNESSMAP_UV "+n.iridescenceThicknessMapUv:"",n.sheenColorMapUv?"#define SHEEN_COLORMAP_UV "+n.sheenColorMapUv:"",n.sheenRoughnessMapUv?"#define SHEEN_ROUGHNESSMAP_UV "+n.sheenRoughnessMapUv:"",n.specularMapUv?"#define SPECULARMAP_UV "+n.specularMapUv:"",n.specularColorMapUv?"#define SPECULAR_COLORMAP_UV "+n.specularColorMapUv:"",n.specularIntensityMapUv?"#define SPECULAR_INTENSITYMAP_UV "+n.specularIntensityMapUv:"",n.transmissionMapUv?"#define TRANSMISSIONMAP_UV "+n.transmissionMapUv:"",n.thicknessMapUv?"#define THICKNESSMAP_UV "+n.thicknessMapUv:"",n.vertexTangents&&!1===n.flatShading?"#define USE_TANGENT":"",n.vertexColors?"#define USE_COLOR":"",n.vertexAlphas?"#define USE_COLOR_ALPHA":"",n.vertexUv1s?"#define USE_UV1":"",n.vertexUv2s?"#define USE_UV2":"",n.vertexUv3s?"#define USE_UV3":"",n.pointsUvs?"#define USE_POINTS_UV":"",n.flatShading?"#define FLAT_SHADED":"",n.skinning?"#define USE_SKINNING":"",n.morphTargets?"#define USE_MORPHTARGETS":"",n.morphNormals&&!1===n.flatShading?"#define USE_MORPHNORMALS":"",n.morphColors&&n.isWebGL2?"#define USE_MORPHCOLORS":"",n.morphTargetsCount>0&&n.isWebGL2?"#define MORPHTARGETS_TEXTURE":"",n.morphTargetsCount>0&&n.isWebGL2?"#define MORPHTARGETS_TEXTURE_STRIDE "+n.morphTextureStride:"",n.morphTargetsCount>0&&n.isWebGL2?"#define MORPHTARGETS_COUNT "+n.morphTargetsCount:"",n.doubleSided?"#define DOUBLE_SIDED":"",n.flipSided?"#define FLIP_SIDED":"",n.shadowMapEnabled?"#define USE_SHADOWMAP":"",n.shadowMapEnabled?"#define "+y:"",n.sizeAttenuation?"#define USE_SIZEATTENUATION":"",n.numLightProbes>0?"#define USE_LIGHT_PROBES":"",n.useLegacyLights?"#define LEGACY_LIGHTS":"",n.logarithmicDepthBuffer?"#define USE_LOGDEPTHBUF":"",n.logarithmicDepthBuffer&&n.rendererExtensionFragDepth?"#define USE_LOGDEPTHBUF_EXT":"","uniform mat4 modelMatrix;","uniform mat4 modelViewMatrix;","uniform mat4 projectionMatrix;","uniform mat4 viewMatrix;","uniform mat3 normalMatrix;","uniform vec3 cameraPosition;","uniform bool isOrthographic;","#ifdef USE_INSTANCING","	attribute mat4 instanceMatrix;","#endif","#ifdef USE_INSTANCING_COLOR","	attribute vec3 instanceColor;","#endif","attribute vec3 position;","attribute vec3 normal;","attribute vec2 uv;","#ifdef USE_UV1","	attribute vec2 uv1;","#endif","#ifdef USE_UV2","	attribute vec2 uv2;","#endif","#ifdef USE_UV3","	attribute vec2 uv3;","#endif","#ifdef USE_TANGENT","	attribute vec4 tangent;","#endif","#if defined( USE_COLOR_ALPHA )","	attribute vec4 color;","#elif defined( USE_COLOR )","	attribute vec3 color;","#endif","#if ( defined( USE_MORPHTARGETS ) && ! defined( MORPHTARGETS_TEXTURE ) )","	attribute vec3 morphTarget0;","	attribute vec3 morphTarget1;","	attribute vec3 morphTarget2;","	attribute vec3 morphTarget3;","	#ifdef USE_MORPHNORMALS","		attribute vec3 morphNormal0;","		attribute vec3 morphNormal1;","		attribute vec3 morphNormal2;","		attribute vec3 morphNormal3;","	#else","		attribute vec3 morphTarget4;","		attribute vec3 morphTarget5;","		attribute vec3 morphTarget6;","		attribute vec3 morphTarget7;","	#endif","#endif","#ifdef USE_SKINNING","	attribute vec4 skinIndex;","	attribute vec4 skinWeight;","#endif","\n"].filter(iL).join("\n"),h=[b,iH(n),"#define SHADER_TYPE "+n.shaderType,"#define SHADER_NAME "+n.shaderName,A,n.useFog&&n.fog?"#define USE_FOG":"",n.useFog&&n.fogExp2?"#define FOG_EXP2":"",n.map?"#define USE_MAP":"",n.matcap?"#define USE_MATCAP":"",n.envMap?"#define USE_ENVMAP":"",n.envMap?"#define "+M:"",n.envMap?"#define "+S:"",n.envMap?"#define "+E:"",T?"#define CUBEUV_TEXEL_WIDTH "+T.texelWidth:"",T?"#define CUBEUV_TEXEL_HEIGHT "+T.texelHeight:"",T?"#define CUBEUV_MAX_MIP "+T.maxMip+".0":"",n.lightMap?"#define USE_LIGHTMAP":"",n.aoMap?"#define USE_AOMAP":"",n.bumpMap?"#define USE_BUMPMAP":"",n.normalMap?"#define USE_NORMALMAP":"",n.normalMapObjectSpace?"#define USE_NORMALMAP_OBJECTSPACE":"",n.normalMapTangentSpace?"#define USE_NORMALMAP_TANGENTSPACE":"",n.emissiveMap?"#define USE_EMISSIVEMAP":"",n.anisotropy?"#define USE_ANISOTROPY":"",n.anisotropyMap?"#define USE_ANISOTROPYMAP":"",n.clearcoat?"#define USE_CLEARCOAT":"",n.clearcoatMap?"#define USE_CLEARCOATMAP":"",n.clearcoatRoughnessMap?"#define USE_CLEARCOAT_ROUGHNESSMAP":"",n.clearcoatNormalMap?"#define USE_CLEARCOAT_NORMALMAP":"",n.iridescence?"#define USE_IRIDESCENCE":"",n.iridescenceMap?"#define USE_IRIDESCENCEMAP":"",n.iridescenceThicknessMap?"#define USE_IRIDESCENCE_THICKNESSMAP":"",n.specularMap?"#define USE_SPECULARMAP":"",n.specularColorMap?"#define USE_SPECULAR_COLORMAP":"",n.specularIntensityMap?"#define USE_SPECULAR_INTENSITYMAP":"",n.roughnessMap?"#define USE_ROUGHNESSMAP":"",n.metalnessMap?"#define USE_METALNESSMAP":"",n.alphaMap?"#define USE_ALPHAMAP":"",n.alphaTest?"#define USE_ALPHATEST":"",n.alphaHash?"#define USE_ALPHAHASH":"",n.sheen?"#define USE_SHEEN":"",n.sheenColorMap?"#define USE_SHEEN_COLORMAP":"",n.sheenRoughnessMap?"#define USE_SHEEN_ROUGHNESSMAP":"",n.transmission?"#define USE_TRANSMISSION":"",n.transmissionMap?"#define USE_TRANSMISSIONMAP":"",n.thicknessMap?"#define USE_THICKNESSMAP":"",n.vertexTangents&&!1===n.flatShading?"#define USE_TANGENT":"",n.vertexColors||n.instancingColor?"#define USE_COLOR":"",n.vertexAlphas?"#define USE_COLOR_ALPHA":"",n.vertexUv1s?"#define USE_UV1":"",n.vertexUv2s?"#define USE_UV2":"",n.vertexUv3s?"#define USE_UV3":"",n.pointsUvs?"#define USE_POINTS_UV":"",n.gradientMap?"#define USE_GRADIENTMAP":"",n.flatShading?"#define FLAT_SHADED":"",n.doubleSided?"#define DOUBLE_SIDED":"",n.flipSided?"#define FLIP_SIDED":"",n.shadowMapEnabled?"#define USE_SHADOWMAP":"",n.shadowMapEnabled?"#define "+y:"",n.premultipliedAlpha?"#define PREMULTIPLIED_ALPHA":"",n.numLightProbes>0?"#define USE_LIGHT_PROBES":"",n.useLegacyLights?"#define LEGACY_LIGHTS":"",n.decodeVideoTexture?"#define DECODE_VIDEO_TEXTURE":"",n.logarithmicDepthBuffer?"#define USE_LOGDEPTHBUF":"",n.logarithmicDepthBuffer&&n.rendererExtensionFragDepth?"#define USE_LOGDEPTHBUF_EXT":"","uniform mat4 viewMatrix;","uniform vec3 cameraPosition;","uniform bool isOrthographic;",0!==n.toneMapping?"#define TONE_MAPPING":"",0!==n.toneMapping?t8.tonemapping_pars_fragment:"",0!==n.toneMapping?function(e,t){let n;switch(t){case 1:n="Linear";break;case 2:n="Reinhard";break;case 3:n="OptimizedCineon";break;case 4:n="ACESFilmic";break;case 5:n="Custom";break;default:console.warn("THREE.WebGLProgram: Unsupported toneMapping:",t),n="Linear"}return"vec3 "+e+"( vec3 color ) { return "+n+"ToneMapping( color ); }"}("toneMapping",n.toneMapping):"",n.dithering?"#define DITHERING":"",n.opaque?"#define OPAQUE":"",t8.colorspace_pars_fragment,function(e,t){let n=function(e){let t,n=N.getPrimaries(N.workingColorSpace),i=N.getPrimaries(e);switch(n===i?t="":"p3"===n&&i===c?t="LinearDisplayP3ToLinearSRGB":n===c&&"p3"===i&&(t="LinearSRGBToLinearDisplayP3"),e){case a:case o:return[t,"LinearTransferOETF"];case r:case s:return[t,"sRGBTransferOETF"];default:return console.warn("THREE.WebGLProgram: Unsupported color space:",e),[t,"LinearTransferOETF"]}}(t);return"vec4 ".concat(e,"( vec4 value ) { return ").concat(n[0],"( ").concat(n[1],"( value ) ); }")}("linearToOutputTexel",n.outputColorSpace),n.useDepthPacking?"#define DEPTH_PACKING "+n.depthPacking:"","\n"].filter(iL).join("\n")),v=iU(v=iP(v=iD(v),n),n),x=iU(x=iP(x=iD(x),n),n),v=iz(v),x=iz(x),n.isWebGL2&&!0!==n.isRawShaderMaterial&&(R="#version 300 es\n",l="precision mediump sampler2DArray;\n#define attribute in\n#define varying out\n#define texture2D texture\n"+l,h=["precision mediump sampler2DArray;","#define varying in",n.glslVersion===u?"":"layout(location = 0) out highp vec4 pc_fragColor;",n.glslVersion===u?"":"#define gl_FragColor pc_fragColor","#define gl_FragDepthEXT gl_FragDepth","#define texture2D texture","#define textureCube texture","#define texture2DProj textureProj","#define texture2DLodEXT textureLod","#define texture2DProjLodEXT textureProjLod","#define textureCubeLodEXT textureLod","#define texture2DGradEXT textureGrad","#define texture2DProjGradEXT textureProjGrad","#define textureCubeGradEXT textureGrad"].join("\n")+"\n"+h);let C=R+l+v,L=R+h+x,P=iw(g,g.VERTEX_SHADER,C),U=iw(g,g.FRAGMENT_SHADER,L);function D(t){if(e.debug.checkShaderErrors){let n=g.getProgramInfoLog(w).trim(),i=g.getShaderInfoLog(P).trim(),r=g.getShaderInfoLog(U).trim(),a=!0,s=!0;if(!1===g.getProgramParameter(w,g.LINK_STATUS))if(a=!1,"function"==typeof e.debug.onShaderError)e.debug.onShaderError(g,w,P,U);else{let e=iC(g,P,"vertex"),t=iC(g,U,"fragment");console.error("THREE.WebGLProgram: Shader Error "+g.getError()+" - VALIDATE_STATUS "+g.getProgramParameter(w,g.VALIDATE_STATUS)+"\n\nProgram Info Log: "+n+"\n"+e+"\n"+t)}else""!==n?console.warn("THREE.WebGLProgram: Program Info Log:",n):(""===i||""===r)&&(s=!1);s&&(t.diagnostics={runnable:a,programLog:n,vertexShader:{log:i,prefix:l},fragmentShader:{log:r,prefix:h}})}g.deleteShader(P),g.deleteShader(U),d=new iA(g,w),p=function(e,t){let n={},i=e.getProgramParameter(t,e.ACTIVE_ATTRIBUTES);for(let r=0;r<i;r++){let i=e.getActiveAttrib(t,r),a=i.name,s=1;i.type===e.FLOAT_MAT2&&(s=2),i.type===e.FLOAT_MAT3&&(s=3),i.type===e.FLOAT_MAT4&&(s=4),n[a]={type:i.type,location:e.getAttribLocation(t,a),locationSize:s}}return n}(g,w)}g.attachShader(w,P),g.attachShader(w,U),void 0!==n.index0AttributeName?g.bindAttribLocation(w,0,n.index0AttributeName):!0===n.morphTargets&&g.bindAttribLocation(w,0,"position"),g.linkProgram(w),this.getUniforms=function(){return void 0===d&&D(this),d},this.getAttributes=function(){return void 0===p&&D(this),p};let I=!1===n.rendererExtensionParallelShaderCompile;return this.isReady=function(){return!1===I&&(I=g.getProgramParameter(w,37297)),I},this.destroy=function(){i.releaseStatesOfProgram(this),g.deleteProgram(w),this.program=void 0},this.type=n.shaderType,this.name=n.shaderName,this.id=iR++,this.cacheKey=t,this.usedTimes=1,this.program=w,this.vertexShader=P,this.fragmentShader=U,this}let iG=0;class ik{update(e){let t=e.vertexShader,n=e.fragmentShader,i=this._getShaderStage(t),r=this._getShaderStage(n),a=this._getShaderCacheForMaterial(e);return!1===a.has(i)&&(a.add(i),i.usedTimes++),!1===a.has(r)&&(a.add(r),r.usedTimes++),this}remove(e){for(let t of this.materialCache.get(e))t.usedTimes--,0===t.usedTimes&&this.shaderCache.delete(t.code);return this.materialCache.delete(e),this}getVertexShaderID(e){return this._getShaderStage(e.vertexShader).id}getFragmentShaderID(e){return this._getShaderStage(e.fragmentShader).id}dispose(){this.shaderCache.clear(),this.materialCache.clear()}_getShaderCacheForMaterial(e){let t=this.materialCache,n=t.get(e);return void 0===n&&(n=new Set,t.set(e,n)),n}_getShaderStage(e){let t=this.shaderCache,n=t.get(e);return void 0===n&&(n=new iW(e),t.set(e,n)),n}constructor(){this.shaderCache=new Map,this.materialCache=new Map}}class iW{constructor(e){this.id=iG++,this.code=e,this.usedTimes=0}}function iX(e,t,n,i,r,s,o){let l=new eI,c=new ik,u=[],d=r.isWebGL2,p=r.logarithmicDepthBuffer,f=r.vertexTextures,m=r.precision,g={MeshDepthMaterial:"depth",MeshDistanceMaterial:"distanceRGBA",MeshNormalMaterial:"normal",MeshBasicMaterial:"basic",MeshLambertMaterial:"lambert",MeshPhongMaterial:"phong",MeshToonMaterial:"toon",MeshStandardMaterial:"physical",MeshPhysicalMaterial:"physical",MeshMatcapMaterial:"matcap",LineBasicMaterial:"basic",LineDashedMaterial:"dashed",PointsMaterial:"points",ShadowMaterial:"shadow",SpriteMaterial:"sprite"};function _(e){return 0===e?"uv":"uv".concat(e)}return{getParameters:function(s,l,u,v,x){let y,M,S,E,T=v.fog,b=x.geometry,A=s.isMeshStandardMaterial?v.environment:null,w=(s.isMeshStandardMaterial?n:t).get(s.envMap||A),R=w&&306===w.mapping?w.image.height:null,C=g[s.type];null!==s.precision&&(m=r.getMaxPrecision(s.precision))!==s.precision&&console.warn("THREE.WebGLProgram.getParameters:",s.precision,"not supported, using",m,"instead.");let L=b.morphAttributes.position||b.morphAttributes.normal||b.morphAttributes.color,P=void 0!==L?L.length:0,U=0;if(void 0!==b.morphAttributes.position&&(U=1),void 0!==b.morphAttributes.normal&&(U=2),void 0!==b.morphAttributes.color&&(U=3),C){let e=t7[C];y=e.vertexShader,M=e.fragmentShader}else y=s.vertexShader,M=s.fragmentShader,c.update(s),S=c.getVertexShaderID(s),E=c.getFragmentShaderID(s);let D=e.getRenderTarget(),I=!0===x.isInstancedMesh,O=!!s.map,F=!!s.matcap,z=!!w,B=!!s.aoMap,H=!!s.lightMap,V=!!s.bumpMap,G=!!s.normalMap,k=!!s.displacementMap,W=!!s.emissiveMap,X=!!s.metalnessMap,j=!!s.roughnessMap,q=s.anisotropy>0,Y=s.clearcoat>0,J=s.iridescence>0,Z=s.sheen>0,K=s.transmission>0,Q=q&&!!s.anisotropyMap,$=Y&&!!s.clearcoatMap,ee=Y&&!!s.clearcoatNormalMap,et=Y&&!!s.clearcoatRoughnessMap,en=J&&!!s.iridescenceMap,ei=J&&!!s.iridescenceThicknessMap,er=Z&&!!s.sheenColorMap,ea=Z&&!!s.sheenRoughnessMap,es=!!s.specularMap,eo=!!s.specularColorMap,el=!!s.specularIntensityMap,eh=K&&!!s.transmissionMap,ec=K&&!!s.thicknessMap,eu=!!s.gradientMap,ed=!!s.alphaMap,ep=s.alphaTest>0,ef=!!s.alphaHash,em=!!s.extensions,eg=!!b.attributes.uv1,e_=!!b.attributes.uv2,ev=!!b.attributes.uv3,ex=0;return s.toneMapped&&(null===D||!0===D.isXRRenderTarget)&&(ex=e.toneMapping),{isWebGL2:d,shaderID:C,shaderType:s.type,shaderName:s.name,vertexShader:y,fragmentShader:M,defines:s.defines,customVertexShaderID:S,customFragmentShaderID:E,isRawShaderMaterial:!0===s.isRawShaderMaterial,glslVersion:s.glslVersion,precision:m,instancing:I,instancingColor:I&&null!==x.instanceColor,supportsVertexTextures:f,outputColorSpace:null===D?e.outputColorSpace:!0===D.isXRRenderTarget?D.texture.colorSpace:a,map:O,matcap:F,envMap:z,envMapMode:z&&w.mapping,envMapCubeUVHeight:R,aoMap:B,lightMap:H,bumpMap:V,normalMap:G,displacementMap:f&&k,emissiveMap:W,normalMapObjectSpace:G&&1===s.normalMapType,normalMapTangentSpace:G&&0===s.normalMapType,metalnessMap:X,roughnessMap:j,anisotropy:q,anisotropyMap:Q,clearcoat:Y,clearcoatMap:$,clearcoatNormalMap:ee,clearcoatRoughnessMap:et,iridescence:J,iridescenceMap:en,iridescenceThicknessMap:ei,sheen:Z,sheenColorMap:er,sheenRoughnessMap:ea,specularMap:es,specularColorMap:eo,specularIntensityMap:el,transmission:K,transmissionMap:eh,thicknessMap:ec,gradientMap:eu,opaque:!1===s.transparent&&1===s.blending,alphaMap:ed,alphaTest:ep,alphaHash:ef,combine:s.combine,mapUv:O&&_(s.map.channel),aoMapUv:B&&_(s.aoMap.channel),lightMapUv:H&&_(s.lightMap.channel),bumpMapUv:V&&_(s.bumpMap.channel),normalMapUv:G&&_(s.normalMap.channel),displacementMapUv:k&&_(s.displacementMap.channel),emissiveMapUv:W&&_(s.emissiveMap.channel),metalnessMapUv:X&&_(s.metalnessMap.channel),roughnessMapUv:j&&_(s.roughnessMap.channel),anisotropyMapUv:Q&&_(s.anisotropyMap.channel),clearcoatMapUv:$&&_(s.clearcoatMap.channel),clearcoatNormalMapUv:ee&&_(s.clearcoatNormalMap.channel),clearcoatRoughnessMapUv:et&&_(s.clearcoatRoughnessMap.channel),iridescenceMapUv:en&&_(s.iridescenceMap.channel),iridescenceThicknessMapUv:ei&&_(s.iridescenceThicknessMap.channel),sheenColorMapUv:er&&_(s.sheenColorMap.channel),sheenRoughnessMapUv:ea&&_(s.sheenRoughnessMap.channel),specularMapUv:es&&_(s.specularMap.channel),specularColorMapUv:eo&&_(s.specularColorMap.channel),specularIntensityMapUv:el&&_(s.specularIntensityMap.channel),transmissionMapUv:eh&&_(s.transmissionMap.channel),thicknessMapUv:ec&&_(s.thicknessMap.channel),alphaMapUv:ed&&_(s.alphaMap.channel),vertexTangents:!!b.attributes.tangent&&(G||q),vertexColors:s.vertexColors,vertexAlphas:!0===s.vertexColors&&!!b.attributes.color&&4===b.attributes.color.itemSize,vertexUv1s:eg,vertexUv2s:e_,vertexUv3s:ev,pointsUvs:!0===x.isPoints&&!!b.attributes.uv&&(O||ed),fog:!!T,useFog:!0===s.fog,fogExp2:T&&T.isFogExp2,flatShading:!0===s.flatShading,sizeAttenuation:!0===s.sizeAttenuation,logarithmicDepthBuffer:p,skinning:!0===x.isSkinnedMesh,morphTargets:void 0!==b.morphAttributes.position,morphNormals:void 0!==b.morphAttributes.normal,morphColors:void 0!==b.morphAttributes.color,morphTargetsCount:P,morphTextureStride:U,numDirLights:l.directional.length,numPointLights:l.point.length,numSpotLights:l.spot.length,numSpotLightMaps:l.spotLightMap.length,numRectAreaLights:l.rectArea.length,numHemiLights:l.hemi.length,numDirLightShadows:l.directionalShadowMap.length,numPointLightShadows:l.pointShadowMap.length,numSpotLightShadows:l.spotShadowMap.length,numSpotLightShadowsWithMaps:l.numSpotLightShadowsWithMaps,numLightProbes:l.numLightProbes,numClippingPlanes:o.numPlanes,numClipIntersection:o.numIntersection,dithering:s.dithering,shadowMapEnabled:e.shadowMap.enabled&&u.length>0,shadowMapType:e.shadowMap.type,toneMapping:ex,useLegacyLights:e._useLegacyLights,decodeVideoTexture:O&&!0===s.map.isVideoTexture&&N.getTransfer(s.map.colorSpace)===h,premultipliedAlpha:s.premultipliedAlpha,doubleSided:2===s.side,flipSided:1===s.side,useDepthPacking:s.depthPacking>=0,depthPacking:s.depthPacking||0,index0AttributeName:s.index0AttributeName,extensionDerivatives:em&&!0===s.extensions.derivatives,extensionFragDepth:em&&!0===s.extensions.fragDepth,extensionDrawBuffers:em&&!0===s.extensions.drawBuffers,extensionShaderTextureLOD:em&&!0===s.extensions.shaderTextureLOD,rendererExtensionFragDepth:d||i.has("EXT_frag_depth"),rendererExtensionDrawBuffers:d||i.has("WEBGL_draw_buffers"),rendererExtensionShaderTextureLod:d||i.has("EXT_shader_texture_lod"),rendererExtensionParallelShaderCompile:i.has("KHR_parallel_shader_compile"),customProgramCacheKey:s.customProgramCacheKey()}},getProgramCacheKey:function(t){var n,i,r,a;let s=[];if(t.shaderID?s.push(t.shaderID):(s.push(t.customVertexShaderID),s.push(t.customFragmentShaderID)),void 0!==t.defines)for(let e in t.defines)s.push(e),s.push(t.defines[e]);return!1===t.isRawShaderMaterial&&(n=s,i=t,n.push(i.precision),n.push(i.outputColorSpace),n.push(i.envMapMode),n.push(i.envMapCubeUVHeight),n.push(i.mapUv),n.push(i.alphaMapUv),n.push(i.lightMapUv),n.push(i.aoMapUv),n.push(i.bumpMapUv),n.push(i.normalMapUv),n.push(i.displacementMapUv),n.push(i.emissiveMapUv),n.push(i.metalnessMapUv),n.push(i.roughnessMapUv),n.push(i.anisotropyMapUv),n.push(i.clearcoatMapUv),n.push(i.clearcoatNormalMapUv),n.push(i.clearcoatRoughnessMapUv),n.push(i.iridescenceMapUv),n.push(i.iridescenceThicknessMapUv),n.push(i.sheenColorMapUv),n.push(i.sheenRoughnessMapUv),n.push(i.specularMapUv),n.push(i.specularColorMapUv),n.push(i.specularIntensityMapUv),n.push(i.transmissionMapUv),n.push(i.thicknessMapUv),n.push(i.combine),n.push(i.fogExp2),n.push(i.sizeAttenuation),n.push(i.morphTargetsCount),n.push(i.morphAttributeCount),n.push(i.numDirLights),n.push(i.numPointLights),n.push(i.numSpotLights),n.push(i.numSpotLightMaps),n.push(i.numHemiLights),n.push(i.numRectAreaLights),n.push(i.numDirLightShadows),n.push(i.numPointLightShadows),n.push(i.numSpotLightShadows),n.push(i.numSpotLightShadowsWithMaps),n.push(i.numLightProbes),n.push(i.shadowMapType),n.push(i.toneMapping),n.push(i.numClippingPlanes),n.push(i.numClipIntersection),n.push(i.depthPacking),r=s,a=t,l.disableAll(),a.isWebGL2&&l.enable(0),a.supportsVertexTextures&&l.enable(1),a.instancing&&l.enable(2),a.instancingColor&&l.enable(3),a.matcap&&l.enable(4),a.envMap&&l.enable(5),a.normalMapObjectSpace&&l.enable(6),a.normalMapTangentSpace&&l.enable(7),a.clearcoat&&l.enable(8),a.iridescence&&l.enable(9),a.alphaTest&&l.enable(10),a.vertexColors&&l.enable(11),a.vertexAlphas&&l.enable(12),a.vertexUv1s&&l.enable(13),a.vertexUv2s&&l.enable(14),a.vertexUv3s&&l.enable(15),a.vertexTangents&&l.enable(16),a.anisotropy&&l.enable(17),a.alphaHash&&l.enable(18),r.push(l.mask),l.disableAll(),a.fog&&l.enable(0),a.useFog&&l.enable(1),a.flatShading&&l.enable(2),a.logarithmicDepthBuffer&&l.enable(3),a.skinning&&l.enable(4),a.morphTargets&&l.enable(5),a.morphNormals&&l.enable(6),a.morphColors&&l.enable(7),a.premultipliedAlpha&&l.enable(8),a.shadowMapEnabled&&l.enable(9),a.useLegacyLights&&l.enable(10),a.doubleSided&&l.enable(11),a.flipSided&&l.enable(12),a.useDepthPacking&&l.enable(13),a.dithering&&l.enable(14),a.transmission&&l.enable(15),a.sheen&&l.enable(16),a.opaque&&l.enable(17),a.pointsUvs&&l.enable(18),a.decodeVideoTexture&&l.enable(19),r.push(l.mask),s.push(e.outputColorSpace)),s.push(t.customProgramCacheKey),s.join()},getUniforms:function(e){let t,n=g[e.type];if(n){let e=t7[n];t=tW.clone(e.uniforms)}else t=e.uniforms;return t},acquireProgram:function(t,n){let i;for(let e=0,t=u.length;e<t;e++){let t=u[e];if(t.cacheKey===n){i=t,++i.usedTimes;break}}return void 0===i&&(i=new iV(e,n,t,s),u.push(i)),i},releaseProgram:function(e){if(0==--e.usedTimes){let t=u.indexOf(e);u[t]=u[u.length-1],u.pop(),e.destroy()}},releaseShaderCache:function(e){c.remove(e)},programs:u,dispose:function(){c.dispose()}}}function ij(){let e=new WeakMap;return{get:function(t){let n=e.get(t);return void 0===n&&(n={},e.set(t,n)),n},remove:function(t){e.delete(t)},update:function(t,n,i){e.get(t)[n]=i},dispose:function(){e=new WeakMap}}}function iq(e,t){return e.groupOrder!==t.groupOrder?e.groupOrder-t.groupOrder:e.renderOrder!==t.renderOrder?e.renderOrder-t.renderOrder:e.material.id!==t.material.id?e.material.id-t.material.id:e.z!==t.z?e.z-t.z:e.id-t.id}function iY(e,t){return e.groupOrder!==t.groupOrder?e.groupOrder-t.groupOrder:e.renderOrder!==t.renderOrder?e.renderOrder-t.renderOrder:e.z!==t.z?t.z-e.z:e.id-t.id}function iJ(){let e=[],t=0,n=[],i=[],r=[];function a(n,i,r,a,s,o){let l=e[t];return void 0===l?(l={id:n.id,object:n,geometry:i,material:r,groupOrder:a,renderOrder:n.renderOrder,z:s,group:o},e[t]=l):(l.id=n.id,l.object=n,l.geometry=i,l.material=r,l.groupOrder=a,l.renderOrder=n.renderOrder,l.z=s,l.group=o),t++,l}return{opaque:n,transmissive:i,transparent:r,init:function(){t=0,n.length=0,i.length=0,r.length=0},push:function(e,t,s,o,l,h){let c=a(e,t,s,o,l,h);s.transmission>0?i.push(c):!0===s.transparent?r.push(c):n.push(c)},unshift:function(e,t,s,o,l,h){let c=a(e,t,s,o,l,h);s.transmission>0?i.unshift(c):!0===s.transparent?r.unshift(c):n.unshift(c)},finish:function(){for(let n=t,i=e.length;n<i;n++){let t=e[n];if(null===t.id)break;t.id=null,t.object=null,t.geometry=null,t.material=null,t.group=null}},sort:function(e,t){n.length>1&&n.sort(e||iq),i.length>1&&i.sort(t||iY),r.length>1&&r.sort(t||iY)}}}function iZ(){let e=new WeakMap;return{get:function(t,n){let i,r=e.get(t);return void 0===r?(i=new iJ,e.set(t,[i])):n>=r.length?(i=new iJ,r.push(i)):i=r[n],i},dispose:function(){e=new WeakMap}}}function iK(){let e={};return{get:function(t){let n;if(void 0!==e[t.id])return e[t.id];switch(t.type){case"DirectionalLight":n={direction:new Y,color:new tn};break;case"SpotLight":n={position:new Y,direction:new Y,color:new tn,distance:0,coneCos:0,penumbraCos:0,decay:0};break;case"PointLight":n={position:new Y,color:new tn,distance:0,decay:0};break;case"HemisphereLight":n={direction:new Y,skyColor:new tn,groundColor:new tn};break;case"RectAreaLight":n={color:new tn,position:new Y,halfWidth:new Y,halfHeight:new Y}}return e[t.id]=n,n}}}let iQ=0;function i$(e,t){return 2*!!t.castShadow-2*!!e.castShadow+ +!!t.map-!!e.map}function i0(e,t){let n=new iK,i=function(){let e={};return{get:function(t){let n;if(void 0!==e[t.id])return e[t.id];switch(t.type){case"DirectionalLight":case"SpotLight":n={shadowBias:0,shadowNormalBias:0,shadowRadius:1,shadowMapSize:new S};break;case"PointLight":n={shadowBias:0,shadowNormalBias:0,shadowRadius:1,shadowMapSize:new S,shadowCameraNear:1,shadowCameraFar:1e3}}return e[t.id]=n,n}}}(),r={version:0,hash:{directionalLength:-1,pointLength:-1,spotLength:-1,rectAreaLength:-1,hemiLength:-1,numDirectionalShadows:-1,numPointShadows:-1,numSpotShadows:-1,numSpotMaps:-1,numLightProbes:-1},ambient:[0,0,0],probe:[],directional:[],directionalShadow:[],directionalShadowMap:[],directionalShadowMatrix:[],spot:[],spotLightMap:[],spotShadow:[],spotShadowMap:[],spotLightMatrix:[],rectArea:[],rectAreaLTC1:null,rectAreaLTC2:null,point:[],pointShadow:[],pointShadowMap:[],pointShadowMatrix:[],hemi:[],numSpotLightShadowsWithMaps:0,numLightProbes:0};for(let e=0;e<9;e++)r.probe.push(new Y);let a=new Y,s=new eT,o=new eT;return{setup:function(a,s){let o=0,l=0,h=0;for(let e=0;e<9;e++)r.probe[e].set(0,0,0);let c=0,u=0,d=0,p=0,f=0,m=0,g=0,_=0,v=0,x=0,y=0;a.sort(i$);let M=!0===s?Math.PI:1;for(let e=0,t=a.length;e<t;e++){let t=a[e],s=t.color,S=t.intensity,E=t.distance,T=t.shadow&&t.shadow.map?t.shadow.map.texture:null;if(t.isAmbientLight)o+=s.r*S*M,l+=s.g*S*M,h+=s.b*S*M;else if(t.isLightProbe){for(let e=0;e<9;e++)r.probe[e].addScaledVector(t.sh.coefficients[e],S);y++}else if(t.isDirectionalLight){let e=n.get(t);if(e.color.copy(t.color).multiplyScalar(t.intensity*M),t.castShadow){let e=t.shadow,n=i.get(t);n.shadowBias=e.bias,n.shadowNormalBias=e.normalBias,n.shadowRadius=e.radius,n.shadowMapSize=e.mapSize,r.directionalShadow[c]=n,r.directionalShadowMap[c]=T,r.directionalShadowMatrix[c]=t.shadow.matrix,m++}r.directional[c]=e,c++}else if(t.isSpotLight){let e=n.get(t);e.position.setFromMatrixPosition(t.matrixWorld),e.color.copy(s).multiplyScalar(S*M),e.distance=E,e.coneCos=Math.cos(t.angle),e.penumbraCos=Math.cos(t.angle*(1-t.penumbra)),e.decay=t.decay,r.spot[d]=e;let a=t.shadow;if(t.map&&(r.spotLightMap[v]=t.map,v++,a.updateMatrices(t),t.castShadow&&x++),r.spotLightMatrix[d]=a.matrix,t.castShadow){let e=i.get(t);e.shadowBias=a.bias,e.shadowNormalBias=a.normalBias,e.shadowRadius=a.radius,e.shadowMapSize=a.mapSize,r.spotShadow[d]=e,r.spotShadowMap[d]=T,_++}d++}else if(t.isRectAreaLight){let e=n.get(t);e.color.copy(s).multiplyScalar(S),e.halfWidth.set(.5*t.width,0,0),e.halfHeight.set(0,.5*t.height,0),r.rectArea[p]=e,p++}else if(t.isPointLight){let e=n.get(t);if(e.color.copy(t.color).multiplyScalar(t.intensity*M),e.distance=t.distance,e.decay=t.decay,t.castShadow){let e=t.shadow,n=i.get(t);n.shadowBias=e.bias,n.shadowNormalBias=e.normalBias,n.shadowRadius=e.radius,n.shadowMapSize=e.mapSize,n.shadowCameraNear=e.camera.near,n.shadowCameraFar=e.camera.far,r.pointShadow[u]=n,r.pointShadowMap[u]=T,r.pointShadowMatrix[u]=t.shadow.matrix,g++}r.point[u]=e,u++}else if(t.isHemisphereLight){let e=n.get(t);e.skyColor.copy(t.color).multiplyScalar(S*M),e.groundColor.copy(t.groundColor).multiplyScalar(S*M),r.hemi[f]=e,f++}}p>0&&(t.isWebGL2||!0===e.has("OES_texture_float_linear")?(r.rectAreaLTC1=t9.LTC_FLOAT_1,r.rectAreaLTC2=t9.LTC_FLOAT_2):!0===e.has("OES_texture_half_float_linear")?(r.rectAreaLTC1=t9.LTC_HALF_1,r.rectAreaLTC2=t9.LTC_HALF_2):console.error("THREE.WebGLRenderer: Unable to use RectAreaLight. Missing WebGL extensions.")),r.ambient[0]=o,r.ambient[1]=l,r.ambient[2]=h;let S=r.hash;(S.directionalLength!==c||S.pointLength!==u||S.spotLength!==d||S.rectAreaLength!==p||S.hemiLength!==f||S.numDirectionalShadows!==m||S.numPointShadows!==g||S.numSpotShadows!==_||S.numSpotMaps!==v||S.numLightProbes!==y)&&(r.directional.length=c,r.spot.length=d,r.rectArea.length=p,r.point.length=u,r.hemi.length=f,r.directionalShadow.length=m,r.directionalShadowMap.length=m,r.pointShadow.length=g,r.pointShadowMap.length=g,r.spotShadow.length=_,r.spotShadowMap.length=_,r.directionalShadowMatrix.length=m,r.pointShadowMatrix.length=g,r.spotLightMatrix.length=_+v-x,r.spotLightMap.length=v,r.numSpotLightShadowsWithMaps=x,r.numLightProbes=y,S.directionalLength=c,S.pointLength=u,S.spotLength=d,S.rectAreaLength=p,S.hemiLength=f,S.numDirectionalShadows=m,S.numPointShadows=g,S.numSpotShadows=_,S.numSpotMaps=v,S.numLightProbes=y,r.version=iQ++)},setupView:function(e,t){let n=0,i=0,l=0,h=0,c=0,u=t.matrixWorldInverse;for(let t=0,d=e.length;t<d;t++){let d=e[t];if(d.isDirectionalLight){let e=r.directional[n];e.direction.setFromMatrixPosition(d.matrixWorld),a.setFromMatrixPosition(d.target.matrixWorld),e.direction.sub(a),e.direction.transformDirection(u),n++}else if(d.isSpotLight){let e=r.spot[l];e.position.setFromMatrixPosition(d.matrixWorld),e.position.applyMatrix4(u),e.direction.setFromMatrixPosition(d.matrixWorld),a.setFromMatrixPosition(d.target.matrixWorld),e.direction.sub(a),e.direction.transformDirection(u),l++}else if(d.isRectAreaLight){let e=r.rectArea[h];e.position.setFromMatrixPosition(d.matrixWorld),e.position.applyMatrix4(u),o.identity(),s.copy(d.matrixWorld),s.premultiply(u),o.extractRotation(s),e.halfWidth.set(.5*d.width,0,0),e.halfHeight.set(0,.5*d.height,0),e.halfWidth.applyMatrix4(o),e.halfHeight.applyMatrix4(o),h++}else if(d.isPointLight){let e=r.point[i];e.position.setFromMatrixPosition(d.matrixWorld),e.position.applyMatrix4(u),i++}else if(d.isHemisphereLight){let e=r.hemi[c];e.direction.setFromMatrixPosition(d.matrixWorld),e.direction.transformDirection(u),c++}}},state:r}}function i1(e,t){let n=new i0(e,t),i=[],r=[];return{init:function(){i.length=0,r.length=0},state:{lightsArray:i,shadowsArray:r,lights:n},setupLights:function(e){n.setup(i,e)},setupLightsView:function(e){n.setupView(i,e)},pushLight:function(e){i.push(e)},pushShadow:function(e){r.push(e)}}}function i2(e,t){let n=new WeakMap;return{get:function(i){let r,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,s=n.get(i);return void 0===s?(r=new i1(e,t),n.set(i,[r])):a>=s.length?(r=new i1(e,t),s.push(r)):r=s[a],r},dispose:function(){n=new WeakMap}}}class i3 extends ta{copy(e){return super.copy(e),this.depthPacking=e.depthPacking,this.map=e.map,this.alphaMap=e.alphaMap,this.displacementMap=e.displacementMap,this.displacementScale=e.displacementScale,this.displacementBias=e.displacementBias,this.wireframe=e.wireframe,this.wireframeLinewidth=e.wireframeLinewidth,this}constructor(e){super(),this.isMeshDepthMaterial=!0,this.type="MeshDepthMaterial",this.depthPacking=3200,this.map=null,this.alphaMap=null,this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.wireframe=!1,this.wireframeLinewidth=1,this.setValues(e)}}class i4 extends ta{copy(e){return super.copy(e),this.map=e.map,this.alphaMap=e.alphaMap,this.displacementMap=e.displacementMap,this.displacementScale=e.displacementScale,this.displacementBias=e.displacementBias,this}constructor(e){super(),this.isMeshDistanceMaterial=!0,this.type="MeshDistanceMaterial",this.map=null,this.alphaMap=null,this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.setValues(e)}}function i5(e,t,n){let i=new t3,r=new S,a=new S,s=new G,o=new i3({depthPacking:3201}),l=new i4,h={},c=n.maxTextureSize,u={0:1,1:0,2:2},d=new tX({defines:{VSM_SAMPLES:8},uniforms:{shadow_pass:{value:null},resolution:{value:new S},radius:{value:4}},vertexShader:"void main() {\n	gl_Position = vec4( position, 1.0 );\n}",fragmentShader:"uniform sampler2D shadow_pass;\nuniform vec2 resolution;\nuniform float radius;\n#include <packing>\nvoid main() {\n	const float samples = float( VSM_SAMPLES );\n	float mean = 0.0;\n	float squared_mean = 0.0;\n	float uvStride = samples <= 1.0 ? 0.0 : 2.0 / ( samples - 1.0 );\n	float uvStart = samples <= 1.0 ? 0.0 : - 1.0;\n	for ( float i = 0.0; i < samples; i ++ ) {\n		float uvOffset = uvStart + i * uvStride;\n		#ifdef HORIZONTAL_PASS\n			vec2 distribution = unpackRGBATo2Half( texture2D( shadow_pass, ( gl_FragCoord.xy + vec2( uvOffset, 0.0 ) * radius ) / resolution ) );\n			mean += distribution.x;\n			squared_mean += distribution.y * distribution.y + distribution.x * distribution.x;\n		#else\n			float depth = unpackRGBAToDepth( texture2D( shadow_pass, ( gl_FragCoord.xy + vec2( 0.0, uvOffset ) * radius ) / resolution ) );\n			mean += depth;\n			squared_mean += depth * depth;\n		#endif\n	}\n	mean = mean / samples;\n	squared_mean = squared_mean / samples;\n	float std_dev = sqrt( squared_mean - mean * mean );\n	gl_FragColor = pack2HalfToRGBA( vec2( mean, std_dev ) );\n}"}),p=d.clone();p.defines.HORIZONTAL_PASS=1;let f=new ty;f.setAttribute("position",new th(new Float32Array([-1,-1,.5,3,-1,.5,-1,3,.5]),3));let m=new tz(f,d),g=this;this.enabled=!1,this.autoUpdate=!0,this.needsUpdate=!1,this.type=1;let _=this.type;function v(t,n,i,r){let a=null,s=!0===i.isPointLight?t.customDistanceMaterial:t.customDepthMaterial;if(void 0!==s)a=s;else if(a=!0===i.isPointLight?l:o,e.localClippingEnabled&&!0===n.clipShadows&&Array.isArray(n.clippingPlanes)&&0!==n.clippingPlanes.length||n.displacementMap&&0!==n.displacementScale||n.alphaMap&&n.alphaTest>0||n.map&&n.alphaTest>0){let e=a.uuid,t=n.uuid,i=h[e];void 0===i&&(i={},h[e]=i);let r=i[t];void 0===r&&(r=a.clone(),i[t]=r),a=r}return a.visible=n.visible,a.wireframe=n.wireframe,3===r?a.side=null!==n.shadowSide?n.shadowSide:n.side:a.side=null!==n.shadowSide?n.shadowSide:u[n.side],a.alphaMap=n.alphaMap,a.alphaTest=n.alphaTest,a.map=n.map,a.clipShadows=n.clipShadows,a.clippingPlanes=n.clippingPlanes,a.clipIntersection=n.clipIntersection,a.displacementMap=n.displacementMap,a.displacementScale=n.displacementScale,a.displacementBias=n.displacementBias,a.wireframeLinewidth=n.wireframeLinewidth,a.linewidth=n.linewidth,!0===i.isPointLight&&!0===a.isMeshDistanceMaterial&&(e.properties.get(a).light=i),a}this.render=function(n,o,l){if(!1===g.enabled||!1===g.autoUpdate&&!1===g.needsUpdate||0===n.length)return;let h=e.getRenderTarget(),u=e.getActiveCubeFace(),f=e.getActiveMipmapLevel(),x=e.state;x.setBlending(0),x.buffers.color.setClear(1,1,1,1),x.buffers.depth.setTest(!0),x.setScissorTest(!1);let y=3!==_&&3===this.type,M=3===_&&3!==this.type;for(let h=0,u=n.length;h<u;h++){let u=n[h],f=u.shadow;if(void 0===f){console.warn("THREE.WebGLShadowMap:",u,"has no shadow.");continue}if(!1===f.autoUpdate&&!1===f.needsUpdate)continue;r.copy(f.mapSize);let g=f.getFrameExtents();if(r.multiply(g),a.copy(f.mapSize),(r.x>c||r.y>c)&&(r.x>c&&(a.x=Math.floor(c/g.x),r.x=a.x*g.x,f.mapSize.x=a.x),r.y>c&&(a.y=Math.floor(c/g.y),r.y=a.y*g.y,f.mapSize.y=a.y)),null===f.map||!0===y||!0===M){let e=3!==this.type?{minFilter:1003,magFilter:1003}:{};null!==f.map&&f.map.dispose(),f.map=new W(r.x,r.y,e),f.map.texture.name=u.name+".shadowMap",f.camera.updateProjectionMatrix()}e.setRenderTarget(f.map),e.clear();let _=f.getViewportCount();for(let n=0;n<_;n++){let r=f.getViewport(n);s.set(a.x*r.x,a.y*r.y,a.x*r.z,a.y*r.w),x.viewport(s),f.updateMatrices(u,n),i=f.getFrustum(),function n(r,a,s,o,l){if(!1===r.visible)return;if(r.layers.test(a.layers)&&(r.isMesh||r.isLine||r.isPoints)&&(r.castShadow||r.receiveShadow&&3===l)&&(!r.frustumCulled||i.intersectsObject(r))){r.modelViewMatrix.multiplyMatrices(s.matrixWorldInverse,r.matrixWorld);let n=t.update(r),i=r.material;if(Array.isArray(i)){let t=n.groups;for(let a=0,h=t.length;a<h;a++){let h=t[a],c=i[h.materialIndex];if(c&&c.visible){let t=v(r,c,o,l);e.renderBufferDirect(s,null,n,t,r,h)}}}else if(i.visible){let t=v(r,i,o,l);e.renderBufferDirect(s,null,n,t,r,null)}}let h=r.children;for(let e=0,t=h.length;e<t;e++)n(h[e],a,s,o,l)}(o,l,f.camera,u,this.type)}!0!==f.isPointLightShadow&&3===this.type&&function(n,i){let a=t.update(m);d.defines.VSM_SAMPLES!==n.blurSamples&&(d.defines.VSM_SAMPLES=n.blurSamples,p.defines.VSM_SAMPLES=n.blurSamples,d.needsUpdate=!0,p.needsUpdate=!0),null===n.mapPass&&(n.mapPass=new W(r.x,r.y)),d.uniforms.shadow_pass.value=n.map.texture,d.uniforms.resolution.value=n.mapSize,d.uniforms.radius.value=n.radius,e.setRenderTarget(n.mapPass),e.clear(),e.renderBufferDirect(i,null,a,d,m,null),p.uniforms.shadow_pass.value=n.mapPass.texture,p.uniforms.resolution.value=n.mapSize,p.uniforms.radius.value=n.radius,e.setRenderTarget(n.map),e.clear(),e.renderBufferDirect(i,null,a,p,m,null)}(f,l),f.needsUpdate=!1}_=this.type,g.needsUpdate=!1,e.setRenderTarget(h,u,f)}}function i6(e,t,n){let i=n.isWebGL2,r=new function(){let t=!1,n=new G,i=null,r=new G(0,0,0,0);return{setMask:function(n){i===n||t||(e.colorMask(n,n,n,n),i=n)},setLocked:function(e){t=e},setClear:function(t,i,a,s,o){!0===o&&(t*=s,i*=s,a*=s),n.set(t,i,a,s),!1===r.equals(n)&&(e.clearColor(t,i,a,s),r.copy(n))},reset:function(){t=!1,i=null,r.set(-1,0,0,0)}}},a=new function(){let t=!1,n=null,i=null,r=null;return{setTest:function(t){t?k(e.DEPTH_TEST):W(e.DEPTH_TEST)},setMask:function(i){n===i||t||(e.depthMask(i),n=i)},setFunc:function(t){if(i!==t){switch(t){case 0:e.depthFunc(e.NEVER);break;case 1:e.depthFunc(e.ALWAYS);break;case 2:e.depthFunc(e.LESS);break;case 3:default:e.depthFunc(e.LEQUAL);break;case 4:e.depthFunc(e.EQUAL);break;case 5:e.depthFunc(e.GEQUAL);break;case 6:e.depthFunc(e.GREATER);break;case 7:e.depthFunc(e.NOTEQUAL)}i=t}},setLocked:function(e){t=e},setClear:function(t){r!==t&&(e.clearDepth(t),r=t)},reset:function(){t=!1,n=null,i=null,r=null}}},s=new function(){let t=!1,n=null,i=null,r=null,a=null,s=null,o=null,l=null,h=null;return{setTest:function(n){t||(n?k(e.STENCIL_TEST):W(e.STENCIL_TEST))},setMask:function(i){n===i||t||(e.stencilMask(i),n=i)},setFunc:function(t,n,s){(i!==t||r!==n||a!==s)&&(e.stencilFunc(t,n,s),i=t,r=n,a=s)},setOp:function(t,n,i){(s!==t||o!==n||l!==i)&&(e.stencilOp(t,n,i),s=t,o=n,l=i)},setLocked:function(e){t=e},setClear:function(t){h!==t&&(e.clearStencil(t),h=t)},reset:function(){t=!1,n=null,i=null,r=null,a=null,s=null,o=null,l=null,h=null}}},o=new WeakMap,l=new WeakMap,h={},c={},u=new WeakMap,d=[],p=null,f=!1,m=null,g=null,_=null,v=null,x=null,y=null,M=null,S=new tn(0,0,0),E=0,T=!1,b=null,A=null,w=null,R=null,C=null,L=e.getParameter(e.MAX_COMBINED_TEXTURE_IMAGE_UNITS),P=!1,U=0,N=e.getParameter(e.VERSION);-1!==N.indexOf("WebGL")?P=parseFloat(/^WebGL (\d)/.exec(N)[1])>=1:-1!==N.indexOf("OpenGL ES")&&(P=parseFloat(/^OpenGL ES (\d)/.exec(N)[1])>=2);let D=null,I={},O=e.getParameter(e.SCISSOR_BOX),F=e.getParameter(e.VIEWPORT),z=new G().fromArray(O),B=new G().fromArray(F);function H(t,n,r,a){let s=new Uint8Array(4),o=e.createTexture();e.bindTexture(t,o),e.texParameteri(t,e.TEXTURE_MIN_FILTER,e.NEAREST),e.texParameteri(t,e.TEXTURE_MAG_FILTER,e.NEAREST);for(let o=0;o<r;o++)i&&(t===e.TEXTURE_3D||t===e.TEXTURE_2D_ARRAY)?e.texImage3D(n,0,e.RGBA,1,1,a,0,e.RGBA,e.UNSIGNED_BYTE,s):e.texImage2D(n+o,0,e.RGBA,1,1,0,e.RGBA,e.UNSIGNED_BYTE,s);return o}let V={};function k(t){!0!==h[t]&&(e.enable(t),h[t]=!0)}function W(t){!1!==h[t]&&(e.disable(t),h[t]=!1)}V[e.TEXTURE_2D]=H(e.TEXTURE_2D,e.TEXTURE_2D,1),V[e.TEXTURE_CUBE_MAP]=H(e.TEXTURE_CUBE_MAP,e.TEXTURE_CUBE_MAP_POSITIVE_X,6),i&&(V[e.TEXTURE_2D_ARRAY]=H(e.TEXTURE_2D_ARRAY,e.TEXTURE_2D_ARRAY,1,1),V[e.TEXTURE_3D]=H(e.TEXTURE_3D,e.TEXTURE_3D,1,1)),r.setClear(0,0,0,1),a.setClear(1),s.setClear(0),k(e.DEPTH_TEST),a.setFunc(3),Y(!1),J(1),k(e.CULL_FACE),q(0);let X={100:e.FUNC_ADD,101:e.FUNC_SUBTRACT,102:e.FUNC_REVERSE_SUBTRACT};if(i)X[103]=e.MIN,X[104]=e.MAX;else{let e=t.get("EXT_blend_minmax");null!==e&&(X[103]=e.MIN_EXT,X[104]=e.MAX_EXT)}let j={200:e.ZERO,201:e.ONE,202:e.SRC_COLOR,204:e.SRC_ALPHA,210:e.SRC_ALPHA_SATURATE,208:e.DST_COLOR,206:e.DST_ALPHA,203:e.ONE_MINUS_SRC_COLOR,205:e.ONE_MINUS_SRC_ALPHA,209:e.ONE_MINUS_DST_COLOR,207:e.ONE_MINUS_DST_ALPHA,211:e.CONSTANT_COLOR,212:e.ONE_MINUS_CONSTANT_COLOR,213:e.CONSTANT_ALPHA,214:e.ONE_MINUS_CONSTANT_ALPHA};function q(t,n,i,r,a,s,o,l,h,c){if(0===t){!0===f&&(W(e.BLEND),f=!1);return}if(!1===f&&(k(e.BLEND),f=!0),5!==t){if(t!==m||c!==T){if((100!==g||100!==x)&&(e.blendEquation(e.FUNC_ADD),g=100,x=100),c)switch(t){case 1:e.blendFuncSeparate(e.ONE,e.ONE_MINUS_SRC_ALPHA,e.ONE,e.ONE_MINUS_SRC_ALPHA);break;case 2:e.blendFunc(e.ONE,e.ONE);break;case 3:e.blendFuncSeparate(e.ZERO,e.ONE_MINUS_SRC_COLOR,e.ZERO,e.ONE);break;case 4:e.blendFuncSeparate(e.ZERO,e.SRC_COLOR,e.ZERO,e.SRC_ALPHA);break;default:console.error("THREE.WebGLState: Invalid blending: ",t)}else switch(t){case 1:e.blendFuncSeparate(e.SRC_ALPHA,e.ONE_MINUS_SRC_ALPHA,e.ONE,e.ONE_MINUS_SRC_ALPHA);break;case 2:e.blendFunc(e.SRC_ALPHA,e.ONE);break;case 3:e.blendFuncSeparate(e.ZERO,e.ONE_MINUS_SRC_COLOR,e.ZERO,e.ONE);break;case 4:e.blendFunc(e.ZERO,e.SRC_COLOR);break;default:console.error("THREE.WebGLState: Invalid blending: ",t)}_=null,v=null,y=null,M=null,S.set(0,0,0),E=0,m=t,T=c}return}a=a||n,s=s||i,o=o||r,(n!==g||a!==x)&&(e.blendEquationSeparate(X[n],X[a]),g=n,x=a),(i!==_||r!==v||s!==y||o!==M)&&(e.blendFuncSeparate(j[i],j[r],j[s],j[o]),_=i,v=r,y=s,M=o),(!1===l.equals(S)||h!==E)&&(e.blendColor(l.r,l.g,l.b,h),S.copy(l),E=h),m=t,T=!1}function Y(t){b!==t&&(t?e.frontFace(e.CW):e.frontFace(e.CCW),b=t)}function J(t){0!==t?(k(e.CULL_FACE),t!==A&&(1===t?e.cullFace(e.BACK):2===t?e.cullFace(e.FRONT):e.cullFace(e.FRONT_AND_BACK))):W(e.CULL_FACE),A=t}function Z(t,n,i){t?(k(e.POLYGON_OFFSET_FILL),(R!==n||C!==i)&&(e.polygonOffset(n,i),R=n,C=i)):W(e.POLYGON_OFFSET_FILL)}return{buffers:{color:r,depth:a,stencil:s},enable:k,disable:W,bindFramebuffer:function(t,n){return c[t]!==n&&(e.bindFramebuffer(t,n),c[t]=n,i&&(t===e.DRAW_FRAMEBUFFER&&(c[e.FRAMEBUFFER]=n),t===e.FRAMEBUFFER&&(c[e.DRAW_FRAMEBUFFER]=n)),!0)},drawBuffers:function(i,r){let a=d,s=!1;if(i)if(void 0===(a=u.get(r))&&(a=[],u.set(r,a)),i.isWebGLMultipleRenderTargets){let t=i.texture;if(a.length!==t.length||a[0]!==e.COLOR_ATTACHMENT0){for(let n=0,i=t.length;n<i;n++)a[n]=e.COLOR_ATTACHMENT0+n;a.length=t.length,s=!0}}else a[0]!==e.COLOR_ATTACHMENT0&&(a[0]=e.COLOR_ATTACHMENT0,s=!0);else a[0]!==e.BACK&&(a[0]=e.BACK,s=!0);s&&(n.isWebGL2?e.drawBuffers(a):t.get("WEBGL_draw_buffers").drawBuffersWEBGL(a))},useProgram:function(t){return p!==t&&(e.useProgram(t),p=t,!0)},setBlending:q,setMaterial:function(t,n){2===t.side?W(e.CULL_FACE):k(e.CULL_FACE);let i=1===t.side;n&&(i=!i),Y(i),1===t.blending&&!1===t.transparent?q(0):q(t.blending,t.blendEquation,t.blendSrc,t.blendDst,t.blendEquationAlpha,t.blendSrcAlpha,t.blendDstAlpha,t.blendColor,t.blendAlpha,t.premultipliedAlpha),a.setFunc(t.depthFunc),a.setTest(t.depthTest),a.setMask(t.depthWrite),r.setMask(t.colorWrite);let o=t.stencilWrite;s.setTest(o),o&&(s.setMask(t.stencilWriteMask),s.setFunc(t.stencilFunc,t.stencilRef,t.stencilFuncMask),s.setOp(t.stencilFail,t.stencilZFail,t.stencilZPass)),Z(t.polygonOffset,t.polygonOffsetFactor,t.polygonOffsetUnits),!0===t.alphaToCoverage?k(e.SAMPLE_ALPHA_TO_COVERAGE):W(e.SAMPLE_ALPHA_TO_COVERAGE)},setFlipSided:Y,setCullFace:J,setLineWidth:function(t){t!==w&&(P&&e.lineWidth(t),w=t)},setPolygonOffset:Z,setScissorTest:function(t){t?k(e.SCISSOR_TEST):W(e.SCISSOR_TEST)},activeTexture:function(t){void 0===t&&(t=e.TEXTURE0+L-1),D!==t&&(e.activeTexture(t),D=t)},bindTexture:function(t,n,i){void 0===i&&(i=null===D?e.TEXTURE0+L-1:D);let r=I[i];void 0===r&&(r={type:void 0,texture:void 0},I[i]=r),(r.type!==t||r.texture!==n)&&(D!==i&&(e.activeTexture(i),D=i),e.bindTexture(t,n||V[t]),r.type=t,r.texture=n)},unbindTexture:function(){let t=I[D];void 0!==t&&void 0!==t.type&&(e.bindTexture(t.type,null),t.type=void 0,t.texture=void 0)},compressedTexImage2D:function(){try{e.compressedTexImage2D.apply(e,arguments)}catch(e){console.error("THREE.WebGLState:",e)}},compressedTexImage3D:function(){try{e.compressedTexImage3D.apply(e,arguments)}catch(e){console.error("THREE.WebGLState:",e)}},texImage2D:function(){try{e.texImage2D.apply(e,arguments)}catch(e){console.error("THREE.WebGLState:",e)}},texImage3D:function(){try{e.texImage3D.apply(e,arguments)}catch(e){console.error("THREE.WebGLState:",e)}},updateUBOMapping:function(t,n){let i=l.get(n);void 0===i&&(i=new WeakMap,l.set(n,i));let r=i.get(t);void 0===r&&(r=e.getUniformBlockIndex(n,t.name),i.set(t,r))},uniformBlockBinding:function(t,n){let i=l.get(n).get(t);o.get(n)!==i&&(e.uniformBlockBinding(n,i,t.__bindingPointIndex),o.set(n,i))},texStorage2D:function(){try{e.texStorage2D.apply(e,arguments)}catch(e){console.error("THREE.WebGLState:",e)}},texStorage3D:function(){try{e.texStorage3D.apply(e,arguments)}catch(e){console.error("THREE.WebGLState:",e)}},texSubImage2D:function(){try{e.texSubImage2D.apply(e,arguments)}catch(e){console.error("THREE.WebGLState:",e)}},texSubImage3D:function(){try{e.texSubImage3D.apply(e,arguments)}catch(e){console.error("THREE.WebGLState:",e)}},compressedTexSubImage2D:function(){try{e.compressedTexSubImage2D.apply(e,arguments)}catch(e){console.error("THREE.WebGLState:",e)}},compressedTexSubImage3D:function(){try{e.compressedTexSubImage3D.apply(e,arguments)}catch(e){console.error("THREE.WebGLState:",e)}},scissor:function(t){!1===z.equals(t)&&(e.scissor(t.x,t.y,t.z,t.w),z.copy(t))},viewport:function(t){!1===B.equals(t)&&(e.viewport(t.x,t.y,t.z,t.w),B.copy(t))},reset:function(){e.disable(e.BLEND),e.disable(e.CULL_FACE),e.disable(e.DEPTH_TEST),e.disable(e.POLYGON_OFFSET_FILL),e.disable(e.SCISSOR_TEST),e.disable(e.STENCIL_TEST),e.disable(e.SAMPLE_ALPHA_TO_COVERAGE),e.blendEquation(e.FUNC_ADD),e.blendFunc(e.ONE,e.ZERO),e.blendFuncSeparate(e.ONE,e.ZERO,e.ONE,e.ZERO),e.blendColor(0,0,0,0),e.colorMask(!0,!0,!0,!0),e.clearColor(0,0,0,0),e.depthMask(!0),e.depthFunc(e.LESS),e.clearDepth(1),e.stencilMask(0xffffffff),e.stencilFunc(e.ALWAYS,0,0xffffffff),e.stencilOp(e.KEEP,e.KEEP,e.KEEP),e.clearStencil(0),e.cullFace(e.BACK),e.frontFace(e.CCW),e.polygonOffset(0,0),e.activeTexture(e.TEXTURE0),e.bindFramebuffer(e.FRAMEBUFFER,null),!0===i&&(e.bindFramebuffer(e.DRAW_FRAMEBUFFER,null),e.bindFramebuffer(e.READ_FRAMEBUFFER,null)),e.useProgram(null),e.lineWidth(1),e.scissor(0,0,e.canvas.width,e.canvas.height),e.viewport(0,0,e.canvas.width,e.canvas.height),h={},D=null,I={},c={},u=new WeakMap,d=[],p=null,f=!1,m=null,g=null,_=null,v=null,x=null,y=null,M=null,S=new tn(0,0,0),E=0,T=!1,b=null,A=null,w=null,R=null,C=null,z.set(0,0,e.canvas.width,e.canvas.height),B.set(0,0,e.canvas.width,e.canvas.height),r.reset(),a.reset(),s.reset()}}}function i8(e,t,n,i,r,s,o){let c,u=r.isWebGL2,d=r.maxTextures,p=r.maxCubemapSize,f=r.maxTextureSize,m=r.maxSamples,g=t.has("WEBGL_multisampled_render_to_texture")?t.get("WEBGL_multisampled_render_to_texture"):null,_="undefined"!=typeof navigator&&/OculusBrowser/g.test(navigator.userAgent),y=new WeakMap,M=new WeakMap,S=!1;try{S="undefined"!=typeof OffscreenCanvas&&null!==new OffscreenCanvas(1,1).getContext("2d")}catch(e){}function E(e,t){return S?new OffscreenCanvas(e,t):A("canvas")}function T(e,t,n,i){let r=1;if((e.width>i||e.height>i)&&(r=i/Math.max(e.width,e.height)),r<1||!0===t)if("undefined"!=typeof HTMLImageElement&&e instanceof HTMLImageElement||"undefined"!=typeof HTMLCanvasElement&&e instanceof HTMLCanvasElement||"undefined"!=typeof ImageBitmap&&e instanceof ImageBitmap){let i=t?x:Math.floor,a=i(r*e.width),s=i(r*e.height);void 0===c&&(c=E(a,s));let o=n?E(a,s):c;return o.width=a,o.height=s,o.getContext("2d").drawImage(e,0,0,a,s),console.warn("THREE.WebGLRenderer: Texture has been resized from ("+e.width+"x"+e.height+") to ("+a+"x"+s+")."),o}else"data"in e&&console.warn("THREE.WebGLRenderer: Image in DataTexture is too big ("+e.width+"x"+e.height+").");return e}function b(e){return v(e.width)&&v(e.height)}function w(e,t){return e.generateMipmaps&&t&&1003!==e.minFilter&&1006!==e.minFilter}function R(t){e.generateMipmap(t)}function C(n,i,r,a){let s=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(!1===u)return i;if(null!==n){if(void 0!==e[n])return e[n];console.warn("THREE.WebGLRenderer: Attempt to use non-existing WebGL internal format '"+n+"'")}let o=i;if(i===e.RED&&(r===e.FLOAT&&(o=e.R32F),r===e.HALF_FLOAT&&(o=e.R16F),r===e.UNSIGNED_BYTE&&(o=e.R8)),i===e.RED_INTEGER&&(r===e.UNSIGNED_BYTE&&(o=e.R8UI),r===e.UNSIGNED_SHORT&&(o=e.R16UI),r===e.UNSIGNED_INT&&(o=e.R32UI),r===e.BYTE&&(o=e.R8I),r===e.SHORT&&(o=e.R16I),r===e.INT&&(o=e.R32I)),i===e.RG&&(r===e.FLOAT&&(o=e.RG32F),r===e.HALF_FLOAT&&(o=e.RG16F),r===e.UNSIGNED_BYTE&&(o=e.RG8)),i===e.RGBA){let t=s?l:N.getTransfer(a);r===e.FLOAT&&(o=e.RGBA32F),r===e.HALF_FLOAT&&(o=e.RGBA16F),r===e.UNSIGNED_BYTE&&(o=t===h?e.SRGB8_ALPHA8:e.RGBA8),r===e.UNSIGNED_SHORT_4_4_4_4&&(o=e.RGBA4),r===e.UNSIGNED_SHORT_5_5_5_1&&(o=e.RGB5_A1)}return(o===e.R16F||o===e.R32F||o===e.RG16F||o===e.RG32F||o===e.RGBA16F||o===e.RGBA32F)&&t.get("EXT_color_buffer_float"),o}function L(e,t,n){return!0===w(e,n)||e.isFramebufferTexture&&1003!==e.minFilter&&1006!==e.minFilter?Math.log2(Math.max(t.width,t.height))+1:void 0!==e.mipmaps&&e.mipmaps.length>0?e.mipmaps.length:e.isCompressedTexture&&Array.isArray(e.image)?t.mipmaps.length:1}function P(t){return 1003===t||1004===t||1005===t?e.NEAREST:e.LINEAR}function U(e){let t=e.target;t.removeEventListener("dispose",U),function(e){let t=i.get(e);if(void 0===t.__webglInit)return;let n=e.source,r=M.get(n);if(r){let i=r[t.__cacheKey];i.usedTimes--,0===i.usedTimes&&I(e),0===Object.keys(r).length&&M.delete(n)}i.remove(e)}(t),t.isVideoTexture&&y.delete(t)}function D(t){let n=t.target;n.removeEventListener("dispose",D),function(t){let n=t.texture,r=i.get(t),a=i.get(n);if(void 0!==a.__webglTexture&&(e.deleteTexture(a.__webglTexture),o.memory.textures--),t.depthTexture&&t.depthTexture.dispose(),t.isWebGLCubeRenderTarget)for(let t=0;t<6;t++){if(Array.isArray(r.__webglFramebuffer[t]))for(let n=0;n<r.__webglFramebuffer[t].length;n++)e.deleteFramebuffer(r.__webglFramebuffer[t][n]);else e.deleteFramebuffer(r.__webglFramebuffer[t]);r.__webglDepthbuffer&&e.deleteRenderbuffer(r.__webglDepthbuffer[t])}else{if(Array.isArray(r.__webglFramebuffer))for(let t=0;t<r.__webglFramebuffer.length;t++)e.deleteFramebuffer(r.__webglFramebuffer[t]);else e.deleteFramebuffer(r.__webglFramebuffer);if(r.__webglDepthbuffer&&e.deleteRenderbuffer(r.__webglDepthbuffer),r.__webglMultisampledFramebuffer&&e.deleteFramebuffer(r.__webglMultisampledFramebuffer),r.__webglColorRenderbuffer)for(let t=0;t<r.__webglColorRenderbuffer.length;t++)r.__webglColorRenderbuffer[t]&&e.deleteRenderbuffer(r.__webglColorRenderbuffer[t]);r.__webglDepthRenderbuffer&&e.deleteRenderbuffer(r.__webglDepthRenderbuffer)}if(t.isWebGLMultipleRenderTargets)for(let t=0,r=n.length;t<r;t++){let r=i.get(n[t]);r.__webglTexture&&(e.deleteTexture(r.__webglTexture),o.memory.textures--),i.remove(n[t])}i.remove(n),i.remove(t)}(n)}function I(t){let n=i.get(t);e.deleteTexture(n.__webglTexture);let r=t.source,a=M.get(r);delete a[n.__cacheKey],o.memory.textures--}let F=0;function z(t,r){let a=i.get(t);if(t.isVideoTexture&&function(e){let t=o.render.frame;y.get(e)!==t&&(y.set(e,t),e.update())}(t),!1===t.isRenderTargetTexture&&t.version>0&&a.__version!==t.version){let e=t.image;if(null===e)console.warn("THREE.WebGLRenderer: Texture marked for update but no image data found.");else{if(!1!==e.complete)return void W(a,t,r);console.warn("THREE.WebGLRenderer: Texture marked for update but image is incomplete")}}n.bindTexture(e.TEXTURE_2D,a.__webglTexture,e.TEXTURE0+r)}let B={1e3:e.REPEAT,1001:e.CLAMP_TO_EDGE,1002:e.MIRRORED_REPEAT},H={1003:e.NEAREST,1004:e.NEAREST_MIPMAP_NEAREST,1005:e.NEAREST_MIPMAP_LINEAR,1006:e.LINEAR,1007:e.LINEAR_MIPMAP_NEAREST,1008:e.LINEAR_MIPMAP_LINEAR},V={512:e.NEVER,519:e.ALWAYS,513:e.LESS,515:e.LEQUAL,514:e.EQUAL,518:e.GEQUAL,516:e.GREATER,517:e.NOTEQUAL};function G(n,a,s){if(s?(e.texParameteri(n,e.TEXTURE_WRAP_S,B[a.wrapS]),e.texParameteri(n,e.TEXTURE_WRAP_T,B[a.wrapT]),(n===e.TEXTURE_3D||n===e.TEXTURE_2D_ARRAY)&&e.texParameteri(n,e.TEXTURE_WRAP_R,B[a.wrapR]),e.texParameteri(n,e.TEXTURE_MAG_FILTER,H[a.magFilter]),e.texParameteri(n,e.TEXTURE_MIN_FILTER,H[a.minFilter])):(e.texParameteri(n,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE),e.texParameteri(n,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE),(n===e.TEXTURE_3D||n===e.TEXTURE_2D_ARRAY)&&e.texParameteri(n,e.TEXTURE_WRAP_R,e.CLAMP_TO_EDGE),(1001!==a.wrapS||1001!==a.wrapT)&&console.warn("THREE.WebGLRenderer: Texture is not power of two. Texture.wrapS and Texture.wrapT should be set to THREE.ClampToEdgeWrapping."),e.texParameteri(n,e.TEXTURE_MAG_FILTER,P(a.magFilter)),e.texParameteri(n,e.TEXTURE_MIN_FILTER,P(a.minFilter)),1003!==a.minFilter&&1006!==a.minFilter&&console.warn("THREE.WebGLRenderer: Texture is not power of two. Texture.minFilter should be set to THREE.NearestFilter or THREE.LinearFilter.")),a.compareFunction&&(e.texParameteri(n,e.TEXTURE_COMPARE_MODE,e.COMPARE_REF_TO_TEXTURE),e.texParameteri(n,e.TEXTURE_COMPARE_FUNC,V[a.compareFunction])),!0===t.has("EXT_texture_filter_anisotropic")){let s=t.get("EXT_texture_filter_anisotropic");1003!==a.magFilter&&(1005===a.minFilter||1008===a.minFilter)&&(1015!==a.type||!1!==t.has("OES_texture_float_linear"))&&(!1!==u||1016!==a.type||!1!==t.has("OES_texture_half_float_linear"))&&(a.anisotropy>1||i.get(a).__currentAnisotropy)&&(e.texParameterf(n,s.TEXTURE_MAX_ANISOTROPY_EXT,Math.min(a.anisotropy,r.getMaxAnisotropy())),i.get(a).__currentAnisotropy=a.anisotropy)}}function k(t,n){let i=!1;void 0===t.__webglInit&&(t.__webglInit=!0,n.addEventListener("dispose",U));let r=n.source,a=M.get(r);void 0===a&&(a={},M.set(r,a));let s=function(e){let t=[];return t.push(e.wrapS),t.push(e.wrapT),t.push(e.wrapR||0),t.push(e.magFilter),t.push(e.minFilter),t.push(e.anisotropy),t.push(e.internalFormat),t.push(e.format),t.push(e.type),t.push(e.generateMipmaps),t.push(e.premultiplyAlpha),t.push(e.flipY),t.push(e.unpackAlignment),t.push(e.colorSpace),t.join()}(n);if(s!==t.__cacheKey){void 0===a[s]&&(a[s]={texture:e.createTexture(),usedTimes:0},o.memory.textures++,i=!0),a[s].usedTimes++;let r=a[t.__cacheKey];void 0!==r&&(a[t.__cacheKey].usedTimes--,0===r.usedTimes&&I(n)),t.__cacheKey=s,t.__webglTexture=a[s].texture}return i}function W(t,r,a){let o=e.TEXTURE_2D;(r.isDataArrayTexture||r.isCompressedArrayTexture)&&(o=e.TEXTURE_2D_ARRAY),r.isData3DTexture&&(o=e.TEXTURE_3D);let l=k(t,r),h=r.source;n.bindTexture(o,t.__webglTexture,e.TEXTURE0+a);let c=i.get(h);if(h.version!==c.__version||!0===l){let t;n.activeTexture(e.TEXTURE0+a);let i=N.getPrimaries(N.workingColorSpace),d=""===r.colorSpace?null:N.getPrimaries(r.colorSpace),p=""===r.colorSpace||i===d?e.NONE:e.BROWSER_DEFAULT_WEBGL;e.pixelStorei(e.UNPACK_FLIP_Y_WEBGL,r.flipY),e.pixelStorei(e.UNPACK_PREMULTIPLY_ALPHA_WEBGL,r.premultiplyAlpha),e.pixelStorei(e.UNPACK_ALIGNMENT,r.unpackAlignment),e.pixelStorei(e.UNPACK_COLORSPACE_CONVERSION_WEBGL,p);let m=!u&&(1001!==r.wrapS||1001!==r.wrapT||1003!==r.minFilter&&1006!==r.minFilter)&&!1===b(r.image),g=T(r.image,m,!1,f),_=b(g=Z(r,g))||u,v=s.convert(r.format,r.colorSpace),x=s.convert(r.type),y=C(r.internalFormat,v,x,r.colorSpace,r.isVideoTexture);G(o,r,_);let M=r.mipmaps,S=u&&!0!==r.isVideoTexture,E=void 0===c.__version||!0===l,A=L(r,g,_);if(r.isDepthTexture)y=e.DEPTH_COMPONENT,u?y=1015===r.type?e.DEPTH_COMPONENT32F:1014===r.type?e.DEPTH_COMPONENT24:1020===r.type?e.DEPTH24_STENCIL8:e.DEPTH_COMPONENT16:1015===r.type&&console.error("WebGLRenderer: Floating point depth texture requires WebGL2."),1026===r.format&&y===e.DEPTH_COMPONENT&&1012!==r.type&&1014!==r.type&&(console.warn("THREE.WebGLRenderer: Use UnsignedShortType or UnsignedIntType for DepthFormat DepthTexture."),r.type=1014,x=s.convert(r.type)),1027===r.format&&y===e.DEPTH_COMPONENT&&(y=e.DEPTH_STENCIL,1020!==r.type&&(console.warn("THREE.WebGLRenderer: Use UnsignedInt248Type for DepthStencilFormat DepthTexture."),r.type=1020,x=s.convert(r.type))),E&&(S?n.texStorage2D(e.TEXTURE_2D,1,y,g.width,g.height):n.texImage2D(e.TEXTURE_2D,0,y,g.width,g.height,0,v,x,null));else if(r.isDataTexture)if(M.length>0&&_){S&&E&&n.texStorage2D(e.TEXTURE_2D,A,y,M[0].width,M[0].height);for(let i=0,r=M.length;i<r;i++)t=M[i],S?n.texSubImage2D(e.TEXTURE_2D,i,0,0,t.width,t.height,v,x,t.data):n.texImage2D(e.TEXTURE_2D,i,y,t.width,t.height,0,v,x,t.data);r.generateMipmaps=!1}else S?(E&&n.texStorage2D(e.TEXTURE_2D,A,y,g.width,g.height),n.texSubImage2D(e.TEXTURE_2D,0,0,0,g.width,g.height,v,x,g.data)):n.texImage2D(e.TEXTURE_2D,0,y,g.width,g.height,0,v,x,g.data);else if(r.isCompressedTexture)if(r.isCompressedArrayTexture){S&&E&&n.texStorage3D(e.TEXTURE_2D_ARRAY,A,y,M[0].width,M[0].height,g.depth);for(let i=0,a=M.length;i<a;i++)t=M[i],1023!==r.format?null!==v?S?n.compressedTexSubImage3D(e.TEXTURE_2D_ARRAY,i,0,0,0,t.width,t.height,g.depth,v,t.data,0,0):n.compressedTexImage3D(e.TEXTURE_2D_ARRAY,i,y,t.width,t.height,g.depth,0,t.data,0,0):console.warn("THREE.WebGLRenderer: Attempt to load unsupported compressed texture format in .uploadTexture()"):S?n.texSubImage3D(e.TEXTURE_2D_ARRAY,i,0,0,0,t.width,t.height,g.depth,v,x,t.data):n.texImage3D(e.TEXTURE_2D_ARRAY,i,y,t.width,t.height,g.depth,0,v,x,t.data)}else{S&&E&&n.texStorage2D(e.TEXTURE_2D,A,y,M[0].width,M[0].height);for(let i=0,a=M.length;i<a;i++)t=M[i],1023!==r.format?null!==v?S?n.compressedTexSubImage2D(e.TEXTURE_2D,i,0,0,t.width,t.height,v,t.data):n.compressedTexImage2D(e.TEXTURE_2D,i,y,t.width,t.height,0,t.data):console.warn("THREE.WebGLRenderer: Attempt to load unsupported compressed texture format in .uploadTexture()"):S?n.texSubImage2D(e.TEXTURE_2D,i,0,0,t.width,t.height,v,x,t.data):n.texImage2D(e.TEXTURE_2D,i,y,t.width,t.height,0,v,x,t.data)}else if(r.isDataArrayTexture)S?(E&&n.texStorage3D(e.TEXTURE_2D_ARRAY,A,y,g.width,g.height,g.depth),n.texSubImage3D(e.TEXTURE_2D_ARRAY,0,0,0,0,g.width,g.height,g.depth,v,x,g.data)):n.texImage3D(e.TEXTURE_2D_ARRAY,0,y,g.width,g.height,g.depth,0,v,x,g.data);else if(r.isData3DTexture)S?(E&&n.texStorage3D(e.TEXTURE_3D,A,y,g.width,g.height,g.depth),n.texSubImage3D(e.TEXTURE_3D,0,0,0,0,g.width,g.height,g.depth,v,x,g.data)):n.texImage3D(e.TEXTURE_3D,0,y,g.width,g.height,g.depth,0,v,x,g.data);else if(r.isFramebufferTexture){if(E)if(S)n.texStorage2D(e.TEXTURE_2D,A,y,g.width,g.height);else{let t=g.width,i=g.height;for(let r=0;r<A;r++)n.texImage2D(e.TEXTURE_2D,r,y,t,i,0,v,x,null),t>>=1,i>>=1}}else if(M.length>0&&_){S&&E&&n.texStorage2D(e.TEXTURE_2D,A,y,M[0].width,M[0].height);for(let i=0,r=M.length;i<r;i++)t=M[i],S?n.texSubImage2D(e.TEXTURE_2D,i,0,0,v,x,t):n.texImage2D(e.TEXTURE_2D,i,y,v,x,t);r.generateMipmaps=!1}else S?(E&&n.texStorage2D(e.TEXTURE_2D,A,y,g.width,g.height),n.texSubImage2D(e.TEXTURE_2D,0,0,0,v,x,g)):n.texImage2D(e.TEXTURE_2D,0,y,v,x,g);w(r,_)&&R(o),c.__version=h.version,r.onUpdate&&r.onUpdate(r)}t.__version=r.version}function X(t,r,a,o,l,h){let c=s.convert(a.format,a.colorSpace),u=s.convert(a.type),d=C(a.internalFormat,c,u,a.colorSpace);if(!i.get(r).__hasExternalTextures){let t=Math.max(1,r.width>>h),i=Math.max(1,r.height>>h);l===e.TEXTURE_3D||l===e.TEXTURE_2D_ARRAY?n.texImage3D(l,h,d,t,i,r.depth,0,c,u,null):n.texImage2D(l,h,d,t,i,0,c,u,null)}n.bindFramebuffer(e.FRAMEBUFFER,t),J(r)?g.framebufferTexture2DMultisampleEXT(e.FRAMEBUFFER,o,l,i.get(a).__webglTexture,0,Y(r)):(l===e.TEXTURE_2D||l>=e.TEXTURE_CUBE_MAP_POSITIVE_X&&l<=e.TEXTURE_CUBE_MAP_NEGATIVE_Z)&&e.framebufferTexture2D(e.FRAMEBUFFER,o,l,i.get(a).__webglTexture,h),n.bindFramebuffer(e.FRAMEBUFFER,null)}function j(t,n,i){if(e.bindRenderbuffer(e.RENDERBUFFER,t),n.depthBuffer&&!n.stencilBuffer){let r=!0===u?e.DEPTH_COMPONENT24:e.DEPTH_COMPONENT16;if(i||J(n)){let t=n.depthTexture;t&&t.isDepthTexture&&(1015===t.type?r=e.DEPTH_COMPONENT32F:1014===t.type&&(r=e.DEPTH_COMPONENT24));let i=Y(n);J(n)?g.renderbufferStorageMultisampleEXT(e.RENDERBUFFER,i,r,n.width,n.height):e.renderbufferStorageMultisample(e.RENDERBUFFER,i,r,n.width,n.height)}else e.renderbufferStorage(e.RENDERBUFFER,r,n.width,n.height);e.framebufferRenderbuffer(e.FRAMEBUFFER,e.DEPTH_ATTACHMENT,e.RENDERBUFFER,t)}else if(n.depthBuffer&&n.stencilBuffer){let r=Y(n);i&&!1===J(n)?e.renderbufferStorageMultisample(e.RENDERBUFFER,r,e.DEPTH24_STENCIL8,n.width,n.height):J(n)?g.renderbufferStorageMultisampleEXT(e.RENDERBUFFER,r,e.DEPTH24_STENCIL8,n.width,n.height):e.renderbufferStorage(e.RENDERBUFFER,e.DEPTH_STENCIL,n.width,n.height),e.framebufferRenderbuffer(e.FRAMEBUFFER,e.DEPTH_STENCIL_ATTACHMENT,e.RENDERBUFFER,t)}else{let t=!0===n.isWebGLMultipleRenderTargets?n.texture:[n.texture];for(let r=0;r<t.length;r++){let a=t[r],o=s.convert(a.format,a.colorSpace),l=s.convert(a.type),h=C(a.internalFormat,o,l,a.colorSpace),c=Y(n);i&&!1===J(n)?e.renderbufferStorageMultisample(e.RENDERBUFFER,c,h,n.width,n.height):J(n)?g.renderbufferStorageMultisampleEXT(e.RENDERBUFFER,c,h,n.width,n.height):e.renderbufferStorage(e.RENDERBUFFER,h,n.width,n.height)}}e.bindRenderbuffer(e.RENDERBUFFER,null)}function q(t){let r=i.get(t),a=!0===t.isWebGLCubeRenderTarget;if(t.depthTexture&&!r.__autoAllocateDepthBuffer){if(a)throw Error("target.depthTexture not supported in Cube render targets");var s=r.__webglFramebuffer;if(t&&t.isWebGLCubeRenderTarget)throw Error("Depth Texture with cube render targets is not supported");if(n.bindFramebuffer(e.FRAMEBUFFER,s),!(t.depthTexture&&t.depthTexture.isDepthTexture))throw Error("renderTarget.depthTexture must be an instance of THREE.DepthTexture");i.get(t.depthTexture).__webglTexture&&t.depthTexture.image.width===t.width&&t.depthTexture.image.height===t.height||(t.depthTexture.image.width=t.width,t.depthTexture.image.height=t.height,t.depthTexture.needsUpdate=!0),z(t.depthTexture,0);let o=i.get(t.depthTexture).__webglTexture,l=Y(t);if(1026===t.depthTexture.format)J(t)?g.framebufferTexture2DMultisampleEXT(e.FRAMEBUFFER,e.DEPTH_ATTACHMENT,e.TEXTURE_2D,o,0,l):e.framebufferTexture2D(e.FRAMEBUFFER,e.DEPTH_ATTACHMENT,e.TEXTURE_2D,o,0);else if(1027===t.depthTexture.format)J(t)?g.framebufferTexture2DMultisampleEXT(e.FRAMEBUFFER,e.DEPTH_STENCIL_ATTACHMENT,e.TEXTURE_2D,o,0,l):e.framebufferTexture2D(e.FRAMEBUFFER,e.DEPTH_STENCIL_ATTACHMENT,e.TEXTURE_2D,o,0);else throw Error("Unknown depthTexture format")}else if(a){r.__webglDepthbuffer=[];for(let i=0;i<6;i++)n.bindFramebuffer(e.FRAMEBUFFER,r.__webglFramebuffer[i]),r.__webglDepthbuffer[i]=e.createRenderbuffer(),j(r.__webglDepthbuffer[i],t,!1)}else n.bindFramebuffer(e.FRAMEBUFFER,r.__webglFramebuffer),r.__webglDepthbuffer=e.createRenderbuffer(),j(r.__webglDepthbuffer,t,!1);n.bindFramebuffer(e.FRAMEBUFFER,null)}function Y(e){return Math.min(m,e.samples)}function J(e){let n=i.get(e);return u&&e.samples>0&&!0===t.has("WEBGL_multisampled_render_to_texture")&&!1!==n.__useRenderToTexture}function Z(e,n){let i=e.colorSpace,r=e.format,s=e.type;return!0===e.isCompressedTexture||!0===e.isVideoTexture||1035===e.format||i!==a&&""!==i&&(N.getTransfer(i)===h?!1===u?!0===t.has("EXT_sRGB")&&1023===r?(e.format=1035,e.minFilter=1006,e.generateMipmaps=!1):n=O.sRGBToLinear(n):(1023!==r||1009!==s)&&console.warn("THREE.WebGLTextures: sRGB encoded textures have to use RGBAFormat and UnsignedByteType."):console.error("THREE.WebGLTextures: Unsupported texture color space:",i)),n}this.allocateTextureUnit=function(){let e=F;return e>=d&&console.warn("THREE.WebGLTextures: Trying to use "+e+" texture units while this GPU supports only "+d),F+=1,e},this.resetTextureUnits=function(){F=0},this.setTexture2D=z,this.setTexture2DArray=function(t,r){let a=i.get(t);if(t.version>0&&a.__version!==t.version)return void W(a,t,r);n.bindTexture(e.TEXTURE_2D_ARRAY,a.__webglTexture,e.TEXTURE0+r)},this.setTexture3D=function(t,r){let a=i.get(t);if(t.version>0&&a.__version!==t.version)return void W(a,t,r);n.bindTexture(e.TEXTURE_3D,a.__webglTexture,e.TEXTURE0+r)},this.setTextureCube=function(t,r){let a=i.get(t);if(t.version>0&&a.__version!==t.version)return void function(t,r,a){if(6!==r.image.length)return;let o=k(t,r),l=r.source;n.bindTexture(e.TEXTURE_CUBE_MAP,t.__webglTexture,e.TEXTURE0+a);let h=i.get(l);if(l.version!==h.__version||!0===o){let t;n.activeTexture(e.TEXTURE0+a);let i=N.getPrimaries(N.workingColorSpace),c=""===r.colorSpace?null:N.getPrimaries(r.colorSpace),d=""===r.colorSpace||i===c?e.NONE:e.BROWSER_DEFAULT_WEBGL;e.pixelStorei(e.UNPACK_FLIP_Y_WEBGL,r.flipY),e.pixelStorei(e.UNPACK_PREMULTIPLY_ALPHA_WEBGL,r.premultiplyAlpha),e.pixelStorei(e.UNPACK_ALIGNMENT,r.unpackAlignment),e.pixelStorei(e.UNPACK_COLORSPACE_CONVERSION_WEBGL,d);let f=r.isCompressedTexture||r.image[0].isCompressedTexture,m=r.image[0]&&r.image[0].isDataTexture,g=[];for(let e=0;e<6;e++)f||m?g[e]=m?r.image[e].image:r.image[e]:g[e]=T(r.image[e],!1,!0,p),g[e]=Z(r,g[e]);let _=g[0],v=b(_)||u,x=s.convert(r.format,r.colorSpace),y=s.convert(r.type),M=C(r.internalFormat,x,y,r.colorSpace),S=u&&!0!==r.isVideoTexture,E=void 0===h.__version||!0===o,A=L(r,_,v);if(G(e.TEXTURE_CUBE_MAP,r,v),f){S&&E&&n.texStorage2D(e.TEXTURE_CUBE_MAP,A,M,_.width,_.height);for(let i=0;i<6;i++){t=g[i].mipmaps;for(let a=0;a<t.length;a++){let s=t[a];1023!==r.format?null!==x?S?n.compressedTexSubImage2D(e.TEXTURE_CUBE_MAP_POSITIVE_X+i,a,0,0,s.width,s.height,x,s.data):n.compressedTexImage2D(e.TEXTURE_CUBE_MAP_POSITIVE_X+i,a,M,s.width,s.height,0,s.data):console.warn("THREE.WebGLRenderer: Attempt to load unsupported compressed texture format in .setTextureCube()"):S?n.texSubImage2D(e.TEXTURE_CUBE_MAP_POSITIVE_X+i,a,0,0,s.width,s.height,x,y,s.data):n.texImage2D(e.TEXTURE_CUBE_MAP_POSITIVE_X+i,a,M,s.width,s.height,0,x,y,s.data)}}}else{t=r.mipmaps,S&&E&&(t.length>0&&A++,n.texStorage2D(e.TEXTURE_CUBE_MAP,A,M,g[0].width,g[0].height));for(let i=0;i<6;i++)if(m){S?n.texSubImage2D(e.TEXTURE_CUBE_MAP_POSITIVE_X+i,0,0,0,g[i].width,g[i].height,x,y,g[i].data):n.texImage2D(e.TEXTURE_CUBE_MAP_POSITIVE_X+i,0,M,g[i].width,g[i].height,0,x,y,g[i].data);for(let r=0;r<t.length;r++){let a=t[r].image[i].image;S?n.texSubImage2D(e.TEXTURE_CUBE_MAP_POSITIVE_X+i,r+1,0,0,a.width,a.height,x,y,a.data):n.texImage2D(e.TEXTURE_CUBE_MAP_POSITIVE_X+i,r+1,M,a.width,a.height,0,x,y,a.data)}}else{S?n.texSubImage2D(e.TEXTURE_CUBE_MAP_POSITIVE_X+i,0,0,0,x,y,g[i]):n.texImage2D(e.TEXTURE_CUBE_MAP_POSITIVE_X+i,0,M,x,y,g[i]);for(let r=0;r<t.length;r++){let a=t[r];S?n.texSubImage2D(e.TEXTURE_CUBE_MAP_POSITIVE_X+i,r+1,0,0,x,y,a.image[i]):n.texImage2D(e.TEXTURE_CUBE_MAP_POSITIVE_X+i,r+1,M,x,y,a.image[i])}}}w(r,v)&&R(e.TEXTURE_CUBE_MAP),h.__version=l.version,r.onUpdate&&r.onUpdate(r)}t.__version=r.version}(a,t,r);n.bindTexture(e.TEXTURE_CUBE_MAP,a.__webglTexture,e.TEXTURE0+r)},this.rebindTextures=function(t,n,r){let a=i.get(t);void 0!==n&&X(a.__webglFramebuffer,t,t.texture,e.COLOR_ATTACHMENT0,e.TEXTURE_2D,0),void 0!==r&&q(t)},this.setupRenderTarget=function(t){let a=t.texture,l=i.get(t),h=i.get(a);t.addEventListener("dispose",D),!0!==t.isWebGLMultipleRenderTargets&&(void 0===h.__webglTexture&&(h.__webglTexture=e.createTexture()),h.__version=a.version,o.memory.textures++);let c=!0===t.isWebGLCubeRenderTarget,d=!0===t.isWebGLMultipleRenderTargets,p=b(t)||u;if(c){l.__webglFramebuffer=[];for(let t=0;t<6;t++)if(u&&a.mipmaps&&a.mipmaps.length>0){l.__webglFramebuffer[t]=[];for(let n=0;n<a.mipmaps.length;n++)l.__webglFramebuffer[t][n]=e.createFramebuffer()}else l.__webglFramebuffer[t]=e.createFramebuffer()}else{if(u&&a.mipmaps&&a.mipmaps.length>0){l.__webglFramebuffer=[];for(let t=0;t<a.mipmaps.length;t++)l.__webglFramebuffer[t]=e.createFramebuffer()}else l.__webglFramebuffer=e.createFramebuffer();if(d)if(r.drawBuffers){let n=t.texture;for(let t=0,r=n.length;t<r;t++){let r=i.get(n[t]);void 0===r.__webglTexture&&(r.__webglTexture=e.createTexture(),o.memory.textures++)}}else console.warn("THREE.WebGLRenderer: WebGLMultipleRenderTargets can only be used with WebGL2 or WEBGL_draw_buffers extension.");if(u&&t.samples>0&&!1===J(t)){let i=d?a:[a];l.__webglMultisampledFramebuffer=e.createFramebuffer(),l.__webglColorRenderbuffer=[],n.bindFramebuffer(e.FRAMEBUFFER,l.__webglMultisampledFramebuffer);for(let n=0;n<i.length;n++){let r=i[n];l.__webglColorRenderbuffer[n]=e.createRenderbuffer(),e.bindRenderbuffer(e.RENDERBUFFER,l.__webglColorRenderbuffer[n]);let a=s.convert(r.format,r.colorSpace),o=s.convert(r.type),h=C(r.internalFormat,a,o,r.colorSpace,!0===t.isXRRenderTarget),c=Y(t);e.renderbufferStorageMultisample(e.RENDERBUFFER,c,h,t.width,t.height),e.framebufferRenderbuffer(e.FRAMEBUFFER,e.COLOR_ATTACHMENT0+n,e.RENDERBUFFER,l.__webglColorRenderbuffer[n])}e.bindRenderbuffer(e.RENDERBUFFER,null),t.depthBuffer&&(l.__webglDepthRenderbuffer=e.createRenderbuffer(),j(l.__webglDepthRenderbuffer,t,!0)),n.bindFramebuffer(e.FRAMEBUFFER,null)}}if(c){n.bindTexture(e.TEXTURE_CUBE_MAP,h.__webglTexture),G(e.TEXTURE_CUBE_MAP,a,p);for(let n=0;n<6;n++)if(u&&a.mipmaps&&a.mipmaps.length>0)for(let i=0;i<a.mipmaps.length;i++)X(l.__webglFramebuffer[n][i],t,a,e.COLOR_ATTACHMENT0,e.TEXTURE_CUBE_MAP_POSITIVE_X+n,i);else X(l.__webglFramebuffer[n],t,a,e.COLOR_ATTACHMENT0,e.TEXTURE_CUBE_MAP_POSITIVE_X+n,0);w(a,p)&&R(e.TEXTURE_CUBE_MAP),n.unbindTexture()}else if(d){let r=t.texture;for(let a=0,s=r.length;a<s;a++){let s=r[a],o=i.get(s);n.bindTexture(e.TEXTURE_2D,o.__webglTexture),G(e.TEXTURE_2D,s,p),X(l.__webglFramebuffer,t,s,e.COLOR_ATTACHMENT0+a,e.TEXTURE_2D,0),w(s,p)&&R(e.TEXTURE_2D)}n.unbindTexture()}else{let i=e.TEXTURE_2D;if((t.isWebGL3DRenderTarget||t.isWebGLArrayRenderTarget)&&(u?i=t.isWebGL3DRenderTarget?e.TEXTURE_3D:e.TEXTURE_2D_ARRAY:console.error("THREE.WebGLTextures: THREE.Data3DTexture and THREE.DataArrayTexture only supported with WebGL2.")),n.bindTexture(i,h.__webglTexture),G(i,a,p),u&&a.mipmaps&&a.mipmaps.length>0)for(let n=0;n<a.mipmaps.length;n++)X(l.__webglFramebuffer[n],t,a,e.COLOR_ATTACHMENT0,i,n);else X(l.__webglFramebuffer,t,a,e.COLOR_ATTACHMENT0,i,0);w(a,p)&&R(i),n.unbindTexture()}t.depthBuffer&&q(t)},this.updateRenderTargetMipmap=function(t){let r=b(t)||u,a=!0===t.isWebGLMultipleRenderTargets?t.texture:[t.texture];for(let s=0,o=a.length;s<o;s++){let o=a[s];if(w(o,r)){let r=t.isWebGLCubeRenderTarget?e.TEXTURE_CUBE_MAP:e.TEXTURE_2D,a=i.get(o).__webglTexture;n.bindTexture(r,a),R(r),n.unbindTexture()}}},this.updateMultisampleRenderTarget=function(t){if(u&&t.samples>0&&!1===J(t)){let r=t.isWebGLMultipleRenderTargets?t.texture:[t.texture],a=t.width,s=t.height,o=e.COLOR_BUFFER_BIT,l=[],h=t.stencilBuffer?e.DEPTH_STENCIL_ATTACHMENT:e.DEPTH_ATTACHMENT,c=i.get(t),u=!0===t.isWebGLMultipleRenderTargets;if(u)for(let t=0;t<r.length;t++)n.bindFramebuffer(e.FRAMEBUFFER,c.__webglMultisampledFramebuffer),e.framebufferRenderbuffer(e.FRAMEBUFFER,e.COLOR_ATTACHMENT0+t,e.RENDERBUFFER,null),n.bindFramebuffer(e.FRAMEBUFFER,c.__webglFramebuffer),e.framebufferTexture2D(e.DRAW_FRAMEBUFFER,e.COLOR_ATTACHMENT0+t,e.TEXTURE_2D,null,0);n.bindFramebuffer(e.READ_FRAMEBUFFER,c.__webglMultisampledFramebuffer),n.bindFramebuffer(e.DRAW_FRAMEBUFFER,c.__webglFramebuffer);for(let n=0;n<r.length;n++){l.push(e.COLOR_ATTACHMENT0+n),t.depthBuffer&&l.push(h);let d=void 0!==c.__ignoreDepthValues&&c.__ignoreDepthValues;if(!1===d&&(t.depthBuffer&&(o|=e.DEPTH_BUFFER_BIT),t.stencilBuffer&&(o|=e.STENCIL_BUFFER_BIT)),u&&e.framebufferRenderbuffer(e.READ_FRAMEBUFFER,e.COLOR_ATTACHMENT0,e.RENDERBUFFER,c.__webglColorRenderbuffer[n]),!0===d&&(e.invalidateFramebuffer(e.READ_FRAMEBUFFER,[h]),e.invalidateFramebuffer(e.DRAW_FRAMEBUFFER,[h])),u){let t=i.get(r[n]).__webglTexture;e.framebufferTexture2D(e.DRAW_FRAMEBUFFER,e.COLOR_ATTACHMENT0,e.TEXTURE_2D,t,0)}e.blitFramebuffer(0,0,a,s,0,0,a,s,o,e.NEAREST),_&&e.invalidateFramebuffer(e.READ_FRAMEBUFFER,l)}if(n.bindFramebuffer(e.READ_FRAMEBUFFER,null),n.bindFramebuffer(e.DRAW_FRAMEBUFFER,null),u)for(let t=0;t<r.length;t++){n.bindFramebuffer(e.FRAMEBUFFER,c.__webglMultisampledFramebuffer),e.framebufferRenderbuffer(e.FRAMEBUFFER,e.COLOR_ATTACHMENT0+t,e.RENDERBUFFER,c.__webglColorRenderbuffer[t]);let a=i.get(r[t]).__webglTexture;n.bindFramebuffer(e.FRAMEBUFFER,c.__webglFramebuffer),e.framebufferTexture2D(e.DRAW_FRAMEBUFFER,e.COLOR_ATTACHMENT0+t,e.TEXTURE_2D,a,0)}n.bindFramebuffer(e.DRAW_FRAMEBUFFER,c.__webglMultisampledFramebuffer)}},this.setupDepthRenderbuffer=q,this.setupFrameBufferTexture=X,this.useMultisampledRTT=J}function i9(e,t,n){let i=n.isWebGL2;return{convert:function(n){let r,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",s=N.getTransfer(a);if(1009===n)return e.UNSIGNED_BYTE;if(1017===n)return e.UNSIGNED_SHORT_4_4_4_4;if(1018===n)return e.UNSIGNED_SHORT_5_5_5_1;if(1010===n)return e.BYTE;if(1011===n)return e.SHORT;if(1012===n)return e.UNSIGNED_SHORT;if(1013===n)return e.INT;if(1014===n)return e.UNSIGNED_INT;if(1015===n)return e.FLOAT;if(1016===n)return i?e.HALF_FLOAT:null!==(r=t.get("OES_texture_half_float"))?r.HALF_FLOAT_OES:null;if(1021===n)return e.ALPHA;if(1023===n)return e.RGBA;if(1024===n)return e.LUMINANCE;if(1025===n)return e.LUMINANCE_ALPHA;if(1026===n)return e.DEPTH_COMPONENT;if(1027===n)return e.DEPTH_STENCIL;if(1035===n)return null!==(r=t.get("EXT_sRGB"))?r.SRGB_ALPHA_EXT:null;if(1028===n)return e.RED;if(1029===n)return e.RED_INTEGER;if(1030===n)return e.RG;if(1031===n)return e.RG_INTEGER;if(1033===n)return e.RGBA_INTEGER;if(33776===n||33777===n||33778===n||33779===n)if(s===h){if(null===(r=t.get("WEBGL_compressed_texture_s3tc_srgb")))return null;if(33776===n)return r.COMPRESSED_SRGB_S3TC_DXT1_EXT;if(33777===n)return r.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT;if(33778===n)return r.COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT;if(33779===n)return r.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT}else{if(null===(r=t.get("WEBGL_compressed_texture_s3tc")))return null;if(33776===n)return r.COMPRESSED_RGB_S3TC_DXT1_EXT;if(33777===n)return r.COMPRESSED_RGBA_S3TC_DXT1_EXT;if(33778===n)return r.COMPRESSED_RGBA_S3TC_DXT3_EXT;if(33779===n)return r.COMPRESSED_RGBA_S3TC_DXT5_EXT}if(35840===n||35841===n||35842===n||35843===n){if(null===(r=t.get("WEBGL_compressed_texture_pvrtc")))return null;if(35840===n)return r.COMPRESSED_RGB_PVRTC_4BPPV1_IMG;if(35841===n)return r.COMPRESSED_RGB_PVRTC_2BPPV1_IMG;if(35842===n)return r.COMPRESSED_RGBA_PVRTC_4BPPV1_IMG;if(35843===n)return r.COMPRESSED_RGBA_PVRTC_2BPPV1_IMG}if(36196===n)return null!==(r=t.get("WEBGL_compressed_texture_etc1"))?r.COMPRESSED_RGB_ETC1_WEBGL:null;if(37492===n||37496===n){if(null===(r=t.get("WEBGL_compressed_texture_etc")))return null;if(37492===n)return s===h?r.COMPRESSED_SRGB8_ETC2:r.COMPRESSED_RGB8_ETC2;if(37496===n)return s===h?r.COMPRESSED_SRGB8_ALPHA8_ETC2_EAC:r.COMPRESSED_RGBA8_ETC2_EAC}if(37808===n||37809===n||37810===n||37811===n||37812===n||37813===n||37814===n||37815===n||37816===n||37817===n||37818===n||37819===n||37820===n||37821===n){if(null===(r=t.get("WEBGL_compressed_texture_astc")))return null;if(37808===n)return s===h?r.COMPRESSED_SRGB8_ALPHA8_ASTC_4x4_KHR:r.COMPRESSED_RGBA_ASTC_4x4_KHR;if(37809===n)return s===h?r.COMPRESSED_SRGB8_ALPHA8_ASTC_5x4_KHR:r.COMPRESSED_RGBA_ASTC_5x4_KHR;if(37810===n)return s===h?r.COMPRESSED_SRGB8_ALPHA8_ASTC_5x5_KHR:r.COMPRESSED_RGBA_ASTC_5x5_KHR;if(37811===n)return s===h?r.COMPRESSED_SRGB8_ALPHA8_ASTC_6x5_KHR:r.COMPRESSED_RGBA_ASTC_6x5_KHR;if(37812===n)return s===h?r.COMPRESSED_SRGB8_ALPHA8_ASTC_6x6_KHR:r.COMPRESSED_RGBA_ASTC_6x6_KHR;if(37813===n)return s===h?r.COMPRESSED_SRGB8_ALPHA8_ASTC_8x5_KHR:r.COMPRESSED_RGBA_ASTC_8x5_KHR;if(37814===n)return s===h?r.COMPRESSED_SRGB8_ALPHA8_ASTC_8x6_KHR:r.COMPRESSED_RGBA_ASTC_8x6_KHR;if(37815===n)return s===h?r.COMPRESSED_SRGB8_ALPHA8_ASTC_8x8_KHR:r.COMPRESSED_RGBA_ASTC_8x8_KHR;if(37816===n)return s===h?r.COMPRESSED_SRGB8_ALPHA8_ASTC_10x5_KHR:r.COMPRESSED_RGBA_ASTC_10x5_KHR;if(37817===n)return s===h?r.COMPRESSED_SRGB8_ALPHA8_ASTC_10x6_KHR:r.COMPRESSED_RGBA_ASTC_10x6_KHR;if(37818===n)return s===h?r.COMPRESSED_SRGB8_ALPHA8_ASTC_10x8_KHR:r.COMPRESSED_RGBA_ASTC_10x8_KHR;if(37819===n)return s===h?r.COMPRESSED_SRGB8_ALPHA8_ASTC_10x10_KHR:r.COMPRESSED_RGBA_ASTC_10x10_KHR;if(37820===n)return s===h?r.COMPRESSED_SRGB8_ALPHA8_ASTC_12x10_KHR:r.COMPRESSED_RGBA_ASTC_12x10_KHR;if(37821===n)return s===h?r.COMPRESSED_SRGB8_ALPHA8_ASTC_12x12_KHR:r.COMPRESSED_RGBA_ASTC_12x12_KHR}if(36492===n||36494===n||36495===n){if(null===(r=t.get("EXT_texture_compression_bptc")))return null;if(36492===n)return s===h?r.COMPRESSED_SRGB_ALPHA_BPTC_UNORM_EXT:r.COMPRESSED_RGBA_BPTC_UNORM_EXT;if(36494===n)return r.COMPRESSED_RGB_BPTC_SIGNED_FLOAT_EXT;if(36495===n)return r.COMPRESSED_RGB_BPTC_UNSIGNED_FLOAT_EXT}if(36283===n||36284===n||36285===n||36286===n){if(null===(r=t.get("EXT_texture_compression_rgtc")))return null;if(36492===n)return r.COMPRESSED_RED_RGTC1_EXT;if(36284===n)return r.COMPRESSED_SIGNED_RED_RGTC1_EXT;if(36285===n)return r.COMPRESSED_RED_GREEN_RGTC2_EXT;if(36286===n)return r.COMPRESSED_SIGNED_RED_GREEN_RGTC2_EXT}return 1020===n?i?e.UNSIGNED_INT_24_8:null!==(r=t.get("WEBGL_depth_texture"))?r.UNSIGNED_INT_24_8_WEBGL:null:void 0!==e[n]?e[n]:null}}}class i7 extends tq{constructor(e=[]){super(),this.isArrayCamera=!0,this.cameras=e}}class re extends eJ{constructor(){super(),this.isGroup=!0,this.type="Group"}}let rt={type:"move"};class rn{getHandSpace(){return null===this._hand&&(this._hand=new re,this._hand.matrixAutoUpdate=!1,this._hand.visible=!1,this._hand.joints={},this._hand.inputState={pinching:!1}),this._hand}getTargetRaySpace(){return null===this._targetRay&&(this._targetRay=new re,this._targetRay.matrixAutoUpdate=!1,this._targetRay.visible=!1,this._targetRay.hasLinearVelocity=!1,this._targetRay.linearVelocity=new Y,this._targetRay.hasAngularVelocity=!1,this._targetRay.angularVelocity=new Y),this._targetRay}getGripSpace(){return null===this._grip&&(this._grip=new re,this._grip.matrixAutoUpdate=!1,this._grip.visible=!1,this._grip.hasLinearVelocity=!1,this._grip.linearVelocity=new Y,this._grip.hasAngularVelocity=!1,this._grip.angularVelocity=new Y),this._grip}dispatchEvent(e){return null!==this._targetRay&&this._targetRay.dispatchEvent(e),null!==this._grip&&this._grip.dispatchEvent(e),null!==this._hand&&this._hand.dispatchEvent(e),this}connect(e){if(e&&e.hand){let t=this._hand;if(t)for(let n of e.hand.values())this._getHandJoint(t,n)}return this.dispatchEvent({type:"connected",data:e}),this}disconnect(e){return this.dispatchEvent({type:"disconnected",data:e}),null!==this._targetRay&&(this._targetRay.visible=!1),null!==this._grip&&(this._grip.visible=!1),null!==this._hand&&(this._hand.visible=!1),this}update(e,t,n){let i=null,r=null,a=null,s=this._targetRay,o=this._grip,l=this._hand;if(e&&"visible-blurred"!==t.session.visibilityState){if(l&&e.hand){for(let i of(a=!0,e.hand.values())){let e=t.getJointPose(i,n),r=this._getHandJoint(l,i);null!==e&&(r.matrix.fromArray(e.transform.matrix),r.matrix.decompose(r.position,r.rotation,r.scale),r.matrixWorldNeedsUpdate=!0,r.jointRadius=e.radius),r.visible=null!==e}let i=l.joints["index-finger-tip"],r=l.joints["thumb-tip"],s=i.position.distanceTo(r.position);l.inputState.pinching&&s>.025?(l.inputState.pinching=!1,this.dispatchEvent({type:"pinchend",handedness:e.handedness,target:this})):!l.inputState.pinching&&s<=.015&&(l.inputState.pinching=!0,this.dispatchEvent({type:"pinchstart",handedness:e.handedness,target:this}))}else null!==o&&e.gripSpace&&null!==(r=t.getPose(e.gripSpace,n))&&(o.matrix.fromArray(r.transform.matrix),o.matrix.decompose(o.position,o.rotation,o.scale),o.matrixWorldNeedsUpdate=!0,r.linearVelocity?(o.hasLinearVelocity=!0,o.linearVelocity.copy(r.linearVelocity)):o.hasLinearVelocity=!1,r.angularVelocity?(o.hasAngularVelocity=!0,o.angularVelocity.copy(r.angularVelocity)):o.hasAngularVelocity=!1);null!==s&&(null===(i=t.getPose(e.targetRaySpace,n))&&null!==r&&(i=r),null!==i&&(s.matrix.fromArray(i.transform.matrix),s.matrix.decompose(s.position,s.rotation,s.scale),s.matrixWorldNeedsUpdate=!0,i.linearVelocity?(s.hasLinearVelocity=!0,s.linearVelocity.copy(i.linearVelocity)):s.hasLinearVelocity=!1,i.angularVelocity?(s.hasAngularVelocity=!0,s.angularVelocity.copy(i.angularVelocity)):s.hasAngularVelocity=!1,this.dispatchEvent(rt)))}return null!==s&&(s.visible=null!==i),null!==o&&(o.visible=null!==r),null!==l&&(l.visible=null!==a),this}_getHandJoint(e,t){if(void 0===e.joints[t.jointName]){let n=new re;n.matrixAutoUpdate=!1,n.visible=!1,e.joints[t.jointName]=n,e.add(n)}return e.joints[t.jointName]}constructor(){this._targetRay=null,this._grip=null,this._hand=null}}class ri extends V{copy(e){return super.copy(e),this.compareFunction=e.compareFunction,this}toJSON(e){let t=super.toJSON(e);return null!==this.compareFunction&&(t.compareFunction=this.compareFunction),t}constructor(e,t,n,i,r,a,s,o,l,h){if(1026!==(h=void 0!==h?h:1026)&&1027!==h)throw Error("DepthTexture format must be either THREE.DepthFormat or THREE.DepthStencilFormat");void 0===n&&1026===h&&(n=1014),void 0===n&&1027===h&&(n=1020),super(null,i,r,a,s,o,h,n,l),this.isDepthTexture=!0,this.image={width:e,height:t},this.magFilter=void 0!==s?s:1003,this.minFilter=void 0!==o?o:1003,this.flipY=!1,this.generateMipmaps=!1,this.compareFunction=null}}class rr extends d{constructor(e,t){super();let n=this,i=null,r=1,a=null,s="local-floor",o=1,l=null,h=null,c=null,u=null,d=null,p=null,f=t.getContextAttributes(),g=null,_=null,v=[],x=[],y=new tq;y.layers.enable(1),y.viewport=new G;let M=new tq;M.layers.enable(2),M.viewport=new G;let S=[y,M],E=new i7;E.layers.enable(1),E.layers.enable(2);let T=null,b=null;function A(e){let t=x.indexOf(e.inputSource);if(-1===t)return;let n=v[t];void 0!==n&&(n.update(e.inputSource,e.frame,l||a),n.dispatchEvent({type:e.type,data:e.inputSource}))}function w(){i.removeEventListener("select",A),i.removeEventListener("selectstart",A),i.removeEventListener("selectend",A),i.removeEventListener("squeeze",A),i.removeEventListener("squeezestart",A),i.removeEventListener("squeezeend",A),i.removeEventListener("end",w),i.removeEventListener("inputsourceschange",R);for(let e=0;e<v.length;e++){let t=x[e];null!==t&&(x[e]=null,v[e].disconnect(t))}T=null,b=null,e.setRenderTarget(g),d=null,u=null,c=null,i=null,_=null,N.stop(),n.isPresenting=!1,n.dispatchEvent({type:"sessionend"})}function R(e){for(let t=0;t<e.removed.length;t++){let n=e.removed[t],i=x.indexOf(n);i>=0&&(x[i]=null,v[i].disconnect(n))}for(let t=0;t<e.added.length;t++){let n=e.added[t],i=x.indexOf(n);if(-1===i){for(let e=0;e<v.length;e++)if(e>=x.length){x.push(n),i=e;break}else if(null===x[e]){x[e]=n,i=e;break}if(-1===i)break}let r=v[i];r&&r.connect(n)}}this.cameraAutoUpdate=!0,this.enabled=!1,this.isPresenting=!1,this.getController=function(e){let t=v[e];return void 0===t&&(t=new rn,v[e]=t),t.getTargetRaySpace()},this.getControllerGrip=function(e){let t=v[e];return void 0===t&&(t=new rn,v[e]=t),t.getGripSpace()},this.getHand=function(e){let t=v[e];return void 0===t&&(t=new rn,v[e]=t),t.getHandSpace()},this.setFramebufferScaleFactor=function(e){r=e,!0===n.isPresenting&&console.warn("THREE.WebXRManager: Cannot change framebuffer scale while presenting.")},this.setReferenceSpaceType=function(e){s=e,!0===n.isPresenting&&console.warn("THREE.WebXRManager: Cannot change reference space type while presenting.")},this.getReferenceSpace=function(){return l||a},this.setReferenceSpace=function(e){l=e},this.getBaseLayer=function(){return null!==u?u:d},this.getBinding=function(){return c},this.getFrame=function(){return p},this.getSession=function(){return i},this.setSession=async function(h){if(null!==(i=h)){if(g=e.getRenderTarget(),i.addEventListener("select",A),i.addEventListener("selectstart",A),i.addEventListener("selectend",A),i.addEventListener("squeeze",A),i.addEventListener("squeezestart",A),i.addEventListener("squeezeend",A),i.addEventListener("end",w),i.addEventListener("inputsourceschange",R),!0!==f.xrCompatible&&await t.makeXRCompatible(),void 0===i.renderState.layers||!1===e.capabilities.isWebGL2){let n={antialias:void 0!==i.renderState.layers||f.antialias,alpha:!0,depth:f.depth,stencil:f.stencil,framebufferScaleFactor:r};d=new XRWebGLLayer(i,t,n),i.updateRenderState({baseLayer:d}),_=new W(d.framebufferWidth,d.framebufferHeight,{format:1023,type:1009,colorSpace:e.outputColorSpace,stencilBuffer:f.stencil})}else{let n=null,a=null,s=null;f.depth&&(s=f.stencil?t.DEPTH24_STENCIL8:t.DEPTH_COMPONENT24,n=f.stencil?1027:1026,a=f.stencil?1020:1014);let o={colorFormat:t.RGBA8,depthFormat:s,scaleFactor:r};u=(c=new XRWebGLBinding(i,t)).createProjectionLayer(o),i.updateRenderState({layers:[u]}),_=new W(u.textureWidth,u.textureHeight,{format:1023,type:1009,depthTexture:new ri(u.textureWidth,u.textureHeight,a,void 0,void 0,void 0,void 0,void 0,void 0,n),stencilBuffer:f.stencil,colorSpace:e.outputColorSpace,samples:4*!!f.antialias}),e.properties.get(_).__ignoreDepthValues=u.ignoreDepthValues}_.isXRRenderTarget=!0,this.setFoveation(o),l=null,a=await i.requestReferenceSpace(s),N.setContext(i),N.start(),n.isPresenting=!0,n.dispatchEvent({type:"sessionstart"})}},this.getEnvironmentBlendMode=function(){if(null!==i)return i.environmentBlendMode};let C=new Y,L=new Y;function P(e,t){null===t?e.matrixWorld.copy(e.matrix):e.matrixWorld.multiplyMatrices(t.matrixWorld,e.matrix),e.matrixWorldInverse.copy(e.matrixWorld).invert()}this.updateCamera=function(e){var t,n,r;if(null===i)return;E.near=M.near=y.near=e.near,E.far=M.far=y.far=e.far,(T!==E.near||b!==E.far)&&(i.updateRenderState({depthNear:E.near,depthFar:E.far}),T=E.near,b=E.far);let a=e.parent,s=E.cameras;P(E,a);for(let e=0;e<s.length;e++)P(s[e],a);2===s.length?function(e,t,n){C.setFromMatrixPosition(t.matrixWorld),L.setFromMatrixPosition(n.matrixWorld);let i=C.distanceTo(L),r=t.projectionMatrix.elements,a=n.projectionMatrix.elements,s=r[14]/(r[10]-1),o=r[14]/(r[10]+1),l=(r[9]+1)/r[5],h=(r[9]-1)/r[5],c=(r[8]-1)/r[0],u=(a[8]+1)/a[0],d=i/(-c+u),p=-(d*c);t.matrixWorld.decompose(e.position,e.quaternion,e.scale),e.translateX(p),e.translateZ(d),e.matrixWorld.compose(e.position,e.quaternion,e.scale),e.matrixWorldInverse.copy(e.matrixWorld).invert();let f=s+d,m=o+d;e.projectionMatrix.makePerspective(s*c-p,s*u+(i-p),l*o/m*f,h*o/m*f,f,m),e.projectionMatrixInverse.copy(e.projectionMatrix).invert()}(E,y,M):E.projectionMatrix.copy(y.projectionMatrix),t=e,n=E,null===(r=a)?t.matrix.copy(n.matrixWorld):(t.matrix.copy(r.matrixWorld),t.matrix.invert(),t.matrix.multiply(n.matrixWorld)),t.matrix.decompose(t.position,t.quaternion,t.scale),t.updateMatrixWorld(!0),t.projectionMatrix.copy(n.projectionMatrix),t.projectionMatrixInverse.copy(n.projectionMatrixInverse),t.isPerspectiveCamera&&(t.fov=2*m*Math.atan(1/t.projectionMatrix.elements[5]),t.zoom=1)},this.getCamera=function(){return E},this.getFoveation=function(){if(null!==u||null!==d)return o},this.setFoveation=function(e){o=e,null!==u&&(u.fixedFoveation=e),null!==d&&void 0!==d.fixedFoveation&&(d.fixedFoveation=e)};let U=null,N=new t4;N.setAnimationLoop(function(t,i){if(h=i.getViewerPose(l||a),p=i,null!==h){let t=h.views;null!==d&&(e.setRenderTargetFramebuffer(_,d.framebuffer),e.setRenderTarget(_));let n=!1;t.length!==E.cameras.length&&(E.cameras.length=0,n=!0);for(let i=0;i<t.length;i++){let r=t[i],a=null;if(null!==d)a=d.getViewport(r);else{let t=c.getViewSubImage(u,r);a=t.viewport,0===i&&(e.setRenderTargetTextures(_,t.colorTexture,u.ignoreDepthValues?void 0:t.depthStencilTexture),e.setRenderTarget(_))}let s=S[i];void 0===s&&((s=new tq).layers.enable(i),s.viewport=new G,S[i]=s),s.matrix.fromArray(r.transform.matrix),s.matrix.decompose(s.position,s.quaternion,s.scale),s.projectionMatrix.fromArray(r.projectionMatrix),s.projectionMatrixInverse.copy(s.projectionMatrix).invert(),s.viewport.set(a.x,a.y,a.width,a.height),0===i&&(E.matrix.copy(s.matrix),E.matrix.decompose(E.position,E.quaternion,E.scale)),!0===n&&E.cameras.push(s)}}for(let e=0;e<v.length;e++){let t=x[e],n=v[e];null!==t&&void 0!==n&&n.update(t,i,l||a)}U&&U(t,i),i.detectedPlanes&&n.dispatchEvent({type:"planesdetected",data:i}),p=null}),this.setAnimationLoop=function(e){U=e},this.dispose=function(){}}}function ra(e,t){function n(e,t){!0===e.matrixAutoUpdate&&e.updateMatrix(),t.value.copy(e.matrix)}function i(i,r){i.opacity.value=r.opacity,r.color&&i.diffuse.value.copy(r.color),r.emissive&&i.emissive.value.copy(r.emissive).multiplyScalar(r.emissiveIntensity),r.map&&(i.map.value=r.map,n(r.map,i.mapTransform)),r.alphaMap&&(i.alphaMap.value=r.alphaMap,n(r.alphaMap,i.alphaMapTransform)),r.bumpMap&&(i.bumpMap.value=r.bumpMap,n(r.bumpMap,i.bumpMapTransform),i.bumpScale.value=r.bumpScale,1===r.side&&(i.bumpScale.value*=-1)),r.normalMap&&(i.normalMap.value=r.normalMap,n(r.normalMap,i.normalMapTransform),i.normalScale.value.copy(r.normalScale),1===r.side&&i.normalScale.value.negate()),r.displacementMap&&(i.displacementMap.value=r.displacementMap,n(r.displacementMap,i.displacementMapTransform),i.displacementScale.value=r.displacementScale,i.displacementBias.value=r.displacementBias),r.emissiveMap&&(i.emissiveMap.value=r.emissiveMap,n(r.emissiveMap,i.emissiveMapTransform)),r.specularMap&&(i.specularMap.value=r.specularMap,n(r.specularMap,i.specularMapTransform)),r.alphaTest>0&&(i.alphaTest.value=r.alphaTest);let a=t.get(r).envMap;if(a&&(i.envMap.value=a,i.flipEnvMap.value=a.isCubeTexture&&!1===a.isRenderTargetTexture?-1:1,i.reflectivity.value=r.reflectivity,i.ior.value=r.ior,i.refractionRatio.value=r.refractionRatio),r.lightMap){i.lightMap.value=r.lightMap;let t=!0===e._useLegacyLights?Math.PI:1;i.lightMapIntensity.value=r.lightMapIntensity*t,n(r.lightMap,i.lightMapTransform)}r.aoMap&&(i.aoMap.value=r.aoMap,i.aoMapIntensity.value=r.aoMapIntensity,n(r.aoMap,i.aoMapTransform))}return{refreshFogUniforms:function(t,n){n.color.getRGB(t.fogColor.value,tk(e)),n.isFog?(t.fogNear.value=n.near,t.fogFar.value=n.far):n.isFogExp2&&(t.fogDensity.value=n.density)},refreshMaterialUniforms:function(e,r,a,s,o){var l,h,c,u,d,p,f,m,g,_,v,x,y,M,S,E,T,b,A,w,R;r.isMeshBasicMaterial||r.isMeshLambertMaterial?i(e,r):r.isMeshToonMaterial?(i(e,r),l=e,(h=r).gradientMap&&(l.gradientMap.value=h.gradientMap)):r.isMeshPhongMaterial?(i(e,r),c=e,u=r,c.specular.value.copy(u.specular),c.shininess.value=Math.max(u.shininess,1e-4)):r.isMeshStandardMaterial?(i(e,r),d=e,p=r,d.metalness.value=p.metalness,p.metalnessMap&&(d.metalnessMap.value=p.metalnessMap,n(p.metalnessMap,d.metalnessMapTransform)),d.roughness.value=p.roughness,p.roughnessMap&&(d.roughnessMap.value=p.roughnessMap,n(p.roughnessMap,d.roughnessMapTransform)),t.get(p).envMap&&(d.envMapIntensity.value=p.envMapIntensity),r.isMeshPhysicalMaterial&&(f=e,m=r,g=o,f.ior.value=m.ior,m.sheen>0&&(f.sheenColor.value.copy(m.sheenColor).multiplyScalar(m.sheen),f.sheenRoughness.value=m.sheenRoughness,m.sheenColorMap&&(f.sheenColorMap.value=m.sheenColorMap,n(m.sheenColorMap,f.sheenColorMapTransform)),m.sheenRoughnessMap&&(f.sheenRoughnessMap.value=m.sheenRoughnessMap,n(m.sheenRoughnessMap,f.sheenRoughnessMapTransform))),m.clearcoat>0&&(f.clearcoat.value=m.clearcoat,f.clearcoatRoughness.value=m.clearcoatRoughness,m.clearcoatMap&&(f.clearcoatMap.value=m.clearcoatMap,n(m.clearcoatMap,f.clearcoatMapTransform)),m.clearcoatRoughnessMap&&(f.clearcoatRoughnessMap.value=m.clearcoatRoughnessMap,n(m.clearcoatRoughnessMap,f.clearcoatRoughnessMapTransform)),m.clearcoatNormalMap&&(f.clearcoatNormalMap.value=m.clearcoatNormalMap,n(m.clearcoatNormalMap,f.clearcoatNormalMapTransform),f.clearcoatNormalScale.value.copy(m.clearcoatNormalScale),1===m.side&&f.clearcoatNormalScale.value.negate())),m.iridescence>0&&(f.iridescence.value=m.iridescence,f.iridescenceIOR.value=m.iridescenceIOR,f.iridescenceThicknessMinimum.value=m.iridescenceThicknessRange[0],f.iridescenceThicknessMaximum.value=m.iridescenceThicknessRange[1],m.iridescenceMap&&(f.iridescenceMap.value=m.iridescenceMap,n(m.iridescenceMap,f.iridescenceMapTransform)),m.iridescenceThicknessMap&&(f.iridescenceThicknessMap.value=m.iridescenceThicknessMap,n(m.iridescenceThicknessMap,f.iridescenceThicknessMapTransform))),m.transmission>0&&(f.transmission.value=m.transmission,f.transmissionSamplerMap.value=g.texture,f.transmissionSamplerSize.value.set(g.width,g.height),m.transmissionMap&&(f.transmissionMap.value=m.transmissionMap,n(m.transmissionMap,f.transmissionMapTransform)),f.thickness.value=m.thickness,m.thicknessMap&&(f.thicknessMap.value=m.thicknessMap,n(m.thicknessMap,f.thicknessMapTransform)),f.attenuationDistance.value=m.attenuationDistance,f.attenuationColor.value.copy(m.attenuationColor)),m.anisotropy>0&&(f.anisotropyVector.value.set(m.anisotropy*Math.cos(m.anisotropyRotation),m.anisotropy*Math.sin(m.anisotropyRotation)),m.anisotropyMap&&(f.anisotropyMap.value=m.anisotropyMap,n(m.anisotropyMap,f.anisotropyMapTransform))),f.specularIntensity.value=m.specularIntensity,f.specularColor.value.copy(m.specularColor),m.specularColorMap&&(f.specularColorMap.value=m.specularColorMap,n(m.specularColorMap,f.specularColorMapTransform)),m.specularIntensityMap&&(f.specularIntensityMap.value=m.specularIntensityMap,n(m.specularIntensityMap,f.specularIntensityMapTransform)))):r.isMeshMatcapMaterial?(i(e,r),_=e,(v=r).matcap&&(_.matcap.value=v.matcap)):r.isMeshDepthMaterial?i(e,r):r.isMeshDistanceMaterial?(i(e,r),function(e,n){let i=t.get(n).light;e.referencePosition.value.setFromMatrixPosition(i.matrixWorld),e.nearDistance.value=i.shadow.camera.near,e.farDistance.value=i.shadow.camera.far}(e,r)):r.isMeshNormalMaterial?i(e,r):r.isLineBasicMaterial?(x=e,y=r,x.diffuse.value.copy(y.color),x.opacity.value=y.opacity,y.map&&(x.map.value=y.map,n(y.map,x.mapTransform)),r.isLineDashedMaterial&&(M=e,S=r,M.dashSize.value=S.dashSize,M.totalSize.value=S.dashSize+S.gapSize,M.scale.value=S.scale)):r.isPointsMaterial?(E=e,T=r,b=a,A=s,E.diffuse.value.copy(T.color),E.opacity.value=T.opacity,E.size.value=T.size*b,E.scale.value=.5*A,T.map&&(E.map.value=T.map,n(T.map,E.uvTransform)),T.alphaMap&&(E.alphaMap.value=T.alphaMap,n(T.alphaMap,E.alphaMapTransform)),T.alphaTest>0&&(E.alphaTest.value=T.alphaTest)):r.isSpriteMaterial?(w=e,R=r,w.diffuse.value.copy(R.color),w.opacity.value=R.opacity,w.rotation.value=R.rotation,R.map&&(w.map.value=R.map,n(R.map,w.mapTransform)),R.alphaMap&&(w.alphaMap.value=R.alphaMap,n(R.alphaMap,w.alphaMapTransform)),R.alphaTest>0&&(w.alphaTest.value=R.alphaTest)):r.isShadowMaterial?(e.color.value.copy(r.color),e.opacity.value=r.opacity):r.isShaderMaterial&&(r.uniformsNeedUpdate=!1)}}}function rs(e,t,n,i){let r={},a={},s=[],o=n.isWebGL2?e.getParameter(e.MAX_UNIFORM_BUFFER_BINDINGS):0;function l(e){let t={boundary:0,storage:0};return"number"==typeof e?(t.boundary=4,t.storage=4):e.isVector2?(t.boundary=8,t.storage=8):e.isVector3||e.isColor?(t.boundary=16,t.storage=12):e.isVector4?(t.boundary=16,t.storage=16):e.isMatrix3?(t.boundary=48,t.storage=48):e.isMatrix4?(t.boundary=64,t.storage=64):e.isTexture?console.warn("THREE.WebGLRenderer: Texture samplers can not be part of an uniforms group."):console.warn("THREE.WebGLRenderer: Unsupported uniform value type.",e),t}function h(t){let n=t.target;n.removeEventListener("dispose",h);let i=s.indexOf(n.__bindingPointIndex);s.splice(i,1),e.deleteBuffer(r[n.id]),delete r[n.id],delete a[n.id]}return{bind:function(e,t){let n=t.program;i.uniformBlockBinding(e,n)},update:function(n,c){let u=r[n.id];void 0===u&&(function(e){let t=e.uniforms,n=0,i=0;for(let e=0,r=t.length;e<r;e++){let r=t[e],a={boundary:0,storage:0},s=Array.isArray(r.value)?r.value:[r.value];for(let e=0,t=s.length;e<t;e++){let t=l(s[e]);a.boundary+=t.boundary,a.storage+=t.storage}if(r.__data=new Float32Array(a.storage/Float32Array.BYTES_PER_ELEMENT),r.__offset=n,e>0){let e=16-(i=n%16);0!==i&&e-a.boundary<0&&(r.__offset=n+=16-i)}n+=a.storage}(i=n%16)>0&&(n+=16-i),e.__size=n,e.__cache={}}(n),u=function(t){let n=function(){for(let e=0;e<o;e++)if(-1===s.indexOf(e))return s.push(e),e;return console.error("THREE.WebGLRenderer: Maximum number of simultaneously usable uniforms groups reached."),0}();t.__bindingPointIndex=n;let i=e.createBuffer(),r=t.__size,a=t.usage;return e.bindBuffer(e.UNIFORM_BUFFER,i),e.bufferData(e.UNIFORM_BUFFER,r,a),e.bindBuffer(e.UNIFORM_BUFFER,null),e.bindBufferBase(e.UNIFORM_BUFFER,n,i),i}(n),r[n.id]=u,n.addEventListener("dispose",h));let d=c.program;i.updateUBOMapping(n,d);let p=t.render.frame;a[n.id]!==p&&(function(t){let n=r[t.id],i=t.uniforms,a=t.__cache;e.bindBuffer(e.UNIFORM_BUFFER,n);for(let t=0,n=i.length;t<n;t++){let n=i[t];if(!0===function(e,t,n){let i=e.value;if(void 0===n[t]){if("number"==typeof i)n[t]=i;else{let e=Array.isArray(i)?i:[i],r=[];for(let t=0;t<e.length;t++)r.push(e[t].clone());n[t]=r}return!0}if("number"==typeof i){if(n[t]!==i)return n[t]=i,!0}else{let e=Array.isArray(n[t])?n[t]:[n[t]],r=Array.isArray(i)?i:[i];for(let t=0;t<e.length;t++){let n=e[t];if(!1===n.equals(r[t]))return n.copy(r[t]),!0}}return!1}(n,t,a)){let t=n.__offset,i=Array.isArray(n.value)?n.value:[n.value],r=0;for(let a=0;a<i.length;a++){let s=i[a],o=l(s);"number"==typeof s?(n.__data[0]=s,e.bufferSubData(e.UNIFORM_BUFFER,t+r,n.__data)):s.isMatrix3?(n.__data[0]=s.elements[0],n.__data[1]=s.elements[1],n.__data[2]=s.elements[2],n.__data[3]=s.elements[0],n.__data[4]=s.elements[3],n.__data[5]=s.elements[4],n.__data[6]=s.elements[5],n.__data[7]=s.elements[0],n.__data[8]=s.elements[6],n.__data[9]=s.elements[7],n.__data[10]=s.elements[8],n.__data[11]=s.elements[0]):(s.toArray(n.__data,r),r+=o.storage/Float32Array.BYTES_PER_ELEMENT)}e.bufferSubData(e.UNIFORM_BUFFER,t,n.__data)}}e.bindBuffer(e.UNIFORM_BUFFER,null)}(n),a[n.id]=p)},dispose:function(){for(let t in r)e.deleteBuffer(r[t]);s=[],r={},a={}}}}class ro{get coordinateSystem(){return 2e3}get outputColorSpace(){return this._outputColorSpace}set outputColorSpace(e){this._outputColorSpace=e;let t=this.getContext();t.drawingBufferColorSpace=e===s?"display-p3":"srgb",t.unpackColorSpace=N.workingColorSpace===o?"display-p3":"srgb"}get physicallyCorrectLights(){return console.warn("THREE.WebGLRenderer: The property .physicallyCorrectLights has been removed. Set renderer.useLegacyLights instead."),!this.useLegacyLights}set physicallyCorrectLights(e){console.warn("THREE.WebGLRenderer: The property .physicallyCorrectLights has been removed. Set renderer.useLegacyLights instead."),this.useLegacyLights=!e}get outputEncoding(){return console.warn("THREE.WebGLRenderer: Property .outputEncoding has been removed. Use .outputColorSpace instead."),this.outputColorSpace===r?3001:3e3}set outputEncoding(e){console.warn("THREE.WebGLRenderer: Property .outputEncoding has been removed. Use .outputColorSpace instead."),this.outputColorSpace=3001===e?r:a}get useLegacyLights(){return console.warn("THREE.WebGLRenderer: The property .useLegacyLights has been deprecated. Migrate your lighting according to the following guide: https://discourse.threejs.org/t/updates-to-lighting-in-three-js-r155/53733."),this._useLegacyLights}set useLegacyLights(e){console.warn("THREE.WebGLRenderer: The property .useLegacyLights has been deprecated. Migrate your lighting according to the following guide: https://discourse.threejs.org/t/updates-to-lighting-in-three-js-r155/53733."),this._useLegacyLights=e}constructor(e={}){let t,n,i,s,o,l,h,c,u,d,p,f,m,g,_,v,y,M,E,T,b,w,R,C,L,{canvas:P=function(){let e=A("canvas");return e.style.display="block",e}(),context:U=null,depth:N=!0,stencil:D=!0,alpha:I=!1,antialias:O=!1,premultipliedAlpha:F=!0,preserveDrawingBuffer:z=!1,powerPreference:B="default",failIfMajorPerformanceCaveat:H=!1}=e;this.isWebGLRenderer=!0,t=null!==U?U.getContextAttributes().alpha:I;let V=new Uint32Array(4),k=new Int32Array(4),X=null,j=null,q=[],J=[];this.domElement=P,this.debug={checkShaderErrors:!0,onShaderError:null},this.autoClear=!0,this.autoClearColor=!0,this.autoClearDepth=!0,this.autoClearStencil=!0,this.sortObjects=!0,this.clippingPlanes=[],this.localClippingEnabled=!1,this._outputColorSpace=r,this._useLegacyLights=!1,this.toneMapping=0,this.toneMappingExposure=1;let Z=this,K=!1,Q=0,$=0,ee=null,et=-1,en=null,ei=new G,er=new G,ea=null,es=new tn(0),eo=0,el=P.width,eh=P.height,ec=1,eu=null,ed=null,ep=new G(0,0,el,eh),ef=new G(0,0,el,eh),em=!1,eg=new t3,e_=!1,ev=!1,ex=null,ey=new eT,eM=new S,eS=new Y,eE={background:null,fog:null,environment:null,overrideMaterial:null,isScene:!0};function eb(){return null===ee?ec:1}let eA=U;function ew(e,t){for(let n=0;n<e.length;n++){let i=e[n],r=P.getContext(i,t);if(null!==r)return r}return null}try{if("setAttribute"in P&&P.setAttribute("data-engine","three.js r".concat("158")),P.addEventListener("webglcontextlost",eL,!1),P.addEventListener("webglcontextrestored",eP,!1),P.addEventListener("webglcontextcreationerror",eU,!1),null===eA){let e=["webgl2","webgl","experimental-webgl"];if(!0===Z.isWebGL1Renderer&&e.shift(),eA=ew(e,{alpha:!0,depth:N,stencil:D,antialias:O,premultipliedAlpha:F,preserveDrawingBuffer:z,powerPreference:B,failIfMajorPerformanceCaveat:H}),null===eA)if(ew(e))throw Error("Error creating WebGL context with your selected attributes.");else throw Error("Error creating WebGL context.")}"undefined"!=typeof WebGLRenderingContext&&eA instanceof WebGLRenderingContext&&console.warn("THREE.WebGLRenderer: WebGL 1 support was deprecated in r153 and will be removed in r163."),void 0===eA.getShaderPrecisionFormat&&(eA.getShaderPrecisionFormat=function(){return{rangeMin:1,rangeMax:1,precision:1}})}catch(e){throw console.error("THREE.WebGLRenderer: "+e.message),e}function eR(){n=new nT(eA),i=new nr(eA,n,e),n.init(i),R=new i9(eA,n,i),s=new i6(eA,n,i),o=new nw(eA),l=new ij,h=new i8(eA,n,s,l,i,R,o),c=new ns(Z),u=new nE(Z),d=new t5(eA,i),C=new nn(eA,n,d,i),p=new nb(eA,d,o,C),f=new nP(eA,p,d,o),T=new nL(eA,i,h),y=new na(l),m=new iX(Z,c,u,n,i,C,y),g=new ra(Z,l),_=new iZ,v=new i2(n,i),E=new nt(Z,c,u,s,f,t,F),M=new i5(Z,f,i),L=new rs(eA,o,i,s),b=new ni(eA,n,o,i),w=new nA(eA,n,o,i),o.programs=m.programs,Z.capabilities=i,Z.extensions=n,Z.properties=l,Z.renderLists=_,Z.shadowMap=M,Z.state=s,Z.info=o}eR();let eC=new rr(Z,eA);function eL(e){e.preventDefault(),console.log("THREE.WebGLRenderer: Context Lost."),K=!0}function eP(){console.log("THREE.WebGLRenderer: Context Restored."),K=!1;let e=o.autoReset,t=M.enabled,n=M.autoUpdate,i=M.needsUpdate,r=M.type;eR(),o.autoReset=e,M.enabled=t,M.autoUpdate=n,M.needsUpdate=i,M.type=r}function eU(e){console.error("THREE.WebGLRenderer: A WebGL context could not be created. Reason: ",e.statusMessage)}function eN(e){var t;let n=e.target;n.removeEventListener("dispose",eN),function(e){let t=l.get(e).programs;void 0!==t&&(t.forEach(function(e){m.releaseProgram(e)}),e.isShaderMaterial&&m.releaseShaderCache(e))}(t=n),l.remove(t)}function eD(e,t,n){!0===e.transparent&&2===e.side&&!1===e.forceSinglePass?(e.side=1,e.needsUpdate=!0,eG(e,t,n),e.side=0,e.needsUpdate=!0,eG(e,t,n),e.side=2):eG(e,t,n)}this.xr=eC,this.getContext=function(){return eA},this.getContextAttributes=function(){return eA.getContextAttributes()},this.forceContextLoss=function(){let e=n.get("WEBGL_lose_context");e&&e.loseContext()},this.forceContextRestore=function(){let e=n.get("WEBGL_lose_context");e&&e.restoreContext()},this.getPixelRatio=function(){return ec},this.setPixelRatio=function(e){void 0!==e&&(ec=e,this.setSize(el,eh,!1))},this.getSize=function(e){return e.set(el,eh)},this.setSize=function(e,t){let n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(eC.isPresenting)return void console.warn("THREE.WebGLRenderer: Can't change size while VR device is presenting.");el=e,eh=t,P.width=Math.floor(e*ec),P.height=Math.floor(t*ec),!0===n&&(P.style.width=e+"px",P.style.height=t+"px"),this.setViewport(0,0,e,t)},this.getDrawingBufferSize=function(e){return e.set(el*ec,eh*ec).floor()},this.setDrawingBufferSize=function(e,t,n){el=e,eh=t,ec=n,P.width=Math.floor(e*n),P.height=Math.floor(t*n),this.setViewport(0,0,e,t)},this.getCurrentViewport=function(e){return e.copy(ei)},this.getViewport=function(e){return e.copy(ep)},this.setViewport=function(e,t,n,i){e.isVector4?ep.set(e.x,e.y,e.z,e.w):ep.set(e,t,n,i),s.viewport(ei.copy(ep).multiplyScalar(ec).floor())},this.getScissor=function(e){return e.copy(ef)},this.setScissor=function(e,t,n,i){e.isVector4?ef.set(e.x,e.y,e.z,e.w):ef.set(e,t,n,i),s.scissor(er.copy(ef).multiplyScalar(ec).floor())},this.getScissorTest=function(){return em},this.setScissorTest=function(e){s.setScissorTest(em=e)},this.setOpaqueSort=function(e){eu=e},this.setTransparentSort=function(e){ed=e},this.getClearColor=function(e){return e.copy(E.getClearColor())},this.setClearColor=function(){E.setClearColor.apply(E,arguments)},this.getClearAlpha=function(){return E.getClearAlpha()},this.setClearAlpha=function(){E.setClearAlpha.apply(E,arguments)},this.clear=function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],t=!(arguments.length>1)||void 0===arguments[1]||arguments[1],n=!(arguments.length>2)||void 0===arguments[2]||arguments[2],i=0;if(e){let e=!1;if(null!==ee){let t=ee.texture.format;e=1033===t||1031===t||1029===t}if(e){let e=ee.texture.type,t=1009===e||1014===e||1012===e||1020===e||1017===e||1018===e,n=E.getClearColor(),i=E.getClearAlpha(),r=n.r,a=n.g,s=n.b;t?(V[0]=r,V[1]=a,V[2]=s,V[3]=i,eA.clearBufferuiv(eA.COLOR,0,V)):(k[0]=r,k[1]=a,k[2]=s,k[3]=i,eA.clearBufferiv(eA.COLOR,0,k))}else i|=eA.COLOR_BUFFER_BIT}t&&(i|=eA.DEPTH_BUFFER_BIT),n&&(i|=eA.STENCIL_BUFFER_BIT,this.state.buffers.stencil.setMask(0xffffffff)),eA.clear(i)},this.clearColor=function(){this.clear(!0,!1,!1)},this.clearDepth=function(){this.clear(!1,!0,!1)},this.clearStencil=function(){this.clear(!1,!1,!0)},this.dispose=function(){P.removeEventListener("webglcontextlost",eL,!1),P.removeEventListener("webglcontextrestored",eP,!1),P.removeEventListener("webglcontextcreationerror",eU,!1),_.dispose(),v.dispose(),l.dispose(),c.dispose(),u.dispose(),f.dispose(),C.dispose(),L.dispose(),m.dispose(),eC.dispose(),eC.removeEventListener("sessionstart",eO),eC.removeEventListener("sessionend",eF),ex&&(ex.dispose(),ex=null),ez.stop()},this.renderBufferDirect=function(e,t,n,r,o,f){let m;null===t&&(t=eE);let _=o.isMesh&&0>o.matrixWorld.determinant(),v=function(e,t,n,r,o){var d,p;!0!==t.isScene&&(t=eE),h.resetTextureUnits();let f=t.fog,m=r.isMeshStandardMaterial?t.environment:null,_=null===ee?Z.outputColorSpace:!0===ee.isXRRenderTarget?ee.texture.colorSpace:a,v=(r.isMeshStandardMaterial?u:c).get(r.envMap||m),x=!0===r.vertexColors&&!!n.attributes.color&&4===n.attributes.color.itemSize,M=!!n.attributes.tangent&&(!!r.normalMap||r.anisotropy>0),S=!!n.morphAttributes.position,E=!!n.morphAttributes.normal,b=!!n.morphAttributes.color,A=0;r.toneMapped&&(null===ee||!0===ee.isXRRenderTarget)&&(A=Z.toneMapping);let w=n.morphAttributes.position||n.morphAttributes.normal||n.morphAttributes.color,R=void 0!==w?w.length:0,C=l.get(r),P=j.state.lights;if(!0===e_&&(!0===ev||e!==en)){let t=e===en&&r.id===et;y.setState(r,e,t)}let U=!1;r.version===C.__version?C.needsLights&&C.lightsStateVersion!==P.state.version||C.outputColorSpace!==_||o.isInstancedMesh&&!1===C.instancing?U=!0:o.isInstancedMesh||!0!==C.instancing?o.isSkinnedMesh&&!1===C.skinning?U=!0:o.isSkinnedMesh||!0!==C.skinning?o.isInstancedMesh&&!0===C.instancingColor&&null===o.instanceColor||o.isInstancedMesh&&!1===C.instancingColor&&null!==o.instanceColor||C.envMap!==v||!0===r.fog&&C.fog!==f||void 0!==C.numClippingPlanes&&(C.numClippingPlanes!==y.numPlanes||C.numIntersection!==y.numIntersection)||C.vertexAlphas!==x||C.vertexTangents!==M||C.morphTargets!==S||C.morphNormals!==E||C.morphColors!==b||C.toneMapping!==A?U=!0:!0===i.isWebGL2&&C.morphTargetsCount!==R&&(U=!0):U=!0:U=!0:(U=!0,C.__version=r.version);let N=C.currentProgram;!0===U&&(N=eG(r,t,o));let D=!1,I=!1,O=!1,F=N.getUniforms(),z=C.uniforms;if(s.useProgram(N.program)&&(D=!0,I=!0,O=!0),r.id!==et&&(et=r.id,I=!0),D||en!==e){F.setValue(eA,"projectionMatrix",e.projectionMatrix),F.setValue(eA,"viewMatrix",e.matrixWorldInverse);let t=F.map.cameraPosition;void 0!==t&&t.setValue(eA,eS.setFromMatrixPosition(e.matrixWorld)),i.logarithmicDepthBuffer&&F.setValue(eA,"logDepthBufFC",2/(Math.log(e.far+1)/Math.LN2)),(r.isMeshPhongMaterial||r.isMeshToonMaterial||r.isMeshLambertMaterial||r.isMeshBasicMaterial||r.isMeshStandardMaterial||r.isShaderMaterial)&&F.setValue(eA,"isOrthographic",!0===e.isOrthographicCamera),en!==e&&(en=e,I=!0,O=!0)}if(o.isSkinnedMesh){F.setOptional(eA,o,"bindMatrix"),F.setOptional(eA,o,"bindMatrixInverse");let e=o.skeleton;e&&(i.floatVertexTextures?(null===e.boneTexture&&e.computeBoneTexture(),F.setValue(eA,"boneTexture",e.boneTexture,h),F.setValue(eA,"boneTextureSize",e.boneTextureSize)):console.warn("THREE.WebGLRenderer: SkinnedMesh can only be used with WebGL 2. With WebGL 1 OES_texture_float and vertex textures support is required."))}let B=n.morphAttributes;if((void 0!==B.position||void 0!==B.normal||void 0!==B.color&&!0===i.isWebGL2)&&T.update(o,n,N),(I||C.receiveShadow!==o.receiveShadow)&&(C.receiveShadow=o.receiveShadow,F.setValue(eA,"receiveShadow",o.receiveShadow)),r.isMeshGouraudMaterial&&null!==r.envMap&&(z.envMap.value=v,z.flipEnvMap.value=v.isCubeTexture&&!1===v.isRenderTargetTexture?-1:1),I&&(F.setValue(eA,"toneMappingExposure",Z.toneMappingExposure),C.needsLights&&(d=z,p=O,d.ambientLightColor.needsUpdate=p,d.lightProbe.needsUpdate=p,d.directionalLights.needsUpdate=p,d.directionalLightShadows.needsUpdate=p,d.pointLights.needsUpdate=p,d.pointLightShadows.needsUpdate=p,d.spotLights.needsUpdate=p,d.spotLightShadows.needsUpdate=p,d.rectAreaLights.needsUpdate=p,d.hemisphereLights.needsUpdate=p),f&&!0===r.fog&&g.refreshFogUniforms(z,f),g.refreshMaterialUniforms(z,r,ec,eh,ex),iA.upload(eA,ek(C),z,h)),r.isShaderMaterial&&!0===r.uniformsNeedUpdate&&(iA.upload(eA,ek(C),z,h),r.uniformsNeedUpdate=!1),r.isSpriteMaterial&&F.setValue(eA,"center",o.center),F.setValue(eA,"modelViewMatrix",o.modelViewMatrix),F.setValue(eA,"normalMatrix",o.normalMatrix),F.setValue(eA,"modelMatrix",o.matrixWorld),r.isShaderMaterial||r.isRawShaderMaterial){let e=r.uniformsGroups;for(let t=0,n=e.length;t<n;t++)if(i.isWebGL2){let n=e[t];L.update(n,N),L.bind(n,N)}else console.warn("THREE.WebGLRenderer: Uniform Buffer Objects can only be used with WebGL 2.")}return N}(e,t,n,r,o);s.setMaterial(r,_);let x=n.index,M=1;if(!0===r.wireframe){if(void 0===(x=p.getWireframeAttribute(n)))return;M=2}let S=n.drawRange,E=n.attributes.position,A=S.start*M,R=(S.start+S.count)*M;null!==f&&(A=Math.max(A,f.start*M),R=Math.min(R,(f.start+f.count)*M)),null!==x?(A=Math.max(A,0),R=Math.min(R,x.count)):null!=E&&(A=Math.max(A,0),R=Math.min(R,E.count));let P=R-A;if(P<0||P===1/0)return;C.setup(o,r,v,n,x);let U=b;if(null!==x&&(m=d.get(x),(U=w).setIndex(m)),o.isMesh)!0===r.wireframe?(s.setLineWidth(r.wireframeLinewidth*eb()),U.setMode(eA.LINES)):U.setMode(eA.TRIANGLES);else if(o.isLine){let e=r.linewidth;void 0===e&&(e=1),s.setLineWidth(e*eb()),o.isLineSegments?U.setMode(eA.LINES):o.isLineLoop?U.setMode(eA.LINE_LOOP):U.setMode(eA.LINE_STRIP)}else o.isPoints?U.setMode(eA.POINTS):o.isSprite&&U.setMode(eA.TRIANGLES);if(o.isInstancedMesh)U.renderInstances(A,P,o.count);else if(n.isInstancedBufferGeometry){let e=void 0!==n._maxInstanceCount?n._maxInstanceCount:1/0,t=Math.min(n.instanceCount,e);U.renderInstances(A,P,t)}else U.render(A,P)},this.compile=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;null===n&&(n=e),(j=v.get(n)).init(),J.push(j),n.traverseVisible(function(e){e.isLight&&e.layers.test(t.layers)&&(j.pushLight(e),e.castShadow&&j.pushShadow(e))}),e!==n&&e.traverseVisible(function(e){e.isLight&&e.layers.test(t.layers)&&(j.pushLight(e),e.castShadow&&j.pushShadow(e))}),j.setupLights(Z._useLegacyLights);let i=new Set;return e.traverse(function(e){let t=e.material;if(t)if(Array.isArray(t))for(let r=0;r<t.length;r++){let a=t[r];eD(a,n,e),i.add(a)}else eD(t,n,e),i.add(t)}),J.pop(),j=null,i},this.compileAsync=function(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=this.compile(e,t,i);return new Promise(t=>{function i(){if(r.forEach(function(e){l.get(e).currentProgram.isReady()&&r.delete(e)}),0===r.size)return void t(e);setTimeout(i,10)}null!==n.get("KHR_parallel_shader_compile")?i():setTimeout(i,10)})};let eI=null;function eO(){ez.stop()}function eF(){ez.start()}let ez=new t4;function eB(e,t,r,a){let o=e.opaque,l=e.transmissive,c=e.transparent;j.setupLightsView(r),!0===e_&&y.setGlobalState(Z.clippingPlanes,r),l.length>0&&function(e,t,r,a){if(null!==(!0===r.isScene?r.overrideMaterial:null))return;let s=i.isWebGL2;null===ex&&(ex=new W(1,1,{generateMipmaps:!0,type:n.has("EXT_color_buffer_half_float")?1016:1009,minFilter:1008,samples:4*!!s})),Z.getDrawingBufferSize(eM),s?ex.setSize(eM.x,eM.y):ex.setSize(x(eM.x),x(eM.y));let o=Z.getRenderTarget();Z.setRenderTarget(ex),Z.getClearColor(es),(eo=Z.getClearAlpha())<1&&Z.setClearColor(0xffffff,.5),Z.clear();let l=Z.toneMapping;Z.toneMapping=0,eH(e,r,a),h.updateMultisampleRenderTarget(ex),h.updateRenderTargetMipmap(ex);let c=!1;for(let e=0,n=t.length;e<n;e++){let n=t[e],i=n.object,s=n.geometry,o=n.material,l=n.group;if(2===o.side&&i.layers.test(a.layers)){let e=o.side;o.side=1,o.needsUpdate=!0,eV(i,r,a,s,o,l),o.side=e,o.needsUpdate=!0,c=!0}}!0===c&&(h.updateMultisampleRenderTarget(ex),h.updateRenderTargetMipmap(ex)),Z.setRenderTarget(o),Z.setClearColor(es,eo),Z.toneMapping=l}(o,l,t,r),a&&s.viewport(ei.copy(a)),o.length>0&&eH(o,t,r),l.length>0&&eH(l,t,r),c.length>0&&eH(c,t,r),s.buffers.depth.setTest(!0),s.buffers.depth.setMask(!0),s.buffers.color.setMask(!0),s.setPolygonOffset(!1)}function eH(e,t,n){let i=!0===t.isScene?t.overrideMaterial:null;for(let r=0,a=e.length;r<a;r++){let a=e[r],s=a.object,o=a.geometry,l=null===i?a.material:i,h=a.group;s.layers.test(n.layers)&&eV(s,t,n,o,l,h)}}function eV(e,t,n,i,r,a){e.onBeforeRender(Z,t,n,i,r,a),e.modelViewMatrix.multiplyMatrices(n.matrixWorldInverse,e.matrixWorld),e.normalMatrix.getNormalMatrix(e.modelViewMatrix),r.onBeforeRender(Z,t,n,i,e,a),!0===r.transparent&&2===r.side&&!1===r.forceSinglePass?(r.side=1,r.needsUpdate=!0,Z.renderBufferDirect(n,t,i,r,e,a),r.side=0,r.needsUpdate=!0,Z.renderBufferDirect(n,t,i,r,e,a),r.side=2):Z.renderBufferDirect(n,t,i,r,e,a),e.onAfterRender(Z,t,n,i,r,a)}function eG(e,t,n){var i;!0!==t.isScene&&(t=eE);let r=l.get(e),a=j.state.lights,s=j.state.shadowsArray,o=a.state.version,h=m.getParameters(e,a.state,s,t,n),d=m.getProgramCacheKey(h),p=r.programs;r.environment=e.isMeshStandardMaterial?t.environment:null,r.fog=t.fog,r.envMap=(e.isMeshStandardMaterial?u:c).get(e.envMap||r.environment),void 0===p&&(e.addEventListener("dispose",eN),r.programs=p=new Map);let f=p.get(d);if(void 0!==f){if(r.currentProgram===f&&r.lightsStateVersion===o)return eW(e,h),f}else h.uniforms=m.getUniforms(e),e.onBuild(n,h,Z),e.onBeforeCompile(h,Z),f=m.acquireProgram(h,d),p.set(d,f),r.uniforms=h.uniforms;let g=r.uniforms;return(e.isShaderMaterial||e.isRawShaderMaterial)&&!0!==e.clipping||(g.clippingPlanes=y.uniform),eW(e,h),r.needsLights=(i=e).isMeshLambertMaterial||i.isMeshToonMaterial||i.isMeshPhongMaterial||i.isMeshStandardMaterial||i.isShadowMaterial||i.isShaderMaterial&&!0===i.lights,r.lightsStateVersion=o,r.needsLights&&(g.ambientLightColor.value=a.state.ambient,g.lightProbe.value=a.state.probe,g.directionalLights.value=a.state.directional,g.directionalLightShadows.value=a.state.directionalShadow,g.spotLights.value=a.state.spot,g.spotLightShadows.value=a.state.spotShadow,g.rectAreaLights.value=a.state.rectArea,g.ltc_1.value=a.state.rectAreaLTC1,g.ltc_2.value=a.state.rectAreaLTC2,g.pointLights.value=a.state.point,g.pointLightShadows.value=a.state.pointShadow,g.hemisphereLights.value=a.state.hemi,g.directionalShadowMap.value=a.state.directionalShadowMap,g.directionalShadowMatrix.value=a.state.directionalShadowMatrix,g.spotShadowMap.value=a.state.spotShadowMap,g.spotLightMatrix.value=a.state.spotLightMatrix,g.spotLightMap.value=a.state.spotLightMap,g.pointShadowMap.value=a.state.pointShadowMap,g.pointShadowMatrix.value=a.state.pointShadowMatrix),r.currentProgram=f,r.uniformsList=null,f}function ek(e){if(null===e.uniformsList){let t=e.currentProgram.getUniforms();e.uniformsList=iA.seqWithValue(t.seq,e.uniforms)}return e.uniformsList}function eW(e,t){let n=l.get(e);n.outputColorSpace=t.outputColorSpace,n.instancing=t.instancing,n.instancingColor=t.instancingColor,n.skinning=t.skinning,n.morphTargets=t.morphTargets,n.morphNormals=t.morphNormals,n.morphColors=t.morphColors,n.morphTargetsCount=t.morphTargetsCount,n.numClippingPlanes=t.numClippingPlanes,n.numIntersection=t.numClipIntersection,n.vertexAlphas=t.vertexAlphas,n.vertexTangents=t.vertexTangents,n.toneMapping=t.toneMapping}ez.setAnimationLoop(function(e){eI&&eI(e)}),"undefined"!=typeof self&&ez.setContext(self),this.setAnimationLoop=function(e){eI=e,eC.setAnimationLoop(e),null===e?ez.stop():ez.start()},eC.addEventListener("sessionstart",eO),eC.addEventListener("sessionend",eF),this.render=function(e,t){if(void 0!==t&&!0!==t.isCamera)return void console.error("THREE.WebGLRenderer.render: camera is not an instance of THREE.Camera.");if(!0===K)return;!0===e.matrixWorldAutoUpdate&&e.updateMatrixWorld(),null===t.parent&&!0===t.matrixWorldAutoUpdate&&t.updateMatrixWorld(),!0===eC.enabled&&!0===eC.isPresenting&&(!0===eC.cameraAutoUpdate&&eC.updateCamera(t),t=eC.getCamera()),!0===e.isScene&&e.onBeforeRender(Z,e,t,ee),(j=v.get(e,J.length)).init(),J.push(j),ey.multiplyMatrices(t.projectionMatrix,t.matrixWorldInverse),eg.setFromProjectionMatrix(ey),ev=this.localClippingEnabled,e_=y.init(this.clippingPlanes,ev),(X=_.get(e,q.length)).init(),q.push(X),function e(t,n,i,r){if(!1===t.visible)return;if(t.layers.test(n.layers)){if(t.isGroup)i=t.renderOrder;else if(t.isLOD)!0===t.autoUpdate&&t.update(n);else if(t.isLight)j.pushLight(t),t.castShadow&&j.pushShadow(t);else if(t.isSprite){if(!t.frustumCulled||eg.intersectsSprite(t)){r&&eS.setFromMatrixPosition(t.matrixWorld).applyMatrix4(ey);let e=f.update(t),n=t.material;n.visible&&X.push(t,e,n,i,eS.z,null)}}else if((t.isMesh||t.isLine||t.isPoints)&&(!t.frustumCulled||eg.intersectsObject(t))){let e=f.update(t),n=t.material;if(r&&(void 0!==t.boundingSphere?(null===t.boundingSphere&&t.computeBoundingSphere(),eS.copy(t.boundingSphere.center)):(null===e.boundingSphere&&e.computeBoundingSphere(),eS.copy(e.boundingSphere.center)),eS.applyMatrix4(t.matrixWorld).applyMatrix4(ey)),Array.isArray(n)){let r=e.groups;for(let a=0,s=r.length;a<s;a++){let s=r[a],o=n[s.materialIndex];o&&o.visible&&X.push(t,e,o,i,eS.z,s)}}else n.visible&&X.push(t,e,n,i,eS.z,null)}}let a=t.children;for(let t=0,s=a.length;t<s;t++)e(a[t],n,i,r)}(e,t,0,Z.sortObjects),X.finish(),!0===Z.sortObjects&&X.sort(eu,ed),this.info.render.frame++,!0===e_&&y.beginShadows();let n=j.state.shadowsArray;if(M.render(n,e,t),!0===e_&&y.endShadows(),!0===this.info.autoReset&&this.info.reset(),E.render(X,e),j.setupLights(Z._useLegacyLights),t.isArrayCamera){let n=t.cameras;for(let t=0,i=n.length;t<i;t++){let i=n[t];eB(X,e,i,i.viewport)}}else eB(X,e,t);null!==ee&&(h.updateMultisampleRenderTarget(ee),h.updateRenderTargetMipmap(ee)),!0===e.isScene&&e.onAfterRender(Z,e,t),C.resetDefaultState(),et=-1,en=null,J.pop(),j=J.length>0?J[J.length-1]:null,q.pop(),X=q.length>0?q[q.length-1]:null},this.getActiveCubeFace=function(){return Q},this.getActiveMipmapLevel=function(){return $},this.getRenderTarget=function(){return ee},this.setRenderTargetTextures=function(e,t,i){l.get(e.texture).__webglTexture=t,l.get(e.depthTexture).__webglTexture=i;let r=l.get(e);r.__hasExternalTextures=!0,r.__hasExternalTextures&&(r.__autoAllocateDepthBuffer=void 0===i,r.__autoAllocateDepthBuffer||!0!==n.has("WEBGL_multisampled_render_to_texture")||(console.warn("THREE.WebGLRenderer: Render-to-texture extension was disabled because an external texture was provided"),r.__useRenderToTexture=!1))},this.setRenderTargetFramebuffer=function(e,t){let n=l.get(e);n.__webglFramebuffer=t,n.__useDefaultFramebuffer=void 0===t},this.setRenderTarget=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;ee=e,Q=t,$=n;let r=!0,a=null,o=!1,c=!1;if(e){let u=l.get(e);void 0!==u.__useDefaultFramebuffer?(s.bindFramebuffer(eA.FRAMEBUFFER,null),r=!1):void 0===u.__webglFramebuffer?h.setupRenderTarget(e):u.__hasExternalTextures&&h.rebindTextures(e,l.get(e.texture).__webglTexture,l.get(e.depthTexture).__webglTexture);let d=e.texture;(d.isData3DTexture||d.isDataArrayTexture||d.isCompressedArrayTexture)&&(c=!0);let p=l.get(e).__webglFramebuffer;e.isWebGLCubeRenderTarget?(a=Array.isArray(p[t])?p[t][n]:p[t],o=!0):a=i.isWebGL2&&e.samples>0&&!1===h.useMultisampledRTT(e)?l.get(e).__webglMultisampledFramebuffer:Array.isArray(p)?p[n]:p,ei.copy(e.viewport),er.copy(e.scissor),ea=e.scissorTest}else ei.copy(ep).multiplyScalar(ec).floor(),er.copy(ef).multiplyScalar(ec).floor(),ea=em;if(s.bindFramebuffer(eA.FRAMEBUFFER,a)&&i.drawBuffers&&r&&s.drawBuffers(e,a),s.viewport(ei),s.scissor(er),s.setScissorTest(ea),o){let i=l.get(e.texture);eA.framebufferTexture2D(eA.FRAMEBUFFER,eA.COLOR_ATTACHMENT0,eA.TEXTURE_CUBE_MAP_POSITIVE_X+t,i.__webglTexture,n)}else if(c){let i=l.get(e.texture);eA.framebufferTextureLayer(eA.FRAMEBUFFER,eA.COLOR_ATTACHMENT0,i.__webglTexture,n||0,t||0)}et=-1},this.readRenderTargetPixels=function(e,t,r,a,o,h,c){if(!(e&&e.isWebGLRenderTarget))return void console.error("THREE.WebGLRenderer.readRenderTargetPixels: renderTarget is not THREE.WebGLRenderTarget.");let u=l.get(e).__webglFramebuffer;if(e.isWebGLCubeRenderTarget&&void 0!==c&&(u=u[c]),u){s.bindFramebuffer(eA.FRAMEBUFFER,u);try{let s=e.texture,l=s.format,c=s.type;if(1023!==l&&R.convert(l)!==eA.getParameter(eA.IMPLEMENTATION_COLOR_READ_FORMAT))return void console.error("THREE.WebGLRenderer.readRenderTargetPixels: renderTarget is not in RGBA or implementation defined format.");let u=1016===c&&(n.has("EXT_color_buffer_half_float")||i.isWebGL2&&n.has("EXT_color_buffer_float"));if(1009!==c&&R.convert(c)!==eA.getParameter(eA.IMPLEMENTATION_COLOR_READ_TYPE)&&!(1015===c&&(i.isWebGL2||n.has("OES_texture_float")||n.has("WEBGL_color_buffer_float")))&&!u)return void console.error("THREE.WebGLRenderer.readRenderTargetPixels: renderTarget is not in UnsignedByteType or implementation defined type.");t>=0&&t<=e.width-a&&r>=0&&r<=e.height-o&&eA.readPixels(t,r,a,o,R.convert(l),R.convert(c),h)}finally{let e=null!==ee?l.get(ee).__webglFramebuffer:null;s.bindFramebuffer(eA.FRAMEBUFFER,e)}}},this.copyFramebufferToTexture=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=Math.pow(2,-n),r=Math.floor(t.image.width*i),a=Math.floor(t.image.height*i);h.setTexture2D(t,0),eA.copyTexSubImage2D(eA.TEXTURE_2D,n,0,0,e.x,e.y,r,a),s.unbindTexture()},this.copyTextureToTexture=function(e,t,n){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,r=t.image.width,a=t.image.height,o=R.convert(n.format),l=R.convert(n.type);h.setTexture2D(n,0),eA.pixelStorei(eA.UNPACK_FLIP_Y_WEBGL,n.flipY),eA.pixelStorei(eA.UNPACK_PREMULTIPLY_ALPHA_WEBGL,n.premultiplyAlpha),eA.pixelStorei(eA.UNPACK_ALIGNMENT,n.unpackAlignment),t.isDataTexture?eA.texSubImage2D(eA.TEXTURE_2D,i,e.x,e.y,r,a,o,l,t.image.data):t.isCompressedTexture?eA.compressedTexSubImage2D(eA.TEXTURE_2D,i,e.x,e.y,t.mipmaps[0].width,t.mipmaps[0].height,o,t.mipmaps[0].data):eA.texSubImage2D(eA.TEXTURE_2D,i,e.x,e.y,o,l,t.image),0===i&&n.generateMipmaps&&eA.generateMipmap(eA.TEXTURE_2D),s.unbindTexture()},this.copyTextureToTexture3D=function(e,t,n,i){let r,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(Z.isWebGL1Renderer)return void console.warn("THREE.WebGLRenderer.copyTextureToTexture3D: can only be used with WebGL2.");let o=e.max.x-e.min.x+1,l=e.max.y-e.min.y+1,c=e.max.z-e.min.z+1,u=R.convert(i.format),d=R.convert(i.type);if(i.isData3DTexture)h.setTexture3D(i,0),r=eA.TEXTURE_3D;else{if(!i.isDataArrayTexture)return void console.warn("THREE.WebGLRenderer.copyTextureToTexture3D: only supports THREE.DataTexture3D and THREE.DataTexture2DArray.");h.setTexture2DArray(i,0),r=eA.TEXTURE_2D_ARRAY}eA.pixelStorei(eA.UNPACK_FLIP_Y_WEBGL,i.flipY),eA.pixelStorei(eA.UNPACK_PREMULTIPLY_ALPHA_WEBGL,i.premultiplyAlpha),eA.pixelStorei(eA.UNPACK_ALIGNMENT,i.unpackAlignment);let p=eA.getParameter(eA.UNPACK_ROW_LENGTH),f=eA.getParameter(eA.UNPACK_IMAGE_HEIGHT),m=eA.getParameter(eA.UNPACK_SKIP_PIXELS),g=eA.getParameter(eA.UNPACK_SKIP_ROWS),_=eA.getParameter(eA.UNPACK_SKIP_IMAGES),v=n.isCompressedTexture?n.mipmaps[0]:n.image;eA.pixelStorei(eA.UNPACK_ROW_LENGTH,v.width),eA.pixelStorei(eA.UNPACK_IMAGE_HEIGHT,v.height),eA.pixelStorei(eA.UNPACK_SKIP_PIXELS,e.min.x),eA.pixelStorei(eA.UNPACK_SKIP_ROWS,e.min.y),eA.pixelStorei(eA.UNPACK_SKIP_IMAGES,e.min.z),n.isDataTexture||n.isData3DTexture?eA.texSubImage3D(r,a,t.x,t.y,t.z,o,l,c,u,d,v.data):n.isCompressedArrayTexture?(console.warn("THREE.WebGLRenderer.copyTextureToTexture3D: untested support for compressed srcTexture."),eA.compressedTexSubImage3D(r,a,t.x,t.y,t.z,o,l,c,u,v.data)):eA.texSubImage3D(r,a,t.x,t.y,t.z,o,l,c,u,d,v),eA.pixelStorei(eA.UNPACK_ROW_LENGTH,p),eA.pixelStorei(eA.UNPACK_IMAGE_HEIGHT,f),eA.pixelStorei(eA.UNPACK_SKIP_PIXELS,m),eA.pixelStorei(eA.UNPACK_SKIP_ROWS,g),eA.pixelStorei(eA.UNPACK_SKIP_IMAGES,_),0===a&&i.generateMipmaps&&eA.generateMipmap(r),s.unbindTexture()},this.initTexture=function(e){e.isCubeTexture?h.setTextureCube(e,0):e.isData3DTexture?h.setTexture3D(e,0):e.isDataArrayTexture||e.isCompressedArrayTexture?h.setTexture2DArray(e,0):h.setTexture2D(e,0),s.unbindTexture()},this.resetState=function(){Q=0,$=0,ee=null,s.reset(),C.reset()},"undefined"!=typeof __THREE_DEVTOOLS__&&__THREE_DEVTOOLS__.dispatchEvent(new CustomEvent("observe",{detail:this}))}}class rl extends ro{}rl.prototype.isWebGL1Renderer=!0;class rh extends ta{copy(e){return super.copy(e),this.color.copy(e.color),this.map=e.map,this.linewidth=e.linewidth,this.linecap=e.linecap,this.linejoin=e.linejoin,this.fog=e.fog,this}constructor(e){super(),this.isLineBasicMaterial=!0,this.type="LineBasicMaterial",this.color=new tn(0xffffff),this.map=null,this.linewidth=1,this.linecap="round",this.linejoin="round",this.fog=!0,this.setValues(e)}}let rc=new Y,ru=new Y,rd=new eT,rp=new eE,rf=new em;class rm extends eJ{copy(e,t){return super.copy(e,t),this.material=Array.isArray(e.material)?e.material.slice():e.material,this.geometry=e.geometry,this}computeLineDistances(){let e=this.geometry;if(null===e.index){let t=e.attributes.position,n=[0];for(let e=1,i=t.count;e<i;e++)rc.fromBufferAttribute(t,e-1),ru.fromBufferAttribute(t,e),n[e]=n[e-1],n[e]+=rc.distanceTo(ru);e.setAttribute("lineDistance",new td(n,1))}else console.warn("THREE.Line.computeLineDistances(): Computation only possible with non-indexed BufferGeometry.");return this}raycast(e,t){let n=this.geometry,i=this.matrixWorld,r=e.params.Line.threshold,a=n.drawRange;if(null===n.boundingSphere&&n.computeBoundingSphere(),rf.copy(n.boundingSphere),rf.applyMatrix4(i),rf.radius+=r,!1===e.ray.intersectsSphere(rf))return;rd.copy(i).invert(),rp.copy(e.ray).applyMatrix4(rd);let s=r/((this.scale.x+this.scale.y+this.scale.z)/3),o=s*s,l=new Y,h=new Y,c=new Y,u=new Y,d=this.isLineSegments?2:1,p=n.index,f=n.attributes.position;if(null!==p){let n=Math.max(0,a.start),i=Math.min(p.count,a.start+a.count);for(let r=n,a=i-1;r<a;r+=d){let n=p.getX(r),i=p.getX(r+1);if(l.fromBufferAttribute(f,n),h.fromBufferAttribute(f,i),rp.distanceSqToSegment(l,h,u,c)>o)continue;u.applyMatrix4(this.matrixWorld);let a=e.ray.origin.distanceTo(u);a<e.near||a>e.far||t.push({distance:a,point:c.clone().applyMatrix4(this.matrixWorld),index:r,face:null,faceIndex:null,object:this})}}else{let n=Math.max(0,a.start),i=Math.min(f.count,a.start+a.count);for(let r=n,a=i-1;r<a;r+=d){if(l.fromBufferAttribute(f,r),h.fromBufferAttribute(f,r+1),rp.distanceSqToSegment(l,h,u,c)>o)continue;u.applyMatrix4(this.matrixWorld);let n=e.ray.origin.distanceTo(u);n<e.near||n>e.far||t.push({distance:n,point:c.clone().applyMatrix4(this.matrixWorld),index:r,face:null,faceIndex:null,object:this})}}}updateMorphTargets(){let e=this.geometry.morphAttributes,t=Object.keys(e);if(t.length>0){let n=e[t[0]];if(void 0!==n){this.morphTargetInfluences=[],this.morphTargetDictionary={};for(let e=0,t=n.length;e<t;e++){let t=n[e].name||String(e);this.morphTargetInfluences.push(0),this.morphTargetDictionary[t]=e}}}}constructor(e=new ty,t=new rh){super(),this.isLine=!0,this.type="Line",this.geometry=e,this.material=t,this.updateMorphTargets()}}class rg{getPoint(){return console.warn("THREE.Curve: .getPoint() not implemented."),null}getPointAt(e,t){let n=this.getUtoTmapping(e);return this.getPoint(n,t)}getPoints(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:5,t=[];for(let n=0;n<=e;n++)t.push(this.getPoint(n/e));return t}getSpacedPoints(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:5,t=[];for(let n=0;n<=e;n++)t.push(this.getPointAt(n/e));return t}getLength(){let e=this.getLengths();return e[e.length-1]}getLengths(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.arcLengthDivisions;if(this.cacheArcLengths&&this.cacheArcLengths.length===e+1&&!this.needsUpdate)return this.cacheArcLengths;this.needsUpdate=!1;let t=[],n,i=this.getPoint(0),r=0;t.push(0);for(let a=1;a<=e;a++)t.push(r+=(n=this.getPoint(a/e)).distanceTo(i)),i=n;return this.cacheArcLengths=t,t}updateArcLengths(){this.needsUpdate=!0,this.getLengths()}getUtoTmapping(e,t){let n,i=this.getLengths(),r=0,a=i.length;n=t||e*i[a-1];let s=0,o=a-1,l;for(;s<=o;)if((l=i[r=Math.floor(s+(o-s)/2)]-n)<0)s=r+1;else if(l>0)o=r-1;else{o=r;break}if(i[r=o]===n)return r/(a-1);let h=i[r],c=i[r+1];return(r+(n-h)/(c-h))/(a-1)}getTangent(e,t){let n=e-1e-4,i=e+1e-4;n<0&&(n=0),i>1&&(i=1);let r=this.getPoint(n),a=this.getPoint(i),s=t||(r.isVector2?new S:new Y);return s.copy(a).sub(r).normalize(),s}getTangentAt(e,t){let n=this.getUtoTmapping(e);return this.getTangent(n,t)}computeFrenetFrames(e,t){let n=new Y,i=[],r=[],a=[],s=new Y,o=new eT;for(let t=0;t<=e;t++){let n=t/e;i[t]=this.getTangentAt(n,new Y)}r[0]=new Y,a[0]=new Y;let l=Number.MAX_VALUE,h=Math.abs(i[0].x),c=Math.abs(i[0].y),u=Math.abs(i[0].z);h<=l&&(l=h,n.set(1,0,0)),c<=l&&(l=c,n.set(0,1,0)),u<=l&&n.set(0,0,1),s.crossVectors(i[0],n).normalize(),r[0].crossVectors(i[0],s),a[0].crossVectors(i[0],r[0]);for(let t=1;t<=e;t++){if(r[t]=r[t-1].clone(),a[t]=a[t-1].clone(),s.crossVectors(i[t-1],i[t]),s.length()>Number.EPSILON){s.normalize();let e=Math.acos(_(i[t-1].dot(i[t]),-1,1));r[t].applyMatrix4(o.makeRotationAxis(s,e))}a[t].crossVectors(i[t],r[t])}if(!0===t){let t=Math.acos(_(r[0].dot(r[e]),-1,1));t/=e,i[0].dot(s.crossVectors(r[0],r[e]))>0&&(t=-t);for(let n=1;n<=e;n++)r[n].applyMatrix4(o.makeRotationAxis(i[n],t*n)),a[n].crossVectors(i[n],r[n])}return{tangents:i,normals:r,binormals:a}}clone(){return new this.constructor().copy(this)}copy(e){return this.arcLengthDivisions=e.arcLengthDivisions,this}toJSON(){let e={metadata:{version:4.6,type:"Curve",generator:"Curve.toJSON"}};return e.arcLengthDivisions=this.arcLengthDivisions,e.type=this.type,e}fromJSON(e){return this.arcLengthDivisions=e.arcLengthDivisions,this}constructor(){this.type="Curve",this.arcLengthDivisions=200}}class r_ extends rg{getPoint(e,t){let n=t||new S,i=2*Math.PI,r=this.aEndAngle-this.aStartAngle,a=Math.abs(r)<Number.EPSILON;for(;r<0;)r+=i;for(;r>i;)r-=i;r<Number.EPSILON&&(r=a?0:i),!0!==this.aClockwise||a||(r===i?r=-i:r-=i);let s=this.aStartAngle+e*r,o=this.aX+this.xRadius*Math.cos(s),l=this.aY+this.yRadius*Math.sin(s);if(0!==this.aRotation){let e=Math.cos(this.aRotation),t=Math.sin(this.aRotation),n=o-this.aX,i=l-this.aY;o=n*e-i*t+this.aX,l=n*t+i*e+this.aY}return n.set(o,l)}copy(e){return super.copy(e),this.aX=e.aX,this.aY=e.aY,this.xRadius=e.xRadius,this.yRadius=e.yRadius,this.aStartAngle=e.aStartAngle,this.aEndAngle=e.aEndAngle,this.aClockwise=e.aClockwise,this.aRotation=e.aRotation,this}toJSON(){let e=super.toJSON();return e.aX=this.aX,e.aY=this.aY,e.xRadius=this.xRadius,e.yRadius=this.yRadius,e.aStartAngle=this.aStartAngle,e.aEndAngle=this.aEndAngle,e.aClockwise=this.aClockwise,e.aRotation=this.aRotation,e}fromJSON(e){return super.fromJSON(e),this.aX=e.aX,this.aY=e.aY,this.xRadius=e.xRadius,this.yRadius=e.yRadius,this.aStartAngle=e.aStartAngle,this.aEndAngle=e.aEndAngle,this.aClockwise=e.aClockwise,this.aRotation=e.aRotation,this}constructor(e=0,t=0,n=1,i=1,r=0,a=2*Math.PI,s=!1,o=0){super(),this.isEllipseCurve=!0,this.type="EllipseCurve",this.aX=e,this.aY=t,this.xRadius=n,this.yRadius=i,this.aStartAngle=r,this.aEndAngle=a,this.aClockwise=s,this.aRotation=o}}class rv extends r_{constructor(e,t,n,i,r,a){super(e,t,n,n,i,r,a),this.isArcCurve=!0,this.type="ArcCurve"}}function rx(){let e=0,t=0,n=0,i=0;function r(r,a,s,o){e=r,t=s,n=-3*r+3*a-2*s-o,i=2*r-2*a+s+o}return{initCatmullRom:function(e,t,n,i,a){r(t,n,a*(n-e),a*(i-t))},initNonuniformCatmullRom:function(e,t,n,i,a,s,o){let l=(t-e)/a-(n-e)/(a+s)+(n-t)/s,h=(n-t)/s-(i-t)/(s+o)+(i-n)/o;r(t,n,l*=s,h*=s)},calc:function(r){let a=r*r;return e+t*r+n*a+a*r*i}}}let ry=new Y,rM=new rx,rS=new rx,rE=new rx;class rT extends rg{getPoint(e){let t,n,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Y,r=this.points,a=r.length,s=(a-!this.closed)*e,o=Math.floor(s),l=s-o;this.closed?o+=o>0?0:(Math.floor(Math.abs(o)/a)+1)*a:0===l&&o===a-1&&(o=a-2,l=1),this.closed||o>0?t=r[(o-1)%a]:(ry.subVectors(r[0],r[1]).add(r[0]),t=ry);let h=r[o%a],c=r[(o+1)%a];if(this.closed||o+2<a?n=r[(o+2)%a]:(ry.subVectors(r[a-1],r[a-2]).add(r[a-1]),n=ry),"centripetal"===this.curveType||"chordal"===this.curveType){let e="chordal"===this.curveType?.5:.25,i=Math.pow(t.distanceToSquared(h),e),r=Math.pow(h.distanceToSquared(c),e),a=Math.pow(c.distanceToSquared(n),e);r<1e-4&&(r=1),i<1e-4&&(i=r),a<1e-4&&(a=r),rM.initNonuniformCatmullRom(t.x,h.x,c.x,n.x,i,r,a),rS.initNonuniformCatmullRom(t.y,h.y,c.y,n.y,i,r,a),rE.initNonuniformCatmullRom(t.z,h.z,c.z,n.z,i,r,a)}else"catmullrom"===this.curveType&&(rM.initCatmullRom(t.x,h.x,c.x,n.x,this.tension),rS.initCatmullRom(t.y,h.y,c.y,n.y,this.tension),rE.initCatmullRom(t.z,h.z,c.z,n.z,this.tension));return i.set(rM.calc(l),rS.calc(l),rE.calc(l)),i}copy(e){super.copy(e),this.points=[];for(let t=0,n=e.points.length;t<n;t++){let n=e.points[t];this.points.push(n.clone())}return this.closed=e.closed,this.curveType=e.curveType,this.tension=e.tension,this}toJSON(){let e=super.toJSON();e.points=[];for(let t=0,n=this.points.length;t<n;t++){let n=this.points[t];e.points.push(n.toArray())}return e.closed=this.closed,e.curveType=this.curveType,e.tension=this.tension,e}fromJSON(e){super.fromJSON(e),this.points=[];for(let t=0,n=e.points.length;t<n;t++){let n=e.points[t];this.points.push(new Y().fromArray(n))}return this.closed=e.closed,this.curveType=e.curveType,this.tension=e.tension,this}constructor(e=[],t=!1,n="centripetal",i=.5){super(),this.isCatmullRomCurve3=!0,this.type="CatmullRomCurve3",this.points=e,this.closed=t,this.curveType=n,this.tension=i}}function rb(e,t,n,i,r){let a=(i-t)*.5,s=(r-n)*.5,o=e*e;return e*o*(2*n-2*i+a+s)+(-3*n+3*i-2*a-s)*o+a*e+n}function rA(e,t,n,i){return function(e,t){let n=1-e;return n*n*t}(e,t)+2*(1-e)*e*n+e*e*i}function rw(e,t,n,i,r){return function(e,t){let n=1-e;return n*n*n*t}(e,t)+function(e,t){let n=1-e;return 3*n*n*e*t}(e,n)+3*(1-e)*e*e*i+e*e*e*r}class rR extends rg{getPoint(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new S,n=this.v0,i=this.v1,r=this.v2,a=this.v3;return t.set(rw(e,n.x,i.x,r.x,a.x),rw(e,n.y,i.y,r.y,a.y)),t}copy(e){return super.copy(e),this.v0.copy(e.v0),this.v1.copy(e.v1),this.v2.copy(e.v2),this.v3.copy(e.v3),this}toJSON(){let e=super.toJSON();return e.v0=this.v0.toArray(),e.v1=this.v1.toArray(),e.v2=this.v2.toArray(),e.v3=this.v3.toArray(),e}fromJSON(e){return super.fromJSON(e),this.v0.fromArray(e.v0),this.v1.fromArray(e.v1),this.v2.fromArray(e.v2),this.v3.fromArray(e.v3),this}constructor(e=new S,t=new S,n=new S,i=new S){super(),this.isCubicBezierCurve=!0,this.type="CubicBezierCurve",this.v0=e,this.v1=t,this.v2=n,this.v3=i}}class rC extends rg{getPoint(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Y,n=this.v0,i=this.v1,r=this.v2,a=this.v3;return t.set(rw(e,n.x,i.x,r.x,a.x),rw(e,n.y,i.y,r.y,a.y),rw(e,n.z,i.z,r.z,a.z)),t}copy(e){return super.copy(e),this.v0.copy(e.v0),this.v1.copy(e.v1),this.v2.copy(e.v2),this.v3.copy(e.v3),this}toJSON(){let e=super.toJSON();return e.v0=this.v0.toArray(),e.v1=this.v1.toArray(),e.v2=this.v2.toArray(),e.v3=this.v3.toArray(),e}fromJSON(e){return super.fromJSON(e),this.v0.fromArray(e.v0),this.v1.fromArray(e.v1),this.v2.fromArray(e.v2),this.v3.fromArray(e.v3),this}constructor(e=new Y,t=new Y,n=new Y,i=new Y){super(),this.isCubicBezierCurve3=!0,this.type="CubicBezierCurve3",this.v0=e,this.v1=t,this.v2=n,this.v3=i}}class rL extends rg{getPoint(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new S;return 1===e?t.copy(this.v2):(t.copy(this.v2).sub(this.v1),t.multiplyScalar(e).add(this.v1)),t}getPointAt(e,t){return this.getPoint(e,t)}getTangent(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new S;return t.subVectors(this.v2,this.v1).normalize()}getTangentAt(e,t){return this.getTangent(e,t)}copy(e){return super.copy(e),this.v1.copy(e.v1),this.v2.copy(e.v2),this}toJSON(){let e=super.toJSON();return e.v1=this.v1.toArray(),e.v2=this.v2.toArray(),e}fromJSON(e){return super.fromJSON(e),this.v1.fromArray(e.v1),this.v2.fromArray(e.v2),this}constructor(e=new S,t=new S){super(),this.isLineCurve=!0,this.type="LineCurve",this.v1=e,this.v2=t}}class rP extends rg{getPoint(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Y;return 1===e?t.copy(this.v2):(t.copy(this.v2).sub(this.v1),t.multiplyScalar(e).add(this.v1)),t}getPointAt(e,t){return this.getPoint(e,t)}getTangent(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Y;return t.subVectors(this.v2,this.v1).normalize()}getTangentAt(e,t){return this.getTangent(e,t)}copy(e){return super.copy(e),this.v1.copy(e.v1),this.v2.copy(e.v2),this}toJSON(){let e=super.toJSON();return e.v1=this.v1.toArray(),e.v2=this.v2.toArray(),e}fromJSON(e){return super.fromJSON(e),this.v1.fromArray(e.v1),this.v2.fromArray(e.v2),this}constructor(e=new Y,t=new Y){super(),this.isLineCurve3=!0,this.type="LineCurve3",this.v1=e,this.v2=t}}class rU extends rg{getPoint(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new S,n=this.v0,i=this.v1,r=this.v2;return t.set(rA(e,n.x,i.x,r.x),rA(e,n.y,i.y,r.y)),t}copy(e){return super.copy(e),this.v0.copy(e.v0),this.v1.copy(e.v1),this.v2.copy(e.v2),this}toJSON(){let e=super.toJSON();return e.v0=this.v0.toArray(),e.v1=this.v1.toArray(),e.v2=this.v2.toArray(),e}fromJSON(e){return super.fromJSON(e),this.v0.fromArray(e.v0),this.v1.fromArray(e.v1),this.v2.fromArray(e.v2),this}constructor(e=new S,t=new S,n=new S){super(),this.isQuadraticBezierCurve=!0,this.type="QuadraticBezierCurve",this.v0=e,this.v1=t,this.v2=n}}class rN extends rg{getPoint(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Y,n=this.v0,i=this.v1,r=this.v2;return t.set(rA(e,n.x,i.x,r.x),rA(e,n.y,i.y,r.y),rA(e,n.z,i.z,r.z)),t}copy(e){return super.copy(e),this.v0.copy(e.v0),this.v1.copy(e.v1),this.v2.copy(e.v2),this}toJSON(){let e=super.toJSON();return e.v0=this.v0.toArray(),e.v1=this.v1.toArray(),e.v2=this.v2.toArray(),e}fromJSON(e){return super.fromJSON(e),this.v0.fromArray(e.v0),this.v1.fromArray(e.v1),this.v2.fromArray(e.v2),this}constructor(e=new Y,t=new Y,n=new Y){super(),this.isQuadraticBezierCurve3=!0,this.type="QuadraticBezierCurve3",this.v0=e,this.v1=t,this.v2=n}}class rD extends rg{getPoint(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new S,n=this.points,i=(n.length-1)*e,r=Math.floor(i),a=i-r,s=n[0===r?r:r-1],o=n[r],l=n[r>n.length-2?n.length-1:r+1],h=n[r>n.length-3?n.length-1:r+2];return t.set(rb(a,s.x,o.x,l.x,h.x),rb(a,s.y,o.y,l.y,h.y)),t}copy(e){super.copy(e),this.points=[];for(let t=0,n=e.points.length;t<n;t++){let n=e.points[t];this.points.push(n.clone())}return this}toJSON(){let e=super.toJSON();e.points=[];for(let t=0,n=this.points.length;t<n;t++){let n=this.points[t];e.points.push(n.toArray())}return e}fromJSON(e){super.fromJSON(e),this.points=[];for(let t=0,n=e.points.length;t<n;t++){let n=e.points[t];this.points.push(new S().fromArray(n))}return this}constructor(e=[]){super(),this.isSplineCurve=!0,this.type="SplineCurve",this.points=e}}var rI=Object.freeze({__proto__:null,ArcCurve:rv,CatmullRomCurve3:rT,CubicBezierCurve:rR,CubicBezierCurve3:rC,EllipseCurve:r_,LineCurve:rL,LineCurve3:rP,QuadraticBezierCurve:rU,QuadraticBezierCurve3:rN,SplineCurve:rD});class rO extends rg{add(e){this.curves.push(e)}closePath(){let e=this.curves[0].getPoint(0),t=this.curves[this.curves.length-1].getPoint(1);if(!e.equals(t)){let n=!0===e.isVector2?"LineCurve":"LineCurve3";this.curves.push(new rI[n](t,e))}return this}getPoint(e,t){let n=e*this.getLength(),i=this.getCurveLengths(),r=0;for(;r<i.length;){if(i[r]>=n){let e=i[r]-n,a=this.curves[r],s=a.getLength(),o=0===s?0:1-e/s;return a.getPointAt(o,t)}r++}return null}getLength(){let e=this.getCurveLengths();return e[e.length-1]}updateArcLengths(){this.needsUpdate=!0,this.cacheLengths=null,this.getCurveLengths()}getCurveLengths(){if(this.cacheLengths&&this.cacheLengths.length===this.curves.length)return this.cacheLengths;let e=[],t=0;for(let n=0,i=this.curves.length;n<i;n++)e.push(t+=this.curves[n].getLength());return this.cacheLengths=e,e}getSpacedPoints(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:40,t=[];for(let n=0;n<=e;n++)t.push(this.getPoint(n/e));return this.autoClose&&t.push(t[0]),t}getPoints(){let e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:12,n=[];for(let i=0,r=this.curves;i<r.length;i++){let a=r[i],s=a.isEllipseCurve?2*t:a.isLineCurve||a.isLineCurve3?1:a.isSplineCurve?t*a.points.length:t,o=a.getPoints(s);for(let t=0;t<o.length;t++){let i=o[t];e&&e.equals(i)||(n.push(i),e=i)}}return this.autoClose&&n.length>1&&!n[n.length-1].equals(n[0])&&n.push(n[0]),n}copy(e){super.copy(e),this.curves=[];for(let t=0,n=e.curves.length;t<n;t++){let n=e.curves[t];this.curves.push(n.clone())}return this.autoClose=e.autoClose,this}toJSON(){let e=super.toJSON();e.autoClose=this.autoClose,e.curves=[];for(let t=0,n=this.curves.length;t<n;t++){let n=this.curves[t];e.curves.push(n.toJSON())}return e}fromJSON(e){super.fromJSON(e),this.autoClose=e.autoClose,this.curves=[];for(let t=0,n=e.curves.length;t<n;t++){let n=e.curves[t];this.curves.push(new rI[n.type]().fromJSON(n))}return this}constructor(){super(),this.type="CurvePath",this.curves=[],this.autoClose=!1}}class rF extends rO{setFromPoints(e){this.moveTo(e[0].x,e[0].y);for(let t=1,n=e.length;t<n;t++)this.lineTo(e[t].x,e[t].y);return this}moveTo(e,t){return this.currentPoint.set(e,t),this}lineTo(e,t){let n=new rL(this.currentPoint.clone(),new S(e,t));return this.curves.push(n),this.currentPoint.set(e,t),this}quadraticCurveTo(e,t,n,i){let r=new rU(this.currentPoint.clone(),new S(e,t),new S(n,i));return this.curves.push(r),this.currentPoint.set(n,i),this}bezierCurveTo(e,t,n,i,r,a){let s=new rR(this.currentPoint.clone(),new S(e,t),new S(n,i),new S(r,a));return this.curves.push(s),this.currentPoint.set(r,a),this}splineThru(e){let t=new rD([this.currentPoint.clone()].concat(e));return this.curves.push(t),this.currentPoint.copy(e[e.length-1]),this}arc(e,t,n,i,r,a){let s=this.currentPoint.x,o=this.currentPoint.y;return this.absarc(e+s,t+o,n,i,r,a),this}absarc(e,t,n,i,r,a){return this.absellipse(e,t,n,n,i,r,a),this}ellipse(e,t,n,i,r,a,s,o){let l=this.currentPoint.x,h=this.currentPoint.y;return this.absellipse(e+l,t+h,n,i,r,a,s,o),this}absellipse(e,t,n,i,r,a,s,o){let l=new r_(e,t,n,i,r,a,s,o);if(this.curves.length>0){let e=l.getPoint(0);e.equals(this.currentPoint)||this.lineTo(e.x,e.y)}this.curves.push(l);let h=l.getPoint(1);return this.currentPoint.copy(h),this}copy(e){return super.copy(e),this.currentPoint.copy(e.currentPoint),this}toJSON(){let e=super.toJSON();return e.currentPoint=this.currentPoint.toArray(),e}fromJSON(e){return super.fromJSON(e),this.currentPoint.fromArray(e.currentPoint),this}constructor(e){super(),this.type="Path",this.currentPoint=new S,e&&this.setFromPoints(e)}}class rz extends ty{copy(e){return super.copy(e),this.parameters=Object.assign({},e.parameters),this}static fromJSON(e){return new rz(e.points,e.segments,e.phiStart,e.phiLength)}constructor(e=[new S(0,-.5),new S(.5,0),new S(0,.5)],t=12,n=0,i=2*Math.PI){super(),this.type="LatheGeometry",this.parameters={points:e,segments:t,phiStart:n,phiLength:i},t=Math.floor(t),i=_(i,0,2*Math.PI);let r=[],a=[],s=[],o=[],l=[],h=1/t,c=new Y,u=new S,d=new Y,p=new Y,f=new Y,m=0,g=0;for(let t=0;t<=e.length-1;t++)switch(t){case 0:m=e[t+1].x-e[t].x,d.x=+(g=e[t+1].y-e[t].y),d.y=-m,d.z=0*g,f.copy(d),d.normalize(),o.push(d.x,d.y,d.z);break;case e.length-1:o.push(f.x,f.y,f.z);break;default:m=e[t+1].x-e[t].x,d.x=+(g=e[t+1].y-e[t].y),d.y=-m,d.z=0*g,p.copy(d),d.x+=f.x,d.y+=f.y,d.z+=f.z,d.normalize(),o.push(d.x,d.y,d.z),f.copy(p)}for(let r=0;r<=t;r++){let d=n+r*h*i,p=Math.sin(d),f=Math.cos(d);for(let n=0;n<=e.length-1;n++){c.x=e[n].x*p,c.y=e[n].y,c.z=e[n].x*f,a.push(c.x,c.y,c.z),u.x=r/t,u.y=n/(e.length-1),s.push(u.x,u.y);let i=o[3*n+0]*p,h=o[3*n+1],d=o[3*n+0]*f;l.push(i,h,d)}}for(let n=0;n<t;n++)for(let t=0;t<e.length-1;t++){let i=t+n*e.length,a=i+e.length,s=i+e.length+1,o=i+1;r.push(i,a,o),r.push(s,o,a)}this.setIndex(r),this.setAttribute("position",new td(a,3)),this.setAttribute("uv",new td(s,2)),this.setAttribute("normal",new td(l,3))}}class rB extends rz{static fromJSON(e){return new rB(e.radius,e.length,e.capSegments,e.radialSegments)}constructor(e=1,t=1,n=4,i=8){let r=new rF;r.absarc(0,-t/2,e,1.5*Math.PI,0),r.absarc(0,t/2,e,0,.5*Math.PI),super(r.getPoints(n),i),this.type="CapsuleGeometry",this.parameters={radius:e,length:t,capSegments:n,radialSegments:i}}}class rH extends ty{copy(e){return super.copy(e),this.parameters=Object.assign({},e.parameters),this}static fromJSON(e){return new rH(e.radius,e.segments,e.thetaStart,e.thetaLength)}constructor(e=1,t=32,n=0,i=2*Math.PI){super(),this.type="CircleGeometry",this.parameters={radius:e,segments:t,thetaStart:n,thetaLength:i},t=Math.max(3,t);let r=[],a=[],s=[],o=[],l=new Y,h=new S;a.push(0,0,0),s.push(0,0,1),o.push(.5,.5);for(let r=0,c=3;r<=t;r++,c+=3){let u=n+r/t*i;l.x=e*Math.cos(u),l.y=e*Math.sin(u),a.push(l.x,l.y,l.z),s.push(0,0,1),h.x=(a[c]/e+1)/2,h.y=(a[c+1]/e+1)/2,o.push(h.x,h.y)}for(let e=1;e<=t;e++)r.push(e,e+1,0);this.setIndex(r),this.setAttribute("position",new td(a,3)),this.setAttribute("normal",new td(s,3)),this.setAttribute("uv",new td(o,2))}}class rV extends ty{copy(e){return super.copy(e),this.parameters=Object.assign({},e.parameters),this}static fromJSON(e){return new rV(e.radiusTop,e.radiusBottom,e.height,e.radialSegments,e.heightSegments,e.openEnded,e.thetaStart,e.thetaLength)}constructor(e=1,t=1,n=1,i=32,r=1,a=!1,s=0,o=2*Math.PI){super(),this.type="CylinderGeometry",this.parameters={radiusTop:e,radiusBottom:t,height:n,radialSegments:i,heightSegments:r,openEnded:a,thetaStart:s,thetaLength:o};let l=this;i=Math.floor(i),r=Math.floor(r);let h=[],c=[],u=[],d=[],p=0,f=[],m=n/2,g=0;function _(n){let r=p,a=new S,f=new Y,_=0,v=!0===n?e:t,x=!0===n?1:-1;for(let e=1;e<=i;e++)c.push(0,m*x,0),u.push(0,x,0),d.push(.5,.5),p++;let y=p;for(let e=0;e<=i;e++){let t=e/i*o+s,n=Math.cos(t),r=Math.sin(t);f.x=v*r,f.y=m*x,f.z=v*n,c.push(f.x,f.y,f.z),u.push(0,x,0),a.x=.5*n+.5,a.y=.5*r*x+.5,d.push(a.x,a.y),p++}for(let e=0;e<i;e++){let t=r+e,i=y+e;!0===n?h.push(i,i+1,t):h.push(i+1,i,t),_+=3}l.addGroup(g,_,!0===n?1:2),g+=_}(function(){let a=new Y,_=new Y,v=0,x=(t-e)/n;for(let l=0;l<=r;l++){let h=[],g=l/r,v=g*(t-e)+e;for(let e=0;e<=i;e++){let t=e/i,r=t*o+s,l=Math.sin(r),f=Math.cos(r);_.x=v*l,_.y=-g*n+m,_.z=v*f,c.push(_.x,_.y,_.z),a.set(l,x,f).normalize(),u.push(a.x,a.y,a.z),d.push(t,1-g),h.push(p++)}f.push(h)}for(let e=0;e<i;e++)for(let t=0;t<r;t++){let n=f[t][e],i=f[t+1][e],r=f[t+1][e+1],a=f[t][e+1];h.push(n,i,a),h.push(i,r,a),v+=6}l.addGroup(g,v,0),g+=v})(),!1===a&&(e>0&&_(!0),t>0&&_(!1)),this.setIndex(h),this.setAttribute("position",new td(c,3)),this.setAttribute("normal",new td(u,3)),this.setAttribute("uv",new td(d,2))}}class rG extends rV{static fromJSON(e){return new rG(e.radius,e.height,e.radialSegments,e.heightSegments,e.openEnded,e.thetaStart,e.thetaLength)}constructor(e=1,t=1,n=32,i=1,r=!1,a=0,s=2*Math.PI){super(0,e,t,n,i,r,a,s),this.type="ConeGeometry",this.parameters={radius:e,height:t,radialSegments:n,heightSegments:i,openEnded:r,thetaStart:a,thetaLength:s}}}class rk extends ty{copy(e){return super.copy(e),this.parameters=Object.assign({},e.parameters),this}static fromJSON(e){return new rk(e.vertices,e.indices,e.radius,e.details)}constructor(e=[],t=[],n=1,i=0){super(),this.type="PolyhedronGeometry",this.parameters={vertices:e,indices:t,radius:n,detail:i};let r=[],a=[];function s(e){r.push(e.x,e.y,e.z)}function o(t,n){let i=3*t;n.x=e[i+0],n.y=e[i+1],n.z=e[i+2]}function l(e,t,n,i){i<0&&1===e.x&&(a[t]=e.x-1),0===n.x&&0===n.z&&(a[t]=i/2/Math.PI+.5)}function h(e){return Math.atan2(e.z,-e.x)}(function(e){let n=new Y,i=new Y,r=new Y;for(let a=0;a<t.length;a+=3)o(t[a+0],n),o(t[a+1],i),o(t[a+2],r),function(e,t,n,i){let r=i+1,a=[];for(let i=0;i<=r;i++){a[i]=[];let s=e.clone().lerp(n,i/r),o=t.clone().lerp(n,i/r),l=r-i;for(let e=0;e<=l;e++)0===e&&i===r?a[i][e]=s:a[i][e]=s.clone().lerp(o,e/l)}for(let e=0;e<r;e++)for(let t=0;t<2*(r-e)-1;t++){let n=Math.floor(t/2);t%2==0?(s(a[e][n+1]),s(a[e+1][n]),s(a[e][n])):(s(a[e][n+1]),s(a[e+1][n+1]),s(a[e+1][n]))}}(n,i,r,e)})(i),function(e){let t=new Y;for(let n=0;n<r.length;n+=3)t.x=r[n+0],t.y=r[n+1],t.z=r[n+2],t.normalize().multiplyScalar(e),r[n+0]=t.x,r[n+1]=t.y,r[n+2]=t.z}(n),function(){let e=new Y;for(let n=0;n<r.length;n+=3){var t;e.x=r[n+0],e.y=r[n+1],e.z=r[n+2];let i=h(e)/2/Math.PI+.5,s=Math.atan2(-(t=e).y,Math.sqrt(t.x*t.x+t.z*t.z))/Math.PI+.5;a.push(i,1-s)}(function(){let e=new Y,t=new Y,n=new Y,i=new Y,s=new S,o=new S,c=new S;for(let u=0,d=0;u<r.length;u+=9,d+=6){e.set(r[u+0],r[u+1],r[u+2]),t.set(r[u+3],r[u+4],r[u+5]),n.set(r[u+6],r[u+7],r[u+8]),s.set(a[d+0],a[d+1]),o.set(a[d+2],a[d+3]),c.set(a[d+4],a[d+5]),i.copy(e).add(t).add(n).divideScalar(3);let p=h(i);l(s,d+0,e,p),l(o,d+2,t,p),l(c,d+4,n,p)}})(),function(){for(let e=0;e<a.length;e+=6){let t=a[e+0],n=a[e+2],i=a[e+4],r=Math.max(t,n,i),s=Math.min(t,n,i);r>.9&&s<.1&&(t<.2&&(a[e+0]+=1),n<.2&&(a[e+2]+=1),i<.2&&(a[e+4]+=1))}}()}(),this.setAttribute("position",new td(r,3)),this.setAttribute("normal",new td(r.slice(),3)),this.setAttribute("uv",new td(a,2)),0===i?this.computeVertexNormals():this.normalizeNormals()}}class rW extends rk{static fromJSON(e){return new rW(e.radius,e.detail)}constructor(e=1,t=0){let n=(1+Math.sqrt(5))/2,i=1/n;super([-1,-1,-1,-1,-1,1,-1,1,-1,-1,1,1,1,-1,-1,1,-1,1,1,1,-1,1,1,1,0,-i,-n,0,-i,n,0,i,-n,0,i,n,-i,-n,0,-i,n,0,i,-n,0,i,n,0,-n,0,-i,n,0,-i,-n,0,i,n,0,i],[3,11,7,3,7,15,3,15,13,7,19,17,7,17,6,7,6,15,17,4,8,17,8,10,17,10,6,8,0,16,8,16,2,8,2,10,0,12,1,0,1,18,0,18,16,6,10,2,6,2,13,6,13,15,2,16,18,2,18,3,2,3,13,18,1,9,18,9,11,18,11,3,4,14,12,4,12,0,4,0,8,11,9,5,11,5,19,11,19,7,19,5,14,19,14,4,19,4,17,1,12,14,1,14,5,1,5,9],e,t),this.type="DodecahedronGeometry",this.parameters={radius:e,detail:t}}}let rX=new Y,rj=new Y,rq=new Y,rY=new e8;class rJ extends rF{getPointsHoles(e){let t=[];for(let n=0,i=this.holes.length;n<i;n++)t[n]=this.holes[n].getPoints(e);return t}extractPoints(e){return{shape:this.getPoints(e),holes:this.getPointsHoles(e)}}copy(e){super.copy(e),this.holes=[];for(let t=0,n=e.holes.length;t<n;t++){let n=e.holes[t];this.holes.push(n.clone())}return this}toJSON(){let e=super.toJSON();e.uuid=this.uuid,e.holes=[];for(let t=0,n=this.holes.length;t<n;t++){let n=this.holes[t];e.holes.push(n.toJSON())}return e}fromJSON(e){super.fromJSON(e),this.uuid=e.uuid,this.holes=[];for(let t=0,n=e.holes.length;t<n;t++){let n=e.holes[t];this.holes.push(new rF().fromJSON(n))}return this}constructor(e){super(e),this.uuid=g(),this.type="Shape",this.holes=[]}}let rZ={triangulate:function(e,t){let n,i,r,a,s,o,l,h=arguments.length>2&&void 0!==arguments[2]?arguments[2]:2,c=t&&t.length,u=c?t[0]*h:e.length,d=rK(e,0,u,h,!0),p=[];if(!d||d.next===d.prev)return p;if(c&&(d=function(e,t,n,i){let r,a,s,o,l,h=[];for(r=0,a=t.length;r<a;r++)s=t[r]*i,o=r<a-1?t[r+1]*i:e.length,(l=rK(e,s,o,i,!1))===l.next&&(l.steiner=!0),h.push(function(e){let t=e,n=e;do(t.x<n.x||t.x===n.x&&t.y<n.y)&&(n=t),t=t.next;while(t!==e);return n}(l));for(h.sort(r$),r=0;r<h.length;r++)n=function(e,t){let n=function(e,t){let n=t,i=-1/0,r,a=e.x,s=e.y;do{if(s<=n.y&&s>=n.next.y&&n.next.y!==n.y){let e=n.x+(s-n.y)*(n.next.x-n.x)/(n.next.y-n.y);if(e<=a&&e>i&&(i=e,r=n.x<n.next.x?n:n.next,e===a))return r}n=n.next}while(n!==t);if(!r)return null;let o=r,l=r.x,h=r.y,c=1/0,u;n=r;do{var d,p;a>=n.x&&n.x>=l&&a!==n.x&&r1(s<h?a:i,s,l,h,s<h?i:a,s,n.x,n.y)&&(u=Math.abs(s-n.y)/(a-n.x),r8(n,e)&&(u<c||u===c&&(n.x>r.x||n.x===r.x&&(d=r,p=n,0>r2(d.prev,d,p.prev)&&0>r2(p.next,d,d.next))))&&(r=n,c=u)),n=n.next}while(n!==o);return r}(e,t);if(!n)return t;let i=r9(n,e);return rQ(i,i.next),rQ(n,n.next)}(h[r],n);return n}(e,t,d,h)),e.length>80*h){n=r=e[0],i=a=e[1];for(let t=h;t<u;t+=h)s=e[t],o=e[t+1],s<n&&(n=s),o<i&&(i=o),s>r&&(r=s),o>a&&(a=o);l=0!==(l=Math.max(r-n,a-i))?32767/l:0}return function e(t,n,i,r,a,s,o){if(!t)return;!o&&s&&function(e,t,n,i){let r=e;do 0===r.z&&(r.z=r0(r.x,r.y,t,n,i)),r.prevZ=r.prev,r.nextZ=r.next,r=r.next;while(r!==e);r.prevZ.nextZ=null,r.prevZ=null,function(e){let t,n,i,r,a,s,o,l,h=1;do{for(n=e,e=null,a=null,s=0;n;){for(s++,i=n,o=0,t=0;t<h&&(o++,i=i.nextZ);t++);for(l=h;o>0||l>0&&i;)0!==o&&(0===l||!i||n.z<=i.z)?(r=n,n=n.nextZ,o--):(r=i,i=i.nextZ,l--),a?a.nextZ=r:e=r,r.prevZ=a,a=r;n=i}a.nextZ=null,h*=2}while(s>1)}(r)}(t,r,a,s);let l=t,h,c;for(;t.prev!==t.next;){if(h=t.prev,c=t.next,s?function(e,t,n,i){let r=e.prev,a=e.next;if(r2(r,e,a)>=0)return!1;let s=r.x,o=e.x,l=a.x,h=r.y,c=e.y,u=a.y,d=s<o?s<l?s:l:o<l?o:l,p=h<c?h<u?h:u:c<u?c:u,f=s>o?s>l?s:l:o>l?o:l,m=h>c?h>u?h:u:c>u?c:u,g=r0(d,p,t,n,i),_=r0(f,m,t,n,i),v=e.prevZ,x=e.nextZ;for(;v&&v.z>=g&&x&&x.z<=_;){if(v.x>=d&&v.x<=f&&v.y>=p&&v.y<=m&&v!==r&&v!==a&&r1(s,h,o,c,l,u,v.x,v.y)&&r2(v.prev,v,v.next)>=0||(v=v.prevZ,x.x>=d&&x.x<=f&&x.y>=p&&x.y<=m&&x!==r&&x!==a&&r1(s,h,o,c,l,u,x.x,x.y)&&r2(x.prev,x,x.next)>=0))return!1;x=x.nextZ}for(;v&&v.z>=g;){if(v.x>=d&&v.x<=f&&v.y>=p&&v.y<=m&&v!==r&&v!==a&&r1(s,h,o,c,l,u,v.x,v.y)&&r2(v.prev,v,v.next)>=0)return!1;v=v.prevZ}for(;x&&x.z<=_;){if(x.x>=d&&x.x<=f&&x.y>=p&&x.y<=m&&x!==r&&x!==a&&r1(s,h,o,c,l,u,x.x,x.y)&&r2(x.prev,x,x.next)>=0)return!1;x=x.nextZ}return!0}(t,r,a,s):function(e){let t=e.prev,n=e.next;if(r2(t,e,n)>=0)return!1;let i=t.x,r=e.x,a=n.x,s=t.y,o=e.y,l=n.y,h=i<r?i<a?i:a:r<a?r:a,c=s<o?s<l?s:l:o<l?o:l,u=i>r?i>a?i:a:r>a?r:a,d=s>o?s>l?s:l:o>l?o:l,p=n.next;for(;p!==t;){if(p.x>=h&&p.x<=u&&p.y>=c&&p.y<=d&&r1(i,s,r,o,a,l,p.x,p.y)&&r2(p.prev,p,p.next)>=0)return!1;p=p.next}return!0}(t)){n.push(h.i/i|0),n.push(t.i/i|0),n.push(c.i/i|0),ae(t),t=c.next,l=c.next;continue}if((t=c)===l){o?1===o?e(t=function(e,t,n){let i=e;do{let r=i.prev,a=i.next.next;!r3(r,a)&&r4(r,i,i.next,a)&&r8(r,a)&&r8(a,r)&&(t.push(r.i/n|0),t.push(i.i/n|0),t.push(a.i/n|0),ae(i),ae(i.next),i=e=a),i=i.next}while(i!==e);return rQ(i)}(rQ(t),n,i),n,i,r,a,s,2):2===o&&function(t,n,i,r,a,s){let o=t;do{let t=o.next.next;for(;t!==o.prev;){var l,h;if(o.i!==t.i&&(l=o,h=t,l.next.i!==h.i&&l.prev.i!==h.i&&!function(e,t){let n=e;do{if(n.i!==e.i&&n.next.i!==e.i&&n.i!==t.i&&n.next.i!==t.i&&r4(n,n.next,e,t))return!0;n=n.next}while(n!==e);return!1}(l,h)&&(r8(l,h)&&r8(h,l)&&function(e,t){let n=e,i=!1,r=(e.x+t.x)/2,a=(e.y+t.y)/2;do n.y>a!=n.next.y>a&&n.next.y!==n.y&&r<(n.next.x-n.x)*(a-n.y)/(n.next.y-n.y)+n.x&&(i=!i),n=n.next;while(n!==e);return i}(l,h)&&(r2(l.prev,l,h.prev)||r2(l,h.prev,h))||r3(l,h)&&r2(l.prev,l,l.next)>0&&r2(h.prev,h,h.next)>0))){let l=r9(o,t);o=rQ(o,o.next),l=rQ(l,l.next),e(o,n,i,r,a,s,0),e(l,n,i,r,a,s,0);return}t=t.next}o=o.next}while(o!==t)}(t,n,i,r,a,s):e(rQ(t),n,i,r,a,s,1);break}}}(d,p,h,n,i,l,0),p}};function rK(e,t,n,i,r){let a,s;if(r===function(e,t,n,i){let r=0;for(let a=t,s=n-i;a<n;a+=i)r+=(e[s]-e[a])*(e[a+1]+e[s+1]),s=a;return r}(e,t,n,i)>0)for(a=t;a<n;a+=i)s=r7(a,e[a],e[a+1],s);else for(a=n-i;a>=t;a-=i)s=r7(a,e[a],e[a+1],s);return s&&r3(s,s.next)&&(ae(s),s=s.next),s}function rQ(e,t){if(!e)return e;t||(t=e);let n=e,i;do if(i=!1,!n.steiner&&(r3(n,n.next)||0===r2(n.prev,n,n.next))){if(ae(n),(n=t=n.prev)===n.next)break;i=!0}else n=n.next;while(i||n!==t);return t}function r$(e,t){return e.x-t.x}function r0(e,t,n,i,r){return(e=((e=((e=((e=((e=(e-n)*r|0)|e<<8)&0xff00ff)|e<<4)&0xf0f0f0f)|e<<2)&0x33333333)|e<<1)&0x55555555)|(t=((t=((t=((t=((t=(t-i)*r|0)|t<<8)&0xff00ff)|t<<4)&0xf0f0f0f)|t<<2)&0x33333333)|t<<1)&0x55555555)<<1}function r1(e,t,n,i,r,a,s,o){return(r-s)*(t-o)>=(e-s)*(a-o)&&(e-s)*(i-o)>=(n-s)*(t-o)&&(n-s)*(a-o)>=(r-s)*(i-o)}function r2(e,t,n){return(t.y-e.y)*(n.x-t.x)-(t.x-e.x)*(n.y-t.y)}function r3(e,t){return e.x===t.x&&e.y===t.y}function r4(e,t,n,i){let r=r6(r2(e,t,n)),a=r6(r2(e,t,i)),s=r6(r2(n,i,e)),o=r6(r2(n,i,t));return!!(r!==a&&s!==o||0===r&&r5(e,n,t)||0===a&&r5(e,i,t)||0===s&&r5(n,e,i)||0===o&&r5(n,t,i))}function r5(e,t,n){return t.x<=Math.max(e.x,n.x)&&t.x>=Math.min(e.x,n.x)&&t.y<=Math.max(e.y,n.y)&&t.y>=Math.min(e.y,n.y)}function r6(e){return e>0?1:e<0?-1:0}function r8(e,t){return 0>r2(e.prev,e,e.next)?r2(e,t,e.next)>=0&&r2(e,e.prev,t)>=0:0>r2(e,t,e.prev)||0>r2(e,e.next,t)}function r9(e,t){let n=new at(e.i,e.x,e.y),i=new at(t.i,t.x,t.y),r=e.next,a=t.prev;return e.next=t,t.prev=e,n.next=r,r.prev=n,i.next=n,n.prev=i,a.next=i,i.prev=a,i}function r7(e,t,n,i){let r=new at(e,t,n);return i?(r.next=i.next,r.prev=i,i.next.prev=r,i.next=r):(r.prev=r,r.next=r),r}function ae(e){e.next.prev=e.prev,e.prev.next=e.next,e.prevZ&&(e.prevZ.nextZ=e.nextZ),e.nextZ&&(e.nextZ.prevZ=e.prevZ)}function at(e,t,n){this.i=e,this.x=t,this.y=n,this.prev=null,this.next=null,this.z=0,this.prevZ=null,this.nextZ=null,this.steiner=!1}class an{static area(e){let t=e.length,n=0;for(let i=t-1,r=0;r<t;i=r++)n+=e[i].x*e[r].y-e[r].x*e[i].y;return .5*n}static isClockWise(e){return 0>an.area(e)}static triangulateShape(e,t){let n=[],i=[],r=[];ai(e),ar(n,e);let a=e.length;t.forEach(ai);for(let e=0;e<t.length;e++)i.push(a),a+=t[e].length,ar(n,t[e]);let s=rZ.triangulate(n,i);for(let e=0;e<s.length;e+=3)r.push(s.slice(e,e+3));return r}}function ai(e){let t=e.length;t>2&&e[t-1].equals(e[0])&&e.pop()}function ar(e,t){for(let n=0;n<t.length;n++)e.push(t[n].x),e.push(t[n].y)}class aa extends ty{copy(e){return super.copy(e),this.parameters=Object.assign({},e.parameters),this}toJSON(){let e=super.toJSON();return function(e,t,n){if(n.shapes=[],Array.isArray(e))for(let t=0,i=e.length;t<i;t++){let i=e[t];n.shapes.push(i.uuid)}else n.shapes.push(e.uuid);return n.options=Object.assign({},t),void 0!==t.extrudePath&&(n.options.extrudePath=t.extrudePath.toJSON()),n}(this.parameters.shapes,this.parameters.options,e)}static fromJSON(e,t){let n=[];for(let i=0,r=e.shapes.length;i<r;i++){let r=t[e.shapes[i]];n.push(r)}let i=e.options.extrudePath;return void 0!==i&&(e.options.extrudePath=new rI[i.type]().fromJSON(i)),new aa(n,e.options)}constructor(e=new rJ([new S(.5,.5),new S(-.5,.5),new S(-.5,-.5),new S(.5,-.5)]),t={}){super(),this.type="ExtrudeGeometry",this.parameters={shapes:e,options:t},e=Array.isArray(e)?e:[e];let n=this,i=[],r=[];for(let a=0,s=e.length;a<s;a++)!function(e){let a,s,o,l,h=[],c=void 0!==t.curveSegments?t.curveSegments:12,u=void 0!==t.steps?t.steps:1,d=void 0!==t.depth?t.depth:1,p=void 0===t.bevelEnabled||t.bevelEnabled,f=void 0!==t.bevelThickness?t.bevelThickness:.2,m=void 0!==t.bevelSize?t.bevelSize:f-.1,g=void 0!==t.bevelOffset?t.bevelOffset:0,_=void 0!==t.bevelSegments?t.bevelSegments:3,v=t.extrudePath,x=void 0!==t.UVGenerator?t.UVGenerator:as,y,M=!1;v&&(y=v.getSpacedPoints(u),M=!0,p=!1,a=v.computeFrenetFrames(u,!1),s=new Y,o=new Y,l=new Y),p||(_=0,f=0,m=0,g=0);let E=e.extractPoints(c),T=E.shape,b=E.holes;if(!an.isClockWise(T)){T=T.reverse();for(let e=0,t=b.length;e<t;e++){let t=b[e];an.isClockWise(t)&&(b[e]=t.reverse())}}let A=an.triangulateShape(T,b),w=T;for(let e=0,t=b.length;e<t;e++){let t=b[e];T=T.concat(t)}function R(e,t,n){return t||console.error("THREE.ExtrudeGeometry: vec does not exist"),e.clone().addScaledVector(t,n)}let C=T.length,L=A.length;function P(e,t,n){let i,r,a,s=e.x-t.x,o=e.y-t.y,l=n.x-e.x,h=n.y-e.y,c=s*s+o*o;if(Math.abs(s*h-o*l)>Number.EPSILON){let u=Math.sqrt(c),d=Math.sqrt(l*l+h*h),p=t.x-o/u,f=t.y+s/u,m=((n.x-h/d-p)*h-(n.y+l/d-f)*l)/(s*h-o*l),g=(i=p+s*m-e.x)*i+(r=f+o*m-e.y)*r;if(g<=2)return new S(i,r);a=Math.sqrt(g/2)}else{let e=!1;s>Number.EPSILON?l>Number.EPSILON&&(e=!0):s<-Number.EPSILON?l<-Number.EPSILON&&(e=!0):Math.sign(o)===Math.sign(h)&&(e=!0),e?(i=-o,r=s,a=Math.sqrt(c)):(i=s,r=o,a=Math.sqrt(c/2))}return new S(i/a,r/a)}let U=[];for(let e=0,t=w.length,n=t-1,i=e+1;e<t;e++,n++,i++)n===t&&(n=0),i===t&&(i=0),U[e]=P(w[e],w[n],w[i]);let N=[],D,I=U.concat();for(let e=0,t=b.length;e<t;e++){let t=b[e];D=[];for(let e=0,n=t.length,i=n-1,r=e+1;e<n;e++,i++,r++)i===n&&(i=0),r===n&&(r=0),D[e]=P(t[e],t[i],t[r]);N.push(D),I=I.concat(D)}for(let e=0;e<_;e++){let t=e/_,n=f*Math.cos(t*Math.PI/2),i=m*Math.sin(t*Math.PI/2)+g;for(let e=0,t=w.length;e<t;e++){let t=R(w[e],U[e],i);z(t.x,t.y,-n)}for(let e=0,t=b.length;e<t;e++){let t=b[e];D=N[e];for(let e=0,r=t.length;e<r;e++){let r=R(t[e],D[e],i);z(r.x,r.y,-n)}}}let O=m+g;for(let e=0;e<C;e++){let t=p?R(T[e],I[e],O):T[e];M?(o.copy(a.normals[0]).multiplyScalar(t.x),s.copy(a.binormals[0]).multiplyScalar(t.y),l.copy(y[0]).add(o).add(s),z(l.x,l.y,l.z)):z(t.x,t.y,0)}for(let e=1;e<=u;e++)for(let t=0;t<C;t++){let n=p?R(T[t],I[t],O):T[t];M?(o.copy(a.normals[e]).multiplyScalar(n.x),s.copy(a.binormals[e]).multiplyScalar(n.y),l.copy(y[e]).add(o).add(s),z(l.x,l.y,l.z)):z(n.x,n.y,d/u*e)}for(let e=_-1;e>=0;e--){let t=e/_,n=f*Math.cos(t*Math.PI/2),i=m*Math.sin(t*Math.PI/2)+g;for(let e=0,t=w.length;e<t;e++){let t=R(w[e],U[e],i);z(t.x,t.y,d+n)}for(let e=0,t=b.length;e<t;e++){let t=b[e];D=N[e];for(let e=0,r=t.length;e<r;e++){let r=R(t[e],D[e],i);M?z(r.x,r.y+y[u-1].y,y[u-1].x+n):z(r.x,r.y,d+n)}}}function F(e,t){let r=e.length;for(;--r>=0;){let a=r,s=r-1;s<0&&(s=e.length-1);for(let e=0,r=u+2*_;e<r;e++){let r=C*e,o=C*(e+1);!function(e,t,r,a){H(e),H(t),H(a),H(t),H(r),H(a);let s=i.length/3,o=x.generateSideWallUV(n,i,s-6,s-3,s-2,s-1);V(o[0]),V(o[1]),V(o[3]),V(o[1]),V(o[2]),V(o[3])}(t+a+r,t+s+r,t+s+o,t+a+o)}}}function z(e,t,n){h.push(e),h.push(t),h.push(n)}function B(e,t,r){H(e),H(t),H(r);let a=i.length/3,s=x.generateTopUV(n,i,a-3,a-2,a-1);V(s[0]),V(s[1]),V(s[2])}function H(e){i.push(h[3*e+0]),i.push(h[3*e+1]),i.push(h[3*e+2])}function V(e){r.push(e.x),r.push(e.y)}(function(){let e=i.length/3;if(p){let e=0,t=0*C;for(let e=0;e<L;e++){let n=A[e];B(n[2]+t,n[1]+t,n[0]+t)}t=C*(u+2*_);for(let e=0;e<L;e++){let n=A[e];B(n[0]+t,n[1]+t,n[2]+t)}}else{for(let e=0;e<L;e++){let t=A[e];B(t[2],t[1],t[0])}for(let e=0;e<L;e++){let t=A[e];B(t[0]+C*u,t[1]+C*u,t[2]+C*u)}}n.addGroup(e,i.length/3-e,0)})(),function(){let e=i.length/3,t=0;F(w,0),t+=w.length;for(let e=0,n=b.length;e<n;e++){let n=b[e];F(n,t),t+=n.length}n.addGroup(e,i.length/3-e,1)}()}(e[a]);this.setAttribute("position",new td(i,3)),this.setAttribute("uv",new td(r,2)),this.computeVertexNormals()}}let as={generateTopUV:function(e,t,n,i,r){let a=t[3*n],s=t[3*n+1],o=t[3*i],l=t[3*i+1],h=t[3*r],c=t[3*r+1];return[new S(a,s),new S(o,l),new S(h,c)]},generateSideWallUV:function(e,t,n,i,r,a){let s=t[3*n],o=t[3*n+1],l=t[3*n+2],h=t[3*i],c=t[3*i+1],u=t[3*i+2],d=t[3*r],p=t[3*r+1],f=t[3*r+2],m=t[3*a],g=t[3*a+1],_=t[3*a+2];return Math.abs(o-c)<Math.abs(s-h)?[new S(s,1-l),new S(h,1-u),new S(d,1-f),new S(m,1-_)]:[new S(o,1-l),new S(c,1-u),new S(p,1-f),new S(g,1-_)]}};class ao extends rk{static fromJSON(e){return new ao(e.radius,e.detail)}constructor(e=1,t=0){let n=(1+Math.sqrt(5))/2;super([-1,n,0,1,n,0,-1,-n,0,1,-n,0,0,-1,n,0,1,n,0,-1,-n,0,1,-n,n,0,-1,n,0,1,-n,0,-1,-n,0,1],[0,11,5,0,5,1,0,1,7,0,7,10,0,10,11,1,5,9,5,11,4,11,10,2,10,7,6,7,1,8,3,9,4,3,4,2,3,2,6,3,6,8,3,8,9,4,9,5,2,4,11,6,2,10,8,6,7,9,8,1],e,t),this.type="IcosahedronGeometry",this.parameters={radius:e,detail:t}}}class al extends rk{static fromJSON(e){return new al(e.radius,e.detail)}constructor(e=1,t=0){super([1,0,0,-1,0,0,0,1,0,0,-1,0,0,0,1,0,0,-1],[0,2,4,0,4,3,0,3,5,0,5,2,1,2,5,1,5,3,1,3,4,1,4,2],e,t),this.type="OctahedronGeometry",this.parameters={radius:e,detail:t}}}class ah extends ty{copy(e){return super.copy(e),this.parameters=Object.assign({},e.parameters),this}static fromJSON(e){return new ah(e.innerRadius,e.outerRadius,e.thetaSegments,e.phiSegments,e.thetaStart,e.thetaLength)}constructor(e=.5,t=1,n=32,i=1,r=0,a=2*Math.PI){super(),this.type="RingGeometry",this.parameters={innerRadius:e,outerRadius:t,thetaSegments:n,phiSegments:i,thetaStart:r,thetaLength:a},n=Math.max(3,n);let s=[],o=[],l=[],h=[],c=e,u=(t-e)/(i=Math.max(1,i)),d=new Y,p=new S;for(let e=0;e<=i;e++){for(let e=0;e<=n;e++){let i=r+e/n*a;d.x=c*Math.cos(i),d.y=c*Math.sin(i),o.push(d.x,d.y,d.z),l.push(0,0,1),p.x=(d.x/t+1)/2,p.y=(d.y/t+1)/2,h.push(p.x,p.y)}c+=u}for(let e=0;e<i;e++){let t=e*(n+1);for(let e=0;e<n;e++){let i=e+t,r=i+n+1,a=i+n+2,o=i+1;s.push(i,r,o),s.push(r,a,o)}}this.setIndex(s),this.setAttribute("position",new td(o,3)),this.setAttribute("normal",new td(l,3)),this.setAttribute("uv",new td(h,2))}}class ac extends ty{copy(e){return super.copy(e),this.parameters=Object.assign({},e.parameters),this}toJSON(){let e=super.toJSON();return function(e,t){if(t.shapes=[],Array.isArray(e))for(let n=0,i=e.length;n<i;n++){let i=e[n];t.shapes.push(i.uuid)}else t.shapes.push(e.uuid);return t}(this.parameters.shapes,e)}static fromJSON(e,t){let n=[];for(let i=0,r=e.shapes.length;i<r;i++){let r=t[e.shapes[i]];n.push(r)}return new ac(n,e.curveSegments)}constructor(e=new rJ([new S(0,.5),new S(-.5,-.5),new S(.5,-.5)]),t=12){super(),this.type="ShapeGeometry",this.parameters={shapes:e,curveSegments:t};let n=[],i=[],r=[],a=[],s=0,o=0;if(!1===Array.isArray(e))l(e);else for(let t=0;t<e.length;t++)l(e[t]),this.addGroup(s,o,t),s+=o,o=0;function l(e){let s=i.length/3,l=e.extractPoints(t),h=l.shape,c=l.holes;!1===an.isClockWise(h)&&(h=h.reverse());for(let e=0,t=c.length;e<t;e++){let t=c[e];!0===an.isClockWise(t)&&(c[e]=t.reverse())}let u=an.triangulateShape(h,c);for(let e=0,t=c.length;e<t;e++){let t=c[e];h=h.concat(t)}for(let e=0,t=h.length;e<t;e++){let t=h[e];i.push(t.x,t.y,0),r.push(0,0,1),a.push(t.x,t.y)}for(let e=0,t=u.length;e<t;e++){let t=u[e],i=t[0]+s,r=t[1]+s,a=t[2]+s;n.push(i,r,a),o+=3}}this.setIndex(n),this.setAttribute("position",new td(i,3)),this.setAttribute("normal",new td(r,3)),this.setAttribute("uv",new td(a,2))}}class au extends ty{copy(e){return super.copy(e),this.parameters=Object.assign({},e.parameters),this}static fromJSON(e){return new au(e.radius,e.widthSegments,e.heightSegments,e.phiStart,e.phiLength,e.thetaStart,e.thetaLength)}constructor(e=1,t=32,n=16,i=0,r=2*Math.PI,a=0,s=Math.PI){super(),this.type="SphereGeometry",this.parameters={radius:e,widthSegments:t,heightSegments:n,phiStart:i,phiLength:r,thetaStart:a,thetaLength:s},t=Math.max(3,Math.floor(t)),n=Math.max(2,Math.floor(n));let o=Math.min(a+s,Math.PI),l=0,h=[],c=new Y,u=new Y,d=[],p=[],f=[],m=[];for(let d=0;d<=n;d++){let g=[],_=d/n,v=0;0===d&&0===a?v=.5/t:d===n&&o===Math.PI&&(v=-.5/t);for(let n=0;n<=t;n++){let o=n/t;c.x=-e*Math.cos(i+o*r)*Math.sin(a+_*s),c.y=e*Math.cos(a+_*s),c.z=e*Math.sin(i+o*r)*Math.sin(a+_*s),p.push(c.x,c.y,c.z),u.copy(c).normalize(),f.push(u.x,u.y,u.z),m.push(o+v,1-_),g.push(l++)}h.push(g)}for(let e=0;e<n;e++)for(let i=0;i<t;i++){let t=h[e][i+1],r=h[e][i],s=h[e+1][i],l=h[e+1][i+1];(0!==e||a>0)&&d.push(t,r,l),(e!==n-1||o<Math.PI)&&d.push(r,s,l)}this.setIndex(d),this.setAttribute("position",new td(p,3)),this.setAttribute("normal",new td(f,3)),this.setAttribute("uv",new td(m,2))}}class ad extends rk{static fromJSON(e){return new ad(e.radius,e.detail)}constructor(e=1,t=0){super([1,1,1,-1,-1,1,-1,1,-1,1,-1,-1],[2,1,0,0,3,2,1,3,0,2,3,1],e,t),this.type="TetrahedronGeometry",this.parameters={radius:e,detail:t}}}class ap extends ty{copy(e){return super.copy(e),this.parameters=Object.assign({},e.parameters),this}static fromJSON(e){return new ap(e.radius,e.tube,e.radialSegments,e.tubularSegments,e.arc)}constructor(e=1,t=.4,n=12,i=48,r=2*Math.PI){super(),this.type="TorusGeometry",this.parameters={radius:e,tube:t,radialSegments:n,tubularSegments:i,arc:r},n=Math.floor(n),i=Math.floor(i);let a=[],s=[],o=[],l=[],h=new Y,c=new Y,u=new Y;for(let a=0;a<=n;a++)for(let d=0;d<=i;d++){let p=d/i*r,f=a/n*Math.PI*2;c.x=(e+t*Math.cos(f))*Math.cos(p),c.y=(e+t*Math.cos(f))*Math.sin(p),c.z=t*Math.sin(f),s.push(c.x,c.y,c.z),h.x=e*Math.cos(p),h.y=e*Math.sin(p),u.subVectors(c,h).normalize(),o.push(u.x,u.y,u.z),l.push(d/i),l.push(a/n)}for(let e=1;e<=n;e++)for(let t=1;t<=i;t++){let n=(i+1)*e+t-1,r=(i+1)*(e-1)+t-1,s=(i+1)*(e-1)+t,o=(i+1)*e+t;a.push(n,r,o),a.push(r,s,o)}this.setIndex(a),this.setAttribute("position",new td(s,3)),this.setAttribute("normal",new td(o,3)),this.setAttribute("uv",new td(l,2))}}class af extends ty{copy(e){return super.copy(e),this.parameters=Object.assign({},e.parameters),this}static fromJSON(e){return new af(e.radius,e.tube,e.tubularSegments,e.radialSegments,e.p,e.q)}constructor(e=1,t=.4,n=64,i=8,r=2,a=3){super(),this.type="TorusKnotGeometry",this.parameters={radius:e,tube:t,tubularSegments:n,radialSegments:i,p:r,q:a},n=Math.floor(n),i=Math.floor(i);let s=[],o=[],l=[],h=[],c=new Y,u=new Y,d=new Y,p=new Y,f=new Y,m=new Y,g=new Y;for(let s=0;s<=n;++s){let v=s/n*r*Math.PI*2;_(v,r,a,e,d),_(v+.01,r,a,e,p),m.subVectors(p,d),g.addVectors(p,d),f.crossVectors(m,g),g.crossVectors(f,m),f.normalize(),g.normalize();for(let e=0;e<=i;++e){let r=e/i*Math.PI*2,a=-t*Math.cos(r),p=t*Math.sin(r);c.x=d.x+(a*g.x+p*f.x),c.y=d.y+(a*g.y+p*f.y),c.z=d.z+(a*g.z+p*f.z),o.push(c.x,c.y,c.z),u.subVectors(c,d).normalize(),l.push(u.x,u.y,u.z),h.push(s/n),h.push(e/i)}}for(let e=1;e<=n;e++)for(let t=1;t<=i;t++){let n=(i+1)*(e-1)+(t-1),r=(i+1)*e+(t-1),a=(i+1)*e+t,o=(i+1)*(e-1)+t;s.push(n,r,o),s.push(r,a,o)}function _(e,t,n,i,r){let a=Math.cos(e),s=Math.sin(e),o=n/t*e,l=Math.cos(o);r.x=i*(2+l)*.5*a,r.y=i*(2+l)*s*.5,r.z=i*Math.sin(o)*.5}this.setIndex(s),this.setAttribute("position",new td(o,3)),this.setAttribute("normal",new td(l,3)),this.setAttribute("uv",new td(h,2))}}class am extends ty{copy(e){return super.copy(e),this.parameters=Object.assign({},e.parameters),this}toJSON(){let e=super.toJSON();return e.path=this.parameters.path.toJSON(),e}static fromJSON(e){return new am(new rI[e.path.type]().fromJSON(e.path),e.tubularSegments,e.radius,e.radialSegments,e.closed)}constructor(e=new rN(new Y(-1,-1,0),new Y(-1,1,0),new Y(1,1,0)),t=64,n=1,i=8,r=!1){super(),this.type="TubeGeometry",this.parameters={path:e,tubularSegments:t,radius:n,radialSegments:i,closed:r};let a=e.computeFrenetFrames(t,r);this.tangents=a.tangents,this.normals=a.normals,this.binormals=a.binormals;let s=new Y,o=new Y,l=new S,h=new Y,c=[],u=[],d=[],p=[];function f(r){h=e.getPointAt(r/t,h);let l=a.normals[r],d=a.binormals[r];for(let e=0;e<=i;e++){let t=e/i*Math.PI*2,r=Math.sin(t),a=-Math.cos(t);o.x=a*l.x+r*d.x,o.y=a*l.y+r*d.y,o.z=a*l.z+r*d.z,o.normalize(),u.push(o.x,o.y,o.z),s.x=h.x+n*o.x,s.y=h.y+n*o.y,s.z=h.z+n*o.z,c.push(s.x,s.y,s.z)}}(function(){for(let e=0;e<t;e++)f(e);f(!1===r?t:0),function(){for(let e=0;e<=t;e++)for(let n=0;n<=i;n++)l.x=e/t,l.y=n/i,d.push(l.x,l.y)}(),function(){for(let e=1;e<=t;e++)for(let t=1;t<=i;t++){let n=(i+1)*(e-1)+(t-1),r=(i+1)*e+(t-1),a=(i+1)*e+t,s=(i+1)*(e-1)+t;p.push(n,r,s),p.push(r,a,s)}}()})(),this.setIndex(p),this.setAttribute("position",new td(c,3)),this.setAttribute("normal",new td(u,3)),this.setAttribute("uv",new td(d,2))}}function ag(e,t,n){let i="".concat(e.x,",").concat(e.y,",").concat(e.z,"-").concat(t.x,",").concat(t.y,",").concat(t.z),r="".concat(t.x,",").concat(t.y,",").concat(t.z,"-").concat(e.x,",").concat(e.y,",").concat(e.z);return!0!==n.has(i)&&!0!==n.has(r)&&(n.add(i),n.add(r),!0)}class a_ extends ta{copy(e){return super.copy(e),this.color.copy(e.color),this.map=e.map,this.lightMap=e.lightMap,this.lightMapIntensity=e.lightMapIntensity,this.aoMap=e.aoMap,this.aoMapIntensity=e.aoMapIntensity,this.emissive.copy(e.emissive),this.emissiveMap=e.emissiveMap,this.emissiveIntensity=e.emissiveIntensity,this.bumpMap=e.bumpMap,this.bumpScale=e.bumpScale,this.normalMap=e.normalMap,this.normalMapType=e.normalMapType,this.normalScale.copy(e.normalScale),this.displacementMap=e.displacementMap,this.displacementScale=e.displacementScale,this.displacementBias=e.displacementBias,this.specularMap=e.specularMap,this.alphaMap=e.alphaMap,this.envMap=e.envMap,this.combine=e.combine,this.reflectivity=e.reflectivity,this.refractionRatio=e.refractionRatio,this.wireframe=e.wireframe,this.wireframeLinewidth=e.wireframeLinewidth,this.wireframeLinecap=e.wireframeLinecap,this.wireframeLinejoin=e.wireframeLinejoin,this.flatShading=e.flatShading,this.fog=e.fog,this}constructor(e){super(),this.isMeshLambertMaterial=!0,this.type="MeshLambertMaterial",this.color=new tn(0xffffff),this.map=null,this.lightMap=null,this.lightMapIntensity=1,this.aoMap=null,this.aoMapIntensity=1,this.emissive=new tn(0),this.emissiveIntensity=1,this.emissiveMap=null,this.bumpMap=null,this.bumpScale=1,this.normalMap=null,this.normalMapType=0,this.normalScale=new S(1,1),this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.specularMap=null,this.alphaMap=null,this.envMap=null,this.combine=0,this.reflectivity=1,this.refractionRatio=.98,this.wireframe=!1,this.wireframeLinewidth=1,this.wireframeLinecap="round",this.wireframeLinejoin="round",this.flatShading=!1,this.fog=!0,this.setValues(e)}}function av(e,t,n){return e&&(n||e.constructor!==t)?"number"==typeof t.BYTES_PER_ELEMENT?new t(e):Array.prototype.slice.call(e):e}class ax{evaluate(e){let t=this.parameterPositions,n=this._cachedIndex,i=t[n],r=t[n-1];e:{t:{let a;n:{i:if(!(e<i)){for(let a=n+2;;){if(void 0===i){if(e<r)break i;return n=t.length,this._cachedIndex=n,this.copySampleValue_(n-1)}if(n===a)break;if(r=i,e<(i=t[++n]))break t}a=t.length;break n}if(!(e>=r)){let s=t[1];e<s&&(n=2,r=s);for(let a=n-2;;){if(void 0===r)return this._cachedIndex=0,this.copySampleValue_(0);if(n===a)break;if(i=r,e>=(r=t[--n-1]))break t}a=n,n=0;break n}break e}for(;n<a;){let i=n+a>>>1;e<t[i]?a=i:n=i+1}if(i=t[n],void 0===(r=t[n-1]))return this._cachedIndex=0,this.copySampleValue_(0);if(void 0===i)return n=t.length,this._cachedIndex=n,this.copySampleValue_(n-1)}this._cachedIndex=n,this.intervalChanged_(n,r,i)}return this.interpolate_(n,r,e,i)}getSettings_(){return this.settings||this.DefaultSettings_}copySampleValue_(e){let t=this.resultBuffer,n=this.sampleValues,i=this.valueSize,r=e*i;for(let e=0;e!==i;++e)t[e]=n[r+e];return t}interpolate_(){throw Error("call to abstract method")}intervalChanged_(){}constructor(e,t,n,i){this.parameterPositions=e,this._cachedIndex=0,this.resultBuffer=void 0!==i?i:new t.constructor(n),this.sampleValues=t,this.valueSize=n,this.settings=null,this.DefaultSettings_={}}}class ay extends ax{intervalChanged_(e,t,n){let i=this.parameterPositions,r=e-2,a=e+1,s=i[r],o=i[a];if(void 0===s)switch(this.getSettings_().endingStart){case 2401:r=e,s=2*t-n;break;case 2402:r=i.length-2,s=t+i[r]-i[r+1];break;default:r=e,s=n}if(void 0===o)switch(this.getSettings_().endingEnd){case 2401:a=e,o=2*n-t;break;case 2402:a=1,o=n+i[1]-i[0];break;default:a=e-1,o=t}let l=(n-t)*.5,h=this.valueSize;this._weightPrev=l/(t-s),this._weightNext=l/(o-n),this._offsetPrev=r*h,this._offsetNext=a*h}interpolate_(e,t,n,i){let r=this.resultBuffer,a=this.sampleValues,s=this.valueSize,o=e*s,l=o-s,h=this._offsetPrev,c=this._offsetNext,u=this._weightPrev,d=this._weightNext,p=(n-t)/(i-t),f=p*p,m=f*p,g=-u*m+2*u*f-u*p,_=(1+u)*m+(-1.5-2*u)*f+(-.5+u)*p+1,v=(-1-d)*m+(1.5+d)*f+.5*p,x=d*m-d*f;for(let e=0;e!==s;++e)r[e]=g*a[h+e]+_*a[l+e]+v*a[o+e]+x*a[c+e];return r}constructor(e,t,n,i){super(e,t,n,i),this._weightPrev=-0,this._offsetPrev=-0,this._weightNext=-0,this._offsetNext=-0,this.DefaultSettings_={endingStart:2400,endingEnd:2400}}}class aM extends ax{interpolate_(e,t,n,i){let r=this.resultBuffer,a=this.sampleValues,s=this.valueSize,o=e*s,l=o-s,h=(n-t)/(i-t),c=1-h;for(let e=0;e!==s;++e)r[e]=a[l+e]*c+a[o+e]*h;return r}constructor(e,t,n,i){super(e,t,n,i)}}class aS extends ax{interpolate_(e){return this.copySampleValue_(e-1)}constructor(e,t,n,i){super(e,t,n,i)}}class aE{static toJSON(e){let t,n=e.constructor;if(n.toJSON!==this.toJSON)t=n.toJSON(e);else{t={name:e.name,times:av(e.times,Array),values:av(e.values,Array)};let n=e.getInterpolation();n!==e.DefaultInterpolation&&(t.interpolation=n)}return t.type=e.ValueTypeName,t}InterpolantFactoryMethodDiscrete(e){return new aS(this.times,this.values,this.getValueSize(),e)}InterpolantFactoryMethodLinear(e){return new aM(this.times,this.values,this.getValueSize(),e)}InterpolantFactoryMethodSmooth(e){return new ay(this.times,this.values,this.getValueSize(),e)}setInterpolation(e){let t;switch(e){case 2300:t=this.InterpolantFactoryMethodDiscrete;break;case 2301:t=this.InterpolantFactoryMethodLinear;break;case 2302:t=this.InterpolantFactoryMethodSmooth}if(void 0===t){let t="unsupported interpolation for "+this.ValueTypeName+" keyframe track named "+this.name;if(void 0===this.createInterpolant)if(e!==this.DefaultInterpolation)this.setInterpolation(this.DefaultInterpolation);else throw Error(t);return console.warn("THREE.KeyframeTrack:",t),this}return this.createInterpolant=t,this}getInterpolation(){switch(this.createInterpolant){case this.InterpolantFactoryMethodDiscrete:return 2300;case this.InterpolantFactoryMethodLinear:return 2301;case this.InterpolantFactoryMethodSmooth:return 2302}}getValueSize(){return this.values.length/this.times.length}shift(e){if(0!==e){let t=this.times;for(let n=0,i=t.length;n!==i;++n)t[n]+=e}return this}scale(e){if(1!==e){let t=this.times;for(let n=0,i=t.length;n!==i;++n)t[n]*=e}return this}trim(e,t){let n=this.times,i=n.length,r=0,a=i-1;for(;r!==i&&n[r]<e;)++r;for(;-1!==a&&n[a]>t;)--a;if(++a,0!==r||a!==i){r>=a&&(r=(a=Math.max(a,1))-1);let e=this.getValueSize();this.times=n.slice(r,a),this.values=this.values.slice(r*e,a*e)}return this}validate(){var e;let t=!0,n=this.getValueSize();n-Math.floor(n)!=0&&(console.error("THREE.KeyframeTrack: Invalid value size in track.",this),t=!1);let i=this.times,r=this.values,a=i.length;0===a&&(console.error("THREE.KeyframeTrack: Track is empty.",this),t=!1);let s=null;for(let e=0;e!==a;e++){let n=i[e];if("number"==typeof n&&isNaN(n)){console.error("THREE.KeyframeTrack: Time is not a valid number.",this,e,n),t=!1;break}if(null!==s&&s>n){console.error("THREE.KeyframeTrack: Out of order keys.",this,e,n,s),t=!1;break}s=n}if(void 0!==r&&ArrayBuffer.isView(e=r)&&!(e instanceof DataView))for(let e=0,n=r.length;e!==n;++e){let n=r[e];if(isNaN(n)){console.error("THREE.KeyframeTrack: Value is not a valid number.",this,e,n),t=!1;break}}return t}optimize(){let e=this.times.slice(),t=this.values.slice(),n=this.getValueSize(),i=2302===this.getInterpolation(),r=e.length-1,a=1;for(let s=1;s<r;++s){let r=!1,o=e[s];if(o!==e[s+1]&&(1!==s||o!==e[0]))if(i)r=!0;else{let e=s*n,i=e-n,a=e+n;for(let s=0;s!==n;++s){let n=t[e+s];if(n!==t[i+s]||n!==t[a+s]){r=!0;break}}}if(r){if(s!==a){e[a]=e[s];let i=s*n,r=a*n;for(let e=0;e!==n;++e)t[r+e]=t[i+e]}++a}}if(r>0){e[a]=e[r];for(let e=r*n,i=a*n,s=0;s!==n;++s)t[i+s]=t[e+s];++a}return a!==e.length?(this.times=e.slice(0,a),this.values=t.slice(0,a*n)):(this.times=e,this.values=t),this}clone(){let e=this.times.slice(),t=this.values.slice(),n=new this.constructor(this.name,e,t);return n.createInterpolant=this.createInterpolant,n}constructor(e,t,n,i){if(void 0===e)throw Error("THREE.KeyframeTrack: track name is undefined");if(void 0===t||0===t.length)throw Error("THREE.KeyframeTrack: no keyframes in track named "+e);this.name=e,this.times=av(t,this.TimeBufferType),this.values=av(n,this.ValueBufferType),this.setInterpolation(i||this.DefaultInterpolation)}}aE.prototype.TimeBufferType=Float32Array,aE.prototype.ValueBufferType=Float32Array,aE.prototype.DefaultInterpolation=2301;class aT extends aE{}aT.prototype.ValueTypeName="bool",aT.prototype.ValueBufferType=Array,aT.prototype.DefaultInterpolation=2300,aT.prototype.InterpolantFactoryMethodLinear=void 0,aT.prototype.InterpolantFactoryMethodSmooth=void 0;class ab extends aE{}ab.prototype.ValueTypeName="color";class aA extends aE{}aA.prototype.ValueTypeName="number";class aw extends ax{interpolate_(e,t,n,i){let r=this.resultBuffer,a=this.sampleValues,s=this.valueSize,o=(n-t)/(i-t),l=e*s;for(let e=l+s;l!==e;l+=4)q.slerpFlat(r,0,a,l-s,a,l,o);return r}constructor(e,t,n,i){super(e,t,n,i)}}class aR extends aE{InterpolantFactoryMethodLinear(e){return new aw(this.times,this.values,this.getValueSize(),e)}}aR.prototype.ValueTypeName="quaternion",aR.prototype.DefaultInterpolation=2301,aR.prototype.InterpolantFactoryMethodSmooth=void 0;class aC extends aE{}aC.prototype.ValueTypeName="string",aC.prototype.ValueBufferType=Array,aC.prototype.DefaultInterpolation=2300,aC.prototype.InterpolantFactoryMethodLinear=void 0,aC.prototype.InterpolantFactoryMethodSmooth=void 0;class aL extends aE{}aL.prototype.ValueTypeName="vector";class aP{constructor(e,t,n){let i,r=this,a=!1,s=0,o=0,l=[];this.onStart=void 0,this.onLoad=e,this.onProgress=t,this.onError=n,this.itemStart=function(e){o++,!1===a&&void 0!==r.onStart&&r.onStart(e,s,o),a=!0},this.itemEnd=function(e){s++,void 0!==r.onProgress&&r.onProgress(e,s,o),s===o&&(a=!1,void 0!==r.onLoad&&r.onLoad())},this.itemError=function(e){void 0!==r.onError&&r.onError(e)},this.resolveURL=function(e){return i?i(e):e},this.setURLModifier=function(e){return i=e,this},this.addHandler=function(e,t){return l.push(e,t),this},this.removeHandler=function(e){let t=l.indexOf(e);return -1!==t&&l.splice(t,2),this},this.getHandler=function(e){for(let t=0,n=l.length;t<n;t+=2){let n=l[t],i=l[t+1];if(n.global&&(n.lastIndex=0),n.test(e))return i}return null}}}let aU=new aP;class aN{load(){}loadAsync(e,t){let n=this;return new Promise(function(i,r){n.load(e,i,t,r)})}parse(){}setCrossOrigin(e){return this.crossOrigin=e,this}setWithCredentials(e){return this.withCredentials=e,this}setPath(e){return this.path=e,this}setResourcePath(e){return this.resourcePath=e,this}setRequestHeader(e){return this.requestHeader=e,this}constructor(e){this.manager=void 0!==e?e:aU,this.crossOrigin="anonymous",this.withCredentials=!1,this.path="",this.resourcePath="",this.requestHeader={}}}aN.DEFAULT_MATERIAL_NAME="__DEFAULT";let aD="\\[\\]\\.:\\/",aI=RegExp("["+aD+"]","g"),aO="[^"+aD+"]",aF="[^"+aD.replace("\\.","")+"]",az=/((?:WC+[\/:])*)/.source.replace("WC",aO),aB=/(WCOD+)?/.source.replace("WCOD",aF),aH=RegExp("^"+az+aB+/(?:\.(WC+)(?:\[(.+)\])?)?/.source.replace("WC",aO)+/\.(WC+)(?:\[(.+)\])?/.source.replace("WC",aO)+"$"),aV=["material","materials","bones","map"];class aG{getValue(e,t){this.bind();let n=this._targetGroup.nCachedObjects_,i=this._bindings[n];void 0!==i&&i.getValue(e,t)}setValue(e,t){let n=this._bindings;for(let i=this._targetGroup.nCachedObjects_,r=n.length;i!==r;++i)n[i].setValue(e,t)}bind(){let e=this._bindings;for(let t=this._targetGroup.nCachedObjects_,n=e.length;t!==n;++t)e[t].bind()}unbind(){let e=this._bindings;for(let t=this._targetGroup.nCachedObjects_,n=e.length;t!==n;++t)e[t].unbind()}constructor(e,t,n){let i=n||ak.parseTrackName(t);this._targetGroup=e,this._bindings=e.subscribe_(t,i)}}class ak{static create(e,t,n){return e&&e.isAnimationObjectGroup?new ak.Composite(e,t,n):new ak(e,t,n)}static sanitizeNodeName(e){return e.replace(/\s/g,"_").replace(aI,"")}static parseTrackName(e){let t=aH.exec(e);if(null===t)throw Error("PropertyBinding: Cannot parse trackName: "+e);let n={nodeName:t[2],objectName:t[3],objectIndex:t[4],propertyName:t[5],propertyIndex:t[6]},i=n.nodeName&&n.nodeName.lastIndexOf(".");if(void 0!==i&&-1!==i){let e=n.nodeName.substring(i+1);-1!==aV.indexOf(e)&&(n.nodeName=n.nodeName.substring(0,i),n.objectName=e)}if(null===n.propertyName||0===n.propertyName.length)throw Error("PropertyBinding: can not parse propertyName from trackName: "+e);return n}static findNode(e,t){if(void 0===t||""===t||"."===t||-1===t||t===e.name||t===e.uuid)return e;if(e.skeleton){let n=e.skeleton.getBoneByName(t);if(void 0!==n)return n}if(e.children){let n=function(e){for(let i=0;i<e.length;i++){let r=e[i];if(r.name===t||r.uuid===t)return r;let a=n(r.children);if(a)return a}return null},i=n(e.children);if(i)return i}return null}_getValue_unavailable(){}_setValue_unavailable(){}_getValue_direct(e,t){e[t]=this.targetObject[this.propertyName]}_getValue_array(e,t){let n=this.resolvedProperty;for(let i=0,r=n.length;i!==r;++i)e[t++]=n[i]}_getValue_arrayElement(e,t){e[t]=this.resolvedProperty[this.propertyIndex]}_getValue_toArray(e,t){this.resolvedProperty.toArray(e,t)}_setValue_direct(e,t){this.targetObject[this.propertyName]=e[t]}_setValue_direct_setNeedsUpdate(e,t){this.targetObject[this.propertyName]=e[t],this.targetObject.needsUpdate=!0}_setValue_direct_setMatrixWorldNeedsUpdate(e,t){this.targetObject[this.propertyName]=e[t],this.targetObject.matrixWorldNeedsUpdate=!0}_setValue_array(e,t){let n=this.resolvedProperty;for(let i=0,r=n.length;i!==r;++i)n[i]=e[t++]}_setValue_array_setNeedsUpdate(e,t){let n=this.resolvedProperty;for(let i=0,r=n.length;i!==r;++i)n[i]=e[t++];this.targetObject.needsUpdate=!0}_setValue_array_setMatrixWorldNeedsUpdate(e,t){let n=this.resolvedProperty;for(let i=0,r=n.length;i!==r;++i)n[i]=e[t++];this.targetObject.matrixWorldNeedsUpdate=!0}_setValue_arrayElement(e,t){this.resolvedProperty[this.propertyIndex]=e[t]}_setValue_arrayElement_setNeedsUpdate(e,t){this.resolvedProperty[this.propertyIndex]=e[t],this.targetObject.needsUpdate=!0}_setValue_arrayElement_setMatrixWorldNeedsUpdate(e,t){this.resolvedProperty[this.propertyIndex]=e[t],this.targetObject.matrixWorldNeedsUpdate=!0}_setValue_fromArray(e,t){this.resolvedProperty.fromArray(e,t)}_setValue_fromArray_setNeedsUpdate(e,t){this.resolvedProperty.fromArray(e,t),this.targetObject.needsUpdate=!0}_setValue_fromArray_setMatrixWorldNeedsUpdate(e,t){this.resolvedProperty.fromArray(e,t),this.targetObject.matrixWorldNeedsUpdate=!0}_getValue_unbound(e,t){this.bind(),this.getValue(e,t)}_setValue_unbound(e,t){this.bind(),this.setValue(e,t)}bind(){let e=this.node,t=this.parsedPath,n=t.objectName,i=t.propertyName,r=t.propertyIndex;if(e||(e=ak.findNode(this.rootNode,t.nodeName),this.node=e),this.getValue=this._getValue_unavailable,this.setValue=this._setValue_unavailable,!e)return void console.warn("THREE.PropertyBinding: No target node found for track: "+this.path+".");if(n){let i=t.objectIndex;switch(n){case"materials":if(!e.material)return void console.error("THREE.PropertyBinding: Can not bind to material as node does not have a material.",this);if(!e.material.materials)return void console.error("THREE.PropertyBinding: Can not bind to material.materials as node.material does not have a materials array.",this);e=e.material.materials;break;case"bones":if(!e.skeleton)return void console.error("THREE.PropertyBinding: Can not bind to bones as node does not have a skeleton.",this);e=e.skeleton.bones;for(let t=0;t<e.length;t++)if(e[t].name===i){i=t;break}break;case"map":if("map"in e){e=e.map;break}if(!e.material)return void console.error("THREE.PropertyBinding: Can not bind to material as node does not have a material.",this);if(!e.material.map)return void console.error("THREE.PropertyBinding: Can not bind to material.map as node.material does not have a map.",this);e=e.material.map;break;default:if(void 0===e[n])return void console.error("THREE.PropertyBinding: Can not bind to objectName of node undefined.",this);e=e[n]}if(void 0!==i){if(void 0===e[i])return void console.error("THREE.PropertyBinding: Trying to bind to objectIndex of objectName, but is undefined.",this,e);e=e[i]}}let a=e[i];if(void 0===a)return void console.error("THREE.PropertyBinding: Trying to update property for track: "+t.nodeName+"."+i+" but it wasn't found.",e);let s=this.Versioning.None;this.targetObject=e,void 0!==e.needsUpdate?s=this.Versioning.NeedsUpdate:void 0!==e.matrixWorldNeedsUpdate&&(s=this.Versioning.MatrixWorldNeedsUpdate);let o=this.BindingType.Direct;if(void 0!==r){if("morphTargetInfluences"===i){if(!e.geometry)return void console.error("THREE.PropertyBinding: Can not bind to morphTargetInfluences because node does not have a geometry.",this);if(!e.geometry.morphAttributes)return void console.error("THREE.PropertyBinding: Can not bind to morphTargetInfluences because node does not have a geometry.morphAttributes.",this);void 0!==e.morphTargetDictionary[r]&&(r=e.morphTargetDictionary[r])}o=this.BindingType.ArrayElement,this.resolvedProperty=a,this.propertyIndex=r}else void 0!==a.fromArray&&void 0!==a.toArray?(o=this.BindingType.HasFromToArray,this.resolvedProperty=a):Array.isArray(a)?(o=this.BindingType.EntireArray,this.resolvedProperty=a):this.propertyName=i;this.getValue=this.GetterByBindingType[o],this.setValue=this.SetterByBindingTypeAndVersioning[o][s]}unbind(){this.node=null,this.getValue=this._getValue_unbound,this.setValue=this._setValue_unbound}constructor(e,t,n){this.path=t,this.parsedPath=n||ak.parseTrackName(t),this.node=ak.findNode(e,this.parsedPath.nodeName),this.rootNode=e,this.getValue=this._getValue_unbound,this.setValue=this._setValue_unbound}}ak.Composite=aG,ak.prototype.BindingType={Direct:0,EntireArray:1,ArrayElement:2,HasFromToArray:3},ak.prototype.Versioning={None:0,NeedsUpdate:1,MatrixWorldNeedsUpdate:2},ak.prototype.GetterByBindingType=[ak.prototype._getValue_direct,ak.prototype._getValue_array,ak.prototype._getValue_arrayElement,ak.prototype._getValue_toArray],ak.prototype.SetterByBindingTypeAndVersioning=[[ak.prototype._setValue_direct,ak.prototype._setValue_direct_setNeedsUpdate,ak.prototype._setValue_direct_setMatrixWorldNeedsUpdate],[ak.prototype._setValue_array,ak.prototype._setValue_array_setNeedsUpdate,ak.prototype._setValue_array_setMatrixWorldNeedsUpdate],[ak.prototype._setValue_arrayElement,ak.prototype._setValue_arrayElement_setNeedsUpdate,ak.prototype._setValue_arrayElement_setMatrixWorldNeedsUpdate],[ak.prototype._setValue_fromArray,ak.prototype._setValue_fromArray_setNeedsUpdate,ak.prototype._setValue_fromArray_setMatrixWorldNeedsUpdate]],new Float32Array(1),"undefined"!=typeof __THREE_DEVTOOLS__&&__THREE_DEVTOOLS__.dispatchEvent(new CustomEvent("register",{detail:{revision:"158"}})),window.__THREE__?console.warn("WARNING: Multiple instances of Three.js being imported."):window.__THREE__="158"}}]);