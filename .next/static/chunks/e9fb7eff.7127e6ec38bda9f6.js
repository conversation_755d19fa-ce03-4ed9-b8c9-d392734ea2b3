"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[917],{2367:(e,t,r)=>{let i,s,n,a,o,l;r.d(t,{uV1:()=>gO});var u=r(9220);let d=["alphaMap","alphaTest","anisotropy","anisotropyMap","anisotropyRotation","aoMap","attenuationColor","attenuationDistance","bumpMap","clearcoat","clearcoatMap","clearcoatNormalMap","clearcoatNormalScale","clearcoatRoughness","color","dispersion","displacementMap","emissive","emissiveMap","envMap","gradientMap","ior","iridescence","iridescenceIOR","iridescenceMap","iridescenceThicknessMap","lightMap","map","matcap","metalness","metalnessMap","normalMap","normalScale","opacity","roughness","roughnessMap","sheen","sheenColor","sheenColorMap","sheenRoughnessMap","shininess","specular","specularColor","specularColorMap","specularIntensity","specularIntensityMap","specularMap","thickness","transmission","transmissionMap"];class h{firstInitialization(e){return!1===this.renderObjects.has(e)&&(this.getRenderObjectData(e),!0)}needsVelocity(e){let t=e.getMRT();return null!==t&&t.has("velocity")}getRenderObjectData(e){let t=this.renderObjects.get(e);if(void 0===t){let{geometry:r,material:i,object:s}=e;if(t={material:this.getMaterialData(i),geometry:{id:r.id,attributes:this.getAttributesData(r.attributes),indexVersion:r.index?r.index.version:null,drawRange:{start:r.drawRange.start,count:r.drawRange.count}},worldMatrix:s.matrixWorld.clone()},s.center&&(t.center=s.center.clone()),s.morphTargetInfluences&&(t.morphTargetInfluences=s.morphTargetInfluences.slice()),null!==e.bundle&&(t.version=e.bundle.version),t.material.transmission>0){let{width:r,height:i}=e.context;t.bufferWidth=r,t.bufferHeight=i}this.renderObjects.set(e,t)}return t}getAttributesData(e){let t={};for(let r in e){let i=e[r];t[r]={version:i.version}}return t}containsNode(e){let t=e.material;for(let e in t)if(t[e]&&t[e].isNode)return!0;return null!==e.renderer.overrideNodes.modelViewMatrix||null!==e.renderer.overrideNodes.modelNormalViewMatrix}getMaterialData(e){let t={};for(let r of this.refreshUniforms){let i=e[r];null!=i&&("object"==typeof i&&void 0!==i.clone?!0===i.isTexture?t[r]={id:i.id,version:i.version}:t[r]=i.clone():t[r]=i)}return t}equals(e){let{object:t,material:r,geometry:i}=e,s=this.getRenderObjectData(e);if(!0!==s.worldMatrix.equals(t.matrixWorld))return s.worldMatrix.copy(t.matrixWorld),!1;let n=s.material;for(let e in n){let t=n[e],i=r[e];if(void 0!==t.equals){if(!1===t.equals(i))return t.copy(i),!1}else if(!0===i.isTexture){if(t.id!==i.id||t.version!==i.version)return t.id=i.id,t.version=i.version,!1}else if(t!==i)return n[e]=i,!1}if(n.transmission>0){let{width:t,height:r}=e.context;if(s.bufferWidth!==t||s.bufferHeight!==r)return s.bufferWidth=t,s.bufferHeight=r,!1}let a=s.geometry,o=i.attributes,l=a.attributes,u=Object.keys(l),d=Object.keys(o);if(a.id!==i.id)return a.id=i.id,!1;if(u.length!==d.length)return s.geometry.attributes=this.getAttributesData(o),!1;for(let e of u){let t=l[e],r=o[e];if(void 0===r)return delete l[e],!1;if(t.version!==r.version)return t.version=r.version,!1}let h=i.index,c=a.indexVersion,p=h?h.version:null;if(c!==p)return a.indexVersion=p,!1;if(a.drawRange.start!==i.drawRange.start||a.drawRange.count!==i.drawRange.count)return a.drawRange.start=i.drawRange.start,a.drawRange.count=i.drawRange.count,!1;if(s.morphTargetInfluences){let e=!1;for(let r=0;r<s.morphTargetInfluences.length;r++)s.morphTargetInfluences[r]!==t.morphTargetInfluences[r]&&(e=!0);if(e)return!0}return s.center&&!1===s.center.equals(t.center)?s.center.copy(t.center):null!==e.bundle&&(s.version=e.bundle.version),!0}needsRefresh(e,t){if(this.hasNode||this.hasAnimation||this.firstInitialization(e)||this.needsVelocity(t.renderer))return!0;let{renderId:r}=t;if(this.renderId!==r)return this.renderId=r,!0;let i=!0===e.object.static,s=null!==e.bundle&&!0===e.bundle.static&&this.getRenderObjectData(e).version===e.bundle.version;return!i&&!s&&!0!==this.equals(e)}constructor(e){this.renderObjects=new WeakMap,this.hasNode=this.containsNode(e),this.hasAnimation=!0===e.object.isSkinnedMesh,this.refreshUniforms=d,this.renderId=0}}function c(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=0xdeadbeef^t,i=0x41c6ce57^t;if(e instanceof Array)for(let t=0,s;t<e.length;t++)r=Math.imul(r^(s=e[t]),0x9e3779b1),i=Math.imul(i^s,0x5f356495);else for(let t=0,s;t<e.length;t++)r=Math.imul(r^(s=e.charCodeAt(t)),0x9e3779b1),i=Math.imul(i^s,0x5f356495);return r=Math.imul(r^r>>>16,0x85ebca6b)^Math.imul(i^i>>>13,0xc2b2ae35),0x100000000*(2097151&(i=Math.imul(i^i>>>16,0x85ebca6b)^Math.imul(r^r>>>13,0xc2b2ae35)))+(r>>>0)}let p=e=>c(e),g=e=>c(e),m=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return c(t)};function f(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=[];for(let{property:i,childNode:s}of(!0===e.isNode&&(r.push(e.id),e=e.getSelf()),y(e)))r.push(c(i.slice(0,-4)),s.getCacheKey(t));return c(r)}function*y(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];for(let r in e){if(!0===r.startsWith("_"))continue;let i=e[r];if(!0===Array.isArray(i))for(let e=0;e<i.length;e++){let s=i[e];s&&(!0===s.isNode||t&&"function"==typeof s.toJSON)&&(yield{property:r,index:e,childNode:s})}else if(i&&!0===i.isNode)yield{property:r,childNode:i};else if("object"==typeof i)for(let e in i){let s=i[e];s&&(!0===s.isNode||t&&"function"==typeof s.toJSON)&&(yield{property:r,index:e,childNode:s})}}}let x=new Map([[1,"float"],[2,"vec2"],[3,"vec3"],[4,"vec4"],[9,"mat3"],[16,"mat4"]]),b=new WeakMap;function T(e){return x.get(e)}function v(e){if(null==e)return null;let t=typeof e;if(!0===e.isNode)return"node";if("number"===t)return"float";if("boolean"===t)return"bool";if("string"===t)return"string";if("function"===t)return"shader";else if(!0===e.isVector2)return"vec2";else if(!0===e.isVector3)return"vec3";else if(!0===e.isVector4)return"vec4";else if(!0===e.isMatrix2)return"mat2";else if(!0===e.isMatrix3)return"mat3";else if(!0===e.isMatrix4)return"mat4";else if(!0===e.isColor)return"color";else if(e instanceof ArrayBuffer)return"ArrayBuffer";return null}function _(e){for(var t=arguments.length,r=Array(t>1?t-1:0),i=1;i<t;i++)r[i-1]=arguments[i];let s=e?e.slice(-4):void 0;if(1===r.length&&("vec2"===s?r=[r[0],r[0]]:"vec3"===s?r=[r[0],r[0],r[0]]:"vec4"===s&&(r=[r[0],r[0],r[0],r[0]])),"color"===e)return new u.Q1f(...r);if("vec2"===s)return new u.I9Y(...r);if("vec3"===s)return new u.Pq0(...r);if("vec4"===s)return new u.IUQ(...r);if("mat2"===s)return new u.k_V(...r);else if("mat3"===s)return new u.dwI(...r);else if("mat4"===s)return new u.kn4(...r);else if("bool"===e)return r[0]||!1;else if("float"===e||"int"===e||"uint"===e)return r[0]||0;else if("string"===e)return r[0]||"";else if("ArrayBuffer"===e)return R(r[0]);return null}function N(e){let t=b.get(e);return void 0===t&&(t={},b.set(e,t)),t}function S(e){let t="",r=new Uint8Array(e);for(let e=0;e<r.length;e++)t+=String.fromCharCode(r[e]);return btoa(t)}function R(e){return Uint8Array.from(atob(e),e=>e.charCodeAt(0)).buffer}let A={VERTEX:"vertex"},E={NONE:"none",FRAME:"frame",RENDER:"render",OBJECT:"object"},C={READ_ONLY:"readOnly",WRITE_ONLY:"writeOnly",READ_WRITE:"readWrite"},w=["setup","analyze","generate"],M=["fragment","vertex","compute"],B=["x","y","z","w"],P=0;class F extends u.Qev{static get type(){return"Node"}set needsUpdate(e){!0===e&&this.version++}get type(){return this.constructor.type}onUpdate(e,t){return this.updateType=t,this.update=e.bind(this.getSelf()),this}onFrameUpdate(e){return this.onUpdate(e,E.FRAME)}onRenderUpdate(e){return this.onUpdate(e,E.RENDER)}onObjectUpdate(e){return this.onUpdate(e,E.OBJECT)}onReference(e){return this.updateReference=e.bind(this.getSelf()),this}getSelf(){return this.self||this}updateReference(){return this}isGlobal(){return this.global}*getChildren(){for(let{childNode:e}of y(this))yield e}dispose(){this.dispatchEvent({type:"dispose"})}traverse(e){for(let t of(e(this),this.getChildren()))t.traverse(e)}getCacheKey(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return(!0===(e=e||this.version!==this._cacheKeyVersion)||null===this._cacheKey)&&(this._cacheKey=m(f(this,e),this.customCacheKey()),this._cacheKeyVersion=this.version),this._cacheKey}customCacheKey(){return 0}getScope(){return this}getHash(){return this.uuid}getUpdateType(){return this.updateType}getUpdateBeforeType(){return this.updateBeforeType}getUpdateAfterType(){return this.updateAfterType}getElementType(e){let t=this.getNodeType(e);return e.getElementType(t)}getMemberType(){return"void"}getNodeType(e){let t=e.getNodeProperties(this);return t.outputNode?t.outputNode.getNodeType(e):this.nodeType}getShared(e){let t=this.getHash(e);return e.getNodeFromHash(t)||this}setup(e){let t=e.getNodeProperties(this),r=0;for(let e of this.getChildren())t["node"+r++]=e;return t.outputNode||null}analyze(e){if(1===e.increaseUsage(this))for(let t of Object.values(e.getNodeProperties(this)))t&&!0===t.isNode&&t.build(e)}generate(e,t){let{outputNode:r}=e.getNodeProperties(this);if(r&&!0===r.isNode)return r.build(e,t)}updateBefore(){console.warn("Abstract function.")}updateAfter(){console.warn("Abstract function.")}update(){console.warn("Abstract function.")}build(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=this.getShared(e);if(this!==r)return r.build(e,t);e.addNode(this),e.addChain(this);let i=null,s=e.getBuildStage();if("setup"===s){this.updateReference(e);let t=e.getNodeProperties(this);if(!0!==t.initialized){t.initialized=!0;let r=this.setup(e),i=r&&!0===r.isNode;for(let r of Object.values(t))if(r&&!0===r.isNode){if(!0===r.parents){let t=e.getNodeProperties(r);t.parents=t.parents||[],t.parents.push(this)}r.build(e)}i&&r.build(e),t.outputNode=r}i=t.outputNode||null}else if("analyze"===s)this.analyze(e);else if("generate"===s)if(1===this.generate.length){let r=this.getNodeType(e),s=e.getDataFromNode(this);void 0===(i=s.snippet)?void 0===s.generated?(s.generated=!0,s.snippet=i=this.generate(e)||""):(console.warn("THREE.Node: Recursion detected.",this),i=""):void 0!==s.flowCodes&&void 0!==e.context.nodeBlock&&e.addFlowCodeHierarchy(this,e.context.nodeBlock),i=e.format(i,r,t)}else i=this.generate(e,t)||"";return e.removeChain(this),e.addSequentialNode(this),i}getSerializeChildren(){return y(this)}serialize(e){let t=this.getSerializeChildren(),r={};for(let{property:i,index:s,childNode:n}of t)void 0!==s?(void 0===r[i]&&(r[i]=Number.isInteger(s)?[]:{}),r[i][s]=n.toJSON(e.meta).uuid):r[i]=n.toJSON(e.meta).uuid;Object.keys(r).length>0&&(e.inputNodes=r)}deserialize(e){if(void 0!==e.inputNodes){let t=e.meta.nodes;for(let r in e.inputNodes)if(Array.isArray(e.inputNodes[r])){let i=[];for(let s of e.inputNodes[r])i.push(t[s]);this[r]=i}else if("object"==typeof e.inputNodes[r]){let i={};for(let s in e.inputNodes[r]){let n=e.inputNodes[r][s];i[s]=t[n]}this[r]=i}else{let i=e.inputNodes[r];this[r]=t[i]}}}toJSON(e){let{uuid:t,type:r}=this,i=void 0===e||"string"==typeof e;i&&(e={textures:{},images:{},nodes:{}});let s=e.nodes[t];function n(e){let t=[];for(let r in e){let i=e[r];delete i.metadata,t.push(i)}return t}if(void 0===s&&(s={uuid:t,type:r,meta:e,metadata:{version:4.6,type:"Node",generator:"Node.toJSON"}},!0!==i&&(e.nodes[s.uuid]=s),this.serialize(s),delete s.meta),i){let t=n(e.textures),r=n(e.images),i=n(e.nodes);t.length>0&&(s.textures=t),r.length>0&&(s.images=r),i.length>0&&(s.nodes=i)}return s}constructor(e=null){super(),this.nodeType=e,this.updateType=E.NONE,this.updateBeforeType=E.NONE,this.updateAfterType=E.NONE,this.uuid=u.cj9.generateUUID(),this.version=0,this.global=!1,this.parents=!1,this.isNode=!0,this._cacheKey=null,this._cacheKeyVersion=0,Object.defineProperty(this,"id",{value:P++})}}class U extends F{static get type(){return"ArrayElementNode"}getNodeType(e){return this.node.getElementType(e)}generate(e){let t=this.indexNode.getNodeType(e),r=this.node.build(e),i=this.indexNode.build(e,!e.isVector(t)&&e.isInteger(t)?t:"uint");return"".concat(r,"[ ").concat(i," ]")}constructor(e,t){super(),this.node=e,this.indexNode=t,this.isArrayElementNode=!0}}class L extends F{static get type(){return"ConvertNode"}getNodeType(e){let t=this.node.getNodeType(e),r=null;for(let i of this.convertTo.split("|"))(null===r||e.getTypeLength(t)===e.getTypeLength(i))&&(r=i);return r}serialize(e){super.serialize(e),e.convertTo=this.convertTo}deserialize(e){super.deserialize(e),this.convertTo=e.convertTo}generate(e,t){let r=this.node,i=this.getNodeType(e),s=r.build(e,i);return e.format(s,i,t)}constructor(e,t){super(),this.node=e,this.convertTo=t}}class I extends F{static get type(){return"TempNode"}hasDependencies(e){return e.getDataFromNode(this).usageCount>1}build(e,t){if("generate"===e.getBuildStage()){let r=e.getVectorType(this.getNodeType(e,t)),i=e.getDataFromNode(this);if(void 0!==i.propertyName)return e.format(i.propertyName,r,t);if("void"!==r&&"void"!==t&&this.hasDependencies(e)){let s=super.build(e,r),n=e.getVarFromNode(this,null,r),a=e.getPropertyName(n);return e.addLineFlowCode("".concat(a," = ").concat(s),this),i.snippet=s,i.propertyName=a,e.format(i.propertyName,r,t)}}return super.build(e,t)}constructor(e=null){super(e),this.isTempNode=!0}}class D extends I{static get type(){return"JoinNode"}getNodeType(e){return null!==this.nodeType?e.getVectorType(this.nodeType):e.getTypeFromLength(this.nodes.reduce((t,r)=>t+e.getTypeLength(r.getNodeType(e)),0))}generate(e,t){let r=this.getNodeType(e),i=e.getTypeLength(r),s=this.nodes,n=e.getComponentType(r),a=[],o=0;for(let t of s){let s;if(o>=i){console.error("THREE.TSL: Length of parameters exceeds maximum length of function '".concat(r,"()' type."));break}let l=t.getNodeType(e),u=e.getTypeLength(l);o+u>i&&(console.error("THREE.TSL: Length of '".concat(r,"()' data exceeds maximum length of output type.")),u=i-o,l=e.getTypeFromLength(u)),o+=u,s=t.build(e,l);let d=e.getComponentType(l);d!==n&&(s=e.format(s,d,n)),a.push(s)}let l="".concat(e.getType(r),"( ").concat(a.join(", ")," )");return e.format(l,r,t)}constructor(e=[],t=null){super(t),this.nodes=e}}let O=B.join("");class V extends F{static get type(){return"SplitNode"}getVectorLength(){let e=this.components.length;for(let t of this.components)e=Math.max(B.indexOf(t)+1,e);return e}getComponentType(e){return e.getComponentType(this.node.getNodeType(e))}getNodeType(e){return e.getTypeFromLength(this.components.length,this.getComponentType(e))}generate(e,t){let r=this.node,i=e.getTypeLength(r.getNodeType(e)),s=null;if(i>1){let n=null;this.getVectorLength()>=i&&(n=e.getTypeFromLength(this.getVectorLength(),this.getComponentType(e)));let a=r.build(e,n);s=this.components.length===i&&this.components===O.slice(0,this.components.length)?e.format(a,n,t):e.format("".concat(a,".").concat(this.components),this.getNodeType(e),t)}else s=r.build(e,t);return s}serialize(e){super.serialize(e),e.components=this.components}deserialize(e){super.deserialize(e),this.components=e.components}constructor(e,t="x"){super(),this.node=e,this.components=t,this.isSplitNode=!0}}class G extends I{static get type(){return"SetNode"}getNodeType(e){return this.sourceNode.getNodeType(e)}generate(e){let{sourceNode:t,components:r,targetNode:i}=this,s=this.getNodeType(e),n=e.getComponentType(i.getNodeType(e)),a=e.getTypeFromLength(r.length,n),o=i.build(e,a),l=t.build(e,s),u=e.getTypeLength(s),d=[];for(let e=0;e<u;e++){let t=B[e];t===r[0]?(d.push(o),e+=r.length-1):d.push(l+"."+t)}return"".concat(e.getType(s),"( ").concat(d.join(", ")," )")}constructor(e,t,r){super(),this.sourceNode=e,this.components=t,this.targetNode=r}}class k extends I{static get type(){return"FlipNode"}getNodeType(e){return this.sourceNode.getNodeType(e)}generate(e){let{components:t,sourceNode:r}=this,i=this.getNodeType(e),s=r.build(e),n=e.getVarFromNode(this),a=e.getPropertyName(n);e.addLineFlowCode(a+" = "+s,this);let o=e.getTypeLength(i),l=[],u=0;for(let e=0;e<o;e++){let r=B[e];r===t[u]?(l.push("1.0 - "+a+"."+r),u++):l.push(a+"."+r)}return"".concat(e.getType(i),"( ").concat(l.join(", ")," )")}constructor(e,t){super(),this.sourceNode=e,this.components=t}}class z extends F{static get type(){return"InputNode"}getNodeType(){return null===this.nodeType?v(this.value):this.nodeType}getInputType(e){return this.getNodeType(e)}setPrecision(e){return this.precision=e,this}serialize(e){super.serialize(e),e.value=this.value,this.value&&this.value.toArray&&(e.value=this.value.toArray()),e.valueType=v(this.value),e.nodeType=this.nodeType,"ArrayBuffer"===e.valueType&&(e.value=S(e.value)),e.precision=this.precision}deserialize(e){super.deserialize(e),this.nodeType=e.nodeType,this.value=Array.isArray(e.value)?_(e.valueType,...e.value):e.value,this.precision=e.precision||null,this.value&&this.value.fromArray&&(this.value=this.value.fromArray(e.value))}generate(){console.warn("Abstract function.")}constructor(e,t=null){super(t),this.isInputNode=!0,this.value=e,this.precision=null}}let H=/float|u?int/;class W extends z{static get type(){return"ConstNode"}generateConst(e){return e.generateConst(this.getNodeType(e),this.value)}generate(e,t){let r=this.getNodeType(e);return H.test(r)&&H.test(t)?e.generateConst(t,this.value):e.format(this.generateConst(e),r,t)}constructor(e,t=null){super(e,t),this.isConstNode=!0}}class q extends F{static get type(){return"MemberNode"}getNodeType(e){return this.node.getMemberType(e,this.property)}generate(e){return this.node.build(e)+"."+this.property}constructor(e,t){super(),this.node=e,this.property=t,this.isMemberNode=!0}}let j=null,X=new Map;function Q(e,t){if(X.has(e))return void console.warn("THREE.TSL: Redefinition of method chaining '".concat(e,"'."));if("function"!=typeof t)throw Error("THREE.TSL: Node element ".concat(e," is not a function"));X.set(e,t)}let Y=e=>e.replace(/r|s/g,"x").replace(/g|t/g,"y").replace(/b|p/g,"z").replace(/a|q/g,"w"),K=e=>Y(e).split("").sort().join(""),Z={setup:(e,t)=>e(ev(t.shift()),...t),get(e,t,r){if("string"==typeof t&&void 0===e[t]){if(!0!==e.isStackNode&&"assign"===t)return function(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return j.assign(r,...t),r};else if(X.has(t)){let i=X.get(t);return e.isStackNode?function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return r.add(i(...t))}:function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return i(r,...t)}}else if("self"===t)return e;else if(t.endsWith("Assign")&&X.has(t.slice(0,t.length-6))){let i=X.get(t.slice(0,t.length-6));return e.isStackNode?function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return r.assign(t[0],i(...t))}:function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return r.assign(i(r,...t))}}else if(!0===/^[xyzwrgbastpq]{1,4}$/.test(t))return eT(new V(r,t=Y(t)));else if(!0===/^set[XYZWRGBASTPQ]{1,4}$/.test(t))return t=K(t.slice(3).toLowerCase()),r=>eT(new G(e,t,r));else if(!0===/^flip[XYZWRGBASTPQ]{1,4}$/.test(t))return t=K(t.slice(4).toLowerCase()),()=>eT(new k(eT(e),t));else if("width"===t||"height"===t||"depth"===t)return"width"===t?t="x":"height"===t?t="y":"depth"===t&&(t="z"),eT(new V(e,t));else if(!0===/^\d+$/.test(t))return eT(new U(r,new W(Number(t),"uint")));else if(!0===/^get$/.test(t))return e=>eT(new q(r,e))}return Reflect.get(e,t,r)},set:(e,t,r,i)=>"string"==typeof t&&void 0===e[t]&&(!0===/^[xyzwrgbastpq]{1,4}$/.test(t)||"width"===t||"height"===t||"depth"===t||!0===/^\d+$/.test(t))?(i[t].assign(r),!0):Reflect.set(e,t,r,i)},$=new WeakMap,J=new WeakMap,ee=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=v(e);if("node"===r){let t=$.get(e);return void 0===t&&(t=new Proxy(e,Z),$.set(e,t),$.set(t,t)),t}return null===t&&("float"===r||"boolean"===r)||r&&"shader"!==r&&"string"!==r?eT(eg(e,t)):"shader"===r?eA(e):e},et=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;for(let r in e)e[r]=eT(e[r],t);return e},er=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=e.length;for(let i=0;i<r;i++)e[i]=eT(e[i],t);return e},ei=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,s=e=>eT(null!==i?Object.assign(e,i):e),n,a=t,o,l;function u(t){let r;return(r=a?/[a-z]/i.test(a)?a+"()":a:e.type,void 0!==o&&t.length<o)?(console.error('THREE.TSL: "'.concat(r,'" parameter length is less than minimum required.')),t.concat(Array(o-t.length).fill(0))):void 0!==l&&t.length>l?(console.error('THREE.TSL: "'.concat(r,'" parameter length exceeds limit.')),t.slice(0,l)):t}return null===t?n=function(){for(var t=arguments.length,r=Array(t),i=0;i<t;i++)r[i]=arguments[i];return s(new e(...e_(u(r))))}:null!==r?(r=eT(r),n=function(){for(var i=arguments.length,n=Array(i),a=0;a<i;a++)n[a]=arguments[a];return s(new e(t,...e_(u(n)),r))}):n=function(){for(var r=arguments.length,i=Array(r),n=0;n<r;n++)i[n]=arguments[n];return s(new e(t,...e_(u(i))))},n.setParameterLength=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return 1===t.length?o=l=t[0]:2===t.length&&([o,l]=t),n},n.setName=e=>(a=e,n),n},es=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),i=1;i<t;i++)r[i-1]=arguments[i];return eT(new e(...e_(r)))};class en extends F{getNodeType(e){return this.shaderNode.nodeType||this.getOutputNode(e).getNodeType(e)}getMemberType(e,t){return this.getOutputNode(e).getMemberType(e,t)}call(e){let{shaderNode:t,inputNodes:r}=this,i=e.getNodeProperties(t);if(i.onceOutput)return i.onceOutput;let s=null;if(t.layout){let i=J.get(e.constructor);void 0===i&&(i=new WeakMap,J.set(e.constructor,i));let n=i.get(t);void 0===n&&(n=eT(e.buildFunctionNode(t)),i.set(t,n)),e.addInclude(n),s=eT(n.call(r))}else{let i=t.jsFunc;s=eT(null!==r||i.length>1?i(r||[],e):i(e))}return t.once&&(i.onceOutput=s),s}getOutputNode(e){let t=e.getNodeProperties(this);return null===t.outputNode&&(t.outputNode=this.setupOutput(e)),t.outputNode}setup(e){return this.getOutputNode(e)}setupOutput(e){return e.addStack(),e.stack.outputNode=this.call(e),e.removeStack()}generate(e,t){return this.getOutputNode(e).build(e,t)}constructor(e,t){super(),this.shaderNode=e,this.inputNodes=t,this.isShaderCallNodeInternal=!0}}class ea extends F{setLayout(e){return this.layout=e,this}call(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return ev(e),eT(new en(this,e))}setup(){return this.call()}constructor(e,t){super(t),this.jsFunc=e,this.layout=null,this.global=!0,this.once=!1}}let eo=[.5,1.5,1/3,1e-6,1e6,Math.PI,2*Math.PI,1/Math.PI,2/Math.PI,1/(2*Math.PI),Math.PI/2],el=new Map;for(let e of[!1,!0])el.set(e,new W(e));let eu=new Map;for(let e of[0,1,2,3])eu.set(e,new W(e,"uint"));let ed=new Map([...eu].map(e=>new W(e.value,"int")));for(let e of[-1,-2])ed.set(e,new W(e,"int"));let eh=new Map([...ed].map(e=>new W(e.value)));for(let e of eo)eh.set(e,new W(e));for(let e of eo)eh.set(-e,new W(-e));let ec={bool:el,uint:eu,ints:ed,float:eh},ep=new Map([...el,...eh]),eg=(e,t)=>ep.has(e)?ep.get(e):!0===e.isNode?e:new W(e,t),em=e=>{try{return e.getNodeType()}catch(e){return}},ef=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return function(){for(var r=arguments.length,i=Array(r),s=0;s<r;s++)i[s]=arguments[s];if((0===i.length||!["bool","float","int","uint"].includes(e)&&i.every(e=>"object"!=typeof e))&&(i=[_(e,...i)]),1===i.length&&null!==t&&t.has(i[0]))return eT(t.get(i[0]));if(1===i.length){let t=eg(i[0],e);return em(t)===e?eT(t):eT(new L(t,e))}return eT(new D(i.map(e=>eg(e)),e))}},ey=e=>"object"==typeof e&&null!==e?e.value:e,ex=e=>null!=e?e.nodeType||e.convertTo||("string"==typeof e?e:null):null;function eb(e,t){return new Proxy(new ea(e,t),Z)}let eT=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return ee(e,t)},ev=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return new et(e,t)},e_=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return new er(e,t)},eN=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return new ei(...t)},eS=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return new es(...t)},eR=0,eA=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=null;null!==t&&("object"==typeof t?r=t.return:("string"==typeof t?r=t:console.error("THREE.TSL: Invalid layout type."),t=null));let i=new eb(e,r),s=function(){let e;for(var t=arguments.length,s=Array(t),n=0;n<t;n++)s[n]=arguments[n];ev(s),e=s[0]&&s[0].isNode?[...s]:s[0];let a=i.call(e);return"void"===r&&a.toStack(),a};if(s.shaderNode=i,s.setLayout=e=>(i.setLayout(e),s),s.once=()=>(i.once=!0,s),null!==t){if("object"!=typeof t.inputs){let e={name:"fn"+eR++,type:r,inputs:[]};for(let r in t)"return"!==r&&e.inputs.push({name:r,type:t[r]});t=e}s.setLayout(t)}return s};Q("toGlobal",e=>(e.global=!0,e));let eE=e=>{j=e},eC=()=>j,ew=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return j.If(...t)};function eM(e){return j&&j.add(e),e}Q("toStack",eM);let eB=new ef("color"),eP=new ef("float",ec.float),eF=new ef("int",ec.ints),eU=new ef("uint",ec.uint),eL=new ef("bool",ec.bool),eI=new ef("vec2"),eD=new ef("ivec2"),eO=new ef("uvec2"),eV=new ef("bvec2"),eG=new ef("vec3"),ek=new ef("ivec3"),ez=new ef("uvec3"),eH=new ef("bvec3"),eW=new ef("vec4"),eq=new ef("ivec4"),ej=new ef("uvec4"),eX=new ef("bvec4"),eQ=new ef("mat2"),eY=new ef("mat3"),eK=new ef("mat4");Q("toColor",eB),Q("toFloat",eP),Q("toInt",eF),Q("toUint",eU),Q("toBool",eL),Q("toVec2",eI),Q("toIVec2",eD),Q("toUVec2",eO),Q("toBVec2",eV),Q("toVec3",eG),Q("toIVec3",ek),Q("toUVec3",ez),Q("toBVec3",eH),Q("toVec4",eW),Q("toIVec4",eq),Q("toUVec4",ej),Q("toBVec4",eX),Q("toMat2",eQ),Q("toMat3",eY),Q("toMat4",eK),Q("element",eN(U).setParameterLength(2)),Q("convert",(e,t)=>eT(new L(eT(e),t))),Q("append",e=>(console.warn("THREE.TSL: .append() has been renamed to .toStack()."),eM(e)));class eZ extends F{static get type(){return"PropertyNode"}getHash(e){return this.name||super.getHash(e)}isGlobal(){return!0}generate(e){let t;return!0===this.varying?(t=e.getVaryingFromNode(this,this.name)).needsInterpolation=!0:t=e.getVarFromNode(this,this.name),e.getPropertyName(t)}constructor(e,t=null,r=!1){super(e),this.name=t,this.varying=r,this.isPropertyNode=!0}}let e$=(e,t)=>eT(new eZ(e,t)),eJ=(e,t)=>eT(new eZ(e,t,!0)),e0=eS(eZ,"vec4","DiffuseColor"),e1=eS(eZ,"vec3","EmissiveColor"),e2=eS(eZ,"float","Roughness"),e3=eS(eZ,"float","Metalness"),e4=eS(eZ,"float","Clearcoat"),e6=eS(eZ,"float","ClearcoatRoughness"),e8=eS(eZ,"vec3","Sheen"),e5=eS(eZ,"float","SheenRoughness"),e9=eS(eZ,"float","Iridescence"),e7=eS(eZ,"float","IridescenceIOR"),te=eS(eZ,"float","IridescenceThickness"),tt=eS(eZ,"float","AlphaT"),tr=eS(eZ,"float","Anisotropy"),ti=eS(eZ,"vec3","AnisotropyT"),ts=eS(eZ,"vec3","AnisotropyB"),tn=eS(eZ,"color","SpecularColor"),ta=eS(eZ,"float","SpecularF90"),to=eS(eZ,"float","Shininess"),tl=eS(eZ,"vec4","Output"),tu=eS(eZ,"float","dashSize"),td=eS(eZ,"float","gapSize"),th=eS(eZ,"float","IOR"),tc=eS(eZ,"float","Transmission"),tp=eS(eZ,"float","Thickness"),tg=eS(eZ,"float","AttenuationDistance"),tm=eS(eZ,"color","AttenuationColor"),tf=eS(eZ,"float","Dispersion");class ty extends F{static get type(){return"UniformGroupNode"}serialize(e){super.serialize(e),e.name=this.name,e.version=this.version,e.shared=this.shared}deserialize(e){super.deserialize(e),this.name=e.name,this.version=e.version,this.shared=e.shared}constructor(e,t=!1,r=1){super("string"),this.name=e,this.shared=t,this.order=r,this.isUniformGroup=!0}}let tx=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return new ty(e,!0,t)},tb=tx("frame"),tT=tx("render"),tv=new ty("object");class t_ extends z{static get type(){return"UniformNode"}label(e){return this.name=e,this}setGroup(e){return this.groupNode=e,this}getGroup(){return this.groupNode}getUniformHash(e){return this.getHash(e)}onUpdate(e,t){let r=this.getSelf();return e=e.bind(r),super.onUpdate(t=>{let i=e(t,r);void 0!==i&&(this.value=i)},t)}generate(e,t){let r=this.getNodeType(e),i=this.getUniformHash(e),s=e.getNodeFromHash(i);void 0===s&&(e.setHashNode(this,i),s=this);let n=s.getInputType(e),a=e.getUniformFromNode(s,n,e.shaderStage,this.name||e.context.label),o=e.getPropertyName(a);return void 0!==e.context.label&&delete e.context.label,e.format(o,r,t)}constructor(e,t=null){super(e,t),this.isUniformNode=!0,this.name="",this.groupNode=tv}}let tN=(e,t)=>{let r=ex(t||e);return eT(new t_(e&&!0===e.isNode?e.node&&e.node.value||e.value:e,r))};class tS extends I{static get type(){return"ArrayNode"}getNodeType(e){return null===this.nodeType&&(this.nodeType=this.values[0].getNodeType(e)),this.nodeType}getElementType(e){return this.getNodeType(e)}generate(e){let t=this.getNodeType(e);return e.generateArray(t,this.count,this.values)}constructor(e,t,r=null){super(e),this.count=t,this.values=r,this.isArrayNode=!0}}let tR=function(){let e;for(var t=arguments.length,r=Array(t),i=0;i<t;i++)r[i]=arguments[i];if(1===r.length){let t=r[0];e=new tS(null,t.length,t)}else e=new tS(r[0],r[1]);return eT(e)};Q("toArray",(e,t)=>tR(Array(t).fill(e)));class tA extends I{static get type(){return"AssignNode"}hasDependencies(){return!1}getNodeType(e,t){return"void"!==t?this.targetNode.getNodeType(e):"void"}needsSplitAssign(e){let{targetNode:t}=this;if(!1===e.isAvailable("swizzleAssign")&&t.isSplitNode&&t.components.length>1){let r=e.getTypeLength(t.node.getNodeType(e));return B.join("").slice(0,r)!==t.components}return!1}generate(e,t){let r,{targetNode:i,sourceNode:s}=this,n=this.needsSplitAssign(e),a=i.getNodeType(e),o=i.context({assign:!0}).build(e),l=s.build(e,a),u=s.getNodeType(e),d=e.getDataFromNode(this);if(!0===d.initialized)"void"!==t&&(r=o);else if(n){let s=e.getVarFromNode(this,null,a),n=e.getPropertyName(s);e.addLineFlowCode("".concat(n," = ").concat(l),this);let u=i.node.context({assign:!0}).build(e);for(let t=0;t<i.components.length;t++){let r=i.components[t];e.addLineFlowCode("".concat(u,".").concat(r," = ").concat(n,"[ ").concat(t," ]"),this)}"void"!==t&&(r=o)}else r="".concat(o," = ").concat(l),("void"===t||"void"===u)&&(e.addLineFlowCode(r,this),"void"!==t&&(r=o));return d.initialized=!0,e.format(r,a,t)}constructor(e,t){super(),this.targetNode=e,this.sourceNode=t}}Q("assign",eN(tA).setParameterLength(2));class tE extends I{static get type(){return"FunctionCallNode"}setParameters(e){return this.parameters=e,this}getParameters(){return this.parameters}getNodeType(e){return this.functionNode.getNodeType(e)}generate(e){let t=[],r=this.functionNode,i=r.getInputs(e),s=this.parameters,n=(t,r)=>{let i,s=r.type;return"pointer"===s?"&"+t.build(e):t.build(e,s)};if(Array.isArray(s)){if(s.length>i.length)console.error("THREE.TSL: The number of provided parameters exceeds the expected number of inputs in 'Fn()'."),s.length=i.length;else if(s.length<i.length)for(console.error("THREE.TSL: The number of provided parameters is less than the expected number of inputs in 'Fn()'.");s.length<i.length;)s.push(eP(0));for(let e=0;e<s.length;e++)t.push(n(s[e],i[e]))}else for(let e of i){let r=s[e.name];void 0!==r?t.push(n(r,e)):(console.error("THREE.TSL: Input '".concat(e.name,"' not found in 'Fn()'.")),t.push(n(eP(0),e)))}let a=r.build(e,"property");return"".concat(a,"( ").concat(t.join(", ")," )")}constructor(e=null,t={}){super(),this.functionNode=e,this.parameters=t}}Q("call",function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),i=1;i<t;i++)r[i-1]=arguments[i];return r=r.length>1||r[0]&&!0===r[0].isNode?e_(r):ev(r[0]),eT(new tE(eT(e),r))});let tC={"==":"equal","!=":"notEqual","<":"lessThan",">":"greaterThan","<=":"lessThanEqual",">=":"greaterThanEqual","%":"mod"};class tw extends I{static get type(){return"OperatorNode"}getOperatorMethod(e,t){return e.getMethod(tC[this.op],t)}getNodeType(e){let t=this.op,r=this.aNode,i=this.bNode,s=r.getNodeType(e),n=void 0!==i?i.getNodeType(e):null;if("void"===s||"void"===n)return"void";if("%"===t)return s;if("~"===t||"&"===t||"|"===t||"^"===t||">>"===t||"<<"===t)return e.getIntegerType(s);if("!"===t||"&&"===t||"||"===t||"^^"===t)return"bool";if("=="===t||"!="===t||"<"===t||">"===t||"<="===t||">="===t){let t=Math.max(e.getTypeLength(s),e.getTypeLength(n));return t>1?"bvec".concat(t):"bool"}else{if(e.isMatrix(s)){if("float"===n)return s;else if(e.isVector(n))return e.getVectorFromMatrix(s);else if(e.isMatrix(n))return s}else if(e.isMatrix(n)){if("float"===s)return n;else if(e.isVector(s))return e.getVectorFromMatrix(n)}return e.getTypeLength(n)>e.getTypeLength(s)?n:s}}generate(e,t){let r=this.op,i=this.aNode,s=this.bNode,n=this.getNodeType(e),a=null,o=null;"void"!==n?(a=i.getNodeType(e),o=void 0!==s?s.getNodeType(e):null,"<"===r||">"===r||"<="===r||">="===r||"=="===r||"!="===r?e.isVector(a)?o=a:e.isVector(o)?a=o:a!==o&&(a=o="float"):">>"===r||"<<"===r?(a=n,o=e.changeComponentType(o,"uint")):"%"===r?(a=n,o=e.isInteger(a)&&e.isInteger(o)?o:a):e.isMatrix(a)?"float"===o?o="float":e.isVector(o)?o=e.getVectorFromMatrix(a):e.isMatrix(o)||(a=o=n):a=e.isMatrix(o)?"float"===a?"float":e.isVector(a)?e.getVectorFromMatrix(o):o=n:o=n):a=o=n;let l=i.build(e,a),d=void 0!==s?s.build(e,o):null,h=e.getFunctionOperator(r);if("void"!==t){let i=e.renderer.coordinateSystem===u.TdN;if("=="===r||"!="===r||"<"===r||">"===r||"<="===r||">="===r)if(!i)return e.format("( ".concat(l," ").concat(r," ").concat(d," )"),n,t);else if(e.isVector(a))return e.format("".concat(this.getOperatorMethod(e,t),"( ").concat(l,", ").concat(d," )"),n,t);else return e.format("( ".concat(l," ").concat(r," ").concat(d," )"),n,t);if("%"===r)if(e.isInteger(o))return e.format("( ".concat(l," % ").concat(d," )"),n,t);else return e.format("".concat(this.getOperatorMethod(e,n),"( ").concat(l,", ").concat(d," )"),n,t);{if("!"===r||"~"===r)return e.format("(".concat(r).concat(l,")"),a,t);if(h)return e.format("".concat(h,"( ").concat(l,", ").concat(d," )"),n,t);if(e.isMatrix(a)&&"float"===o)return e.format("( ".concat(d," ").concat(r," ").concat(l," )"),n,t);if("float"===a&&e.isMatrix(o))return e.format("".concat(l," ").concat(r," ").concat(d),n,t);let s="( ".concat(l," ").concat(r," ").concat(d," )");return!i&&"bool"===n&&e.isVector(a)&&e.isVector(o)&&(s="all".concat(s)),e.format(s,n,t)}}if("void"!==a)if(h)return e.format("".concat(h,"( ").concat(l,", ").concat(d," )"),n,t);else if(e.isMatrix(a)&&"float"===o)return e.format("".concat(d," ").concat(r," ").concat(l),n,t);else return e.format("".concat(l," ").concat(r," ").concat(d),n,t)}serialize(e){super.serialize(e),e.op=this.op}deserialize(e){super.deserialize(e),this.op=e.op}constructor(e,t,r,...i){if(super(),i.length>0){let s=new tw(e,t,r);for(let t=0;t<i.length-1;t++)s=new tw(e,s,i[t]);t=s,r=i[i.length-1]}this.op=e,this.aNode=t,this.bNode=r,this.isOperatorNode=!0}}let tM=eN(tw,"+").setParameterLength(2,1/0).setName("add"),tB=eN(tw,"-").setParameterLength(2,1/0).setName("sub"),tP=eN(tw,"*").setParameterLength(2,1/0).setName("mul"),tF=eN(tw,"/").setParameterLength(2,1/0).setName("div"),tU=eN(tw,"%").setParameterLength(2).setName("mod"),tL=eN(tw,"==").setParameterLength(2).setName("equal"),tI=eN(tw,"!=").setParameterLength(2).setName("notEqual"),tD=eN(tw,"<").setParameterLength(2).setName("lessThan"),tO=eN(tw,">").setParameterLength(2).setName("greaterThan"),tV=eN(tw,"<=").setParameterLength(2).setName("lessThanEqual"),tG=eN(tw,">=").setParameterLength(2).setName("greaterThanEqual"),tk=eN(tw,"&&").setParameterLength(2,1/0).setName("and"),tz=eN(tw,"||").setParameterLength(2,1/0).setName("or"),tH=eN(tw,"!").setParameterLength(1).setName("not"),tW=eN(tw,"^^").setParameterLength(2).setName("xor"),tq=eN(tw,"&").setParameterLength(2).setName("bitAnd"),tj=eN(tw,"~").setParameterLength(2).setName("bitNot"),tX=eN(tw,"|").setParameterLength(2).setName("bitOr"),tQ=eN(tw,"^").setParameterLength(2).setName("bitXor"),tY=eN(tw,"<<").setParameterLength(2).setName("shiftLeft"),tK=eN(tw,">>").setParameterLength(2).setName("shiftRight"),tZ=eA(e=>{let[t]=e;return t.addAssign(1),t}),t$=eA(e=>{let[t]=e;return t.subAssign(1),t}),tJ=eA(e=>{let[t]=e,r=eF(t).toConst();return t.addAssign(1),r}),t0=eA(e=>{let[t]=e,r=eF(t).toConst();return t.subAssign(1),r});Q("add",tM),Q("sub",tB),Q("mul",tP),Q("div",tF),Q("mod",tU),Q("equal",tL),Q("notEqual",tI),Q("lessThan",tD),Q("greaterThan",tO),Q("lessThanEqual",tV),Q("greaterThanEqual",tG),Q("and",tk),Q("or",tz),Q("not",tH),Q("xor",tW),Q("bitAnd",tq),Q("bitNot",tj),Q("bitOr",tX),Q("bitXor",tQ),Q("shiftLeft",tY),Q("shiftRight",tK),Q("incrementBefore",tZ),Q("decrementBefore",t$),Q("increment",tJ),Q("decrement",t0);Q("remainder",(e,t)=>(console.warn('THREE.TSL: "remainder()" is deprecated. Use "mod( int( ... ) )" instead.'),tU(e,t))),Q("modInt",(e,t)=>(console.warn('THREE.TSL: "modInt()" is deprecated. Use "mod( int( ... ) )" instead.'),tU(eF(e),eF(t))));class t1 extends I{static get type(){return"MathNode"}getInputType(e){let t=this.aNode.getNodeType(e),r=this.bNode?this.bNode.getNodeType(e):null,i=this.cNode?this.cNode.getNodeType(e):null,s=e.isMatrix(t)?0:e.getTypeLength(t),n=e.isMatrix(r)?0:e.getTypeLength(r),a=e.isMatrix(i)?0:e.getTypeLength(i);if(s>n&&s>a);else if(n>a)return r;else if(a>s)return i;return t}getNodeType(e){let t=this.method;return t===t1.LENGTH||t===t1.DISTANCE||t===t1.DOT?"float":t===t1.CROSS?"vec3":t===t1.ALL||t===t1.ANY?"bool":t===t1.EQUALS?e.changeComponentType(this.aNode.getNodeType(e),"bool"):this.getInputType(e)}generate(e,t){let r=this.method,i=this.getNodeType(e),s=this.getInputType(e),n=this.aNode,a=this.bNode,o=this.cNode,l=e.renderer.coordinateSystem;if(r===t1.TRANSFORM_DIRECTION){let r=n,i=a;return e.isMatrix(r.getNodeType(e))?i=eW(eG(i),0):r=eW(eG(r),0),ra(tP(r,i).xyz).build(e,t)}{if(r===t1.NEGATE)return e.format("( - "+n.build(e,s)+" )",i,t);if(r===t1.ONE_MINUS)return tB(1,n).build(e,t);if(r===t1.RECIPROCAL)return tF(1,n).build(e,t);if(r===t1.DIFFERENCE)return rg(tB(n,a)).build(e,t);let d=[];return r===t1.CROSS?d.push(n.build(e,i),a.build(e,i)):l===u.TdN&&r===t1.STEP?d.push(n.build(e,1===e.getTypeLength(n.getNodeType(e))?"float":s),a.build(e,s)):l===u.TdN&&(r===t1.MIN||r===t1.MAX)?d.push(n.build(e,s),a.build(e,1===e.getTypeLength(a.getNodeType(e))?"float":s)):r===t1.REFRACT?d.push(n.build(e,s),a.build(e,s),o.build(e,"float")):r===t1.MIX?d.push(n.build(e,s),a.build(e,s),o.build(e,1===e.getTypeLength(o.getNodeType(e))?"float":s)):(l===u.i7u&&r===t1.ATAN&&null!==a&&(r="atan2"),"fragment"!==e.shaderStage&&(r===t1.DFDX||r===t1.DFDY)&&(console.warn("THREE.TSL: '".concat(r,"' is not supported in the ").concat(e.shaderStage," stage.")),r="/*"+r+"*/"),d.push(n.build(e,s)),null!==a&&d.push(a.build(e,s)),null!==o&&d.push(o.build(e,s))),e.format("".concat(e.getMethod(r,i),"( ").concat(d.join(", ")," )"),i,t)}}serialize(e){super.serialize(e),e.method=this.method}deserialize(e){super.deserialize(e),this.method=e.method}constructor(e,t,r=null,i=null){if(super(),(e===t1.MAX||e===t1.MIN)&&arguments.length>3){let s=new t1(e,t,r);for(let t=2;t<arguments.length-1;t++)s=new t1(e,s,arguments[t]);t=s,r=arguments[arguments.length-1],i=null}this.method=e,this.aNode=t,this.bNode=r,this.cNode=i,this.isMathNode=!0}}t1.ALL="all",t1.ANY="any",t1.RADIANS="radians",t1.DEGREES="degrees",t1.EXP="exp",t1.EXP2="exp2",t1.LOG="log",t1.LOG2="log2",t1.SQRT="sqrt",t1.INVERSE_SQRT="inversesqrt",t1.FLOOR="floor",t1.CEIL="ceil",t1.NORMALIZE="normalize",t1.FRACT="fract",t1.SIN="sin",t1.COS="cos",t1.TAN="tan",t1.ASIN="asin",t1.ACOS="acos",t1.ATAN="atan",t1.ABS="abs",t1.SIGN="sign",t1.LENGTH="length",t1.NEGATE="negate",t1.ONE_MINUS="oneMinus",t1.DFDX="dFdx",t1.DFDY="dFdy",t1.ROUND="round",t1.RECIPROCAL="reciprocal",t1.TRUNC="trunc",t1.FWIDTH="fwidth",t1.TRANSPOSE="transpose",t1.BITCAST="bitcast",t1.EQUALS="equals",t1.MIN="min",t1.MAX="max",t1.STEP="step",t1.REFLECT="reflect",t1.DISTANCE="distance",t1.DIFFERENCE="difference",t1.DOT="dot",t1.CROSS="cross",t1.POW="pow",t1.TRANSFORM_DIRECTION="transformDirection",t1.MIX="mix",t1.CLAMP="clamp",t1.REFRACT="refract",t1.SMOOTHSTEP="smoothstep",t1.FACEFORWARD="faceforward";let t2=eP(1e-6),t3=eP(Math.PI),t4=(Math.PI,eN(t1,t1.ALL).setParameterLength(1)),t6=eN(t1,t1.ANY).setParameterLength(1),t8=eN(t1,t1.RADIANS).setParameterLength(1),t5=eN(t1,t1.DEGREES).setParameterLength(1),t9=eN(t1,t1.EXP).setParameterLength(1),t7=eN(t1,t1.EXP2).setParameterLength(1),re=eN(t1,t1.LOG).setParameterLength(1),rt=eN(t1,t1.LOG2).setParameterLength(1),rr=eN(t1,t1.SQRT).setParameterLength(1),ri=eN(t1,t1.INVERSE_SQRT).setParameterLength(1),rs=eN(t1,t1.FLOOR).setParameterLength(1),rn=eN(t1,t1.CEIL).setParameterLength(1),ra=eN(t1,t1.NORMALIZE).setParameterLength(1),ro=eN(t1,t1.FRACT).setParameterLength(1),rl=eN(t1,t1.SIN).setParameterLength(1),ru=eN(t1,t1.COS).setParameterLength(1),rd=eN(t1,t1.TAN).setParameterLength(1),rh=eN(t1,t1.ASIN).setParameterLength(1),rc=eN(t1,t1.ACOS).setParameterLength(1),rp=eN(t1,t1.ATAN).setParameterLength(1,2),rg=eN(t1,t1.ABS).setParameterLength(1),rm=eN(t1,t1.SIGN).setParameterLength(1),rf=eN(t1,t1.LENGTH).setParameterLength(1),ry=eN(t1,t1.NEGATE).setParameterLength(1),rx=eN(t1,t1.ONE_MINUS).setParameterLength(1),rb=eN(t1,t1.DFDX).setParameterLength(1),rT=eN(t1,t1.DFDY).setParameterLength(1),rv=eN(t1,t1.ROUND).setParameterLength(1),r_=eN(t1,t1.RECIPROCAL).setParameterLength(1),rN=eN(t1,t1.TRUNC).setParameterLength(1),rS=eN(t1,t1.FWIDTH).setParameterLength(1),rR=eN(t1,t1.TRANSPOSE).setParameterLength(1),rA=eN(t1,t1.MIN).setParameterLength(2,1/0),rE=eN(t1,t1.MAX).setParameterLength(2,1/0),rC=eN(t1,t1.STEP).setParameterLength(2),rw=eN(t1,t1.REFLECT).setParameterLength(2),rM=eN(t1,t1.DISTANCE).setParameterLength(2),rB=eN(t1,t1.DIFFERENCE).setParameterLength(2),rP=eN(t1,t1.DOT).setParameterLength(2),rF=eN(t1,t1.CROSS).setParameterLength(2),rU=eN(t1,t1.POW).setParameterLength(2),rL=eN(t1,t1.POW,2).setParameterLength(1),rI=eN(t1,t1.POW,3).setParameterLength(1),rD=eN(t1,t1.POW,4).setParameterLength(1),rO=eN(t1,t1.TRANSFORM_DIRECTION).setParameterLength(2),rV=e=>rP(e,e),rG=eN(t1,t1.MIX).setParameterLength(3),rk=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;return eT(new t1(t1.CLAMP,eT(e),eT(t),eT(r)))},rz=eN(t1,t1.REFRACT).setParameterLength(3),rH=eN(t1,t1.SMOOTHSTEP).setParameterLength(3),rW=eN(t1,t1.FACEFORWARD).setParameterLength(3),rq=eA(e=>{let[t]=e;return ro(rl(tU(rP(t.xy,eI(12.9898,78.233)),t3)).mul(43758.5453))});Q("all",t4),Q("any",t6),Q("equals",(e,t)=>(console.warn('THREE.TSL: "equals" is deprecated. Use "equal" inside a vector instead, like: "bvec*( equal( ... ) )"'),tL(e,t))),Q("radians",t8),Q("degrees",t5),Q("exp",t9),Q("exp2",t7),Q("log",re),Q("log2",rt),Q("sqrt",rr),Q("inverseSqrt",ri),Q("floor",rs),Q("ceil",rn),Q("normalize",ra),Q("fract",ro),Q("sin",rl),Q("cos",ru),Q("tan",rd),Q("asin",rh),Q("acos",rc),Q("atan",rp),Q("abs",rg),Q("sign",rm),Q("length",rf),Q("lengthSq",rV),Q("negate",ry),Q("oneMinus",rx),Q("dFdx",rb),Q("dFdy",rT),Q("round",rv),Q("reciprocal",r_),Q("trunc",rN),Q("fwidth",rS),Q("atan2",(e,t)=>(console.warn('THREE.TSL: "atan2" is overloaded. Use "atan" instead.'),rp(e,t))),Q("min",rA),Q("max",rE),Q("step",rC),Q("reflect",rw),Q("distance",rM),Q("dot",rP),Q("cross",rF),Q("pow",rU),Q("pow2",rL),Q("pow3",rI),Q("pow4",rD),Q("transformDirection",rO),Q("mix",(e,t,r)=>rG(t,r,e)),Q("clamp",rk),Q("refract",rz),Q("smoothstep",(e,t,r)=>rH(t,r,e)),Q("faceForward",rW),Q("difference",rB),Q("saturate",e=>rk(e)),Q("cbrt",e=>tP(rm(e),rU(rg(e),1/3))),Q("transpose",rR),Q("rand",rq);class rj extends F{static get type(){return"ConditionalNode"}getNodeType(e){let{ifNode:t,elseNode:r}=e.getNodeProperties(this);if(void 0===t)return this.setup(e),this.getNodeType(e);let i=t.getNodeType(e);if(null!==r){let t=r.getNodeType(e);if(e.getTypeLength(t)>e.getTypeLength(i))return t}return i}setup(e){let t=this.condNode.cache(),r=this.ifNode.cache(),i=this.elseNode?this.elseNode.cache():null,s=e.context.nodeBlock;e.getDataFromNode(r).parentNodeBlock=s,null!==i&&(e.getDataFromNode(i).parentNodeBlock=s);let n=e.getNodeProperties(this);n.condNode=t,n.ifNode=r.context({nodeBlock:r}),n.elseNode=i?i.context({nodeBlock:i}):null}generate(e,t){let r=this.getNodeType(e),i=e.getDataFromNode(this);if(void 0!==i.nodeProperty)return i.nodeProperty;let{condNode:s,ifNode:n,elseNode:a}=e.getNodeProperties(this),o=e.currentFunctionNode,l="void"!==t,u=l?e$(r).build(e):"";i.nodeProperty=u;let d=s.build(e,"bool");e.addFlowCode("\n".concat(e.tab,"if ( ").concat(d," ) {\n\n")).addFlowTab();let h=n.build(e,r);if(h&&(l?h=u+" = "+h+";":(h="return "+h+";",null===o&&(console.warn("THREE.TSL: Return statement used in an inline 'Fn()'. Define a layout struct to allow return values."),h="// "+h))),e.removeFlowTab().addFlowCode(e.tab+"	"+h+"\n\n"+e.tab+"}"),null!==a){e.addFlowCode(" else {\n\n").addFlowTab();let t=a.build(e,r);t&&(l?t=u+" = "+t+";":(t="return "+t+";",null===o&&(console.warn("THREE.TSL: Return statement used in an inline 'Fn()'. Define a layout struct to allow return values."),t="// "+t))),e.removeFlowTab().addFlowCode(e.tab+"	"+t+"\n\n"+e.tab+"}\n\n")}else e.addFlowCode("\n\n");return e.format(u,r,t)}constructor(e,t,r=null){super(),this.condNode=e,this.ifNode=t,this.elseNode=r}}let rX=eN(rj).setParameterLength(2,3);Q("select",rX);Q("cond",function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return console.warn("THREE.TSL: cond() has been renamed to select()."),rX(...t)});class rQ extends F{static get type(){return"ContextNode"}getScope(){return this.node.getScope()}getNodeType(e){return this.node.getNodeType(e)}analyze(e){this.node.build(e)}setup(e){let t=e.getContext();e.setContext({...e.context,...this.value});let r=this.node.build(e);return e.setContext(t),r}generate(e,t){let r=e.getContext();e.setContext({...e.context,...this.value});let i=this.node.build(e,t);return e.setContext(r),i}constructor(e,t={}){super(),this.isContextNode=!0,this.node=e,this.value=t}}let rY=eN(rQ).setParameterLength(1,2);Q("context",rY),Q("label",(e,t)=>rY(e,{label:t}));class rK extends F{static get type(){return"VarNode"}getMemberType(e,t){return this.node.getMemberType(e,t)}getElementType(e){return this.node.getElementType(e)}getNodeType(e){return this.node.getNodeType(e)}generate(e){let{node:t,name:r,readOnly:i}=this,{renderer:s}=e,n=!0===s.backend.isWebGPUBackend,a=!1,o=!1;i&&(a=e.isDeterministic(t),o=n?i:a);let l=e.getVectorType(this.getNodeType(e)),u=t.build(e,l),d=e.getVarFromNode(this,r,l,void 0,o),h=e.getPropertyName(d),c=h;if(o)if(n)c=a?"const ".concat(h):"let ".concat(h);else{let r=e.getArrayCount(t);c="const ".concat(e.getVar(d.type,h,r))}return e.addLineFlowCode("".concat(c," = ").concat(u),this),h}constructor(e,t=null,r=!1){super(),this.node=e,this.name=t,this.global=!0,this.isVarNode=!0,this.readOnly=r}}let rZ=eN(rK);Q("toVar",function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return rZ(e,t).toStack()}),Q("toConst",function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return rZ(e,t,!0).toStack()});Q("temp",e=>(console.warn('TSL: "temp( node )" is deprecated. Use "Var( node )" or "node.toVar()" instead.'),rZ(e)));class r$ extends F{static get type(){return"VaryingNode"}isGlobal(){return!0}setInterpolation(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return this.interpolationType=e,this.interpolationSampling=t,this}getHash(e){return this.name||super.getHash(e)}getNodeType(e){return this.node.getNodeType(e)}setupVarying(e){let t=e.getNodeProperties(this),r=t.varying;if(void 0===r){let i=this.name,s=this.getNodeType(e),n=this.interpolationType,a=this.interpolationSampling;t.varying=r=e.getVaryingFromNode(this,i,s,n,a),t.node=this.node}return r.needsInterpolation||(r.needsInterpolation="fragment"===e.shaderStage),r}setup(e){this.setupVarying(e)}analyze(e){return this.setupVarying(e),this.node.analyze(e)}generate(e){let t=e.getNodeProperties(this),r=this.setupVarying(e),i="fragment"===e.shaderStage&&!0===t.reassignPosition&&e.context.needsPositionReassign;if(void 0===t.propertyName||i){let s=this.getNodeType(e),n=e.getPropertyName(r,A.VERTEX);e.flowNodeFromShaderStage(A.VERTEX,this.node,s,n),t.propertyName=n,i?t.reassignPosition=!1:void 0===t.reassignPosition&&e.context.isPositionNodeInput&&(t.reassignPosition=!0)}return e.getPropertyName(r)}constructor(e,t=null){super(),this.node=e,this.name=t,this.isVaryingNode=!0,this.interpolationType=null,this.interpolationSampling=null}}let rJ=eN(r$).setParameterLength(1,2);Q("toVarying",rJ),Q("toVertexStage",e=>rJ(e)),Q("varying",function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return console.warn("THREE.TSL: .varying() has been renamed to .toVarying()."),rJ(...t)}),Q("vertexStage",function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return console.warn("THREE.TSL: .vertexStage() has been renamed to .toVertexStage()."),rJ(...t)});let r0=eA(e=>{let[t]=e,r=t.mul(.9478672986).add(.0521327014).pow(2.4);return rG(r,t.mul(.0773993808),t.lessThanEqual(.04045))}).setLayout({name:"sRGBTransferEOTF",type:"vec3",inputs:[{name:"color",type:"vec3"}]}),r1=eA(e=>{let[t]=e,r=t.pow(.41666).mul(1.055).sub(.055);return rG(r,t.mul(12.92),t.lessThanEqual(.0031308))}).setLayout({name:"sRGBTransferOETF",type:"vec3",inputs:[{name:"color",type:"vec3"}]}),r2="WorkingColorSpace",r3="OutputColorSpace";class r4 extends I{static get type(){return"ColorSpaceNode"}resolveColorSpace(e,t){return t===r2?u.ppV.workingColorSpace:t===r3?e.context.outputColorSpace||e.renderer.outputColorSpace:t}setup(e){let{colorNode:t}=this,r=this.resolveColorSpace(e,this.source),i=this.resolveColorSpace(e,this.target),s=t;return!1!==u.ppV.enabled&&r!==i&&r&&i&&(u.ppV.getTransfer(r)===u.KLL&&(s=eW(r0(s.rgb),s.a)),u.ppV.getPrimaries(r)!==u.ppV.getPrimaries(i)&&(s=eW(eY(u.ppV._getMatrix(new u.dwI,r,i)).mul(s.rgb),s.a)),u.ppV.getTransfer(i)===u.KLL&&(s=eW(r1(s.rgb),s.a))),s}constructor(e,t,r){super("vec4"),this.colorNode=e,this.source=t,this.target=r}}let r6=(e,t)=>eT(new r4(eT(e),t,r2));Q("toOutputColorSpace",e=>eT(new r4(eT(e),r2,r3))),Q("toWorkingColorSpace",e=>eT(new r4(eT(e),r3,r2))),Q("workingToColorSpace",(e,t)=>eT(new r4(eT(e),r2,t))),Q("colorSpaceToWorking",r6);let r8=class extends U{static get type(){return"ReferenceElementNode"}getNodeType(){return this.referenceNode.uniformType}generate(e){let t=super.generate(e),r=this.referenceNode.getNodeType(),i=this.getNodeType();return e.format(t,r,i)}constructor(e,t){super(e,t),this.referenceNode=e,this.isReferenceElementNode=!0}};class r5 extends F{static get type(){return"ReferenceBaseNode"}setGroup(e){return this.group=e,this}element(e){return eT(new r8(this,eT(e)))}setNodeType(e){let t=tN(null,e).getSelf();null!==this.group&&t.setGroup(this.group),this.node=t}getNodeType(e){return null===this.node&&(this.updateReference(e),this.updateValue()),this.node.getNodeType(e)}getValueFromReference(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.reference,{properties:t}=this,r=e[t[0]];for(let e=1;e<t.length;e++)r=r[t[e]];return r}updateReference(e){return this.reference=null!==this.object?this.object:e.object,this.reference}setup(){return this.updateValue(),this.node}update(){this.updateValue()}updateValue(){null===this.node&&this.setNodeType(this.uniformType);let e=this.getValueFromReference();Array.isArray(e)?this.node.array=e:this.node.value=e}constructor(e,t,r=null,i=null){super(),this.property=e,this.uniformType=t,this.object=r,this.count=i,this.properties=e.split("."),this.reference=r,this.node=null,this.group=null,this.updateType=E.OBJECT}}let r9=(e,t,r)=>eT(new r5(e,t,r));class r7 extends r5{static get type(){return"RendererReferenceNode"}updateReference(e){return this.reference=null!==this.renderer?this.renderer:e.renderer,this.reference}constructor(e,t,r=null){super(e,t,r),this.renderer=r,this.setGroup(tT)}}class ie extends I{static get type(){return"ToneMappingNode"}customCacheKey(){return m(this.toneMapping)}setup(e){let t=this.colorNode||e.context.color,r=this.toneMapping;if(r===u.y_p)return t;let i=null,s=e.renderer.library.getToneMappingFunction(r);return null!==s?i=eW(s(t.rgb,this.exposureNode),t.a):(console.error("ToneMappingNode: Unsupported Tone Mapping configuration.",r),i=t),i}constructor(e,t=ir,r=null){super("vec3"),this.toneMapping=e,this.exposureNode=t,this.colorNode=r}}let it=(e,t,r)=>eT(new ie(e,eT(t),eT(r))),ir=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;return eT(new r7(e,t,r))}("toneMappingExposure","float");Q("toneMapping",(e,t,r)=>it(t,r,e));class ii extends z{static get type(){return"BufferAttributeNode"}getHash(e){if(0===this.bufferStride&&0===this.bufferOffset){let t=e.globalCache.getData(this.value);return void 0===t&&(t={node:this},e.globalCache.setData(this.value,t)),t.node.uuid}return this.uuid}getNodeType(e){return null===this.bufferType&&(this.bufferType=e.getTypeFromAttribute(this.attribute)),this.bufferType}setup(e){if(null!==this.attribute)return;let t=this.getNodeType(e),r=this.value,i=e.getTypeLength(t),s=this.bufferStride||i,n=this.bufferOffset,a=!0===r.isInterleavedBuffer?r:new u.eB$(r,s),o=new u.eHs(a,i,n);a.setUsage(this.usage),this.attribute=o,this.attribute.isInstancedBufferAttribute=this.instanced}generate(e){let t=this.getNodeType(e),r=e.getBufferAttributeFromNode(this,t),i=e.getPropertyName(r),s=null;return"vertex"===e.shaderStage||"compute"===e.shaderStage?(this.name=i,s=i):s=rJ(this).build(e,t),s}getInputType(){return"bufferAttribute"}setUsage(e){return this.usage=e,this.attribute&&!0===this.attribute.isBufferAttribute&&(this.attribute.usage=e),this}setInstanced(e){return this.instanced=e,this}constructor(e,t=null,r=0,i=0){super(e,t),this.isBufferNode=!0,this.bufferType=t,this.bufferStride=r,this.bufferOffset=i,this.usage=u.agE,this.instanced=!1,this.attribute=null,this.global=!0,e&&!0===e.isBufferAttribute&&(this.attribute=e,this.usage=e.usage,this.instanced=e.isInstancedBufferAttribute)}}let is=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0;return eT(new ii(e,t,r,i))},ia=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0;return is(e,t,r,i).setUsage(u.Vnu)},io=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0;return is(e,t,r,i).setInstanced(!0)},il=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0;return ia(e,t,r,i).setInstanced(!0)};Q("toAttribute",e=>is(e.value));class iu extends F{static get type(){return"ComputeNode"}dispose(){this.dispatchEvent({type:"dispose"})}label(e){return this.name=e,this}updateDispatchCount(){let{count:e,workgroupSize:t}=this,r=t[0];for(let e=1;e<t.length;e++)r*=t[e];this.dispatchCount=Math.ceil(e/r)}onInit(e){return this.onInitFunction=e,this}updateBefore(e){let{renderer:t}=e;t.compute(this)}setup(e){let t=this.computeNode.build(e);return t&&(e.getNodeProperties(this).outputComputeNode=t.outputNode,t.outputNode=null),t}generate(e,t){let{shaderStage:r}=e;if("compute"===r){let t=this.computeNode.build(e,"void");""!==t&&e.addLineFlowCode(t,this)}else{let r=e.getNodeProperties(this).outputComputeNode;if(r)return r.build(e,t)}}constructor(e,t,r=[64]){super("void"),this.isComputeNode=!0,this.computeNode=e,this.count=t,this.workgroupSize=r,this.dispatchCount=0,this.version=1,this.name="",this.updateBeforeType=E.OBJECT,this.onInitFunction=null,this.updateDispatchCount()}}Q("compute",(e,t,r)=>eT(new iu(eT(e),t,r)));class id extends F{static get type(){return"CacheNode"}getNodeType(e){let t=e.getCache(),r=e.getCacheFromNode(this,this.parent);e.setCache(r);let i=this.node.getNodeType(e);return e.setCache(t),i}build(e){for(var t=arguments.length,r=Array(t>1?t-1:0),i=1;i<t;i++)r[i-1]=arguments[i];let s=e.getCache(),n=e.getCacheFromNode(this,this.parent);e.setCache(n);let a=this.node.build(e,...r);return e.setCache(s),a}constructor(e,t=!0){super(),this.node=e,this.parent=t,this.isCacheNode=!0}}let ih=(e,t)=>eT(new id(eT(e),t));Q("cache",ih);class ic extends F{static get type(){return"BypassNode"}getNodeType(e){return this.outputNode.getNodeType(e)}generate(e){let t=this.callNode.build(e,"void");return""!==t&&e.addLineFlowCode(t,this),this.outputNode.build(e)}constructor(e,t){super(),this.isBypassNode=!0,this.outputNode=e,this.callNode=t}}Q("bypass",eN(ic).setParameterLength(2));class ip extends F{static get type(){return"RemapNode"}setup(){let{node:e,inLowNode:t,inHighNode:r,outLowNode:i,outHighNode:s,doClamp:n}=this,a=e.sub(t).div(r.sub(t));return!0===n&&(a=a.clamp()),a.mul(s.sub(i)).add(i)}constructor(e,t,r,i=eP(0),s=eP(1)){super(),this.node=e,this.inLowNode=t,this.inHighNode=r,this.outLowNode=i,this.outHighNode=s,this.doClamp=!0}}let ig=eN(ip,null,null,{doClamp:!1}).setParameterLength(3,5),im=eN(ip).setParameterLength(3,5);Q("remap",ig),Q("remapClamp",im);class iy extends F{static get type(){return"ExpressionNode"}generate(e,t){let r=this.getNodeType(e),i=this.snippet;if("void"!==r)return e.format(i,r,t);e.addLineFlowCode(i,this)}constructor(e="",t="void"){super(t),this.snippet=e}}let ix=eN(iy).setParameterLength(1,2);Q("discard",e=>(e?rX(e,ix("discard")):ix("discard")).toStack());class ib extends I{static get type(){return"RenderOutputNode"}setup(e){let{context:t}=e,r=this.colorNode||t.color,i=(null!==this.toneMapping?this.toneMapping:t.toneMapping)||u.y_p,s=(null!==this.outputColorSpace?this.outputColorSpace:t.outputColorSpace)||u.jf0;return i!==u.y_p&&(r=r.toneMapping(i)),s!==u.jf0&&s!==u.ppV.workingColorSpace&&(r=r.workingToColorSpace(s)),r}constructor(e,t,r){super("vec4"),this.colorNode=e,this.toneMapping=t,this.outputColorSpace=r,this.isRenderOutputNode=!0}}Q("renderOutput",function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;return eT(new ib(eT(e),t,r))});class iT extends I{static get type(){return"DebugNode"}getNodeType(e){return this.node.getNodeType(e)}setup(e){return this.node.build(e)}analyze(e){return this.node.build(e)}generate(e){let t=this.callback,r=this.node.build(e),i="--- TSL debug - "+e.shaderStage+" shader ---",s="-".repeat(i.length),n="";return n+="// #"+i+"#\n"+e.flow.code.replace(/^\t/mg,"")+"\n"+("/* ... */ "+r)+" /* ... */\n"+("// #"+s)+"#\n",null!==t?t(e,n):console.log(n),r}constructor(e,t=null){super(),this.node=e,this.callback=t}}Q("debug",function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return eT(new iT(eT(e),t))});class iv extends F{static get type(){return"AttributeNode"}getHash(e){return this.getAttributeName(e)}getNodeType(e){let t=this.nodeType;if(null===t){let r=this.getAttributeName(e);if(e.hasGeometryAttribute(r)){let i=e.geometry.getAttribute(r);t=e.getTypeFromAttribute(i)}else t="float"}return t}setAttributeName(e){return this._attributeName=e,this}getAttributeName(){return this._attributeName}generate(e){let t=this.getAttributeName(e),r=this.getNodeType(e);if(!0!==e.hasGeometryAttribute(t))return console.warn('AttributeNode: Vertex attribute "'.concat(t,'" not found on geometry.')),e.generateConst(r);{let i=e.geometry.getAttribute(t),s=e.getTypeFromAttribute(i),n=e.getAttribute(t,s);return"vertex"===e.shaderStage?e.format(n.name,s,r):rJ(this).build(e,r)}}serialize(e){super.serialize(e),e.global=this.global,e._attributeName=this._attributeName}deserialize(e){super.deserialize(e),this.global=e.global,this._attributeName=e._attributeName}constructor(e,t=null){super(t),this.global=!0,this._attributeName=e}}let i_=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return eT(new iv(e,t))},iN=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return i_("uv"+(e>0?e:""),"vec2")};class iS extends F{static get type(){return"TextureSizeNode"}generate(e,t){let r=this.textureNode.build(e,"property"),i=null===this.levelNode?"0":this.levelNode.build(e,"int");return e.format("".concat(e.getMethod("textureDimensions"),"( ").concat(r,", ").concat(i," )"),this.getNodeType(e),t)}constructor(e,t=null){super("uvec2"),this.isTextureSizeNode=!0,this.textureNode=e,this.levelNode=t}}let iR=eN(iS).setParameterLength(1,2);class iA extends t_{static get type(){return"MaxMipLevelNode"}get textureNode(){return this._textureNode}get texture(){return this._textureNode.value}update(){let e=this.texture,t=e.images,r=t&&t.length>0?t[0]&&t[0].image||t[0]:e.image;if(r&&void 0!==r.width){let{width:e,height:t}=r;this.value=Math.log2(Math.max(e,t))}}constructor(e){super(0),this._textureNode=e,this.updateType=E.FRAME}}let iE=eN(iA).setParameterLength(1);class iC extends t_{static get type(){return"TextureNode"}set value(e){this.referenceNode?this.referenceNode.value=e:this._value=e}get value(){return this.referenceNode?this.referenceNode.value:this._value}getUniformHash(){return this.value.uuid}getNodeType(){return!0===this.value.isDepthTexture?"float":this.value.type===u.bkx?"uvec4":this.value.type===u.Yuy?"ivec4":"vec4"}getInputType(){return"texture"}getDefaultUV(){return iN(this.value.channel)}updateReference(){return this.value}getTransformedUV(e){return null===this._matrixUniform&&(this._matrixUniform=tN(this.value.matrix)),this._matrixUniform.mul(eG(e,1)).xy}setUpdateMatrix(e){return this.updateMatrix=e,this.updateType=e?E.OBJECT:E.NONE,this}setupUV(e,t){let r=this.value;return e.isFlipY()&&(r.image instanceof ImageBitmap&&!0===r.flipY||!0===r.isRenderTargetTexture||!0===r.isFramebufferTexture||!0===r.isDepthTexture)&&(t=this.sampler?t.flipY():t.setY(eF(iR(this,this.levelNode).y).sub(t.y).sub(1))),t}setup(e){let t=e.getNodeProperties(this);t.referenceNode=this.referenceNode;let r=this.value;if(!r||!0!==r.isTexture)throw Error("THREE.TSL: `texture( value )` function expects a valid instance of THREE.Texture().");let i=this.uvNode;(null===i||!0===e.context.forceUVContext)&&e.context.getUV&&(i=e.context.getUV(this,e)),i||(i=this.getDefaultUV()),!0===this.updateMatrix&&(i=this.getTransformedUV(i)),i=this.setupUV(e,i);let s=this.levelNode;null===s&&e.context.getTextureLevel&&(s=e.context.getTextureLevel(this)),t.uvNode=i,t.levelNode=s,t.biasNode=this.biasNode,t.compareNode=this.compareNode,t.gradNode=this.gradNode,t.depthNode=this.depthNode}generateUV(e,t){return t.build(e,!0===this.sampler?"vec2":"ivec2")}generateSnippet(e,t,r,i,s,n,a,o){let l,u=this.value;return i?e.generateTextureLevel(u,t,r,i,n):s?e.generateTextureBias(u,t,r,s,n):o?e.generateTextureGrad(u,t,r,o,n):a?e.generateTextureCompare(u,t,r,a,n):!1===this.sampler?e.generateTextureLoad(u,t,r,n):e.generateTexture(u,t,r,n)}generate(e,t){let r=this.value,i=e.getNodeProperties(this),s=super.generate(e,"property");if(/^sampler/.test(t))return s+"_sampler";{if(e.isReference(t))return s;let n=e.getDataFromNode(this),a=n.propertyName;if(void 0===a){let{uvNode:t,levelNode:r,biasNode:o,compareNode:l,depthNode:u,gradNode:d}=i,h=this.generateUV(e,t),c=r?r.build(e,"float"):null,p=o?o.build(e,"float"):null,g=u?u.build(e,"int"):null,m=l?l.build(e,"float"):null,f=d?[d[0].build(e,"vec2"),d[1].build(e,"vec2")]:null,y=e.getVarFromNode(this);a=e.getPropertyName(y);let x=this.generateSnippet(e,s,h,c,p,g,m,f);e.addLineFlowCode("".concat(a," = ").concat(x),this),n.snippet=x,n.propertyName=a}let o=a,l=this.getNodeType(e);return e.needsToWorkingColorSpace(r)&&(o=r6(ix(o,l),r.colorSpace).setup(e).build(e,l)),e.format(o,l,t)}}setSampler(e){return this.sampler=e,this}getSampler(){return this.sampler}uv(e){return console.warn("THREE.TextureNode: .uv() has been renamed. Use .sample() instead."),this.sample(e)}sample(e){let t=this.clone();return t.uvNode=eT(e),t.referenceNode=this.getSelf(),eT(t)}blur(e){let t=this.clone();t.biasNode=eT(e).mul(iE(t)),t.referenceNode=this.getSelf();let r=t.value;return!1===t.generateMipmaps&&(r&&!1===r.generateMipmaps||r.minFilter===u.hxR||r.magFilter===u.hxR)&&(console.warn("THREE.TSL: texture().blur() requires mipmaps and sampling. Use .generateMipmaps=true and .minFilter/.magFilter=THREE.LinearFilter in the Texture."),t.biasNode=null),eT(t)}level(e){let t=this.clone();return t.levelNode=eT(e),t.referenceNode=this.getSelf(),eT(t)}size(e){return iR(this,e)}bias(e){let t=this.clone();return t.biasNode=eT(e),t.referenceNode=this.getSelf(),eT(t)}compare(e){let t=this.clone();return t.compareNode=eT(e),t.referenceNode=this.getSelf(),eT(t)}grad(e,t){let r=this.clone();return r.gradNode=[eT(e),eT(t)],r.referenceNode=this.getSelf(),eT(r)}depth(e){let t=this.clone();return t.depthNode=eT(e),t.referenceNode=this.getSelf(),eT(t)}serialize(e){super.serialize(e),e.value=this.value.toJSON(e.meta).uuid,e.sampler=this.sampler,e.updateMatrix=this.updateMatrix,e.updateType=this.updateType}deserialize(e){super.deserialize(e),this.value=e.meta.textures[e.value],this.sampler=e.sampler,this.updateMatrix=e.updateMatrix,this.updateType=e.updateType}update(){let e=this.value,t=this._matrixUniform;null!==t&&(t.value=e.matrix),!0===e.matrixAutoUpdate&&e.updateMatrix()}clone(){let e=new this.constructor(this.value,this.uvNode,this.levelNode,this.biasNode);return e.sampler=this.sampler,e.depthNode=this.depthNode,e.compareNode=this.compareNode,e.gradNode=this.gradNode,e}constructor(e,t=null,r=null,i=null){super(e),this.isTextureNode=!0,this.uvNode=t,this.levelNode=r,this.biasNode=i,this.compareNode=null,this.depthNode=null,this.gradNode=null,this.sampler=!0,this.updateMatrix=!1,this.updateType=E.NONE,this.referenceNode=null,this._value=e,this._matrixUniform=null,this.setUpdateMatrix(null===t)}}let iw=eN(iC).setParameterLength(1,4),iM=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return iw(...t).setSampler(!1)};class iB extends t_{static get type(){return"BufferNode"}getElementType(e){return this.getNodeType(e)}getInputType(){return"buffer"}constructor(e,t,r=0){super(e,t),this.isBufferNode=!0,this.bufferType=t,this.bufferCount=r}}let iP=(e,t,r)=>eT(new iB(e,t,r));class iF extends U{static get type(){return"UniformArrayElementNode"}generate(e){let t=super.generate(e),r=this.getNodeType(),i=this.node.getPaddedType();return e.format(t,i,r)}constructor(e,t){super(e,t),this.isArrayBufferElementNode=!0}}class iU extends iB{static get type(){return"UniformArrayNode"}getNodeType(){return this.paddedType}getElementType(){return this.elementType}getPaddedType(){let e=this.elementType,t="vec4";return"mat2"===e?t="mat2":!0===/mat/.test(e)?t="mat4":"i"===e.charAt(0)?t="ivec4":"u"===e.charAt(0)&&(t="uvec4"),t}update(){let{array:e,value:t}=this,r=this.elementType;if("float"===r||"int"===r||"uint"===r)for(let r=0;r<e.length;r++)t[4*r]=e[r];else if("color"===r)for(let r=0;r<e.length;r++){let i=4*r,s=e[r];t[i]=s.r,t[i+1]=s.g,t[i+2]=s.b||0}else if("mat2"===r)for(let r=0;r<e.length;r++){let i=4*r,s=e[r];t[i]=s.elements[0],t[i+1]=s.elements[1],t[i+2]=s.elements[2],t[i+3]=s.elements[3]}else if("mat3"===r)for(let r=0;r<e.length;r++){let i=16*r,s=e[r];t[i]=s.elements[0],t[i+1]=s.elements[1],t[i+2]=s.elements[2],t[i+4]=s.elements[3],t[i+5]=s.elements[4],t[i+6]=s.elements[5],t[i+8]=s.elements[6],t[i+9]=s.elements[7],t[i+10]=s.elements[8],t[i+15]=1}else if("mat4"===r)for(let r=0;r<e.length;r++){let i=16*r,s=e[r];for(let e=0;e<s.elements.length;e++)t[i+e]=s.elements[e]}else for(let r=0;r<e.length;r++){let i=4*r,s=e[r];t[i]=s.x,t[i+1]=s.y,t[i+2]=s.z||0,t[i+3]=s.w||0}}setup(e){let t=this.array.length,r=this.elementType,i=Float32Array,s=this.paddedType,n=e.getTypeLength(s);return"i"===r.charAt(0)&&(i=Int32Array),"u"===r.charAt(0)&&(i=Uint32Array),this.value=new i(t*n),this.bufferCount=t,this.bufferType=s,super.setup(e)}element(e){return eT(new iF(this,eT(e)))}constructor(e,t=null){super(null),this.array=e,this.elementType=null===t?v(e[0]):t,this.paddedType=this.getPaddedType(),this.updateType=E.RENDER,this.isArrayBufferNode=!0}}let iL=(e,t)=>eT(new iU(e,t));class iI extends F{generate(){return this.name}constructor(e){super("float"),this.name=e,this.isBuiltinNode=!0}}let iD=eN(iI).setParameterLength(1),iO=tN(0,"uint").label("u_cameraIndex").setGroup(tx("cameraIndex")).toVarying("v_cameraIndex"),iV=tN("float").label("cameraNear").setGroup(tT).onRenderUpdate(e=>{let{camera:t}=e;return t.near}),iG=tN("float").label("cameraFar").setGroup(tT).onRenderUpdate(e=>{let{camera:t}=e;return t.far}),ik=eA(e=>{let t,{camera:r}=e;if(r.isArrayCamera&&r.cameras.length>0){let e=[];for(let t of r.cameras)e.push(t.projectionMatrix);t=iL(e).setGroup(tT).label("cameraProjectionMatrices").element(r.isMultiViewCamera?iD("gl_ViewID_OVR"):iO).toVar("cameraProjectionMatrix")}else t=tN("mat4").label("cameraProjectionMatrix").setGroup(tT).onRenderUpdate(e=>{let{camera:t}=e;return t.projectionMatrix});return t}).once()(),iz=eA(e=>{let t,{camera:r}=e;if(r.isArrayCamera&&r.cameras.length>0){let e=[];for(let t of r.cameras)e.push(t.matrixWorldInverse);t=iL(e).setGroup(tT).label("cameraViewMatrices").element(r.isMultiViewCamera?iD("gl_ViewID_OVR"):iO).toVar("cameraViewMatrix")}else t=tN("mat4").label("cameraViewMatrix").setGroup(tT).onRenderUpdate(e=>{let{camera:t}=e;return t.matrixWorldInverse});return t}).once()(),iH=(e=>{let{camera:t}=e;return t.matrixWorld},tN(new u.Pq0).label("cameraPosition").setGroup(tT).onRenderUpdate((e,t)=>{let{camera:r}=e;return t.value.setFromMatrixPosition(r.matrixWorld)})),iW=new u.iyt;class iq extends F{static get type(){return"Object3DNode"}getNodeType(){let e=this.scope;return e===iq.WORLD_MATRIX?"mat4":e===iq.POSITION||e===iq.VIEW_POSITION||e===iq.DIRECTION||e===iq.SCALE?"vec3":e===iq.RADIUS?"float":void 0}update(e){let t=this.object3d,r=this._uniformNode,i=this.scope;if(i===iq.WORLD_MATRIX)r.value=t.matrixWorld;else if(i===iq.POSITION)r.value=r.value||new u.Pq0,r.value.setFromMatrixPosition(t.matrixWorld);else if(i===iq.SCALE)r.value=r.value||new u.Pq0,r.value.setFromMatrixScale(t.matrixWorld);else if(i===iq.DIRECTION)r.value=r.value||new u.Pq0,t.getWorldDirection(r.value);else if(i===iq.VIEW_POSITION){let i=e.camera;r.value=r.value||new u.Pq0,r.value.setFromMatrixPosition(t.matrixWorld),r.value.applyMatrix4(i.matrixWorldInverse)}else if(i===iq.RADIUS){let i=e.object.geometry;null===i.boundingSphere&&i.computeBoundingSphere(),iW.copy(i.boundingSphere).applyMatrix4(t.matrixWorld),r.value=iW.radius}}generate(e){let t=this.scope;return t===iq.WORLD_MATRIX?this._uniformNode.nodeType="mat4":t===iq.POSITION||t===iq.VIEW_POSITION||t===iq.DIRECTION||t===iq.SCALE?this._uniformNode.nodeType="vec3":t===iq.RADIUS&&(this._uniformNode.nodeType="float"),this._uniformNode.build(e)}serialize(e){super.serialize(e),e.scope=this.scope}deserialize(e){super.deserialize(e),this.scope=e.scope}constructor(e,t=null){super(),this.scope=e,this.object3d=t,this.updateType=E.OBJECT,this._uniformNode=new t_(null)}}iq.WORLD_MATRIX="worldMatrix",iq.POSITION="position",iq.SCALE="scale",iq.VIEW_POSITION="viewPosition",iq.DIRECTION="direction",iq.RADIUS="radius";let ij=eN(iq,iq.POSITION).setParameterLength(1);class iX extends iq{static get type(){return"ModelNode"}update(e){this.object3d=e.object,super.update(e)}constructor(e){super(e)}}iX.DIRECTION;let iQ=eS(iX,iX.WORLD_MATRIX),iY=(iX.POSITION,iX.SCALE,iX.VIEW_POSITION,iX.RADIUS,tN(new u.dwI).onObjectUpdate((e,t)=>{let{object:r}=e;return t.value.getNormalMatrix(r.matrixWorld)})),iK=((e,t)=>{let{object:r}=e;return t.value.copy(r.matrixWorld).invert()},eA(e=>e.renderer.overrideNodes.modelViewMatrix||iZ).once()().toVar("modelViewMatrix")),iZ=iz.mul(iQ),i$=eA(e=>(e.context.isHighPrecisionModelViewMatrix=!0,tN("mat4").onObjectUpdate(e=>{let{object:t,camera:r}=e;return t.modelViewMatrix.multiplyMatrices(r.matrixWorldInverse,t.matrixWorld)}))).once()().toVar("highpModelViewMatrix"),iJ=eA(e=>{let t=e.context.isHighPrecisionModelViewMatrix;return tN("mat3").onObjectUpdate(e=>{let{object:r,camera:i}=e;return!0!==t&&r.modelViewMatrix.multiplyMatrices(i.matrixWorldInverse,r.matrixWorld),r.normalMatrix.getNormalMatrix(r.modelViewMatrix)})}).once()().toVar("highpModelNormalViewMatrix"),i0=i_("position","vec3"),i1=i0.toVarying("positionLocal"),i2=i0.toVarying("positionPrevious"),i3=iQ.mul(i1).xyz.toVarying("v_positionWorld").context({needsPositionReassign:!0}),i4=i1.transformDirection(iQ).toVarying("v_positionWorldDirection").normalize().toVar("positionWorldDirection").context({needsPositionReassign:!0}),i6=eA(e=>e.context.setupPositionView(),"vec3").once()().toVarying("v_positionView").context({needsPositionReassign:!0}),i8=i6.negate().toVarying("v_positionViewDirection").normalize().toVar("positionViewDirection");class i5 extends F{static get type(){return"FrontFacingNode"}generate(e){let{renderer:t,material:r}=e;return t.coordinateSystem===u.TdN&&r.side===u.hsX?"false":e.getFrontFacing()}constructor(){super("bool"),this.isFrontFacingNode=!0}}let i9=eP(eS(i5)).mul(2).sub(1),i7=i_("normal","vec3"),se=eA(e=>!1===e.geometry.hasAttribute("normal")?(console.warn('THREE.TSL: Vertex attribute "normal" not found on geometry.'),eG(0,1,0)):i7,"vec3").once()().toVar("normalLocal"),st=i6.dFdx().cross(i6.dFdy()).normalize().toVar("normalFlat"),sr=eA(e=>{let t;return!0===e.material.flatShading?st:rJ(sl(se),"v_normalView").normalize()},"vec3").once()().toVar("normalView"),si=eA(e=>{let t=sr.transformDirection(iz);return!0!==e.material.flatShading&&(t=rJ(t,"v_normalWorld")),t},"vec3").once()().normalize().toVar("normalWorld"),ss=eA(e=>{let t=e.context.setupNormal().context({getUV:null});return!0!==e.material.flatShading&&(t=t.mul(i9)),t},"vec3").once()().toVar("transformedNormalView"),sn=ss.transformDirection(iz).toVar("transformedNormalWorld"),sa=eA(e=>{let t=e.context.setupClearcoatNormal().context({getUV:null});return!0!==e.material.flatShading&&(t=t.mul(i9)),t},"vec3").once()().toVar("transformedClearcoatNormalView"),so=eA(e=>{let[t,r=iQ]=e,i=eY(r),s=t.div(eG(i[0].dot(i[0]),i[1].dot(i[1]),i[2].dot(i[2])));return i.mul(s).xyz}),sl=eA((e,t)=>{let[r]=e,i=t.renderer.overrideNodes.modelNormalViewMatrix;if(null!==i)return i.transformDirection(r);let s=iY.mul(r);return iz.transformDirection(s)}),su=new u.O9p,sd=new u.kn4,sh=tN(0).onReference(e=>{let{material:t}=e;return t}).onObjectUpdate(e=>{let{material:t}=e;return t.refractionRatio}),sc=tN(1).onReference(e=>{let{material:t}=e;return t}).onObjectUpdate(function(e){let{material:t,scene:r}=e;return t.envMap?t.envMapIntensity:r.environmentIntensity}),sp=tN(new u.kn4).onReference(function(e){return e.material}).onObjectUpdate(function(e){let{material:t,scene:r}=e,i=null!==r.environment&&null===t.envMap?r.environmentRotation:t.envMapRotation;return i?(su.copy(i),sd.makeRotationFromEuler(su)):sd.identity(),sd}),sg=i8.negate().reflect(ss),sm=i8.negate().refract(ss,sh),sf=sg.transformDirection(iz).toVar("reflectVector"),sy=sm.transformDirection(iz).toVar("reflectVector");class sx extends iC{static get type(){return"CubeTextureNode"}getInputType(){return"cubeTexture"}getDefaultUV(){let e=this.value;return e.mapping===u.hy7?sf:e.mapping===u.xFO?sy:(console.error('THREE.CubeTextureNode: Mapping "%s" not supported.',e.mapping),eG(0,0,0))}setUpdateMatrix(){}setupUV(e,t){let r=this.value;return e.renderer.coordinateSystem!==u.i7u&&r.isRenderTargetTexture||(t=eG(t.x.negate(),t.yz)),sp.mul(t)}generateUV(e,t){return t.build(e,"vec3")}constructor(e,t=null,r=null,i=null){super(e,t,r,i),this.isCubeTextureNode=!0}}let sb=eN(sx).setParameterLength(1,4).setName("cubeTexture");class sT extends U{static get type(){return"ReferenceElementNode"}getNodeType(){return this.referenceNode.uniformType}generate(e){let t=super.generate(e),r=this.referenceNode.getNodeType(),i=this.getNodeType();return e.format(t,r,i)}constructor(e,t){super(e,t),this.referenceNode=e,this.isReferenceElementNode=!0}}class sv extends F{static get type(){return"ReferenceNode"}element(e){return eT(new sT(this,eT(e)))}setGroup(e){return this.group=e,this}label(e){return this.name=e,this}setNodeType(e){let t=null;t=null!==this.count?iP(null,e,this.count):Array.isArray(this.getValueFromReference())?iL(null,e):"texture"===e?iw(null):"cubeTexture"===e?sb(null):tN(null,e),null!==this.group&&t.setGroup(this.group),null!==this.name&&t.label(this.name),this.node=t.getSelf()}getNodeType(e){return null===this.node&&(this.updateReference(e),this.updateValue()),this.node.getNodeType(e)}getValueFromReference(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.reference,{properties:t}=this,r=e[t[0]];for(let e=1;e<t.length;e++)r=r[t[e]];return r}updateReference(e){return this.reference=null!==this.object?this.object:e.object,this.reference}setup(){return this.updateValue(),this.node}update(){this.updateValue()}updateValue(){null===this.node&&this.setNodeType(this.uniformType);let e=this.getValueFromReference();Array.isArray(e)?this.node.array=e:this.node.value=e}constructor(e,t,r=null,i=null){super(),this.property=e,this.uniformType=t,this.object=r,this.count=i,this.properties=e.split("."),this.reference=r,this.node=null,this.group=null,this.name=null,this.updateType=E.OBJECT}}let s_=(e,t,r)=>eT(new sv(e,t,r)),sN=(e,t,r,i)=>eT(new sv(e,t,i,r));class sS extends sv{static get type(){return"MaterialReferenceNode"}updateReference(e){return this.reference=null!==this.material?this.material:e.material,this.reference}constructor(e,t,r=null){super(e,t,r),this.material=r,this.isMaterialReferenceNode=!0}}let sR=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;return eT(new sS(e,t,r))},sA=eA(e=>(!1===e.geometry.hasAttribute("tangent")&&e.geometry.computeTangents(),i_("tangent","vec4")))(),sE=sA.xyz.toVar("tangentLocal"),sC=iK.mul(eW(sE,0)).xyz.toVarying("v_tangentView").normalize().toVar("tangentView"),sw=sC.transformDirection(iz).toVarying("v_tangentWorld").normalize().toVar("tangentWorld"),sM=sC.toVar("transformedTangentView"),sB=eA((e,t)=>{let[r,i]=e,s=r.mul(sA.w).xyz;return!0!==t.material.flatShading&&(s=rJ(r,i)),s}).once(),sP=sB(sr.cross(sC),"v_bitangentView").normalize().toVar("bitangentView"),sF=eY(sC,sP,sr),sU=(()=>{let e=ts.cross(i8);return rG(e=e.cross(ts).normalize(),ss,tr.mul(e2.oneMinus()).oneMinus().pow2().pow2()).normalize()})(),sL=eA(e=>{let{eye_pos:t,surf_norm:r,mapN:i,uv:s}=e,n=t.dFdx(),a=t.dFdy(),o=s.dFdx(),l=s.dFdy(),u=a.cross(r),d=r.cross(n),h=u.mul(o.x).add(d.mul(l.x)),c=u.mul(o.y).add(d.mul(l.y)),p=h.dot(h).max(c.dot(c)),g=i9.mul(p.inverseSqrt());return tM(h.mul(i.x,g),c.mul(i.y,g),r.mul(i.z)).normalize()});class sI extends I{static get type(){return"NormalMapNode"}setup(e){let{normalMapType:t,scaleNode:r}=this,i=this.node.mul(2).sub(1);null!==r&&(i=eG(i.xy.mul(r),i.z));let s=null;return t===u.vyJ?s=sl(i):t===u.bI3&&(s=!0===e.hasGeometryAttribute("tangent")?sF.mul(i).normalize():sL({eye_pos:i6,surf_norm:sr,mapN:i,uv:iN()})),s}constructor(e,t=null){super("vec3"),this.node=e,this.scaleNode=t,this.normalMapType=u.bI3}}let sD=eN(sI).setParameterLength(1,2),sO=eA(e=>{let{textureNode:t,bumpScale:r}=e,i=e=>t.cache().context({getUV:t=>e(t.uvNode||iN()),forceUVContext:!0}),s=eP(i(e=>e));return eI(eP(i(e=>e.add(e.dFdx()))).sub(s),eP(i(e=>e.add(e.dFdy()))).sub(s)).mul(r)}),sV=eA(e=>{let{surf_pos:t,surf_norm:r,dHdxy:i}=e,s=t.dFdx().normalize(),n=t.dFdy().normalize().cross(r),a=r.cross(s),o=s.dot(n).mul(i9),l=o.sign().mul(i.x.mul(n).add(i.y.mul(a)));return o.abs().mul(r).sub(l).normalize()});class sG extends I{static get type(){return"BumpMapNode"}setup(){let e=null!==this.scaleNode?this.scaleNode:1;return sV({surf_pos:i6,surf_norm:sr,dHdxy:sO({textureNode:this.textureNode,bumpScale:e})})}constructor(e,t=null){super("vec3"),this.textureNode=e,this.scaleNode=t}}let sk=eN(sG).setParameterLength(1,2),sz=new Map;class sH extends F{static get type(){return"MaterialNode"}getCache(e,t){let r=sz.get(e);return void 0===r&&(r=sR(e,t),sz.set(e,r)),r}getFloat(e){return this.getCache(e,"float")}getColor(e){return this.getCache(e,"color")}getTexture(e){return this.getCache("map"===e?"map":e+"Map","texture")}setup(e){let t=e.context.material,r=this.scope,i=null;if(r===sH.COLOR){let e=void 0!==t.color?this.getColor(r):eG();i=t.map&&!0===t.map.isTexture?e.mul(this.getTexture("map")):e}else if(r===sH.OPACITY){let e=this.getFloat(r);i=t.alphaMap&&!0===t.alphaMap.isTexture?e.mul(this.getTexture("alpha")):e}else if(r===sH.SPECULAR_STRENGTH)i=t.specularMap&&!0===t.specularMap.isTexture?this.getTexture("specular").r:eP(1);else if(r===sH.SPECULAR_INTENSITY){let e=this.getFloat(r);i=t.specularIntensityMap&&!0===t.specularIntensityMap.isTexture?e.mul(this.getTexture(r).a):e}else if(r===sH.SPECULAR_COLOR){let e=this.getColor(r);i=t.specularColorMap&&!0===t.specularColorMap.isTexture?e.mul(this.getTexture(r).rgb):e}else if(r===sH.ROUGHNESS){let e=this.getFloat(r);i=t.roughnessMap&&!0===t.roughnessMap.isTexture?e.mul(this.getTexture(r).g):e}else if(r===sH.METALNESS){let e=this.getFloat(r);i=t.metalnessMap&&!0===t.metalnessMap.isTexture?e.mul(this.getTexture(r).b):e}else if(r===sH.EMISSIVE){let e=this.getFloat("emissiveIntensity"),s=this.getColor(r).mul(e);i=t.emissiveMap&&!0===t.emissiveMap.isTexture?s.mul(this.getTexture(r)):s}else if(r===sH.NORMAL)t.normalMap?(i=sD(this.getTexture("normal"),this.getCache("normalScale","vec2"))).normalMapType=t.normalMapType:i=t.bumpMap?sk(this.getTexture("bump").r,this.getFloat("bumpScale")):sr;else if(r===sH.CLEARCOAT){let e=this.getFloat(r);i=t.clearcoatMap&&!0===t.clearcoatMap.isTexture?e.mul(this.getTexture(r).r):e}else if(r===sH.CLEARCOAT_ROUGHNESS){let e=this.getFloat(r);i=t.clearcoatRoughnessMap&&!0===t.clearcoatRoughnessMap.isTexture?e.mul(this.getTexture(r).r):e}else if(r===sH.CLEARCOAT_NORMAL)i=t.clearcoatNormalMap?sD(this.getTexture(r),this.getCache(r+"Scale","vec2")):sr;else if(r===sH.SHEEN){let e=this.getColor("sheenColor").mul(this.getFloat("sheen"));i=t.sheenColorMap&&!0===t.sheenColorMap.isTexture?e.mul(this.getTexture("sheenColor").rgb):e}else if(r===sH.SHEEN_ROUGHNESS){let e=this.getFloat(r);i=(i=t.sheenRoughnessMap&&!0===t.sheenRoughnessMap.isTexture?e.mul(this.getTexture(r).a):e).clamp(.07,1)}else if(r===sH.ANISOTROPY)if(t.anisotropyMap&&!0===t.anisotropyMap.isTexture){let e=this.getTexture(r);i=eQ(nf.x,nf.y,nf.y.negate(),nf.x).mul(e.rg.mul(2).sub(eI(1)).normalize().mul(e.b))}else i=nf;else if(r===sH.IRIDESCENCE_THICKNESS){let e=s_("1","float",t.iridescenceThicknessRange);if(t.iridescenceThicknessMap){let s=s_("0","float",t.iridescenceThicknessRange);i=e.sub(s).mul(this.getTexture(r).g).add(s)}else i=e}else if(r===sH.TRANSMISSION){let e=this.getFloat(r);i=t.transmissionMap?e.mul(this.getTexture(r).r):e}else if(r===sH.THICKNESS){let e=this.getFloat(r);i=t.thicknessMap?e.mul(this.getTexture(r).g):e}else if(r===sH.IOR)i=this.getFloat(r);else if(r===sH.LIGHT_MAP)i=this.getTexture(r).rgb.mul(this.getFloat("lightMapIntensity"));else if(r===sH.AO)i=this.getTexture(r).r.sub(1).mul(this.getFloat("aoMapIntensity")).add(1);else{let t=this.getNodeType(e);i=this.getCache(r,t)}return i}constructor(e){super(),this.scope=e}}sH.ALPHA_TEST="alphaTest",sH.COLOR="color",sH.OPACITY="opacity",sH.SHININESS="shininess",sH.SPECULAR="specular",sH.SPECULAR_STRENGTH="specularStrength",sH.SPECULAR_INTENSITY="specularIntensity",sH.SPECULAR_COLOR="specularColor",sH.REFLECTIVITY="reflectivity",sH.ROUGHNESS="roughness",sH.METALNESS="metalness",sH.NORMAL="normal",sH.CLEARCOAT="clearcoat",sH.CLEARCOAT_ROUGHNESS="clearcoatRoughness",sH.CLEARCOAT_NORMAL="clearcoatNormal",sH.EMISSIVE="emissive",sH.ROTATION="rotation",sH.SHEEN="sheen",sH.SHEEN_ROUGHNESS="sheenRoughness",sH.ANISOTROPY="anisotropy",sH.IRIDESCENCE="iridescence",sH.IRIDESCENCE_IOR="iridescenceIOR",sH.IRIDESCENCE_THICKNESS="iridescenceThickness",sH.IOR="ior",sH.TRANSMISSION="transmission",sH.THICKNESS="thickness",sH.ATTENUATION_DISTANCE="attenuationDistance",sH.ATTENUATION_COLOR="attenuationColor",sH.LINE_SCALE="scale",sH.LINE_DASH_SIZE="dashSize",sH.LINE_GAP_SIZE="gapSize",sH.LINE_WIDTH="linewidth",sH.LINE_DASH_OFFSET="dashOffset",sH.POINT_SIZE="size",sH.DISPERSION="dispersion",sH.LIGHT_MAP="light",sH.AO="ao";let sW=eS(sH,sH.ALPHA_TEST),sq=eS(sH,sH.COLOR),sj=eS(sH,sH.SHININESS),sX=eS(sH,sH.EMISSIVE),sQ=eS(sH,sH.OPACITY),sY=eS(sH,sH.SPECULAR),sK=eS(sH,sH.SPECULAR_INTENSITY),sZ=eS(sH,sH.SPECULAR_COLOR),s$=eS(sH,sH.SPECULAR_STRENGTH),sJ=eS(sH,sH.REFLECTIVITY),s0=eS(sH,sH.ROUGHNESS),s1=eS(sH,sH.METALNESS),s2=eS(sH,sH.NORMAL),s3=eS(sH,sH.CLEARCOAT),s4=eS(sH,sH.CLEARCOAT_ROUGHNESS),s6=eS(sH,sH.CLEARCOAT_NORMAL),s8=eS(sH,sH.ROTATION),s5=eS(sH,sH.SHEEN),s9=eS(sH,sH.SHEEN_ROUGHNESS),s7=eS(sH,sH.ANISOTROPY),ne=eS(sH,sH.IRIDESCENCE),nt=eS(sH,sH.IRIDESCENCE_IOR),nr=eS(sH,sH.IRIDESCENCE_THICKNESS),ni=eS(sH,sH.TRANSMISSION),ns=eS(sH,sH.THICKNESS),nn=eS(sH,sH.IOR),na=eS(sH,sH.ATTENUATION_DISTANCE),no=eS(sH,sH.ATTENUATION_COLOR),nl=eS(sH,sH.LINE_SCALE),nu=eS(sH,sH.LINE_DASH_SIZE),nd=eS(sH,sH.LINE_GAP_SIZE),nh=(sH.LINE_WIDTH,eS(sH,sH.LINE_DASH_OFFSET)),nc=eS(sH,sH.POINT_SIZE),np=eS(sH,sH.DISPERSION),ng=eS(sH,sH.LIGHT_MAP),nm=eS(sH,sH.AO),nf=tN(new u.I9Y).onReference(function(e){return e.material}).onRenderUpdate(function(e){let{material:t}=e;this.value.set(t.anisotropy*Math.cos(t.anisotropyRotation),t.anisotropy*Math.sin(t.anisotropyRotation))}),ny=eA(e=>e.context.setupModelViewProjection(),"vec4").once()().toVarying("v_modelViewProjection");class nx extends F{static get type(){return"IndexNode"}generate(e){let t,r,i=this.getNodeType(e),s=this.scope;if(s===nx.VERTEX)t=e.getVertexIndex();else if(s===nx.INSTANCE)t=e.getInstanceIndex();else if(s===nx.DRAW)t=e.getDrawIndex();else if(s===nx.INVOCATION_LOCAL)t=e.getInvocationLocalIndex();else if(s===nx.INVOCATION_SUBGROUP)t=e.getInvocationSubgroupIndex();else if(s===nx.SUBGROUP)t=e.getSubgroupIndex();else throw Error("THREE.IndexNode: Unknown scope: "+s);return"vertex"===e.shaderStage||"compute"===e.shaderStage?t:rJ(this).build(e,i)}constructor(e){super("uint"),this.scope=e,this.isIndexNode=!0}}nx.VERTEX="vertex",nx.INSTANCE="instance",nx.SUBGROUP="subgroup",nx.INVOCATION_LOCAL="invocationLocal",nx.INVOCATION_SUBGROUP="invocationSubgroup",nx.DRAW="draw";let nb=eS(nx,nx.VERTEX),nT=eS(nx,nx.INSTANCE),nv=(nx.SUBGROUP,nx.INVOCATION_SUBGROUP,nx.INVOCATION_LOCAL,eS(nx,nx.DRAW));class n_ extends F{static get type(){return"InstanceNode"}setup(e){let{count:t,instanceMatrix:r,instanceColor:i}=this,{instanceMatrixNode:s,instanceColorNode:n}=this;if(null===s){if(t<=1e3)s=iP(r.array,"mat4",Math.max(t,1)).element(nT);else{let e=new u.LuO(r.array,16,1);this.buffer=e;let t=r.usage===u.Vnu?il:io;s=eK(t(e,"vec4",16,0),t(e,"vec4",16,4),t(e,"vec4",16,8),t(e,"vec4",16,12))}this.instanceMatrixNode=s}if(i&&null===n){let e=new u.uWO(i.array,3),t=i.usage===u.Vnu?il:io;this.bufferColor=e,n=eG(t(e,"vec3",3,0)),this.instanceColorNode=n}let a=s.mul(i1).xyz;if(i1.assign(a),e.hasGeometryAttribute("normal")){let e=so(se,s);se.assign(e)}null!==this.instanceColorNode&&eJ("vec3","vInstanceColor").assign(this.instanceColorNode)}update(){this.instanceMatrix.usage!==u.Vnu&&null!==this.buffer&&this.instanceMatrix.version!==this.buffer.version&&(this.buffer.version=this.instanceMatrix.version),this.instanceColor&&this.instanceColor.usage!==u.Vnu&&null!==this.bufferColor&&this.instanceColor.version!==this.bufferColor.version&&(this.bufferColor.version=this.instanceColor.version)}constructor(e,t,r=null){super("void"),this.count=e,this.instanceMatrix=t,this.instanceColor=r,this.instanceMatrixNode=null,this.instanceColorNode=null,this.updateType=E.FRAME,this.buffer=null,this.bufferColor=null}}class nN extends n_{static get type(){return"InstancedMeshNode"}constructor(e){let{count:t,instanceMatrix:r,instanceColor:i}=e;super(t,r,i),this.instancedMesh=e}}let nS=eN(nN).setParameterLength(1);class nR extends F{static get type(){return"BatchNode"}setup(e){null===this.batchingIdNode&&(null===e.getDrawIndex()?this.batchingIdNode=nT:this.batchingIdNode=nv);let t=eA(e=>{let[t]=e,r=eF(iR(iM(this.batchMesh._indirectTexture),0).x),i=eF(t).mod(r),s=eF(t).div(r);return iM(this.batchMesh._indirectTexture,eD(i,s)).x}).setLayout({name:"getIndirectIndex",type:"uint",inputs:[{name:"id",type:"int"}]})(eF(this.batchingIdNode)),r=this.batchMesh._matricesTexture,i=eF(iR(iM(r),0).x),s=eP(t).mul(4).toInt().toVar(),n=s.mod(i),a=s.div(i),o=eK(iM(r,eD(n,a)),iM(r,eD(n.add(1),a)),iM(r,eD(n.add(2),a)),iM(r,eD(n.add(3),a))),l=this.batchMesh._colorsTexture;if(null!==l){let e=eA(e=>{let[t]=e,r=eF(iR(iM(l),0).x);return iM(l,eD(t.mod(r),t.div(r))).rgb}).setLayout({name:"getBatchingColor",type:"vec3",inputs:[{name:"id",type:"int"}]})(t);eJ("vec3","vBatchColor").assign(e)}let u=eY(o);i1.assign(o.mul(i1));let d=se.div(eG(u[0].dot(u[0]),u[1].dot(u[1]),u[2].dot(u[2]))),h=u.mul(d).xyz;se.assign(h),e.hasGeometryAttribute("tangent")&&sE.mulAssign(u)}constructor(e){super("void"),this.batchMesh=e,this.batchingIdNode=null}}let nA=eN(nR).setParameterLength(1);class nE extends U{static get type(){return"StorageArrayElementNode"}set storageBufferNode(e){this.node=e}get storageBufferNode(){return this.node}getMemberType(e,t){let r=this.storageBufferNode.structTypeNode;return r?r.getMemberType(e,t):"void"}setup(e){return!1===e.isAvailable("storageBuffer")&&!0===this.node.isPBO&&e.setupPBO(this.node),super.setup(e)}generate(e,t){let r,i=e.context.assign;if(r=!1===e.isAvailable("storageBuffer")?!0===this.node.isPBO&&!0!==i&&(this.node.value.isInstancedBufferAttribute||"compute"!==e.shaderStage)?e.generatePBO(this):this.node.build(e):super.generate(e),!0!==i){let i=this.getNodeType(e);r=e.format(r,i,t)}return r}constructor(e,t){super(e,t),this.isStorageArrayElementNode=!0}}let nC=eN(nE).setParameterLength(2);class nw extends iB{static get type(){return"StorageBufferNode"}getHash(e){if(0===this.bufferCount){let t=e.globalCache.getData(this.value);return void 0===t&&(t={node:this},e.globalCache.setData(this.value,t)),t.node.uuid}return this.uuid}getInputType(){return this.value.isIndirectStorageBufferAttribute?"indirectStorageBuffer":"storageBuffer"}element(e){return nC(this,e)}setPBO(e){return this.isPBO=e,this}getPBO(){return this.isPBO}setAccess(e){return this.access=e,this}toReadOnly(){return this.setAccess(C.READ_ONLY)}setAtomic(e){return this.isAtomic=e,this}toAtomic(){return this.setAtomic(!0)}getAttributeData(){return null===this._attribute&&(this._attribute=is(this.value),this._varying=rJ(this._attribute)),{attribute:this._attribute,varying:this._varying}}getNodeType(e){if(null!==this.structTypeNode)return this.structTypeNode.getNodeType(e);if(e.isAvailable("storageBuffer")||e.isAvailable("indirectStorageBuffer"))return super.getNodeType(e);let{attribute:t}=this.getAttributeData();return t.getNodeType(e)}generate(e){if(null!==this.structTypeNode&&this.structTypeNode.build(e),e.isAvailable("storageBuffer")||e.isAvailable("indirectStorageBuffer"))return super.generate(e);let{attribute:t,varying:r}=this.getAttributeData(),i=r.build(e);return e.registerTransform(i,t),i}constructor(e,t=null,r=0){var i;let s,n=null;t&&t.isStruct?(s="struct",n=t.layout,(e.isStorageBufferAttribute||e.isStorageInstancedBufferAttribute)&&(r=e.count)):null===t&&(e.isStorageBufferAttribute||e.isStorageInstancedBufferAttribute)?(i=e.itemSize,s=x.get(i),r=e.count):s=t,super(e,s,r),this.isStorageBufferNode=!0,this.structTypeNode=n,this.access=C.READ_WRITE,this.isAtomic=!1,this.isPBO=!1,this._attribute=null,this._varying=null,this.global=!0,!0!==e.isStorageBufferAttribute&&!0!==e.isStorageInstancedBufferAttribute&&(e.isInstancedBufferAttribute?e.isStorageInstancedBufferAttribute=!0:e.isStorageBufferAttribute=!0)}}let nM=new WeakMap;class nB extends F{static get type(){return"SkinningNode"}getSkinnedPosition(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.boneMatricesNode,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.positionNode,{skinIndexNode:r,skinWeightNode:i,bindMatrixNode:s,bindMatrixInverseNode:n}=this,a=e.element(r.x),o=e.element(r.y),l=e.element(r.z),u=e.element(r.w),d=s.mul(t),h=tM(a.mul(i.x).mul(d),o.mul(i.y).mul(d),l.mul(i.z).mul(d),u.mul(i.w).mul(d));return n.mul(h).xyz}getSkinnedNormal(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.boneMatricesNode,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:se,{skinIndexNode:r,skinWeightNode:i,bindMatrixNode:s,bindMatrixInverseNode:n}=this,a=e.element(r.x),o=e.element(r.y),l=e.element(r.z),u=e.element(r.w),d=tM(i.x.mul(a),i.y.mul(o),i.z.mul(l),i.w.mul(u));return(d=n.mul(d).mul(s)).transformDirection(t).xyz}getPreviousSkinnedPosition(e){let t=e.object;return null===this.previousBoneMatricesNode&&(t.skeleton.previousBoneMatrices=new Float32Array(t.skeleton.boneMatrices),this.previousBoneMatricesNode=sN("skeleton.previousBoneMatrices","mat4",t.skeleton.bones.length)),this.getSkinnedPosition(this.previousBoneMatricesNode,i2)}needsPreviousBoneMatrices(e){let t=e.renderer.getMRT();return t&&t.has("velocity")||!0===N(e.object).useVelocity}setup(e){this.needsPreviousBoneMatrices(e)&&i2.assign(this.getPreviousSkinnedPosition(e));let t=this.getSkinnedPosition();if(this.toPositionNode&&this.toPositionNode.assign(t),e.hasGeometryAttribute("normal")){let t=this.getSkinnedNormal();se.assign(t),e.hasGeometryAttribute("tangent")&&sE.assign(t)}return t}generate(e,t){if("void"!==t)return super.generate(e,t)}update(e){let t=e.object&&e.object.skeleton?e.object.skeleton:this.skinnedMesh.skeleton;nM.get(t)!==e.frameId&&(nM.set(t,e.frameId),null!==this.previousBoneMatricesNode&&t.previousBoneMatrices.set(t.boneMatrices),t.update())}constructor(e){super("void"),this.skinnedMesh=e,this.updateType=E.OBJECT,this.skinIndexNode=i_("skinIndex","uvec4"),this.skinWeightNode=i_("skinWeight","vec4"),this.bindMatrixNode=s_("bindMatrix","mat4"),this.bindMatrixInverseNode=s_("bindMatrixInverse","mat4"),this.boneMatricesNode=sN("skeleton.boneMatrices","mat4",e.skeleton.bones.length),this.positionNode=i1,this.toPositionNode=i1,this.previousBoneMatricesNode=null}}let nP=e=>eT(new nB(e));class nF extends F{static get type(){return"LoopNode"}getVarName(e){return String.fromCharCode(105+e)}getProperties(e){let t=e.getNodeProperties(this);if(void 0!==t.stackNode)return t;let r={};for(let e=0,t=this.params.length-1;e<t;e++){let t=this.params[e],i=!0!==t.isNode&&t.name||this.getVarName(e),s=!0!==t.isNode&&t.type||"int";r[i]=ix(i,s)}let i=e.addStack();t.returnsNode=this.params[this.params.length-1](r,e),t.stackNode=i;let s=this.params[0];return!0!==s.isNode&&"function"==typeof s.update&&(t.updateNode=eA(this.params[0].update)(r)),e.removeStack(),t}getNodeType(e){let{returnsNode:t}=this.getProperties(e);return t?t.getNodeType(e):"void"}setup(e){this.getProperties(e)}generate(e){let t=this.getProperties(e),r=this.params,i=t.stackNode;for(let i=0,s=r.length-1;i<s;i++){let s,n=r[i],a=!1,o=null,l=null,u=null,d=null,h=null,c=null;if(n.isNode?"bool"===n.getNodeType(e)?(a=!0,d="bool",l=n.build(e,d)):(d="int",u=this.getVarName(i),o="0",l=n.build(e,d),h="<"):(d=n.type||"int",u=n.name||this.getVarName(i),o=n.start,l=n.end,h=n.condition,c=n.update,"number"==typeof o?o=e.generateConst(d,o):o&&o.isNode&&(o=o.build(e,d)),"number"==typeof l?l=e.generateConst(d,l):l&&l.isNode&&(l=l.build(e,d)),void 0!==o&&void 0===l?(o+=" - 1",l="0",h=">="):void 0!==l&&void 0===o&&(o="0",h="<"),void 0===h&&(h=Number(o)>Number(l)?">=":"<")),a)s="while ( ".concat(l," )");else{let r,i={start:o,end:l},n=i.start,a=i.end,p=()=>h.includes("<")?"+=":"-=";if(null!=c)switch(typeof c){case"function":r=e.flowStagesNode(t.updateNode,"void").code.replace(/\t|;/g,"");break;case"number":r=u+" "+p()+" "+e.generateConst(d,c);break;case"string":r=u+" "+c;break;default:c.isNode?r=u+" "+p()+" "+c.build(e):(console.error("THREE.TSL: 'Loop( { update: ... } )' is not a function, string or number."),r="break /* invalid update */")}else r=u+" "+(c="int"===d||"uint"===d?h.includes("<")?"++":"--":p()+" 1.");let g=e.getVar(d,u)+" = "+n,m=u+" "+h+" "+a;s="for ( ".concat(g,"; ").concat(m,"; ").concat(r," )")}e.addFlowCode((0===i?"\n":"")+e.tab+s+" {\n\n").addFlowTab()}let s=i.build(e,"void"),n=t.returnsNode?t.returnsNode.build(e):"";e.removeFlowTab().addFlowCode("\n"+e.tab+s);for(let t=0,r=this.params.length-1;t<r;t++)e.addFlowCode((0===t?"":e.tab)+"}\n\n").removeFlowTab();return e.addFlowTab(),n}constructor(e=[]){super(),this.params=e}}let nU=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return eT(new nF(e_(t,"int"))).toStack()},nL=()=>ix("break").toStack(),nI=new WeakMap,nD=new u.IUQ,nO=eA(e=>{let{bufferMap:t,influence:r,stride:i,width:s,depth:n,offset:a}=e,o=eF(nb).mul(i).add(a),l=o.div(s);return iM(t,eD(o.sub(l.mul(s)),l)).depth(n).xyz.mul(r)});class nV extends F{static get type(){return"MorphNode"}setup(e){let{geometry:t}=e,r=void 0!==t.morphAttributes.position,i=t.hasAttribute("normal")&&void 0!==t.morphAttributes.normal,s=t.morphAttributes.position||t.morphAttributes.normal||t.morphAttributes.color,n=void 0!==s?s.length:0,{texture:a,stride:o,size:l}=function(e){let t=void 0!==e.morphAttributes.position,r=void 0!==e.morphAttributes.normal,i=void 0!==e.morphAttributes.color,s=e.morphAttributes.position||e.morphAttributes.normal||e.morphAttributes.color,n=void 0!==s?s.length:0,a=nI.get(e);if(void 0===a||a.count!==n){void 0!==a&&a.texture.dispose();let s=e.morphAttributes.position||[],o=e.morphAttributes.normal||[],l=e.morphAttributes.color||[],d=0;!0===t&&(d=1),!0===r&&(d=2),!0===i&&(d=3);let h=e.attributes.position.count*d,c=1;h>4096&&(c=Math.ceil(h/4096),h=4096);let p=new Float32Array(h*c*4*n),g=new u.rFo(p,h,c,n);g.type=u.RQf,g.needsUpdate=!0;let m=4*d;for(let e=0;e<n;e++){let n=s[e],a=o[e],u=l[e],d=h*c*4*e;for(let e=0;e<n.count;e++){let s=e*m;!0===t&&(nD.fromBufferAttribute(n,e),p[d+s+0]=nD.x,p[d+s+1]=nD.y,p[d+s+2]=nD.z,p[d+s+3]=0),!0===r&&(nD.fromBufferAttribute(a,e),p[d+s+4]=nD.x,p[d+s+5]=nD.y,p[d+s+6]=nD.z,p[d+s+7]=0),!0===i&&(nD.fromBufferAttribute(u,e),p[d+s+8]=nD.x,p[d+s+9]=nD.y,p[d+s+10]=nD.z,p[d+s+11]=4===u.itemSize?nD.w:1)}}a={count:n,texture:g,stride:d,size:new u.I9Y(h,c)},nI.set(e,a),e.addEventListener("dispose",function t(){g.dispose(),nI.delete(e),e.removeEventListener("dispose",t)})}return a}(t);!0===r&&i1.mulAssign(this.morphBaseInfluence),!0===i&&se.mulAssign(this.morphBaseInfluence);let d=eF(l.width);nU(n,e=>{let{i:t}=e,s=eP(0).toVar();this.mesh.count>1&&null!==this.mesh.morphTexture&&void 0!==this.mesh.morphTexture?s.assign(iM(this.mesh.morphTexture,eD(eF(t).add(1),eF(nT))).r):s.assign(s_("morphTargetInfluences","float").element(t).toVar()),ew(s.notEqual(0),()=>{!0===r&&i1.addAssign(nO({bufferMap:a,influence:s,stride:o,width:d,depth:t,offset:eF(0)})),!0===i&&se.addAssign(nO({bufferMap:a,influence:s,stride:o,width:d,depth:t,offset:eF(1)}))})})}update(){let e=this.morphBaseInfluence;this.mesh.geometry.morphTargetsRelative?e.value=1:e.value=1-this.mesh.morphTargetInfluences.reduce((e,t)=>e+t,0)}constructor(e){super("void"),this.mesh=e,this.morphBaseInfluence=tN(1),this.updateType=E.OBJECT}}let nG=eN(nV).setParameterLength(1);class nk extends F{static get type(){return"LightingNode"}constructor(){super("vec3"),this.isLightingNode=!0}}class nz extends nk{static get type(){return"AONode"}setup(e){e.context.ambientOcclusion.mulAssign(this.aoNode)}constructor(e=null){super(),this.aoNode=e}}class nH extends rQ{static get type(){return"LightingContextNode"}getContext(){let{backdropNode:e,backdropAlphaNode:t}=this,r=eG().toVar("directDiffuse"),i=eG().toVar("directSpecular"),s=eG().toVar("indirectDiffuse"),n=eG().toVar("indirectSpecular");return{radiance:eG().toVar("radiance"),irradiance:eG().toVar("irradiance"),iblIrradiance:eG().toVar("iblIrradiance"),ambientOcclusion:eP(1).toVar("ambientOcclusion"),reflectedLight:{directDiffuse:r,directSpecular:i,indirectDiffuse:s,indirectSpecular:n},backdrop:e,backdropAlpha:t}}setup(e){return this.value=this._value||(this._value=this.getContext()),this.value.lightingModel=this.lightingModel||e.context.lightingModel,super.setup(e)}constructor(e,t=null,r=null,i=null){super(e),this.lightingModel=t,this.backdropNode=r,this.backdropAlphaNode=i,this._value=null}}let nW=eN(nH);class nq extends nk{static get type(){return"IrradianceNode"}setup(e){e.context.irradiance.addAssign(this.node)}constructor(e){super(),this.node=e}}class nj extends F{static get type(){return"ScreenNode"}getNodeType(){return this.scope===nj.VIEWPORT?"vec4":"vec2"}getUpdateType(){let e=E.NONE;return(this.scope===nj.SIZE||this.scope===nj.VIEWPORT)&&(e=E.RENDER),this.updateType=e,e}update(e){let{renderer:t}=e,r=t.getRenderTarget();this.scope===nj.VIEWPORT?null!==r?s.copy(r.viewport):(t.getViewport(s),s.multiplyScalar(t.getPixelRatio())):null!==r?(i.width=r.width,i.height=r.height):t.getDrawingBufferSize(i)}setup(){let e=this.scope,t=null;return e===nj.SIZE?tN(i||(i=new u.I9Y)):e===nj.VIEWPORT?tN(s||(s=new u.IUQ)):eI(nY.div(nQ))}generate(e){if(this.scope===nj.COORDINATE){let t=e.getFragCoord();if(e.isFlipY()){let r=e.getNodeProperties(nQ).outputNode.build(e);t="".concat(e.getType("vec2"),"( ").concat(t,".x, ").concat(r,".y - ").concat(t,".y )")}return t}return super.generate(e)}constructor(e){super(),this.scope=e,this.isViewportNode=!0}}nj.COORDINATE="coordinate",nj.VIEWPORT="viewport",nj.SIZE="size",nj.UV="uv";let nX=eS(nj,nj.UV),nQ=eS(nj,nj.SIZE),nY=eS(nj,nj.COORDINATE),nK=eS(nj,nj.VIEWPORT),nZ=nK.zw,n$=new u.I9Y;class nJ extends iC{static get type(){return"ViewportTextureNode"}updateBefore(e){let t=e.renderer;t.getDrawingBufferSize(n$);let r=this.value;(r.image.width!==n$.width||r.image.height!==n$.height)&&(r.image.width=n$.width,r.image.height=n$.height,r.needsUpdate=!0);let i=r.generateMipmaps;r.generateMipmaps=this.generateMipmaps,t.copyFramebufferToTexture(r),r.generateMipmaps=i}clone(){let e=new this.constructor(this.uvNode,this.levelNode,this.value);return e.generateMipmaps=this.generateMipmaps,e}constructor(e=nX,t=null,r=null){null===r&&((r=new u.Pem).minFilter=u.$_I),super(r,e,t),this.generateMipmaps=!1,this.isOutputTextureNode=!0,this.updateBeforeType=E.FRAME}}let n0=eN(nJ,null,null,{generateMipmaps:!0}).setParameterLength(0,3),n1=null;class n2 extends nJ{static get type(){return"ViewportDepthTextureNode"}constructor(e=nX,t=null){null===n1&&(n1=new u.VCu),super(e,t,n1)}}let n3=eN(n2).setParameterLength(0,2);class n4 extends F{static get type(){return"ViewportDepthNode"}generate(e){let{scope:t}=this;return t===n4.DEPTH_BASE?e.getFragDepth():super.generate(e)}setup(e){let{camera:t}=e,{scope:r}=this,i=this.valueNode,s=null;return r===n4.DEPTH_BASE?null!==i&&(s=n7().assign(i)):r===n4.DEPTH?s=t.isPerspectiveCamera?n8(i6.z,iV,iG):n6(i6.z,iV,iG):r===n4.LINEAR_DEPTH&&(s=null!==i?t.isPerspectiveCamera?n6(n5(i,iV,iG),iV,iG):i:n6(i6.z,iV,iG)),s}constructor(e,t=null){super("float"),this.scope=e,this.valueNode=t,this.isViewportDepthNode=!0}}n4.DEPTH_BASE="depthBase",n4.DEPTH="depth",n4.LINEAR_DEPTH="linearDepth";let n6=(e,t,r)=>e.add(t).div(t.sub(r)),n8=(e,t,r)=>t.add(e).mul(r).div(r.sub(t).mul(e)),n5=(e,t,r)=>t.mul(r).div(r.sub(t).mul(e).sub(r)),n9=(e,t,r)=>{t=t.max(1e-6).toVar();let i=rt(e.negate().div(t)),s=rt(r.div(t));return i.div(s)},n7=eN(n4,n4.DEPTH_BASE),ae=eS(n4,n4.DEPTH),at=eN(n4,n4.LINEAR_DEPTH).setParameterLength(0,1);n3(),ae.assign=e=>n7(e);class ar extends F{static get type(){return"ClippingNode"}setup(e){super.setup(e);let{intersectionPlanes:t,unionPlanes:r}=e.clippingContext;return(this.hardwareClipping=e.material.hardwareClipping,this.scope===ar.ALPHA_TO_COVERAGE)?this.setupAlphaToCoverage(t,r):this.scope===ar.HARDWARE?this.setupHardwareClipping(r,e):this.setupDefault(t,r)}setupAlphaToCoverage(e,t){return eA(()=>{let r=eP().toVar("distanceToPlane"),i=eP().toVar("distanceToGradient"),s=eP(1).toVar("clipOpacity"),n=t.length;if(!1===this.hardwareClipping&&n>0){let e=iL(t);nU(n,t=>{let{i:n}=t,a=e.element(n);r.assign(i6.dot(a.xyz).negate().add(a.w)),i.assign(r.fwidth().div(2)),s.mulAssign(rH(i.negate(),i,r))})}let a=e.length;if(a>0){let t=iL(e),n=eP(1).toVar("intersectionClipOpacity");nU(a,e=>{let{i:s}=e,a=t.element(s);r.assign(i6.dot(a.xyz).negate().add(a.w)),i.assign(r.fwidth().div(2)),n.mulAssign(rH(i.negate(),i,r).oneMinus())}),s.mulAssign(n.oneMinus())}e0.a.mulAssign(s),e0.a.equal(0).discard()})()}setupDefault(e,t){return eA(()=>{let r=t.length;if(!1===this.hardwareClipping&&r>0){let e=iL(t);nU(r,t=>{let{i:r}=t,i=e.element(r);i6.dot(i.xyz).greaterThan(i.w).discard()})}let i=e.length;if(i>0){let t=iL(e),r=eL(!0).toVar("clipped");nU(i,e=>{let{i}=e,s=t.element(i);r.assign(i6.dot(s.xyz).greaterThan(s.w).and(r))}),r.discard()}})()}setupHardwareClipping(e,t){let r=e.length;return t.enableHardwareClipping(r),eA(()=>{let i=iL(e),s=iD(t.getClipDistance());nU(r,e=>{let{i:t}=e,r=i.element(t),n=i6.dot(r.xyz).sub(r.w).negate();s.element(t).assign(n)})})()}constructor(e=ar.DEFAULT){super(),this.scope=e}}ar.ALPHA_TO_COVERAGE="alphaToCoverage",ar.DEFAULT="default",ar.HARDWARE="hardware";let ai=()=>eT(new ar),as=()=>eT(new ar(ar.ALPHA_TO_COVERAGE)),an=()=>eT(new ar(ar.HARDWARE)),aa=eA(e=>{let[t]=e;return ro(tP(1e4,rl(tP(17,t.x).add(tP(.1,t.y)))).mul(tM(.1,rg(rl(tP(13,t.y).add(t.x))))))}),ao=eA(e=>{let[t]=e;return aa(eI(aa(t.xy),t.z))}),al=eA(e=>{let[t]=e,r=rE(rf(rb(t.xyz)),rf(rT(t.xyz))),i=eP(1).div(eP(.05).mul(r)).toVar("pixScale"),s=eI(t7(rs(rt(i))),t7(rn(rt(i)))),n=eI(ao(rs(s.x.mul(t.xyz))),ao(rs(s.y.mul(t.xyz)))),a=ro(rt(i)),o=tM(tP(a.oneMinus(),n.x),tP(a,n.y)),l=rA(a,a.oneMinus()),u=eG(o.mul(o).div(tP(2,l).mul(tB(1,l))),o.sub(tP(.5,l)).div(tB(1,l)),tB(1,tB(1,o).mul(tB(1,o)).div(tP(2,l).mul(tB(1,l)))));return rk(o.lessThan(l.oneMinus()).select(o.lessThan(l).select(u.x,u.y),u.z),1e-6,1)}).setLayout({name:"getAlphaHashThreshold",type:"float",inputs:[{name:"position",type:"vec3"}]});class au extends iv{static get type(){return"VertexColorNode"}getAttributeName(){let e=this.index;return"color"+(e>0?e:"")}generate(e){let t,r=this.getAttributeName(e);return!0===e.hasGeometryAttribute(r)?super.generate(e):e.generateConst(this.nodeType,new u.IUQ(1,1,1,1))}serialize(e){super.serialize(e),e.index=this.index}deserialize(e){super.deserialize(e),this.index=e.index}constructor(e){super(null,"vec4"),this.isVertexColorNode=!0,this.index=e}}let ad=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return eT(new au(e))};class ah extends u.imn{static get type(){return"NodeMaterial"}get type(){return this.constructor.type}set type(e){}customProgramCacheKey(){return this.type+f(this)}build(e){this.setup(e)}setupObserver(e){return new h(e)}setup(e){let t;e.context.setupNormal=()=>this.setupNormal(e),e.context.setupPositionView=()=>this.setupPositionView(e),e.context.setupModelViewProjection=()=>this.setupModelViewProjection(e);let r=e.renderer,i=r.getRenderTarget();e.addStack();let s=this.vertexNode||this.setupVertex(e);e.stack.outputNode=s,this.setupHardwareClipping(e),null!==this.geometryNode&&(e.stack.outputNode=e.stack.outputNode.bypass(this.geometryNode)),e.addFlow("vertex",e.removeStack()),e.addStack();let n=this.setupClipping(e);if((!0===this.depthWrite||!0===this.depthTest)&&(null!==i?!0===i.depthBuffer&&this.setupDepth(e):!0===r.depth&&this.setupDepth(e)),null===this.fragmentNode){this.setupDiffuseColor(e),this.setupVariants(e);let s=this.setupLighting(e);null!==n&&e.stack.add(n);let a=eW(s,e0.a).max(0);t=this.setupOutput(e,a),tl.assign(t);let o=null!==this.outputNode;if(o&&(t=this.outputNode),null!==i){let e=r.getMRT(),i=this.mrtNode;null!==e?(o&&tl.assign(t),t=e,null!==i&&(t=e.merge(i))):null!==i&&(t=i)}}else{let r=this.fragmentNode;!0!==r.isOutputStructNode&&(r=eW(r)),t=this.setupOutput(e,r)}e.stack.outputNode=t,e.addFlow("fragment",e.removeStack()),e.observer=this.setupObserver(e)}setupClipping(e){if(null===e.clippingContext)return null;let{unionPlanes:t,intersectionPlanes:r}=e.clippingContext,i=null;if(t.length>0||r.length>0){let t=e.renderer.samples;this.alphaToCoverage&&t>1?i=as():e.stack.add(ai())}return i}setupHardwareClipping(e){if(this.hardwareClipping=!1,null===e.clippingContext)return;let t=e.clippingContext.unionPlanes.length;t>0&&t<=8&&e.isAvailable("clipDistance")&&(e.stack.add(an()),this.hardwareClipping=!0)}setupDepth(e){let{renderer:t,camera:r}=e,i=this.depthNode;if(null===i){let e=t.getMRT();e&&e.has("depth")?i=e.get("depth"):!0===t.logarithmicDepthBuffer&&(i=r.isPerspectiveCamera?n9(i6.z,iV,iG):n6(i6.z,iV,iG))}null!==i&&ae.assign(i).toStack()}setupPositionView(){return iK.mul(i1).xyz}setupModelViewProjection(){return ik.mul(i6)}setupVertex(e){return e.addStack(),this.setupPosition(e),e.context.vertex=e.removeStack(),ny}setupPosition(e){let{object:t,geometry:r}=e;if((r.morphAttributes.position||r.morphAttributes.normal||r.morphAttributes.color)&&nG(t).toStack(),!0===t.isSkinnedMesh&&nP(t).toStack(),this.displacementMap){let e=sR("displacementMap","texture"),t=sR("displacementScale","float"),r=sR("displacementBias","float");i1.addAssign(se.normalize().mul(e.x.mul(t).add(r)))}return t.isBatchedMesh&&nA(t).toStack(),t.isInstancedMesh&&t.instanceMatrix&&!0===t.instanceMatrix.isInstancedBufferAttribute&&nS(t).toStack(),null!==this.positionNode&&i1.assign(this.positionNode.context({isPositionNodeInput:!0})),i1}setupDiffuseColor(e){let{object:t,geometry:r}=e,i=this.colorNode?eW(this.colorNode):sq;!0===this.vertexColors&&r.hasAttribute("color")&&(i=i.mul(ad())),t.instanceColor&&(i=eJ("vec3","vInstanceColor").mul(i)),t.isBatchedMesh&&t._colorsTexture&&(i=eJ("vec3","vBatchColor").mul(i)),e0.assign(i);let s=this.opacityNode?eP(this.opacityNode):sQ;if(e0.a.assign(e0.a.mul(s)),null!==this.alphaTestNode||this.alphaTest>0){let e=null!==this.alphaTestNode?eP(this.alphaTestNode):sW;e0.a.lessThanEqual(e).discard()}!0===this.alphaHash&&e0.a.lessThan(al(i1)).discard(),!1===this.transparent&&this.blending===u.NTi&&!1===this.alphaToCoverage&&e0.a.assign(1)}setupVariants(){}setupOutgoingLight(){return!0===this.lights?eG(0):e0.rgb}setupNormal(){return this.normalNode?eG(this.normalNode):s2}setupEnvironment(){let e=null;return this.envNode?e=this.envNode:this.envMap&&(e=this.envMap.isCubeTexture?sR("envMap","cubeTexture"):sR("envMap","texture")),e}setupLightMap(e){let t=null;return e.material.lightMap&&(t=new nq(ng)),t}setupLights(e){let t=[],r=this.setupEnvironment(e);r&&r.isLightingNode&&t.push(r);let i=this.setupLightMap(e);if(i&&i.isLightingNode&&t.push(i),null!==this.aoNode||e.material.aoMap){let e=null!==this.aoNode?this.aoNode:nm;t.push(new nz(e))}let s=this.lightsNode||e.lightsNode;return t.length>0&&(s=e.renderer.lighting.createNode([...s.getLights(),...t])),s}setupLightingModel(){}setupLighting(e){let{material:t}=e,{backdropNode:r,backdropAlphaNode:i,emissiveNode:s}=this,n=!0===this.lights||null!==this.lightsNode?this.setupLights(e):null,a=this.setupOutgoingLight(e);return n&&n.getScope().hasLights?a=nW(n,this.setupLightingModel(e)||null,r,i):null!==r&&(a=eG(null!==i?rG(a,r,i):r)),(s&&!0===s.isNode||t.emissive&&!0===t.emissive.isColor)&&(e1.assign(eG(s||sX)),a=a.add(e1)),a}setupFog(e,t){let r=e.fogNode;return r&&(tl.assign(t),t=eW(r)),t}setupOutput(e,t){return!0===this.fog&&(t=this.setupFog(e,t)),t}setDefaultValues(e){for(let t in e){let r=e[t];void 0===this[t]&&(this[t]=r,r&&r.clone&&(this[t]=r.clone()))}let t=Object.getOwnPropertyDescriptors(e.constructor.prototype);for(let e in t)void 0===Object.getOwnPropertyDescriptor(this.constructor.prototype,e)&&void 0!==t[e].get&&Object.defineProperty(this.constructor.prototype,e,t[e])}toJSON(e){let t=void 0===e||"string"==typeof e;t&&(e={textures:{},images:{},nodes:{}});let r=u.imn.prototype.toJSON.call(this,e),i=y(this);for(let{property:t,childNode:s}of(r.inputNodes={},i))r.inputNodes[t]=s.toJSON(e).uuid;function s(e){let t=[];for(let r in e){let i=e[r];delete i.metadata,t.push(i)}return t}if(t){let t=s(e.textures),i=s(e.images),n=s(e.nodes);t.length>0&&(r.textures=t),i.length>0&&(r.images=i),n.length>0&&(r.nodes=n)}return r}copy(e){return this.lightsNode=e.lightsNode,this.envNode=e.envNode,this.colorNode=e.colorNode,this.normalNode=e.normalNode,this.opacityNode=e.opacityNode,this.backdropNode=e.backdropNode,this.backdropAlphaNode=e.backdropAlphaNode,this.alphaTestNode=e.alphaTestNode,this.positionNode=e.positionNode,this.geometryNode=e.geometryNode,this.depthNode=e.depthNode,this.receivedShadowPositionNode=e.receivedShadowPositionNode,this.castShadowPositionNode=e.castShadowPositionNode,this.receivedShadowNode=e.receivedShadowNode,this.castShadowNode=e.castShadowNode,this.outputNode=e.outputNode,this.mrtNode=e.mrtNode,this.fragmentNode=e.fragmentNode,this.vertexNode=e.vertexNode,super.copy(e)}constructor(){super(),this.isNodeMaterial=!0,this.fog=!0,this.lights=!1,this.hardwareClipping=!1,this.lightsNode=null,this.envNode=null,this.aoNode=null,this.colorNode=null,this.normalNode=null,this.opacityNode=null,this.backdropNode=null,this.backdropAlphaNode=null,this.alphaTestNode=null,this.positionNode=null,this.geometryNode=null,this.depthNode=null,this.receivedShadowPositionNode=null,this.castShadowPositionNode=null,this.receivedShadowNode=null,this.castShadowNode=null,this.outputNode=null,this.mrtNode=null,this.fragmentNode=null,this.vertexNode=null,Object.defineProperty(this,"shadowPositionNode",{get:()=>this.receivedShadowPositionNode,set:e=>{console.warn('THREE.NodeMaterial: ".shadowPositionNode" was renamed to ".receivedShadowPositionNode".'),this.receivedShadowPositionNode=e}})}}let ac=new u.mrM;class ap extends ah{static get type(){return"LineBasicNodeMaterial"}constructor(e){super(),this.isLineBasicNodeMaterial=!0,this.setDefaultValues(ac),this.setValues(e)}}let ag=new u.Fvt;class am extends ah{static get type(){return"LineDashedNodeMaterial"}setupVariants(){let e=this.offsetNode?eP(this.offsetNode):nh,t=this.dashScaleNode?eP(this.dashScaleNode):nl,r=this.dashSizeNode?eP(this.dashSizeNode):nu,i=this.gapSizeNode?eP(this.gapSizeNode):nd;tu.assign(r),td.assign(i);let s=rJ(i_("lineDistance").mul(t));(e?s.add(e):s).mod(tu.add(td)).greaterThan(tu).discard()}constructor(e){super(),this.isLineDashedNodeMaterial=!0,this.setDefaultValues(ag),this.dashOffset=0,this.offsetNode=null,this.dashScaleNode=null,this.dashSizeNode=null,this.gapSizeNode=null,this.setValues(e)}}let af=null;class ay extends nJ{static get type(){return"ViewportSharedTextureNode"}updateReference(){return this}constructor(e=nX,t=null){null===af&&(af=new u.Pem),super(e,t,af)}}let ax=e=>eT(e).mul(.5).add(.5),ab=new u.qBx;class aT extends ah{static get type(){return"MeshNormalNodeMaterial"}setupDiffuseColor(){let e=this.opacityNode?eP(this.opacityNode):sQ;e0.assign(r6(eW(ax(ss),e),u.er$))}constructor(e){super(),this.isMeshNormalNodeMaterial=!0,this.setDefaultValues(ab),this.setValues(e)}}class av extends I{static get type(){return"EquirectUVNode"}setup(){let e=this.dirNode;return eI(e.z.atan(e.x).mul(1/(2*Math.PI)).add(.5),e.y.clamp(-1,1).asin().mul(1/Math.PI).add(.5))}constructor(e=i4){super("vec2"),this.dirNode=e}}let a_=eN(av).setParameterLength(0,1);class aN extends u.o6l{fromEquirectangularTexture(e,t){let r=t.minFilter,i=t.generateMipmaps;t.generateMipmaps=!0,this.texture.type=t.type,this.texture.colorSpace=t.colorSpace,this.texture.generateMipmaps=t.generateMipmaps,this.texture.minFilter=t.minFilter,this.texture.magFilter=t.magFilter;let s=new u.iNn(5,5,5),n=a_(i4),a=new ah;a.colorNode=iw(t,n,0),a.side=u.hsX,a.blending=u.XIg;let o=new u.eaF(s,a),l=new u.Z58;l.add(o),t.minFilter===u.$_I&&(t.minFilter=u.k6q);let d=new u.F1T(1,10,this),h=e.getMRT();return e.setMRT(null),d.update(e,l),e.setMRT(h),t.minFilter=r,t.currentGenerateMipmaps=i,o.geometry.dispose(),o.material.dispose(),this}constructor(e=1,t={}){super(e,t),this.isCubeRenderTarget=!0}}let aS=new WeakMap;class aR extends I{static get type(){return"CubeMapNode"}updateBefore(e){let{renderer:t,material:r}=e,i=this.envNode;if(i.isTextureNode||i.isMaterialReferenceNode){let e=i.isTextureNode?i.value:r[i.property];if(e&&e.isTexture){let r=e.mapping;if(r===u.wfO||r===u.uV5){if(aS.has(e)){let t=aS.get(e);aE(t,e.mapping),this._cubeTexture=t}else{var s;let r=e.image;if(null!=(s=r)&&s.height>0){let i=new aN(r.height);i.fromEquirectangularTexture(t,e),aE(i.texture,e.mapping),this._cubeTexture=i.texture,aS.set(e,i.texture),e.addEventListener("dispose",aA)}else this._cubeTexture=this._defaultTexture}this._cubeTextureNode.value=this._cubeTexture}else this._cubeTextureNode=this.envNode}}}setup(e){return this.updateBefore(e),this._cubeTextureNode}constructor(e){super("vec3"),this.envNode=e,this._cubeTexture=null,this._cubeTextureNode=sb(null);let t=new u.b4q;t.isRenderTargetTexture=!0,this._defaultTexture=t,this.updateBeforeType=E.RENDER}}function aA(e){let t=e.target;t.removeEventListener("dispose",aA);let r=aS.get(t);void 0!==r&&(aS.delete(t),r.dispose())}function aE(e,t){t===u.wfO?e.mapping=u.hy7:t===u.uV5&&(e.mapping=u.xFO)}let aC=eN(aR).setParameterLength(1);class aw extends nk{static get type(){return"BasicEnvironmentNode"}setup(e){e.context.environment=aC(this.envNode)}constructor(e=null){super(),this.envNode=e}}class aM extends nk{static get type(){return"BasicLightMapNode"}setup(e){let t=eP(1/Math.PI);e.context.irradianceLightMap=this.lightMapNode.mul(t)}constructor(e=null){super(),this.lightMapNode=e}}class aB{start(e){e.lightsNode.setupLights(e,e.lightsNode.getLightNodes(e)),this.indirect(e)}finish(){}direct(){}directRectArea(){}indirect(){}ambientOcclusion(){}}class aP extends aB{indirect(e){let{context:t}=e,r=t.ambientOcclusion,i=t.reflectedLight,s=t.irradianceLightMap;i.indirectDiffuse.assign(eW(0)),s?i.indirectDiffuse.addAssign(s):i.indirectDiffuse.addAssign(eW(1,1,1,0)),i.indirectDiffuse.mulAssign(r),i.indirectDiffuse.mulAssign(e0.rgb)}finish(e){let{material:t,context:r}=e,i=r.outgoingLight,s=e.context.environment;if(s)switch(t.combine){case u.caT:i.rgb.assign(rG(i.rgb,i.rgb.mul(s.rgb),s$.mul(sJ)));break;case u.KRh:i.rgb.assign(rG(i.rgb,s.rgb,s$.mul(sJ)));break;case u.XrR:i.rgb.addAssign(s.rgb.mul(s$.mul(sJ)));break;default:console.warn("THREE.BasicLightingModel: Unsupported .combine value:",t.combine)}}constructor(){super()}}let aF=new u.V9B;class aU extends ah{static get type(){return"MeshBasicNodeMaterial"}setupNormal(){return sr}setupEnvironment(e){let t=super.setupEnvironment(e);return t?new aw(t):null}setupLightMap(e){let t=null;return e.material.lightMap&&(t=new aM(ng)),t}setupOutgoingLight(){return e0.rgb}setupLightingModel(){return new aP}constructor(e){super(),this.isMeshBasicNodeMaterial=!0,this.lights=!0,this.setDefaultValues(aF),this.setValues(e)}}let aL=eA(e=>{let{f0:t,f90:r,dotVH:i}=e,s=i.mul(-5.55473).sub(6.98316).mul(i).exp2();return t.mul(s.oneMinus()).add(r.mul(s))}),aI=eA(e=>e.diffuseColor.mul(1/Math.PI)),aD=()=>eP(.25),aO=eA(e=>{let{dotNH:t}=e;return to.mul(eP(.5)).add(1).mul(eP(1/Math.PI)).mul(t.pow(to))}),aV=eA(e=>{let{lightDirection:t}=e,r=t.add(i8).normalize(),i=ss.dot(r).clamp(),s=aL({f0:tn,f90:1,dotVH:i8.dot(r).clamp()}),n=aD(),a=aO({dotNH:i});return s.mul(n).mul(a)});class aG extends aP{direct(e){let{lightDirection:t,lightColor:r,reflectedLight:i}=e,s=ss.dot(t).clamp().mul(r);i.directDiffuse.addAssign(s.mul(aI({diffuseColor:e0.rgb}))),!0===this.specular&&i.directSpecular.addAssign(s.mul(aV({lightDirection:t})).mul(s$))}indirect(e){let{ambientOcclusion:t,irradiance:r,reflectedLight:i}=e.context;i.indirectDiffuse.addAssign(r.mul(aI({diffuseColor:e0}))),i.indirectDiffuse.mulAssign(t)}constructor(e=!0){super(),this.specular=e}}let ak=new u.G_z;class az extends ah{static get type(){return"MeshLambertNodeMaterial"}setupEnvironment(e){let t=super.setupEnvironment(e);return t?new aw(t):null}setupLightingModel(){return new aG(!1)}constructor(e){super(),this.isMeshLambertNodeMaterial=!0,this.lights=!0,this.setDefaultValues(ak),this.setValues(e)}}let aH=new u.tXL;class aW extends ah{static get type(){return"MeshPhongNodeMaterial"}setupEnvironment(e){let t=super.setupEnvironment(e);return t?new aw(t):null}setupLightingModel(){return new aG}setupVariants(){let e=(this.shininessNode?eP(this.shininessNode):sj).max(1e-4);to.assign(e);let t=this.specularNode||sY;tn.assign(t)}copy(e){return this.shininessNode=e.shininessNode,this.specularNode=e.specularNode,super.copy(e)}constructor(e){super(),this.isMeshPhongNodeMaterial=!0,this.lights=!0,this.shininessNode=null,this.specularNode=null,this.setDefaultValues(aH),this.setValues(e)}}let aq=eA(e=>{if(!1===e.geometry.hasAttribute("normal"))return eP(0);let t=sr.dFdx().abs().max(sr.dFdy().abs());return t.x.max(t.y).max(t.z)}),aj=eA(e=>{let{roughness:t}=e,r=aq(),i=t.max(.0525);return(i=i.add(r)).min(1)}),aX=eA(e=>{let{alpha:t,dotNL:r,dotNV:i}=e,s=t.pow2(),n=r.mul(s.add(s.oneMinus().mul(i.pow2())).sqrt()),a=i.mul(s.add(s.oneMinus().mul(r.pow2())).sqrt());return tF(.5,n.add(a).max(t2))}).setLayout({name:"V_GGX_SmithCorrelated",type:"float",inputs:[{name:"alpha",type:"float"},{name:"dotNL",type:"float"},{name:"dotNV",type:"float"}]}),aQ=eA(e=>{let{alphaT:t,alphaB:r,dotTV:i,dotBV:s,dotTL:n,dotBL:a,dotNV:o,dotNL:l}=e,u=l.mul(eG(t.mul(i),r.mul(s),o).length()),d=o.mul(eG(t.mul(n),r.mul(a),l).length());return tF(.5,u.add(d)).saturate()}).setLayout({name:"V_GGX_SmithCorrelated_Anisotropic",type:"float",inputs:[{name:"alphaT",type:"float",qualifier:"in"},{name:"alphaB",type:"float",qualifier:"in"},{name:"dotTV",type:"float",qualifier:"in"},{name:"dotBV",type:"float",qualifier:"in"},{name:"dotTL",type:"float",qualifier:"in"},{name:"dotBL",type:"float",qualifier:"in"},{name:"dotNV",type:"float",qualifier:"in"},{name:"dotNL",type:"float",qualifier:"in"}]}),aY=eA(e=>{let{alpha:t,dotNH:r}=e,i=t.pow2(),s=r.pow2().mul(i.oneMinus()).oneMinus();return i.div(s.pow2()).mul(1/Math.PI)}).setLayout({name:"D_GGX",type:"float",inputs:[{name:"alpha",type:"float"},{name:"dotNH",type:"float"}]}),aK=eP(1/Math.PI),aZ=eA(e=>{let{alphaT:t,alphaB:r,dotNH:i,dotTH:s,dotBH:n}=e,a=t.mul(r),o=eG(r.mul(s),t.mul(n),a.mul(i)),l=o.dot(o),u=a.div(l);return aK.mul(a.mul(u.pow2()))}).setLayout({name:"D_GGX_Anisotropic",type:"float",inputs:[{name:"alphaT",type:"float",qualifier:"in"},{name:"alphaB",type:"float",qualifier:"in"},{name:"dotNH",type:"float",qualifier:"in"},{name:"dotTH",type:"float",qualifier:"in"},{name:"dotBH",type:"float",qualifier:"in"}]}),a$=eA(e=>{let t,r,{lightDirection:i,f0:s,f90:n,roughness:a,f:o,USE_IRIDESCENCE:l,USE_ANISOTROPY:u}=e,d=e.normalView||ss,h=a.pow2(),c=i.add(i8).normalize(),p=d.dot(i).clamp(),g=d.dot(i8).clamp(),m=d.dot(c).clamp(),f=aL({f0:s,f90:n,dotVH:i8.dot(c).clamp()});if(ey(l)&&(f=e9.mix(f,o)),ey(u)){let e=ti.dot(i),s=ti.dot(i8),n=ti.dot(c),a=ts.dot(i),o=ts.dot(i8),l=ts.dot(c);t=aQ({alphaT:tt,alphaB:h,dotTV:s,dotBV:o,dotTL:e,dotBL:a,dotNV:g,dotNL:p}),r=aZ({alphaT:tt,alphaB:h,dotNH:m,dotTH:n,dotBH:l})}else t=aX({alpha:h,dotNL:p,dotNV:g}),r=aY({alpha:h,dotNH:m});return f.mul(t).mul(r)}),aJ=eA(e=>{let{roughness:t,dotNV:r}=e,i=eW(-1,-.0275,-.572,.022),s=eW(1,.0425,1.04,-.04),n=t.mul(i).add(s),a=n.x.mul(n.x).min(r.mul(-9.28).exp2()).mul(n.x).add(n.y);return eI(-1.04,1.04).mul(a).add(n.zw)}).setLayout({name:"DFGApprox",type:"vec2",inputs:[{name:"roughness",type:"float"},{name:"dotNV",type:"vec3"}]}),a0=eA(e=>{let{dotNV:t,specularColor:r,specularF90:i,roughness:s}=e,n=aJ({dotNV:t,roughness:s});return r.mul(n.x).add(i.mul(n.y))}),a1=eA(e=>{let{f:t,f90:r,dotVH:i}=e,s=i.oneMinus().saturate(),n=s.mul(s),a=s.mul(n,n).clamp(0,.9999);return t.sub(eG(r).mul(a)).div(a.oneMinus())}).setLayout({name:"Schlick_to_F0",type:"vec3",inputs:[{name:"f",type:"vec3"},{name:"f90",type:"float"},{name:"dotVH",type:"float"}]}),a2=eA(e=>{let{roughness:t,dotNH:r}=e,i=t.pow2(),s=eP(1).div(i),n=r.pow2().oneMinus().max(.0078125);return eP(2).add(s).mul(n.pow(s.mul(.5))).div(2*Math.PI)}).setLayout({name:"D_Charlie",type:"float",inputs:[{name:"roughness",type:"float"},{name:"dotNH",type:"float"}]}),a3=eA(e=>{let{dotNV:t,dotNL:r}=e;return eP(1).div(eP(4).mul(r.add(t).sub(r.mul(t))))}).setLayout({name:"V_Neubelt",type:"float",inputs:[{name:"dotNV",type:"float"},{name:"dotNL",type:"float"}]}),a4=eA(e=>{let{lightDirection:t}=e,r=t.add(i8).normalize(),i=ss.dot(t).clamp(),s=ss.dot(i8).clamp(),n=a2({roughness:e5,dotNH:ss.dot(r).clamp()}),a=a3({dotNV:s,dotNL:i});return e8.mul(n).mul(a)}),a6=eA(e=>{let{N:t,V:r,roughness:i}=e,s=eI(i,t.dot(r).saturate().oneMinus().sqrt());return s.assign(s.mul(.984375).add(.0078125)),s}).setLayout({name:"LTC_Uv",type:"vec2",inputs:[{name:"N",type:"vec3"},{name:"V",type:"vec3"},{name:"roughness",type:"float"}]}),a8=eA(e=>{let{f:t}=e,r=t.length();return rE(r.mul(r).add(t.z).div(r.add(1)),0)}).setLayout({name:"LTC_ClippedSphereFormFactor",type:"float",inputs:[{name:"f",type:"vec3"}]}),a5=eA(e=>{let{v1:t,v2:r}=e,i=t.dot(r),s=i.abs().toVar(),n=s.mul(.0145206).add(.4965155).mul(s).add(.8543985).toVar(),a=s.add(4.1616724).mul(s).add(3.417594).toVar(),o=n.div(a),l=i.greaterThan(0).select(o,rE(i.mul(i).oneMinus(),1e-7).inverseSqrt().mul(.5).sub(o));return t.cross(r).mul(l)}).setLayout({name:"LTC_EdgeVectorFormFactor",type:"vec3",inputs:[{name:"v1",type:"vec3"},{name:"v2",type:"vec3"}]}),a9=eA(e=>{let{N:t,V:r,P:i,mInv:s,p0:n,p1:a,p2:o,p3:l}=e,u=a.sub(n).toVar(),d=l.sub(n).toVar(),h=u.cross(d),c=eG().toVar();return ew(h.dot(i.sub(n)).greaterThanEqual(0),()=>{let e=r.sub(t.mul(r.dot(t))).normalize(),u=t.cross(e).negate(),d=s.mul(eY(e,u,t).transpose()).toVar(),h=d.mul(n.sub(i)).normalize().toVar(),p=d.mul(a.sub(i)).normalize().toVar(),g=d.mul(o.sub(i)).normalize().toVar(),m=d.mul(l.sub(i)).normalize().toVar(),f=eG(0).toVar();f.addAssign(a5({v1:h,v2:p})),f.addAssign(a5({v1:p,v2:g})),f.addAssign(a5({v1:g,v2:m})),f.addAssign(a5({v1:m,v2:h})),c.assign(eG(a8({f:f})))}),c}).setLayout({name:"LTC_Evaluate",type:"vec3",inputs:[{name:"N",type:"vec3"},{name:"V",type:"vec3"},{name:"P",type:"vec3"},{name:"mInv",type:"mat3"},{name:"p0",type:"vec3"},{name:"p1",type:"vec3"},{name:"p2",type:"vec3"},{name:"p3",type:"vec3"}]}),a7=1/6,oe=e=>tP(a7,tP(e,tP(e,e.negate().add(3)).sub(3)).add(1)),ot=e=>tP(a7,tP(e,tP(e,tP(3,e).sub(6))).add(4)),or=e=>tP(a7,tP(e,tP(e,tP(-3,e).add(3)).add(3)).add(1)),oi=e=>tP(a7,rU(e,3)),os=e=>oe(e).add(ot(e)),on=e=>or(e).add(oi(e)),oa=e=>tM(-1,ot(e).div(oe(e).add(ot(e)))),oo=e=>tM(1,oi(e).div(or(e).add(oi(e)))),ol=(e,t,r)=>{let i=tP(e.uvNode,t.zw).add(.5),s=rs(i),n=ro(i),a=os(n.x),o=on(n.x),l=oa(n.x),u=oo(n.x),d=oa(n.y),h=oo(n.y),c=eI(s.x.add(l),s.y.add(d)).sub(.5).mul(t.xy),p=eI(s.x.add(u),s.y.add(d)).sub(.5).mul(t.xy),g=eI(s.x.add(l),s.y.add(h)).sub(.5).mul(t.xy),m=eI(s.x.add(u),s.y.add(h)).sub(.5).mul(t.xy),f=os(n.y).mul(tM(a.mul(e.sample(c).level(r)),o.mul(e.sample(p).level(r)))),y=on(n.y).mul(tM(a.mul(e.sample(g).level(r)),o.mul(e.sample(m).level(r))));return f.add(y)},ou=eA(e=>{let[t,r=eP(3)]=e,i=eI(t.size(eF(r))),s=eI(t.size(eF(r.add(1)))),n=tF(1,i),a=tF(1,s),o=ol(t,eW(n,i),rs(r)),l=ol(t,eW(a,s),rn(r));return ro(r).mix(o,l)}),od=eA(e=>{let[t,r,i,s,n]=e,a=eG(rz(r.negate(),ra(t),tF(1,s))),o=eG(rf(n[0].xyz),rf(n[1].xyz),rf(n[2].xyz));return ra(a).mul(i.mul(o))}).setLayout({name:"getVolumeTransmissionRay",type:"vec3",inputs:[{name:"n",type:"vec3"},{name:"v",type:"vec3"},{name:"thickness",type:"float"},{name:"ior",type:"float"},{name:"modelMatrix",type:"mat4"}]}),oh=eA(e=>{let[t,r]=e;return t.mul(rk(r.mul(2).sub(2),0,1))}).setLayout({name:"applyIorToRoughness",type:"float",inputs:[{name:"roughness",type:"float"},{name:"ior",type:"float"}]}),oc=n0(),op=n0(),og=eA((e,t)=>{let[r,i,s]=e,{material:n}=t;return ou((n.side===u.hsX?oc:op).sample(r),rt(nQ.x).mul(oh(i,s)))}),om=eA(e=>{let[t,r,i]=e;return ew(i.notEqual(0),()=>t9(re(r).negate().div(i).negate().mul(t))),eG(1)}).setLayout({name:"volumeAttenuation",type:"vec3",inputs:[{name:"transmissionDistance",type:"float"},{name:"attenuationColor",type:"vec3"},{name:"attenuationDistance",type:"float"}]}),of=eA(e=>{let t,r,[i,s,n,a,o,l,u,d,h,c,p,g,m,f,y]=e;if(y){t=eW().toVar(),r=eG().toVar();let e=p.sub(1).mul(y.mul(.025)),o=eG(p.sub(e),p,p.add(e));nU({start:0,end:3},e=>{let{i:l}=e,p=o.element(l),y=od(i,s,g,p,d),x=u.add(y),b=c.mul(h.mul(eW(x,1))),T=eI(b.xy.div(b.w)).toVar();T.addAssign(1),T.divAssign(2),T.assign(eI(T.x,T.y.oneMinus()));let v=og(T,n,p);t.element(l).assign(v.element(l)),t.a.addAssign(v.a),r.element(l).assign(a.element(l).mul(om(rf(y),m,f).element(l)))}),t.a.divAssign(3)}else{let e=od(i,s,g,p,d),o=u.add(e),l=c.mul(h.mul(eW(o,1))),y=eI(l.xy.div(l.w)).toVar();y.addAssign(1),y.divAssign(2),y.assign(eI(y.x,y.y.oneMinus())),t=og(y,n,p),r=a.mul(om(rf(e),m,f))}let x=r.rgb.mul(t.rgb),b=eG(a0({dotNV:i.dot(s).clamp(),specularColor:o,specularF90:l,roughness:n})),T=r.r.add(r.g,r.b).div(3);return eW(b.oneMinus().mul(x),t.a.oneMinus().mul(T).oneMinus())}),oy=eY(3.2404542,-.969266,.0556434,-1.5371385,1.8760108,-.2040259,-.4985314,.041556,1.0572252),ox=e=>{let t=e.sqrt();return eG(1).add(t).div(eG(1).sub(t))},ob=(e,t)=>e.sub(t).div(e.add(t)).pow2(),oT=(e,t)=>{let r=e.mul(2*Math.PI*1e-9),i=eG(54856e-17,44201e-17,52481e-17),s=eG(1681e3,1795300,2208400),n=eG(43278e5,93046e5,66121e5),a=eP(9747e-17*Math.sqrt(2*Math.PI*45282e5)).mul(r.mul(2239900).add(t.x).cos()).mul(r.pow2().mul(-45282e5).exp()),o=i.mul(n.mul(2*Math.PI).sqrt()).mul(s.mul(r).add(t).cos()).mul(r.pow2().negate().mul(n).exp());return o=eG(o.x.add(a),o.y,o.z).div(10685e-11),oy.mul(o)},ov=eA(e=>{let{outsideIOR:t,eta2:r,cosTheta1:i,thinFilmThickness:s,baseF0:n}=e,a=rG(t,r,rH(0,.03,s)),o=t.div(a).pow2().mul(i.pow2().oneMinus()).oneMinus();ew(o.lessThan(0),()=>eG(1));let l=o.sqrt(),u=aL({f0:ob(a,t),f90:1,dotVH:i}),d=u.oneMinus(),h=a.lessThan(t).select(Math.PI,0),c=eP(Math.PI).sub(h),p=ox(n.clamp(0,.9999)),g=aL({f0:ob(p,a.toVec3()),f90:1,dotVH:l}),m=eG(p.x.lessThan(a).select(Math.PI,0),p.y.lessThan(a).select(Math.PI,0),p.z.lessThan(a).select(Math.PI,0)),f=a.mul(s,l,2),y=eG(c).add(m),x=u.mul(g).clamp(1e-5,.9999),b=x.sqrt(),T=d.pow2().mul(g).div(eG(1).sub(x)),v=u.add(T).toVar(),_=T.sub(d).toVar();return nU({start:1,end:2,condition:"<=",name:"m"},e=>{let{m:t}=e;_.mulAssign(b);let r=oT(eP(t).mul(f),eP(t).mul(y)).mul(2);v.addAssign(_.mul(r))}),v.max(eG(0))}).setLayout({name:"evalIridescence",type:"vec3",inputs:[{name:"outsideIOR",type:"float"},{name:"eta2",type:"float"},{name:"cosTheta1",type:"float"},{name:"thinFilmThickness",type:"float"},{name:"baseF0",type:"vec3"}]}),o_=eA(e=>{let{normal:t,viewDir:r,roughness:i}=e,s=t.dot(r).saturate(),n=i.pow2(),a=rX(i.lessThan(.25),eP(-339.2).mul(n).add(eP(161.4).mul(i)).sub(25.9),eP(-8.48).mul(n).add(eP(14.3).mul(i)).sub(9.95)),o=rX(i.lessThan(.25),eP(44).mul(n).sub(eP(23.7).mul(i)).add(3.26),eP(1.97).mul(n).sub(eP(3.27).mul(i)).add(.72));return rX(i.lessThan(.25),0,eP(.1).mul(i).sub(.025)).add(a.mul(s).add(o).exp()).mul(1/Math.PI).saturate()}),oN=eG(.04),oS=eP(1);class oR extends aB{start(e){if(!0===this.clearcoat&&(this.clearcoatRadiance=eG().toVar("clearcoatRadiance"),this.clearcoatSpecularDirect=eG().toVar("clearcoatSpecularDirect"),this.clearcoatSpecularIndirect=eG().toVar("clearcoatSpecularIndirect")),!0===this.sheen&&(this.sheenSpecularDirect=eG().toVar("sheenSpecularDirect"),this.sheenSpecularIndirect=eG().toVar("sheenSpecularIndirect")),!0===this.iridescence){let e=ss.dot(i8).clamp();this.iridescenceFresnel=ov({outsideIOR:eP(1),eta2:e7,cosTheta1:e,thinFilmThickness:te,baseF0:tn}),this.iridescenceF0=a1({f:this.iridescenceFresnel,f90:1,dotVH:e})}if(!0===this.transmission){let t=iH.sub(i3).normalize(),r=e.context;r.backdrop=of(sn,t,e2,e0,tn,ta,i3,iQ,iz,ik,th,tp,tm,tg,this.dispersion?tf:null),r.backdropAlpha=tc,e0.a.mulAssign(rG(1,r.backdrop.a,tc))}super.start(e)}computeMultiscattering(e,t,r){let i=aJ({roughness:e2,dotNV:ss.dot(i8).clamp()}),s=(this.iridescenceF0?e9.mix(tn,this.iridescenceF0):tn).mul(i.x).add(r.mul(i.y)),n=i.x.add(i.y).oneMinus(),a=tn.add(tn.oneMinus().mul(.047619)),o=s.mul(a).div(n.mul(a).oneMinus());e.addAssign(s),t.addAssign(o.mul(n))}direct(e){let{lightDirection:t,lightColor:r,reflectedLight:i}=e,s=ss.dot(t).clamp().mul(r);if(!0===this.sheen&&this.sheenSpecularDirect.addAssign(s.mul(a4({lightDirection:t}))),!0===this.clearcoat){let e=sa.dot(t).clamp().mul(r);this.clearcoatSpecularDirect.addAssign(e.mul(a$({lightDirection:t,f0:oN,f90:oS,roughness:e6,normalView:sa})))}i.directDiffuse.addAssign(s.mul(aI({diffuseColor:e0.rgb}))),i.directSpecular.addAssign(s.mul(a$({lightDirection:t,f0:tn,f90:1,roughness:e2,iridescence:this.iridescence,f:this.iridescenceFresnel,USE_IRIDESCENCE:this.iridescence,USE_ANISOTROPY:this.anisotropy})))}directRectArea(e){let{lightColor:t,lightPosition:r,halfWidth:i,halfHeight:s,reflectedLight:n,ltc_1:a,ltc_2:o}=e,l=r.add(i).sub(s),u=r.sub(i).sub(s),d=r.sub(i).add(s),h=r.add(i).add(s),c=i6.toVar(),p=a6({N:ss,V:i8,roughness:e2}),g=a.sample(p).toVar(),m=o.sample(p).toVar(),f=eY(eG(g.x,0,g.y),eG(0,1,0),eG(g.z,0,g.w)).toVar(),y=tn.mul(m.x).add(tn.oneMinus().mul(m.y)).toVar();n.directSpecular.addAssign(t.mul(y).mul(a9({N:ss,V:i8,P:c,mInv:f,p0:l,p1:u,p2:d,p3:h}))),n.directDiffuse.addAssign(t.mul(e0).mul(a9({N:ss,V:i8,P:c,mInv:eY(1,0,0,0,1,0,0,0,1),p0:l,p1:u,p2:d,p3:h})))}indirect(e){this.indirectDiffuse(e),this.indirectSpecular(e),this.ambientOcclusion(e)}indirectDiffuse(e){let{irradiance:t,reflectedLight:r}=e.context;r.indirectDiffuse.addAssign(t.mul(aI({diffuseColor:e0})))}indirectSpecular(e){let{radiance:t,iblIrradiance:r,reflectedLight:i}=e.context;if(!0===this.sheen&&this.sheenSpecularIndirect.addAssign(r.mul(e8,o_({normal:ss,viewDir:i8,roughness:e5}))),!0===this.clearcoat){let e=a0({dotNV:sa.dot(i8).clamp(),specularColor:oN,specularF90:oS,roughness:e6});this.clearcoatSpecularIndirect.addAssign(this.clearcoatRadiance.mul(e))}let s=eG().toVar("singleScattering"),n=eG().toVar("multiScattering"),a=r.mul(1/Math.PI);this.computeMultiscattering(s,n,ta);let o=s.add(n),l=e0.mul(o.r.max(o.g).max(o.b).oneMinus());i.indirectSpecular.addAssign(t.mul(s)),i.indirectSpecular.addAssign(n.mul(a)),i.indirectDiffuse.addAssign(l.mul(a))}ambientOcclusion(e){let{ambientOcclusion:t,reflectedLight:r}=e.context,i=ss.dot(i8).clamp().add(t),s=e2.mul(-16).oneMinus().negate().exp2(),n=t.sub(i.pow(s).oneMinus()).clamp();!0===this.clearcoat&&this.clearcoatSpecularIndirect.mulAssign(t),!0===this.sheen&&this.sheenSpecularIndirect.mulAssign(t),r.indirectDiffuse.mulAssign(t),r.indirectSpecular.mulAssign(n)}finish(e){let{context:t}=e,{outgoingLight:r}=t;if(!0===this.clearcoat){let e=aL({dotVH:sa.dot(i8).clamp(),f0:oN,f90:oS}),t=r.mul(e4.mul(e).oneMinus()).add(this.clearcoatSpecularDirect.add(this.clearcoatSpecularIndirect).mul(e4));r.assign(t)}if(!0===this.sheen){let e=e8.r.max(e8.g).max(e8.b).mul(.157).oneMinus(),t=r.mul(e).add(this.sheenSpecularDirect,this.sheenSpecularIndirect);r.assign(t)}}constructor(e=!1,t=!1,r=!1,i=!1,s=!1,n=!1){super(),this.clearcoat=e,this.sheen=t,this.iridescence=r,this.anisotropy=i,this.transmission=s,this.dispersion=n,this.clearcoatRadiance=null,this.clearcoatSpecularDirect=null,this.clearcoatSpecularIndirect=null,this.sheenSpecularDirect=null,this.sheenSpecularIndirect=null,this.iridescenceFresnel=null,this.iridescenceF0=null}}let oA=eP(1),oE=eP(-2),oC=eP(.8),ow=eP(-1),oM=eP(.4),oB=eP(2),oP=eP(.305),oF=eP(3),oU=eP(.21),oL=eP(4),oI=eP(4),oD=eP(16),oO=eA(e=>{let[t]=e,r=eG(rg(t)).toVar(),i=eP(-1).toVar();return ew(r.x.greaterThan(r.z),()=>{ew(r.x.greaterThan(r.y),()=>{i.assign(rX(t.x.greaterThan(0),0,3))}).Else(()=>{i.assign(rX(t.y.greaterThan(0),1,4))})}).Else(()=>{ew(r.z.greaterThan(r.y),()=>{i.assign(rX(t.z.greaterThan(0),2,5))}).Else(()=>{i.assign(rX(t.y.greaterThan(0),1,4))})}),i}).setLayout({name:"getFace",type:"float",inputs:[{name:"direction",type:"vec3"}]}),oV=eA(e=>{let[t,r]=e,i=eI().toVar();return ew(r.equal(0),()=>{i.assign(eI(t.z,t.y).div(rg(t.x)))}).ElseIf(r.equal(1),()=>{i.assign(eI(t.x.negate(),t.z.negate()).div(rg(t.y)))}).ElseIf(r.equal(2),()=>{i.assign(eI(t.x.negate(),t.y).div(rg(t.z)))}).ElseIf(r.equal(3),()=>{i.assign(eI(t.z.negate(),t.y).div(rg(t.x)))}).ElseIf(r.equal(4),()=>{i.assign(eI(t.x.negate(),t.z).div(rg(t.y)))}).Else(()=>{i.assign(eI(t.x,t.y).div(rg(t.z)))}),tP(.5,i.add(1))}).setLayout({name:"getUV",type:"vec2",inputs:[{name:"direction",type:"vec3"},{name:"face",type:"float"}]}),oG=eA(e=>{let[t]=e,r=eP(0).toVar();return ew(t.greaterThanEqual(oC),()=>{r.assign(oA.sub(t).mul(ow.sub(oE)).div(oA.sub(oC)).add(oE))}).ElseIf(t.greaterThanEqual(oM),()=>{r.assign(oC.sub(t).mul(oB.sub(ow)).div(oC.sub(oM)).add(ow))}).ElseIf(t.greaterThanEqual(oP),()=>{r.assign(oM.sub(t).mul(oF.sub(oB)).div(oM.sub(oP)).add(oB))}).ElseIf(t.greaterThanEqual(oU),()=>{r.assign(oP.sub(t).mul(oL.sub(oF)).div(oP.sub(oU)).add(oF))}).Else(()=>{r.assign(eP(-2).mul(rt(tP(1.16,t))))}),r}).setLayout({name:"roughnessToMip",type:"float",inputs:[{name:"roughness",type:"float"}]}),ok=eA(e=>{let[t,r]=e,i=t.toVar();i.assign(tP(2,i).sub(1));let s=eG(i,1).toVar();return ew(r.equal(0),()=>{s.assign(s.zyx)}).ElseIf(r.equal(1),()=>{s.assign(s.xzy),s.xz.mulAssign(-1)}).ElseIf(r.equal(2),()=>{s.x.mulAssign(-1)}).ElseIf(r.equal(3),()=>{s.assign(s.zyx),s.xz.mulAssign(-1)}).ElseIf(r.equal(4),()=>{s.assign(s.xzy),s.xy.mulAssign(-1)}).ElseIf(r.equal(5),()=>{s.z.mulAssign(-1)}),s}).setLayout({name:"getDirection",type:"vec3",inputs:[{name:"uv",type:"vec2"},{name:"face",type:"float"}]}),oz=eA(e=>{let[t,r,i,s,n,a]=e,o=eP(i),l=eG(r),u=rk(oG(o),oE,a),d=ro(u),h=rs(u),c=eG(oH(t,l,h,s,n,a)).toVar();return ew(d.notEqual(0),()=>{let e=eG(oH(t,l,h.add(1),s,n,a)).toVar();c.assign(rG(c,e,d))}),c}),oH=eA(e=>{let[t,r,i,s,n,a]=e,o=eP(i).toVar(),l=eG(r),u=eP(oO(l)).toVar(),d=eP(rE(oI.sub(o),0)).toVar();o.assign(rE(o,oI));let h=eP(t7(o)).toVar(),c=eI(oV(l,u).mul(h.sub(2)).add(1)).toVar();return ew(u.greaterThan(2),()=>{c.y.addAssign(h),u.subAssign(3)}),c.x.addAssign(u.mul(h)),c.x.addAssign(d.mul(tP(3,oD))),c.y.addAssign(tP(4,t7(a).sub(h))),c.x.mulAssign(s),c.y.mulAssign(n),t.sample(c).grad(eI(),eI())}),oW=eA(e=>{let{envMap:t,mipInt:r,outputDirection:i,theta:s,axis:n,CUBEUV_TEXEL_WIDTH:a,CUBEUV_TEXEL_HEIGHT:o,CUBEUV_MAX_MIP:l}=e,u=ru(s);return oH(t,i.mul(u).add(n.cross(i).mul(rl(s))).add(n.mul(n.dot(i).mul(u.oneMinus()))),r,a,o,l)}),oq=eA(e=>{let{n:t,latitudinal:r,poleAxis:i,outputDirection:s,weights:n,samples:a,dTheta:o,mipInt:l,envMap:u,CUBEUV_TEXEL_WIDTH:d,CUBEUV_TEXEL_HEIGHT:h,CUBEUV_MAX_MIP:c}=e,p=eG(rX(r,i,rF(i,s))).toVar();ew(p.equal(eG(0)),()=>{p.assign(eG(s.z,0,s.x.negate()))}),p.assign(ra(p));let g=eG().toVar();return g.addAssign(n.element(0).mul(oW({theta:0,axis:p,outputDirection:s,mipInt:l,envMap:u,CUBEUV_TEXEL_WIDTH:d,CUBEUV_TEXEL_HEIGHT:h,CUBEUV_MAX_MIP:c}))),nU({start:eF(1),end:t},e=>{let{i:t}=e;ew(t.greaterThanEqual(a),()=>{nL()});let r=eP(o.mul(eP(t))).toVar();g.addAssign(n.element(t).mul(oW({theta:r.mul(-1),axis:p,outputDirection:s,mipInt:l,envMap:u,CUBEUV_TEXEL_WIDTH:d,CUBEUV_TEXEL_HEIGHT:h,CUBEUV_MAX_MIP:c}))),g.addAssign(n.element(t).mul(oW({theta:r,axis:p,outputDirection:s,mipInt:l,envMap:u,CUBEUV_TEXEL_WIDTH:d,CUBEUV_TEXEL_HEIGHT:h,CUBEUV_MAX_MIP:c})))}),eW(g,1)}),oj=[.125,.215,.35,.446,.526,.582],oX=new u.qUd(-1,1,1,-1,0,1),oQ=new u.ubm(90,1),oY=new u.Q1f,oK=null,oZ=0,o$=0,oJ=(1+Math.sqrt(5))/2,o0=1/oJ,o1=[new u.Pq0(-oJ,o0,0),new u.Pq0(oJ,o0,0),new u.Pq0(-o0,0,oJ),new u.Pq0(o0,0,oJ),new u.Pq0(0,oJ,-o0),new u.Pq0(0,oJ,o0),new u.Pq0(-1,1,-1),new u.Pq0(1,1,-1),new u.Pq0(-1,1,1),new u.Pq0(1,1,1)],o2=new u.Pq0,o3=new WeakMap,o4=[3,1,5,0,4,2],o6=ok(iN(),i_("faceIndex")).normalize(),o8=eG(o6.x,o6.y,o6.z);class o5{get _hasInitialized(){return this._renderer.hasInitialized()}fromScene(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:.1,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:100,s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},{size:n=256,position:a=o2,renderTarget:o=null}=s;if(this._setSize(n),!1===this._hasInitialized){console.warn("THREE.PMREMGenerator: .fromScene() called before the backend is initialized. Try using .fromSceneAsync() instead.");let n=o||this._allocateTargets();return s.renderTarget=n,this.fromSceneAsync(e,t,r,i,s),n}oK=this._renderer.getRenderTarget(),oZ=this._renderer.getActiveCubeFace(),o$=this._renderer.getActiveMipmapLevel();let l=o||this._allocateTargets();return l.depthBuffer=!0,this._sceneToCubeUV(e,r,i,l,a),t>0&&this._blur(l,0,0,t),this._applyPMREM(l),this._cleanup(l),l}async fromSceneAsync(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:.1,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:100,s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{};return!1===this._hasInitialized&&await this._renderer.init(),this.fromScene(e,t,r,i,s)}fromEquirectangular(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(!1===this._hasInitialized){console.warn("THREE.PMREMGenerator: .fromEquirectangular() called before the backend is initialized. Try using .fromEquirectangularAsync() instead."),this._setSizeFromTexture(e);let r=t||this._allocateTargets();return this.fromEquirectangularAsync(e,r),r}return this._fromTexture(e,t)}async fromEquirectangularAsync(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return!1===this._hasInitialized&&await this._renderer.init(),this._fromTexture(e,t)}fromCubemap(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(!1===this._hasInitialized){console.warn("THREE.PMREMGenerator: .fromCubemap() called before the backend is initialized. Try using .fromCubemapAsync() instead."),this._setSizeFromTexture(e);let r=t||this._allocateTargets();return this.fromCubemapAsync(e,t),r}return this._fromTexture(e,t)}async fromCubemapAsync(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return!1===this._hasInitialized&&await this._renderer.init(),this._fromTexture(e,t)}async compileCubemapShader(){null===this._cubemapMaterial&&(this._cubemapMaterial=lt(),await this._compileMaterial(this._cubemapMaterial))}async compileEquirectangularShader(){null===this._equirectMaterial&&(this._equirectMaterial=lr(),await this._compileMaterial(this._equirectMaterial))}dispose(){this._dispose(),null!==this._cubemapMaterial&&this._cubemapMaterial.dispose(),null!==this._equirectMaterial&&this._equirectMaterial.dispose(),null!==this._backgroundBox&&(this._backgroundBox.geometry.dispose(),this._backgroundBox.material.dispose())}_setSizeFromTexture(e){e.mapping===u.hy7||e.mapping===u.xFO?this._setSize(0===e.image.length?16:e.image[0].width||e.image[0].image.width):this._setSize(e.image.width/4)}_setSize(e){this._lodMax=Math.floor(Math.log2(e)),this._cubeSize=Math.pow(2,this._lodMax)}_dispose(){null!==this._blurMaterial&&this._blurMaterial.dispose(),null!==this._pingPongRenderTarget&&this._pingPongRenderTarget.dispose();for(let e=0;e<this._lodPlanes.length;e++)this._lodPlanes[e].dispose()}_cleanup(e){this._renderer.setRenderTarget(oK,oZ,o$),e.scissorTest=!1,o7(e,0,0,e.width,e.height)}_fromTexture(e,t){this._setSizeFromTexture(e),oK=this._renderer.getRenderTarget(),oZ=this._renderer.getActiveCubeFace(),o$=this._renderer.getActiveMipmapLevel();let r=t||this._allocateTargets();return this._textureToCubeUV(e,r),this._applyPMREM(r),this._cleanup(r),r}_allocateTargets(){let e=3*Math.max(this._cubeSize,112),t=4*this._cubeSize,r={magFilter:u.k6q,minFilter:u.k6q,generateMipmaps:!1,type:u.ix0,format:u.GWd,colorSpace:u.Zr2},i=o9(e,t,r);if(null===this._pingPongRenderTarget||this._pingPongRenderTarget.width!==e||this._pingPongRenderTarget.height!==t){null!==this._pingPongRenderTarget&&this._dispose(),this._pingPongRenderTarget=o9(e,t,r);let{_lodMax:i}=this;({sizeLods:this._sizeLods,lodPlanes:this._lodPlanes,sigmas:this._sigmas,lodMeshes:this._lodMeshes}=function(e){let t=[],r=[],i=[],s=[],n=e,a=e-4+1+oj.length;for(let o=0;o<a;o++){let a=Math.pow(2,n);r.push(a);let l=1/a;o>e-4?l=oj[o-e+4-1]:0===o&&(l=0),i.push(l);let d=1/(a-2),h=-d,c=1+d,p=[h,h,c,h,c,c,h,h,c,c,h,c],g=new Float32Array(108),m=new Float32Array(72),f=new Float32Array(36);for(let e=0;e<6;e++){let t=e%3*2/3-1,r=e>2?0:-1,i=[t,r,0,t+2/3,r,0,t+2/3,r+1,0,t,r,0,t+2/3,r+1,0,t,r+1,0],s=o4[e];g.set(i,18*s),m.set(p,12*s);let n=[s,s,s,s,s,s];f.set(n,6*s)}let y=new u.LoY;y.setAttribute("position",new u.THS(g,3)),y.setAttribute("uv",new u.THS(m,2)),y.setAttribute("faceIndex",new u.THS(f,1)),t.push(y),s.push(new u.eaF(y,null)),n>4&&n--}return{lodPlanes:t,sizeLods:r,sigmas:i,lodMeshes:s}}(i)),this._blurMaterial=function(e,t,r){let i=iL(Array(20).fill(0)),s=tN(new u.Pq0(0,1,0)),n=tN(0),a=eP(20),o=tN(0),l=tN(1),d=iw(null),h=tN(0),c=eP(1/t),p={n:a,latitudinal:o,weights:i,poleAxis:s,outputDirection:o8,dTheta:n,samples:l,envMap:d,mipInt:h,CUBEUV_TEXEL_WIDTH:c,CUBEUV_TEXEL_HEIGHT:eP(1/r),CUBEUV_MAX_MIP:eP(e)},g=le("blur");return g.fragmentNode=oq({...p,latitudinal:o.equal(1)}),o3.set(g,p),g}(i,e,t)}return i}async _compileMaterial(e){let t=new u.eaF(this._lodPlanes[0],e);await this._renderer.compile(t,oX)}_sceneToCubeUV(e,t,r,i,s){oQ.near=t,oQ.far=r;let n=[1,1,1,1,-1,1],a=[1,-1,1,-1,1,-1],o=this._renderer,l=o.autoClear;o.getClearColor(oY),o.autoClear=!1;let d=this._backgroundBox;if(null===d){let e=new u.V9B({name:"PMREM.Background",side:u.hsX,depthWrite:!1,depthTest:!1});d=new u.eaF(new u.iNn,e)}let h=!1,c=e.background;c?c.isColor&&(d.material.color.copy(c),e.background=null,h=!0):(d.material.color.copy(oY),h=!0),o.setRenderTarget(i),o.clear(),h&&o.render(d,oQ);for(let t=0;t<6;t++){let r=t%3;0===r?(oQ.up.set(0,n[t],0),oQ.position.set(s.x,s.y,s.z),oQ.lookAt(s.x+a[t],s.y,s.z)):1===r?(oQ.up.set(0,0,n[t]),oQ.position.set(s.x,s.y,s.z),oQ.lookAt(s.x,s.y+a[t],s.z)):(oQ.up.set(0,n[t],0),oQ.position.set(s.x,s.y,s.z),oQ.lookAt(s.x,s.y,s.z+a[t]));let l=this._cubeSize;o7(i,r*l,t>2?l:0,l,l),o.render(e,oQ)}o.autoClear=l,e.background=c}_textureToCubeUV(e,t){let r=this._renderer,i=e.mapping===u.hy7||e.mapping===u.xFO;i?null===this._cubemapMaterial&&(this._cubemapMaterial=lt(e)):null===this._equirectMaterial&&(this._equirectMaterial=lr(e));let s=i?this._cubemapMaterial:this._equirectMaterial;s.fragmentNode.value=e;let n=this._lodMeshes[0];n.material=s;let a=this._cubeSize;o7(t,0,0,3*a,2*a),r.setRenderTarget(t),r.render(n,oX)}_applyPMREM(e){let t=this._renderer,r=t.autoClear;t.autoClear=!1;let i=this._lodPlanes.length;for(let t=1;t<i;t++){let r=Math.sqrt(this._sigmas[t]*this._sigmas[t]-this._sigmas[t-1]*this._sigmas[t-1]),s=o1[(i-t-1)%o1.length];this._blur(e,t-1,t,r,s)}t.autoClear=r}_blur(e,t,r,i,s){let n=this._pingPongRenderTarget;this._halfBlur(e,n,t,r,i,"latitudinal",s),this._halfBlur(n,e,r,r,i,"longitudinal",s)}_halfBlur(e,t,r,i,s,n,a){let o=this._renderer,l=this._blurMaterial;"latitudinal"!==n&&"longitudinal"!==n&&console.error("blur direction must be either latitudinal or longitudinal!");let u=this._lodMeshes[i];u.material=l;let d=o3.get(l),h=this._sizeLods[r]-1,c=isFinite(s)?Math.PI/(2*h):2*Math.PI/39,p=s/c,g=isFinite(s)?1+Math.floor(3*p):20;g>20&&console.warn("sigmaRadians, ".concat(s,", is too large and will clip, as it requested ").concat(g," samples when the maximum is set to ").concat(20));let m=[],f=0;for(let e=0;e<20;++e){let t=e/p,r=Math.exp(-t*t/2);m.push(r),0===e?f+=r:e<g&&(f+=2*r)}for(let e=0;e<m.length;e++)m[e]=m[e]/f;e.texture.frame=(e.texture.frame||0)+1,d.envMap.value=e.texture,d.samples.value=g,d.weights.array=m,d.latitudinal.value=+("latitudinal"===n),a&&(d.poleAxis.value=a);let{_lodMax:y}=this;d.dTheta.value=c,d.mipInt.value=y-r;let x=this._sizeLods[i],b=4*(this._cubeSize-x);o7(t,3*x*(i>y-4?i-y+4:0),b,3*x,2*x),o.setRenderTarget(t),o.render(u,oX)}constructor(e){this._renderer=e,this._pingPongRenderTarget=null,this._lodMax=0,this._cubeSize=0,this._lodPlanes=[],this._sizeLods=[],this._sigmas=[],this._lodMeshes=[],this._blurMaterial=null,this._cubemapMaterial=null,this._equirectMaterial=null,this._backgroundBox=null}}function o9(e,t,r){let i=new u.O0B(e,t,r);return i.texture.mapping=u.Om,i.texture.name="PMREM.cubeUv",i.texture.isPMREMTexture=!0,i.scissorTest=!0,i}function o7(e,t,r,i,s){e.viewport.set(t,r,i,s),e.scissor.set(t,r,i,s)}function le(e){let t=new ah;return t.depthTest=!1,t.depthWrite=!1,t.blending=u.XIg,t.name="PMREM_".concat(e),t}function lt(e){let t=le("cubemap");return t.fragmentNode=sb(e,o8),t}function lr(e){let t=le("equirect");return t.fragmentNode=iw(e,a_(o8),0),t}let li=new WeakMap;class ls extends I{static get type(){return"PMREMNode"}set value(e){this._value=e,this._pmrem=null}get value(){return this._value}updateFromTexture(e){let t=function(e){let t=Math.log2(e)-2;return{texelWidth:1/(3*Math.max(Math.pow(2,t),112)),texelHeight:1/e,maxMip:t}}(e.image.height);this._texture.value=e,this._width.value=t.texelWidth,this._height.value=t.texelHeight,this._maxMip.value=t.maxMip}updateBefore(e){let t=this._pmrem,r=t?t.pmremVersion:-1,i=this._value;r!==i.pmremVersion&&null!==(t=!0===i.isPMREMTexture?i:function(e,t,r){var i,s;let n,a=(i=t,void 0===(n=li.get(i))&&(n=new WeakMap,li.set(i,n)),n),o=a.get(e);if((void 0!==o?o.pmremVersion:-1)!==e.pmremVersion){let t=e.image;if(e.isCubeTexture)if(!function(e){if(null==e)return!1;let t=0;for(let r=0;r<6;r++)void 0!==e[r]&&t++;return 6===t}(t))return null;else o=r.fromCubemap(e,o);else{if(!(null!=(s=t)&&s.height>0))return null;o=r.fromEquirectangular(e,o)}o.pmremVersion=e.pmremVersion,a.set(e,o)}return o.texture}(i,e.renderer,this._generator))&&(this._pmrem=t,this.updateFromTexture(t))}setup(e){null===this._generator&&(this._generator=new o5(e.renderer)),this.updateBefore(e);let t=this.uvNode;null===t&&e.context.getUV&&(t=e.context.getUV(this)),t=sp.mul(eG(t.x,t.y.negate(),t.z));let r=this.levelNode;return null===r&&e.context.getTextureLevel&&(r=e.context.getTextureLevel(this)),oz(this._texture,t,r,this._width,this._height,this._maxMip)}dispose(){super.dispose(),null!==this._generator&&this._generator.dispose()}constructor(e,t=null,r=null){super("vec3"),this._value=e,this._pmrem=null,this.uvNode=t,this.levelNode=r,this._generator=null;let i=new u.gPd;i.isRenderTargetTexture=!0,this._texture=iw(i),this._width=tN(0),this._height=tN(0),this._maxMip=tN(0),this.updateBeforeType=E.RENDER}}let ln=eN(ls).setParameterLength(1,3),la=new WeakMap;class lo extends nk{static get type(){return"EnvironmentNode"}setup(e){let{material:t}=e,r=this.envNode;if(r.isTextureNode||r.isMaterialReferenceNode){let e=r.isTextureNode?r.value:t[r.property],i=la.get(e);void 0===i&&(i=ln(e),la.set(e,i)),r=i}let i=!0===t.useAnisotropy||t.anisotropy>0?sU:ss,s=r.context(ll(e2,i)).mul(sc),n=r.context(lu(sn)).mul(Math.PI).mul(sc),a=ih(s),o=ih(n);e.context.radiance.addAssign(a),e.context.iblIrradiance.addAssign(o);let l=e.context.lightingModel.clearcoatRadiance;if(l){let e=ih(r.context(ll(e6,sa)).mul(sc));l.addAssign(e)}}constructor(e=null){super(),this.envNode=e}}let ll=(e,t)=>{let r=null;return{getUV:()=>(null===r&&(r=i8.negate().reflect(t),r=(r=e.mul(e).mix(r,t).normalize()).transformDirection(iz)),r),getTextureLevel:()=>e}},lu=e=>({getUV:()=>e,getTextureLevel:()=>eP(1)}),ld=new u._4j;class lh extends ah{static get type(){return"MeshStandardNodeMaterial"}setupEnvironment(e){let t=super.setupEnvironment(e);return null===t&&e.environmentNode&&(t=e.environmentNode),t?new lo(t):null}setupLightingModel(){return new oR}setupSpecular(){let e=rG(eG(.04),e0.rgb,e3);tn.assign(e),ta.assign(1)}setupVariants(){let e=this.metalnessNode?eP(this.metalnessNode):s1;e3.assign(e);let t=this.roughnessNode?eP(this.roughnessNode):s0;t=aj({roughness:t}),e2.assign(t),this.setupSpecular(),e0.assign(eW(e0.rgb.mul(e.oneMinus()),e0.a))}copy(e){return this.emissiveNode=e.emissiveNode,this.metalnessNode=e.metalnessNode,this.roughnessNode=e.roughnessNode,super.copy(e)}constructor(e){super(),this.isMeshStandardNodeMaterial=!0,this.lights=!0,this.emissiveNode=null,this.metalnessNode=null,this.roughnessNode=null,this.setDefaultValues(ld),this.setValues(e)}}let lc=new u.uSd;class lp extends lh{static get type(){return"MeshPhysicalNodeMaterial"}get useClearcoat(){return this.clearcoat>0||null!==this.clearcoatNode}get useIridescence(){return this.iridescence>0||null!==this.iridescenceNode}get useSheen(){return this.sheen>0||null!==this.sheenNode}get useAnisotropy(){return this.anisotropy>0||null!==this.anisotropyNode}get useTransmission(){return this.transmission>0||null!==this.transmissionNode}get useDispersion(){return this.dispersion>0||null!==this.dispersionNode}setupSpecular(){let e=this.iorNode?eP(this.iorNode):nn;th.assign(e),tn.assign(rG(rA(rL(th.sub(1).div(th.add(1))).mul(sZ),eG(1)).mul(sK),e0.rgb,e3)),ta.assign(rG(sK,1,e3))}setupLightingModel(){return new oR(this.useClearcoat,this.useSheen,this.useIridescence,this.useAnisotropy,this.useTransmission,this.useDispersion)}setupVariants(e){if(super.setupVariants(e),this.useClearcoat){let e=this.clearcoatNode?eP(this.clearcoatNode):s3,t=this.clearcoatRoughnessNode?eP(this.clearcoatRoughnessNode):s4;e4.assign(e),e6.assign(aj({roughness:t}))}if(this.useSheen){let e=this.sheenNode?eG(this.sheenNode):s5,t=this.sheenRoughnessNode?eP(this.sheenRoughnessNode):s9;e8.assign(e),e5.assign(t)}if(this.useIridescence){let e=this.iridescenceNode?eP(this.iridescenceNode):ne,t=this.iridescenceIORNode?eP(this.iridescenceIORNode):nt,r=this.iridescenceThicknessNode?eP(this.iridescenceThicknessNode):nr;e9.assign(e),e7.assign(t),te.assign(r)}if(this.useAnisotropy){let e=(this.anisotropyNode?eI(this.anisotropyNode):s7).toVar();tr.assign(e.length()),ew(tr.equal(0),()=>{e.assign(eI(1,0))}).Else(()=>{e.divAssign(eI(tr)),tr.assign(tr.saturate())}),tt.assign(tr.pow2().mix(e2.pow2(),1)),ti.assign(sF[0].mul(e.x).add(sF[1].mul(e.y))),ts.assign(sF[1].mul(e.x).sub(sF[0].mul(e.y)))}if(this.useTransmission){let e=this.transmissionNode?eP(this.transmissionNode):ni,t=this.thicknessNode?eP(this.thicknessNode):ns,r=this.attenuationDistanceNode?eP(this.attenuationDistanceNode):na,i=this.attenuationColorNode?eG(this.attenuationColorNode):no;if(tc.assign(e),tp.assign(t),tg.assign(r),tm.assign(i),this.useDispersion){let e=this.dispersionNode?eP(this.dispersionNode):np;tf.assign(e)}}}setupClearcoatNormal(){return this.clearcoatNormalNode?eG(this.clearcoatNormalNode):s6}setup(e){e.context.setupClearcoatNormal=()=>this.setupClearcoatNormal(e),super.setup(e)}copy(e){return this.clearcoatNode=e.clearcoatNode,this.clearcoatRoughnessNode=e.clearcoatRoughnessNode,this.clearcoatNormalNode=e.clearcoatNormalNode,this.sheenNode=e.sheenNode,this.sheenRoughnessNode=e.sheenRoughnessNode,this.iridescenceNode=e.iridescenceNode,this.iridescenceIORNode=e.iridescenceIORNode,this.iridescenceThicknessNode=e.iridescenceThicknessNode,this.specularIntensityNode=e.specularIntensityNode,this.specularColorNode=e.specularColorNode,this.transmissionNode=e.transmissionNode,this.thicknessNode=e.thicknessNode,this.attenuationDistanceNode=e.attenuationDistanceNode,this.attenuationColorNode=e.attenuationColorNode,this.dispersionNode=e.dispersionNode,this.anisotropyNode=e.anisotropyNode,super.copy(e)}constructor(e){super(),this.isMeshPhysicalNodeMaterial=!0,this.clearcoatNode=null,this.clearcoatRoughnessNode=null,this.clearcoatNormalNode=null,this.sheenNode=null,this.sheenRoughnessNode=null,this.iridescenceNode=null,this.iridescenceIORNode=null,this.iridescenceThicknessNode=null,this.specularIntensityNode=null,this.specularColorNode=null,this.iorNode=null,this.transmissionNode=null,this.thicknessNode=null,this.attenuationDistanceNode=null,this.attenuationColorNode=null,this.dispersionNode=null,this.anisotropyNode=null,this.setDefaultValues(lc),this.setValues(e)}}let lg=eA(e=>{let{normal:t,lightDirection:r,builder:i}=e,s=eI(t.dot(r).mul(.5).add(.5),0);if(i.material.gradientMap)return eG(sR("gradientMap","texture").context({getUV:()=>s}).r);{let e=s.fwidth().mul(.5);return rG(eG(.7),eG(1),rH(eP(.7).sub(e.x),eP(.7).add(e.x),s.x))}});class lm extends aB{direct(e,t){let{lightDirection:r,lightColor:i,reflectedLight:s}=e,n=lg({normal:i7,lightDirection:r,builder:t}).mul(i);s.directDiffuse.addAssign(n.mul(aI({diffuseColor:e0.rgb})))}indirect(e){let{ambientOcclusion:t,irradiance:r,reflectedLight:i}=e.context;i.indirectDiffuse.addAssign(r.mul(aI({diffuseColor:e0}))),i.indirectDiffuse.mulAssign(t)}}let lf=new u.Df;class ly extends ah{static get type(){return"MeshToonNodeMaterial"}setupLightingModel(){return new lm}constructor(e){super(),this.isMeshToonNodeMaterial=!0,this.lights=!0,this.setDefaultValues(lf),this.setValues(e)}}class lx extends I{static get type(){return"MatcapUVNode"}setup(){let e=eG(i8.z,0,i8.x.negate()).normalize(),t=i8.cross(e);return eI(e.dot(ss),t.dot(ss)).mul(.495).add(.5)}constructor(){super("vec2")}}let lb=eS(lx),lT=new u.FNr;class lv extends ah{static get type(){return"MeshMatcapNodeMaterial"}setupVariants(e){let t;t=e.material.matcap?sR("matcap","texture").context({getUV:()=>lb}):eG(rG(.2,.8,lb.y)),e0.rgb.mulAssign(t.rgb)}constructor(e){super(),this.isMeshMatcapNodeMaterial=!0,this.setDefaultValues(lT),this.setValues(e)}}class l_ extends I{static get type(){return"RotateNode"}getNodeType(e){return this.positionNode.getNodeType(e)}setup(e){let{rotationNode:t,positionNode:r}=this;if("vec2"===this.getNodeType(e)){let e=t.cos(),i=t.sin();return eQ(e,i,i.negate(),e).mul(r)}{let e=eK(eW(1,0,0,0),eW(0,ru(t.x),rl(t.x).negate(),0),eW(0,rl(t.x),ru(t.x),0),eW(0,0,0,1)),i=eK(eW(ru(t.y),0,rl(t.y),0),eW(0,1,0,0),eW(rl(t.y).negate(),0,ru(t.y),0),eW(0,0,0,1)),s=eK(eW(ru(t.z),rl(t.z).negate(),0,0),eW(rl(t.z),ru(t.z),0,0),eW(0,0,1,0),eW(0,0,0,1));return e.mul(i).mul(s).mul(eW(r,1)).xyz}}constructor(e,t){super(),this.positionNode=e,this.rotationNode=t}}let lN=eN(l_).setParameterLength(2),lS=new u.RoJ;class lR extends ah{static get type(){return"SpriteNodeMaterial"}setupPositionView(e){let{object:t,camera:r}=e,i=this.sizeAttenuation,{positionNode:s,rotationNode:n,scaleNode:a}=this,o=iK.mul(eG(s||0)),l=eI(iQ[0].xyz.length(),iQ[1].xyz.length());if(null!==a&&(l=l.mul(eI(a))),!1===i)if(r.isPerspectiveCamera)l=l.mul(o.z.negate());else{let e=eP(2).div(ik.element(1).element(1));l=l.mul(e.mul(2))}let u=i0.xy;if(t.center&&!0===t.center.isVector2){let e=r9("center","vec2",t);u=u.sub(e.sub(.5))}let d=lN(u=u.mul(l),eP(n||s8));return eW(o.xy.add(d),o.zw)}copy(e){return this.positionNode=e.positionNode,this.rotationNode=e.rotationNode,this.scaleNode=e.scaleNode,super.copy(e)}get sizeAttenuation(){return this._useSizeAttenuation}set sizeAttenuation(e){this._useSizeAttenuation!==e&&(this._useSizeAttenuation=e,this.needsUpdate=!0)}constructor(e){super(),this.isSpriteNodeMaterial=!0,this._useSizeAttenuation=!0,this.positionNode=null,this.rotationNode=null,this.scaleNode=null,this.transparent=!0,this.setDefaultValues(lS),this.setValues(e)}}let lA=new u.BH$;class lE extends lR{static get type(){return"PointsNodeMaterial"}setupPositionView(){let{positionNode:e}=this;return iK.mul(eG(e||i1)).xyz}setupVertex(e){let t=super.setupVertex(e);if(!0!==e.material.isNodeMaterial)return t;let{rotationNode:r,scaleNode:i,sizeNode:s}=this,n=i0.xy.toVar(),a=nK.z.div(nK.w);if(r&&r.isNode){let e=eP(r);n.assign(lN(n,e))}let o=null!==s?eI(s):nc;return!0===this.sizeAttenuation&&(o=o.mul(o.div(i6.z.negate()))),i&&i.isNode&&(o=o.mul(eI(i))),n.mulAssign(o.mul(2)),n.assign(n.div(nK.z)),n.y.assign(n.y.mul(a)),n.assign(n.mul(t.w)),t.addAssign(eW(n,0,0)),t}get alphaToCoverage(){return this._useAlphaToCoverage}set alphaToCoverage(e){this._useAlphaToCoverage!==e&&(this._useAlphaToCoverage=e,this.needsUpdate=!0)}constructor(e){super(),this.sizeNode=null,this.isPointsNodeMaterial=!0,this.setDefaultValues(lA),this.setValues(e)}}class lC extends aB{direct(e){let{lightNode:t}=e;this.shadowNode.mulAssign(t.shadowNode)}finish(e){let{context:t}=e;e0.a.mulAssign(this.shadowNode.oneMinus()),t.outgoingLight.rgb.assign(e0.rgb)}constructor(){super(),this.shadowNode=eP(1).toVar("shadowMask")}}let lw=new u.q2;class lM extends ah{static get type(){return"ShadowNodeMaterial"}setupLightingModel(){return new lC}constructor(e){super(),this.isShadowNodeMaterial=!0,this.lights=!0,this.transparent=!0,this.setDefaultValues(lw),this.setValues(e)}}e$("vec3"),e$("vec3"),e$("vec3");class lB{start(){let e=(t,r)=>{this._requestId=this._context.requestAnimationFrame(e),!0===this.info.autoReset&&this.info.reset(),this.nodes.nodeFrame.update(),this.info.frame=this.nodes.nodeFrame.frameId,null!==this._animationLoop&&this._animationLoop(t,r)};e()}stop(){this._context.cancelAnimationFrame(this._requestId),this._requestId=null}getAnimationLoop(){return this._animationLoop}setAnimationLoop(e){this._animationLoop=e}getContext(){return this._context}setContext(e){this._context=e}dispose(){this.stop()}constructor(e,t){this.nodes=e,this.info=t,this._context="undefined"!=typeof self?self:null,this._animationLoop=null,this._requestId=null}}class lP{get(e){let t=this.weakMap;for(let r=0;r<e.length-1;r++)if(void 0===(t=t.get(e[r])))return;return t.get(e[e.length-1])}set(e,t){let r=this.weakMap;for(let t=0;t<e.length-1;t++){let i=e[t];!1===r.has(i)&&r.set(i,new WeakMap),r=r.get(i)}return r.set(e[e.length-1],t),this}delete(e){let t=this.weakMap;for(let r=0;r<e.length-1;r++)if(void 0===(t=t.get(e[r])))return!1;return t.delete(e[e.length-1])}constructor(){this.weakMap=new WeakMap}}let lF=0;class lU{updateClipping(e){this.clippingContext=e}get clippingNeedsUpdate(){return null!==this.clippingContext&&this.clippingContext.cacheKey!==this.clippingContextCacheKey&&(this.clippingContextCacheKey=this.clippingContext.cacheKey,!0)}get hardwareClippingPlanes(){return!0===this.material.hardwareClipping?this.clippingContext.unionClippingCount:0}getNodeBuilderState(){return this._nodeBuilderState||(this._nodeBuilderState=this._nodes.getForRender(this))}getMonitor(){return this._monitor||(this._monitor=this.getNodeBuilderState().observer)}getBindings(){return this._bindings||(this._bindings=this.getNodeBuilderState().createBindings())}getBindingGroup(e){for(let t of this.getBindings())if(t.name===e)return t}getIndex(){return this._geometries.getIndex(this)}getIndirect(){return this._geometries.getIndirect(this)}getChainArray(){return[this.object,this.material,this.context,this.lightsNode]}setGeometry(e){this.geometry=e,this.attributes=null}getAttributes(){if(null!==this.attributes)return this.attributes;let e=this.getNodeBuilderState().nodeAttributes,t=this.geometry,r=[],i=new Set;for(let s of e){let e=s.node&&s.node.attribute?s.node.attribute:t.getAttribute(s.name);if(void 0===e)continue;r.push(e);let n=e.isInterleavedBufferAttribute?e.data:e;i.add(n)}return this.attributes=r,this.vertexBuffers=Array.from(i.values()),r}getVertexBuffers(){return null===this.vertexBuffers&&this.getAttributes(),this.vertexBuffers}getDrawParameters(){let{object:e,material:t,geometry:r,group:i,drawRange:s}=this,n=this.drawParams||(this.drawParams={vertexCount:0,firstVertex:0,instanceCount:0,firstInstance:0}),a=this.getIndex(),o=null!==a,l=1;if(!0===r.isInstancedBufferGeometry?l=r.instanceCount:void 0!==e.count&&(l=Math.max(0,e.count)),0===l)return null;if(n.instanceCount=l,!0===e.isBatchedMesh)return n;let u=1;!0!==t.wireframe||e.isPoints||e.isLineSegments||e.isLine||e.isLineLoop||(u=2);let d=s.start*u,h=(s.start+s.count)*u;null!==i&&(d=Math.max(d,i.start*u),h=Math.min(h,(i.start+i.count)*u));let c=r.attributes.position,p=1/0;o?p=a.count:null!=c&&(p=c.count),d=Math.max(d,0);let g=(h=Math.min(h,p))-d;return g<0||g===1/0?null:(n.vertexCount=g,n.firstVertex=d,n)}getGeometryCacheKey(){let{geometry:e}=this,t="";for(let r of Object.keys(e.attributes).sort()){let i=e.attributes[r];t+=r+",",i.data&&(t+=i.data.stride+","),i.offset&&(t+=i.offset+","),i.itemSize&&(t+=i.itemSize+","),i.normalized&&(t+="n,")}for(let r of Object.keys(e.morphAttributes).sort()){let i=e.morphAttributes[r];t+="morph-"+r+",";for(let e=0,r=i.length;e<r;e++)t+=i[e].id+","}return e.index&&(t+="index,"),t}getMaterialCacheKey(){let{object:e,material:t}=this,r=t.customProgramCacheKey();for(let e of function(e){let t=Object.keys(e),r=Object.getPrototypeOf(e);for(;r;){let e=Object.getOwnPropertyDescriptors(r);for(let r in e)if(void 0!==e[r]){let i=e[r];i&&"function"==typeof i.get&&t.push(r)}r=Object.getPrototypeOf(r)}return t}(t)){let i;if(/^(is[A-Z]|_)|^(visible|version|uuid|name|opacity|userData)$/.test(e))continue;let s=t[e];if(null!==s){let e=typeof s;"number"===e?i=0!==s?"1":"0":"object"===e?(i="{",s.isTexture&&(i+=s.mapping),i+="}"):i=String(s)}else i=String(s);r+=i+","}return r+=this.clippingContextCacheKey+",",e.geometry&&(r+=this.getGeometryCacheKey()),e.skeleton&&(r+=e.skeleton.bones.length+","),e.isBatchedMesh&&(r+=e._matricesTexture.uuid+",",null!==e._colorsTexture&&(r+=e._colorsTexture.uuid+",")),e.count>1&&(r+=e.uuid+","),p(r+=e.receiveShadow+",")}get needsGeometryUpdate(){return this.geometry.id!==this.object.geometry.id}get needsUpdate(){return this.initialNodesCacheKey!==this.getDynamicCacheKey()||this.clippingNeedsUpdate}getDynamicCacheKey(){let e=0;return!0!==this.material.isShadowPassMaterial&&(e=this._nodes.getCacheKey(this.scene,this.lightsNode)),this.camera.isArrayCamera&&(e=m(e,this.camera.cameras.length)),this.object.receiveShadow&&(e=m(e,1)),e}getCacheKey(){return this.getMaterialCacheKey()+this.getDynamicCacheKey()}dispose(){this.material.removeEventListener("dispose",this.onMaterialDispose),this.onDispose()}constructor(e,t,r,i,s,n,a,o,l,u){this.id=lF++,this._nodes=e,this._geometries=t,this.renderer=r,this.object=i,this.material=s,this.scene=n,this.camera=a,this.lightsNode=o,this.context=l,this.geometry=i.geometry,this.version=s.version,this.drawRange=null,this.attributes=null,this.pipeline=null,this.group=null,this.vertexBuffers=null,this.drawParams=null,this.bundle=null,this.clippingContext=u,this.clippingContextCacheKey=null!==u?u.cacheKey:"",this.initialNodesCacheKey=this.getDynamicCacheKey(),this.initialCacheKey=this.getCacheKey(),this._nodeBuilderState=null,this._bindings=null,this._monitor=null,this.onDispose=null,this.isRenderObject=!0,this.onMaterialDispose=()=>{this.dispose()},this.material.addEventListener("dispose",this.onMaterialDispose)}}let lL=[];class lI{get(e,t,r,i,s,n,a,o){let l=this.getChainMap(o);lL[0]=e,lL[1]=t,lL[2]=n,lL[3]=s;let u=l.get(lL);return void 0===u?(u=this.createRenderObject(this.nodes,this.geometries,this.renderer,e,t,r,i,s,n,a,o),l.set(lL,u)):(u.updateClipping(a),u.needsGeometryUpdate&&u.setGeometry(e.geometry),(u.version!==t.version||u.needsUpdate)&&(u.initialCacheKey!==u.getCacheKey()?(u.dispose(),u=this.get(e,t,r,i,s,n,a,o)):u.version=t.version)),lL.length=0,u}getChainMap(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"default";return this.chainMaps[e]||(this.chainMaps[e]=new lP)}dispose(){this.chainMaps={}}createRenderObject(e,t,r,i,s,n,a,o,l,u,d){let h=this.getChainMap(d),c=new lU(e,t,r,i,s,n,a,o,l,u);return c.onDispose=()=>{this.pipelines.delete(c),this.bindings.delete(c),this.nodes.delete(c),h.delete(c.getChainArray())},c}constructor(e,t,r,i,s,n){this.renderer=e,this.nodes=t,this.geometries=r,this.pipelines=i,this.bindings=s,this.info=n,this.chainMaps={}}}class lD{get(e){let t=this.data.get(e);return void 0===t&&(t={},this.data.set(e,t)),t}delete(e){let t=null;return this.data.has(e)&&(t=this.data.get(e),this.data.delete(e)),t}has(e){return this.data.has(e)}dispose(){this.data=new WeakMap}constructor(){this.data=new WeakMap}}let lO={VERTEX:1,INDEX:2,STORAGE:3,INDIRECT:4};class lV extends lD{delete(e){let t=super.delete(e);return null!==t&&this.backend.destroyAttribute(e),t}update(e,t){let r=this.get(e);if(void 0===r.version)t===lO.VERTEX?this.backend.createAttribute(e):t===lO.INDEX?this.backend.createIndexAttribute(e):t===lO.STORAGE?this.backend.createStorageAttribute(e):t===lO.INDIRECT&&this.backend.createIndirectStorageAttribute(e),r.version=this._getBufferAttribute(e).version;else{let t=this._getBufferAttribute(e);(r.version<t.version||t.usage===u.Vnu)&&(this.backend.updateAttribute(e),r.version=t.version)}}_getBufferAttribute(e){return e.isInterleavedBufferAttribute&&(e=e.data),e}constructor(e){super(),this.backend=e}}function lG(e){return null!==e.index?e.index.version:e.attributes.position.version}function lk(e){let t=[],r=e.index,i=e.attributes.position;if(null!==r){let e=r.array;for(let r=0,i=e.length;r<i;r+=3){let i=e[r+0],s=e[r+1],n=e[r+2];t.push(i,s,s,n,n,i)}}else{let e=i.array;for(let r=0,i=e.length/3-1;r<i;r+=3){let e=r+0,i=r+1,s=r+2;t.push(e,i,i,s,s,e)}}let s=new((0,u.AQS)(t)?u.MW4:u.A$4)(t,1);return s.version=lG(e),s}class lz extends lD{has(e){let t=e.geometry;return super.has(t)&&!0===this.get(t).initialized}updateForRender(e){!1===this.has(e)&&this.initGeometry(e),this.updateAttributes(e)}initGeometry(e){let t=e.geometry;this.get(t).initialized=!0,this.info.memory.geometries++;let r=()=>{this.info.memory.geometries--;let i=t.index,s=e.getAttributes();for(let e of(null!==i&&this.attributes.delete(i),s))this.attributes.delete(e);let n=this.wireframes.get(t);void 0!==n&&this.attributes.delete(n),t.removeEventListener("dispose",r)};t.addEventListener("dispose",r)}updateAttributes(e){for(let t of e.getAttributes())t.isStorageBufferAttribute||t.isStorageInstancedBufferAttribute?this.updateAttribute(t,lO.STORAGE):this.updateAttribute(t,lO.VERTEX);let t=this.getIndex(e);null!==t&&this.updateAttribute(t,lO.INDEX);let r=e.geometry.indirect;null!==r&&this.updateAttribute(r,lO.INDIRECT)}updateAttribute(e,t){let r=this.info.render.calls;e.isInterleavedBufferAttribute?void 0===this.attributeCall.get(e)?(this.attributes.update(e,t),this.attributeCall.set(e,r)):this.attributeCall.get(e.data)!==r&&(this.attributes.update(e,t),this.attributeCall.set(e.data,r),this.attributeCall.set(e,r)):this.attributeCall.get(e)!==r&&(this.attributes.update(e,t),this.attributeCall.set(e,r))}getIndirect(e){return e.geometry.indirect}getIndex(e){let{geometry:t,material:r}=e,i=t.index;if(!0===r.wireframe){let e=this.wireframes,r=e.get(t);void 0===r?(r=lk(t),e.set(t,r)):r.version!==lG(t)&&(this.attributes.delete(r),r=lk(t),e.set(t,r)),i=r}return i}constructor(e,t){super(),this.attributes=e,this.info=t,this.wireframes=new WeakMap,this.attributeCall=new WeakMap}}class lH{update(e,t,r){this.render.drawCalls++,e.isMesh||e.isSprite?this.render.triangles+=t/3*r:e.isPoints?this.render.points+=r*t:e.isLineSegments?this.render.lines+=t/2*r:e.isLine?this.render.lines+=r*(t-1):console.error("THREE.WebGPUInfo: Unknown object type.")}reset(){this.render.drawCalls=0,this.render.frameCalls=0,this.compute.frameCalls=0,this.render.triangles=0,this.render.points=0,this.render.lines=0}dispose(){this.reset(),this.calls=0,this.render.calls=0,this.compute.calls=0,this.render.timestamp=0,this.compute.timestamp=0,this.memory.geometries=0,this.memory.textures=0}constructor(){this.autoReset=!0,this.frame=0,this.calls=0,this.render={calls:0,frameCalls:0,drawCalls:0,triangles:0,points:0,lines:0,timestamp:0},this.compute={calls:0,frameCalls:0,timestamp:0},this.memory={geometries:0,textures:0}}}class lW{constructor(e){this.cacheKey=e,this.usedTimes=0}}class lq extends lW{constructor(e,t,r){super(e),this.vertexProgram=t,this.fragmentProgram=r}}class lj extends lW{constructor(e,t){super(e),this.computeProgram=t,this.isComputePipeline=!0}}let lX=0;class lQ{constructor(e,t,r,i=null,s=null){this.id=lX++,this.code=e,this.stage=t,this.name=r,this.transforms=i,this.attributes=s,this.usedTimes=0}}class lY extends lD{getForCompute(e,t){let{backend:r}=this,i=this.get(e);if(this._needsComputeUpdate(e)){let s=i.pipeline;s&&(s.usedTimes--,s.computeProgram.usedTimes--);let n=this.nodes.getForCompute(e),a=this.programs.compute.get(n.computeShader);void 0===a&&(s&&0===s.computeProgram.usedTimes&&this._releaseProgram(s.computeProgram),a=new lQ(n.computeShader,"compute",e.name,n.transforms,n.nodeAttributes),this.programs.compute.set(n.computeShader,a),r.createProgram(a));let o=this._getComputeCacheKey(e,a),l=this.caches.get(o);void 0===l&&(s&&0===s.usedTimes&&this._releasePipeline(s),l=this._getComputePipeline(e,a,o,t)),l.usedTimes++,a.usedTimes++,i.version=e.version,i.pipeline=l}return i.pipeline}getForRender(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,{backend:r}=this,i=this.get(e);if(this._needsRenderUpdate(e)){let s=i.pipeline;s&&(s.usedTimes--,s.vertexProgram.usedTimes--,s.fragmentProgram.usedTimes--);let n=e.getNodeBuilderState(),a=e.material?e.material.name:"",o=this.programs.vertex.get(n.vertexShader);void 0===o&&(s&&0===s.vertexProgram.usedTimes&&this._releaseProgram(s.vertexProgram),o=new lQ(n.vertexShader,"vertex",a),this.programs.vertex.set(n.vertexShader,o),r.createProgram(o));let l=this.programs.fragment.get(n.fragmentShader);void 0===l&&(s&&0===s.fragmentProgram.usedTimes&&this._releaseProgram(s.fragmentProgram),l=new lQ(n.fragmentShader,"fragment",a),this.programs.fragment.set(n.fragmentShader,l),r.createProgram(l));let u=this._getRenderCacheKey(e,o,l),d=this.caches.get(u);void 0===d?(s&&0===s.usedTimes&&this._releasePipeline(s),d=this._getRenderPipeline(e,o,l,u,t)):e.pipeline=d,d.usedTimes++,o.usedTimes++,l.usedTimes++,i.pipeline=d}return i.pipeline}delete(e){let t=this.get(e).pipeline;return t&&(t.usedTimes--,0===t.usedTimes&&this._releasePipeline(t),t.isComputePipeline?(t.computeProgram.usedTimes--,0===t.computeProgram.usedTimes&&this._releaseProgram(t.computeProgram)):(t.fragmentProgram.usedTimes--,t.vertexProgram.usedTimes--,0===t.vertexProgram.usedTimes&&this._releaseProgram(t.vertexProgram),0===t.fragmentProgram.usedTimes&&this._releaseProgram(t.fragmentProgram))),super.delete(e)}dispose(){super.dispose(),this.caches=new Map,this.programs={vertex:new Map,fragment:new Map,compute:new Map}}updateForRender(e){this.getForRender(e)}_getComputePipeline(e,t,r,i){r=r||this._getComputeCacheKey(e,t);let s=this.caches.get(r);return void 0===s&&(s=new lj(r,t),this.caches.set(r,s),this.backend.createComputePipeline(s,i)),s}_getRenderPipeline(e,t,r,i,s){i=i||this._getRenderCacheKey(e,t,r);let n=this.caches.get(i);return void 0===n&&(n=new lq(i,t,r),this.caches.set(i,n),e.pipeline=n,this.backend.createRenderPipeline(e,s)),n}_getComputeCacheKey(e,t){return e.id+","+t.id}_getRenderCacheKey(e,t,r){return t.id+","+r.id+","+this.backend.getRenderCacheKey(e)}_releasePipeline(e){this.caches.delete(e.cacheKey)}_releaseProgram(e){let t=e.code,r=e.stage;this.programs[r].delete(t)}_needsComputeUpdate(e){let t=this.get(e);return void 0===t.pipeline||t.version!==e.version}_needsRenderUpdate(e){return void 0===this.get(e).pipeline||this.backend.needsRenderUpdate(e)}constructor(e,t){super(),this.backend=e,this.nodes=t,this.bindings=null,this.caches=new Map,this.programs={vertex:new Map,fragment:new Map,compute:new Map}}}class lK extends lD{getForRender(e){let t=e.getBindings();for(let e of t){let r=this.get(e);void 0===r.bindGroup&&(this._init(e),this.backend.createBindings(e,t,0),r.bindGroup=e)}return t}getForCompute(e){let t=this.nodes.getForCompute(e).bindings;for(let e of t){let r=this.get(e);void 0===r.bindGroup&&(this._init(e),this.backend.createBindings(e,t,0),r.bindGroup=e)}return t}updateForCompute(e){this._updateBindings(this.getForCompute(e))}updateForRender(e){this._updateBindings(this.getForRender(e))}_updateBindings(e){for(let t of e)this._update(t,e)}_init(e){for(let t of e.bindings)if(t.isSampledTexture)this.textures.updateTexture(t.texture);else if(t.isStorageBuffer){let e=t.attribute,r=e.isIndirectStorageBufferAttribute?lO.INDIRECT:lO.STORAGE;this.attributes.update(e,r)}}_update(e,t){let{backend:r}=this,i=!1,s=!0,n=0,a=0;for(let t of e.bindings)if(!t.isNodeUniformsGroup||!1!==this.nodes.updateGroup(t)){if(t.isStorageBuffer){let e=t.attribute,r=e.isIndirectStorageBufferAttribute?lO.INDIRECT:lO.STORAGE;this.attributes.update(e,r)}if(t.isUniformBuffer)t.update()&&r.updateBinding(t);else if(t.isSampler)t.update();else if(t.isSampledTexture){let e=this.textures.get(t.texture);t.needsBindingsUpdate(e.generation)&&(i=!0);let o=t.update(),l=t.texture;o&&this.textures.updateTexture(l);let u=r.get(l);if(void 0!==u.externalTexture||e.isDefaultTexture?s=!1:(n=10*n+l.id,a+=l.version),!0===r.isWebGPUBackend&&void 0===u.texture&&void 0===u.externalTexture&&(console.error("Bindings._update: binding should be available:",t,o,l,t.textureNode.value,i),this.textures.updateTexture(l),i=!0),!0===l.isStorageTexture){let e=this.get(l);!0===t.store?e.needsMipmap=!0:this.textures.needsMipmaps(l)&&!0===e.needsMipmap&&(this.backend.generateMipmaps(l),e.needsMipmap=!1)}}}!0===i&&this.backend.updateBindings(e,t,s?n:0,a)}constructor(e,t,r,i,s,n){super(),this.backend=e,this.textures=r,this.pipelines=s,this.attributes=i,this.nodes=t,this.info=n,this.pipelines.bindings=this}}function lZ(e,t){return e.groupOrder!==t.groupOrder?e.groupOrder-t.groupOrder:e.renderOrder!==t.renderOrder?e.renderOrder-t.renderOrder:e.z!==t.z?e.z-t.z:e.id-t.id}function l$(e,t){return e.groupOrder!==t.groupOrder?e.groupOrder-t.groupOrder:e.renderOrder!==t.renderOrder?e.renderOrder-t.renderOrder:e.z!==t.z?t.z-e.z:e.id-t.id}function lJ(e){return(e.transmission>0||e.transmissionNode)&&e.side===u.$EB&&!1===e.forceSinglePass}class l0{begin(){return this.renderItemsIndex=0,this.opaque.length=0,this.transparentDoublePass.length=0,this.transparent.length=0,this.bundles.length=0,this.lightsArray.length=0,this.occlusionQueryCount=0,this}getNextRenderItem(e,t,r,i,s,n,a){let o=this.renderItems[this.renderItemsIndex];return void 0===o?(o={id:e.id,object:e,geometry:t,material:r,groupOrder:i,renderOrder:e.renderOrder,z:s,group:n,clippingContext:a},this.renderItems[this.renderItemsIndex]=o):(o.id=e.id,o.object=e,o.geometry=t,o.material=r,o.groupOrder=i,o.renderOrder=e.renderOrder,o.z=s,o.group=n,o.clippingContext=a),this.renderItemsIndex++,o}push(e,t,r,i,s,n,a){let o=this.getNextRenderItem(e,t,r,i,s,n,a);!0===e.occlusionTest&&this.occlusionQueryCount++,!0===r.transparent||r.transmission>0?(lJ(r)&&this.transparentDoublePass.push(o),this.transparent.push(o)):this.opaque.push(o)}unshift(e,t,r,i,s,n,a){let o=this.getNextRenderItem(e,t,r,i,s,n,a);!0===r.transparent||r.transmission>0?(lJ(r)&&this.transparentDoublePass.unshift(o),this.transparent.unshift(o)):this.opaque.unshift(o)}pushBundle(e){this.bundles.push(e)}pushLight(e){this.lightsArray.push(e)}sort(e,t){this.opaque.length>1&&this.opaque.sort(e||lZ),this.transparentDoublePass.length>1&&this.transparentDoublePass.sort(t||l$),this.transparent.length>1&&this.transparent.sort(t||l$)}finish(){this.lightsNode.setLights(this.lightsArray);for(let e=this.renderItemsIndex,t=this.renderItems.length;e<t;e++){let t=this.renderItems[e];if(null===t.id)break;t.id=null,t.object=null,t.geometry=null,t.material=null,t.groupOrder=null,t.renderOrder=null,t.z=null,t.group=null,t.clippingContext=null}}constructor(e,t,r){this.renderItems=[],this.renderItemsIndex=0,this.opaque=[],this.transparentDoublePass=[],this.transparent=[],this.bundles=[],this.lightsNode=e.getNode(t,r),this.lightsArray=[],this.scene=t,this.camera=r,this.occlusionQueryCount=0}}let l1=[];class l2{get(e,t){let r=this.lists;l1[0]=e,l1[1]=t;let i=r.get(l1);return void 0===i&&(i=new l0(this.lighting,e,t),r.set(l1,i)),l1.length=0,i}dispose(){this.lists=new lP}constructor(e){this.lighting=e,this.lists=new lP}}let l3=0;class l4{getCacheKey(){return l6(this)}constructor(){this.id=l3++,this.color=!0,this.clearColor=!0,this.clearColorValue={r:0,g:0,b:0,a:1},this.depth=!0,this.clearDepth=!0,this.clearDepthValue=1,this.stencil=!1,this.clearStencil=!0,this.clearStencilValue=1,this.viewport=!1,this.viewportValue=new u.IUQ,this.scissor=!1,this.scissorValue=new u.IUQ,this.renderTarget=null,this.textures=null,this.depthTexture=null,this.activeCubeFace=0,this.activeMipmapLevel=0,this.sampleCount=1,this.width=0,this.height=0,this.occlusionQueryCount=0,this.clippingContext=null,this.isRenderContext=!0}}function l6(e){let{textures:t,activeCubeFace:r}=e,i=[r];for(let e of t)i.push(e.id);return g(i)}let l8=[],l5=new u.Z58,l9=new u.i7d;class l7{get(e,t){let r,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(l8[0]=e,l8[1]=t,null===i)r="default";else{let e=i.texture.format,t=i.textures.length;r="".concat(t,":").concat(e,":").concat(i.samples,":").concat(i.depthBuffer,":").concat(i.stencilBuffer)}let s=this._getChainMap(r),n=s.get(l8);return void 0===n&&(n=new l4,s.set(l8,n)),l8.length=0,null!==i&&(n.sampleCount=0===i.samples?1:i.samples),n}getForClear(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return this.get(l5,l9,e)}_getChainMap(e){return this.chainMaps[e]||(this.chainMaps[e]=new lP)}dispose(){this.chainMaps={}}constructor(){this.chainMaps={}}}let ue=new u.Pq0;class ut extends lD{updateRenderTarget(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=this.get(e),i=0===e.samples?1:e.samples,s=r.depthTextureMips||(r.depthTextureMips={}),n=e.textures,a=this.getSize(n[0]),o=a.width>>t,l=a.height>>t,d=e.depthTexture||s[t],h=!0===e.depthBuffer||!0===e.stencilBuffer,c=!1;void 0===d&&h&&((d=!0===e.multiview&&a.depth>1?new u.S7T:new u.VCu).format=e.stencilBuffer?u.dcC:u.zdS,d.type=e.stencilBuffer?u.V3x:u.bkx,d.image.width=o,d.image.height=l,d.image.depth=a.depth,s[t]=d),(r.width!==a.width||a.height!==r.height)&&(c=!0,d&&(d.needsUpdate=!0,d.image.width=o,d.image.height=l,d.image.depth=d.isDepthArrayTexture?d.image.depth:1)),r.width=a.width,r.height=a.height,r.textures=n,r.depthTexture=d||null,r.depth=e.depthBuffer,r.stencil=e.stencilBuffer,r.renderTarget=e,r.sampleCount!==i&&(c=!0,d&&(d.needsUpdate=!0),r.sampleCount=i);let p={sampleCount:i};if(!0!==e.isXRRenderTarget){for(let t=0;t<n.length;t++){let r=n[t];r.isTextureArray=!0===e.multiview&&a.depth>1,c&&(r.needsUpdate=!0),this.updateTexture(r,p)}d&&this.updateTexture(d,p)}if(!0!==r.initialized){r.initialized=!0;let t=()=>{e.removeEventListener("dispose",t);for(let e=0;e<n.length;e++)this._destroyTexture(n[e]);d&&this._destroyTexture(d),this.delete(e)};e.addEventListener("dispose",t)}}updateTexture(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=this.get(e);if(!0===r.initialized&&r.version===e.version)return;let i=e.isRenderTargetTexture||e.isDepthTexture||e.isFramebufferTexture,s=this.backend;if(i&&!0===r.initialized&&(s.destroySampler(e),s.destroyTexture(e)),e.isFramebufferTexture){let t=this.renderer.getRenderTarget();t?e.type=t.texture.type:e.type=u.OUM}let{width:n,height:a,depth:o}=this.getSize(e);if(t.width=n,t.height=a,t.depth=o,t.needsMipmaps=this.needsMipmaps(e),t.levels=t.needsMipmaps?this.getMipLevels(e,n,a):1,i||!0===e.isStorageTexture)s.createSampler(e),s.createTexture(e,t),r.generation=e.version;else if(!0!==r.initialized&&s.createSampler(e),e.version>0){let i=e.image;if(void 0===i)console.warn("THREE.Renderer: Texture marked for update but image is undefined.");else if(!1===i.complete)console.warn("THREE.Renderer: Texture marked for update but image is incomplete.");else{if(e.images){let r=[];for(let t of e.images)r.push(t);t.images=r}else t.image=i;(void 0===r.isDefaultTexture||!0===r.isDefaultTexture)&&(s.createTexture(e,t),r.isDefaultTexture=!1,r.generation=e.version),!0===e.source.dataReady&&s.updateTexture(e,t),t.needsMipmaps&&0===e.mipmaps.length&&s.generateMipmaps(e)}}else s.createDefaultTexture(e),r.isDefaultTexture=!0,r.generation=e.version;if(!0!==r.initialized){r.initialized=!0,r.generation=e.version,this.info.memory.textures++;let t=()=>{e.removeEventListener("dispose",t),this._destroyTexture(e)};e.addEventListener("dispose",t)}r.version=e.version}getSize(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:ue,r=e.images?e.images[0]:e.image;return r?(void 0!==r.image&&(r=r.image),t.width=r.width||1,t.height=r.height||1,t.depth=e.isCubeTexture?6:r.depth||1):t.width=t.height=t.depth=1,t}getMipLevels(e,t,r){let i;return e.isCompressedTexture?e.mipmaps?e.mipmaps.length:1:Math.floor(Math.log2(Math.max(t,r)))+1}needsMipmaps(e){return!0===e.isCompressedTexture||e.generateMipmaps}_destroyTexture(e){!0===this.has(e)&&(this.backend.destroySampler(e),this.backend.destroyTexture(e),this.delete(e),this.info.memory.textures--)}constructor(e,t,r){super(),this.renderer=e,this.backend=t,this.info=r}}class ur extends u.Q1f{set(e,t,r){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1;return this.a=i,super.set(e,t,r)}copy(e){return void 0!==e.a&&(this.a=e.a),super.copy(e)}clone(){return new this.constructor(this.r,this.g,this.b,this.a)}constructor(e,t,r,i=1){super(e,t,r),this.a=i}}class ui extends eZ{static get type(){return"ParameterNode"}getHash(){return this.uuid}generate(){return this.name}constructor(e,t=null){super(e,t),this.isParameterNode=!0}}class us extends F{static get type(){return"StackNode"}getNodeType(e){return this.outputNode?this.outputNode.getNodeType(e):"void"}getMemberType(e,t){return this.outputNode?this.outputNode.getMemberType(e,t):"void"}add(e){return this.nodes.push(e),this}If(e,t){let r=new eb(t);return this._currentCond=rX(e,r),this.add(this._currentCond)}ElseIf(e,t){let r=rX(e,new eb(t));return this._currentCond.elseNode=r,this._currentCond=r,this}Else(e){return this._currentCond.elseNode=new eb(e),this}Switch(e){return this._expressionNode=eT(e),this}Case(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let i=[];if(t.length>=2)for(let e=0;e<t.length-1;e++)i.push(this._expressionNode.equal(eT(t[e])));else throw Error("TSL: Invalid parameter length. Case() requires at least two parameters.");let s=new eb(t[t.length-1]),n=i[0];for(let e=1;e<i.length;e++)n=n.or(i[e]);let a=rX(n,s);return null===this._currentCond?(this._currentCond=a,this.add(this._currentCond)):(this._currentCond.elseNode=a,this._currentCond=a,this)}Default(e){return this.Else(e),this}build(e){for(var t=arguments.length,r=Array(t>1?t-1:0),i=1;i<t;i++)r[i-1]=arguments[i];let s=eC();for(let t of(eE(this),this.nodes))t.build(e,"void");return eE(s),this.outputNode?this.outputNode.build(e,...r):super.build(e,...r)}else(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return console.warn("THREE.TSL: .else() has been renamed to .Else()."),this.Else(...t)}elseif(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return console.warn("THREE.TSL: .elseif() has been renamed to .ElseIf()."),this.ElseIf(...t)}constructor(e=null){super(),this.nodes=[],this.outputNode=null,this.parent=e,this._currentCond=null,this._expressionNode=null,this.isStackNode=!0}}let un=eN(us).setParameterLength(0,1);class ua extends F{static get type(){return"OutputStructNode"}getNodeType(e){let t=e.getNodeProperties(this);if(void 0===t.membersLayout){let r=this.members,i=[];for(let t=0;t<r.length;t++){let s="m"+t,n=r[t].getNodeType(e);i.push({name:s,type:n,index:t})}t.membersLayout=i,t.structType=e.getOutputStructTypeFromNode(this,t.membersLayout)}return t.structType.name}generate(e){let t=e.getOutputStructName(),r=this.members,i=""!==t?t+".":"";for(let t=0;t<r.length;t++){let s=r[t].build(e);e.addLineFlowCode("".concat(i,"m").concat(t," = ").concat(s),this)}return t}constructor(...e){super(),this.members=e,this.isOutputStructNode=!0}}class uo extends ua{static get type(){return"MRTNode"}has(e){return void 0!==this.outputNodes[e]}get(e){return this.outputNodes[e]}merge(e){return ul({...this.outputNodes,...e.outputNodes})}setup(e){let t=this.outputNodes,r=e.renderer.getRenderTarget(),i=[],s=r.textures;for(let e in t)i[function(e,t){for(let r=0;r<e.length;r++)if(e[r].name===t)return r;return -1}(s,e)]=eW(t[e]);return this.members=i,super.setup(e)}constructor(e){super(),this.outputNodes=e,this.isMRTNode=!0}}let ul=eN(uo),uu=(e=>{let[t]=e,r=t.toUint().mul(0x2c9277b5).add(0xac564b05),i=r.shiftRight(r.shiftRight(28).add(4)).bitXor(r).mul(0x108ef2d9);return i.shiftRight(22).bitXor(i).toFloat().mul(1/0x100000000)},eA(e=>{let[t]=e;return t.fract().sub(.5).abs()}).setLayout({name:"tri",type:"float",inputs:[{name:"x",type:"float"}]})),ud=eA(e=>{let[t]=e;return eG(uu(t.z.add(uu(t.y.mul(1)))),uu(t.z.add(uu(t.x.mul(1)))),uu(t.y.add(uu(t.x.mul(1)))))}).setLayout({name:"tri3",type:"vec3",inputs:[{name:"p",type:"vec3"}]});class uh extends F{static get type(){return"FunctionOverloadingNode"}getNodeType(){return this.functionNodes[0].shaderNode.layout.type}setup(e){let t=this.parametersNodes,r=this._candidateFnCall;if(null===r){let i=null,s=-1;for(let r of this.functionNodes){let n=r.shaderNode.layout;if(null===n)throw Error("FunctionOverloadingNode: FunctionNode must be a layout.");let a=n.inputs;if(t.length===a.length){let n=0;for(let r=0;r<t.length;r++){let i=t[r],s=a[r];i.getNodeType(e)===s.type?n++:n=0}n>s&&(i=r,s=n)}}this._candidateFnCall=r=i(...t)}return r}constructor(e=[],...t){super(),this.functionNodes=e,this.parametersNodes=t,this._candidateFnCall=null,this.global=!0}}let uc=eN(uh),up=e=>function(){for(var t=arguments.length,r=Array(t),i=0;i<t;i++)r[i]=arguments[i];return uc(e,...r)};e=>e.time,e=>{let[t=null]=e,r=at();return at(n3(t)).sub(r).lessThan(0).select(nX,t)};class ug extends F{static get type(){return"SpriteSheetUVNode"}setup(){let{frameNode:e,uvNode:t,countNode:r}=this,{width:i,height:s}=r,n=e.mod(i.mul(s)).floor(),a=n.mod(i),o=s.sub(n.add(1).div(i).ceil()),l=r.reciprocal(),u=eI(a,o);return t.add(u).mul(l)}constructor(e,t=iN(),r=eP(0)){super("vec2"),this.countNode=e,this.uvNode=t,this.frameNode=r}}class um extends F{static get type(){return"TriplanarTexturesNode"}setup(){let{textureXNode:e,textureYNode:t,textureZNode:r,scaleNode:i,positionNode:s,normalNode:n}=this,a=n.abs().normalize();a=a.div(a.dot(eG(1)));let o=s.yz.mul(i),l=s.zx.mul(i),u=s.xy.mul(i),d=e.value,h=null!==t?t.value:d,c=null!==r?r.value:d,p=iw(d,o).mul(a.x);return tM(p,iw(h,l).mul(a.y),iw(c,u).mul(a.z))}constructor(e,t=null,r=null,i=eP(1),s=i1,n=se){super("vec4"),this.textureXNode=e,this.textureYNode=t,this.textureZNode=r,this.scaleNode=i,this.positionNode=s,this.normalNode=n}}let uf=new u.Zcv,uy=new u.Pq0,ux=new u.Pq0,ub=new u.Pq0,uT=new u.kn4,uv=new u.Pq0(0,0,-1),u_=new u.IUQ,uN=new u.Pq0,uS=new u.Pq0,uR=new u.IUQ,uA=new u.I9Y,uE=new u.O0B,uC=nX.flipX();uE.depthTexture=new u.VCu(1,1);let uw=!1;class uM extends iC{static get type(){return"ReflectorNode"}get reflector(){return this._reflectorBaseNode}get target(){return this._reflectorBaseNode.target}getDepthNode(){if(null===this._depthNode){if(!0!==this._reflectorBaseNode.depth)throw Error("THREE.ReflectorNode: Depth node can only be requested when the reflector is created with { depth: true }. ");this._depthNode=eT(new uM({defaultTexture:uE.depthTexture,reflector:this._reflectorBaseNode}))}return this._depthNode}setup(e){return e.object.isQuadMesh||this._reflectorBaseNode.build(e),super.setup(e)}clone(){let e=new this.constructor(this.reflectorNode);return e._reflectorBaseNode=this._reflectorBaseNode,e}dispose(){super.dispose(),this._reflectorBaseNode.dispose()}constructor(e={}){super(e.defaultTexture||uE.texture,uC),this._reflectorBaseNode=e.reflector||new uB(this,e),this._depthNode=null,this.setUpdateMatrix(!1)}}class uB extends F{static get type(){return"ReflectorBaseNode"}_updateResolution(e,t){let r=this.resolution;t.getDrawingBufferSize(uA),e.setSize(Math.round(uA.width*r),Math.round(uA.height*r))}setup(e){return this._updateResolution(uE,e.renderer),super.setup(e)}dispose(){for(let e of(super.dispose(),this.renderTargets.values()))e.dispose()}getVirtualCamera(e){let t=this.virtualCameras.get(e);return void 0===t&&(t=e.clone(),this.virtualCameras.set(e,t)),t}getRenderTarget(e){let t=this.renderTargets.get(e);return void 0===t&&(t=new u.O0B(0,0,{type:u.ix0}),!0===this.generateMipmaps&&(t.texture.minFilter=u.NZq,t.texture.generateMipmaps=!0),!0===this.depth&&(t.depthTexture=new u.VCu),this.renderTargets.set(e,t)),t}updateBefore(e){if(!1===this.bounces&&uw)return!1;uw=!0;let{scene:t,camera:r,renderer:i,material:s}=e,{target:n}=this,a=this.getVirtualCamera(r),o=this.getRenderTarget(a);if(i.getDrawingBufferSize(uA),this._updateResolution(o,i),ux.setFromMatrixPosition(n.matrixWorld),ub.setFromMatrixPosition(r.matrixWorld),uT.extractRotation(n.matrixWorld),uy.set(0,0,1),uy.applyMatrix4(uT),uN.subVectors(ux,ub),!0==uN.dot(uy)>0&&!1===this.forceUpdate)return;uN.reflect(uy).negate(),uN.add(ux),uT.extractRotation(r.matrixWorld),uv.set(0,0,-1),uv.applyMatrix4(uT),uv.add(ub),uS.subVectors(ux,uv),uS.reflect(uy).negate(),uS.add(ux),a.coordinateSystem=r.coordinateSystem,a.position.copy(uN),a.up.set(0,1,0),a.up.applyMatrix4(uT),a.up.reflect(uy),a.lookAt(uS),a.near=r.near,a.far=r.far,a.updateMatrixWorld(),a.projectionMatrix.copy(r.projectionMatrix),uf.setFromNormalAndCoplanarPoint(uy,ux),uf.applyMatrix4(a.matrixWorldInverse),u_.set(uf.normal.x,uf.normal.y,uf.normal.z,uf.constant);let l=a.projectionMatrix;uR.x=(Math.sign(u_.x)+l.elements[8])/l.elements[0],uR.y=(Math.sign(u_.y)+l.elements[9])/l.elements[5],uR.z=-1,uR.w=(1+l.elements[10])/l.elements[14],u_.multiplyScalar(1/u_.dot(uR)),l.elements[2]=u_.x,l.elements[6]=u_.y,l.elements[10]=i.coordinateSystem===u.i7u?u_.z-0:u_.z+1-0,l.elements[14]=u_.w,this.textureNode.value=o.texture,!0===this.depth&&(this.textureNode.getDepthNode().value=o.depthTexture),s.visible=!1;let d=i.getRenderTarget(),h=i.getMRT(),c=i.autoClear;i.setMRT(null),i.setRenderTarget(o),i.autoClear=!0,i.render(t,a),i.setMRT(h),i.setRenderTarget(d),i.autoClear=c,s.visible=!0,uw=!1,this.forceUpdate=!1}constructor(e,t={}){super();let{target:r=new u.B69,resolution:i=1,generateMipmaps:s=!1,bounces:n=!0,depth:a=!1}=t;this.textureNode=e,this.target=r,this.resolution=i,this.generateMipmaps=s,this.bounces=n,this.depth=a,this.updateBeforeType=n?E.RENDER:E.FRAME,this.virtualCameras=new WeakMap,this.renderTargets=new Map,this.forceUpdate=!1}}let uP=new u.qUd(-1,1,1,-1,0,1);class uF extends u.LoY{constructor(e=!1){super(),this.setAttribute("position",new u.qtW([-1,3,0,-1,-1,0,3,-1,0],3)),this.setAttribute("uv",new u.qtW(!1===e?[0,-1,0,1,2,1]:[0,2,0,0,2,0],2))}}let uU=new uF;class uL extends u.eaF{async renderAsync(e){return e.renderAsync(this,uP)}render(e){e.render(this,uP)}constructor(e=null){super(uU,e),this.camera=uP,this.isQuadMesh=!0}}let uI=new u.I9Y;class uD extends iC{static get type(){return"RTTNode"}get autoSize(){return null===this.width}setup(e){return this._rttNode=this.node.context(e.getSharedContext()),this._quadMesh.material.name="RTT",this._quadMesh.material.needsUpdate=!0,super.setup(e)}setSize(e,t){this.width=e,this.height=t;let r=e*this.pixelRatio,i=t*this.pixelRatio;this.renderTarget.setSize(r,i),this.textureNeedsUpdate=!0}setPixelRatio(e){this.pixelRatio=e,this.setSize(this.width,this.height)}updateBefore(e){let{renderer:t}=e;if(!1===this.textureNeedsUpdate&&!1===this.autoUpdate)return;if(this.textureNeedsUpdate=!1,!0===this.autoSize){this.pixelRatio=t.getPixelRatio();let e=t.getSize(uI);this.setSize(e.width,e.height)}this._quadMesh.material.fragmentNode=this._rttNode;let r=t.getRenderTarget();t.setRenderTarget(this.renderTarget),this._quadMesh.render(t),t.setRenderTarget(r)}clone(){let e=new iC(this.value,this.uvNode,this.levelNode);return e.sampler=this.sampler,e.referenceNode=this,e}constructor(e,t=null,r=null,i={type:u.ix0}){let s=new u.O0B(t,r,i);super(s.texture,iN()),this.node=e,this.width=t,this.height=r,this.pixelRatio=1,this.renderTarget=s,this.textureNeedsUpdate=!0,this.autoUpdate=!0,this._rttNode=null,this._quadMesh=new uL(new ah),this.updateBeforeType=E.RENDER}}let uO=eA((e,t)=>{let r,[i,s,n]=e;r=t.renderer.coordinateSystem===u.i7u?eW(eG(i=eI(i.x,i.y.oneMinus()).mul(2).sub(1),s),1):eW(eG(i.x,i.y.oneMinus(),s).mul(2).sub(1),1);let a=eW(n.mul(r));return a.xyz.div(a.w)});u.uWO,u.THS;class uV extends F{static get type(){return"PointUVNode"}generate(){return"vec2( gl_PointCoord.x, 1.0 - gl_PointCoord.y )"}constructor(){super("vec2"),this.isPointUVNode=!0}}let uG=new u.O9p,uk=new u.kn4;class uz extends F{static get type(){return"SceneNode"}setup(e){let t,r=this.scope,i=null!==this.scene?this.scene:e.scene;return r===uz.BACKGROUND_BLURRINESS?t=s_("backgroundBlurriness","float",i):r===uz.BACKGROUND_INTENSITY?t=s_("backgroundIntensity","float",i):r===uz.BACKGROUND_ROTATION?t=tN("mat4").label("backgroundRotation").setGroup(tT).onRenderUpdate(()=>{let e=i.background;return null!==e&&e.isTexture&&e.mapping!==u.UTZ?(uG.copy(i.backgroundRotation),uG.x*=-1,uG.y*=-1,uG.z*=-1,uk.makeRotationFromEuler(uG)):uk.identity(),uk}):console.error("THREE.SceneNode: Unknown scope:",r),t}constructor(e=uz.BACKGROUND_BLURRINESS,t=null){super(),this.scope=e,this.scene=t}}uz.BACKGROUND_BLURRINESS="backgroundBlurriness",uz.BACKGROUND_INTENSITY="backgroundIntensity",uz.BACKGROUND_ROTATION="backgroundRotation";let uH=eS(uz,uz.BACKGROUND_BLURRINESS),uW=eS(uz,uz.BACKGROUND_INTENSITY),uq=eS(uz,uz.BACKGROUND_ROTATION);class uj extends iC{static get type(){return"StorageTextureNode"}getInputType(){return"storageTexture"}setup(e){super.setup(e),e.getNodeProperties(this).storeNode=this.storeNode}setAccess(e){return this.access=e,this}generate(e,t){let r;return null!==this.storeNode?this.generateStore(e):super.generate(e,t)}toReadWrite(){return this.setAccess(C.READ_WRITE)}toReadOnly(){return this.setAccess(C.READ_ONLY)}toWriteOnly(){return this.setAccess(C.WRITE_ONLY)}generateStore(e){let{uvNode:t,storeNode:r}=e.getNodeProperties(this),i=super.generate(e,"property"),s=t.build(e,"uvec2"),n=r.build(e,"vec4"),a=e.generateTextureStore(e,i,s,n);e.addLineFlowCode(a,this)}constructor(e,t,r=null){super(e,t),this.storeNode=r,this.isStorageTextureNode=!0,this.access=C.WRITE_ONLY}}let uX=eA(e=>{let{texture:t,uv:r}=e,i=eG().toVar();return ew(r.x.lessThan(1e-4),()=>{i.assign(eG(1,0,0))}).ElseIf(r.y.lessThan(1e-4),()=>{i.assign(eG(0,1,0))}).ElseIf(r.z.lessThan(1e-4),()=>{i.assign(eG(0,0,1))}).ElseIf(r.x.greaterThan(.9999),()=>{i.assign(eG(-1,0,0))}).ElseIf(r.y.greaterThan(.9999),()=>{i.assign(eG(0,-1,0))}).ElseIf(r.z.greaterThan(.9999),()=>{i.assign(eG(0,0,-1))}).Else(()=>{let e=t.sample(r.add(eG(-.01,0,0))).r.sub(t.sample(r.add(eG(.01,0,0))).r),s=t.sample(r.add(eG(0,-.01,0))).r.sub(t.sample(r.add(eG(0,.01,0))).r),n=t.sample(r.add(eG(0,0,-.01))).r.sub(t.sample(r.add(eG(0,0,.01))).r);i.assign(eG(e,s,n))}),i.normalize()});class uQ extends iC{static get type(){return"Texture3DNode"}getInputType(){return"texture3D"}getDefaultUV(){return eG(.5,.5,.5)}setUpdateMatrix(){}setupUV(e,t){let r=this.value;return e.isFlipY()&&(!0===r.isRenderTargetTexture||!0===r.isFramebufferTexture)&&(t=this.sampler?t.flipY():t.setY(eF(iR(this,this.levelNode).y).sub(t.y).sub(1))),t}generateUV(e,t){return t.build(e,"vec3")}normal(e){return uX({texture:this,uv:e})}constructor(e,t=null,r=null){super(e,t,r),this.isTexture3DNode=!0}}let uY=eN(uQ).setParameterLength(1,3),uK=new WeakMap;class uZ extends I{static get type(){return"VelocityNode"}setProjectionMatrix(e){this.projectionMatrix=e}update(e){let{frameId:t,camera:r,object:i}=e,s=uJ(i);this.previousModelWorldMatrix.value.copy(s);let n=u$(r);n.frameId!==t&&(n.frameId=t,void 0===n.previousProjectionMatrix?(n.previousProjectionMatrix=new u.kn4,n.previousCameraViewMatrix=new u.kn4,n.currentProjectionMatrix=new u.kn4,n.currentCameraViewMatrix=new u.kn4,n.previousProjectionMatrix.copy(this.projectionMatrix||r.projectionMatrix),n.previousCameraViewMatrix.copy(r.matrixWorldInverse)):(n.previousProjectionMatrix.copy(n.currentProjectionMatrix),n.previousCameraViewMatrix.copy(n.currentCameraViewMatrix)),n.currentProjectionMatrix.copy(this.projectionMatrix||r.projectionMatrix),n.currentCameraViewMatrix.copy(r.matrixWorldInverse),this.previousProjectionMatrix.value.copy(n.previousProjectionMatrix),this.previousCameraViewMatrix.value.copy(n.previousCameraViewMatrix))}updateAfter(e){let{object:t}=e;uJ(t).copy(t.matrixWorld)}setup(){let e=null===this.projectionMatrix?ik:tN(this.projectionMatrix),t=this.previousCameraViewMatrix.mul(this.previousModelWorldMatrix),r=e.mul(iK).mul(i1),i=this.previousProjectionMatrix.mul(t).mul(i2);return tB(r.xy.div(r.w),i.xy.div(i.w))}constructor(){super("vec2"),this.projectionMatrix=null,this.updateType=E.OBJECT,this.updateAfterType=E.OBJECT,this.previousModelWorldMatrix=tN(new u.kn4),this.previousProjectionMatrix=tN(new u.kn4).setGroup(tT),this.previousCameraViewMatrix=tN(new u.kn4)}}function u$(e){let t=uK.get(e);return void 0===t&&(t={},uK.set(e,t)),t}function uJ(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=u$(e),i=r[t];return void 0===i&&(r[t]=i=new u.kn4,r[t].copy(e.matrixWorld)),i}e=>{let[t]=e;return u0(t.rgb)},e=>{let[t,r=eP(1)]=e,i=eG(.57735,.57735,.57735),s=r.cos();return eG(t.rgb.mul(s).add(i.cross(t.rgb).mul(r.sin()).add(i.mul(rP(i,t.rgb).mul(s.oneMinus())))))};let u0=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:eG(u.ppV.getLuminanceCoefficients(new u.Pq0));return rP(e,t)};e=>{let[t,r=eG(1),i=eG(0),s=eG(1),n=eP(1),a=eG(u.ppV.getLuminanceCoefficients(new u.Pq0,u.Zr2))]=e,o=t.rgb.dot(eG(a)),l=rE(t.rgb.mul(r).add(i),0).toVar(),d=l.pow(s).toVar();return ew(l.r.greaterThan(0),()=>{l.r.assign(d.r)}),ew(l.g.greaterThan(0),()=>{l.g.assign(d.g)}),ew(l.b.greaterThan(0),()=>{l.b.assign(d.b)}),l.assign(o.add(l.sub(o).mul(n))),eW(l.rgb,t.a)};class u1 extends I{static get type(){return"PosterizeNode"}setup(){let{sourceNode:e,stepsNode:t}=this;return e.mul(t).floor().div(t)}constructor(e,t){super(),this.sourceNode=e,this.stepsNode=t}}let u2=new u.I9Y;class u3 extends iC{static get type(){return"PassTextureNode"}setup(e){return e.object.isQuadMesh&&this.passNode.build(e),super.setup(e)}clone(){return new this.constructor(this.passNode,this.value)}constructor(e,t){super(t),this.passNode=e,this.setUpdateMatrix(!1)}}class u4 extends u3{static get type(){return"PassMultipleTextureNode"}updateTexture(){this.value=this.previousTexture?this.passNode.getPreviousTexture(this.textureName):this.passNode.getTexture(this.textureName)}setup(e){return this.updateTexture(),super.setup(e)}clone(){return new this.constructor(this.passNode,this.textureName,this.previousTexture)}constructor(e,t,r=!1){super(e,null),this.textureName=t,this.previousTexture=r}}class u6 extends I{static get type(){return"PassNode"}setResolution(e){return this._resolution=e,this}getResolution(){return this._resolution}setLayers(e){return this._layers=e,this}getLayers(){return this._layers}setMRT(e){return this._mrt=e,this}getMRT(){return this._mrt}isGlobal(){return!0}getTexture(e){let t=this._textures[e];return void 0===t&&((t=this.renderTarget.texture.clone()).name=e,this._textures[e]=t,this.renderTarget.textures.push(t)),t}getPreviousTexture(e){let t=this._previousTextures[e];return void 0===t&&(t=this.getTexture(e).clone(),this._previousTextures[e]=t),t}toggleTexture(e){let t=this._previousTextures[e];if(void 0!==t){let r=this._textures[e],i=this.renderTarget.textures.indexOf(r);this.renderTarget.textures[i]=t,this._textures[e]=t,this._previousTextures[e]=r,this._textureNodes[e].updateTexture(),this._previousTextureNodes[e].updateTexture()}}getTextureNode(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"output",t=this._textureNodes[e];return void 0===t&&((t=eT(new u4(this,e))).updateTexture(),this._textureNodes[e]=t),t}getPreviousTextureNode(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"output",t=this._previousTextureNodes[e];return void 0===t&&(void 0===this._textureNodes[e]&&this.getTextureNode(e),(t=eT(new u4(this,e,!0))).updateTexture(),this._previousTextureNodes[e]=t),t}getViewZNode(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"depth",t=this._viewZNodes[e];if(void 0===t){let r=this._cameraNear,i=this._cameraFar;this._viewZNodes[e]=t=n5(this.getTextureNode(e),r,i)}return t}getLinearDepthNode(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"depth",t=this._linearDepthNodes[e];if(void 0===t){let r=this._cameraNear,i=this._cameraFar,s=this.getViewZNode(e);this._linearDepthNodes[e]=t=n6(s,r,i)}return t}setup(e){let{renderer:t}=e;return this.renderTarget.samples=void 0===this.options.samples?t.samples:this.options.samples,!0===t.backend.isWebGLBackend&&(this.renderTarget.samples=0),this.renderTarget.texture.type=t.getColorBufferType(),this.scope===u6.COLOR?this.getTextureNode():this.getLinearDepthNode()}updateBefore(e){let t,r,{renderer:i}=e,{scene:s}=this,n=i.getOutputRenderTarget();n&&!0===n.isXRRenderTarget?(r=1,t=i.xr.getCamera(),i.xr.updateCamera(t),u2.set(n.width,n.height)):(t=this.camera,r=i.getPixelRatio(),i.getSize(u2)),this._pixelRatio=r,this.setSize(u2.width,u2.height);let a=i.getRenderTarget(),o=i.getMRT(),l=t.layers.mask;for(let e in this._cameraNear.value=t.near,this._cameraFar.value=t.far,null!==this._layers&&(t.layers.mask=this._layers.mask),this._previousTextures)this.toggleTexture(e);i.setRenderTarget(this.renderTarget),i.setMRT(this._mrt),i.render(s,t),i.setRenderTarget(a),i.setMRT(o),t.layers.mask=l}setSize(e,t){this._width=e,this._height=t;let r=this._width*this._pixelRatio*this._resolution,i=this._height*this._pixelRatio*this._resolution;this.renderTarget.setSize(r,i)}setPixelRatio(e){this._pixelRatio=e,this.setSize(this._width,this._height)}dispose(){this.renderTarget.dispose()}constructor(e,t,r,i={}){super("vec4"),this.scope=e,this.scene=t,this.camera=r,this.options=i,this._pixelRatio=1,this._width=1,this._height=1;let s=new u.VCu;s.isRenderTargetTexture=!0,s.name="depth";let n=new u.O0B(this._width*this._pixelRatio,this._height*this._pixelRatio,{type:u.ix0,...i});n.texture.name="output",n.depthTexture=s,this.renderTarget=n,this._textures={output:n.texture,depth:s},this._textureNodes={},this._linearDepthNodes={},this._viewZNodes={},this._previousTextures={},this._previousTextureNodes={},this._cameraNear=tN(0),this._cameraFar=tN(0),this._mrt=null,this._layers=null,this._resolution=1,this.isPassNode=!0,this.updateBeforeType=E.FRAME}}u6.COLOR="color",u6.DEPTH="depth";let u8=eA(e=>{let[t,r]=e;return t.mul(r).clamp()}).setLayout({name:"linearToneMapping",type:"vec3",inputs:[{name:"color",type:"vec3"},{name:"exposure",type:"float"}]}),u5=eA(e=>{let[t,r]=e;return(t=t.mul(r)).div(t.add(1)).clamp()}).setLayout({name:"reinhardToneMapping",type:"vec3",inputs:[{name:"color",type:"vec3"},{name:"exposure",type:"float"}]}),u9=eA(e=>{let[t,r]=e,i=(t=(t=t.mul(r)).sub(.004).max(0)).mul(t.mul(6.2).add(.5)),s=t.mul(t.mul(6.2).add(1.7)).add(.06);return i.div(s).pow(2.2)}).setLayout({name:"cineonToneMapping",type:"vec3",inputs:[{name:"color",type:"vec3"},{name:"exposure",type:"float"}]}),u7=eA(e=>{let[t]=e,r=t.mul(t.add(.0245786)).sub(90537e-9),i=t.mul(t.add(.432951).mul(.983729)).add(.238081);return r.div(i)}),de=eA(e=>{let[t,r]=e,i=eY(.59719,.35458,.04823,.076,.90834,.01566,.0284,.13383,.83777),s=eY(1.60475,-.53108,-.07367,-.10208,1.10813,-.00605,-.00327,-.07276,1.07602);return t=t.mul(r).div(.6),t=u7(t=i.mul(t)),(t=s.mul(t)).clamp()}).setLayout({name:"acesFilmicToneMapping",type:"vec3",inputs:[{name:"color",type:"vec3"},{name:"exposure",type:"float"}]}),dt=eY(eG(1.6605,-.1246,-.0182),eG(-.5876,1.1329,-.1006),eG(-.0728,-.0083,1.1187)),dr=eY(eG(.6274,.0691,.0164),eG(.3293,.9195,.088),eG(.0433,.0113,.8956)),di=eA(e=>{let[t]=e,r=eG(t).toVar(),i=eG(r.mul(r)).toVar(),s=eG(i.mul(i)).toVar();return eP(15.5).mul(s.mul(i)).sub(tP(40.14,s.mul(r))).add(tP(31.96,s).sub(tP(6.868,i.mul(r))).add(tP(.4298,i).add(tP(.1191,r).sub(.00232))))}),ds=eA(e=>{let[t,r]=e,i=eG(t).toVar(),s=eY(eG(.856627153315983,.137318972929847,.11189821299995),eG(.0951212405381588,.761241990602591,.0767994186031903),eG(.0482516061458583,.101439036467562,.811302368396859)),n=eY(eG(1.1271005818144368,-.1413297634984383,-.14132976349843826),eG(-.11060664309660323,1.157823702216272,-.11060664309660294),eG(-.016493938717834573,-.016493938717834257,1.2519364065950405)),a=eP(-12.47393),o=eP(4.026069);return i.mulAssign(r),i.assign(dr.mul(i)),i.assign(s.mul(i)),i.assign(rE(i,1e-10)),i.assign(rt(i)),i.assign(i.sub(a).div(o.sub(a))),i.assign(rk(i,0,1)),i.assign(di(i)),i.assign(n.mul(i)),i.assign(rU(rE(eG(0),i),eG(2.2))),i.assign(dt.mul(i)),i.assign(rk(i,0,1)),i}).setLayout({name:"agxToneMapping",type:"vec3",inputs:[{name:"color",type:"vec3"},{name:"exposure",type:"float"}]}),dn=eA(e=>{let[t,r]=e,i=eP(.76),s=eP(.15),n=rA((t=t.mul(r)).r,rA(t.g,t.b)),a=rX(n.lessThan(.08),n.sub(tP(6.25,n.mul(n))),.04);t.subAssign(a);let o=rE(t.r,rE(t.g,t.b));ew(o.lessThan(i),()=>t);let l=tB(1,i),u=tB(1,l.mul(l).div(o.add(l.sub(i))));t.mulAssign(u.div(o));let d=tB(1,tF(1,s.mul(o.sub(u)).add(1)));return rG(t,eG(u),d)}).setLayout({name:"neutralToneMapping",type:"vec3",inputs:[{name:"color",type:"vec3"},{name:"exposure",type:"float"}]});class da extends F{static get type(){return"CodeNode"}isGlobal(){return!0}setIncludes(e){return this.includes=e,this}getIncludes(){return this.includes}generate(e){for(let t of this.getIncludes(e))t.build(e);let t=e.getCodeFromNode(this,this.getNodeType(e));return t.code=this.code,t.code}serialize(e){super.serialize(e),e.code=this.code,e.language=this.language}deserialize(e){super.deserialize(e),this.code=e.code,this.language=e.language}constructor(e="",t=[],r=""){super("code"),this.isCodeNode=!0,this.code=e,this.includes=t,this.language=r}}class dl extends da{static get type(){return"FunctionNode"}getNodeType(e){return this.getNodeFunction(e).type}getInputs(e){return this.getNodeFunction(e).inputs}getNodeFunction(e){let t=e.getDataFromNode(this),r=t.nodeFunction;return void 0===r&&(t.nodeFunction=r=e.parser.parseFunction(this.code)),r}generate(e,t){super.generate(e);let r=this.getNodeFunction(e),i=r.name,s=r.type,n=e.getCodeFromNode(this,s);""!==i&&(n.name=i);let a=e.getPropertyName(n);return(n.code=this.getNodeFunction(e).getCode(a)+"\n","property"===t)?a:e.format("".concat(a,"()"),s,t)}constructor(e="",t=[],r=""){super(e,t,r)}}class du extends F{static get type(){return"ScriptableValueNode"}get isScriptableOutputNode(){return null!==this.outputType}set value(e){this._value!==e&&(this._cache&&"URL"===this.inputType&&this.value.value instanceof ArrayBuffer&&(URL.revokeObjectURL(this._cache),this._cache=null),this._value=e,this.events.dispatchEvent({type:"change"}),this.refresh())}get value(){return this._value}refresh(){this.events.dispatchEvent({type:"refresh"})}getValue(){let e=this.value;if(e&&null===this._cache&&"URL"===this.inputType&&e.value instanceof ArrayBuffer)this._cache=URL.createObjectURL(new Blob([e.value]));else if(e&&null!==e.value&&void 0!==e.value&&(("URL"===this.inputType||"String"===this.inputType)&&"string"==typeof e.value||"Number"===this.inputType&&"number"==typeof e.value||"Vector2"===this.inputType&&e.value.isVector2||"Vector3"===this.inputType&&e.value.isVector3||"Vector4"===this.inputType&&e.value.isVector4||"Color"===this.inputType&&e.value.isColor||"Matrix3"===this.inputType&&e.value.isMatrix3||"Matrix4"===this.inputType&&e.value.isMatrix4))return e.value;return this._cache||e}getNodeType(e){return this.value&&this.value.isNode?this.value.getNodeType(e):"float"}setup(){return this.value&&this.value.isNode?this.value:eP()}serialize(e){super.serialize(e),null!==this.value?"ArrayBuffer"===this.inputType?e.value=S(this.value):e.value=this.value?this.value.toJSON(e.meta).uuid:null:e.value=null,e.inputType=this.inputType,e.outputType=this.outputType}deserialize(e){super.deserialize(e);let t=null;null!==e.value&&(t="ArrayBuffer"===e.inputType?R(e.value):"Texture"===e.inputType?e.meta.textures[e.value]:e.meta.nodes[e.value]||null),this.value=t,this.inputType=e.inputType,this.outputType=e.outputType}constructor(e=null){super(),this._value=e,this._cache=null,this.inputType=null,this.outputType=null,this.events=new u.Qev,this.isScriptableValueNode=!0}}let dd=eN(du).setParameterLength(1);class dh extends Map{get(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;for(var r=arguments.length,i=Array(r>2?r-2:0),s=2;s<r;s++)i[s-2]=arguments[s];if(this.has(e))return super.get(e);if(null!==t){let r=t(...i);return this.set(e,r),r}}}class dc{get parameters(){return this.scriptableNode.parameters}get layout(){return this.scriptableNode.getLayout()}getInputLayout(e){return this.scriptableNode.getInputLayout(e)}get(e){let t=this.parameters[e];return t?t.getValue():null}constructor(e){this.scriptableNode=e}}let dp=new dh;class dg extends F{static get type(){return"ScriptableNode"}get source(){return this.codeNode?this.codeNode.code:""}setLocal(e,t){return this._local.set(e,t)}getLocal(e){return this._local.get(e)}onRefresh(){this._refresh()}getInputLayout(e){for(let t of this.getLayout())if(t.inputType&&(t.id===e||t.name===e))return t}getOutputLayout(e){for(let t of this.getLayout())if(t.outputType&&(t.id===e||t.name===e))return t}setOutput(e,t){let r=this._outputs;return void 0===r[e]?r[e]=dd(t):r[e].value=t,this}getOutput(e){return this._outputs[e]}getParameter(e){return this.parameters[e]}setParameter(e,t){let r=this.parameters;return t&&t.isScriptableNode?(this.deleteParameter(e),r[e]=t,r[e].getDefaultOutput().events.addEventListener("refresh",this.onRefresh)):t&&t.isScriptableValueNode?(this.deleteParameter(e),r[e]=t,r[e].events.addEventListener("refresh",this.onRefresh)):void 0===r[e]?(r[e]=dd(t),r[e].events.addEventListener("refresh",this.onRefresh)):r[e].value=t,this}getValue(){return this.getDefaultOutput().getValue()}deleteParameter(e){let t=this.parameters[e];return t&&(t.isScriptableNode&&(t=t.getDefaultOutput()),t.events.removeEventListener("refresh",this.onRefresh)),this}clearParameters(){for(let e of Object.keys(this.parameters))this.deleteParameter(e);return this.needsUpdate=!0,this}call(e){for(var t=arguments.length,r=Array(t>1?t-1:0),i=1;i<t;i++)r[i-1]=arguments[i];let s=this.getObject()[e];if("function"==typeof s)return s(...r)}async callAsync(e){for(var t=arguments.length,r=Array(t>1?t-1:0),i=1;i<t;i++)r[i-1]=arguments[i];let s=this.getObject()[e];if("function"==typeof s)return"AsyncFunction"===s.constructor.name?await s(...r):s(...r)}getNodeType(e){return this.getDefaultOutputNode().getNodeType(e)}refresh(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;null!==e?this.getOutput(e).refresh():this._refresh()}getObject(){if(this.needsUpdate&&this.dispose(),null!==this._object)return this._object;let e=new dc(this),t=dp.get("THREE"),r=dp.get("TSL"),i=this.getMethod(),s=[e,this._local,dp,()=>this.refresh(),(e,t)=>this.setOutput(e,t),t,r];this._object=i(...s);let n=this._object.layout;if(n&&(!1===n.cache&&this._local.clear(),this._output.outputType=n.outputType||null,Array.isArray(n.elements)))for(let e of n.elements){let t=e.id||e.name;e.inputType&&(void 0===this.getParameter(t)&&this.setParameter(t,null),this.getParameter(t).inputType=e.inputType),e.outputType&&(void 0===this.getOutput(t)&&this.setOutput(t,null),this.getOutput(t).outputType=e.outputType)}return this._object}deserialize(e){for(let t in super.deserialize(e),this.parameters){let e=this.parameters[t];e.isScriptableNode&&(e=e.getDefaultOutput()),e.events.addEventListener("refresh",this.onRefresh)}}getLayout(){return this.getObject().layout}getDefaultOutputNode(){let e=this.getDefaultOutput().value;return e&&e.isNode?e:eP()}getDefaultOutput(){return this._exec()._output}getMethod(){if(this.needsUpdate&&this.dispose(),null!==this._method)return this._method;let e="layout, init, main, dispose",t="var "+e+"; var output = {};\n"+this.codeNode.code+"\nreturn { ...output, "+e+" };";return this._method=Function("parameters","local","global","refresh","setOutput","THREE","TSL",t),this._method}dispose(){null!==this._method&&(this._object&&"function"==typeof this._object.dispose&&this._object.dispose(),this._method=null,this._object=null,this._source=null,this._value=null,this._needsOutputUpdate=!0,this._output.value=null,this._outputs={})}setup(){return this.getDefaultOutputNode()}getCacheKey(e){let t=[p(this.source),this.getDefaultOutputNode().getCacheKey(e)];for(let r in this.parameters)t.push(this.parameters[r].getCacheKey(e));return g(t)}set needsUpdate(e){!0===e&&this.dispose()}get needsUpdate(){return this.source!==this._source}_exec(){return null===this.codeNode||(!0===this._needsOutputUpdate&&(this._value=this.call("main"),this._needsOutputUpdate=!1),this._output.value=this._value),this}_refresh(){this.needsUpdate=!0,this._exec(),this._output.refresh()}constructor(e=null,t={}){super(),this.codeNode=e,this.parameters=t,this._local=new dh,this._output=dd(null),this._outputs={},this._source=this.source,this._method=null,this._object=null,this._value=null,this._needsOutputUpdate=!0,this.onRefresh=this.onRefresh.bind(this),this.isScriptableNode=!0}}function dm(e){let t,r=e.context.getViewZ;return void 0!==r&&(t=r(this)),(t||i6.z).negate()}let df=eA((e,t)=>{let[r,i]=e;return rH(r,i,dm(t))}),dy=eA((e,t)=>{let[r]=e,i=dm(t);return r.mul(r,i,i).negate().exp().oneMinus()}),dx=eA(e=>{let[t,r]=e;return eW(r.toFloat().mix(tl.rgb,t.toVec3()),tl.a)}),db=null,dT=null;class dv extends F{static get type(){return"RangeNode"}getVectorLength(e){let t=e.getTypeLength(v(this.minNode.value)),r=e.getTypeLength(v(this.maxNode.value));return t>r?t:r}getNodeType(e){return e.object.count>1?e.getTypeFromLength(this.getVectorLength(e)):"float"}setup(e){let t=e.object,r=null;if(t.count>1){let i=this.minNode.value,s=this.maxNode.value,n=e.getTypeLength(v(i)),a=e.getTypeLength(v(s));db=db||new u.IUQ,dT=dT||new u.IUQ,db.setScalar(0),dT.setScalar(0),1===n?db.setScalar(i):i.isColor?db.set(i.r,i.g,i.b,1):db.set(i.x,i.y,i.z||0,i.w||0),1===a?dT.setScalar(s):s.isColor?dT.set(s.r,s.g,s.b,1):dT.set(s.x,s.y,s.z||0,s.w||0);let o=4*t.count,l=new Float32Array(o);for(let e=0;e<o;e++){let t=e%4,r=db.getComponent(t),i=dT.getComponent(t);l[e]=u.cj9.lerp(r,i,Math.random())}let d=this.getNodeType(e);if(t.count<=4096)r=iP(l,"vec4",t.count).element(nT).convert(d);else{let t=new u.uWO(l,4);e.geometry.setAttribute("__range"+this.id,t),r=io(t).convert(d)}}else r=eP(0);return r}constructor(e=eP(),t=eP()){super(),this.minNode=e,this.maxNode=t}}class d_ extends F{static get type(){return"ComputeBuiltinNode"}getHash(e){return this.getBuiltinName(e)}getNodeType(){return this.nodeType}setBuiltinName(e){return this._builtinName=e,this}getBuiltinName(){return this._builtinName}hasBuiltin(e){return e.hasBuiltin(this._builtinName)}generate(e,t){let r=this.getBuiltinName(e),i=this.getNodeType(e);return"compute"===e.shaderStage?e.format(r,i,t):(console.warn("ComputeBuiltinNode: Compute built-in value ".concat(r," can not be accessed in the ").concat(e.shaderStage," stage")),e.generateConst(i))}serialize(e){super.serialize(e),e.global=this.global,e._builtinName=this._builtinName}deserialize(e){super.deserialize(e),this.global=e.global,this._builtinName=e._builtinName}constructor(e,t){super(t),this._builtinName=e}}let dN=(e,t)=>eT(new d_(e,t));class dS extends F{generate(e){let{scope:t}=this,{renderer:r}=e;!0===r.backend.isWebGLBackend?e.addFlowCode("	// ".concat(t,"Barrier \n")):e.addLineFlowCode("".concat(t,"Barrier()"),this)}constructor(e){super(),this.scope=e}}eN(dS);class dR extends U{generate(e,t){let r,i=e.context.assign;if(r=super.generate(e),!0!==i){let i=this.getNodeType(e);r=e.format(r,i,t)}return r}constructor(e,t){super(e,t),this.isWorkgroupInfoElementNode=!0}}class dA extends F{static get type(){return"AtomicFunctionNode"}getInputType(e){return this.pointerNode.getNodeType(e)}getNodeType(e){return this.getInputType(e)}generate(e){let t=e.getNodeProperties(this),r=t.parents,i=this.method,s=this.getNodeType(e),n=this.getInputType(e),a=this.pointerNode,o=this.valueNode,l=[];l.push("&".concat(a.build(e,n))),null!==o&&l.push(o.build(e,n));let u="".concat(e.getMethod(i,s),"( ").concat(l.join(", ")," )");if(1!==r.length||!0!==r[0].isStackNode)return void 0===t.constNode&&(t.constNode=ix(u,s).toConst()),t.constNode.build(e);e.addLineFlowCode(u,this)}constructor(e,t,r){super("uint"),this.method=e,this.pointerNode=t,this.valueNode=r,this.parents=!0}}dA.ATOMIC_LOAD="atomicLoad",dA.ATOMIC_STORE="atomicStore",dA.ATOMIC_ADD="atomicAdd",dA.ATOMIC_SUB="atomicSub",dA.ATOMIC_MAX="atomicMax",dA.ATOMIC_MIN="atomicMin",dA.ATOMIC_AND="atomicAnd",dA.ATOMIC_OR="atomicOr",dA.ATOMIC_XOR="atomicXor";let dE=eN(dA);function dC(e){let t=(n=n||new WeakMap).get(e);return void 0===t&&n.set(e,t={}),t}function dw(e){let t=dC(e);return t.shadowMatrix||(t.shadowMatrix=tN("mat4").setGroup(tT).onRenderUpdate(()=>(!0!==e.castShadow&&e.shadow.updateMatrices(e),e.shadow.matrix)))}function dM(e){let t=dC(e);return t.position||(t.position=tN(new u.Pq0).setGroup(tT).onRenderUpdate((t,r)=>r.value.setFromMatrixPosition(e.matrixWorld)))}function dB(e){let t=dC(e);return t.viewPosition||(t.viewPosition=tN(new u.Pq0).setGroup(tT).onRenderUpdate((t,r)=>{let{camera:i}=t;r.value=r.value||new u.Pq0,r.value.setFromMatrixPosition(e.matrixWorld),r.value.applyMatrix4(i.matrixWorldInverse)}))}let dP=e=>iz.transformDirection(dM(e).sub(function(e){let t=dC(e);return t.targetPosition||(t.targetPosition=tN(new u.Pq0).setGroup(tT).onRenderUpdate((t,r)=>r.value.setFromMatrixPosition(e.target.matrixWorld)))}(e))),dF=e=>e.sort((e,t)=>e.id-t.id),dU=(e,t)=>{for(let r of t)if(r.isAnalyticLightNode&&r.light.id===e)return r;return null},dL=new WeakMap;class dI extends F{static get type(){return"LightsNode"}customCacheKey(){let e=[],t=this._lights;for(let r=0;r<t.length;r++){let i=t[r];if(e.push(i.id),!0===i.isSpotLight){let t=null!==i.map?i.map.id:-1;e.push(t)}}return g(e)}getHash(e){if(null===this._lightNodesHash){null===this._lightNodes&&this.setupLightsNode(e);let t=[];for(let e of this._lightNodes)t.push(e.getSelf().getHash());this._lightNodesHash="lights-"+t.join(",")}return this._lightNodesHash}analyze(e){for(let t of e.getDataFromNode(this).nodes)t.build(e)}setupLightsNode(e){let t=[],r=this._lightNodes,i=dF(this._lights),s=e.renderer.library;for(let e of i)if(e.isNode)t.push(eT(e));else{let i=null;if(null!==r&&(i=dU(e.id,r)),null===i){let r=s.getLightNodeClass(e.constructor);if(null===r){console.warn("LightsNode.setupNodeLights: Light node not found for ".concat(e.constructor.name));continue}let i=null;dL.has(e)?i=dL.get(e):(i=eT(new r(e)),dL.set(e,i)),t.push(i)}}this._lightNodes=t}setupDirectLight(e,t,r){let{lightingModel:i,reflectedLight:s}=e.context;i.direct({...r,lightNode:t,reflectedLight:s},e)}setupDirectRectAreaLight(e,t,r){let{lightingModel:i,reflectedLight:s}=e.context;i.directRectArea({...r,lightNode:t,reflectedLight:s},e)}setupLights(e,t){for(let r of t)r.build(e)}getLightNodes(e){return null===this._lightNodes&&this.setupLightsNode(e),this._lightNodes}setup(e){let t=e.lightsNode;e.lightsNode=this;let r=this.outgoingLightNode,i=e.context,s=i.lightingModel,n=e.getDataFromNode(this);if(s){let{totalDiffuseNode:t,totalSpecularNode:a}=this;i.outgoingLight=r,n.nodes=e.addStack().nodes,s.start(e);let{backdrop:o,backdropAlpha:l}=i,{directDiffuse:u,directSpecular:d,indirectDiffuse:h,indirectSpecular:c}=i.reflectedLight,p=u.add(h);null!==o&&(p=null!==l?eG(l.mix(p,o)):eG(o),i.material.transparent=!0),t.assign(p),a.assign(d.add(c)),r.assign(t.add(a)),s.finish(e),r=r.bypass(e.removeStack())}else n.nodes=[];return e.lightsNode=t,r}setLights(e){return this._lights=e,this._lightNodes=null,this._lightNodesHash=null,this}getLights(){return this._lights}get hasLights(){return this._lights.length>0}constructor(){super("vec3"),this.totalDiffuseNode=eG().toVar(),this.totalSpecularNode=eG().toVar(),this.outgoingLightNode=eG().toVar(),this._lights=[],this._lightNodes=null,this._lightNodesHash=null,this.global=!0}}class dD extends F{static get type(){return"ShadowBaseNode"}setupShadowPosition(e){let{context:t,material:r}=e;dO.assign(r.receivedShadowPositionNode||t.shadowPositionWorld||i3)}dispose(){this.updateBeforeType=E.NONE}constructor(e){super(),this.light=e,this.updateBeforeType=E.RENDER,this.isShadowBaseNode=!0}}let dO=e$("vec3","shadowPositionWorld"),dV=new WeakMap,dG=eA(e=>{let{depthTexture:t,shadowCoord:r,depthLayer:i}=e,s=iw(t,r.xy).label("t_basic");return t.isDepthArrayTexture&&(s=s.depth(i)),s.compare(r.z)}),dk=eA(e=>{let{depthTexture:t,shadowCoord:r,shadow:i,depthLayer:s}=e,n=(e,r)=>{let i=iw(t,e);return t.isDepthArrayTexture&&(i=i.depth(s)),i.compare(r)},a=s_("mapSize","vec2",i).setGroup(tT),o=s_("radius","float",i).setGroup(tT),l=eI(1).div(a),u=l.x.negate().mul(o),d=l.y.negate().mul(o),h=l.x.mul(o),c=l.y.mul(o),p=u.div(2),g=d.div(2),m=h.div(2),f=c.div(2);return tM(n(r.xy.add(eI(u,d)),r.z),n(r.xy.add(eI(0,d)),r.z),n(r.xy.add(eI(h,d)),r.z),n(r.xy.add(eI(p,g)),r.z),n(r.xy.add(eI(0,g)),r.z),n(r.xy.add(eI(m,g)),r.z),n(r.xy.add(eI(u,0)),r.z),n(r.xy.add(eI(p,0)),r.z),n(r.xy,r.z),n(r.xy.add(eI(m,0)),r.z),n(r.xy.add(eI(h,0)),r.z),n(r.xy.add(eI(p,f)),r.z),n(r.xy.add(eI(0,f)),r.z),n(r.xy.add(eI(m,f)),r.z),n(r.xy.add(eI(u,c)),r.z),n(r.xy.add(eI(0,c)),r.z),n(r.xy.add(eI(h,c)),r.z)).mul(1/17)}),dz=eA(e=>{let{depthTexture:t,shadowCoord:r,shadow:i,depthLayer:s}=e,n=(e,r)=>{let i=iw(t,e);return t.isDepthArrayTexture&&(i=i.depth(s)),i.compare(r)},a=s_("mapSize","vec2",i).setGroup(tT),o=eI(1).div(a),l=o.x,u=o.y,d=r.xy,h=ro(d.mul(a).add(.5));return d.subAssign(h.mul(o)),tM(n(d,r.z),n(d.add(eI(l,0)),r.z),n(d.add(eI(0,u)),r.z),n(d.add(o),r.z),rG(n(d.add(eI(l.negate(),0)),r.z),n(d.add(eI(l.mul(2),0)),r.z),h.x),rG(n(d.add(eI(l.negate(),u)),r.z),n(d.add(eI(l.mul(2),u)),r.z),h.x),rG(n(d.add(eI(0,u.negate())),r.z),n(d.add(eI(0,u.mul(2))),r.z),h.y),rG(n(d.add(eI(l,u.negate())),r.z),n(d.add(eI(l,u.mul(2))),r.z),h.y),rG(rG(n(d.add(eI(l.negate(),u.negate())),r.z),n(d.add(eI(l.mul(2),u.negate())),r.z),h.x),rG(n(d.add(eI(l.negate(),u.mul(2))),r.z),n(d.add(eI(l.mul(2),u.mul(2))),r.z),h.x),h.y)).mul(1/9)}),dH=eA(e=>{let{depthTexture:t,shadowCoord:r,depthLayer:i}=e,s=eP(1).toVar(),n=iw(t).sample(r.xy);(t.isDepthArrayTexture||t.isDataArrayTexture)&&(n=n.depth(i)),n=n.rg;let a=rC(r.z,n.x);return ew(a.notEqual(eP(1)),()=>{let e=r.z.sub(n.x),t=rE(0,n.y.mul(n.y)),i=t.div(t.add(e.mul(e)));i=rk(tB(i,.3).div(.95-.3)),s.assign(rk(rE(a,i)))}),s}),dW=eA(e=>{let[t,r,i]=e,s=i3.sub(t).length();return(s=s.sub(r).div(i.sub(r))).saturate()}),dq=e=>{let t=e.shadow.camera,r=s_("near","float",t).setGroup(tT),i=s_("far","float",t).setGroup(tT);return dW(ij(e),r,i)},dj=e=>{let t=dV.get(e);if(void 0===t){let r=e.isPointLight?dq(e):null;(t=new ah).colorNode=eW(0,0,0,1),t.depthNode=r,t.isShadowPassMaterial=!0,t.name="ShadowMaterial",t.fog=!1,dV.set(e,t)}return t},dX=new lP,dQ=[],dY=(e,t,r,i)=>{dQ[0]=e,dQ[1]=t;let s=dX.get(dQ);return(void 0===s||s.shadowType!==r||s.useVelocity!==i)&&((s=function(s,n,a,o,l,d){for(var h=arguments.length,c=Array(h>6?h-6:0),p=6;p<h;p++)c[p-6]=arguments[p];(!0===s.castShadow||s.receiveShadow&&r===u.RyA)&&(i&&(N(s).useVelocity=!0),s.onBeforeShadow(e,s,a,t.camera,o,n.overrideMaterial,d),e.renderObject(s,n,a,o,l,d,...c),s.onAfterShadow(e,s,a,t.camera,o,n.overrideMaterial,d))}).shadowType=r,s.useVelocity=i,dX.set(dQ,s)),dQ[0]=null,dQ[1]=null,s},dK=eA(e=>{let{samples:t,radius:r,size:i,shadowPass:s,depthLayer:n}=e,a=eP(0).toVar("meanVertical"),o=eP(0).toVar("squareMeanVertical"),l=t.lessThanEqual(eP(1)).select(eP(0),eP(2).div(t.sub(1))),u=t.lessThanEqual(eP(1)).select(eP(0),eP(-1));nU({start:eF(0),end:eF(t),type:"int",condition:"<"},e=>{let{i:t}=e,d=u.add(eP(t).mul(l)),h=s.sample(tM(nY.xy,eI(0,d).mul(r)).div(i));(s.value.isDepthArrayTexture||s.value.isDataArrayTexture)&&(h=h.depth(n)),h=h.x,a.addAssign(h),o.addAssign(h.mul(h))}),a.divAssign(t),o.divAssign(t);let d=rr(o.sub(a.mul(a)));return eI(a,d)}),dZ=eA(e=>{let{samples:t,radius:r,size:i,shadowPass:s,depthLayer:n}=e,a=eP(0).toVar("meanHorizontal"),o=eP(0).toVar("squareMeanHorizontal"),l=t.lessThanEqual(eP(1)).select(eP(0),eP(2).div(t.sub(1))),u=t.lessThanEqual(eP(1)).select(eP(0),eP(-1));nU({start:eF(0),end:eF(t),type:"int",condition:"<"},e=>{let{i:t}=e,d=u.add(eP(t).mul(l)),h=s.sample(tM(nY.xy,eI(d,0).mul(r)).div(i));(s.value.isDepthArrayTexture||s.value.isDataArrayTexture)&&(h=h.depth(n)),a.addAssign(h.x),o.addAssign(tM(h.y.mul(h.y),h.x.mul(h.x)))}),a.divAssign(t),o.divAssign(t);let d=rr(o.sub(a.mul(a)));return eI(a,d)}),d$=[dG,dk,dz,dH],dJ=new uL;class d0 extends dD{static get type(){return"ShadowNode"}setupShadowFilter(e,t){let{filterFn:r,depthTexture:i,shadowCoord:s,shadow:n,depthLayer:a}=t,o=s.x.greaterThanEqual(0).and(s.x.lessThanEqual(1)).and(s.y.greaterThanEqual(0)).and(s.y.lessThanEqual(1)).and(s.z.lessThanEqual(1)),l=r({depthTexture:i,shadowCoord:s,shadow:n,depthLayer:a});return o.select(l,eP(1))}setupShadowCoord(e,t){let r,{shadow:i}=this,{renderer:s}=e,n=s_("bias","float",i).setGroup(tT),a=t;if(i.camera.isOrthographicCamera||!0!==s.logarithmicDepthBuffer)r=(a=a.xyz.div(a.w)).z,s.coordinateSystem===u.i7u&&(r=r.mul(2).sub(1));else{let e=a.w;a=a.xy.div(e);let t=s_("near","float",i.camera).setGroup(tT),s=s_("far","float",i.camera).setGroup(tT);r=n9(e.negate(),t,s)}return eG(a.x,a.y.oneMinus(),r.add(n))}getShadowFilterFn(e){return d$[e]}setupRenderTarget(e,t){let r=new u.VCu(e.mapSize.width,e.mapSize.height);r.name="ShadowDepthTexture",r.compareFunction=u.vim;let i=t.createRenderTarget(e.mapSize.width,e.mapSize.height);return i.texture.name="ShadowMap",i.texture.type=e.mapType,i.depthTexture=r,{shadowMap:i,depthTexture:r}}setupShadow(e){let{renderer:t}=e,{light:r,shadow:i}=this,s=t.shadowMap.type,{depthTexture:n,shadowMap:a}=this.setupRenderTarget(i,e);if(i.camera.updateProjectionMatrix(),s===u.RyA){n.compareFunction=null,a.isRenderTargetArray?(a._vsmShadowMapVertical||(a._vsmShadowMapVertical=e.createRenderTargetArray(i.mapSize.width,i.mapSize.height,a.depth,{format:u.paN,type:u.ix0,depthBuffer:!1}),a._vsmShadowMapVertical.texture.name="VSMVertical"),this.vsmShadowMapVertical=a._vsmShadowMapVertical,a._vsmShadowMapHorizontal||(a._vsmShadowMapHorizontal=e.createRenderTargetArray(i.mapSize.width,i.mapSize.height,a.depth,{format:u.paN,type:u.ix0,depthBuffer:!1}),a._vsmShadowMapHorizontal.texture.name="VSMHorizontal"),this.vsmShadowMapHorizontal=a._vsmShadowMapHorizontal):(this.vsmShadowMapVertical=e.createRenderTarget(i.mapSize.width,i.mapSize.height,{format:u.paN,type:u.ix0,depthBuffer:!1}),this.vsmShadowMapHorizontal=e.createRenderTarget(i.mapSize.width,i.mapSize.height,{format:u.paN,type:u.ix0,depthBuffer:!1}));let t=iw(n);n.isDepthArrayTexture&&(t=t.depth(this.depthLayer));let r=iw(this.vsmShadowMapVertical.texture);n.isDepthArrayTexture&&(r=r.depth(this.depthLayer));let s=s_("blurSamples","float",i).setGroup(tT),o=s_("radius","float",i).setGroup(tT),l=s_("mapSize","vec2",i).setGroup(tT),d=this.vsmMaterialVertical||(this.vsmMaterialVertical=new ah);d.fragmentNode=dK({samples:s,radius:o,size:l,shadowPass:t,depthLayer:this.depthLayer}).context(e.getSharedContext()),d.name="VSMVertical",(d=this.vsmMaterialHorizontal||(this.vsmMaterialHorizontal=new ah)).fragmentNode=dZ({samples:s,radius:o,size:l,shadowPass:r,depthLayer:this.depthLayer}).context(e.getSharedContext()),d.name="VSMHorizontal"}let o=s_("intensity","float",i).setGroup(tT),l=s_("normalBias","float",i).setGroup(tT),d=dw(r).mul(dO.add(sn.mul(l))),h=this.setupShadowCoord(e,d),c=i.filterNode||this.getShadowFilterFn(t.shadowMap.type)||null;if(null===c)throw Error("THREE.WebGPURenderer: Shadow map type not supported yet.");let p=s===u.RyA?this.vsmShadowMapHorizontal.texture:n,g=this.setupShadowFilter(e,{filterFn:c,shadowTexture:a.texture,depthTexture:p,shadowCoord:h,shadow:i,depthLayer:this.depthLayer}),m=iw(a.texture,h);n.isDepthArrayTexture&&(m=m.depth(this.depthLayer));let f=rG(1,g.rgb.mix(m,1),o.mul(m.a)).toVar();return this.shadowMap=a,this.shadow.map=a,f}setup(e){if(!1!==e.renderer.shadowMap.enabled)return eA(()=>{let t=this._node;return this.setupShadowPosition(e),null===t&&(this._node=t=this.setupShadow(e)),e.material.shadowNode&&console.warn('THREE.NodeMaterial: ".shadowNode" is deprecated. Use ".castShadowNode" instead.'),e.material.receivedShadowNode&&(t=e.material.receivedShadowNode(t)),t})()}renderShadow(e){let{shadow:t,shadowMap:r,light:i}=this,{renderer:s,scene:n}=e;t.updateMatrices(i),r.setSize(t.mapSize.width,t.mapSize.height,r.depth),s.render(n,t.camera)}updateShadow(e){var t,r,i,s,n,o,l,d,h,c,p,g,m,f;let{shadowMap:y,light:x,shadow:b}=this,{renderer:T,scene:v,camera:_}=e,N=T.shadowMap.type,S=y.depthTexture.version;this._depthVersionCached=S;let R=b.camera.layers.mask;(0xfffffffe&b.camera.layers.mask)==0&&(b.camera.layers.mask=_.layers.mask);let A=T.getRenderObjectFunction(),E=T.getMRT(),C=!!E&&E.has("velocity");t=T,r=v,i=a,l=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return t.background=e.background,t.backgroundNode=e.backgroundNode,t.overrideMaterial=e.overrideMaterial,t}(o=r,l=(n=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return t.toneMapping=e.toneMapping,t.toneMappingExposure=e.toneMappingExposure,t.outputColorSpace=e.outputColorSpace,t.renderTarget=e.getRenderTarget(),t.activeCubeFace=e.getActiveCubeFace(),t.activeMipmapLevel=e.getActiveMipmapLevel(),t.renderObjectFunction=e.getRenderObjectFunction(),t.pixelRatio=e.getPixelRatio(),t.mrt=e.getMRT(),t.clearColor=e.getClearColor(t.clearColor||new u.Q1f),t.clearAlpha=e.getClearAlpha(),t.autoClear=e.autoClear,t.scissorTest=e.getScissorTest(),t}(s=t,n=i),s.setMRT(null),s.setRenderObjectFunction(null),s.setClearColor(0,1),s.autoClear=!0,i=n)),o.background=null,o.backgroundNode=null,o.overrideMaterial=null,a=i=l,v.overrideMaterial=dj(x),T.setRenderObjectFunction(dY(T,b,N,C)),T.setClearColor(0,0),T.setRenderTarget(y),this.renderShadow(e),T.setRenderObjectFunction(A),!0!==x.isPointLight&&N===u.RyA&&this.vsmPass(T),b.camera.layers.mask=R,d=T,h=v,c=a,(p=d).toneMapping=(g=c).toneMapping,p.toneMappingExposure=g.toneMappingExposure,p.outputColorSpace=g.outputColorSpace,p.setRenderTarget(g.renderTarget,g.activeCubeFace,g.activeMipmapLevel),p.setRenderObjectFunction(g.renderObjectFunction),p.setPixelRatio(g.pixelRatio),p.setMRT(g.mrt),p.setClearColor(g.clearColor,g.clearAlpha),p.autoClear=g.autoClear,p.setScissorTest(g.scissorTest),(m=h).background=(f=c).background,m.backgroundNode=f.backgroundNode,m.overrideMaterial=f.overrideMaterial}vsmPass(e){let{shadow:t}=this,r=this.shadowMap.depth;this.vsmShadowMapVertical.setSize(t.mapSize.width,t.mapSize.height,r),this.vsmShadowMapHorizontal.setSize(t.mapSize.width,t.mapSize.height,r),e.setRenderTarget(this.vsmShadowMapVertical),dJ.material=this.vsmMaterialVertical,dJ.render(e),e.setRenderTarget(this.vsmShadowMapHorizontal),dJ.material=this.vsmMaterialHorizontal,dJ.render(e)}dispose(){this.shadowMap.dispose(),this.shadowMap=null,null!==this.vsmShadowMapVertical&&(this.vsmShadowMapVertical.dispose(),this.vsmShadowMapVertical=null,this.vsmMaterialVertical.dispose(),this.vsmMaterialVertical=null),null!==this.vsmShadowMapHorizontal&&(this.vsmShadowMapHorizontal.dispose(),this.vsmShadowMapHorizontal=null,this.vsmMaterialHorizontal.dispose(),this.vsmMaterialHorizontal=null),super.dispose()}updateBefore(e){let{shadow:t}=this,r=t.needsUpdate||t.autoUpdate;r&&(this._cameraFrameId[e.camera]===e.frameId&&(r=!1),this._cameraFrameId[e.camera]=e.frameId),r&&(this.updateShadow(e),this.shadowMap.depthTexture.version===this._depthVersionCached&&(t.needsUpdate=!1))}constructor(e,t=null){super(e),this.shadow=t||e.shadow,this.shadowMap=null,this.vsmShadowMapVertical=null,this.vsmShadowMapHorizontal=null,this.vsmMaterialVertical=null,this.vsmMaterialHorizontal=null,this._node=null,this._cameraFrameId=new WeakMap,this.isShadowNode=!0,this.depthLayer=0}}let d1=(e,t)=>eT(new d0(e,t)),d2=new u.Q1f,d3=eA(e=>{let[t,r]=e,i=t.toVar(),s=rg(i),n=tF(1,rE(s.x,rE(s.y,s.z)));s.mulAssign(n),i.mulAssign(n.mul(r.mul(2).oneMinus()));let a=eI(i.xy).toVar(),o=r.mul(1.5).oneMinus();return ew(s.z.greaterThanEqual(o),()=>{ew(i.z.greaterThan(0),()=>{a.x.assign(tB(4,i.x))})}).ElseIf(s.x.greaterThanEqual(o),()=>{let e=rm(i.x);a.x.assign(i.z.mul(e).add(e.mul(2)))}).ElseIf(s.y.greaterThanEqual(o),()=>{let e=rm(i.y);a.x.assign(i.x.add(e.mul(2)).add(2)),a.y.assign(i.z.mul(e).sub(2))}),eI(.125,.25).mul(a).add(eI(.375,.75)).flipY()}).setLayout({name:"cubeToUV",type:"vec2",inputs:[{name:"pos",type:"vec3"},{name:"texelSizeY",type:"float"}]}),d4=eA(e=>{let{depthTexture:t,bd3D:r,dp:i,texelSize:s}=e;return iw(t,d3(r,s.y)).compare(i)}),d6=eA(e=>{let{depthTexture:t,bd3D:r,dp:i,texelSize:s,shadow:n}=e,a=s_("radius","float",n).setGroup(tT),o=eI(-1,1).mul(a).mul(s.y);return iw(t,d3(r.add(o.xyy),s.y)).compare(i).add(iw(t,d3(r.add(o.yyy),s.y)).compare(i)).add(iw(t,d3(r.add(o.xyx),s.y)).compare(i)).add(iw(t,d3(r.add(o.yyx),s.y)).compare(i)).add(iw(t,d3(r,s.y)).compare(i)).add(iw(t,d3(r.add(o.xxy),s.y)).compare(i)).add(iw(t,d3(r.add(o.yxy),s.y)).compare(i)).add(iw(t,d3(r.add(o.xxx),s.y)).compare(i)).add(iw(t,d3(r.add(o.yxx),s.y)).compare(i)).mul(1/9)}),d8=eA(e=>{let{filterFn:t,depthTexture:r,shadowCoord:i,shadow:s}=e,n=i.xyz.toVar(),a=n.length(),o=tN("float").setGroup(tT).onRenderUpdate(()=>s.camera.near),l=tN("float").setGroup(tT).onRenderUpdate(()=>s.camera.far),u=s_("bias","float",s).setGroup(tT),d=tN(s.mapSize).setGroup(tT),h=eP(1).toVar();return ew(a.sub(l).lessThanEqual(0).and(a.sub(o).greaterThanEqual(0)),()=>{let e=a.sub(o).div(l.sub(o)).toVar();e.addAssign(u);let i=n.normalize(),c=eI(1).div(d.mul(eI(4,2)));h.assign(t({depthTexture:r,bd3D:i,dp:e,texelSize:c,shadow:s}))}),h}),d5=new u.IUQ,d9=new u.I9Y,d7=new u.I9Y;class he extends d0{static get type(){return"PointShadowNode"}getShadowFilterFn(e){return e===u.bTm?d4:d6}setupShadowCoord(e,t){return t}setupShadowFilter(e,t){let{filterFn:r,shadowTexture:i,depthTexture:s,shadowCoord:n,shadow:a}=t;return d8({filterFn:r,shadowTexture:i,depthTexture:s,shadowCoord:n,shadow:a})}renderShadow(e){let{shadow:t,shadowMap:r,light:i}=this,{renderer:s,scene:n}=e,a=t.getFrameExtents();d7.copy(t.mapSize),d7.multiply(a),r.setSize(d7.width,d7.height),d9.copy(t.mapSize);let o=s.autoClear,l=s.getClearColor(d2),u=s.getClearAlpha();s.autoClear=!1,s.setClearColor(t.clearColor,t.clearAlpha),s.clear();let d=t.getViewportCount();for(let e=0;e<d;e++){let a=t.getViewport(e),o=d9.x*a.x,l=d7.y-d9.y-d9.y*a.y;d5.set(o,l,d9.x*a.z,d9.y*a.w),r.viewport.copy(d5),t.updateMatrices(i,e),s.render(n,t.camera)}s.autoClear=o,s.setClearColor(l,u)}constructor(e,t=null){super(e,t)}}let ht=(e,t)=>eT(new he(e,t));class hr extends nk{static get type(){return"AnalyticLightNode"}customCacheKey(){return m(this.light.id,+!!this.light.castShadow)}getHash(){return this.light.uuid}getLightVector(e){return dB(this.light).sub(e.context.positionView||i6)}setupDirect(){}setupDirectRectArea(){}setupShadowNode(){return d1(this.light)}setupShadow(e){let{renderer:t}=e;if(!1===t.shadowMap.enabled)return;let r=this.shadowColorNode;if(null===r){let e,t=this.light.shadow.shadowNode;e=void 0!==t?eT(t):this.setupShadowNode(),this.shadowNode=e,this.shadowColorNode=r=this.colorNode.mul(e),this.baseColorNode=this.colorNode}this.colorNode=r}setup(e){this.colorNode=this.baseColorNode||this.colorNode,this.light.castShadow?e.object.receiveShadow&&this.setupShadow(e):null!==this.shadowNode&&(this.shadowNode.dispose(),this.shadowNode=null,this.shadowColorNode=null);let t=this.setupDirect(e),r=this.setupDirectRectArea(e);t&&e.lightsNode.setupDirectLight(e,this,t),r&&e.lightsNode.setupDirectRectAreaLight(e,this,r)}update(){let{light:e}=this;this.color.copy(e.color).multiplyScalar(e.intensity)}constructor(e=null){super(),this.light=e,this.color=new u.Q1f,this.colorNode=e&&e.colorNode||tN(this.color).setGroup(tT),this.baseColorNode=null,this.shadowNode=null,this.shadowColorNode=null,this.isAnalyticLightNode=!0,this.updateType=E.FRAME}}let hi=eA(e=>{let{lightDistance:t,cutoffDistance:r,decayExponent:i}=e,s=t.pow(i).max(.01).reciprocal();return r.greaterThan(0).select(s.mul(t.div(r).pow4().oneMinus().clamp().pow2()),s)}),hs=e=>{let{color:t,lightVector:r,cutoffDistance:i,decayExponent:s}=e,n=r.normalize(),a=hi({lightDistance:r.length(),cutoffDistance:i,decayExponent:s});return{lightDirection:n,lightColor:t.mul(a)}};class hn extends hr{static get type(){return"PointLightNode"}update(e){let{light:t}=this;super.update(e),this.cutoffDistanceNode.value=t.distance,this.decayExponentNode.value=t.decay}setupShadowNode(){return ht(this.light)}setupDirect(e){return hs({color:this.colorNode,lightVector:this.getLightVector(e),cutoffDistance:this.cutoffDistanceNode,decayExponent:this.decayExponentNode})}constructor(e=null){super(e),this.cutoffDistanceNode=tN(0).setGroup(tT),this.decayExponentNode=tN(2).setGroup(tT)}}e=>{let[t=iN()]=e,r=t.mul(2),i=r.x.floor(),s=r.y.floor();return i.add(s).mod(2).sign()},eA((e,t)=>{let[r=iN()]=e,{renderer:i,material:s}=t,n=eP(1).toVar(),a=rV(r.mul(2).sub(1));if(s.alphaToCoverage&&i.samples>1){let e=eP(a.fwidth()).toVar();n.assign(rH(e.oneMinus(),e.add(1),a).oneMinus())}else a.greaterThan(1).discard();return n});let ha=eA(e=>{let[t,r,i]=e,s=eP(i).toVar(),n=eP(r).toVar();return rX(eL(t).toVar(),n,s)}).setLayout({name:"mx_select",type:"float",inputs:[{name:"b",type:"bool"},{name:"t",type:"float"},{name:"f",type:"float"}]}),ho=eA(e=>{let[t,r]=e,i=eL(r).toVar(),s=eP(t).toVar();return rX(i,s.negate(),s)}).setLayout({name:"mx_negate_if",type:"float",inputs:[{name:"val",type:"float"},{name:"b",type:"bool"}]}),hl=eA(e=>{let[t]=e;return eF(rs(eP(t).toVar()))}).setLayout({name:"mx_floor",type:"int",inputs:[{name:"x",type:"float"}]}),hu=eA(e=>{let[t,r]=e,i=eP(t).toVar();return r.assign(hl(i)),i.sub(eP(r))}),hd=up([eA(e=>{let[t,r,i,s,n,a]=e,o=eP(a).toVar(),l=eP(n).toVar(),u=eP(s).toVar(),d=eP(i).toVar(),h=eP(r).toVar(),c=eP(t).toVar(),p=eP(tB(1,l)).toVar();return tB(1,o).mul(c.mul(p).add(h.mul(l))).add(o.mul(d.mul(p).add(u.mul(l))))}).setLayout({name:"mx_bilerp_0",type:"float",inputs:[{name:"v0",type:"float"},{name:"v1",type:"float"},{name:"v2",type:"float"},{name:"v3",type:"float"},{name:"s",type:"float"},{name:"t",type:"float"}]}),eA(e=>{let[t,r,i,s,n,a]=e,o=eP(a).toVar(),l=eP(n).toVar(),u=eG(s).toVar(),d=eG(i).toVar(),h=eG(r).toVar(),c=eG(t).toVar(),p=eP(tB(1,l)).toVar();return tB(1,o).mul(c.mul(p).add(h.mul(l))).add(o.mul(d.mul(p).add(u.mul(l))))}).setLayout({name:"mx_bilerp_1",type:"vec3",inputs:[{name:"v0",type:"vec3"},{name:"v1",type:"vec3"},{name:"v2",type:"vec3"},{name:"v3",type:"vec3"},{name:"s",type:"float"},{name:"t",type:"float"}]})]),hh=up([eA(e=>{let[t,r,i,s,n,a,o,l,u,d,h]=e,c=eP(h).toVar(),p=eP(d).toVar(),g=eP(u).toVar(),m=eP(l).toVar(),f=eP(o).toVar(),y=eP(a).toVar(),x=eP(n).toVar(),b=eP(s).toVar(),T=eP(i).toVar(),v=eP(r).toVar(),_=eP(t).toVar(),N=eP(tB(1,g)).toVar(),S=eP(tB(1,p)).toVar();return eP(tB(1,c)).toVar().mul(S.mul(_.mul(N).add(v.mul(g))).add(p.mul(T.mul(N).add(b.mul(g))))).add(c.mul(S.mul(x.mul(N).add(y.mul(g))).add(p.mul(f.mul(N).add(m.mul(g))))))}).setLayout({name:"mx_trilerp_0",type:"float",inputs:[{name:"v0",type:"float"},{name:"v1",type:"float"},{name:"v2",type:"float"},{name:"v3",type:"float"},{name:"v4",type:"float"},{name:"v5",type:"float"},{name:"v6",type:"float"},{name:"v7",type:"float"},{name:"s",type:"float"},{name:"t",type:"float"},{name:"r",type:"float"}]}),eA(e=>{let[t,r,i,s,n,a,o,l,u,d,h]=e,c=eP(h).toVar(),p=eP(d).toVar(),g=eP(u).toVar(),m=eG(l).toVar(),f=eG(o).toVar(),y=eG(a).toVar(),x=eG(n).toVar(),b=eG(s).toVar(),T=eG(i).toVar(),v=eG(r).toVar(),_=eG(t).toVar(),N=eP(tB(1,g)).toVar(),S=eP(tB(1,p)).toVar();return eP(tB(1,c)).toVar().mul(S.mul(_.mul(N).add(v.mul(g))).add(p.mul(T.mul(N).add(b.mul(g))))).add(c.mul(S.mul(x.mul(N).add(y.mul(g))).add(p.mul(f.mul(N).add(m.mul(g))))))}).setLayout({name:"mx_trilerp_1",type:"vec3",inputs:[{name:"v0",type:"vec3"},{name:"v1",type:"vec3"},{name:"v2",type:"vec3"},{name:"v3",type:"vec3"},{name:"v4",type:"vec3"},{name:"v5",type:"vec3"},{name:"v6",type:"vec3"},{name:"v7",type:"vec3"},{name:"s",type:"float"},{name:"t",type:"float"},{name:"r",type:"float"}]})]),hc=up([eA(e=>{let[t,r,i]=e,s=eP(i).toVar(),n=eP(r).toVar(),a=eU(t).toVar(),o=eU(a.bitAnd(eU(7))).toVar(),l=eP(ha(o.lessThan(eU(4)),n,s)).toVar(),u=eP(tP(2,ha(o.lessThan(eU(4)),s,n))).toVar();return ho(l,eL(o.bitAnd(eU(1)))).add(ho(u,eL(o.bitAnd(eU(2)))))}).setLayout({name:"mx_gradient_float_0",type:"float",inputs:[{name:"hash",type:"uint"},{name:"x",type:"float"},{name:"y",type:"float"}]}),eA(e=>{let[t,r,i,s]=e,n=eP(s).toVar(),a=eP(i).toVar(),o=eP(r).toVar(),l=eU(t).toVar(),u=eU(l.bitAnd(eU(15))).toVar(),d=eP(ha(u.lessThan(eU(8)),o,a)).toVar(),h=eP(ha(u.lessThan(eU(4)),a,ha(u.equal(eU(12)).or(u.equal(eU(14))),o,n))).toVar();return ho(d,eL(u.bitAnd(eU(1)))).add(ho(h,eL(u.bitAnd(eU(2)))))}).setLayout({name:"mx_gradient_float_1",type:"float",inputs:[{name:"hash",type:"uint"},{name:"x",type:"float"},{name:"y",type:"float"},{name:"z",type:"float"}]})]),hp=up([eA(e=>{let[t,r,i]=e,s=eP(i).toVar(),n=eP(r).toVar(),a=ez(t).toVar();return eG(hc(a.x,n,s),hc(a.y,n,s),hc(a.z,n,s))}).setLayout({name:"mx_gradient_vec3_0",type:"vec3",inputs:[{name:"hash",type:"uvec3"},{name:"x",type:"float"},{name:"y",type:"float"}]}),eA(e=>{let[t,r,i,s]=e,n=eP(s).toVar(),a=eP(i).toVar(),o=eP(r).toVar(),l=ez(t).toVar();return eG(hc(l.x,o,a,n),hc(l.y,o,a,n),hc(l.z,o,a,n))}).setLayout({name:"mx_gradient_vec3_1",type:"vec3",inputs:[{name:"hash",type:"uvec3"},{name:"x",type:"float"},{name:"y",type:"float"},{name:"z",type:"float"}]})]),hg=eA(e=>{let[t]=e;return tP(.6616,eP(t).toVar())}).setLayout({name:"mx_gradient_scale2d_0",type:"float",inputs:[{name:"v",type:"float"}]}),hm=eA(e=>{let[t]=e;return tP(.982,eP(t).toVar())}).setLayout({name:"mx_gradient_scale3d_0",type:"float",inputs:[{name:"v",type:"float"}]}),hf=up([hg,eA(e=>{let[t]=e;return tP(.6616,eG(t).toVar())}).setLayout({name:"mx_gradient_scale2d_1",type:"vec3",inputs:[{name:"v",type:"vec3"}]})]),hy=up([hm,eA(e=>{let[t]=e;return tP(.982,eG(t).toVar())}).setLayout({name:"mx_gradient_scale3d_1",type:"vec3",inputs:[{name:"v",type:"vec3"}]})]),hx=eA(e=>{let[t,r]=e,i=eF(r).toVar(),s=eU(t).toVar();return s.shiftLeft(i).bitOr(s.shiftRight(eF(32).sub(i)))}).setLayout({name:"mx_rotl32",type:"uint",inputs:[{name:"x",type:"uint"},{name:"k",type:"int"}]}),hb=eA(e=>{let[t,r,i]=e;t.subAssign(i),t.bitXorAssign(hx(i,eF(4))),i.addAssign(r),r.subAssign(t),r.bitXorAssign(hx(t,eF(6))),t.addAssign(i),i.subAssign(r),i.bitXorAssign(hx(r,eF(8))),r.addAssign(t),t.subAssign(i),t.bitXorAssign(hx(i,eF(16))),i.addAssign(r),r.subAssign(t),r.bitXorAssign(hx(t,eF(19))),t.addAssign(i),i.subAssign(r),i.bitXorAssign(hx(r,eF(4))),r.addAssign(t)}),hT=eA(e=>{let[t,r,i]=e,s=eU(i).toVar(),n=eU(r).toVar(),a=eU(t).toVar();return s.bitXorAssign(n),s.subAssign(hx(n,eF(14))),a.bitXorAssign(s),a.subAssign(hx(s,eF(11))),n.bitXorAssign(a),n.subAssign(hx(a,eF(25))),s.bitXorAssign(n),s.subAssign(hx(n,eF(16))),a.bitXorAssign(s),a.subAssign(hx(s,eF(4))),n.bitXorAssign(a),n.subAssign(hx(a,eF(14))),s.bitXorAssign(n),s.subAssign(hx(n,eF(24))),s}).setLayout({name:"mx_bjfinal",type:"uint",inputs:[{name:"a",type:"uint"},{name:"b",type:"uint"},{name:"c",type:"uint"}]}),hv=eA(e=>{let[t]=e;return eP(eU(t).toVar()).div(eP(eU(eF(0xffffffff))))}).setLayout({name:"mx_bits_to_01",type:"float",inputs:[{name:"bits",type:"uint"}]}),h_=eA(e=>{let[t]=e,r=eP(t).toVar();return r.mul(r).mul(r).mul(r.mul(r.mul(6).sub(15)).add(10))}).setLayout({name:"mx_fade",type:"float",inputs:[{name:"t",type:"float"}]}),hN=eA(e=>{let[t]=e,r=eF(t).toVar(),i=eU(eU(1)).toVar(),s=eU(eU(eF(0xdeadbeef)).add(i.shiftLeft(eU(2))).add(eU(13))).toVar();return hT(s.add(eU(r)),s,s)}).setLayout({name:"mx_hash_int_0",type:"uint",inputs:[{name:"x",type:"int"}]}),hS=eA(e=>{let[t,r]=e,i=eF(r).toVar(),s=eF(t).toVar(),n=eU(eU(2)).toVar(),a=eU().toVar(),o=eU().toVar(),l=eU().toVar();return a.assign(o.assign(l.assign(eU(eF(0xdeadbeef)).add(n.shiftLeft(eU(2))).add(eU(13))))),a.addAssign(eU(s)),o.addAssign(eU(i)),hT(a,o,l)}).setLayout({name:"mx_hash_int_1",type:"uint",inputs:[{name:"x",type:"int"},{name:"y",type:"int"}]}),hR=eA(e=>{let[t,r,i]=e,s=eF(i).toVar(),n=eF(r).toVar(),a=eF(t).toVar(),o=eU(eU(3)).toVar(),l=eU().toVar(),u=eU().toVar(),d=eU().toVar();return l.assign(u.assign(d.assign(eU(eF(0xdeadbeef)).add(o.shiftLeft(eU(2))).add(eU(13))))),l.addAssign(eU(a)),u.addAssign(eU(n)),d.addAssign(eU(s)),hT(l,u,d)}).setLayout({name:"mx_hash_int_2",type:"uint",inputs:[{name:"x",type:"int"},{name:"y",type:"int"},{name:"z",type:"int"}]}),hA=up([hN,hS,hR,eA(e=>{let[t,r,i,s]=e,n=eF(s).toVar(),a=eF(i).toVar(),o=eF(r).toVar(),l=eF(t).toVar(),u=eU(eU(4)).toVar(),d=eU().toVar(),h=eU().toVar(),c=eU().toVar();return d.assign(h.assign(c.assign(eU(eF(0xdeadbeef)).add(u.shiftLeft(eU(2))).add(eU(13))))),d.addAssign(eU(l)),h.addAssign(eU(o)),c.addAssign(eU(a)),hb(d,h,c),d.addAssign(eU(n)),hT(d,h,c)}).setLayout({name:"mx_hash_int_3",type:"uint",inputs:[{name:"x",type:"int"},{name:"y",type:"int"},{name:"z",type:"int"},{name:"xx",type:"int"}]}),eA(e=>{let[t,r,i,s,n]=e,a=eF(n).toVar(),o=eF(s).toVar(),l=eF(i).toVar(),u=eF(r).toVar(),d=eF(t).toVar(),h=eU(eU(5)).toVar(),c=eU().toVar(),p=eU().toVar(),g=eU().toVar();return c.assign(p.assign(g.assign(eU(eF(0xdeadbeef)).add(h.shiftLeft(eU(2))).add(eU(13))))),c.addAssign(eU(d)),p.addAssign(eU(u)),g.addAssign(eU(l)),hb(c,p,g),c.addAssign(eU(o)),p.addAssign(eU(a)),hT(c,p,g)}).setLayout({name:"mx_hash_int_4",type:"uint",inputs:[{name:"x",type:"int"},{name:"y",type:"int"},{name:"z",type:"int"},{name:"xx",type:"int"},{name:"yy",type:"int"}]})]),hE=up([eA(e=>{let[t,r]=e,i=eF(r).toVar(),s=eU(hA(eF(t).toVar(),i)).toVar(),n=ez().toVar();return n.x.assign(s.bitAnd(eF(255))),n.y.assign(s.shiftRight(eF(8)).bitAnd(eF(255))),n.z.assign(s.shiftRight(eF(16)).bitAnd(eF(255))),n}).setLayout({name:"mx_hash_vec3_0",type:"uvec3",inputs:[{name:"x",type:"int"},{name:"y",type:"int"}]}),eA(e=>{let[t,r,i]=e,s=eF(i).toVar(),n=eF(r).toVar(),a=eU(hA(eF(t).toVar(),n,s)).toVar(),o=ez().toVar();return o.x.assign(a.bitAnd(eF(255))),o.y.assign(a.shiftRight(eF(8)).bitAnd(eF(255))),o.z.assign(a.shiftRight(eF(16)).bitAnd(eF(255))),o}).setLayout({name:"mx_hash_vec3_1",type:"uvec3",inputs:[{name:"x",type:"int"},{name:"y",type:"int"},{name:"z",type:"int"}]})]),hC=up([eA(e=>{let[t]=e,r=eI(t).toVar(),i=eF().toVar(),s=eF().toVar(),n=eP(hu(r.x,i)).toVar(),a=eP(hu(r.y,s)).toVar(),o=eP(h_(n)).toVar(),l=eP(h_(a)).toVar();return hf(eP(hd(hc(hA(i,s),n,a),hc(hA(i.add(eF(1)),s),n.sub(1),a),hc(hA(i,s.add(eF(1))),n,a.sub(1)),hc(hA(i.add(eF(1)),s.add(eF(1))),n.sub(1),a.sub(1)),o,l)).toVar())}).setLayout({name:"mx_perlin_noise_float_0",type:"float",inputs:[{name:"p",type:"vec2"}]}),eA(e=>{let[t]=e,r=eG(t).toVar(),i=eF().toVar(),s=eF().toVar(),n=eF().toVar(),a=eP(hu(r.x,i)).toVar(),o=eP(hu(r.y,s)).toVar(),l=eP(hu(r.z,n)).toVar(),u=eP(h_(a)).toVar(),d=eP(h_(o)).toVar(),h=eP(h_(l)).toVar();return hy(eP(hh(hc(hA(i,s,n),a,o,l),hc(hA(i.add(eF(1)),s,n),a.sub(1),o,l),hc(hA(i,s.add(eF(1)),n),a,o.sub(1),l),hc(hA(i.add(eF(1)),s.add(eF(1)),n),a.sub(1),o.sub(1),l),hc(hA(i,s,n.add(eF(1))),a,o,l.sub(1)),hc(hA(i.add(eF(1)),s,n.add(eF(1))),a.sub(1),o,l.sub(1)),hc(hA(i,s.add(eF(1)),n.add(eF(1))),a,o.sub(1),l.sub(1)),hc(hA(i.add(eF(1)),s.add(eF(1)),n.add(eF(1))),a.sub(1),o.sub(1),l.sub(1)),u,d,h)).toVar())}).setLayout({name:"mx_perlin_noise_float_1",type:"float",inputs:[{name:"p",type:"vec3"}]})]),hw=up([eA(e=>{let[t]=e,r=eI(t).toVar(),i=eF().toVar(),s=eF().toVar(),n=eP(hu(r.x,i)).toVar(),a=eP(hu(r.y,s)).toVar(),o=eP(h_(n)).toVar(),l=eP(h_(a)).toVar();return hf(eG(hd(hp(hE(i,s),n,a),hp(hE(i.add(eF(1)),s),n.sub(1),a),hp(hE(i,s.add(eF(1))),n,a.sub(1)),hp(hE(i.add(eF(1)),s.add(eF(1))),n.sub(1),a.sub(1)),o,l)).toVar())}).setLayout({name:"mx_perlin_noise_vec3_0",type:"vec3",inputs:[{name:"p",type:"vec2"}]}),eA(e=>{let[t]=e,r=eG(t).toVar(),i=eF().toVar(),s=eF().toVar(),n=eF().toVar(),a=eP(hu(r.x,i)).toVar(),o=eP(hu(r.y,s)).toVar(),l=eP(hu(r.z,n)).toVar(),u=eP(h_(a)).toVar(),d=eP(h_(o)).toVar(),h=eP(h_(l)).toVar();return hy(eG(hh(hp(hE(i,s,n),a,o,l),hp(hE(i.add(eF(1)),s,n),a.sub(1),o,l),hp(hE(i,s.add(eF(1)),n),a,o.sub(1),l),hp(hE(i.add(eF(1)),s.add(eF(1)),n),a.sub(1),o.sub(1),l),hp(hE(i,s,n.add(eF(1))),a,o,l.sub(1)),hp(hE(i.add(eF(1)),s,n.add(eF(1))),a.sub(1),o,l.sub(1)),hp(hE(i,s.add(eF(1)),n.add(eF(1))),a,o.sub(1),l.sub(1)),hp(hE(i.add(eF(1)),s.add(eF(1)),n.add(eF(1))),a.sub(1),o.sub(1),l.sub(1)),u,d,h)).toVar())}).setLayout({name:"mx_perlin_noise_vec3_1",type:"vec3",inputs:[{name:"p",type:"vec3"}]})]),hM=eA(e=>{let[t]=e;return hv(hA(eF(hl(eP(t).toVar())).toVar()))}).setLayout({name:"mx_cell_noise_float_0",type:"float",inputs:[{name:"p",type:"float"}]}),hB=eA(e=>{let[t]=e,r=eI(t).toVar();return hv(hA(eF(hl(r.x)).toVar(),eF(hl(r.y)).toVar()))}).setLayout({name:"mx_cell_noise_float_1",type:"float",inputs:[{name:"p",type:"vec2"}]}),hP=eA(e=>{let[t]=e,r=eF(hl(eP(t).toVar())).toVar();return eG(hv(hA(r,eF(0))),hv(hA(r,eF(1))),hv(hA(r,eF(2))))}).setLayout({name:"mx_cell_noise_vec3_0",type:"vec3",inputs:[{name:"p",type:"float"}]}),hF=eA(e=>{let[t]=e,r=eI(t).toVar(),i=eF(hl(r.x)).toVar(),s=eF(hl(r.y)).toVar();return eG(hv(hA(i,s,eF(0))),hv(hA(i,s,eF(1))),hv(hA(i,s,eF(2))))}).setLayout({name:"mx_cell_noise_vec3_1",type:"vec3",inputs:[{name:"p",type:"vec2"}]}),hU=up([hP,hF,eA(e=>{let[t]=e,r=eG(t).toVar(),i=eF(hl(r.x)).toVar(),s=eF(hl(r.y)).toVar(),n=eF(hl(r.z)).toVar();return eG(hv(hA(i,s,n,eF(0))),hv(hA(i,s,n,eF(1))),hv(hA(i,s,n,eF(2))))}).setLayout({name:"mx_cell_noise_vec3_2",type:"vec3",inputs:[{name:"p",type:"vec3"}]}),eA(e=>{let[t]=e,r=eW(t).toVar(),i=eF(hl(r.x)).toVar(),s=eF(hl(r.y)).toVar(),n=eF(hl(r.z)).toVar(),a=eF(hl(r.w)).toVar();return eG(hv(hA(i,s,n,a,eF(0))),hv(hA(i,s,n,a,eF(1))),hv(hA(i,s,n,a,eF(2))))}).setLayout({name:"mx_cell_noise_vec3_3",type:"vec3",inputs:[{name:"p",type:"vec4"}]})]),hL=eA(e=>{let[t,r,i,s]=e,n=eP(s).toVar(),a=eP(i).toVar(),o=eF(r).toVar(),l=eG(t).toVar(),u=eP(0).toVar(),d=eP(1).toVar();return nU(o,()=>{u.addAssign(d.mul(hC(l))),d.mulAssign(n),l.mulAssign(a)}),u}).setLayout({name:"mx_fractal_noise_float",type:"float",inputs:[{name:"p",type:"vec3"},{name:"octaves",type:"int"},{name:"lacunarity",type:"float"},{name:"diminish",type:"float"}]}),hI=eA(e=>{let[t,r,i,s]=e,n=eP(s).toVar(),a=eP(i).toVar(),o=eF(r).toVar(),l=eG(t).toVar(),u=eG(0).toVar(),d=eP(1).toVar();return nU(o,()=>{u.addAssign(d.mul(hw(l))),d.mulAssign(n),l.mulAssign(a)}),u}).setLayout({name:"mx_fractal_noise_vec3",type:"vec3",inputs:[{name:"p",type:"vec3"},{name:"octaves",type:"int"},{name:"lacunarity",type:"float"},{name:"diminish",type:"float"}]}),hD=up([eA(e=>{let[t,r,i,s,n,a,o]=e,l=eF(o).toVar(),u=eP(a).toVar(),d=eF(n).toVar(),h=eF(s).toVar(),c=eF(i).toVar(),p=eF(r).toVar(),g=eI(t).toVar(),m=eG(hU(eI(p.add(h),c.add(d)))).toVar(),f=eI(m.x,m.y).toVar();f.subAssign(.5),f.mulAssign(u),f.addAssign(.5);let y=eI(eI(eP(p),eP(c)).add(f)).toVar(),x=eI(y.sub(g)).toVar();return ew(l.equal(eF(2)),()=>rg(x.x).add(rg(x.y))),ew(l.equal(eF(3)),()=>rE(rg(x.x),rg(x.y))),rP(x,x)}).setLayout({name:"mx_worley_distance_0",type:"float",inputs:[{name:"p",type:"vec2"},{name:"x",type:"int"},{name:"y",type:"int"},{name:"xoff",type:"int"},{name:"yoff",type:"int"},{name:"jitter",type:"float"},{name:"metric",type:"int"}]}),eA(e=>{let[t,r,i,s,n,a,o,l,u]=e,d=eF(u).toVar(),h=eP(l).toVar(),c=eF(o).toVar(),p=eF(a).toVar(),g=eF(n).toVar(),m=eF(s).toVar(),f=eF(i).toVar(),y=eF(r).toVar(),x=eG(t).toVar(),b=eG(hU(eG(y.add(g),f.add(p),m.add(c)))).toVar();b.subAssign(.5),b.mulAssign(h),b.addAssign(.5);let T=eG(eG(eP(y),eP(f),eP(m)).add(b)).toVar(),v=eG(T.sub(x)).toVar();return ew(d.equal(eF(2)),()=>rg(v.x).add(rg(v.y)).add(rg(v.z))),ew(d.equal(eF(3)),()=>rE(rE(rg(v.x),rg(v.y)),rg(v.z))),rP(v,v)}).setLayout({name:"mx_worley_distance_1",type:"float",inputs:[{name:"p",type:"vec3"},{name:"x",type:"int"},{name:"y",type:"int"},{name:"z",type:"int"},{name:"xoff",type:"int"},{name:"yoff",type:"int"},{name:"zoff",type:"int"},{name:"jitter",type:"float"},{name:"metric",type:"int"}]})]),hO=eA(e=>{let[t,r,i]=e,s=eF(i).toVar(),n=eP(r).toVar(),a=eI(t).toVar(),o=eF().toVar(),l=eF().toVar(),u=eI(hu(a.x,o),hu(a.y,l)).toVar(),d=eP(1e6).toVar();return nU({start:-1,end:eF(1),name:"x",condition:"<="},e=>{let{x:t}=e;nU({start:-1,end:eF(1),name:"y",condition:"<="},e=>{let{y:r}=e,i=eP(hD(u,t,r,o,l,n,s)).toVar();d.assign(rA(d,i))})}),ew(s.equal(eF(0)),()=>{d.assign(rr(d))}),d}).setLayout({name:"mx_worley_noise_float_0",type:"float",inputs:[{name:"p",type:"vec2"},{name:"jitter",type:"float"},{name:"metric",type:"int"}]}),hV=eA(e=>{let[t,r,i]=e,s=eF(i).toVar(),n=eP(r).toVar(),a=eI(t).toVar(),o=eF().toVar(),l=eF().toVar(),u=eI(hu(a.x,o),hu(a.y,l)).toVar(),d=eI(1e6,1e6).toVar();return nU({start:-1,end:eF(1),name:"x",condition:"<="},e=>{let{x:t}=e;nU({start:-1,end:eF(1),name:"y",condition:"<="},e=>{let{y:r}=e,i=eP(hD(u,t,r,o,l,n,s)).toVar();ew(i.lessThan(d.x),()=>{d.y.assign(d.x),d.x.assign(i)}).ElseIf(i.lessThan(d.y),()=>{d.y.assign(i)})})}),ew(s.equal(eF(0)),()=>{d.assign(rr(d))}),d}).setLayout({name:"mx_worley_noise_vec2_0",type:"vec2",inputs:[{name:"p",type:"vec2"},{name:"jitter",type:"float"},{name:"metric",type:"int"}]}),hG=eA(e=>{let[t,r,i]=e,s=eF(i).toVar(),n=eP(r).toVar(),a=eI(t).toVar(),o=eF().toVar(),l=eF().toVar(),u=eI(hu(a.x,o),hu(a.y,l)).toVar(),d=eG(1e6,1e6,1e6).toVar();return nU({start:-1,end:eF(1),name:"x",condition:"<="},e=>{let{x:t}=e;nU({start:-1,end:eF(1),name:"y",condition:"<="},e=>{let{y:r}=e,i=eP(hD(u,t,r,o,l,n,s)).toVar();ew(i.lessThan(d.x),()=>{d.z.assign(d.y),d.y.assign(d.x),d.x.assign(i)}).ElseIf(i.lessThan(d.y),()=>{d.z.assign(d.y),d.y.assign(i)}).ElseIf(i.lessThan(d.z),()=>{d.z.assign(i)})})}),ew(s.equal(eF(0)),()=>{d.assign(rr(d))}),d}).setLayout({name:"mx_worley_noise_vec3_0",type:"vec3",inputs:[{name:"p",type:"vec2"},{name:"jitter",type:"float"},{name:"metric",type:"int"}]}),hk=(e,t)=>{e=eP(e);let r=eI((t=eP(t)).dFdx(),t.dFdy()).length().mul(.7071067811865476);return rH(e.sub(r),e.add(r),t)},hz=(e=>{let[t,r,i]=e,s=ra(t).toVar(),n=tB(eP(.5).mul(r.sub(i)),i3).div(s).toVar(),a=tB(eP(-.5).mul(r.sub(i)),i3).div(s).toVar(),o=eG().toVar();o.x=s.x.greaterThan(eP(0)).select(n.x,a.x),o.y=s.y.greaterThan(eP(0)).select(n.y,a.y),o.z=s.z.greaterThan(eP(0)).select(n.z,a.z);let l=rA(rA(o.x,o.y),o.z).toVar();return i3.add(s.mul(l)).toVar().sub(i)},eA(e=>{let[t,r]=e,i=t.x,s=t.y,n=t.z,a=r.element(0).mul(.886227);return(a=(a=(a=(a=(a=(a=(a=a.add(r.element(1).mul(1.023328).mul(s))).add(r.element(2).mul(1.023328).mul(n))).add(r.element(3).mul(1.023328).mul(i))).add(r.element(4).mul(.858086).mul(i).mul(s))).add(r.element(5).mul(.858086).mul(s).mul(n))).add(r.element(6).mul(n.mul(n).mul(.743125).sub(.247708)))).add(r.element(7).mul(.858086).mul(i).mul(n))).add(r.element(8).mul(.429043).mul(tP(i,i).sub(tP(s,s))))})),hH=new ur;class hW extends lD{update(e,t,r){let i=this.renderer,s=this.nodes.getBackgroundNode(e)||e.background,n=!1;if(null===s)i._clearColor.getRGB(hH),hH.a=i._clearColor.a;else if(!0===s.isColor)s.getRGB(hH),hH.a=1,n=!0;else if(!0===s.isNode){let r=this.get(e);hH.copy(i._clearColor);let n=r.backgroundMesh;if(void 0===n){let e=rY(eW(s).mul(uW),{getUV:()=>uq.mul(si),getTextureLevel:()=>uH}),t=ny;t=t.setZ(t.w);let i=new ah;i.name="Background.material",i.side=u.hsX,i.depthTest=!1,i.depthWrite=!1,i.allowOverride=!1,i.fog=!1,i.lights=!1,i.vertexNode=t,i.colorNode=e,r.backgroundMeshNode=e,r.backgroundMesh=n=new u.eaF(new u.Gu$(1,32,32),i),n.frustumCulled=!1,n.name="Background.mesh",n.onBeforeRender=function(e,t,r){this.matrixWorld.copyPosition(r.matrixWorld)},s.addEventListener("dispose",function e(){s.removeEventListener("dispose",e),n.material.dispose(),n.geometry.dispose()})}let a=s.getCacheKey();r.backgroundCacheKey!==a&&(r.backgroundMeshNode.node=eW(s).mul(uW),r.backgroundMeshNode.needsUpdate=!0,n.material.needsUpdate=!0,r.backgroundCacheKey=a),t.unshift(n,n.geometry,n.material,0,0,null,null)}else console.error("THREE.Renderer: Unsupported background configuration.",s);let a=i.xr.getEnvironmentBlendMode();if("additive"===a?hH.set(0,0,0,1):"alpha-blend"===a&&hH.set(0,0,0,0),!0===i.autoClear||!0===n){let e=r.clearColorValue;e.r=hH.r,e.g=hH.g,e.b=hH.b,e.a=hH.a,(!0===i.backend.isWebGLBackend||!0===i.alpha)&&(e.r*=e.a,e.g*=e.a,e.b*=e.a),r.depthClearValue=i._clearDepth,r.stencilClearValue=i._clearStencil,r.clearColor=!0===i.autoClearColor,r.clearDepth=!0===i.autoClearDepth,r.clearStencil=!0===i.autoClearStencil}else r.clearColor=!1,r.clearDepth=!1,r.clearStencil=!1}constructor(e,t){super(),this.renderer=e,this.nodes=t}}let hq=0;class hj{constructor(e="",t=[],r=0,i=[]){this.name=e,this.bindings=t,this.index=r,this.bindingsReference=i,this.id=hq++}}class hX{createBindings(){let e=[];for(let t of this.bindings)if(!0!==t.bindings[0].groupNode.shared){let r=new hj(t.name,[],t.index,t);for(let i of(e.push(r),t.bindings))r.bindings.push(i.clone())}else e.push(t);return e}constructor(e,t,r,i,s,n,a,o,l,u=[]){this.vertexShader=e,this.fragmentShader=t,this.computeShader=r,this.transforms=u,this.nodeAttributes=i,this.bindings=s,this.updateNodes=n,this.updateBeforeNodes=a,this.updateAfterNodes=o,this.observer=l,this.usedTimes=0}}class hQ{constructor(e,t,r=null){this.isNodeAttribute=!0,this.name=e,this.type=t,this.node=r}}class hY{get value(){return this.node.value}set value(e){this.node.value=e}get id(){return this.node.id}get groupNode(){return this.node.groupNode}constructor(e,t,r){this.isNodeUniform=!0,this.name=e,this.type=t,this.node=r.getSelf()}}class hK{constructor(e,t,r=!1,i=null){this.isNodeVar=!0,this.name=e,this.type=t,this.readOnly=r,this.count=i}}class hZ extends hK{constructor(e,t,r=null,i=null){super(e,t),this.needsInterpolation=!1,this.isNodeVarying=!0,this.interpolationType=r,this.interpolationSampling=i}}class h${constructor(e,t,r=""){this.name=e,this.type=t,this.code=r,Object.defineProperty(this,"isNodeCode",{value:!0})}}let hJ=0;class h0{getData(e){let t=this.nodesData.get(e);return void 0===t&&null!==this.parent&&(t=this.parent.getData(e)),t}setData(e,t){this.nodesData.set(e,t)}constructor(e=null){this.id=hJ++,this.nodesData=new WeakMap,this.parent=e}}class h1{constructor(e,t){this.name=e,this.members=t,this.output=!1}}class h2{setValue(e){this.value=e}getValue(){return this.value}constructor(e,t){this.name=e,this.value=t,this.boundary=0,this.itemSize=0,this.offset=0}}class h3 extends h2{constructor(e,t=0){super(e,t),this.isNumberUniform=!0,this.boundary=4,this.itemSize=1}}class h4 extends h2{constructor(e,t=new u.I9Y){super(e,t),this.isVector2Uniform=!0,this.boundary=8,this.itemSize=2}}class h6 extends h2{constructor(e,t=new u.Pq0){super(e,t),this.isVector3Uniform=!0,this.boundary=16,this.itemSize=3}}class h8 extends h2{constructor(e,t=new u.IUQ){super(e,t),this.isVector4Uniform=!0,this.boundary=16,this.itemSize=4}}class h5 extends h2{constructor(e,t=new u.Q1f){super(e,t),this.isColorUniform=!0,this.boundary=16,this.itemSize=3}}class h9 extends h2{constructor(e,t=new u.k_V){super(e,t),this.isMatrix2Uniform=!0,this.boundary=16,this.itemSize=4}}class h7 extends h2{constructor(e,t=new u.dwI){super(e,t),this.isMatrix3Uniform=!0,this.boundary=48,this.itemSize=12}}class ce extends h2{constructor(e,t=new u.kn4){super(e,t),this.isMatrix4Uniform=!0,this.boundary=64,this.itemSize=16}}class ct extends h3{getValue(){return this.nodeUniform.value}getType(){return this.nodeUniform.type}constructor(e){super(e.name,e.value),this.nodeUniform=e}}class cr extends h4{getValue(){return this.nodeUniform.value}getType(){return this.nodeUniform.type}constructor(e){super(e.name,e.value),this.nodeUniform=e}}class ci extends h6{getValue(){return this.nodeUniform.value}getType(){return this.nodeUniform.type}constructor(e){super(e.name,e.value),this.nodeUniform=e}}class cs extends h8{getValue(){return this.nodeUniform.value}getType(){return this.nodeUniform.type}constructor(e){super(e.name,e.value),this.nodeUniform=e}}class cn extends h5{getValue(){return this.nodeUniform.value}getType(){return this.nodeUniform.type}constructor(e){super(e.name,e.value),this.nodeUniform=e}}class ca extends h9{getValue(){return this.nodeUniform.value}getType(){return this.nodeUniform.type}constructor(e){super(e.name,e.value),this.nodeUniform=e}}class co extends h7{getValue(){return this.nodeUniform.value}getType(){return this.nodeUniform.type}constructor(e){super(e.name,e.value),this.nodeUniform=e}}class cl extends ce{getValue(){return this.nodeUniform.value}getType(){return this.nodeUniform.type}constructor(e){super(e.name,e.value),this.nodeUniform=e}}let cu=new WeakMap,cd=new Map([[Int8Array,"int"],[Int16Array,"int"],[Int32Array,"int"],[Uint8Array,"uint"],[Uint16Array,"uint"],[Uint32Array,"uint"],[Float32Array,"float"]]),ch=e=>/e/g.test(e)?String(e).replace(/\+/g,""):(e=Number(e))+(e%1?"":".0");class cc{getBindGroupsCache(){let e=cu.get(this.renderer);return void 0===e&&(e=new lP,cu.set(this.renderer,e)),e}createRenderTarget(e,t,r){return new u.O0B(e,t,r)}createRenderTargetArray(e,t,r,i){return new u.rrX(e,t,r,i)}createCubeRenderTarget(e,t){return new aN(e,t)}includes(e){return this.nodes.includes(e)}getOutputStructName(){}_getBindGroup(e,t){let r,i=this.getBindGroupsCache(),s=[],n=!0;for(let e of t)s.push(e),n=n&&!0!==e.groupNode.shared;return n?void 0===(r=i.get(s))&&(r=new hj(e,s,this.bindingsIndexes[e].group,s),i.set(s,r)):r=new hj(e,s,this.bindingsIndexes[e].group,s),r}getBindGroupArray(e,t){let r=this.bindings[t],i=r[e];return void 0===i&&(void 0===this.bindingsIndexes[e]&&(this.bindingsIndexes[e]={binding:0,group:Object.keys(this.bindingsIndexes).length}),r[e]=i=[]),i}getBindings(){let e=this.bindGroups;if(null===e){let t={},r=this.bindings;for(let e of M)for(let i in r[e]){let s=r[e][i];(t[i]||(t[i]=[])).push(...s)}for(let r in e=[],t){let i=t[r],s=this._getBindGroup(r,i);e.push(s)}this.bindGroups=e}return e}sortBindingGroups(){let e=this.getBindings();e.sort((e,t)=>e.bindings[0].groupNode.order-t.bindings[0].groupNode.order);for(let t=0;t<e.length;t++){let r=e[t];this.bindingsIndexes[r.name].group=t,r.index=t}}setHashNode(e,t){this.hashNodes[t]=e}addNode(e){!1===this.nodes.includes(e)&&(this.nodes.push(e),this.setHashNode(e,e.getHash(this)))}addSequentialNode(e){!1===this.sequentialNodes.includes(e)&&this.sequentialNodes.push(e)}buildUpdateNodes(){for(let e of this.nodes)e.getUpdateType()!==E.NONE&&this.updateNodes.push(e.getSelf());for(let e of this.sequentialNodes){let t=e.getUpdateBeforeType(),r=e.getUpdateAfterType();t!==E.NONE&&this.updateBeforeNodes.push(e.getSelf()),r!==E.NONE&&this.updateAfterNodes.push(e.getSelf())}}get currentNode(){return this.chaining[this.chaining.length-1]}isFilteredTexture(e){return e.magFilter===u.k6q||e.magFilter===u.kRr||e.magFilter===u.Cfg||e.magFilter===u.$_I||e.minFilter===u.k6q||e.minFilter===u.kRr||e.minFilter===u.Cfg||e.minFilter===u.$_I}addChain(e){this.chaining.push(e)}removeChain(e){if(this.chaining.pop()!==e)throw Error("NodeBuilder: Invalid node chaining!")}getMethod(e){return e}getNodeFromHash(e){return this.hashNodes[e]}addFlow(e,t){return this.flowNodes[e].push(t),t}setContext(e){this.context=e}getContext(){return this.context}getSharedContext(){return{...this.context},this.context}setCache(e){this.cache=e}getCache(){return this.cache}getCacheFromNode(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1],r=this.getDataFromNode(e);return void 0===r.cache&&(r.cache=new h0(t?this.getCache():null)),r.cache}isAvailable(){return!1}getVertexIndex(){console.warn("Abstract function.")}getInstanceIndex(){console.warn("Abstract function.")}getDrawIndex(){console.warn("Abstract function.")}getFrontFacing(){console.warn("Abstract function.")}getFragCoord(){console.warn("Abstract function.")}isFlipY(){return!1}increaseUsage(e){let t=this.getDataFromNode(e);return t.usageCount=void 0===t.usageCount?1:t.usageCount+1,t.usageCount}generateTexture(){console.warn("Abstract function.")}generateTextureLod(){console.warn("Abstract function.")}generateArrayDeclaration(e,t){return this.getType(e)+"[ "+t+" ]"}generateArray(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,i=this.generateArrayDeclaration(e,t)+"( ";for(let s=0;s<t;s++){let n=r?r[s]:null;null!==n?i+=n.build(this,e):i+=this.generateConst(e),s<t-1&&(i+=", ")}return i+" )"}generateStruct(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,i=[];for(let e of t){let{name:t,type:s}=e;r&&r[t]&&r[t].isNode?i.push(r[t].build(this,s)):i.push(this.generateConst(s))}return e+"( "+i.join(", ")+" )"}generateConst(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(null===t&&("float"===e||"int"===e||"uint"===e?t=0:"bool"===e?t=!1:"color"===e?t=new u.Q1f:"vec2"===e?t=new u.I9Y:"vec3"===e?t=new u.Pq0:"vec4"===e&&(t=new u.IUQ)),"float"===e)return ch(t);if("int"===e)return"".concat(Math.round(t));if("uint"===e)return t>=0?"".concat(Math.round(t),"u"):"0u";if("bool"===e)return t?"true":"false";if("color"===e)return"".concat(this.getType("vec3"),"( ").concat(ch(t.r),", ").concat(ch(t.g),", ").concat(ch(t.b)," )");let r=this.getTypeLength(e),i=this.getComponentType(e),s=e=>this.generateConst(i,e);if(2===r)return"".concat(this.getType(e),"( ").concat(s(t.x),", ").concat(s(t.y)," )");if(3===r)return"".concat(this.getType(e),"( ").concat(s(t.x),", ").concat(s(t.y),", ").concat(s(t.z)," )");if(4===r&&"mat2"!==e)return"".concat(this.getType(e),"( ").concat(s(t.x),", ").concat(s(t.y),", ").concat(s(t.z),", ").concat(s(t.w)," )");if(r>=4&&t&&(t.isMatrix2||t.isMatrix3||t.isMatrix4))return"".concat(this.getType(e),"( ").concat(t.elements.map(s).join(", ")," )");if(r>4)return"".concat(this.getType(e),"()");throw Error("NodeBuilder: Type '".concat(e,"' not found in generate constant attempt."))}getType(e){return"color"===e?"vec3":e}hasGeometryAttribute(e){return this.geometry&&void 0!==this.geometry.getAttribute(e)}getAttribute(e,t){let r=this.attributes;for(let t of r)if(t.name===e)return t;let i=new hQ(e,t);return this.registerDeclaration(i),r.push(i),i}getPropertyName(e){return e.name}isVector(e){return/vec\d/.test(e)}isMatrix(e){return/mat\d/.test(e)}isReference(e){return"void"===e||"property"===e||"sampler"===e||"samplerComparison"===e||"texture"===e||"cubeTexture"===e||"storageTexture"===e||"depthTexture"===e||"texture3D"===e}needsToWorkingColorSpace(){return!1}getComponentTypeFromTexture(e){let t=e.type;if(e.isDataTexture){if(t===u.Yuy)return"int";if(t===u.bkx)return"uint"}return"float"}getElementType(e){return"mat2"===e?"vec2":"mat3"===e?"vec3":"mat4"===e?"vec4":this.getComponentType(e)}getComponentType(e){if("float"===(e=this.getVectorType(e))||"bool"===e||"int"===e||"uint"===e)return e;let t=/(b|i|u|)(vec|mat)([2-4])/.exec(e);return null===t?null:"b"===t[1]?"bool":"i"===t[1]?"int":"u"===t[1]?"uint":"float"}getVectorType(e){return"color"===e?"vec3":"texture"===e||"cubeTexture"===e||"storageTexture"===e||"texture3D"===e?"vec4":e}getTypeFromLength(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"float";if(1===e)return r;let i=(t=e,x.get(t)),s="float"===r?"":r[0];return!0===/mat2/.test(r)&&(i=i.replace("vec","mat")),s+i}getTypeFromArray(e){return cd.get(e.constructor)}isInteger(e){return/int|uint|(i|u)vec/.test(e)}getTypeFromAttribute(e){let t,r=e;e.isInterleavedBufferAttribute&&(r=e.data);let i=r.array,s=e.itemSize,n=e.normalized;return e instanceof u.Oax||!0===n||(t=this.getTypeFromArray(i)),this.getTypeFromLength(s,t)}getTypeLength(e){let t=this.getVectorType(e),r=/vec([2-4])/.exec(t);return null!==r?Number(r[1]):"float"===t||"bool"===t||"int"===t||"uint"===t?1:!0===/mat2/.test(e)?4:!0===/mat3/.test(e)?9:16*(!0===/mat4/.test(e))}getVectorFromMatrix(e){return e.replace("mat","vec")}changeComponentType(e,t){return this.getTypeFromLength(this.getTypeLength(e),t)}getIntegerType(e){let t=this.getComponentType(e);return"int"===t||"uint"===t?e:this.changeComponentType(e,"int")}addStack(){return this.stack=un(this.stack),this.stacks.push(eC()||this.stack),eE(this.stack),this.stack}removeStack(){let e=this.stack;return this.stack=e.parent,eE(this.stacks.pop()),e}getDataFromNode(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.shaderStage,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,i=(r=null===r?e.isGlobal(this)?this.globalCache:this.cache:r).getData(e);return void 0===i&&(i={},r.setData(e,i)),void 0===i[t]&&(i[t]={}),i[t]}getNodeProperties(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"any",r=this.getDataFromNode(e,t);return r.properties||(r.properties={outputNode:null})}getBufferAttributeFromNode(e,t){let r=this.getDataFromNode(e),i=r.bufferAttribute;return void 0===i&&(i=new hQ("nodeAttribute"+this.uniforms.index++,t,e),this.bufferAttributes.push(i),r.bufferAttribute=i),i}getStructTypeFromNode(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:this.shaderStage,s=this.getDataFromNode(e,i,this.globalCache),n=s.structType;if(void 0===n){let e=this.structs.index++;null===r&&(r="StructType"+e),n=new h1(r,t),this.structs[i].push(n),s.structType=n}return n}getOutputStructTypeFromNode(e,t){let r=this.getStructTypeFromNode(e,t,"OutputType","fragment");return r.output=!0,r}getUniformFromNode(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.shaderStage,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,s=this.getDataFromNode(e,r,this.globalCache),n=s.uniform;if(void 0===n){let a=this.uniforms.index++;n=new hY(i||"nodeUniform"+a,t,e),this.uniforms[r].push(n),this.registerDeclaration(n),s.uniform=n}return n}getArrayCount(e){let t=null;return e.isArrayNode?t=e.count:e.isVarNode&&e.node.isArrayNode&&(t=e.node.count),t}getVarFromNode(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e.getNodeType(this),i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:this.shaderStage,s=arguments.length>4&&void 0!==arguments[4]&&arguments[4],n=this.getDataFromNode(e,i),a=n.variable;if(void 0===a){let o=s?"_const":"_var",l=this.vars[i]||(this.vars[i]=[]),u=this.vars[o]||(this.vars[o]=0);null===t&&(t=(s?"nodeConst":"nodeVar")+u,this.vars[o]++),a=new hK(t,r,s,this.getArrayCount(e)),s||l.push(a),this.registerDeclaration(a),n.variable=a}return a}isDeterministic(e){if(e.isMathNode)return this.isDeterministic(e.aNode)&&(!e.bNode||this.isDeterministic(e.bNode))&&(!e.cNode||this.isDeterministic(e.cNode));if(e.isOperatorNode)return this.isDeterministic(e.aNode)&&(!e.bNode||this.isDeterministic(e.bNode));if(e.isArrayNode){if(null!==e.values){for(let t of e.values)if(!this.isDeterministic(t))return!1}return!0}return!!e.isConstNode||!1}getVaryingFromNode(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e.getNodeType(this),i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,n=this.getDataFromNode(e,"any"),a=n.varying;if(void 0===a){let e=this.varyings,o=e.length;null===t&&(t="nodeVarying"+o),a=new hZ(t,r,i,s),e.push(a),this.registerDeclaration(a),n.varying=a}return a}registerDeclaration(e){let t=this.shaderStage,r=this.declarations[t]||(this.declarations[t]={}),i=this.getPropertyName(e),s=1,n=i;for(;void 0!==r[n];)n=i+"_"+s++;s>1&&(e.name=n,console.warn("THREE.TSL: Declaration name '".concat(i,"' of '").concat(e.type,"' already in use. Renamed to '").concat(n,"'."))),r[n]=e}getCodeFromNode(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.shaderStage,i=this.getDataFromNode(e),s=i.code;if(void 0===s){let e=this.codes[r]||(this.codes[r]=[]);s=new h$("nodeCode"+e.length,t),e.push(s),i.code=s}return s}addFlowCodeHierarchy(e,t){let{flowCodes:r,flowCodeBlock:i}=this.getDataFromNode(e),s=!0,n=t;for(;n;){if(!0===i.get(n)){s=!1;break}n=this.getDataFromNode(n).parentNodeBlock}if(s)for(let e of r)this.addLineFlowCode(e)}addLineFlowCodeBlock(e,t,r){let i=this.getDataFromNode(e),s=i.flowCodes||(i.flowCodes=[]),n=i.flowCodeBlock||(i.flowCodeBlock=new WeakMap);s.push(t),n.set(r,!0)}addLineFlowCode(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return""===e||(null!==t&&this.context.nodeBlock&&this.addLineFlowCodeBlock(t,e,this.context.nodeBlock),e=this.tab+e,/;\s*$/.test(e)||(e+=";\n"),this.flow.code+=e),this}addFlowCode(e){return this.flow.code+=e,this}addFlowTab(){return this.tab+="	",this}removeFlowTab(){return this.tab=this.tab.slice(0,-1),this}getFlowData(e){return this.flowsData.get(e)}flowNode(e){let t=e.getNodeType(this),r=this.flowChildNode(e,t);return this.flowsData.set(e,r),r}addInclude(e){null!==this.currentFunctionNode&&this.currentFunctionNode.includes.push(e)}buildFunctionNode(e){let t=new dl,r=this.currentFunctionNode;return this.currentFunctionNode=t,t.code=this.buildFunctionCode(e),this.currentFunctionNode=r,t}flowShaderNode(e){let t=e.layout,r={[Symbol.iterator](){let e=0,t=Object.values(this);return{next:()=>({value:t[e],done:e++>=t.length})}}};for(let e of t.inputs)r[e.name]=new ui(e.type,e.name);e.layout=null;let i=e.call(r),s=this.flowStagesNode(i,t.type);return e.layout=t,s}flowStagesNode(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=this.flow,i=this.vars,s=this.declarations,n=this.cache,a=this.buildStage,o=this.stack,l={code:""};for(let r of(this.flow=l,this.vars={},this.declarations={},this.cache=new h0,this.stack=un(),w))this.setBuildStage(r),l.result=e.build(this,t);return l.vars=this.getVars(this.shaderStage),this.flow=r,this.vars=i,this.declarations=s,this.cache=n,this.stack=o,this.setBuildStage(a),l}getFunctionOperator(){return null}buildFunctionCode(){console.warn("Abstract function.")}flowChildNode(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=this.flow,i={code:""};return this.flow=i,i.result=e.build(this,t),this.flow=r,i}flowNodeFromShaderStage(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,s=this.shaderStage;this.setShaderStage(e);let n=this.flowChildNode(t,r);return null!==i&&(n.code+="".concat(this.tab+i," = ").concat(n.result,";\n")),this.flowCode[e]=this.flowCode[e]+n.code,this.setShaderStage(s),n}getAttributesArray(){return this.attributes.concat(this.bufferAttributes)}getAttributes(){console.warn("Abstract function.")}getVaryings(){console.warn("Abstract function.")}getVar(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;return"".concat(null!==r?this.generateArrayDeclaration(e,r):this.getType(e)," ").concat(t)}getVars(e){let t="",r=this.vars[e];if(void 0!==r)for(let e of r)t+="".concat(this.getVar(e.type,e.name),"; ");return t}getUniforms(){console.warn("Abstract function.")}getCodes(e){let t=this.codes[e],r="";if(void 0!==t)for(let e of t)r+=e.code+"\n";return r}getHash(){return this.vertexShader+this.fragmentShader+this.computeShader}setShaderStage(e){this.shaderStage=e}getShaderStage(){return this.shaderStage}setBuildStage(e){this.buildStage=e}getBuildStage(){return this.buildStage}buildCode(){console.warn("Abstract function.")}build(){let{object:e,material:t,renderer:r}=this;if(null!==t){let e=r.library.fromMaterial(t);null===e&&(console.error('NodeMaterial: Material "'.concat(t.type,'" is not compatible.')),e=new ah),e.build(this)}else this.addFlow("compute",e);for(let e of w)for(let t of(this.setBuildStage(e),this.context.vertex&&this.context.vertex.isNode&&this.flowNodeFromShaderStage("vertex",this.context.vertex),M))for(let r of(this.setShaderStage(t),this.flowNodes[t]))"generate"===e?this.flowNode(r):r.build(this);return this.setBuildStage(null),this.setShaderStage(null),this.buildCode(),this.buildUpdateNodes(),this}getNodeUniform(e,t){if("float"===t||"int"===t||"uint"===t)return new ct(e);if("vec2"===t||"ivec2"===t||"uvec2"===t)return new cr(e);if("vec3"===t||"ivec3"===t||"uvec3"===t)return new ci(e);if("vec4"===t||"ivec4"===t||"uvec4"===t)return new cs(e);if("color"===t)return new cn(e);if("mat2"===t)return new ca(e);if("mat3"===t)return new co(e);if("mat4"===t)return new cl(e);throw Error('Uniform "'.concat(t,'" not declared.'))}format(e,t,r){if(t=this.getVectorType(t),r=this.getVectorType(r),t===r||null===r||this.isReference(r))return e;let i=this.getTypeLength(t),s=this.getTypeLength(r);return 16===i&&9===s?"".concat(this.getType(r),"( ").concat(e,"[ 0 ].xyz, ").concat(e,"[ 1 ].xyz, ").concat(e,"[ 2 ].xyz )"):9===i&&4===s?"".concat(this.getType(r),"( ").concat(e,"[ 0 ].xy, ").concat(e,"[ 1 ].xy )"):i>4||s>4||0===s?e:i===s?"".concat(this.getType(r),"( ").concat(e," )"):i>s?(e="bool"===r?"all( ".concat(e," )"):"".concat(e,".").concat("xyz".slice(0,s)),this.format(e,this.getTypeFromLength(s,this.getComponentType(t)),r)):4===s&&i>1?"".concat(this.getType(r),"( ").concat(this.format(e,t,"vec3"),", 1.0 )"):2===i?"".concat(this.getType(r),"( ").concat(this.format(e,t,"vec2"),", 0.0 )"):(1===i&&s>1&&t!==this.getComponentType(r)&&(e="".concat(this.getType(this.getComponentType(r)),"( ").concat(e," )")),"".concat(this.getType(r),"( ").concat(e," )"))}getSignature(){return"// Three.js r".concat(u.sPf," - Node System\n")}*[Symbol.iterator](){}createNodeMaterial(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"NodeMaterial";throw Error("THREE.NodeBuilder: createNodeMaterial() was deprecated. Use new ".concat(e,"() instead."))}constructor(e,t,r){this.object=e,this.material=e&&e.material||null,this.geometry=e&&e.geometry||null,this.renderer=t,this.parser=r,this.scene=null,this.camera=null,this.nodes=[],this.sequentialNodes=[],this.updateNodes=[],this.updateBeforeNodes=[],this.updateAfterNodes=[],this.hashNodes={},this.observer=null,this.lightsNode=null,this.environmentNode=null,this.fogNode=null,this.clippingContext=null,this.vertexShader=null,this.fragmentShader=null,this.computeShader=null,this.flowNodes={vertex:[],fragment:[],compute:[]},this.flowCode={vertex:"",fragment:"",compute:""},this.uniforms={vertex:[],fragment:[],compute:[],index:0},this.structs={vertex:[],fragment:[],compute:[],index:0},this.bindings={vertex:{},fragment:{},compute:{}},this.bindingsIndexes={},this.bindGroups=null,this.attributes=[],this.bufferAttributes=[],this.varyings=[],this.codes={},this.vars={},this.declarations={},this.flow={code:""},this.chaining=[],this.stack=un(),this.stacks=[],this.tab="	",this.currentFunctionNode=null,this.context={material:this.material},this.cache=new h0,this.globalCache=this.cache,this.flowsData=new WeakMap,this.shaderStage=null,this.buildStage=null}}class cp{_getMaps(e,t){let r=e.get(t);return void 0===r&&(r={renderMap:new WeakMap,frameMap:new WeakMap},e.set(t,r)),r}updateBeforeNode(e){let t=e.getUpdateBeforeType(),r=e.updateReference(this);if(t===E.FRAME){let{frameMap:t}=this._getMaps(this.updateBeforeMap,r);t.get(r)!==this.frameId&&!1!==e.updateBefore(this)&&t.set(r,this.frameId)}else if(t===E.RENDER){let{renderMap:t}=this._getMaps(this.updateBeforeMap,r);t.get(r)!==this.renderId&&!1!==e.updateBefore(this)&&t.set(r,this.renderId)}else t===E.OBJECT&&e.updateBefore(this)}updateAfterNode(e){let t=e.getUpdateAfterType(),r=e.updateReference(this);if(t===E.FRAME){let{frameMap:t}=this._getMaps(this.updateAfterMap,r);t.get(r)!==this.frameId&&!1!==e.updateAfter(this)&&t.set(r,this.frameId)}else if(t===E.RENDER){let{renderMap:t}=this._getMaps(this.updateAfterMap,r);t.get(r)!==this.renderId&&!1!==e.updateAfter(this)&&t.set(r,this.renderId)}else t===E.OBJECT&&e.updateAfter(this)}updateNode(e){let t=e.getUpdateType(),r=e.updateReference(this);if(t===E.FRAME){let{frameMap:t}=this._getMaps(this.updateMap,r);t.get(r)!==this.frameId&&!1!==e.update(this)&&t.set(r,this.frameId)}else if(t===E.RENDER){let{renderMap:t}=this._getMaps(this.updateMap,r);t.get(r)!==this.renderId&&!1!==e.update(this)&&t.set(r,this.renderId)}else t===E.OBJECT&&e.update(this)}update(){this.frameId++,void 0===this.lastTime&&(this.lastTime=performance.now()),this.deltaTime=(performance.now()-this.lastTime)/1e3,this.lastTime=performance.now(),this.time+=this.deltaTime}constructor(){this.time=0,this.deltaTime=0,this.frameId=0,this.renderId=0,this.updateMap=new WeakMap,this.updateBeforeMap=new WeakMap,this.updateAfterMap=new WeakMap,this.renderer=null,this.material=null,this.camera=null,this.object=null,this.scene=null}}class cg{constructor(e,t,r=null,i="",s=!1){this.type=e,this.name=t,this.count=r,this.qualifier=i,this.isConst=s}}cg.isNodeFunctionInput=!0;class cm extends hr{static get type(){return"DirectionalLightNode"}setupDirect(){let e=this.colorNode;return{lightDirection:dP(this.light),lightColor:e}}constructor(e=null){super(e)}}let cf=new u.kn4,cy=new u.kn4,cx=null;class cb extends hr{static get type(){return"RectAreaLightNode"}update(e){super.update(e);let{light:t}=this,r=e.camera.matrixWorldInverse;cy.identity(),cf.copy(t.matrixWorld),cf.premultiply(r),cy.extractRotation(cf),this.halfWidth.value.set(.5*t.width,0,0),this.halfHeight.value.set(0,.5*t.height,0),this.halfWidth.value.applyMatrix4(cy),this.halfHeight.value.applyMatrix4(cy)}setupDirectRectArea(e){let t,r;e.isAvailable("float32Filterable")?(t=iw(cx.LTC_FLOAT_1),r=iw(cx.LTC_FLOAT_2)):(t=iw(cx.LTC_HALF_1),r=iw(cx.LTC_HALF_2));let{colorNode:i,light:s}=this;return{lightColor:i,lightPosition:dB(s),halfWidth:this.halfWidth,halfHeight:this.halfHeight,ltc_1:t,ltc_2:r}}static setLTC(e){cx=e}constructor(e=null){super(e),this.halfHeight=tN(new u.Pq0).setGroup(tT),this.halfWidth=tN(new u.Pq0).setGroup(tT),this.updateType=E.RENDER}}class cT extends hr{static get type(){return"SpotLightNode"}update(e){super.update(e);let{light:t}=this;this.coneCosNode.value=Math.cos(t.angle),this.penumbraCosNode.value=Math.cos(t.angle*(1-t.penumbra)),this.cutoffDistanceNode.value=t.distance,this.decayExponentNode.value=t.decay}getSpotAttenuation(e){let{coneCosNode:t,penumbraCosNode:r}=this;return rH(t,r,e)}setupDirect(e){let{colorNode:t,cutoffDistanceNode:r,decayExponentNode:i,light:s}=this,n=this.getLightVector(e),a=n.normalize(),o=a.dot(dP(s)),l=this.getSpotAttenuation(o),u=hi({lightDistance:n.length(),cutoffDistance:r,decayExponent:i}),d=t.mul(l).mul(u);if(s.map){let t=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i3,r=dw(e).mul(t);return r.xyz.div(r.w)}(s,e.context.positionWorld),r=iw(s.map,t.xy).onRenderUpdate(()=>s.map);d=t.mul(2).sub(1).abs().lessThan(1).all().select(d.mul(r),d)}return{lightColor:d,lightDirection:a}}constructor(e=null){super(e),this.coneCosNode=tN(0).setGroup(tT),this.penumbraCosNode=tN(0).setGroup(tT),this.cutoffDistanceNode=tN(0).setGroup(tT),this.decayExponentNode=tN(0).setGroup(tT)}}class cv extends cT{static get type(){return"IESSpotLightNode"}getSpotAttenuation(e){let t=this.light.iesMap,r=null;return t&&!0===t.isTexture?iw(t,eI(e.acos().mul(1/Math.PI),0),0).r:super.getSpotAttenuation(e)}}class c_ extends hr{static get type(){return"AmbientLightNode"}setup(e){let{context:t}=e;t.irradiance.addAssign(this.colorNode)}constructor(e=null){super(e)}}class cN extends hr{static get type(){return"HemisphereLightNode"}update(e){let{light:t}=this;super.update(e),this.lightPositionNode.object3d=t,this.groundColorNode.value.copy(t.groundColor).multiplyScalar(t.intensity)}setup(e){let{colorNode:t,groundColorNode:r,lightDirectionNode:i}=this,s=rG(r,t,sr.dot(i).mul(.5).add(.5));e.context.irradiance.addAssign(s)}constructor(e=null){super(e),this.lightPositionNode=dM(e),this.lightDirectionNode=this.lightPositionNode.normalize(),this.groundColorNode=tN(new u.Q1f).setGroup(tT)}}class cS extends hr{static get type(){return"LightProbeNode"}update(e){let{light:t}=this;super.update(e);for(let e=0;e<9;e++)this.lightProbe.array[e].copy(t.sh.coefficients[e]).multiplyScalar(t.intensity)}setup(e){let t=hz(si,this.lightProbe);e.context.irradiance.addAssign(t)}constructor(e=null){super(e);let t=[];for(let e=0;e<9;e++)t.push(new u.Pq0);this.lightProbe=iL(t)}}class cR{parseFunction(){console.warn("Abstract function.")}}class cA{getCode(){console.warn("Abstract function.")}constructor(e,t,r="",i=""){this.type=e,this.inputs=t,this.name=r,this.precision=i}}cA.isNodeFunction=!0;let cE=/^\s*(highp|mediump|lowp)?\s*([a-z_0-9]+)\s*([a-z_0-9]+)?\s*\(([\s\S]*?)\)/i,cC=/[a-z_0-9]+/ig,cw="#pragma main",cM=e=>{let t=(e=e.trim()).indexOf(cw),r=-1!==t?e.slice(t+cw.length):e,i=r.match(cE);if(null!==i&&5===i.length){let s=i[4],n=[],a=null;for(;null!==(a=cC.exec(s));)n.push(a);let o=[],l=0;for(;l<n.length;){let e="const"===n[l][0];!0===e&&l++;let t=n[l][0];"in"===t||"out"===t||"inout"===t?l++:t="";let r=n[l++][0],i=Number.parseInt(n[l][0]);!1===Number.isNaN(i)?l++:i=null;let s=n[l++][0];o.push(new cg(r,s,i,t,e))}let u=r.substring(i[0].length),d=void 0!==i[3]?i[3]:"",h=i[2];return{type:h,inputs:o,name:d,precision:void 0!==i[1]?i[1]:"",inputsCode:s,blockCode:u,headerCode:-1!==t?e.slice(0,t):""}}throw Error("FunctionNode: Function is not a GLSL code.")};class cB extends cA{getCode(){let e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.name,r=this.blockCode;if(""!==r){let{type:i,inputsCode:s,headerCode:n,precision:a}=this,o="".concat(i," ").concat(t," ( ").concat(s.trim()," )");""!==a&&(o="".concat(a," ").concat(o)),e=n+o+r}else e="";return e}constructor(e){let{type:t,inputs:r,name:i,precision:s,inputsCode:n,blockCode:a,headerCode:o}=cM(e);super(t,r,i,s),this.inputsCode=n,this.blockCode=a,this.headerCode=o}}class cP extends cR{parseFunction(e){return new cB(e)}}let cF=new WeakMap,cU=[],cL=[];class cI extends lD{updateGroup(e){let t=e.groupNode,r=t.name;if(r===tv.name)return!0;if(r===tT.name){let t=this.get(e),r=this.nodeFrame.renderId;return t.renderId!==r&&(t.renderId=r,!0)}if(r===tb.name){let t=this.get(e),r=this.nodeFrame.frameId;return t.frameId!==r&&(t.frameId=r,!0)}cU[0]=t,cU[1]=e;let i=this.groupsData.get(cU);return void 0===i&&this.groupsData.set(cU,i={}),cU.length=0,i.version!==t.version&&(i.version=t.version,!0)}getForRenderCacheKey(e){return e.initialCacheKey}getForRender(e){let t=this.get(e),r=t.nodeBuilderState;if(void 0===r){let{nodeBuilderCache:i}=this,s=this.getForRenderCacheKey(e);if(void 0===(r=i.get(s))){let t=this.backend.createNodeBuilder(e.object,this.renderer);t.scene=e.scene,t.material=e.material,t.camera=e.camera,t.context.material=e.material,t.lightsNode=e.lightsNode,t.environmentNode=this.getEnvironmentNode(e.scene),t.fogNode=this.getFogNode(e.scene),t.clippingContext=e.clippingContext,this.renderer.getRenderTarget()&&this.renderer.getRenderTarget().multiview&&t.enableMultiview(),t.build(),r=this._createNodeBuilderState(t),i.set(s,r)}r.usedTimes++,t.nodeBuilderState=r}return r}delete(e){if(e.isRenderObject){let t=this.get(e).nodeBuilderState;t.usedTimes--,0===t.usedTimes&&this.nodeBuilderCache.delete(this.getForRenderCacheKey(e))}return super.delete(e)}getForCompute(e){let t=this.get(e),r=t.nodeBuilderState;if(void 0===r){let i=this.backend.createNodeBuilder(e,this.renderer);i.build(),t.nodeBuilderState=r=this._createNodeBuilderState(i)}return r}_createNodeBuilderState(e){return new hX(e.vertexShader,e.fragmentShader,e.computeShader,e.getAttributesArray(),e.getBindings(),e.updateNodes,e.updateBeforeNodes,e.updateAfterNodes,e.observer,e.transforms)}getEnvironmentNode(e){this.updateEnvironment(e);let t=null;if(e.environmentNode&&e.environmentNode.isNode)t=e.environmentNode;else{let r=this.get(e);r.environmentNode&&(t=r.environmentNode)}return t}getBackgroundNode(e){this.updateBackground(e);let t=null;if(e.backgroundNode&&e.backgroundNode.isNode)t=e.backgroundNode;else{let r=this.get(e);r.backgroundNode&&(t=r.backgroundNode)}return t}getFogNode(e){return this.updateFog(e),e.fogNode||this.get(e).fogNode||null}getCacheKey(e,t){cU[0]=e,cU[1]=t;let r=this.renderer.info.calls,i=this.callHashCache.get(cU)||{};if(i.callId!==r){let s=this.getEnvironmentNode(e),n=this.getFogNode(e);t&&cL.push(t.getCacheKey(!0)),s&&cL.push(s.getCacheKey()),n&&cL.push(n.getCacheKey()),cL.push(+!!this.renderer.shadowMap.enabled),i.callId=r,i.cacheKey=g(cL),this.callHashCache.set(cU,i),cL.length=0}return cU.length=0,i.cacheKey}get isToneMappingState(){return!this.renderer.getRenderTarget()}updateBackground(e){let t=this.get(e),r=e.background;if(r){let i=0===e.backgroundBlurriness&&t.backgroundBlurriness>0||e.backgroundBlurriness>0&&0===t.backgroundBlurriness;(t.background!==r||i)&&(t.backgroundNode=this.getCacheNode("background",r,()=>{if(!0===r.isCubeTexture||r.mapping===u.wfO||r.mapping===u.uV5||r.mapping===u.Om)if(e.backgroundBlurriness>0||r.mapping===u.Om)return ln(r);else{let e;return aC(!0===r.isCubeTexture?sb(r):iw(r))}if(!0===r.isTexture)return iw(r,nX.flipY()).setUpdateMatrix(!0);!0!==r.isColor&&console.error("WebGPUNodes: Unsupported background configuration.",r)},i),t.background=r,t.backgroundBlurriness=e.backgroundBlurriness)}else t.backgroundNode&&(delete t.backgroundNode,delete t.background)}getCacheNode(e,t,r){let i=arguments.length>3&&void 0!==arguments[3]&&arguments[3],s=this.cacheLib[e]||(this.cacheLib[e]=new WeakMap),n=s.get(t);return(void 0===n||i)&&(n=r(),s.set(t,n)),n}updateFog(e){let t=this.get(e),r=e.fog;r?t.fog!==r&&(t.fogNode=this.getCacheNode("fog",r,()=>{if(r.isFogExp2)return dx(s_("color","color",r).setGroup(tT),dy(s_("density","float",r).setGroup(tT)));if(r.isFog){let e=s_("color","color",r).setGroup(tT);return dx(e,df(s_("near","float",r).setGroup(tT),s_("far","float",r).setGroup(tT)))}console.error("THREE.Renderer: Unsupported fog configuration.",r)}),t.fog=r):(delete t.fogNode,delete t.fog)}updateEnvironment(e){let t=this.get(e),r=e.environment;r?t.environment!==r&&(t.environmentNode=this.getCacheNode("environment",r,()=>!0===r.isCubeTexture?sb(r):!0===r.isTexture?iw(r):void console.error("Nodes: Unsupported environment configuration.",r)),t.environment=r):t.environmentNode&&(delete t.environmentNode,delete t.environment)}getNodeFrame(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.renderer,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,n=this.nodeFrame;return n.renderer=e,n.scene=t,n.object=r,n.camera=i,n.material=s,n}getNodeFrameForRender(e){return this.getNodeFrame(e.renderer,e.scene,e.object,e.camera,e.material)}getOutputCacheKey(){let e=this.renderer;return e.toneMapping+","+e.currentColorSpace+","+e.xr.isPresenting}hasOutputChange(e){return cF.get(e)!==this.getOutputCacheKey()}getOutputNode(e){let t=this.renderer,r=this.getOutputCacheKey(),i=e.isTextureArray?uY(e,eG(nX,iD("gl_ViewID_OVR"))).renderOutput(t.toneMapping,t.currentColorSpace):iw(e,nX).renderOutput(t.toneMapping,t.currentColorSpace);return cF.set(e,r),i}updateBefore(e){for(let t of e.getNodeBuilderState().updateBeforeNodes)this.getNodeFrameForRender(e).updateBeforeNode(t)}updateAfter(e){for(let t of e.getNodeBuilderState().updateAfterNodes)this.getNodeFrameForRender(e).updateAfterNode(t)}updateForCompute(e){let t=this.getNodeFrame();for(let r of this.getForCompute(e).updateNodes)t.updateNode(r)}updateForRender(e){let t=this.getNodeFrameForRender(e);for(let r of e.getNodeBuilderState().updateNodes)t.updateNode(r)}needsRefresh(e){let t=this.getNodeFrameForRender(e);return e.getMonitor().needsRefresh(e,t)}dispose(){super.dispose(),this.nodeFrame=new cp,this.nodeBuilderCache=new Map,this.cacheLib={}}constructor(e,t){super(),this.renderer=e,this.backend=t,this.nodeFrame=new cp,this.nodeBuilderCache=new Map,this.callHashCache=new lP,this.groupsData=new lP,this.cacheLib={}}}let cD=new u.Zcv;class cO{projectPlanes(e,t,r){let i=e.length;for(let s=0;s<i;s++){cD.copy(e[s]).applyMatrix4(this.viewMatrix,this.viewNormalMatrix);let i=t[r+s],n=cD.normal;i.x=-n.x,i.y=-n.y,i.z=-n.z,i.w=cD.constant}}updateGlobal(e,t){this.shadowPass=null!==e.overrideMaterial&&e.overrideMaterial.isShadowPassMaterial,this.viewMatrix=t.matrixWorldInverse,this.viewNormalMatrix.getNormalMatrix(this.viewMatrix)}update(e,t){let r,i,s=!1;e.version!==this.parentVersion&&(this.intersectionPlanes=Array.from(e.intersectionPlanes),this.unionPlanes=Array.from(e.unionPlanes),this.parentVersion=e.version),this.clipIntersection!==t.clipIntersection&&(this.clipIntersection=t.clipIntersection,this.clipIntersection?this.unionPlanes.length=e.unionPlanes.length:this.intersectionPlanes.length=e.intersectionPlanes.length);let n=t.clippingPlanes,a=n.length;if(this.clipIntersection?(r=this.intersectionPlanes,i=e.intersectionPlanes.length):(r=this.unionPlanes,i=e.unionPlanes.length),r.length!==i+a){r.length=i+a;for(let e=0;e<a;e++)r[i+e]=new u.IUQ;s=!0}this.projectPlanes(n,r,i),s&&(this.version++,this.cacheKey="".concat(this.intersectionPlanes.length,":").concat(this.unionPlanes.length))}getGroupContext(e){if(this.shadowPass&&!e.clipShadows)return this;let t=this.clippingGroupContexts.get(e);return void 0===t&&(t=new cO(this),this.clippingGroupContexts.set(e,t)),t.update(this,e),t}get unionClippingCount(){return this.unionPlanes.length}constructor(e=null){this.version=0,this.clipIntersection=null,this.cacheKey="",this.shadowPass=!1,this.viewNormalMatrix=new u.dwI,this.clippingGroupContexts=new WeakMap,this.intersectionPlanes=[],this.unionPlanes=[],this.parentVersion=null,null!==e&&(this.viewNormalMatrix=e.viewNormalMatrix,this.clippingGroupContexts=e.clippingGroupContexts,this.shadowPass=e.shadowPass,this.viewMatrix=e.viewMatrix)}}class cV{constructor(e,t){this.bundleGroup=e,this.camera=t}}let cG=[];class ck{get(e,t){let r=this.bundles;cG[0]=e,cG[1]=t;let i=r.get(cG);return void 0===i&&(i=new cV(e,t),r.set(cG,i)),cG.length=0,i}dispose(){this.bundles=new lP}constructor(){this.bundles=new lP}}class cz{fromMaterial(e){if(e.isNodeMaterial)return e;let t=null,r=this.getMaterialNodeClass(e.type);if(null!==r)for(let i in t=new r,e)t[i]=e[i];return t}addToneMapping(e,t){this.addType(e,t,this.toneMappingNodes)}getToneMappingFunction(e){return this.toneMappingNodes.get(e)||null}getMaterialNodeClass(e){return this.materialNodes.get(e)||null}addMaterial(e,t){this.addType(e,t,this.materialNodes)}getLightNodeClass(e){return this.lightNodes.get(e)||null}addLight(e,t){this.addClass(e,t,this.lightNodes)}addType(e,t,r){if(r.has(t))return void console.warn("Redefinition of node ".concat(t));if("function"!=typeof e)throw Error("Node class ".concat(e.name," is not a class."));if("function"==typeof t||"object"==typeof t)throw Error("Base class ".concat(t," is not a class."));r.set(t,e)}addClass(e,t,r){if(r.has(t))return void console.warn("Redefinition of node ".concat(t.name));if("function"!=typeof e)throw Error("Node class ".concat(e.name," is not a class."));if("function"!=typeof t)throw Error("Base class ".concat(t.name," is not a class."));r.set(t,e)}constructor(){this.lightNodes=new WeakMap,this.materialNodes=new Map,this.toneMappingNodes=new Map}}let cH=new dI,cW=[];class cq extends lP{createNode(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return new dI().setLights(e)}getNode(e,t){if(e.isQuadMesh)return cH;cW[0]=e,cW[1]=t;let r=this.get(cW);return void 0===r&&(r=this.createNode(),this.set(cW,r)),cW.length=0,r}constructor(){super()}}class cj extends u.O0B{copy(e){return super.copy(e),this.hasExternalTextures=e.hasExternalTextures,this.autoAllocateDepthBuffer=e.autoAllocateDepthBuffer,this}constructor(e=1,t=1,r={}){super(e,t,r),this.isXRRenderTarget=!0,this.hasExternalTextures=!1,this.autoAllocateDepthBuffer=!0}}let cX=new u.Pq0,cQ=new u.Pq0;class cY extends u.Qev{getController(e){return this._getController(e).getTargetRaySpace()}getControllerGrip(e){return this._getController(e).getGripSpace()}getHand(e){return this._getController(e).getHandSpace()}getFoveation(){if(null!==this._glProjLayer||null!==this._glBaseLayer)return this._foveation}setFoveation(e){this._foveation=e,null!==this._glProjLayer&&(this._glProjLayer.fixedFoveation=e),null!==this._glBaseLayer&&void 0!==this._glBaseLayer.fixedFoveation&&(this._glBaseLayer.fixedFoveation=e)}getFramebufferScaleFactor(){return this._framebufferScaleFactor}setFramebufferScaleFactor(e){this._framebufferScaleFactor=e,!0===this.isPresenting&&console.warn("THREE.XRManager: Cannot change framebuffer scale while presenting.")}getReferenceSpaceType(){return this._referenceSpaceType}setReferenceSpaceType(e){this._referenceSpaceType=e,!0===this.isPresenting&&console.warn("THREE.XRManager: Cannot change reference space type while presenting.")}getReferenceSpace(){return this._customReferenceSpace||this._referenceSpace}setReferenceSpace(e){this._customReferenceSpace=e}getCamera(){return this._cameraXR}getEnvironmentBlendMode(){if(null!==this._session)return this._session.environmentBlendMode}getFrame(){return this._xrFrame}useMultiview(){return this._useMultiview}createQuadLayer(e,t,r,i,s,n,a){let o=arguments.length>7&&void 0!==arguments[7]?arguments[7]:[],l=new u.bdM(e,t),d=new cj(s,n,{format:u.GWd,type:u.OUM,depthTexture:new u.VCu(s,n,o.stencil?u.V3x:u.bkx,void 0,void 0,void 0,void 0,void 0,void 0,o.stencil?u.dcC:u.zdS),stencilBuffer:o.stencil,resolveDepthBuffer:!1,resolveStencilBuffer:!1}),h=new u.V9B({color:0xffffff,side:u.hB5});h.map=d.texture,h.map.offset.y=1,h.map.repeat.y=-1;let c=new u.eaF(l,h);c.position.copy(r),c.quaternion.copy(i);let p={type:"quad",width:e,height:t,translation:r,quaternion:i,pixelwidth:s,pixelheight:n,plane:c,material:h,rendercall:a,renderTarget:d};if(this._layers.push(p),null!==this._session){p.plane.material=new u.V9B({color:0xffffff,side:u.hB5}),p.plane.material.blending=u.bCz,p.plane.material.blendEquation=u.gO9,p.plane.material.blendSrc=u.ojh,p.plane.material.blendDst=u.ojh,p.xrlayer=this._createXRLayer(p);let e=this._session.renderState.layers;e.unshift(p.xrlayer),this._session.updateRenderState({layers:e})}else d.isXRRenderTarget=!1;return c}createCylinderLayer(e,t,r,i,s,n,a,o){let l=arguments.length>8&&void 0!==arguments[8]?arguments[8]:[],d=new u.Ho_(e,e,e*t/r,64,64,!0,Math.PI-t/2,t),h=new cj(n,a,{format:u.GWd,type:u.OUM,depthTexture:new u.VCu(n,a,l.stencil?u.V3x:u.bkx,void 0,void 0,void 0,void 0,void 0,void 0,l.stencil?u.dcC:u.zdS),stencilBuffer:l.stencil,resolveDepthBuffer:!1,resolveStencilBuffer:!1}),c=new u.V9B({color:0xffffff,side:u.hsX});c.map=h.texture,c.map.offset.y=1,c.map.repeat.y=-1;let p=new u.eaF(d,c);p.position.copy(i),p.quaternion.copy(s);let g={type:"cylinder",radius:e,centralAngle:t,aspectratio:r,translation:i,quaternion:s,pixelwidth:n,pixelheight:a,plane:p,material:c,rendercall:o,renderTarget:h};if(this._layers.push(g),null!==this._session){g.plane.material=new u.V9B({color:0xffffff,side:u.hsX}),g.plane.material.blending=u.bCz,g.plane.material.blendEquation=u.gO9,g.plane.material.blendSrc=u.ojh,g.plane.material.blendDst=u.ojh,g.xrlayer=this._createXRLayer(g);let e=this._session.renderState.layers;e.unshift(g.xrlayer),this._session.updateRenderState({layers:e})}else h.isXRRenderTarget=!1;return p}renderLayers(){let e=new u.Pq0,t=new u.PTz,r=this.isPresenting;for(let r of(this.isPresenting=!1,this._layers)){if(r.renderTarget.isXRRenderTarget=null!==this._session,r.renderTarget.hasExternalTextures=r.renderTarget.isXRRenderTarget,r.renderTarget.autoAllocateDepthBuffer=!r.renderTarget.isXRRenderTarget,r.renderTarget.isXRRenderTarget&&this._supportsLayers){r.xrlayer.transform=new XRRigidTransform(r.plane.getWorldPosition(e),r.plane.getWorldQuaternion(t));let i=this._glBinding.getSubImage(r.xrlayer,this._xrFrame);this._renderer.backend.setXRRenderTargetTextures(r.renderTarget,i.colorTexture,i.depthStencilTexture)}this._renderer.setRenderTarget(r.renderTarget),r.rendercall()}this.isPresenting=r,this._renderer.setRenderTarget(null)}getSession(){return this._session}async setSession(e){let t=this._renderer,r=t.backend;this._gl=t.getContext();let i=this._gl,s=i.getContextAttributes();if(this._session=e,null!==e){if(!0===r.isWebGPUBackend)throw Error('THREE.XRManager: XR is currently not supported with a WebGPU backend. Use WebGL by passing "{ forceWebGL: true }" to the constructor of the renderer.');if(e.addEventListener("select",this._onSessionEvent),e.addEventListener("selectstart",this._onSessionEvent),e.addEventListener("selectend",this._onSessionEvent),e.addEventListener("squeeze",this._onSessionEvent),e.addEventListener("squeezestart",this._onSessionEvent),e.addEventListener("squeezeend",this._onSessionEvent),e.addEventListener("end",this._onSessionEnd),e.addEventListener("inputsourceschange",this._onInputSourcesChange),await r.makeXRCompatible(),this._currentPixelRatio=t.getPixelRatio(),t.getSize(this._currentSize),this._currentAnimationContext=t._animation.getContext(),this._currentAnimationLoop=t._animation.getAnimationLoop(),t._animation.stop(),!0===this._useLayers){let r,n=null,a=null,o=null;t.depth&&(o=t.stencil?i.DEPTH24_STENCIL8:i.DEPTH_COMPONENT24,n=t.stencil?u.dcC:u.zdS,a=t.stencil?u.V3x:u.bkx);let l={colorFormat:i.RGBA8,depthFormat:o,scaleFactor:this._framebufferScaleFactor};this._useMultiviewIfPossible&&t.hasFeature("OVR_multiview2")&&(l.textureType="texture-array",this._useMultiview=!0);let d=new XRWebGLBinding(e,i),h=d.createProjectionLayer(l),c=[h];if(this._glBinding=d,this._glProjLayer=h,t.setPixelRatio(1),t.setSize(h.textureWidth,h.textureHeight,!1),this._useMultiview?((r=new u.S7T(h.textureWidth,h.textureHeight,2)).type=a,r.format=n):r=new u.VCu(h.textureWidth,h.textureHeight,a,void 0,void 0,void 0,void 0,void 0,void 0,n),this._xrRenderTarget=new cj(h.textureWidth,h.textureHeight,{format:u.GWd,type:u.OUM,colorSpace:t.outputColorSpace,depthTexture:r,stencilBuffer:t.stencil,samples:4*!!s.antialias,resolveDepthBuffer:!1===h.ignoreDepthValues,resolveStencilBuffer:!1===h.ignoreDepthValues,depth:this._useMultiview?2:1,multiview:this._useMultiview}),this._xrRenderTarget.hasExternalTextures=!0,this._xrRenderTarget.depth=this._useMultiview?2:1,this._supportsLayers=e.enabledFeatures.includes("layers"),this._referenceSpace=await e.requestReferenceSpace(this.getReferenceSpaceType()),this._supportsLayers)for(let e of this._layers)e.plane.material=new u.V9B({color:0xffffff,side:"cylinder"===e.type?u.hsX:u.hB5}),e.plane.material.blending=u.bCz,e.plane.material.blendEquation=u.gO9,e.plane.material.blendSrc=u.ojh,e.plane.material.blendDst=u.ojh,e.xrlayer=this._createXRLayer(e),c.unshift(e.xrlayer);e.updateRenderState({layers:c})}else{let r=new XRWebGLLayer(e,i,{antialias:t.samples>0,alpha:!0,depth:t.depth,stencil:t.stencil,framebufferScaleFactor:this.getFramebufferScaleFactor()});this._glBaseLayer=r,e.updateRenderState({baseLayer:r}),t.setPixelRatio(1),t.setSize(r.framebufferWidth,r.framebufferHeight,!1),this._xrRenderTarget=new cj(r.framebufferWidth,r.framebufferHeight,{format:u.GWd,type:u.OUM,colorSpace:t.outputColorSpace,stencilBuffer:t.stencil,resolveDepthBuffer:!1===r.ignoreDepthValues,resolveStencilBuffer:!1===r.ignoreDepthValues}),this._referenceSpace=await e.requestReferenceSpace(this.getReferenceSpaceType())}this.setFoveation(this.getFoveation()),t._animation.setAnimationLoop(this._onAnimationFrame),t._animation.setContext(e),t._animation.start(),this.isPresenting=!0,this.dispatchEvent({type:"sessionstart"})}}updateCamera(e){var t,r,i;let s=this._session;if(null===s)return;let n=e.near,a=e.far,o=this._cameraXR,l=this._cameraL,d=this._cameraR;o.near=d.near=l.near=n,o.far=d.far=l.far=a,o.isMultiViewCamera=this._useMultiview,(this._currentDepthNear!==o.near||this._currentDepthFar!==o.far)&&(s.updateRenderState({depthNear:o.near,depthFar:o.far}),this._currentDepthNear=o.near,this._currentDepthFar=o.far),l.layers.mask=2|e.layers.mask,d.layers.mask=4|e.layers.mask,o.layers.mask=l.layers.mask|d.layers.mask;let h=e.parent,c=o.cameras;cK(o,h);for(let e=0;e<c.length;e++)cK(c[e],h);2===c.length?function(e,t,r){cX.setFromMatrixPosition(t.matrixWorld),cQ.setFromMatrixPosition(r.matrixWorld);let i=cX.distanceTo(cQ),s=t.projectionMatrix.elements,n=r.projectionMatrix.elements,a=s[14]/(s[10]-1),o=s[14]/(s[10]+1),l=(s[9]+1)/s[5],u=(s[9]-1)/s[5],d=(s[8]-1)/s[0],h=(n[8]+1)/n[0],c=i/(-d+h),p=-(c*d);if(t.matrixWorld.decompose(e.position,e.quaternion,e.scale),e.translateX(p),e.translateZ(c),e.matrixWorld.compose(e.position,e.quaternion,e.scale),e.matrixWorldInverse.copy(e.matrixWorld).invert(),-1===s[10])e.projectionMatrix.copy(t.projectionMatrix),e.projectionMatrixInverse.copy(t.projectionMatrixInverse);else{let t=a+c,r=o+c;e.projectionMatrix.makePerspective(a*d-p,a*h+(i-p),l*o/r*t,u*o/r*t,t,r),e.projectionMatrixInverse.copy(e.projectionMatrix).invert()}}(o,l,d):o.projectionMatrix.copy(l.projectionMatrix),t=e,r=o,null===(i=h)?t.matrix.copy(r.matrixWorld):(t.matrix.copy(i.matrixWorld),t.matrix.invert(),t.matrix.multiply(r.matrixWorld)),t.matrix.decompose(t.position,t.quaternion,t.scale),t.updateMatrixWorld(!0),t.projectionMatrix.copy(r.projectionMatrix),t.projectionMatrixInverse.copy(r.projectionMatrixInverse),t.isPerspectiveCamera&&(t.fov=2*u.a55*Math.atan(1/t.projectionMatrix.elements[5]),t.zoom=1)}_getController(e){let t=this._controllers[e];return void 0===t&&(t=new u.R3r,this._controllers[e]=t),t}constructor(e,t=!1){super(),this.enabled=!1,this.isPresenting=!1,this.cameraAutoUpdate=!0,this._renderer=e,this._cameraL=new u.ubm,this._cameraL.viewport=new u.IUQ,this._cameraR=new u.ubm,this._cameraR.viewport=new u.IUQ,this._cameras=[this._cameraL,this._cameraR],this._cameraXR=new u.nZQ,this._currentDepthNear=null,this._currentDepthFar=null,this._controllers=[],this._controllerInputSources=[],this._xrRenderTarget=null,this._layers=[],this._supportsLayers=!1,this._createXRLayer=c0.bind(this),this._gl=null,this._currentAnimationContext=null,this._currentAnimationLoop=null,this._currentPixelRatio=null,this._currentSize=new u.I9Y,this._onSessionEvent=cZ.bind(this),this._onSessionEnd=c$.bind(this),this._onInputSourcesChange=cJ.bind(this),this._onAnimationFrame=c1.bind(this),this._referenceSpace=null,this._referenceSpaceType="local-floor",this._customReferenceSpace=null,this._framebufferScaleFactor=1,this._foveation=1,this._session=null,this._glBaseLayer=null,this._glBinding=null,this._glProjLayer=null,this._xrFrame=null,this._useLayers="undefined"!=typeof XRWebGLBinding&&"createProjectionLayer"in XRWebGLBinding.prototype,this._useMultiviewIfPossible=t,this._useMultiview=!1}}function cK(e,t){null===t?e.matrixWorld.copy(e.matrix):e.matrixWorld.multiplyMatrices(t.matrixWorld,e.matrix),e.matrixWorldInverse.copy(e.matrixWorld).invert()}function cZ(e){let t=this._controllerInputSources.indexOf(e.inputSource);if(-1===t)return;let r=this._controllers[t];if(void 0!==r){let t=this.getReferenceSpace();r.update(e.inputSource,e.frame,t),r.dispatchEvent({type:e.type,data:e.inputSource})}}function c$(){let e=this._session,t=this._renderer;e.removeEventListener("select",this._onSessionEvent),e.removeEventListener("selectstart",this._onSessionEvent),e.removeEventListener("selectend",this._onSessionEvent),e.removeEventListener("squeeze",this._onSessionEvent),e.removeEventListener("squeezestart",this._onSessionEvent),e.removeEventListener("squeezeend",this._onSessionEvent),e.removeEventListener("end",this._onSessionEnd),e.removeEventListener("inputsourceschange",this._onInputSourcesChange);for(let e=0;e<this._controllers.length;e++){let t=this._controllerInputSources[e];null!==t&&(this._controllerInputSources[e]=null,this._controllers[e].disconnect(t))}if(this._currentDepthNear=null,this._currentDepthFar=null,t.backend.setXRTarget(null),t.setOutputRenderTarget(null),t.setRenderTarget(null),this._session=null,this._xrRenderTarget=null,!0===this._supportsLayers)for(let e of this._layers)e.renderTarget=new cj(e.pixelwidth,e.pixelheight,{format:u.GWd,type:u.OUM,depthTexture:new u.VCu(e.pixelwidth,e.pixelheight,e.stencilBuffer?u.V3x:u.bkx,void 0,void 0,void 0,void 0,void 0,void 0,e.stencilBuffer?u.dcC:u.zdS),stencilBuffer:e.stencilBuffer,resolveDepthBuffer:!1,resolveStencilBuffer:!1}),e.renderTarget.isXRRenderTarget=!1,e.plane.material=e.material,e.material.map=e.renderTarget.texture,delete e.xrlayer;this.isPresenting=!1,this._useMultiview=!1,t._animation.stop(),t._animation.setAnimationLoop(this._currentAnimationLoop),t._animation.setContext(this._currentAnimationContext),t._animation.start(),t.setPixelRatio(this._currentPixelRatio),t.setSize(this._currentSize.width,this._currentSize.height,!1),this.dispatchEvent({type:"sessionend"})}function cJ(e){let t=this._controllers,r=this._controllerInputSources;for(let i=0;i<e.removed.length;i++){let s=e.removed[i],n=r.indexOf(s);n>=0&&(r[n]=null,t[n].disconnect(s))}for(let i=0;i<e.added.length;i++){let s=e.added[i],n=r.indexOf(s);if(-1===n){for(let e=0;e<t.length;e++)if(e>=r.length){r.push(s),n=e;break}else if(null===r[e]){r[e]=s,n=e;break}if(-1===n)break}let a=t[n];a&&a.connect(s)}}function c0(e){return"quad"===e.type?this._glBinding.createQuadLayer({transform:new XRRigidTransform(e.translation,e.quaternion),depthFormat:this._gl.DEPTH_COMPONENT,width:e.width/2,height:e.height/2,space:this._referenceSpace,viewPixelWidth:e.pixelwidth,viewPixelHeight:e.pixelheight}):this._glBinding.createCylinderLayer({transform:new XRRigidTransform(e.translation,e.quaternion),depthFormat:this._gl.DEPTH_COMPONENT,radius:e.radius,centralAngle:e.centralAngle,aspectRatio:e.aspectRatio,space:this._referenceSpace,viewPixelWidth:e.pixelwidth,viewPixelHeight:e.pixelheight})}function c1(e,t){if(void 0===t)return;let r=this._cameraXR,i=this._renderer,s=i.backend,n=this._glBaseLayer,a=this.getReferenceSpace(),o=t.getViewerPose(a);if(this._xrFrame=t,null!==o){let e=o.views;null!==this._glBaseLayer&&s.setXRTarget(n.framebuffer);let t=!1;e.length!==r.cameras.length&&(r.cameras.length=0,t=!0);for(let i=0;i<e.length;i++){let a,o=e[i];if(!0===this._useLayers){let e=this._glBinding.getViewSubImage(this._glProjLayer,o);a=e.viewport,0===i&&s.setXRRenderTargetTextures(this._xrRenderTarget,e.colorTexture,this._glProjLayer.ignoreDepthValues&&!this._useMultiview?void 0:e.depthStencilTexture)}else a=n.getViewport(o);let l=this._cameras[i];void 0===l&&((l=new u.ubm).layers.enable(i),l.viewport=new u.IUQ,this._cameras[i]=l),l.matrix.fromArray(o.transform.matrix),l.matrix.decompose(l.position,l.quaternion,l.scale),l.projectionMatrix.fromArray(o.projectionMatrix),l.projectionMatrixInverse.copy(l.projectionMatrix).invert(),l.viewport.set(a.x,a.y,a.width,a.height),0===i&&(r.matrix.copy(l.matrix),r.matrix.decompose(r.position,r.quaternion,r.scale)),!0===t&&r.cameras.push(l)}i.setOutputRenderTarget(this._xrRenderTarget)}for(let e=0;e<this._controllers.length;e++){let r=this._controllerInputSources[e],i=this._controllers[e];null!==r&&void 0!==i&&i.update(r,t,a)}this._currentAnimationLoop&&this._currentAnimationLoop(e,t),t.detectedPlanes&&this.dispatchEvent({type:"planesdetected",data:t}),this._xrFrame=null}let c2=new u.Z58,c3=new u.I9Y,c4=new u.IUQ,c6=new u.PPD,c8=new u.uf3,c5=new u.kn4,c9=new u.IUQ;class c7{async init(){if(this._initialized)throw Error("Renderer: Backend has already been initialized.");return null!==this._initPromise||(this._initPromise=new Promise(async(e,t)=>{let r=this.backend;try{await r.init(this)}catch(e){if(null===this._getFallback)return void t(e);try{this.backend=r=this._getFallback(e),await r.init(this)}catch(e){t(e);return}}this._nodes=new cI(this,r),this._animation=new lB(this._nodes,this.info),this._attributes=new lV(r),this._background=new hW(this,this._nodes),this._geometries=new lz(this._attributes,this.info),this._textures=new ut(this,r,this.info),this._pipelines=new lY(r,this._nodes),this._bindings=new lK(r,this._nodes,this._textures,this._attributes,this._pipelines,this.info),this._objects=new lI(this,this._nodes,this._geometries,this._pipelines,this._bindings,this.info),this._renderLists=new l2(this.lighting),this._bundles=new ck,this._renderContexts=new l7,this._animation.start(),this._initialized=!0,e(this)})),this._initPromise}get coordinateSystem(){return this.backend.coordinateSystem}async compileAsync(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(!0===this._isDeviceLost)return;!1===this._initialized&&await this.init();let i=this._nodes.nodeFrame,s=i.renderId,n=this._currentRenderContext,a=this._currentRenderObjectFunction,o=this._compilationPromises,l=!0===e.isScene?e:c2;null===r&&(r=e);let u=this._renderTarget,d=this._renderContexts.get(r,t,u),h=this._activeMipmapLevel,c=[];this._currentRenderContext=d,this._currentRenderObjectFunction=this.renderObject,this._handleObjectFunction=this._createObjectPipeline,this._compilationPromises=c,i.renderId++,i.update(),d.depth=this.depth,d.stencil=this.stencil,d.clippingContext||(d.clippingContext=new cO),d.clippingContext.updateGlobal(l,t),l.onBeforeRender(this,e,t,u);let p=this._renderLists.get(e,t);if(p.begin(),this._projectObject(e,t,0,p,d.clippingContext),r!==e&&r.traverseVisible(function(e){e.isLight&&e.layers.test(t.layers)&&p.pushLight(e)}),p.finish(),null!==u){this._textures.updateRenderTarget(u,h);let e=this._textures.get(u);d.textures=e.textures,d.depthTexture=e.depthTexture}else d.textures=null,d.depthTexture=null;this._background.update(l,p,d);let g=p.opaque,m=p.transparent,f=p.transparentDoublePass,y=p.lightsNode;!0===this.opaque&&g.length>0&&this._renderObjects(g,t,l,y),!0===this.transparent&&m.length>0&&this._renderTransparents(m,f,t,l,y),i.renderId=s,this._currentRenderContext=n,this._currentRenderObjectFunction=a,this._compilationPromises=o,this._handleObjectFunction=this._renderObjectDirect,await Promise.all(c)}async renderAsync(e,t){!1===this._initialized&&await this.init(),this._renderScene(e,t)}async waitForGPU(){await this.backend.waitForGPU()}set highPrecision(e){!0===e?(this.overrideNodes.modelViewMatrix=i$,this.overrideNodes.modelNormalViewMatrix=iJ):this.highPrecision&&(this.overrideNodes.modelViewMatrix=null,this.overrideNodes.modelNormalViewMatrix=null)}get highPrecision(){return this.overrideNodes.modelViewMatrix===i$&&this.overrideNodes.modelNormalViewMatrix===iJ}setMRT(e){return this._mrt=e,this}getMRT(){return this._mrt}getColorBufferType(){return this._colorBufferType}_onDeviceLost(e){let t="THREE.WebGPURenderer: ".concat(e.api," Device Lost:\n\nMessage: ").concat(e.message);e.reason&&(t+="\nReason: ".concat(e.reason)),console.error(t),this._isDeviceLost=!0}_renderBundle(e,t,r){let{bundleGroup:i,camera:s,renderList:n}=e,a=this._currentRenderContext,o=this._bundles.get(i,s),l=this.backend.get(o);void 0===l.renderContexts&&(l.renderContexts=new Set);let u=i.version!==l.version,d=!1===l.renderContexts.has(a)||u;if(l.renderContexts.add(a),d){this.backend.beginBundle(a),(void 0===l.renderObjects||u)&&(l.renderObjects=[]),this._currentRenderBundle=o;let{transparentDoublePass:e,transparent:d,opaque:h}=n;!0===this.opaque&&h.length>0&&this._renderObjects(h,s,t,r),!0===this.transparent&&d.length>0&&this._renderTransparents(d,e,s,t,r),this._currentRenderBundle=null,this.backend.finishBundle(a,o),l.version=i.version}else{let{renderObjects:e}=l;for(let t=0,r=e.length;t<r;t++){let r=e[t];this._nodes.needsRefresh(r)&&(this._nodes.updateBefore(r),this._nodes.updateForRender(r),this._bindings.updateForRender(r),this._nodes.updateAfter(r))}}this.backend.addBundle(a,o)}render(e,t){if(!1===this._initialized)return console.warn("THREE.Renderer: .render() called before the backend is initialized. Try using .renderAsync() instead."),this.renderAsync(e,t);this._renderScene(e,t)}_getFrameBufferTarget(){let{currentToneMapping:e,currentColorSpace:t}=this,r=e!==u.y_p,i=t!==u.Zr2;if(!1===r&&!1===i)return null;let{width:s,height:n}=this.getDrawingBufferSize(c3),{depth:a,stencil:o}=this,l=this._frameBufferTarget;null===l&&((l=new u.O0B(s,n,{depthBuffer:a,stencilBuffer:o,type:this._colorBufferType,format:u.GWd,colorSpace:u.Zr2,generateMipmaps:!1,minFilter:u.k6q,magFilter:u.k6q,samples:this.samples})).isPostProcessingRenderTarget=!0,this._frameBufferTarget=l);let d=this.getOutputRenderTarget();return l.depthBuffer=a,l.stencilBuffer=o,l.setSize(s,n,null!==d?d.depth:1),l.viewport.copy(this._viewport),l.scissor.copy(this._scissor),l.viewport.multiplyScalar(this._pixelRatio),l.scissor.multiplyScalar(this._pixelRatio),l.scissorTest=this._scissorTest,l.multiview=null!==d&&d.multiview,l}_renderScene(e,t){let r,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!0===this._isDeviceLost)return;let s=i?this._getFrameBufferTarget():null,n=this._nodes.nodeFrame,a=n.renderId,o=this._currentRenderContext,l=this._currentRenderObjectFunction,u=!0===e.isScene?e:c2,d=this._renderTarget||this._outputRenderTarget,h=this._activeCubeFace,c=this._activeMipmapLevel;null!==s?(r=s,this.setRenderTarget(r)):r=d;let p=this._renderContexts.get(e,t,r);this._currentRenderContext=p,this._currentRenderObjectFunction=this._renderObjectFunction||this.renderObject,this.info.calls++,this.info.render.calls++,this.info.render.frameCalls++,n.renderId=this.info.calls;let g=this.coordinateSystem,m=this.xr;if(t.coordinateSystem!==g&&!1===m.isPresenting&&(t.coordinateSystem=g,t.updateProjectionMatrix(),t.isArrayCamera))for(let e of t.cameras)e.coordinateSystem=g,e.updateProjectionMatrix();!0===e.matrixWorldAutoUpdate&&e.updateMatrixWorld(),null===t.parent&&!0===t.matrixWorldAutoUpdate&&t.updateMatrixWorld(),!0===m.enabled&&!0===m.isPresenting&&(!0===m.cameraAutoUpdate&&m.updateCamera(t),t=m.getCamera());let f=this._viewport,y=this._scissor,x=this._pixelRatio;null!==r&&(f=r.viewport,y=r.scissor,x=1),this.getDrawingBufferSize(c3),c4.set(0,0,c3.width,c3.height);let b=void 0===f.minDepth?0:f.minDepth,T=void 0===f.maxDepth?1:f.maxDepth;p.viewportValue.copy(f).multiplyScalar(x).floor(),p.viewportValue.width>>=c,p.viewportValue.height>>=c,p.viewportValue.minDepth=b,p.viewportValue.maxDepth=T,p.viewport=!1===p.viewportValue.equals(c4),p.scissorValue.copy(y).multiplyScalar(x).floor(),p.scissor=this._scissorTest&&!1===p.scissorValue.equals(c4),p.scissorValue.width>>=c,p.scissorValue.height>>=c,p.clippingContext||(p.clippingContext=new cO),p.clippingContext.updateGlobal(u,t),u.onBeforeRender(this,e,t,r);let v=t.isArrayCamera?c8:c6;t.isArrayCamera||(c5.multiplyMatrices(t.projectionMatrix,t.matrixWorldInverse),v.setFromProjectionMatrix(c5,g));let _=this._renderLists.get(e,t);if(_.begin(),this._projectObject(e,t,0,_,p.clippingContext),_.finish(),!0===this.sortObjects&&_.sort(this._opaqueSort,this._transparentSort),null!==r){this._textures.updateRenderTarget(r,c);let e=this._textures.get(r);p.textures=e.textures,p.depthTexture=e.depthTexture,p.width=e.width,p.height=e.height,p.renderTarget=r,p.depth=r.depthBuffer,p.stencil=r.stencilBuffer}else p.textures=null,p.depthTexture=null,p.width=this.domElement.width,p.height=this.domElement.height,p.depth=this.depth,p.stencil=this.stencil;p.width>>=c,p.height>>=c,p.activeCubeFace=h,p.activeMipmapLevel=c,p.occlusionQueryCount=_.occlusionQueryCount,this._background.update(u,_,p),p.camera=t,this.backend.beginRender(p);let{bundles:N,lightsNode:S,transparentDoublePass:R,transparent:A,opaque:E}=_;return N.length>0&&this._renderBundles(N,u,S),!0===this.opaque&&E.length>0&&this._renderObjects(E,t,u,S),!0===this.transparent&&A.length>0&&this._renderTransparents(A,R,t,u,S),this.backend.finishRender(p),n.renderId=a,this._currentRenderContext=o,this._currentRenderObjectFunction=l,null!==s&&(this.setRenderTarget(d,h,c),this._renderOutput(r)),u.onAfterRender(this,e,t,r),p}_renderOutput(e){let t=this._quad;this._nodes.hasOutputChange(e.texture)&&(t.material.fragmentNode=this._nodes.getOutputNode(e.texture),t.material.needsUpdate=!0);let r=this.autoClear,i=this.xr.enabled;this.autoClear=!1,this.xr.enabled=!1,this._renderScene(t,t.camera,!1),this.autoClear=r,this.xr.enabled=i}getMaxAnisotropy(){return this.backend.getMaxAnisotropy()}getActiveCubeFace(){return this._activeCubeFace}getActiveMipmapLevel(){return this._activeMipmapLevel}async setAnimationLoop(e){!1===this._initialized&&await this.init(),this._animation.setAnimationLoop(e)}async getArrayBufferAsync(e){return await this.backend.getArrayBufferAsync(e)}getContext(){return this.backend.getContext()}getPixelRatio(){return this._pixelRatio}getDrawingBufferSize(e){return e.set(this._width*this._pixelRatio,this._height*this._pixelRatio).floor()}getSize(e){return e.set(this._width,this._height)}setPixelRatio(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;this._pixelRatio!==e&&(this._pixelRatio=e,this.setSize(this._width,this._height,!1))}setDrawingBufferSize(e,t,r){this.xr&&this.xr.isPresenting||(this._width=e,this._height=t,this._pixelRatio=r,this.domElement.width=Math.floor(e*r),this.domElement.height=Math.floor(t*r),this.setViewport(0,0,e,t),this._initialized&&this.backend.updateSize())}setSize(e,t){let r=!(arguments.length>2)||void 0===arguments[2]||arguments[2];this.xr&&this.xr.isPresenting||(this._width=e,this._height=t,this.domElement.width=Math.floor(e*this._pixelRatio),this.domElement.height=Math.floor(t*this._pixelRatio),!0===r&&(this.domElement.style.width=e+"px",this.domElement.style.height=t+"px"),this.setViewport(0,0,e,t),this._initialized&&this.backend.updateSize())}setOpaqueSort(e){this._opaqueSort=e}setTransparentSort(e){this._transparentSort=e}getScissor(e){let t=this._scissor;return e.x=t.x,e.y=t.y,e.width=t.width,e.height=t.height,e}setScissor(e,t,r,i){let s=this._scissor;e.isVector4?s.copy(e):s.set(e,t,r,i)}getScissorTest(){return this._scissorTest}setScissorTest(e){this._scissorTest=e,this.backend.setScissorTest(e)}getViewport(e){return e.copy(this._viewport)}setViewport(e,t,r,i){let s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,n=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1,a=this._viewport;e.isVector4?a.copy(e):a.set(e,t,r,i),a.minDepth=s,a.maxDepth=n}getClearColor(e){return e.copy(this._clearColor)}setClearColor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;this._clearColor.set(e),this._clearColor.a=t}getClearAlpha(){return this._clearColor.a}setClearAlpha(e){this._clearColor.a=e}getClearDepth(){return this._clearDepth}setClearDepth(e){this._clearDepth=e}getClearStencil(){return this._clearStencil}setClearStencil(e){this._clearStencil=e}isOccluded(e){let t=this._currentRenderContext;return t&&this.backend.isOccluded(t,e)}clear(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],t=!(arguments.length>1)||void 0===arguments[1]||arguments[1],r=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!1===this._initialized)return console.warn("THREE.Renderer: .clear() called before the backend is initialized. Try using .clearAsync() instead."),this.clearAsync(e,t,r);let i=this._renderTarget||this._getFrameBufferTarget(),s=null;if(null!==i){this._textures.updateRenderTarget(i);let e=this._textures.get(i);(s=this._renderContexts.getForClear(i)).textures=e.textures,s.depthTexture=e.depthTexture,s.width=e.width,s.height=e.height,s.renderTarget=i,s.depth=i.depthBuffer,s.stencil=i.stencilBuffer,s.clearColorValue=this.backend.getClearColor(),s.activeCubeFace=this.getActiveCubeFace(),s.activeMipmapLevel=this.getActiveMipmapLevel()}this.backend.clear(e,t,r,s),null!==i&&null===this._renderTarget&&this._renderOutput(i)}clearColor(){return this.clear(!0,!1,!1)}clearDepth(){return this.clear(!1,!0,!1)}clearStencil(){return this.clear(!1,!1,!0)}async clearAsync(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],t=!(arguments.length>1)||void 0===arguments[1]||arguments[1],r=!(arguments.length>2)||void 0===arguments[2]||arguments[2];!1===this._initialized&&await this.init(),this.clear(e,t,r)}async clearColorAsync(){this.clearAsync(!0,!1,!1)}async clearDepthAsync(){this.clearAsync(!1,!0,!1)}async clearStencilAsync(){this.clearAsync(!1,!1,!0)}get currentToneMapping(){return this.isOutputTarget?this.toneMapping:u.y_p}get currentColorSpace(){return this.isOutputTarget?this.outputColorSpace:u.Zr2}get isOutputTarget(){return this._renderTarget===this._outputRenderTarget||null===this._renderTarget}dispose(){this.info.dispose(),this.backend.dispose(),this._animation.dispose(),this._objects.dispose(),this._pipelines.dispose(),this._nodes.dispose(),this._bindings.dispose(),this._renderLists.dispose(),this._renderContexts.dispose(),this._textures.dispose(),null!==this._frameBufferTarget&&this._frameBufferTarget.dispose(),Object.values(this.backend.timestampQueryPool).forEach(e=>{null!==e&&e.dispose()}),this.setRenderTarget(null),this.setAnimationLoop(null)}setRenderTarget(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;this._renderTarget=e,this._activeCubeFace=t,this._activeMipmapLevel=r}getRenderTarget(){return this._renderTarget}setOutputRenderTarget(e){this._outputRenderTarget=e}getOutputRenderTarget(){return this._outputRenderTarget}setRenderObjectFunction(e){this._renderObjectFunction=e}getRenderObjectFunction(){return this._renderObjectFunction}compute(e){if(!0===this._isDeviceLost)return;if(!1===this._initialized)return console.warn("THREE.Renderer: .compute() called before the backend is initialized. Try using .computeAsync() instead."),this.computeAsync(e);let t=this._nodes.nodeFrame,r=t.renderId;this.info.calls++,this.info.compute.calls++,this.info.compute.frameCalls++,t.renderId=this.info.calls;let i=this.backend,s=this._pipelines,n=this._bindings,a=this._nodes,o=Array.isArray(e)?e:[e];if(void 0===o[0]||!0!==o[0].isComputeNode)throw Error("THREE.Renderer: .compute() expects a ComputeNode.");for(let t of(i.beginCompute(e),o)){if(!1===s.has(t)){let e=()=>{t.removeEventListener("dispose",e),s.delete(t),n.delete(t),a.delete(t)};t.addEventListener("dispose",e);let r=t.onInitFunction;null!==r&&r.call(t,{renderer:this})}a.updateForCompute(t),n.updateForCompute(t);let r=n.getForCompute(t),o=s.getForCompute(t,r);i.compute(e,t,r,o)}i.finishCompute(e),t.renderId=r}async computeAsync(e){!1===this._initialized&&await this.init(),this.compute(e)}async hasFeatureAsync(e){return!1===this._initialized&&await this.init(),this.backend.hasFeature(e)}async resolveTimestampsAsync(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"render";return!1===this._initialized&&await this.init(),this.backend.resolveTimestampsAsync(e)}hasFeature(e){return!1===this._initialized?(console.warn("THREE.Renderer: .hasFeature() called before the backend is initialized. Try using .hasFeatureAsync() instead."),!1):this.backend.hasFeature(e)}hasInitialized(){return this._initialized}async initTextureAsync(e){!1===this._initialized&&await this.init(),this._textures.updateTexture(e)}initTexture(e){!1===this._initialized&&console.warn("THREE.Renderer: .initTexture() called before the backend is initialized. Try using .initTextureAsync() instead."),this._textures.updateTexture(e)}copyFramebufferToTexture(e){let t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(null!==r)if(r.isVector2)r=c9.set(r.x,r.y,e.image.width,e.image.height).floor();else{if(!r.isVector4)return void console.error("THREE.Renderer.copyFramebufferToTexture: Invalid rectangle.");r=c9.copy(r).floor()}else r=c9.set(0,0,e.image.width,e.image.height);let i=this._currentRenderContext;null!==i?t=i.renderTarget:null!==(t=this._renderTarget||this._getFrameBufferTarget())&&(this._textures.updateRenderTarget(t),i=this._textures.get(t)),this._textures.updateTexture(e,{renderTarget:t}),this.backend.copyFramebufferToTexture(e,i,r)}copyTextureToTexture(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,n=arguments.length>5&&void 0!==arguments[5]?arguments[5]:0;this._textures.updateTexture(e),this._textures.updateTexture(t),this.backend.copyTextureToTexture(e,t,r,i,s,n)}async readRenderTargetPixelsAsync(e,t,r,i,s){let n=arguments.length>5&&void 0!==arguments[5]?arguments[5]:0,a=arguments.length>6&&void 0!==arguments[6]?arguments[6]:0;return this.backend.copyTextureToBuffer(e.textures[n],t,r,i,s,a)}_projectObject(e,t,r,i,s){if(!1===e.visible)return;if(e.layers.test(t.layers)){if(e.isGroup)r=e.renderOrder,e.isClippingGroup&&e.enabled&&(s=s.getGroupContext(e));else if(e.isLOD)!0===e.autoUpdate&&e.update(t);else if(e.isLight)i.pushLight(e);else if(e.isSprite){let n=t.isArrayCamera?c8:c6;if(!e.frustumCulled||n.intersectsSprite(e,t)){!0===this.sortObjects&&c9.setFromMatrixPosition(e.matrixWorld).applyMatrix4(c5);let{geometry:t,material:n}=e;n.visible&&i.push(e,t,n,r,c9.z,null,s)}}else if(e.isLineLoop)console.error("THREE.Renderer: Objects of type THREE.LineLoop are not supported. Please use THREE.Line or THREE.LineSegments.");else if(e.isMesh||e.isLine||e.isPoints){let n=t.isArrayCamera?c8:c6;if(!e.frustumCulled||n.intersectsObject(e,t)){let{geometry:t,material:n}=e;if(!0===this.sortObjects&&(null===t.boundingSphere&&t.computeBoundingSphere(),c9.copy(t.boundingSphere.center).applyMatrix4(e.matrixWorld).applyMatrix4(c5)),Array.isArray(n)){let a=t.groups;for(let o=0,l=a.length;o<l;o++){let l=a[o],u=n[l.materialIndex];u&&u.visible&&i.push(e,t,u,r,c9.z,l,s)}}else n.visible&&i.push(e,t,n,r,c9.z,null,s)}}}if(!0===e.isBundleGroup&&void 0!==this.backend.beginBundle){let r=i;(i=this._renderLists.get(e,t)).begin(),r.pushBundle({bundleGroup:e,camera:t,renderList:i}),i.finish()}let n=e.children;for(let e=0,a=n.length;e<a;e++)this._projectObject(n[e],t,r,i,s)}_renderBundles(e,t,r){for(let i of e)this._renderBundle(i,t,r)}_renderTransparents(e,t,r,i,s){if(t.length>0){for(let{material:e}of t)e.side=u.hsX;for(let{material:e}of(this._renderObjects(t,r,i,s,"backSide"),t))e.side=u.hB5;for(let{material:n}of(this._renderObjects(e,r,i,s),t))n.side=u.$EB}else this._renderObjects(e,r,i,s)}_renderObjects(e,t,r,i){let s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null;for(let n=0,a=e.length;n<a;n++){let{object:a,geometry:o,material:l,group:u,clippingContext:d}=e[n];this._currentRenderObjectFunction(a,r,t,o,l,u,i,d,s)}}renderObject(e,t,r,i,s,n,a){let o,l,d,h=arguments.length>7&&void 0!==arguments[7]?arguments[7]:null,c=arguments.length>8&&void 0!==arguments[8]?arguments[8]:null;if(e.onBeforeRender(this,t,r,i,s,n),!0===s.allowOverride&&null!==t.overrideMaterial){let e=t.overrideMaterial;s.positionNode&&s.positionNode.isNode&&(o=e.positionNode,e.positionNode=s.positionNode),e.alphaTest=s.alphaTest,e.alphaMap=s.alphaMap,e.transparent=s.transparent||s.transmission>0,e.isShadowPassMaterial&&(e.side=null===s.shadowSide?s.side:s.shadowSide,s.depthNode&&s.depthNode.isNode&&(d=e.depthNode,e.depthNode=s.depthNode),s.castShadowNode&&s.castShadowNode.isNode&&(l=e.colorNode,e.colorNode=s.castShadowNode),s.castShadowPositionNode&&s.castShadowPositionNode.isNode&&(o=e.positionNode,e.positionNode=s.castShadowPositionNode)),s=e}!0===s.transparent&&s.side===u.$EB&&!1===s.forceSinglePass?(s.side=u.hsX,this._handleObjectFunction(e,s,t,r,a,n,h,"backSide"),s.side=u.hB5,this._handleObjectFunction(e,s,t,r,a,n,h,c),s.side=u.$EB):this._handleObjectFunction(e,s,t,r,a,n,h,c),void 0!==o&&(t.overrideMaterial.positionNode=o),void 0!==d&&(t.overrideMaterial.depthNode=d),void 0!==l&&(t.overrideMaterial.colorNode=l),e.onAfterRender(this,t,r,i,s,n)}_renderObjectDirect(e,t,r,i,s,n,a,o){let l=this._objects.get(e,t,r,i,s,this._currentRenderContext,a,o);l.drawRange=e.geometry.drawRange,l.group=n;let u=this._nodes.needsRefresh(l);u&&(this._nodes.updateBefore(l),this._geometries.updateForRender(l),this._nodes.updateForRender(l),this._bindings.updateForRender(l)),this._pipelines.updateForRender(l),null!==this._currentRenderBundle&&(this.backend.get(this._currentRenderBundle).renderObjects.push(l),l.bundle=this._currentRenderBundle.bundleGroup),this.backend.draw(l,this.info),u&&this._nodes.updateAfter(l)}_createObjectPipeline(e,t,r,i,s,n,a,o){let l=this._objects.get(e,t,r,i,s,this._currentRenderContext,a,o);l.drawRange=e.geometry.drawRange,l.group=n,this._nodes.updateBefore(l),this._geometries.updateForRender(l),this._nodes.updateForRender(l),this._bindings.updateForRender(l),this._pipelines.getForRender(l,this._compilationPromises),this._nodes.updateAfter(l)}get compile(){return this.compileAsync}constructor(e,t={}){this.isRenderer=!0;let{logarithmicDepthBuffer:r=!1,alpha:i=!0,depth:s=!0,stencil:n=!1,antialias:a=!1,samples:o=0,getFallback:l=null,colorBufferType:d=u.ix0,multiview:h=!1}=t;this.domElement=e.getDomElement(),this.backend=e,this.samples=o||!0===a?4:0,this.autoClear=!0,this.autoClearColor=!0,this.autoClearDepth=!0,this.autoClearStencil=!0,this.alpha=i,this.logarithmicDepthBuffer=r,this.outputColorSpace=u.er$,this.toneMapping=u.y_p,this.toneMappingExposure=1,this.sortObjects=!0,this.depth=s,this.stencil=n,this.info=new lH,this.overrideNodes={modelViewMatrix:null,modelNormalViewMatrix:null},this.library=new cz,this.lighting=new cq,this._getFallback=l,this._pixelRatio=1,this._width=this.domElement.width,this._height=this.domElement.height,this._viewport=new u.IUQ(0,0,this._width,this._height),this._scissor=new u.IUQ(0,0,this._width,this._height),this._scissorTest=!1,this._attributes=null,this._geometries=null,this._nodes=null,this._animation=null,this._bindings=null,this._objects=null,this._pipelines=null,this._bundles=null,this._renderLists=null,this._renderContexts=null,this._textures=null,this._background=null,this._quad=new uL(new ah),this._quad.material.name="Renderer_output",this._currentRenderContext=null,this._opaqueSort=null,this._transparentSort=null,this._frameBufferTarget=null;let c=+(!0!==this.alpha);this._clearColor=new ur(0,0,0,c),this._clearDepth=1,this._clearStencil=0,this._renderTarget=null,this._activeCubeFace=0,this._activeMipmapLevel=0,this._outputRenderTarget=null,this._mrt=null,this._renderObjectFunction=null,this._currentRenderObjectFunction=null,this._currentRenderBundle=null,this._handleObjectFunction=this._renderObjectDirect,this._isDeviceLost=!1,this.onDeviceLost=this._onDeviceLost,this._colorBufferType=d,this._initialized=!1,this._initPromise=null,this._compilationPromises=null,this.transparent=!0,this.opaque=!0,this.shadowMap={enabled:!1,type:u.QP0},this.xr=new cY(this,h),this.debug={checkShaderErrors:!0,onShaderError:null,getShaderAsync:async(e,t,r)=>{await this.compileAsync(e,t);let i=this._renderLists.get(e,t),s=this._renderContexts.get(e,t,this._renderTarget),n=e.overrideMaterial||r.material,{fragmentShader:a,vertexShader:o}=this._objects.get(r,n,e,t,i.lightsNode,s,s.clippingContext).getNodeBuilderState();return{fragmentShader:a,vertexShader:o}}}}}class pe{setVisibility(e){this.visibility|=e}clone(){return Object.assign(new this.constructor,this)}constructor(e=""){this.name=e,this.visibility=0}}class pt extends pe{get byteLength(){var e;return(e=this._buffer.byteLength)+(16-e%16)%16}get buffer(){return this._buffer}update(){return!0}constructor(e,t=null){super(e),this.isBuffer=!0,this.bytesPerElement=Float32Array.BYTES_PER_ELEMENT,this._buffer=t}}class pr extends pt{constructor(e,t=null){super(e,t),this.isUniformBuffer=!0}}let pi=0;class ps extends pr{get buffer(){return this.nodeUniform.value}constructor(e,t){super("UniformBuffer_"+pi++,e?e.value:null),this.nodeUniform=e,this.groupNode=t}}class pn extends pr{addUniform(e){return this.uniforms.push(e),this}removeUniform(e){let t=this.uniforms.indexOf(e);return -1!==t&&this.uniforms.splice(t,1),this}get values(){return null===this._values&&(this._values=Array.from(this.buffer)),this._values}get buffer(){let e=this._buffer;return null===e&&(e=new Float32Array(new ArrayBuffer(this.byteLength)),this._buffer=e),e}get byteLength(){let e=0;for(let t=0,r=this.uniforms.length;t<r;t++){let r=this.uniforms[t],{boundary:i,itemSize:s}=r,n=e%16,a=16-n;0!==n&&a-i<0?e+=16-n:n%i!=0&&(e+=n%i),r.offset=e/this.bytesPerElement,e+=s*this.bytesPerElement}return 16*Math.ceil(e/16)}update(){let e=!1;for(let t of this.uniforms)!0===this.updateByType(t)&&(e=!0);return e}updateByType(e){return e.isNumberUniform?this.updateNumber(e):e.isVector2Uniform?this.updateVector2(e):e.isVector3Uniform?this.updateVector3(e):e.isVector4Uniform?this.updateVector4(e):e.isColorUniform?this.updateColor(e):e.isMatrix3Uniform?this.updateMatrix3(e):e.isMatrix4Uniform?this.updateMatrix4(e):void console.error("THREE.WebGPUUniformsGroup: Unsupported uniform type.",e)}updateNumber(e){let t=!1,r=this.values,i=e.getValue(),s=e.offset,n=e.getType();return r[s]!==i&&(this._getBufferForType(n)[s]=r[s]=i,t=!0),t}updateVector2(e){let t=!1,r=this.values,i=e.getValue(),s=e.offset,n=e.getType();if(r[s+0]!==i.x||r[s+1]!==i.y){let e=this._getBufferForType(n);e[s+0]=r[s+0]=i.x,e[s+1]=r[s+1]=i.y,t=!0}return t}updateVector3(e){let t=!1,r=this.values,i=e.getValue(),s=e.offset,n=e.getType();if(r[s+0]!==i.x||r[s+1]!==i.y||r[s+2]!==i.z){let e=this._getBufferForType(n);e[s+0]=r[s+0]=i.x,e[s+1]=r[s+1]=i.y,e[s+2]=r[s+2]=i.z,t=!0}return t}updateVector4(e){let t=!1,r=this.values,i=e.getValue(),s=e.offset,n=e.getType();if(r[s+0]!==i.x||r[s+1]!==i.y||r[s+2]!==i.z||r[s+4]!==i.w){let e=this._getBufferForType(n);e[s+0]=r[s+0]=i.x,e[s+1]=r[s+1]=i.y,e[s+2]=r[s+2]=i.z,e[s+3]=r[s+3]=i.w,t=!0}return t}updateColor(e){let t=!1,r=this.values,i=e.getValue(),s=e.offset;if(r[s+0]!==i.r||r[s+1]!==i.g||r[s+2]!==i.b){let e=this.buffer;e[s+0]=r[s+0]=i.r,e[s+1]=r[s+1]=i.g,e[s+2]=r[s+2]=i.b,t=!0}return t}updateMatrix3(e){let t=!1,r=this.values,i=e.getValue().elements,s=e.offset;if(r[s+0]!==i[0]||r[s+1]!==i[1]||r[s+2]!==i[2]||r[s+4]!==i[3]||r[s+5]!==i[4]||r[s+6]!==i[5]||r[s+8]!==i[6]||r[s+9]!==i[7]||r[s+10]!==i[8]){let e=this.buffer;e[s+0]=r[s+0]=i[0],e[s+1]=r[s+1]=i[1],e[s+2]=r[s+2]=i[2],e[s+4]=r[s+4]=i[3],e[s+5]=r[s+5]=i[4],e[s+6]=r[s+6]=i[5],e[s+8]=r[s+8]=i[6],e[s+9]=r[s+9]=i[7],e[s+10]=r[s+10]=i[8],t=!0}return t}updateMatrix4(e){let t=!1,r=this.values,i=e.getValue().elements,s=e.offset;return!1===function(e,t,r){for(let i=0,s=t.length;i<s;i++)if(e[r+i]!==t[i])return!1;return!0}(r,i,s)&&(this.buffer.set(i,s),function(e,t,r){for(let i=0,s=t.length;i<s;i++)e[r+i]=t[i]}(r,i,s),t=!0),t}_getBufferForType(e){return"int"===e||"ivec2"===e||"ivec3"===e||"ivec4"===e?new Int32Array(this.buffer.buffer):"uint"===e||"uvec2"===e||"uvec3"===e||"uvec4"===e?new Uint32Array(this.buffer.buffer):this.buffer}constructor(e){super(e),this.isUniformsGroup=!0,this._values=null,this.uniforms=[]}}let pa=0;class po extends pn{constructor(e,t){super(e),this.id=pa++,this.groupNode=t,this.isNodeUniformsGroup=!0}}let pl=0;class pu extends pe{needsBindingsUpdate(e){let{texture:t}=this;return e!==this.generation?(this.generation=e,!0):t.isVideoTexture}update(){let{texture:e,version:t}=this;return t!==e.version&&(this.version=e.version,!0)}constructor(e,t){super(e),this.id=pl++,this.texture=t,this.version=t?t.version:0,this.store=!1,this.generation=null,this.isSampledTexture=!0}}class pd extends pu{needsBindingsUpdate(e){return this.textureNode.value!==this.texture||super.needsBindingsUpdate(e)}update(){let{textureNode:e}=this;return this.texture!==e.value?(this.texture=e.value,!0):super.update()}constructor(e,t,r,i=null){super(e,t?t.value:null),this.textureNode=t,this.groupNode=r,this.access=i}}class ph extends pd{constructor(e,t,r,i=null){super(e,t,r,i),this.isSampledCubeTexture=!0}}class pc extends pd{constructor(e,t,r,i=null){super(e,t,r,i),this.isSampledTexture3D=!0}}let pp={textureDimensions:"textureSize",equals:"equal"},pg={low:"lowp",medium:"mediump",high:"highp"},pm={swizzleAssign:!0,storageBuffer:!1},pf={perspective:"smooth",linear:"noperspective"},py={centroid:"centroid","flat first":"flat","flat either":"flat"},px="\nprecision highp float;\nprecision highp int;\nprecision highp sampler2D;\nprecision highp sampler3D;\nprecision highp samplerCube;\nprecision highp sampler2DArray;\n\nprecision highp usampler2D;\nprecision highp usampler3D;\nprecision highp usamplerCube;\nprecision highp usampler2DArray;\n\nprecision highp isampler2D;\nprecision highp isampler3D;\nprecision highp isamplerCube;\nprecision highp isampler2DArray;\n\nprecision lowp sampler2DShadow;\nprecision lowp sampler2DArrayShadow;\nprecision lowp samplerCubeShadow;\n";class pb extends cc{needsToWorkingColorSpace(e){return!0===e.isVideoTexture&&e.colorSpace!==u.jf0}getMethod(e){return pp[e]||e}getOutputStructName(){return""}buildFunctionCode(e){let t=e.layout,r=this.flowShaderNode(e),i=[];for(let e of t.inputs)i.push(this.getType(e.type)+" "+e.name);return"".concat(this.getType(t.type)," ").concat(t.name,"( ").concat(i.join(", ")," ) {\n\n	").concat(r.vars,"\n\n").concat(r.code,"\n	return ").concat(r.result,";\n\n}")}setupPBO(e){let t=e.value;if(void 0===t.pbo){let e=t.array,r=t.count*t.itemSize,{itemSize:i}=t,s=t.array.constructor.name.toLowerCase().includes("int"),n=s?u.ZQM:u.VT0;2===i?n=s?u.TkQ:u.paN:3===i?n=s?u.VGF:u.HIg:4===i&&(n=s?u.c90:u.GWd);let a={Float32Array:u.RQf,Uint8Array:u.OUM,Uint16Array:u.cHt,Uint32Array:u.bkx,Int8Array:u.tJf,Int16Array:u.fBL,Int32Array:u.Yuy,Uint8ClampedArray:u.OUM},o=Math.pow(2,Math.ceil(Math.log2(Math.sqrt(r/i)))),l=Math.ceil(r/i/o);o*l*i<r&&l++;let d=o*l*i,h=new e.constructor(d);h.set(e,0),t.array=h;let c=new u.GYF(t.array,o,l,n,a[t.array.constructor.name]||u.RQf);c.needsUpdate=!0,c.isPBOTexture=!0;let p=new iC(c,null,null);p.setPrecision("high"),t.pboNode=p,t.pbo=p.value,this.getUniformFromNode(t.pboNode,"texture",this.shaderStage,this.context.label)}}getPropertyName(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.shaderStage;return e.isNodeUniform&&!0!==e.node.isTextureNode&&!0!==e.node.isBufferNode?t.charAt(0)+"_"+e.name:super.getPropertyName(e,t)}generatePBO(e){let{node:t,indexNode:r}=e,i=t.value;this.renderer.backend.has(i)&&(this.renderer.backend.get(i).pbo=i.pbo);let s=this.getUniformFromNode(i.pboNode,"texture",this.shaderStage,this.context.label),n=this.getPropertyName(s);this.increaseUsage(r);let a=r.build(this,"uint"),o=this.getDataFromNode(e),l=o.propertyName;if(void 0===l){let r=this.getVarFromNode(e);l=this.getPropertyName(r);let s=this.getDataFromNode(t),d=s.propertySizeName;void 0===d&&(d=l+"Size",this.getVarFromNode(t,d,"uint"),this.addLineFlowCode("".concat(d," = uint( textureSize( ").concat(n,", 0 ).x )"),e),s.propertySizeName=d);let{itemSize:h}=i,c="."+B.join("").slice(0,h),p="ivec2(".concat(a," % ").concat(d,", ").concat(a," / ").concat(d,")"),g=this.generateTextureLoad(null,n,p,null,"0"),m="vec4";i.pbo.type===u.bkx?m="uvec4":i.pbo.type===u.Yuy&&(m="ivec4"),this.addLineFlowCode("".concat(l," = ").concat(m,"(").concat(g,")").concat(c),e),o.propertyName=l}return l}generateTextureLoad(e,t,r,i){let s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:"0";return i?"texelFetch( ".concat(t,", ivec3( ").concat(r,", ").concat(i," ), ").concat(s," )"):"texelFetch( ".concat(t,", ").concat(r,", ").concat(s," )")}generateTexture(e,t,r,i){return e.isDepthTexture?(i&&(r="vec4( ".concat(r,", ").concat(i," )")),"texture( ".concat(t,", ").concat(r," ).x")):(i&&(r="vec3( ".concat(r,", ").concat(i," )")),"texture( ".concat(t,", ").concat(r," )"))}generateTextureLevel(e,t,r,i){return"textureLod( ".concat(t,", ").concat(r,", ").concat(i," )")}generateTextureBias(e,t,r,i){return"texture( ".concat(t,", ").concat(r,", ").concat(i," )")}generateTextureGrad(e,t,r,i){return"textureGrad( ".concat(t,", ").concat(r,", ").concat(i[0],", ").concat(i[1]," )")}generateTextureCompare(e,t,r,i,s){let n=arguments.length>5&&void 0!==arguments[5]?arguments[5]:this.shaderStage;if("fragment"===n)return s?"texture( ".concat(t,", vec4( ").concat(r,", ").concat(s,", ").concat(i," ) )"):"texture( ".concat(t,", vec3( ").concat(r,", ").concat(i," ) )");console.error("WebGPURenderer: THREE.DepthTexture.compareFunction() does not support ".concat(n," shader."))}getVars(e){let t=[],r=this.vars[e];if(void 0!==r)for(let e of r)t.push("".concat(this.getVar(e.type,e.name,e.count),";"));return t.join("\n	")}getUniforms(e){let t=this.uniforms[e],r=[],i={};for(let s of t){let t=null,n=!1;if("texture"===s.type||"texture3D"===s.type){let e=s.node.value,r="";(!0===e.isDataTexture||!0===e.isData3DTexture)&&(e.type===u.bkx?r="u":e.type===u.Yuy&&(r="i")),t="texture3D"===s.type&&!1===e.isTextureArray?"".concat(r,"sampler3D ").concat(s.name,";"):e.compareFunction?!0===e.isDepthArrayTexture?"sampler2DArrayShadow ".concat(s.name,";"):"sampler2DShadow ".concat(s.name,";"):!0===e.isDataArrayTexture||!0===e.isCompressedArrayTexture||!0===e.isTextureArray?"".concat(r,"sampler2DArray ").concat(s.name,";"):"".concat(r,"sampler2D ").concat(s.name,";")}else if("cubeTexture"===s.type)t="samplerCube ".concat(s.name,";");else if("buffer"===s.type){let e=s.node,r=this.getType(e.bufferType),i=e.bufferCount,n=i>0?i:"";t="".concat(e.name," {\n	").concat(r," ").concat(s.name,"[").concat(n,"];\n};\n")}else{let r=this.getVectorType(s.type);t="".concat(r," ").concat(this.getPropertyName(s,e),";"),n=!0}let a=s.node.precision;if(null!==a&&(t=pg[a]+" "+t),n){t="	"+t;let e=s.groupNode.name;(i[e]||(i[e]=[])).push(t)}else t="uniform "+t,r.push(t)}let s="";for(let t in i){let r=i[t];s+=this._getGLSLUniformStruct(e+"_"+t,r.join("\n"))+"\n"}return s+r.join("\n")}getTypeFromAttribute(e){let t=super.getTypeFromAttribute(e);if(/^[iu]/.test(t)&&e.gpuType!==u.Yuy){let r=e;e.isInterleavedBufferAttribute&&(r=e.data);let i=r.array;!1==(i instanceof Uint32Array||i instanceof Int32Array)&&(t=t.slice(1))}return t}getAttributes(e){let t="";if("vertex"===e||"compute"===e){let e=this.getAttributesArray(),r=0;for(let i of e)t+="layout( location = ".concat(r++," ) in ").concat(i.type," ").concat(i.name,";\n")}return t}getStructMembers(e){let t=[];for(let r of e.members)t.push("	".concat(r.type," ").concat(r.name,";"));return t.join("\n")}getStructs(e){let t=[],r=this.structs[e],i=[];for(let e of r)if(e.output)for(let t of e.members)i.push("layout( location = ".concat(t.index," ) out ").concat(t.type," ").concat(t.name,";"));else{let r="struct "+e.name+" {\n";r+=this.getStructMembers(e),r+="\n};\n",t.push(r)}return 0===i.length&&i.push("layout( location = 0 ) out vec4 fragColor;"),"\n"+i.join("\n")+"\n\n"+t.join("\n")}getVaryings(e){let t="",r=this.varyings;if("vertex"===e||"compute"===e)for(let i of r){"compute"===e&&(i.needsInterpolation=!0);let r=this.getType(i.type);if(i.needsInterpolation)if(i.interpolationType){let e=pf[i.interpolationType]||i.interpolationType,s=py[i.interpolationSampling]||"";t+="".concat(e," ").concat(s," out ").concat(r," ").concat(i.name,";\n")}else{let e=r.includes("int")||r.includes("uv")||r.includes("iv")?"flat ":"";t+="".concat(e,"out ").concat(r," ").concat(i.name,";\n")}else t+="".concat(r," ").concat(i.name,";\n")}else if("fragment"===e){for(let e of r)if(e.needsInterpolation){let r=this.getType(e.type);if(e.interpolationType){let i=pf[e.interpolationType]||e.interpolationType,s=py[e.interpolationSampling]||"";t+="".concat(i," ").concat(s," in ").concat(r," ").concat(e.name,";\n")}else{let i=r.includes("int")||r.includes("uv")||r.includes("iv")?"flat ":"";t+="".concat(i,"in ").concat(r," ").concat(e.name,";\n")}}}for(let r of this.builtins[e])t+="".concat(r,";\n");return t}getVertexIndex(){return"uint( gl_VertexID )"}getInstanceIndex(){return"uint( gl_InstanceID )"}getInvocationLocalIndex(){let e=this.object.workgroupSize.reduce((e,t)=>e*t,1);return"uint( gl_InstanceID ) % ".concat(e,"u")}getDrawIndex(){return this.renderer.backend.extensions.has("WEBGL_multi_draw")?"uint( gl_DrawID )":null}getFrontFacing(){return"gl_FrontFacing"}getFragCoord(){return"gl_FragCoord.xy"}getFragDepth(){return"gl_FragDepth"}enableExtension(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.shaderStage,i=this.extensions[r]||(this.extensions[r]=new Map);!1===i.has(e)&&i.set(e,{name:e,behavior:t})}getExtensions(e){let t=[];if("vertex"===e){let t=this.renderer.backend.extensions;this.object.isBatchedMesh&&t.has("WEBGL_multi_draw")&&this.enableExtension("GL_ANGLE_multi_draw","require",e)}let r=this.extensions[e];if(void 0!==r)for(let{name:e,behavior:i}of r.values())t.push("#extension ".concat(e," : ").concat(i));return t.join("\n")}getClipDistance(){return"gl_ClipDistance"}isAvailable(e){let t=pm[e];if(void 0===t){let r;switch(t=!1,e){case"float32Filterable":r="OES_texture_float_linear";break;case"clipDistance":r="WEBGL_clip_cull_distance"}if(void 0!==r){let e=this.renderer.backend.extensions;e.has(r)&&(e.get(r),t=!0)}pm[e]=t}return t}isFlipY(){return!0}enableHardwareClipping(e){this.enableExtension("GL_ANGLE_clip_cull_distance","require"),this.builtins.vertex.push("out float gl_ClipDistance[ ".concat(e," ]"))}enableMultiview(){this.enableExtension("GL_OVR_multiview2","require","fragment"),this.enableExtension("GL_OVR_multiview2","require","vertex"),this.builtins.vertex.push("layout(num_views = 2) in")}registerTransform(e,t){this.transforms.push({varyingName:e,attributeNode:t})}getTransforms(){let e=this.transforms,t="";for(let r=0;r<e.length;r++){let i=e[r],s=this.getPropertyName(i.attributeNode);s&&(t+="".concat(i.varyingName," = ").concat(s,";\n	"))}return t}_getGLSLUniformStruct(e,t){return"\nlayout( std140 ) uniform ".concat(e," {\n").concat(t,"\n};")}_getGLSLVertexCode(e){return"#version 300 es\n\n".concat(this.getSignature(),"\n\n// extensions\n").concat(e.extensions,"\n\n// precision\n").concat(px,"\n\n// uniforms\n").concat(e.uniforms,"\n\n// varyings\n").concat(e.varyings,"\n\n// attributes\n").concat(e.attributes,"\n\n// codes\n").concat(e.codes,"\n\nvoid main() {\n\n	// vars\n	").concat(e.vars,"\n\n	// transforms\n	").concat(e.transforms,"\n\n	// flow\n	").concat(e.flow,"\n\n	gl_PointSize = 1.0;\n\n}\n")}_getGLSLFragmentCode(e){return"#version 300 es\n\n".concat(this.getSignature(),"\n\n// extensions\n").concat(e.extensions,"\n\n// precision\n").concat(px,"\n\n// uniforms\n").concat(e.uniforms,"\n\n// varyings\n").concat(e.varyings,"\n\n// codes\n").concat(e.codes,"\n\n// structs\n").concat(e.structs,"\n\nvoid main() {\n\n	// vars\n	").concat(e.vars,"\n\n	// flow\n	").concat(e.flow,"\n\n}\n")}buildCode(){let e=null!==this.material?{fragment:{},vertex:{}}:{compute:{}};for(let t in this.sortBindingGroups(),e){let r="// code\n\n";r+=this.flowCode[t];let i=this.flowNodes[t],s=i[i.length-1];for(let e of i){let i=this.getFlowData(e),n=e.name;n&&(r.length>0&&(r+="\n"),r+="	// flow -> ".concat(n,"\n	")),r+="".concat(i.code,"\n	"),e===s&&"compute"!==t&&(r+="// result\n	","vertex"===t?(r+="gl_Position = ",r+="".concat(i.result,";")):"fragment"!==t||e.outputNode.isOutputStructNode||(r+="fragColor = ",r+="".concat(i.result,";")))}let n=e[t];n.extensions=this.getExtensions(t),n.uniforms=this.getUniforms(t),n.attributes=this.getAttributes(t),n.varyings=this.getVaryings(t),n.vars=this.getVars(t),n.structs=this.getStructs(t),n.codes=this.getCodes(t),n.transforms=this.getTransforms(t),n.flow=r}null!==this.material?(this.vertexShader=this._getGLSLVertexCode(e.vertex),this.fragmentShader=this._getGLSLFragmentCode(e.fragment)):this.computeShader=this._getGLSLVertexCode(e.compute)}getUniformFromNode(e,t,r){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,s=super.getUniformFromNode(e,t,r,i),n=this.getDataFromNode(e,r,this.globalCache),a=n.uniformGPU;if(void 0===a){let i=e.groupNode,o=i.name,l=this.getBindGroupArray(o,r);if("texture"===t)a=new pd(s.name,s.node,i),l.push(a);else if("cubeTexture"===t)a=new ph(s.name,s.node,i),l.push(a);else if("texture3D"===t)a=new pc(s.name,s.node,i),l.push(a);else if("buffer"===t){e.name="NodeBuffer_".concat(e.id),s.name="buffer".concat(e.id);let t=new ps(e,i);t.name=e.name,l.push(t),a=t}else{let e=this.uniformGroups[r]||(this.uniformGroups[r]={}),n=e[o];void 0===n&&(n=new po(r+"_"+o,i),e[o]=n,l.push(n)),a=this.getNodeUniform(s,t),n.addUniform(a)}n.uniformGPU=a}return s}constructor(e,t){super(e,t,new cP),this.uniformGroups={},this.transforms=[],this.extensions={},this.builtins={vertex:[],fragment:[],compute:[]}}}let pT=null,pv=null;class p_{async init(e){this.renderer=e}get coordinateSystem(){}beginRender(){}finishRender(){}beginCompute(){}finishCompute(){}draw(){}compute(){}createProgram(){}destroyProgram(){}createBindings(){}updateBindings(){}updateBinding(){}createRenderPipeline(){}createComputePipeline(){}needsRenderUpdate(){}getRenderCacheKey(){}createNodeBuilder(){}createSampler(){}destroySampler(){}createDefaultTexture(){}createTexture(){}updateTexture(){}generateMipmaps(){}destroyTexture(){}async copyTextureToBuffer(){}copyTextureToTexture(){}copyFramebufferToTexture(){}createAttribute(){}createIndexAttribute(){}createStorageAttribute(){}updateAttribute(){}destroyAttribute(){}getContext(){}updateSize(){}updateViewport(){}isOccluded(){}async resolveTimestampsAsync(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"render";if(!this.trackTimestamp)return void(0,u.mcG)("WebGPURenderer: Timestamp tracking is disabled.");let t=this.timestampQueryPool[e];if(!t)return void(0,u.mcG)("WebGPURenderer: No timestamp query pool for type '".concat(e,"' found."));let r=await t.resolveQueriesAsync();return this.renderer.info[e].timestamp=r,r}async waitForGPU(){}async getArrayBufferAsync(){}async hasFeatureAsync(){}hasFeature(){}getMaxAnisotropy(){}getDrawingBufferSize(){return pT=pT||new u.I9Y,this.renderer.getDrawingBufferSize(pT)}setScissorTest(){}getClearColor(){let e=this.renderer;return pv=pv||new ur,e.getClearColor(pv),pv.getRGB(pv),pv}getDomElement(){let e=this.domElement;return null===e&&("setAttribute"in(e=void 0!==this.parameters.canvas?this.parameters.canvas:(0,u.lPF)())&&e.setAttribute("data-engine","three.js r".concat(u.sPf," webgpu")),this.domElement=e),e}set(e,t){this.data.set(e,t)}get(e){let t=this.data.get(e);return void 0===t&&(t={},this.data.set(e,t)),t}has(e){return this.data.has(e)}delete(e){this.data.delete(e)}dispose(){}constructor(e={}){this.parameters=Object.assign({},e),this.data=new WeakMap,this.renderer=null,this.domElement=null,this.timestampQueryPool={render:null,compute:null},this.trackTimestamp=!0===e.trackTimestamp}}let pN=0;class pS{get id(){return"".concat(this.baseId,"|").concat(this.activeBufferIndex)}get bufferGPU(){return this.buffers[this.activeBufferIndex]}get transformBuffer(){return this.buffers[1^this.activeBufferIndex]}switchBuffers(){this.activeBufferIndex^=1}constructor(e,t){this.buffers=[e.bufferGPU,t],this.type=e.type,this.bufferType=e.bufferType,this.pbo=e.pbo,this.byteLength=e.byteLength,this.bytesPerElement=e.BYTES_PER_ELEMENT,this.version=e.version,this.isInteger=e.isInteger,this.activeBufferIndex=0,this.baseId=e.id}}class pR{createAttribute(e,t){let r,i=this.backend,{gl:s}=i,n=e.array,a=e.usage||s.STATIC_DRAW,o=e.isInterleavedBufferAttribute?e.data:e,l=i.get(o),d=l.bufferGPU;if(void 0===d&&(l.bufferGPU=d=this._createBuffer(s,t,n,a),l.bufferType=t,l.version=o.version),n instanceof Float32Array)r=s.FLOAT;else if(n instanceof Uint16Array)r=e.isFloat16BufferAttribute?s.HALF_FLOAT:s.UNSIGNED_SHORT;else if(n instanceof Int16Array)r=s.SHORT;else if(n instanceof Uint32Array)r=s.UNSIGNED_INT;else if(n instanceof Int32Array)r=s.INT;else if(n instanceof Int8Array)r=s.BYTE;else if(n instanceof Uint8Array)r=s.UNSIGNED_BYTE;else if(n instanceof Uint8ClampedArray)r=s.UNSIGNED_BYTE;else throw Error("THREE.WebGLBackend: Unsupported buffer data format: "+n);let h={bufferGPU:d,bufferType:t,type:r,byteLength:n.byteLength,bytesPerElement:n.BYTES_PER_ELEMENT,version:e.version,pbo:e.pbo,isInteger:r===s.INT||r===s.UNSIGNED_INT||e.gpuType===u.Yuy,id:pN++};(e.isStorageBufferAttribute||e.isStorageInstancedBufferAttribute)&&(h=new pS(h,this._createBuffer(s,t,n,a))),i.set(e,h)}updateAttribute(e){let t=this.backend,{gl:r}=t,i=e.array,s=e.isInterleavedBufferAttribute?e.data:e,n=t.get(s),a=n.bufferType,o=e.isInterleavedBufferAttribute?e.data.updateRanges:e.updateRanges;if(r.bindBuffer(a,n.bufferGPU),0===o.length)r.bufferSubData(a,0,i);else{for(let e=0,t=o.length;e<t;e++){let t=o[e];r.bufferSubData(a,t.start*i.BYTES_PER_ELEMENT,i,t.start,t.count)}s.clearUpdateRanges()}r.bindBuffer(a,null),n.version=s.version}destroyAttribute(e){let t=this.backend,{gl:r}=t;e.isInterleavedBufferAttribute&&t.delete(e.data);let i=t.get(e);r.deleteBuffer(i.bufferGPU),t.delete(e)}async getArrayBufferAsync(e){let t=this.backend,{gl:r}=t,i=e.isInterleavedBufferAttribute?e.data:e,{bufferGPU:s}=t.get(i),n=e.array,a=n.byteLength;r.bindBuffer(r.COPY_READ_BUFFER,s);let o=r.createBuffer();r.bindBuffer(r.COPY_WRITE_BUFFER,o),r.bufferData(r.COPY_WRITE_BUFFER,a,r.STREAM_READ),r.copyBufferSubData(r.COPY_READ_BUFFER,r.COPY_WRITE_BUFFER,0,0,a),await t.utils._clientWaitAsync();let l=new e.array.constructor(n.length);return r.bindBuffer(r.COPY_WRITE_BUFFER,o),r.getBufferSubData(r.COPY_WRITE_BUFFER,0,l),r.deleteBuffer(o),r.bindBuffer(r.COPY_READ_BUFFER,null),r.bindBuffer(r.COPY_WRITE_BUFFER,null),l.buffer}_createBuffer(e,t,r,i){let s=e.createBuffer();return e.bindBuffer(t,s),e.bufferData(t,r,i),e.bindBuffer(t,null),s}constructor(e){this.backend=e}}class pA{_init(){let e=this.gl;o={[u.gO9]:e.FUNC_ADD,[u.FXf]:e.FUNC_SUBTRACT,[u.nST]:e.FUNC_REVERSE_SUBTRACT},l={[u.ojh]:e.ZERO,[u.qad]:e.ONE,[u.f4X]:e.SRC_COLOR,[u.ie2]:e.SRC_ALPHA,[u.hgQ]:e.SRC_ALPHA_SATURATE,[u.wn6]:e.DST_COLOR,[u.hdd]:e.DST_ALPHA,[u.LiQ]:e.ONE_MINUS_SRC_COLOR,[u.OuU]:e.ONE_MINUS_SRC_ALPHA,[u.aEY]:e.ONE_MINUS_DST_COLOR,[u.Nt7]:e.ONE_MINUS_DST_ALPHA};let t=e.getParameter(e.SCISSOR_BOX),r=e.getParameter(e.VIEWPORT);this.currentScissor=new u.IUQ().fromArray(t),this.currentViewport=new u.IUQ().fromArray(r),this._tempVec4=new u.IUQ}enable(e){let{enabled:t}=this;!0!==t[e]&&(this.gl.enable(e),t[e]=!0)}disable(e){let{enabled:t}=this;!1!==t[e]&&(this.gl.disable(e),t[e]=!1)}setFlipSided(e){if(this.currentFlipSided!==e){let{gl:t}=this;e?t.frontFace(t.CW):t.frontFace(t.CCW),this.currentFlipSided=e}}setCullFace(e){let{gl:t}=this;e!==u.WNZ?(this.enable(t.CULL_FACE),e!==this.currentCullFace&&(e===u.Vb5?t.cullFace(t.BACK):e===u.Jnc?t.cullFace(t.FRONT):t.cullFace(t.FRONT_AND_BACK))):this.disable(t.CULL_FACE),this.currentCullFace=e}setLineWidth(e){let{currentLineWidth:t,gl:r}=this;e!==t&&(r.lineWidth(e),this.currentLineWidth=e)}setBlending(e,t,r,i,s,n,a,d){let{gl:h}=this;if(e===u.XIg){!0===this.currentBlendingEnabled&&(this.disable(h.BLEND),this.currentBlendingEnabled=!1);return}if(!1===this.currentBlendingEnabled&&(this.enable(h.BLEND),this.currentBlendingEnabled=!0),e!==u.bCz){if(e!==this.currentBlending||d!==this.currentPremultipledAlpha){if((this.currentBlendEquation!==u.gO9||this.currentBlendEquationAlpha!==u.gO9)&&(h.blendEquation(h.FUNC_ADD),this.currentBlendEquation=u.gO9,this.currentBlendEquationAlpha=u.gO9),d)switch(e){case u.NTi:h.blendFuncSeparate(h.ONE,h.ONE_MINUS_SRC_ALPHA,h.ONE,h.ONE_MINUS_SRC_ALPHA);break;case u.EZo:h.blendFunc(h.ONE,h.ONE);break;case u.Kwu:h.blendFuncSeparate(h.ZERO,h.ONE_MINUS_SRC_COLOR,h.ZERO,h.ONE);break;case u.EdD:h.blendFuncSeparate(h.ZERO,h.SRC_COLOR,h.ZERO,h.SRC_ALPHA);break;default:console.error("THREE.WebGLState: Invalid blending: ",e)}else switch(e){case u.NTi:h.blendFuncSeparate(h.SRC_ALPHA,h.ONE_MINUS_SRC_ALPHA,h.ONE,h.ONE_MINUS_SRC_ALPHA);break;case u.EZo:h.blendFunc(h.SRC_ALPHA,h.ONE);break;case u.Kwu:h.blendFuncSeparate(h.ZERO,h.ONE_MINUS_SRC_COLOR,h.ZERO,h.ONE);break;case u.EdD:h.blendFunc(h.ZERO,h.SRC_COLOR);break;default:console.error("THREE.WebGLState: Invalid blending: ",e)}this.currentBlendSrc=null,this.currentBlendDst=null,this.currentBlendSrcAlpha=null,this.currentBlendDstAlpha=null,this.currentBlending=e,this.currentPremultipledAlpha=d}return}s=s||t,n=n||r,a=a||i,(t!==this.currentBlendEquation||s!==this.currentBlendEquationAlpha)&&(h.blendEquationSeparate(o[t],o[s]),this.currentBlendEquation=t,this.currentBlendEquationAlpha=s),(r!==this.currentBlendSrc||i!==this.currentBlendDst||n!==this.currentBlendSrcAlpha||a!==this.currentBlendDstAlpha)&&(h.blendFuncSeparate(l[r],l[i],l[n],l[a]),this.currentBlendSrc=r,this.currentBlendDst=i,this.currentBlendSrcAlpha=n,this.currentBlendDstAlpha=a),this.currentBlending=e,this.currentPremultipledAlpha=!1}setColorMask(e){this.currentColorMask!==e&&(this.gl.colorMask(e,e,e,e),this.currentColorMask=e)}setDepthTest(e){let{gl:t}=this;e?this.enable(t.DEPTH_TEST):this.disable(t.DEPTH_TEST)}setDepthMask(e){this.currentDepthMask!==e&&(this.gl.depthMask(e),this.currentDepthMask=e)}setDepthFunc(e){if(this.currentDepthFunc!==e){let{gl:t}=this;switch(e){case u.eHc:t.depthFunc(t.NEVER);break;case u.lGu:t.depthFunc(t.ALWAYS);break;case u.brA:t.depthFunc(t.LESS);break;case u.xSv:t.depthFunc(t.LEQUAL);break;case u.U3G:t.depthFunc(t.EQUAL);break;case u.Gwm:t.depthFunc(t.GEQUAL);break;case u.K52:t.depthFunc(t.GREATER);break;case u.bw0:t.depthFunc(t.NOTEQUAL);break;default:t.depthFunc(t.LEQUAL)}this.currentDepthFunc=e}}scissor(e,t,r,i){let s=this._tempVec4.set(e,t,r,i);if(!1===this.currentScissor.equals(s)){let{gl:e}=this;e.scissor(s.x,s.y,s.z,s.w),this.currentScissor.copy(s)}}viewport(e,t,r,i){let s=this._tempVec4.set(e,t,r,i);if(!1===this.currentViewport.equals(s)){let{gl:e}=this;e.viewport(s.x,s.y,s.z,s.w),this.currentViewport.copy(s)}}setScissorTest(e){let t=this.gl;e?t.enable(t.SCISSOR_TEST):t.disable(t.SCISSOR_TEST)}setStencilTest(e){let{gl:t}=this;e?this.enable(t.STENCIL_TEST):this.disable(t.STENCIL_TEST)}setStencilMask(e){this.currentStencilMask!==e&&(this.gl.stencilMask(e),this.currentStencilMask=e)}setStencilFunc(e,t,r){(this.currentStencilFunc!==e||this.currentStencilRef!==t||this.currentStencilFuncMask!==r)&&(this.gl.stencilFunc(e,t,r),this.currentStencilFunc=e,this.currentStencilRef=t,this.currentStencilFuncMask=r)}setStencilOp(e,t,r){(this.currentStencilFail!==e||this.currentStencilZFail!==t||this.currentStencilZPass!==r)&&(this.gl.stencilOp(e,t,r),this.currentStencilFail=e,this.currentStencilZFail=t,this.currentStencilZPass=r)}setMaterial(e,t,r){let{gl:i}=this;e.side===u.$EB?this.disable(i.CULL_FACE):this.enable(i.CULL_FACE);let s=e.side===u.hsX;t&&(s=!s),this.setFlipSided(s),e.blending===u.NTi&&!1===e.transparent?this.setBlending(u.XIg):this.setBlending(e.blending,e.blendEquation,e.blendSrc,e.blendDst,e.blendEquationAlpha,e.blendSrcAlpha,e.blendDstAlpha,e.premultipliedAlpha),this.setDepthFunc(e.depthFunc),this.setDepthTest(e.depthTest),this.setDepthMask(e.depthWrite),this.setColorMask(e.colorWrite);let n=e.stencilWrite;if(this.setStencilTest(n),n&&(this.setStencilMask(e.stencilWriteMask),this.setStencilFunc(e.stencilFunc,e.stencilRef,e.stencilFuncMask),this.setStencilOp(e.stencilFail,e.stencilZFail,e.stencilZPass)),this.setPolygonOffset(e.polygonOffset,e.polygonOffsetFactor,e.polygonOffsetUnits),!0===e.alphaToCoverage&&this.backend.renderer.samples>1?this.enable(i.SAMPLE_ALPHA_TO_COVERAGE):this.disable(i.SAMPLE_ALPHA_TO_COVERAGE),r>0&&this.currentClippingPlanes!==r)for(let e=0;e<8;e++)e<r?this.enable(12288+e):this.disable(12288+e)}setPolygonOffset(e,t,r){let{gl:i}=this;e?(this.enable(i.POLYGON_OFFSET_FILL),(this.currentPolygonOffsetFactor!==t||this.currentPolygonOffsetUnits!==r)&&(i.polygonOffset(t,r),this.currentPolygonOffsetFactor=t,this.currentPolygonOffsetUnits=r)):this.disable(i.POLYGON_OFFSET_FILL)}useProgram(e){return this.currentProgram!==e&&(this.gl.useProgram(e),this.currentProgram=e,!0)}setVertexState(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=this.gl;return(this.currentVAO!==e||this.currentIndex!==t)&&(r.bindVertexArray(e),null!==t&&r.bindBuffer(r.ELEMENT_ARRAY_BUFFER,t),this.currentVAO=e,this.currentIndex=t,!0)}resetVertexState(){let e=this.gl;e.bindVertexArray(null),e.bindBuffer(e.ELEMENT_ARRAY_BUFFER,null),this.currentVAO=null,this.currentIndex=null}bindFramebuffer(e,t){let{gl:r,currentBoundFramebuffers:i}=this;return i[e]!==t&&(r.bindFramebuffer(e,t),i[e]=t,e===r.DRAW_FRAMEBUFFER&&(i[r.FRAMEBUFFER]=t),e===r.FRAMEBUFFER&&(i[r.DRAW_FRAMEBUFFER]=t),!0)}drawBuffers(e,t){let{gl:r}=this,i=[],s=!1;if(null!==e.textures){void 0===(i=this.currentDrawbuffers.get(t))&&(i=[],this.currentDrawbuffers.set(t,i));let n=e.textures;if(i.length!==n.length||i[0]!==r.COLOR_ATTACHMENT0){for(let e=0,t=n.length;e<t;e++)i[e]=r.COLOR_ATTACHMENT0+e;i.length=n.length,s=!0}}else i[0]!==r.BACK&&(i[0]=r.BACK,s=!0);s&&r.drawBuffers(i)}activeTexture(e){let{gl:t,currentTextureSlot:r,maxTextures:i}=this;void 0===e&&(e=t.TEXTURE0+i-1),r!==e&&(t.activeTexture(e),this.currentTextureSlot=e)}bindTexture(e,t,r){let{gl:i,currentTextureSlot:s,currentBoundTextures:n,maxTextures:a}=this;void 0===r&&(r=null===s?i.TEXTURE0+a-1:s);let o=n[r];void 0===o&&(o={type:void 0,texture:void 0},n[r]=o),(o.type!==e||o.texture!==t)&&(s!==r&&(i.activeTexture(r),this.currentTextureSlot=r),i.bindTexture(e,t),o.type=e,o.texture=t)}bindBufferBase(e,t,r){let{gl:i}=this,s="".concat(e,"-").concat(t);return this.currentBoundBufferBases[s]!==r&&(i.bindBufferBase(e,t,r),this.currentBoundBufferBases[s]=r,!0)}unbindTexture(){let{gl:e,currentTextureSlot:t,currentBoundTextures:r}=this,i=r[t];void 0!==i&&void 0!==i.type&&(e.bindTexture(i.type,null),i.type=void 0,i.texture=void 0)}constructor(e){this.backend=e,this.gl=this.backend.gl,this.enabled={},this.currentFlipSided=null,this.currentCullFace=null,this.currentProgram=null,this.currentBlendingEnabled=!1,this.currentBlending=null,this.currentBlendSrc=null,this.currentBlendDst=null,this.currentBlendSrcAlpha=null,this.currentBlendDstAlpha=null,this.currentPremultipledAlpha=null,this.currentPolygonOffsetFactor=null,this.currentPolygonOffsetUnits=null,this.currentColorMask=null,this.currentDepthFunc=null,this.currentDepthMask=null,this.currentStencilFunc=null,this.currentStencilRef=null,this.currentStencilFuncMask=null,this.currentStencilFail=null,this.currentStencilZFail=null,this.currentStencilZPass=null,this.currentStencilMask=null,this.currentLineWidth=null,this.currentClippingPlanes=0,this.currentVAO=null,this.currentIndex=null,this.currentBoundFramebuffers={},this.currentDrawbuffers=new WeakMap,this.maxTextures=this.gl.getParameter(this.gl.MAX_TEXTURE_IMAGE_UNITS),this.currentTextureSlot=null,this.currentBoundTextures={},this.currentBoundBufferBases={},this._init()}}class pE{convert(e){let t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:u.jf0,{gl:i,extensions:s}=this;if(e===u.OUM)return i.UNSIGNED_BYTE;if(e===u.Wew)return i.UNSIGNED_SHORT_4_4_4_4;if(e===u.gJ2)return i.UNSIGNED_SHORT_5_5_5_1;if(e===u.Dmk)return i.UNSIGNED_INT_5_9_9_9_REV;if(e===u.tJf)return i.BYTE;if(e===u.fBL)return i.SHORT;if(e===u.cHt)return i.UNSIGNED_SHORT;if(e===u.Yuy)return i.INT;if(e===u.bkx)return i.UNSIGNED_INT;if(e===u.RQf)return i.FLOAT;if(e===u.ix0)return i.HALF_FLOAT;if(e===u.wrO)return i.ALPHA;if(e===u.HIg)return i.RGB;if(e===u.GWd)return i.RGBA;if(e===u.zdS)return i.DEPTH_COMPONENT;if(e===u.dcC)return i.DEPTH_STENCIL;if(e===u.VT0)return i.RED;if(e===u.ZQM)return i.RED_INTEGER;if(e===u.paN)return i.RG;if(e===u.TkQ)return i.RG_INTEGER;if(e===u.c90)return i.RGBA_INTEGER;if(e===u.IE4||e===u.Nz6||e===u.jR7||e===u.BXX)if(r===u.er$){if(null===(t=s.get("WEBGL_compressed_texture_s3tc_srgb")))return null;if(e===u.IE4)return t.COMPRESSED_SRGB_S3TC_DXT1_EXT;if(e===u.Nz6)return t.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT;if(e===u.jR7)return t.COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT;if(e===u.BXX)return t.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT}else{if(null===(t=s.get("WEBGL_compressed_texture_s3tc")))return null;if(e===u.IE4)return t.COMPRESSED_RGB_S3TC_DXT1_EXT;if(e===u.Nz6)return t.COMPRESSED_RGBA_S3TC_DXT1_EXT;if(e===u.jR7)return t.COMPRESSED_RGBA_S3TC_DXT3_EXT;if(e===u.BXX)return t.COMPRESSED_RGBA_S3TC_DXT5_EXT}if(e===u.k6Q||e===u.kTp||e===u.HXV||e===u.pBf){if(null===(t=s.get("WEBGL_compressed_texture_pvrtc")))return null;if(e===u.k6Q)return t.COMPRESSED_RGB_PVRTC_4BPPV1_IMG;if(e===u.kTp)return t.COMPRESSED_RGB_PVRTC_2BPPV1_IMG;if(e===u.HXV)return t.COMPRESSED_RGBA_PVRTC_4BPPV1_IMG;if(e===u.pBf)return t.COMPRESSED_RGBA_PVRTC_2BPPV1_IMG}if(e===u.CVz||e===u.Riy||e===u.KDk){if(null===(t=s.get("WEBGL_compressed_texture_etc")))return null;if(e===u.CVz||e===u.Riy)return r===u.er$?t.COMPRESSED_SRGB8_ETC2:t.COMPRESSED_RGB8_ETC2;if(e===u.KDk)return r===u.er$?t.COMPRESSED_SRGB8_ALPHA8_ETC2_EAC:t.COMPRESSED_RGBA8_ETC2_EAC}if(e===u.qa3||e===u.B_h||e===u.czI||e===u.rSH||e===u.Qrf||e===u.psI||e===u.a5J||e===u._QJ||e===u.uB5||e===u.lyL||e===u.bC7||e===u.y3Z||e===u.ojs||e===u.S$4){if(null===(t=s.get("WEBGL_compressed_texture_astc")))return null;if(e===u.qa3)return r===u.er$?t.COMPRESSED_SRGB8_ALPHA8_ASTC_4x4_KHR:t.COMPRESSED_RGBA_ASTC_4x4_KHR;if(e===u.B_h)return r===u.er$?t.COMPRESSED_SRGB8_ALPHA8_ASTC_5x4_KHR:t.COMPRESSED_RGBA_ASTC_5x4_KHR;if(e===u.czI)return r===u.er$?t.COMPRESSED_SRGB8_ALPHA8_ASTC_5x5_KHR:t.COMPRESSED_RGBA_ASTC_5x5_KHR;if(e===u.rSH)return r===u.er$?t.COMPRESSED_SRGB8_ALPHA8_ASTC_6x5_KHR:t.COMPRESSED_RGBA_ASTC_6x5_KHR;if(e===u.Qrf)return r===u.er$?t.COMPRESSED_SRGB8_ALPHA8_ASTC_6x6_KHR:t.COMPRESSED_RGBA_ASTC_6x6_KHR;if(e===u.psI)return r===u.er$?t.COMPRESSED_SRGB8_ALPHA8_ASTC_8x5_KHR:t.COMPRESSED_RGBA_ASTC_8x5_KHR;if(e===u.a5J)return r===u.er$?t.COMPRESSED_SRGB8_ALPHA8_ASTC_8x6_KHR:t.COMPRESSED_RGBA_ASTC_8x6_KHR;if(e===u._QJ)return r===u.er$?t.COMPRESSED_SRGB8_ALPHA8_ASTC_8x8_KHR:t.COMPRESSED_RGBA_ASTC_8x8_KHR;if(e===u.uB5)return r===u.er$?t.COMPRESSED_SRGB8_ALPHA8_ASTC_10x5_KHR:t.COMPRESSED_RGBA_ASTC_10x5_KHR;if(e===u.lyL)return r===u.er$?t.COMPRESSED_SRGB8_ALPHA8_ASTC_10x6_KHR:t.COMPRESSED_RGBA_ASTC_10x6_KHR;if(e===u.bC7)return r===u.er$?t.COMPRESSED_SRGB8_ALPHA8_ASTC_10x8_KHR:t.COMPRESSED_RGBA_ASTC_10x8_KHR;if(e===u.y3Z)return r===u.er$?t.COMPRESSED_SRGB8_ALPHA8_ASTC_10x10_KHR:t.COMPRESSED_RGBA_ASTC_10x10_KHR;if(e===u.ojs)return r===u.er$?t.COMPRESSED_SRGB8_ALPHA8_ASTC_12x10_KHR:t.COMPRESSED_RGBA_ASTC_12x10_KHR;if(e===u.S$4)return r===u.er$?t.COMPRESSED_SRGB8_ALPHA8_ASTC_12x12_KHR:t.COMPRESSED_RGBA_ASTC_12x12_KHR}if(e===u.Fn){if(null===(t=s.get("EXT_texture_compression_bptc")))return null;if(e===u.Fn)return r===u.er$?t.COMPRESSED_SRGB_ALPHA_BPTC_UNORM_EXT:t.COMPRESSED_RGBA_BPTC_UNORM_EXT}if(e===u.Kef||e===u.XG_||e===u.HO_||e===u.CWW){if(null===(t=s.get("EXT_texture_compression_rgtc")))return null;if(e===u.Fn)return t.COMPRESSED_RED_RGTC1_EXT;if(e===u.XG_)return t.COMPRESSED_SIGNED_RED_RGTC1_EXT;if(e===u.HO_)return t.COMPRESSED_RED_GREEN_RGTC2_EXT;if(e===u.CWW)return t.COMPRESSED_SIGNED_RED_GREEN_RGTC2_EXT}return e===u.V3x?i.UNSIGNED_INT_24_8:void 0!==i[e]?i[e]:null}_clientWaitAsync(){let{gl:e}=this,t=e.fenceSync(e.SYNC_GPU_COMMANDS_COMPLETE,0);return e.flush(),new Promise((r,i)=>{!function s(){let n=e.clientWaitSync(t,e.SYNC_FLUSH_COMMANDS_BIT,0);if(n===e.WAIT_FAILED){e.deleteSync(t),i();return}if(n===e.TIMEOUT_EXPIRED)return void requestAnimationFrame(s);e.deleteSync(t),r()}()})}constructor(e){this.backend=e,this.gl=this.backend.gl,this.extensions=e.extensions}}let pC=!1,pw,pM,pB;class pP{_init(){let e=this.gl;pw={[u.GJx]:e.REPEAT,[u.ghU]:e.CLAMP_TO_EDGE,[u.kTW]:e.MIRRORED_REPEAT},pM={[u.hxR]:e.NEAREST,[u.pHI]:e.NEAREST_MIPMAP_NEAREST,[u.Cfg]:e.NEAREST_MIPMAP_LINEAR,[u.k6q]:e.LINEAR,[u.kRr]:e.LINEAR_MIPMAP_NEAREST,[u.$_I]:e.LINEAR_MIPMAP_LINEAR},pB={[u.amv]:e.NEVER,[u.FFZ]:e.ALWAYS,[u.vim]:e.LESS,[u.TiK]:e.LEQUAL,[u.kO0]:e.EQUAL,[u.gWB]:e.GEQUAL,[u.eoi]:e.GREATER,[u.jzd]:e.NOTEQUAL}}getGLTextureType(e){let t,{gl:r}=this;return!0===e.isCubeTexture?r.TEXTURE_CUBE_MAP:!0===e.isDepthArrayTexture||!0===e.isDataArrayTexture||!0===e.isCompressedArrayTexture||!0===e.isTextureArray?r.TEXTURE_2D_ARRAY:!0===e.isData3DTexture?r.TEXTURE_3D:r.TEXTURE_2D}getInternalFormat(e,t,r,i){let s=arguments.length>4&&void 0!==arguments[4]&&arguments[4],{gl:n,extensions:a}=this;if(null!==e){if(void 0!==n[e])return n[e];console.warn("THREE.WebGLRenderer: Attempt to use non-existing WebGL internal format '"+e+"'")}let o=t;return t===n.RED&&(r===n.FLOAT&&(o=n.R32F),r===n.HALF_FLOAT&&(o=n.R16F),r===n.UNSIGNED_BYTE&&(o=n.R8),r===n.UNSIGNED_SHORT&&(o=n.R16),r===n.UNSIGNED_INT&&(o=n.R32UI),r===n.BYTE&&(o=n.R8I),r===n.SHORT&&(o=n.R16I),r===n.INT&&(o=n.R32I)),t===n.RED_INTEGER&&(r===n.UNSIGNED_BYTE&&(o=n.R8UI),r===n.UNSIGNED_SHORT&&(o=n.R16UI),r===n.UNSIGNED_INT&&(o=n.R32UI),r===n.BYTE&&(o=n.R8I),r===n.SHORT&&(o=n.R16I),r===n.INT&&(o=n.R32I)),t===n.RG&&(r===n.FLOAT&&(o=n.RG32F),r===n.HALF_FLOAT&&(o=n.RG16F),r===n.UNSIGNED_BYTE&&(o=n.RG8),r===n.UNSIGNED_SHORT&&(o=n.RG16),r===n.UNSIGNED_INT&&(o=n.RG32UI),r===n.BYTE&&(o=n.RG8I),r===n.SHORT&&(o=n.RG16I),r===n.INT&&(o=n.RG32I)),t===n.RG_INTEGER&&(r===n.UNSIGNED_BYTE&&(o=n.RG8UI),r===n.UNSIGNED_SHORT&&(o=n.RG16UI),r===n.UNSIGNED_INT&&(o=n.RG32UI),r===n.BYTE&&(o=n.RG8I),r===n.SHORT&&(o=n.RG16I),r===n.INT&&(o=n.RG32I)),t===n.RGB&&(r===n.FLOAT&&(o=n.RGB32F),r===n.HALF_FLOAT&&(o=n.RGB16F),r===n.UNSIGNED_BYTE&&(o=n.RGB8),r===n.UNSIGNED_SHORT&&(o=n.RGB16),r===n.UNSIGNED_INT&&(o=n.RGB32UI),r===n.BYTE&&(o=n.RGB8I),r===n.SHORT&&(o=n.RGB16I),r===n.INT&&(o=n.RGB32I),r===n.UNSIGNED_BYTE&&(o=i===u.er$&&!1===s?n.SRGB8:n.RGB8),r===n.UNSIGNED_SHORT_5_6_5&&(o=n.RGB565),r===n.UNSIGNED_SHORT_5_5_5_1&&(o=n.RGB5_A1),r===n.UNSIGNED_SHORT_4_4_4_4&&(o=n.RGB4),r===n.UNSIGNED_INT_5_9_9_9_REV&&(o=n.RGB9_E5)),t===n.RGB_INTEGER&&(r===n.UNSIGNED_BYTE&&(o=n.RGB8UI),r===n.UNSIGNED_SHORT&&(o=n.RGB16UI),r===n.UNSIGNED_INT&&(o=n.RGB32UI),r===n.BYTE&&(o=n.RGB8I),r===n.SHORT&&(o=n.RGB16I),r===n.INT&&(o=n.RGB32I)),t===n.RGBA&&(r===n.FLOAT&&(o=n.RGBA32F),r===n.HALF_FLOAT&&(o=n.RGBA16F),r===n.UNSIGNED_BYTE&&(o=n.RGBA8),r===n.UNSIGNED_SHORT&&(o=n.RGBA16),r===n.UNSIGNED_INT&&(o=n.RGBA32UI),r===n.BYTE&&(o=n.RGBA8I),r===n.SHORT&&(o=n.RGBA16I),r===n.INT&&(o=n.RGBA32I),r===n.UNSIGNED_BYTE&&(o=i===u.er$&&!1===s?n.SRGB8_ALPHA8:n.RGBA8),r===n.UNSIGNED_SHORT_4_4_4_4&&(o=n.RGBA4),r===n.UNSIGNED_SHORT_5_5_5_1&&(o=n.RGB5_A1)),t===n.RGBA_INTEGER&&(r===n.UNSIGNED_BYTE&&(o=n.RGBA8UI),r===n.UNSIGNED_SHORT&&(o=n.RGBA16UI),r===n.UNSIGNED_INT&&(o=n.RGBA32UI),r===n.BYTE&&(o=n.RGBA8I),r===n.SHORT&&(o=n.RGBA16I),r===n.INT&&(o=n.RGBA32I)),t===n.DEPTH_COMPONENT&&(r===n.UNSIGNED_SHORT&&(o=n.DEPTH_COMPONENT16),r===n.UNSIGNED_INT&&(o=n.DEPTH_COMPONENT24),r===n.FLOAT&&(o=n.DEPTH_COMPONENT32F)),t===n.DEPTH_STENCIL&&r===n.UNSIGNED_INT_24_8&&(o=n.DEPTH24_STENCIL8),(o===n.R16F||o===n.R32F||o===n.RG16F||o===n.RG32F||o===n.RGBA16F||o===n.RGBA32F)&&a.get("EXT_color_buffer_float"),o}setTextureParameters(e,t){let{gl:r,extensions:i,backend:s}=this;r.pixelStorei(r.UNPACK_FLIP_Y_WEBGL,t.flipY),r.pixelStorei(r.UNPACK_PREMULTIPLY_ALPHA_WEBGL,t.premultiplyAlpha),r.pixelStorei(r.UNPACK_ALIGNMENT,t.unpackAlignment),r.pixelStorei(r.UNPACK_COLORSPACE_CONVERSION_WEBGL,r.NONE),r.texParameteri(e,r.TEXTURE_WRAP_S,pw[t.wrapS]),r.texParameteri(e,r.TEXTURE_WRAP_T,pw[t.wrapT]),(e===r.TEXTURE_3D||e===r.TEXTURE_2D_ARRAY)&&!0!==t.isDepthArrayTexture&&!1===t.isTextureArray&&r.texParameteri(e,r.TEXTURE_WRAP_R,pw[t.wrapR]),r.texParameteri(e,r.TEXTURE_MAG_FILTER,pM[t.magFilter]);let n=void 0!==t.mipmaps&&t.mipmaps.length>0,a=t.minFilter===u.k6q&&n?u.$_I:t.minFilter;if(r.texParameteri(e,r.TEXTURE_MIN_FILTER,pM[a]),t.compareFunction&&(r.texParameteri(e,r.TEXTURE_COMPARE_MODE,r.COMPARE_REF_TO_TEXTURE),r.texParameteri(e,r.TEXTURE_COMPARE_FUNC,pB[t.compareFunction])),!0===i.has("EXT_texture_filter_anisotropic")){if(t.magFilter===u.hxR||t.minFilter!==u.Cfg&&t.minFilter!==u.$_I||t.type===u.RQf&&!1===i.has("OES_texture_float_linear"))return;if(t.anisotropy>1){let n=i.get("EXT_texture_filter_anisotropic");r.texParameterf(e,n.TEXTURE_MAX_ANISOTROPY_EXT,Math.min(t.anisotropy,s.getMaxAnisotropy()))}}}createDefaultTexture(e){let{gl:t,backend:r,defaultTextures:i}=this,s=this.getGLTextureType(e),n=i[s];void 0===n&&(n=t.createTexture(),r.state.bindTexture(s,n),t.texParameteri(s,t.TEXTURE_MIN_FILTER,t.NEAREST),t.texParameteri(s,t.TEXTURE_MAG_FILTER,t.NEAREST),i[s]=n),r.set(e,{textureGPU:n,glTextureType:s,isDefault:!0})}createTexture(e,t){let{gl:r,backend:i}=this,{levels:s,width:n,height:a,depth:o}=t,l=i.utils.convert(e.format,e.colorSpace),u=i.utils.convert(e.type),d=this.getInternalFormat(e.internalFormat,l,u,e.colorSpace,e.isVideoTexture),h=r.createTexture(),c=this.getGLTextureType(e);i.state.bindTexture(c,h),this.setTextureParameters(c,e),e.isDepthArrayTexture||e.isDataArrayTexture||e.isCompressedArrayTexture||e.isTextureArray?r.texStorage3D(r.TEXTURE_2D_ARRAY,s,d,n,a,o):e.isData3DTexture?r.texStorage3D(r.TEXTURE_3D,s,d,n,a,o):e.isVideoTexture||r.texStorage2D(c,s,d,n,a),i.set(e,{textureGPU:h,glTextureType:c,glFormat:l,glType:u,glInternalFormat:d})}copyBufferToTexture(e,t){let{gl:r,backend:i}=this,{textureGPU:s,glTextureType:n,glFormat:a,glType:o}=i.get(t),{width:l,height:u}=t.source.data;r.bindBuffer(r.PIXEL_UNPACK_BUFFER,e),i.state.bindTexture(n,s),r.pixelStorei(r.UNPACK_FLIP_Y_WEBGL,!1),r.pixelStorei(r.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!1),r.texSubImage2D(n,0,0,0,l,u,a,o,0),r.bindBuffer(r.PIXEL_UNPACK_BUFFER,null),i.state.unbindTexture()}updateTexture(e,t){let{gl:r}=this,{width:i,height:s}=t,{textureGPU:n,glTextureType:a,glFormat:o,glType:l,glInternalFormat:u}=this.backend.get(e);if(e.isRenderTargetTexture||void 0===n)return;let d=e=>e.isDataTexture?e.image.data:"undefined"!=typeof HTMLImageElement&&e instanceof HTMLImageElement||"undefined"!=typeof HTMLCanvasElement&&e instanceof HTMLCanvasElement||"undefined"!=typeof ImageBitmap&&e instanceof ImageBitmap||e instanceof OffscreenCanvas?e:e.data;if(this.backend.state.bindTexture(a,n),this.setTextureParameters(a,e),e.isCompressedTexture){let i=e.mipmaps,s=t.image;for(let t=0;t<i.length;t++){let n=i[t];e.isCompressedArrayTexture?e.format!==r.RGBA?null!==o?r.compressedTexSubImage3D(r.TEXTURE_2D_ARRAY,t,0,0,0,n.width,n.height,s.depth,o,n.data):console.warn("THREE.WebGLRenderer: Attempt to load unsupported compressed texture format in .uploadTexture()"):r.texSubImage3D(r.TEXTURE_2D_ARRAY,t,0,0,0,n.width,n.height,s.depth,o,l,n.data):null!==o?r.compressedTexSubImage2D(r.TEXTURE_2D,t,0,0,n.width,n.height,o,n.data):console.warn("Unsupported compressed texture format")}}else if(e.isCubeTexture){let e=t.images;for(let t=0;t<6;t++){let n=d(e[t]);r.texSubImage2D(r.TEXTURE_CUBE_MAP_POSITIVE_X+t,0,0,0,i,s,o,l,n)}}else if(e.isDataArrayTexture||e.isDepthArrayTexture){let e=t.image;r.texSubImage3D(r.TEXTURE_2D_ARRAY,0,0,0,0,e.width,e.height,e.depth,o,l,e.data)}else if(e.isData3DTexture){let e=t.image;r.texSubImage3D(r.TEXTURE_3D,0,0,0,0,e.width,e.height,e.depth,o,l,e.data)}else if(e.isVideoTexture)e.update(),r.texImage2D(a,0,u,o,l,t.image);else{let e=d(t.image);r.texSubImage2D(a,0,0,0,i,s,o,l,e)}}generateMipmaps(e){let{gl:t,backend:r}=this,{textureGPU:i,glTextureType:s}=r.get(e);r.state.bindTexture(s,i),t.generateMipmap(s)}deallocateRenderBuffers(e){let{gl:t,backend:r}=this;if(e){let i=r.get(e);if(i.renderBufferStorageSetup=void 0,i.framebuffers){for(let e in i.framebuffers)t.deleteFramebuffer(i.framebuffers[e]);delete i.framebuffers}if(i.depthRenderbuffer&&(t.deleteRenderbuffer(i.depthRenderbuffer),delete i.depthRenderbuffer),i.stencilRenderbuffer&&(t.deleteRenderbuffer(i.stencilRenderbuffer),delete i.stencilRenderbuffer),i.msaaFrameBuffer&&(t.deleteFramebuffer(i.msaaFrameBuffer),delete i.msaaFrameBuffer),i.msaaRenderbuffers){for(let e=0;e<i.msaaRenderbuffers.length;e++)t.deleteRenderbuffer(i.msaaRenderbuffers[e]);delete i.msaaRenderbuffers}}}destroyTexture(e){let{gl:t,backend:r}=this,{textureGPU:i,renderTarget:s}=r.get(e);this.deallocateRenderBuffers(s),t.deleteTexture(i),r.delete(e)}copyTextureToTexture(e,t){let r,i,s,n,a,o,l,u,d,h=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,c=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,p=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,g=arguments.length>5&&void 0!==arguments[5]?arguments[5]:0,{gl:m,backend:f}=this,{state:y}=this.backend,{textureGPU:x,glTextureType:b,glType:T,glFormat:v}=f.get(t);y.bindTexture(b,x);let _=e.isCompressedTexture?e.mipmaps[g]:e.image;if(null!==h)r=h.max.x-h.min.x,i=h.max.y-h.min.y,s=h.isBox3?h.max.z-h.min.z:1,n=h.min.x,a=h.min.y,o=h.isBox3?h.min.z:0;else{let t=Math.pow(2,-p);r=Math.floor(_.width*t),i=Math.floor(_.height*t),s=e.isDataArrayTexture||e.isDepthArrayTexture?_.depth:e.isData3DTexture?Math.floor(_.depth*t):1,n=0,a=0,o=0}null!==c?(l=c.x,u=c.y,d=c.z):(l=0,u=0,d=0),m.pixelStorei(m.UNPACK_FLIP_Y_WEBGL,t.flipY),m.pixelStorei(m.UNPACK_PREMULTIPLY_ALPHA_WEBGL,t.premultiplyAlpha),m.pixelStorei(m.UNPACK_ALIGNMENT,t.unpackAlignment);let N=m.getParameter(m.UNPACK_ROW_LENGTH),S=m.getParameter(m.UNPACK_IMAGE_HEIGHT),R=m.getParameter(m.UNPACK_SKIP_PIXELS),A=m.getParameter(m.UNPACK_SKIP_ROWS),E=m.getParameter(m.UNPACK_SKIP_IMAGES);m.pixelStorei(m.UNPACK_ROW_LENGTH,_.width),m.pixelStorei(m.UNPACK_IMAGE_HEIGHT,_.height),m.pixelStorei(m.UNPACK_SKIP_PIXELS,n),m.pixelStorei(m.UNPACK_SKIP_ROWS,a),m.pixelStorei(m.UNPACK_SKIP_IMAGES,o);let C=t.isDataArrayTexture||t.isData3DTexture||t.isDepthArrayTexture;if(e.isRenderTargetTexture||e.isDepthTexture){let s=f.get(e),o=f.get(t),d=f.get(s.renderTarget),h=f.get(o.renderTarget),c=d.framebuffers[s.cacheKey],p=h.framebuffers[o.cacheKey];y.bindFramebuffer(m.READ_FRAMEBUFFER,c),y.bindFramebuffer(m.DRAW_FRAMEBUFFER,p);let g=m.COLOR_BUFFER_BIT;e.isDepthTexture&&(g=m.DEPTH_BUFFER_BIT),m.blitFramebuffer(n,a,r,i,l,u,r,i,g,m.NEAREST),y.bindFramebuffer(m.READ_FRAMEBUFFER,null),y.bindFramebuffer(m.DRAW_FRAMEBUFFER,null)}else C?e.isDataTexture||e.isData3DTexture?m.texSubImage3D(b,g,l,u,d,r,i,s,v,T,_.data):t.isCompressedArrayTexture?m.compressedTexSubImage3D(b,g,l,u,d,r,i,s,v,_.data):m.texSubImage3D(b,g,l,u,d,r,i,s,v,T,_):e.isDataTexture?m.texSubImage2D(b,g,l,u,r,i,v,T,_.data):e.isCompressedTexture?m.compressedTexSubImage2D(b,g,l,u,_.width,_.height,v,_.data):m.texSubImage2D(b,g,l,u,r,i,v,T,_);m.pixelStorei(m.UNPACK_ROW_LENGTH,N),m.pixelStorei(m.UNPACK_IMAGE_HEIGHT,S),m.pixelStorei(m.UNPACK_SKIP_PIXELS,R),m.pixelStorei(m.UNPACK_SKIP_ROWS,A),m.pixelStorei(m.UNPACK_SKIP_IMAGES,E),0===g&&t.generateMipmaps&&m.generateMipmap(b),y.unbindTexture()}copyFramebufferToTexture(e,t,r){let{gl:i}=this,{state:s}=this.backend,{textureGPU:n}=this.backend.get(e),{x:a,y:o,z:l,w:u}=r,d=!0===e.isDepthTexture||t.renderTarget&&t.renderTarget.samples>0,h=t.renderTarget?t.renderTarget.height:this.backend.getDrawingBufferSize().y;if(d){let r,d,c=0!==a||0!==o;if(!0===e.isDepthTexture?(r=i.DEPTH_BUFFER_BIT,d=i.DEPTH_ATTACHMENT,t.stencil&&(r|=i.STENCIL_BUFFER_BIT)):(r=i.COLOR_BUFFER_BIT,d=i.COLOR_ATTACHMENT0),c){let e=this.backend.get(t.renderTarget),d=e.framebuffers[t.getCacheKey()],c=e.msaaFrameBuffer;s.bindFramebuffer(i.DRAW_FRAMEBUFFER,d),s.bindFramebuffer(i.READ_FRAMEBUFFER,c);let p=h-o-u;i.blitFramebuffer(a,p,a+l,p+u,a,p,a+l,p+u,r,i.NEAREST),s.bindFramebuffer(i.READ_FRAMEBUFFER,d),s.bindTexture(i.TEXTURE_2D,n),i.copyTexSubImage2D(i.TEXTURE_2D,0,0,0,a,p,l,u),s.unbindTexture()}else{let e=i.createFramebuffer();s.bindFramebuffer(i.DRAW_FRAMEBUFFER,e),i.framebufferTexture2D(i.DRAW_FRAMEBUFFER,d,i.TEXTURE_2D,n,0),i.blitFramebuffer(0,0,l,u,0,0,l,u,r,i.NEAREST),i.deleteFramebuffer(e)}}else s.bindTexture(i.TEXTURE_2D,n),i.copyTexSubImage2D(i.TEXTURE_2D,0,0,0,a,h-u-o,l,u),s.unbindTexture();e.generateMipmaps&&this.generateMipmaps(e),this.backend._setFramebuffer(t)}setupRenderBufferStorage(e,t,r){let i=arguments.length>3&&void 0!==arguments[3]&&arguments[3],{gl:s}=this,n=t.renderTarget,{depthTexture:a,depthBuffer:o,stencilBuffer:l,width:u,height:d}=n;if(s.bindRenderbuffer(s.RENDERBUFFER,e),o&&!l){let t=s.DEPTH_COMPONENT24;!0===i?this.extensions.get("WEBGL_multisampled_render_to_texture").renderbufferStorageMultisampleEXT(s.RENDERBUFFER,n.samples,t,u,d):r>0?(a&&a.isDepthTexture&&a.type===s.FLOAT&&(t=s.DEPTH_COMPONENT32F),s.renderbufferStorageMultisample(s.RENDERBUFFER,r,t,u,d)):s.renderbufferStorage(s.RENDERBUFFER,t,u,d),s.framebufferRenderbuffer(s.FRAMEBUFFER,s.DEPTH_ATTACHMENT,s.RENDERBUFFER,e)}else o&&l&&(r>0?s.renderbufferStorageMultisample(s.RENDERBUFFER,r,s.DEPTH24_STENCIL8,u,d):s.renderbufferStorage(s.RENDERBUFFER,s.DEPTH_STENCIL,u,d),s.framebufferRenderbuffer(s.FRAMEBUFFER,s.DEPTH_STENCIL_ATTACHMENT,s.RENDERBUFFER,e))}async copyTextureToBuffer(e,t,r,i,s,n){let{backend:a,gl:o}=this,{textureGPU:l,glFormat:u,glType:d}=this.backend.get(e),h=o.createFramebuffer();o.bindFramebuffer(o.READ_FRAMEBUFFER,h);let c=e.isCubeTexture?o.TEXTURE_CUBE_MAP_POSITIVE_X+n:o.TEXTURE_2D;o.framebufferTexture2D(o.READ_FRAMEBUFFER,o.COLOR_ATTACHMENT0,c,l,0);let p=this._getTypedArrayType(d),g=i*s*this._getBytesPerTexel(d,u),m=o.createBuffer();o.bindBuffer(o.PIXEL_PACK_BUFFER,m),o.bufferData(o.PIXEL_PACK_BUFFER,g,o.STREAM_READ),o.readPixels(t,r,i,s,u,d,0),o.bindBuffer(o.PIXEL_PACK_BUFFER,null),await a.utils._clientWaitAsync();let f=new p(g/p.BYTES_PER_ELEMENT);return o.bindBuffer(o.PIXEL_PACK_BUFFER,m),o.getBufferSubData(o.PIXEL_PACK_BUFFER,0,f),o.bindBuffer(o.PIXEL_PACK_BUFFER,null),o.deleteFramebuffer(h),f}_getTypedArrayType(e){let{gl:t}=this;if(e===t.UNSIGNED_BYTE)return Uint8Array;if(e===t.UNSIGNED_SHORT_4_4_4_4||e===t.UNSIGNED_SHORT_5_5_5_1||e===t.UNSIGNED_SHORT_5_6_5||e===t.UNSIGNED_SHORT)return Uint16Array;if(e===t.UNSIGNED_INT)return Uint32Array;if(e===t.HALF_FLOAT)return Uint16Array;if(e===t.FLOAT)return Float32Array;throw Error("Unsupported WebGL type: ".concat(e))}_getBytesPerTexel(e,t){let{gl:r}=this,i=0;return(e===r.UNSIGNED_BYTE&&(i=1),(e===r.UNSIGNED_SHORT_4_4_4_4||e===r.UNSIGNED_SHORT_5_5_5_1||e===r.UNSIGNED_SHORT_5_6_5||e===r.UNSIGNED_SHORT||e===r.HALF_FLOAT)&&(i=2),(e===r.UNSIGNED_INT||e===r.FLOAT)&&(i=4),t===r.RGBA)?4*i:t===r.RGB?3*i:t===r.ALPHA?i:void 0}constructor(e){this.backend=e,this.gl=e.gl,this.extensions=e.extensions,this.defaultTextures={},!1===pC&&(this._init(),pC=!0)}}class pF{get(e){let t=this.extensions[e];return void 0===t&&(t=this.gl.getExtension(e),this.extensions[e]=t),t}has(e){return this.availableExtensions.includes(e)}constructor(e){this.backend=e,this.gl=this.backend.gl,this.availableExtensions=this.gl.getSupportedExtensions(),this.extensions={}}}class pU{getMaxAnisotropy(){if(null!==this.maxAnisotropy)return this.maxAnisotropy;let e=this.backend.gl,t=this.backend.extensions;if(!0===t.has("EXT_texture_filter_anisotropic")){let r=t.get("EXT_texture_filter_anisotropic");this.maxAnisotropy=e.getParameter(r.MAX_TEXTURE_MAX_ANISOTROPY_EXT)}else this.maxAnisotropy=0;return this.maxAnisotropy}constructor(e){this.backend=e,this.maxAnisotropy=null}}let pL={WEBGL_multi_draw:"WEBGL_multi_draw",WEBGL_compressed_texture_astc:"texture-compression-astc",WEBGL_compressed_texture_etc:"texture-compression-etc2",WEBGL_compressed_texture_etc1:"texture-compression-etc1",WEBGL_compressed_texture_pvrtc:"texture-compression-pvrtc",WEBKIT_WEBGL_compressed_texture_pvrtc:"texture-compression-pvrtc",WEBGL_compressed_texture_s3tc:"texture-compression-bc",EXT_texture_compression_bptc:"texture-compression-bptc",EXT_disjoint_timer_query_webgl2:"timestamp-query",OVR_multiview2:"OVR_multiview2"};class pI{render(e,t){let{gl:r,mode:i,object:s,type:n,info:a,index:o}=this;0!==o?r.drawElements(i,t,n,e):r.drawArrays(i,e,t),a.update(s,t,1)}renderInstances(e,t,r){let{gl:i,mode:s,type:n,index:a,object:o,info:l}=this;0!==r&&(0!==a?i.drawElementsInstanced(s,t,n,e,r):i.drawArraysInstanced(s,e,t,r),l.update(o,t,r))}renderMultiDraw(e,t,r){let{extensions:i,mode:s,object:n,info:a}=this;if(0===r)return;let o=i.get("WEBGL_multi_draw");if(null===o)for(let i=0;i<r;i++)this.render(e[i],t[i]);else{0!==this.index?o.multiDrawElementsWEBGL(s,t,0,this.type,e,0,r):o.multiDrawArraysWEBGL(s,e,0,t,0,r);let i=0;for(let e=0;e<r;e++)i+=t[e];a.update(n,i,1)}}renderMultiDrawInstances(e,t,r,i){let{extensions:s,mode:n,object:a,info:o}=this;if(0===r)return;let l=s.get("WEBGL_multi_draw");if(null===l)for(let s=0;s<r;s++)this.renderInstances(e[s],t[s],i[s]);else{0!==this.index?l.multiDrawElementsInstancedWEBGL(n,t,0,this.type,e,0,i,0,r):l.multiDrawArraysInstancedWEBGL(n,e,0,t,0,i,0,r);let s=0;for(let e=0;e<r;e++)s+=t[e]*i[e];o.update(a,s,1)}}constructor(e){this.gl=e.gl,this.extensions=e.extensions,this.info=e.renderer.info,this.mode=null,this.index=0,this.type=null,this.object=null}}class pD{allocateQueriesForContext(){}async resolveQueriesAsync(){}dispose(){}constructor(e=256){this.trackTimestamp=!0,this.maxQueries=e,this.currentQueryIndex=0,this.queryOffsets=new Map,this.isDisposed=!1,this.lastValue=0,this.pendingResolve=!1}}class pO extends pD{allocateQueriesForContext(e){if(!this.trackTimestamp)return null;if(this.currentQueryIndex+2>this.maxQueries)return(0,u.mcG)("WebGPUTimestampQueryPool [".concat(this.type,"]: Maximum number of queries exceeded, when using trackTimestamp it is necessary to resolves the queries via renderer.resolveTimestampsAsync( THREE.TimestampQuery.").concat(this.type.toUpperCase()," ).")),null;let t=this.currentQueryIndex;return this.currentQueryIndex+=2,this.queryStates.set(t,"inactive"),this.queryOffsets.set(e.id,t),t}beginQuery(e){if(!this.trackTimestamp||this.isDisposed)return;let t=this.queryOffsets.get(e.id);if(null==t||null!==this.activeQuery)return;let r=this.queries[t];if(r)try{"inactive"===this.queryStates.get(t)&&(this.gl.beginQuery(this.ext.TIME_ELAPSED_EXT,r),this.activeQuery=t,this.queryStates.set(t,"started"))}catch(e){console.error("Error in beginQuery:",e),this.activeQuery=null,this.queryStates.set(t,"inactive")}}endQuery(e){if(!this.trackTimestamp||this.isDisposed)return;let t=this.queryOffsets.get(e.id);if(null!=t&&this.activeQuery===t)try{this.gl.endQuery(this.ext.TIME_ELAPSED_EXT),this.queryStates.set(t,"ended"),this.activeQuery=null}catch(e){console.error("Error in endQuery:",e),this.queryStates.set(t,"inactive"),this.activeQuery=null}}async resolveQueriesAsync(){if(!this.trackTimestamp||this.pendingResolve)return this.lastValue;this.pendingResolve=!0;try{let e=[];for(let[t,r]of this.queryStates)if("ended"===r){let r=this.queries[t];e.push(this.resolveQuery(r))}if(0===e.length)return this.lastValue;let t=(await Promise.all(e)).reduce((e,t)=>e+t,0);return this.lastValue=t,this.currentQueryIndex=0,this.queryOffsets.clear(),this.queryStates.clear(),this.activeQuery=null,t}catch(e){return console.error("Error resolving queries:",e),this.lastValue}finally{this.pendingResolve=!1}}async resolveQuery(e){return new Promise(t=>{let r;if(this.isDisposed)return void t(this.lastValue);let i=!1,s=()=>{r&&(clearTimeout(r),r=null)},n=e=>{i||(i=!0,s(),t(e))},a=()=>{if(this.isDisposed)return void n(this.lastValue);try{if(this.gl.getParameter(this.ext.GPU_DISJOINT_EXT))return void n(this.lastValue);if(!this.gl.getQueryParameter(e,this.gl.QUERY_RESULT_AVAILABLE)){r=setTimeout(a,1);return}let i=this.gl.getQueryParameter(e,this.gl.QUERY_RESULT);t(Number(i)/1e6)}catch(e){console.error("Error checking query:",e),t(this.lastValue)}};a()})}dispose(){if(!this.isDisposed&&(this.isDisposed=!0,this.trackTimestamp)){for(let e of this.queries)this.gl.deleteQuery(e);this.queries=[],this.queryStates.clear(),this.queryOffsets.clear(),this.lastValue=0,this.activeQuery=null}}constructor(e,t,r=2048){if(super(r),this.gl=e,this.type=t,this.ext=e.getExtension("EXT_disjoint_timer_query_webgl2")||e.getExtension("EXT_disjoint_timer_query"),!this.ext){console.warn("EXT_disjoint_timer_query not supported; timestamps will be disabled."),this.trackTimestamp=!1;return}this.queries=[];for(let t=0;t<this.maxQueries;t++)this.queries.push(e.createQuery());this.activeQuery=null,this.queryStates=new Map}}class pV extends p_{init(e){super.init(e);let t=this.parameters,r={antialias:e.samples>0,alpha:!0,depth:e.depth,stencil:e.stencil},i=void 0!==t.context?t.context:e.domElement.getContext("webgl2",r);function s(t){t.preventDefault();let r={api:"WebGL",message:t.statusMessage||"Unknown reason",reason:null,originalEvent:t};e.onDeviceLost(r)}this._onContextLost=s,e.domElement.addEventListener("webglcontextlost",s,!1),this.gl=i,this.extensions=new pF(this),this.capabilities=new pU(this),this.attributeUtils=new pR(this),this.textureUtils=new pP(this),this.bufferRenderer=new pI(this),this.state=new pA(this),this.utils=new pE(this),this.extensions.get("EXT_color_buffer_float"),this.extensions.get("WEBGL_clip_cull_distance"),this.extensions.get("OES_texture_float_linear"),this.extensions.get("EXT_color_buffer_half_float"),this.extensions.get("WEBGL_multisampled_render_to_texture"),this.extensions.get("WEBGL_render_shared_exponent"),this.extensions.get("WEBGL_multi_draw"),this.extensions.get("OVR_multiview2"),this.disjoint=this.extensions.get("EXT_disjoint_timer_query_webgl2"),this.parallel=this.extensions.get("KHR_parallel_shader_compile")}get coordinateSystem(){return u.TdN}async getArrayBufferAsync(e){return await this.attributeUtils.getArrayBufferAsync(e)}async waitForGPU(){await this.utils._clientWaitAsync()}async makeXRCompatible(){!0!==this.gl.getContextAttributes().xrCompatible&&await this.gl.makeXRCompatible()}setXRTarget(e){this._xrFramebuffer=e}setXRRenderTargetTextures(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,i=this.gl;if(this.set(e.texture,{textureGPU:t,glInternalFormat:i.RGBA8}),null!==r){let t=e.stencilBuffer?i.DEPTH24_STENCIL8:i.DEPTH_COMPONENT24;this.set(e.depthTexture,{textureGPU:r,glInternalFormat:t}),!0===this.extensions.has("WEBGL_multisampled_render_to_texture")&&!0===e.autoAllocateDepthBuffer&&!1===e.multiview&&console.warn("THREE.WebGLBackend: Render-to-texture extension was disabled because an external texture was provided"),e.autoAllocateDepthBuffer=!1}}initTimestampQuery(e){if(!this.disjoint||!this.trackTimestamp)return;let t=e.isComputeNode?"compute":"render";this.timestampQueryPool[t]||(this.timestampQueryPool[t]=new pO(this.gl,t,2048));let r=this.timestampQueryPool[t];null!==r.allocateQueriesForContext(e)&&r.beginQuery(e)}prepareTimestampBuffer(e){if(!this.disjoint||!this.trackTimestamp)return;let t=e.isComputeNode?"compute":"render";this.timestampQueryPool[t].endQuery(e)}getContext(){return this.gl}beginRender(e){let{state:t,gl:r}=this,i=this.get(e);if(e.viewport?this.updateViewport(e):t.viewport(0,0,r.drawingBufferWidth,r.drawingBufferHeight),e.scissor){let{x:r,y:i,width:s,height:n}=e.scissorValue;t.scissor(r,e.height-n-i,s,n)}this.initTimestampQuery(e),i.previousContext=this._currentContext,this._currentContext=e,this._setFramebuffer(e),this.clear(e.clearColor,e.clearDepth,e.clearStencil,e,!1);let s=e.occlusionQueryCount;s>0&&(i.currentOcclusionQueries=i.occlusionQueries,i.currentOcclusionQueryObjects=i.occlusionQueryObjects,i.lastOcclusionObject=null,i.occlusionQueries=Array(s),i.occlusionQueryObjects=Array(s),i.occlusionQueryIndex=0)}finishRender(e){let{gl:t,state:r}=this,i=this.get(e),s=i.previousContext;r.resetVertexState();let n=e.occlusionQueryCount;n>0&&(n>i.occlusionQueryIndex&&t.endQuery(t.ANY_SAMPLES_PASSED),this.resolveOccludedAsync(e));let a=e.textures;if(null!==a)for(let e=0;e<a.length;e++){let t=a[e];t.generateMipmaps&&this.generateMipmaps(t)}if(this._currentContext=s,null!==e.textures&&e.renderTarget){let i=this.get(e.renderTarget),{samples:s}=e.renderTarget;if(s>0&&!1===this._useMultisampledExtension(e.renderTarget)){let s=i.framebuffers[e.getCacheKey()],n=t.COLOR_BUFFER_BIT,a=i.msaaFrameBuffer,o=e.textures;r.bindFramebuffer(t.READ_FRAMEBUFFER,a),r.bindFramebuffer(t.DRAW_FRAMEBUFFER,s);for(let r=0;r<o.length;r++)if(e.scissor){let{x:r,y:s,width:a,height:o}=e.scissorValue,l=e.height-o-s;t.blitFramebuffer(r,l,r+a,l+o,r,l,r+a,l+o,n,t.NEAREST),!0===this._supportsInvalidateFramebuffer&&t.invalidateSubFramebuffer(t.READ_FRAMEBUFFER,i.invalidationArray,r,l,a,o)}else t.blitFramebuffer(0,0,e.width,e.height,0,0,e.width,e.height,n,t.NEAREST),!0===this._supportsInvalidateFramebuffer&&t.invalidateFramebuffer(t.READ_FRAMEBUFFER,i.invalidationArray)}}null!==s&&(this._setFramebuffer(s),s.viewport?this.updateViewport(s):r.viewport(0,0,t.drawingBufferWidth,t.drawingBufferHeight)),this.prepareTimestampBuffer(e)}resolveOccludedAsync(e){let t=this.get(e),{currentOcclusionQueries:r,currentOcclusionQueryObjects:i}=t;if(r&&i){let e=new WeakSet,{gl:s}=this;t.currentOcclusionQueryObjects=null,t.currentOcclusionQueries=null;let n=()=>{let a=0;for(let t=0;t<r.length;t++){let n=r[t];null!==n&&s.getQueryParameter(n,s.QUERY_RESULT_AVAILABLE)&&(0===s.getQueryParameter(n,s.QUERY_RESULT)&&e.add(i[t]),r[t]=null,s.deleteQuery(n),a++)}a<r.length?requestAnimationFrame(n):t.occluded=e};n()}}isOccluded(e,t){let r=this.get(e);return r.occluded&&r.occluded.has(t)}updateViewport(e){let{state:t}=this,{x:r,y:i,width:s,height:n}=e.viewportValue;t.viewport(r,e.height-n-i,s,n)}setScissorTest(e){this.state.setScissorTest(e)}getClearColor(){let e=super.getClearColor();return e.r*=e.a,e.g*=e.a,e.b*=e.a,e}clear(e,t,r){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,s=!(arguments.length>4)||void 0===arguments[4]||arguments[4],{gl:n,renderer:a}=this;null===i&&(i={textures:null,clearColorValue:this.getClearColor()});let o=0;if(e&&(o|=n.COLOR_BUFFER_BIT),t&&(o|=n.DEPTH_BUFFER_BIT),r&&(o|=n.STENCIL_BUFFER_BIT),0!==o){let l;l=i.clearColorValue?i.clearColorValue:this.getClearColor();let u=a.getClearDepth(),d=a.getClearStencil();if(t&&this.state.setDepthMask(!0),null===i.textures)n.clearColor(l.r,l.g,l.b,l.a),n.clear(o);else{if(s&&this._setFramebuffer(i),e)for(let e=0;e<i.textures.length;e++)0===e?n.clearBufferfv(n.COLOR,e,[l.r,l.g,l.b,l.a]):n.clearBufferfv(n.COLOR,e,[0,0,0,1]);t&&r?n.clearBufferfi(n.DEPTH_STENCIL,0,u,d):t?n.clearBufferfv(n.DEPTH,0,[u]):r&&n.clearBufferiv(n.STENCIL,0,[d])}}}beginCompute(e){let{state:t,gl:r}=this;t.bindFramebuffer(r.FRAMEBUFFER,null),this.initTimestampQuery(e)}compute(e,t,r,i){let{state:s,gl:n}=this;!1===this.discard&&(n.enable(n.RASTERIZER_DISCARD),this.discard=!0);let{programGPU:a,transformBuffers:o,attributes:l}=this.get(i),u=this._getVaoKey(l),d=this.vaoCache[u];void 0===d?this._createVao(l):s.setVertexState(d),s.useProgram(a),this._bindUniforms(r);let h=this._getTransformFeedback(o);n.bindTransformFeedback(n.TRANSFORM_FEEDBACK,h),n.beginTransformFeedback(n.POINTS),l[0].isStorageInstancedBufferAttribute?n.drawArraysInstanced(n.POINTS,0,1,t.count):n.drawArrays(n.POINTS,0,t.count),n.endTransformFeedback(),n.bindTransformFeedback(n.TRANSFORM_FEEDBACK,null);for(let e=0;e<o.length;e++){let t=o[e];t.pbo&&this.textureUtils.copyBufferToTexture(t.transformBuffer,t.pbo),t.switchBuffers()}}finishCompute(e){let t=this.gl;this.discard=!1,t.disable(t.RASTERIZER_DISCARD),this.prepareTimestampBuffer(e),this._currentContext&&this._setFramebuffer(this._currentContext)}_isRenderCameraDepthArray(e){return e.depthTexture&&e.depthTexture.isDepthArrayTexture&&e.camera.isArrayCamera}draw(e){let{object:t,pipeline:r,material:i,context:s,hardwareClippingPlanes:n}=e,{programGPU:a}=this.get(r),{gl:o,state:l}=this,d=this.get(s),h=e.getDrawParameters();if(null===h)return;this._bindUniforms(e.getBindings());let c=t.isMesh&&0>t.matrixWorld.determinant();l.setMaterial(i,c,n),l.useProgram(a);let p=this.get(e),g=p.staticVao;if(void 0===g||p.geometryId!==e.geometry.id){let t=this._getVaoKey(e.getAttributes());if(void 0===(g=this.vaoCache[t])){let t;({vaoGPU:g,staticVao:t}=this._createVao(e.getAttributes())),t&&(p.staticVao=g,p.geometryId=e.geometry.id)}}let m=e.getIndex(),f=null!==m?this.get(m).bufferGPU:null;l.setVertexState(g,f);let y=d.lastOcclusionObject;if(y!==t&&void 0!==y){if(null!==y&&!0===y.occlusionTest&&(o.endQuery(o.ANY_SAMPLES_PASSED),d.occlusionQueryIndex++),!0===t.occlusionTest){let e=o.createQuery();o.beginQuery(o.ANY_SAMPLES_PASSED,e),d.occlusionQueries[d.occlusionQueryIndex]=e,d.occlusionQueryObjects[d.occlusionQueryIndex]=t}d.lastOcclusionObject=t}let x=this.bufferRenderer;t.isPoints?x.mode=o.POINTS:t.isLineSegments?x.mode=o.LINES:t.isLine?x.mode=o.LINE_STRIP:t.isLineLoop?x.mode=o.LINE_LOOP:!0===i.wireframe?(l.setLineWidth(i.wireframeLinewidth*this.renderer.getPixelRatio()),x.mode=o.LINES):x.mode=o.TRIANGLES;let{vertexCount:b,instanceCount:T}=h,{firstVertex:v}=h;if(x.object=t,null!==m){v*=m.array.BYTES_PER_ELEMENT;let e=this.get(m);x.index=m.count,x.type=e.type}else x.index=0;let _=()=>{t.isBatchedMesh?null!==t._multiDrawInstances?((0,u.mcG)("THREE.WebGLBackend: renderMultiDrawInstances has been deprecated and will be removed in r184. Append to renderMultiDraw arguments and use indirection."),x.renderMultiDrawInstances(t._multiDrawStarts,t._multiDrawCounts,t._multiDrawCount,t._multiDrawInstances)):this.hasFeature("WEBGL_multi_draw")?x.renderMultiDraw(t._multiDrawStarts,t._multiDrawCounts,t._multiDrawCount):(0,u.mcG)("THREE.WebGLRenderer: WEBGL_multi_draw not supported."):T>1?x.renderInstances(v,b,T):x.render(v,b)};if(!0===e.camera.isArrayCamera&&e.camera.cameras.length>0&&!1===e.camera.isMultiViewCamera){let r=this.get(e.camera),i=e.camera.cameras,s=e.getBindingGroup("cameraIndex").bindings[0];if(void 0===r.indexesGPU||r.indexesGPU.length!==i.length){let e=new Uint32Array([0,0,0,0]),t=[];for(let r=0,s=i.length;r<s;r++){let i=o.createBuffer();e[0]=r,o.bindBuffer(o.UNIFORM_BUFFER,i),o.bufferData(o.UNIFORM_BUFFER,e,o.STATIC_DRAW),t.push(i)}r.indexesGPU=t}let n=this.get(s),a=this.renderer.getPixelRatio(),u=this._currentContext.renderTarget,d=this._isRenderCameraDepthArray(this._currentContext),h=this._currentContext.activeCubeFace;if(d){let e=this.get(u.depthTexture);if(e.clearedRenderId!==this.renderer._nodes.nodeFrame.renderId){e.clearedRenderId=this.renderer._nodes.nodeFrame.renderId;let{stencilBuffer:t}=u;for(let e=0,r=i.length;e<r;e++)this.renderer._activeCubeFace=e,this._currentContext.activeCubeFace=e,this._setFramebuffer(this._currentContext),this.clear(!1,!0,t,this._currentContext,!1);this.renderer._activeCubeFace=h,this._currentContext.activeCubeFace=h}}for(let s=0,u=i.length;s<u;s++){let u=i[s];if(t.layers.test(u.layers)){d&&(this.renderer._activeCubeFace=s,this._currentContext.activeCubeFace=s,this._setFramebuffer(this._currentContext));let t=u.viewport;if(void 0!==t){let r=t.x*a,i=t.y*a,s=t.width*a,n=t.height*a;l.viewport(Math.floor(r),Math.floor(e.context.height-n-i),Math.floor(s),Math.floor(n))}l.bindBufferBase(o.UNIFORM_BUFFER,n.index,r.indexesGPU[s]),_()}this._currentContext.activeCubeFace=h,this.renderer._activeCubeFace=h}}else _()}needsRenderUpdate(){return!1}getRenderCacheKey(){return""}createDefaultTexture(e){this.textureUtils.createDefaultTexture(e)}createTexture(e,t){this.textureUtils.createTexture(e,t)}updateTexture(e,t){this.textureUtils.updateTexture(e,t)}generateMipmaps(e){this.textureUtils.generateMipmaps(e)}destroyTexture(e){this.textureUtils.destroyTexture(e)}async copyTextureToBuffer(e,t,r,i,s,n){return this.textureUtils.copyTextureToBuffer(e,t,r,i,s,n)}createSampler(){}destroySampler(){}createNodeBuilder(e,t){return new pb(e,t)}createProgram(e){let t=this.gl,{stage:r,code:i}=e,s="fragment"===r?t.createShader(t.FRAGMENT_SHADER):t.createShader(t.VERTEX_SHADER);t.shaderSource(s,i),t.compileShader(s),this.set(e,{shaderGPU:s})}destroyProgram(e){this.delete(e)}createRenderPipeline(e,t){let r=this.gl,i=e.pipeline,{fragmentProgram:s,vertexProgram:n}=i,a=r.createProgram(),o=this.get(s).shaderGPU,l=this.get(n).shaderGPU;if(r.attachShader(a,o),r.attachShader(a,l),r.linkProgram(a),this.set(i,{programGPU:a,fragmentShader:o,vertexShader:l}),null!==t&&this.parallel){let s=new Promise(t=>{let s=this.parallel,n=()=>{r.getProgramParameter(a,s.COMPLETION_STATUS_KHR)?(this._completeCompile(e,i),t()):requestAnimationFrame(n)};n()});t.push(s);return}this._completeCompile(e,i)}_handleSource(e,t){let r=e.split("\n"),i=[],s=Math.max(t-6,0),n=Math.min(t+6,r.length);for(let e=s;e<n;e++){let s=e+1;i.push("".concat(s===t?">":" "," ").concat(s,": ").concat(r[e]))}return i.join("\n")}_getShaderErrors(e,t,r){let i=e.getShaderParameter(t,e.COMPILE_STATUS),s=e.getShaderInfoLog(t).trim();if(i&&""===s)return"";let n=/ERROR: 0:(\d+)/.exec(s);if(!n)return s;{let i=parseInt(n[1]);return r.toUpperCase()+"\n\n"+s+"\n\n"+this._handleSource(e.getShaderSource(t),i)}}_logProgramError(e,t,r){if(this.renderer.debug.checkShaderErrors){let i=this.gl,s=i.getProgramInfoLog(e).trim();if(!1===i.getProgramParameter(e,i.LINK_STATUS))if("function"==typeof this.renderer.debug.onShaderError)this.renderer.debug.onShaderError(i,e,r,t);else{let n=this._getShaderErrors(i,r,"vertex"),a=this._getShaderErrors(i,t,"fragment");console.error("THREE.WebGLProgram: Shader Error "+i.getError()+" - VALIDATE_STATUS "+i.getProgramParameter(e,i.VALIDATE_STATUS)+"\n\nProgram Info Log: "+s+"\n"+n+"\n"+a)}else""!==s&&console.warn("THREE.WebGLProgram: Program Info Log:",s)}}_completeCompile(e,t){let{state:r,gl:i}=this,{programGPU:s,fragmentShader:n,vertexShader:a}=this.get(t);!1===i.getProgramParameter(s,i.LINK_STATUS)&&this._logProgramError(s,n,a),r.useProgram(s);let o=e.getBindings();this._setupBindings(o,s),this.set(t,{programGPU:s})}createComputePipeline(e,t){let{state:r,gl:i}=this,s={stage:"fragment",code:"#version 300 es\nprecision highp float;\nvoid main() {}"};this.createProgram(s);let{computeProgram:n}=e,a=i.createProgram(),o=this.get(s).shaderGPU,l=this.get(n).shaderGPU,u=n.transforms,d=[],h=[];for(let e=0;e<u.length;e++){let t=u[e];d.push(t.varyingName),h.push(t.attributeNode)}i.attachShader(a,o),i.attachShader(a,l),i.transformFeedbackVaryings(a,d,i.SEPARATE_ATTRIBS),i.linkProgram(a),!1===i.getProgramParameter(a,i.LINK_STATUS)&&this._logProgramError(a,o,l),r.useProgram(a),this._setupBindings(t,a);let c=n.attributes,p=[],g=[];for(let e=0;e<c.length;e++){let t=c[e].node.attribute;p.push(t),this.has(t)||this.attributeUtils.createAttribute(t,i.ARRAY_BUFFER)}for(let e=0;e<h.length;e++){let t=h[e].attribute;this.has(t)||this.attributeUtils.createAttribute(t,i.ARRAY_BUFFER);let r=this.get(t);g.push(r)}this.set(e,{programGPU:a,transformBuffers:g,attributes:p})}createBindings(e,t){if(!1===this._knownBindings.has(t)){this._knownBindings.add(t);let e=0,r=0;for(let i of t)for(let t of(this.set(i,{textures:r,uniformBuffers:e}),i.bindings))t.isUniformBuffer&&e++,t.isSampledTexture&&r++}this.updateBindings(e,t)}updateBindings(e){let{gl:t}=this,r=this.get(e),i=r.uniformBuffers,s=r.textures;for(let r of e.bindings)if(r.isUniformsGroup||r.isUniformBuffer){let e=r.buffer,s=t.createBuffer();t.bindBuffer(t.UNIFORM_BUFFER,s),t.bufferData(t.UNIFORM_BUFFER,e,t.DYNAMIC_DRAW),this.set(r,{index:i++,bufferGPU:s})}else if(r.isSampledTexture){let{textureGPU:e,glTextureType:t}=this.get(r.texture);this.set(r,{index:s++,textureGPU:e,glTextureType:t})}}updateBinding(e){let t=this.gl;if(e.isUniformsGroup||e.isUniformBuffer){let r=this.get(e).bufferGPU,i=e.buffer;t.bindBuffer(t.UNIFORM_BUFFER,r),t.bufferData(t.UNIFORM_BUFFER,i,t.DYNAMIC_DRAW)}}createIndexAttribute(e){let t=this.gl;this.attributeUtils.createAttribute(e,t.ELEMENT_ARRAY_BUFFER)}createAttribute(e){if(this.has(e))return;let t=this.gl;this.attributeUtils.createAttribute(e,t.ARRAY_BUFFER)}createStorageAttribute(e){if(this.has(e))return;let t=this.gl;this.attributeUtils.createAttribute(e,t.ARRAY_BUFFER)}updateAttribute(e){this.attributeUtils.updateAttribute(e)}destroyAttribute(e){this.attributeUtils.destroyAttribute(e)}hasFeature(e){let t=Object.keys(pL).filter(t=>pL[t]===e),r=this.extensions;for(let e=0;e<t.length;e++)if(r.has(t[e]))return!0;return!1}getMaxAnisotropy(){return this.capabilities.getMaxAnisotropy()}copyTextureToTexture(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,n=arguments.length>5&&void 0!==arguments[5]?arguments[5]:0;this.textureUtils.copyTextureToTexture(e,t,r,i,s,n)}copyFramebufferToTexture(e,t,r){this.textureUtils.copyFramebufferToTexture(e,t,r)}_setFramebuffer(e){let{gl:t,state:r}=this,i=null;if(null!==e.textures){let s,n=e.renderTarget,a=this.get(n),{samples:o,depthBuffer:l,stencilBuffer:u}=n,d=!0===n.isWebGLCubeRenderTarget,h=!0===n.isRenderTarget3D,c=!0===n.isRenderTargetArray,p=!0===n.isXRRenderTarget,g=!0===p&&!0===n.hasExternalTextures,m=a.msaaFrameBuffer,f=a.depthRenderbuffer,y=this.extensions.get("WEBGL_multisampled_render_to_texture"),x=this.extensions.get("OVR_multiview2"),b=this._useMultisampledExtension(n),T=l6(e);if(d?(a.cubeFramebuffers||(a.cubeFramebuffers={}),s=a.cubeFramebuffers[T]):p&&!1===g?s=this._xrFramebuffer:(a.framebuffers||(a.framebuffers={}),s=a.framebuffers[T]),void 0===s){s=t.createFramebuffer(),r.bindFramebuffer(t.FRAMEBUFFER,s);let i=e.textures;if(d){a.cubeFramebuffers[T]=s;let{textureGPU:e}=this.get(i[0]),r=this.renderer._activeCubeFace;t.framebufferTexture2D(t.FRAMEBUFFER,t.COLOR_ATTACHMENT0,t.TEXTURE_CUBE_MAP_POSITIVE_X+r,e,0)}else{a.framebuffers[T]=s;for(let r=0;r<i.length;r++){let s=i[r],a=this.get(s);a.renderTarget=e.renderTarget,a.cacheKey=T;let l=t.COLOR_ATTACHMENT0+r;if(h||c){let e=this.renderer._activeCubeFace;t.framebufferTextureLayer(t.FRAMEBUFFER,l,a.textureGPU,0,e)}else n.multiview?x.framebufferTextureMultisampleMultiviewOVR(t.FRAMEBUFFER,l,a.textureGPU,0,o,0,2):g&&b?y.framebufferTexture2DMultisampleEXT(t.FRAMEBUFFER,l,t.TEXTURE_2D,a.textureGPU,0,o):t.framebufferTexture2D(t.FRAMEBUFFER,l,t.TEXTURE_2D,a.textureGPU,0)}r.drawBuffers(e,s)}if(n.isXRRenderTarget&&!0===n.autoAllocateDepthBuffer){let r=t.createRenderbuffer();this.textureUtils.setupRenderBufferStorage(r,e,0,b),a.xrDepthRenderbuffer=r}else if(null!==e.depthTexture){let r=this.get(e.depthTexture),i=u?t.DEPTH_STENCIL_ATTACHMENT:t.DEPTH_ATTACHMENT;if(r.renderTarget=e.renderTarget,r.cacheKey=T,n.multiview)x.framebufferTextureMultisampleMultiviewOVR(t.FRAMEBUFFER,i,r.textureGPU,0,o,0,2);else if(g&&b)y.framebufferTexture2DMultisampleEXT(t.FRAMEBUFFER,i,t.TEXTURE_2D,r.textureGPU,0,o);else if(e.depthTexture.isDepthArrayTexture){let e=this.renderer._activeCubeFace;t.framebufferTextureLayer(t.FRAMEBUFFER,i,r.textureGPU,0,e)}else t.framebufferTexture2D(t.FRAMEBUFFER,i,t.TEXTURE_2D,r.textureGPU,0)}}else{if(this._isRenderCameraDepthArray(e)){r.bindFramebuffer(t.FRAMEBUFFER,s);let i=this.renderer._activeCubeFace,n=this.get(e.depthTexture),a=u?t.DEPTH_STENCIL_ATTACHMENT:t.DEPTH_ATTACHMENT;t.framebufferTextureLayer(t.FRAMEBUFFER,a,n.textureGPU,0,i)}if(p&&g||n.multiview){r.bindFramebuffer(t.FRAMEBUFFER,s);let i=this.get(e.textures[0]);n.multiview?x.framebufferTextureMultisampleMultiviewOVR(t.FRAMEBUFFER,t.COLOR_ATTACHMENT0,i.textureGPU,0,o,0,2):b?y.framebufferTexture2DMultisampleEXT(t.FRAMEBUFFER,t.COLOR_ATTACHMENT0,t.TEXTURE_2D,i.textureGPU,0,o):t.framebufferTexture2D(t.FRAMEBUFFER,t.COLOR_ATTACHMENT0,t.TEXTURE_2D,i.textureGPU,0);let l=u?t.DEPTH_STENCIL_ATTACHMENT:t.DEPTH_ATTACHMENT;if(!0===n.autoAllocateDepthBuffer){let e=a.xrDepthRenderbuffer;t.bindRenderbuffer(t.RENDERBUFFER,e),t.framebufferRenderbuffer(t.FRAMEBUFFER,l,t.RENDERBUFFER,e)}else{let r=this.get(e.depthTexture);n.multiview?x.framebufferTextureMultisampleMultiviewOVR(t.FRAMEBUFFER,l,r.textureGPU,0,o,0,2):b?y.framebufferTexture2DMultisampleEXT(t.FRAMEBUFFER,l,t.TEXTURE_2D,r.textureGPU,0,o):t.framebufferTexture2D(t.FRAMEBUFFER,l,t.TEXTURE_2D,r.textureGPU,0)}}}if(o>0&&!1===b&&!n.multiview){if(void 0===m){let i=[];m=t.createFramebuffer(),r.bindFramebuffer(t.FRAMEBUFFER,m);let s=[],n=e.textures;for(let r=0;r<n.length;r++){if(s[r]=t.createRenderbuffer(),t.bindRenderbuffer(t.RENDERBUFFER,s[r]),i.push(t.COLOR_ATTACHMENT0+r),l){let e=u?t.DEPTH_STENCIL_ATTACHMENT:t.DEPTH_ATTACHMENT;i.push(e)}let n=e.textures[r],a=this.get(n);t.renderbufferStorageMultisample(t.RENDERBUFFER,o,a.glInternalFormat,e.width,e.height),t.framebufferRenderbuffer(t.FRAMEBUFFER,t.COLOR_ATTACHMENT0+r,t.RENDERBUFFER,s[r])}if(a.msaaFrameBuffer=m,a.msaaRenderbuffers=s,void 0===f){f=t.createRenderbuffer(),this.textureUtils.setupRenderBufferStorage(f,e,o),a.depthRenderbuffer=f;let r=u?t.DEPTH_STENCIL_ATTACHMENT:t.DEPTH_ATTACHMENT;i.push(r)}a.invalidationArray=i}i=a.msaaFrameBuffer}else i=s}r.bindFramebuffer(t.FRAMEBUFFER,i)}_getVaoKey(e){let t="";for(let r=0;r<e.length;r++)t+=":"+this.get(e[r]).id;return t}_createVao(e){let{gl:t}=this,r=t.createVertexArray(),i="",s=!0;t.bindVertexArray(r);for(let r=0;r<e.length;r++){let n,a,o=e[r],l=this.get(o);i+=":"+l.id,t.bindBuffer(t.ARRAY_BUFFER,l.bufferGPU),t.enableVertexAttribArray(r),(o.isStorageBufferAttribute||o.isStorageInstancedBufferAttribute)&&(s=!1),!0===o.isInterleavedBufferAttribute?(n=o.data.stride*l.bytesPerElement,a=o.offset*l.bytesPerElement):(n=0,a=0),l.isInteger?t.vertexAttribIPointer(r,o.itemSize,l.type,n,a):t.vertexAttribPointer(r,o.itemSize,l.type,o.normalized,n,a),o.isInstancedBufferAttribute&&!o.isInterleavedBufferAttribute?t.vertexAttribDivisor(r,o.meshPerAttribute):o.isInterleavedBufferAttribute&&o.data.isInstancedInterleavedBuffer&&t.vertexAttribDivisor(r,o.data.meshPerAttribute)}return t.bindBuffer(t.ARRAY_BUFFER,null),this.vaoCache[i]=r,{vaoGPU:r,staticVao:s}}_getTransformFeedback(e){let t="";for(let r=0;r<e.length;r++)t+=":"+e[r].id;let r=this.transformFeedbackCache[t];if(void 0!==r)return r;let{gl:i}=this;r=i.createTransformFeedback(),i.bindTransformFeedback(i.TRANSFORM_FEEDBACK,r);for(let t=0;t<e.length;t++){let r=e[t];i.bindBufferBase(i.TRANSFORM_FEEDBACK_BUFFER,t,r.transformBuffer)}return i.bindTransformFeedback(i.TRANSFORM_FEEDBACK,null),this.transformFeedbackCache[t]=r,r}_setupBindings(e,t){let r=this.gl;for(let i of e)for(let e of i.bindings){let i=this.get(e).index;if(e.isUniformsGroup||e.isUniformBuffer){let s=r.getUniformBlockIndex(t,e.name);r.uniformBlockBinding(t,s,i)}else if(e.isSampledTexture){let s=r.getUniformLocation(t,e.name);r.uniform1i(s,i)}}}_bindUniforms(e){let{gl:t,state:r}=this;for(let i of e)for(let e of i.bindings){let i=this.get(e),s=i.index;e.isUniformsGroup||e.isUniformBuffer?r.bindBufferBase(t.UNIFORM_BUFFER,s,i.bufferGPU):e.isSampledTexture&&r.bindTexture(i.glTextureType,i.textureGPU,t.TEXTURE0+s)}}_useMultisampledExtension(e){return!0===e.multiview||e.samples>0&&!0===this.extensions.has("WEBGL_multisampled_render_to_texture")&&!1!==e.autoAllocateDepthBuffer}dispose(){let e=this.extensions.get("WEBGL_lose_context");e&&e.loseContext(),this.renderer.domElement.removeEventListener("webglcontextlost",this._onContextLost)}constructor(e={}){super(e),this.isWebGLBackend=!0,this.attributeUtils=null,this.extensions=null,this.capabilities=null,this.textureUtils=null,this.bufferRenderer=null,this.gl=null,this.state=null,this.utils=null,this.vaoCache={},this.transformFeedbackCache={},this.discard=!1,this.disjoint=null,this.parallel=null,this._currentContext=null,this._knownBindings=new WeakSet,this._supportsInvalidateFramebuffer="undefined"!=typeof navigator&&/OculusBrowser/g.test(navigator.userAgent),this._xrFramebuffer=null}}let pG={PointList:"point-list",LineList:"line-list",LineStrip:"line-strip",TriangleList:"triangle-list",TriangleStrip:"triangle-strip"},pk={Never:"never",Less:"less",Equal:"equal",LessEqual:"less-equal",Greater:"greater",NotEqual:"not-equal",GreaterEqual:"greater-equal",Always:"always"},pz={Store:"store"},pH={Load:"load",Clear:"clear"},pW={CCW:"ccw"},pq={None:"none",Front:"front",Back:"back"},pj={Uint16:"uint16",Uint32:"uint32"},pX={R8Unorm:"r8unorm",R8Snorm:"r8snorm",R8Uint:"r8uint",R8Sint:"r8sint",R16Uint:"r16uint",R16Sint:"r16sint",R16Float:"r16float",RG8Unorm:"rg8unorm",RG8Snorm:"rg8snorm",RG8Uint:"rg8uint",RG8Sint:"rg8sint",R32Uint:"r32uint",R32Sint:"r32sint",R32Float:"r32float",RG16Uint:"rg16uint",RG16Sint:"rg16sint",RG16Float:"rg16float",RGBA8Unorm:"rgba8unorm",RGBA8UnormSRGB:"rgba8unorm-srgb",RGBA8Snorm:"rgba8snorm",RGBA8Uint:"rgba8uint",RGBA8Sint:"rgba8sint",BGRA8Unorm:"bgra8unorm",BGRA8UnormSRGB:"bgra8unorm-srgb",RGB9E5UFloat:"rgb9e5ufloat",RGB10A2Unorm:"rgb10a2unorm",RG11B10UFloat:"rgb10a2unorm",RG32Uint:"rg32uint",RG32Sint:"rg32sint",RG32Float:"rg32float",RGBA16Uint:"rgba16uint",RGBA16Sint:"rgba16sint",RGBA16Float:"rgba16float",RGBA32Uint:"rgba32uint",RGBA32Sint:"rgba32sint",RGBA32Float:"rgba32float",Depth16Unorm:"depth16unorm",Depth24Plus:"depth24plus",Depth24PlusStencil8:"depth24plus-stencil8",Depth32Float:"depth32float",Depth32FloatStencil8:"depth32float-stencil8",BC1RGBAUnorm:"bc1-rgba-unorm",BC1RGBAUnormSRGB:"bc1-rgba-unorm-srgb",BC2RGBAUnorm:"bc2-rgba-unorm",BC2RGBAUnormSRGB:"bc2-rgba-unorm-srgb",BC3RGBAUnorm:"bc3-rgba-unorm",BC3RGBAUnormSRGB:"bc3-rgba-unorm-srgb",BC4RUnorm:"bc4-r-unorm",BC4RSnorm:"bc4-r-snorm",BC5RGUnorm:"bc5-rg-unorm",BC5RGSnorm:"bc5-rg-snorm",BC6HRGBUFloat:"bc6h-rgb-ufloat",BC6HRGBFloat:"bc6h-rgb-float",BC7RGBAUnorm:"bc7-rgba-unorm",BC7RGBAUnormSRGB:"bc7-rgba-srgb",ETC2RGB8Unorm:"etc2-rgb8unorm",ETC2RGB8UnormSRGB:"etc2-rgb8unorm-srgb",ETC2RGB8A1Unorm:"etc2-rgb8a1unorm",ETC2RGB8A1UnormSRGB:"etc2-rgb8a1unorm-srgb",ETC2RGBA8Unorm:"etc2-rgba8unorm",ETC2RGBA8UnormSRGB:"etc2-rgba8unorm-srgb",EACR11Unorm:"eac-r11unorm",EACR11Snorm:"eac-r11snorm",EACRG11Unorm:"eac-rg11unorm",EACRG11Snorm:"eac-rg11snorm",ASTC4x4Unorm:"astc-4x4-unorm",ASTC4x4UnormSRGB:"astc-4x4-unorm-srgb",ASTC5x4Unorm:"astc-5x4-unorm",ASTC5x4UnormSRGB:"astc-5x4-unorm-srgb",ASTC5x5Unorm:"astc-5x5-unorm",ASTC5x5UnormSRGB:"astc-5x5-unorm-srgb",ASTC6x5Unorm:"astc-6x5-unorm",ASTC6x5UnormSRGB:"astc-6x5-unorm-srgb",ASTC6x6Unorm:"astc-6x6-unorm",ASTC6x6UnormSRGB:"astc-6x6-unorm-srgb",ASTC8x5Unorm:"astc-8x5-unorm",ASTC8x5UnormSRGB:"astc-8x5-unorm-srgb",ASTC8x6Unorm:"astc-8x6-unorm",ASTC8x6UnormSRGB:"astc-8x6-unorm-srgb",ASTC8x8Unorm:"astc-8x8-unorm",ASTC8x8UnormSRGB:"astc-8x8-unorm-srgb",ASTC10x5Unorm:"astc-10x5-unorm",ASTC10x5UnormSRGB:"astc-10x5-unorm-srgb",ASTC10x6Unorm:"astc-10x6-unorm",ASTC10x6UnormSRGB:"astc-10x6-unorm-srgb",ASTC10x8Unorm:"astc-10x8-unorm",ASTC10x8UnormSRGB:"astc-10x8-unorm-srgb",ASTC10x10Unorm:"astc-10x10-unorm",ASTC10x10UnormSRGB:"astc-10x10-unorm-srgb",ASTC12x10Unorm:"astc-12x10-unorm",ASTC12x10UnormSRGB:"astc-12x10-unorm-srgb",ASTC12x12Unorm:"astc-12x12-unorm",ASTC12x12UnormSRGB:"astc-12x12-unorm-srgb"},pQ={ClampToEdge:"clamp-to-edge",Repeat:"repeat",MirrorRepeat:"mirror-repeat"},pY={Linear:"linear",Nearest:"nearest"},pK={Zero:"zero",One:"one",Src:"src",OneMinusSrc:"one-minus-src",SrcAlpha:"src-alpha",OneMinusSrcAlpha:"one-minus-src-alpha",Dst:"dst",OneMinusDstColor:"one-minus-dst",DstAlpha:"dst-alpha",OneMinusDstAlpha:"one-minus-dst-alpha",SrcAlphaSaturated:"src-alpha-saturated",Constant:"constant",OneMinusConstant:"one-minus-constant"},pZ={Add:"add",Subtract:"subtract",ReverseSubtract:"reverse-subtract",Min:"min",Max:"max"},p$={None:0,All:15},pJ={Keep:"keep",Zero:"zero",Replace:"replace",Invert:"invert",IncrementClamp:"increment-clamp",DecrementClamp:"decrement-clamp",IncrementWrap:"increment-wrap",DecrementWrap:"decrement-wrap"},p0={Storage:"storage",ReadOnlyStorage:"read-only-storage"},p1={WriteOnly:"write-only",ReadOnly:"read-only",ReadWrite:"read-write"},p2={NonFiltering:"non-filtering",Comparison:"comparison"},p3={Float:"float",UnfilterableFloat:"unfilterable-float",Depth:"depth",SInt:"sint",UInt:"uint"},p4={TwoD:"2d",ThreeD:"3d"},p6={TwoD:"2d",TwoDArray:"2d-array",Cube:"cube",ThreeD:"3d"},p8={All:"all"},p5={Vertex:"vertex",Instance:"instance"},p9={DepthClipControl:"depth-clip-control",Depth32FloatStencil8:"depth32float-stencil8",TextureCompressionBC:"texture-compression-bc",TextureCompressionETC2:"texture-compression-etc2",TextureCompressionASTC:"texture-compression-astc",TimestampQuery:"timestamp-query",IndirectFirstInstance:"indirect-first-instance",ShaderF16:"shader-f16",RG11B10UFloat:"rg11b10ufloat-renderable",BGRA8UNormStorage:"bgra8unorm-storage",Float32Filterable:"float32-filterable",ClipDistances:"clip-distances",DualSourceBlending:"dual-source-blending",Subgroups:"subgroups"};class p7 extends pe{constructor(e,t){super(e),this.texture=t,this.version=t?t.version:0,this.isSampler=!0}}class ge extends p7{update(){this.texture=this.textureNode.value}constructor(e,t,r){super(e,t?t.value:null),this.textureNode=t,this.groupNode=r}}class gt extends pt{constructor(e,t){super(e,t?t.array:null),this.attribute=t,this.isStorageBuffer=!0}}let gr=0;class gi extends gt{get buffer(){return this.nodeUniform.value}constructor(e,t){super("StorageBuffer_"+gr++,e?e.value:null),this.nodeUniform=e,this.access=e?e.access:C.READ_WRITE,this.groupNode=t}}class gs extends lD{getTransferPipeline(e){let t=this.transferPipelines[e];return void 0===t&&(t=this.device.createRenderPipeline({label:"mipmap-".concat(e),vertex:{module:this.mipmapVertexShaderModule,entryPoint:"main"},fragment:{module:this.mipmapFragmentShaderModule,entryPoint:"main",targets:[{format:e}]},primitive:{topology:pG.TriangleStrip,stripIndexFormat:pj.Uint32},layout:"auto"}),this.transferPipelines[e]=t),t}getFlipYPipeline(e){let t=this.flipYPipelines[e];return void 0===t&&(t=this.device.createRenderPipeline({label:"flipY-".concat(e),vertex:{module:this.mipmapVertexShaderModule,entryPoint:"main"},fragment:{module:this.flipYFragmentShaderModule,entryPoint:"main",targets:[{format:e}]},primitive:{topology:pG.TriangleStrip,stripIndexFormat:pj.Uint32},layout:"auto"}),this.flipYPipelines[e]=t),t}flipY(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=t.format,{width:s,height:n}=t.size,a=this.getTransferPipeline(i),o=this.getFlipYPipeline(i),l=this.device.createTexture({size:{width:s,height:n,depthOrArrayLayers:1},format:i,usage:GPUTextureUsage.RENDER_ATTACHMENT|GPUTextureUsage.TEXTURE_BINDING}),u=e.createView({baseMipLevel:0,mipLevelCount:1,dimension:p6.TwoD,baseArrayLayer:r}),d=l.createView({baseMipLevel:0,mipLevelCount:1,dimension:p6.TwoD,baseArrayLayer:0}),h=this.device.createCommandEncoder({}),c=(e,t,r)=>{let i=e.getBindGroupLayout(0),s=this.device.createBindGroup({layout:i,entries:[{binding:0,resource:this.flipYSampler},{binding:1,resource:t}]}),n=h.beginRenderPass({colorAttachments:[{view:r,loadOp:pH.Clear,storeOp:pz.Store,clearValue:[0,0,0,0]}]});n.setPipeline(e),n.setBindGroup(0,s),n.draw(4,1,0,0),n.end()};c(a,u,d),c(o,d,u),this.device.queue.submit([h.finish()]),l.destroy()}generateMipmaps(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=this.get(e);void 0===i.useCount&&(i.useCount=0,i.layers=[]);let s=i.layers[r]||this._mipmapCreateBundles(e,t,r),n=this.device.createCommandEncoder({});this._mipmapRunBundles(n,s),this.device.queue.submit([n.finish()]),0!==i.useCount&&(i.layers[r]=s),i.useCount++}_mipmapCreateBundles(e,t,r){let i=this.getTransferPipeline(t.format),s=i.getBindGroupLayout(0),n=e.createView({baseMipLevel:0,mipLevelCount:1,dimension:p6.TwoD,baseArrayLayer:r}),a=[];for(let o=1;o<t.mipLevelCount;o++){let l=this.device.createBindGroup({layout:s,entries:[{binding:0,resource:this.mipmapSampler},{binding:1,resource:n}]}),u=e.createView({baseMipLevel:o,mipLevelCount:1,dimension:p6.TwoD,baseArrayLayer:r}),d={colorAttachments:[{view:u,loadOp:pH.Clear,storeOp:pz.Store,clearValue:[0,0,0,0]}]},h=this.device.createRenderBundleEncoder({colorFormats:[t.format]});h.setPipeline(i),h.setBindGroup(0,l),h.draw(4,1,0,0),a.push({renderBundles:[h.finish()],passDescriptor:d}),n=u}return a}_mipmapRunBundles(e,t){let r=t.length;for(let i=0;i<r;i++){let r=t[i],s=e.beginRenderPass(r.passDescriptor);s.executeBundles(r.renderBundles),s.end()}}constructor(e){super(),this.device=e,this.mipmapSampler=e.createSampler({minFilter:pY.Linear}),this.flipYSampler=e.createSampler({minFilter:pY.Nearest}),this.transferPipelines={},this.flipYPipelines={},this.mipmapVertexShaderModule=e.createShaderModule({label:"mipmapVertex",code:"\nstruct VarysStruct {\n	@builtin( position ) Position: vec4<f32>,\n	@location( 0 ) vTex : vec2<f32>\n};\n\n@vertex\nfn main( @builtin( vertex_index ) vertexIndex : u32 ) -> VarysStruct {\n\n	var Varys : VarysStruct;\n\n	var pos = array< vec2<f32>, 4 >(\n		vec2<f32>( -1.0,  1.0 ),\n		vec2<f32>(  1.0,  1.0 ),\n		vec2<f32>( -1.0, -1.0 ),\n		vec2<f32>(  1.0, -1.0 )\n	);\n\n	var tex = array< vec2<f32>, 4 >(\n		vec2<f32>( 0.0, 0.0 ),\n		vec2<f32>( 1.0, 0.0 ),\n		vec2<f32>( 0.0, 1.0 ),\n		vec2<f32>( 1.0, 1.0 )\n	);\n\n	Varys.vTex = tex[ vertexIndex ];\n	Varys.Position = vec4<f32>( pos[ vertexIndex ], 0.0, 1.0 );\n\n	return Varys;\n\n}\n"}),this.mipmapFragmentShaderModule=e.createShaderModule({label:"mipmapFragment",code:"\n@group( 0 ) @binding( 0 )\nvar imgSampler : sampler;\n\n@group( 0 ) @binding( 1 )\nvar img : texture_2d<f32>;\n\n@fragment\nfn main( @location( 0 ) vTex : vec2<f32> ) -> @location( 0 ) vec4<f32> {\n\n	return textureSample( img, imgSampler, vTex );\n\n}\n"}),this.flipYFragmentShaderModule=e.createShaderModule({label:"flipYFragment",code:"\n@group( 0 ) @binding( 0 )\nvar imgSampler : sampler;\n\n@group( 0 ) @binding( 1 )\nvar img : texture_2d<f32>;\n\n@fragment\nfn main( @location( 0 ) vTex : vec2<f32> ) -> @location( 0 ) vec4<f32> {\n\n	return textureSample( img, imgSampler, vec2( vTex.x, 1.0 - vTex.y ) );\n\n}\n"})}}let gn={[u.amv]:"never",[u.vim]:"less",[u.kO0]:"equal",[u.TiK]:"less-equal",[u.eoi]:"greater",[u.gWB]:"greater-equal",[u.FFZ]:"always",[u.jzd]:"not-equal"},ga=[0,1,3,2,4,5];class go{createSampler(e){let t=this.backend,r=t.device,i=t.get(e),s={addressModeU:this._convertAddressMode(e.wrapS),addressModeV:this._convertAddressMode(e.wrapT),addressModeW:this._convertAddressMode(e.wrapR),magFilter:this._convertFilterMode(e.magFilter),minFilter:this._convertFilterMode(e.minFilter),mipmapFilter:this._convertFilterMode(e.minFilter),maxAnisotropy:1};s.magFilter===pY.Linear&&s.minFilter===pY.Linear&&s.mipmapFilter===pY.Linear&&(s.maxAnisotropy=e.anisotropy),e.isDepthTexture&&null!==e.compareFunction&&(s.compare=gn[e.compareFunction]),i.sampler=r.createSampler(s)}createDefaultTexture(e){let t,r=gl(e);e.isCubeTexture?t=this._getDefaultCubeTextureGPU(r):e.isVideoTexture?this.backend.get(e).externalTexture=this._getDefaultVideoFrame():t=this._getDefaultTextureGPU(r),this.backend.get(e).texture=t}createTexture(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=this.backend,i=r.get(e);if(i.initialized)throw Error("WebGPUTextureUtils: Texture already initialized.");void 0===t.needsMipmaps&&(t.needsMipmaps=!1),void 0===t.levels&&(t.levels=1),void 0===t.depth&&(t.depth=1);let{width:s,height:n,depth:a,levels:o}=t;e.isFramebufferTexture&&(t.renderTarget?t.format=this.backend.utils.getCurrentColorFormat(t.renderTarget):t.format=this.backend.utils.getPreferredCanvasFormat());let l=this._getDimension(e),u=e.internalFormat||t.format||gl(e,r.device);i.format=u;let{samples:d,primarySamples:h,isMSAA:c}=r.utils.getTextureSampleData(e),p=GPUTextureUsage.TEXTURE_BINDING|GPUTextureUsage.COPY_DST|GPUTextureUsage.COPY_SRC;!0===e.isStorageTexture&&(p|=GPUTextureUsage.STORAGE_BINDING),!0!==e.isCompressedTexture&&!0!==e.isCompressedArrayTexture&&(p|=GPUTextureUsage.RENDER_ATTACHMENT);let g={label:e.name,size:{width:s,height:n,depthOrArrayLayers:a},mipLevelCount:o,sampleCount:h,dimension:l,format:u,usage:p};if(e.isVideoTexture){let t=e.source.data,r=new VideoFrame(t);g.size.width=r.displayWidth,g.size.height=r.displayHeight,r.close(),i.externalTexture=t}else{if(void 0===u){console.warn("WebGPURenderer: Texture format not supported."),this.createDefaultTexture(e);return}e.isCubeTexture&&(g.textureBindingViewDimension=p6.Cube),i.texture=r.device.createTexture(g)}if(c){let e=Object.assign({},g);e.label=e.label+"-msaa",e.sampleCount=d,i.msaaTexture=r.device.createTexture(e)}i.initialized=!0,i.textureDescriptorGPU=g}destroyTexture(e){let t=this.backend,r=t.get(e);void 0!==r.texture&&r.texture.destroy(),void 0!==r.msaaTexture&&r.msaaTexture.destroy(),t.delete(e)}destroySampler(e){let t=this.backend.get(e);delete t.sampler}generateMipmaps(e){let t=this.backend.get(e);if(e.isCubeTexture)for(let e=0;e<6;e++)this._generateMipmaps(t.texture,t.textureDescriptorGPU,e);else{let r=e.image.depth||1;for(let e=0;e<r;e++)this._generateMipmaps(t.texture,t.textureDescriptorGPU,e)}}getColorBuffer(){this.colorBuffer&&this.colorBuffer.destroy();let e=this.backend,{width:t,height:r}=e.getDrawingBufferSize();return this.colorBuffer=e.device.createTexture({label:"colorBuffer",size:{width:t,height:r,depthOrArrayLayers:1},sampleCount:e.utils.getSampleCount(e.renderer.samples),format:e.utils.getPreferredCanvasFormat(),usage:GPUTextureUsage.RENDER_ATTACHMENT|GPUTextureUsage.COPY_SRC}),this.colorBuffer}getDepthBuffer(){let e,t,r=!(arguments.length>0)||void 0===arguments[0]||arguments[0],i=arguments.length>1&&void 0!==arguments[1]&&arguments[1],s=this.backend,{width:n,height:a}=s.getDrawingBufferSize(),o=this.depthTexture,l=s.get(o).texture;if(i?(e=u.dcC,t=u.V3x):r&&(e=u.zdS,t=u.bkx),void 0!==l){if(o.image.width===n&&o.image.height===a&&o.format===e&&o.type===t)return l;this.destroyTexture(o)}return o.name="depthBuffer",o.format=e,o.type=t,o.image.width=n,o.image.height=a,this.createTexture(o,{width:n,height:a}),s.get(o).texture}updateTexture(e,t){let r=this.backend.get(e),{textureDescriptorGPU:i}=r;if(!e.isRenderTargetTexture&&void 0!==i){if(e.isDataTexture)this._copyBufferToTexture(t.image,r.texture,i,0,e.flipY);else if(e.isDataArrayTexture||e.isDepthArrayTexture||e.isData3DTexture)for(let s=0;s<t.image.depth;s++)this._copyBufferToTexture(t.image,r.texture,i,s,e.flipY,s);else e.isCompressedTexture||e.isCompressedArrayTexture?this._copyCompressedBufferToTexture(e.mipmaps,r.texture,i):e.isCubeTexture?this._copyCubeMapToTexture(t.images,r.texture,i,e.flipY):e.isVideoTexture?r.externalTexture=e.source.data:this._copyImageToTexture(t.image,r.texture,i,0,e.flipY);r.version=e.version,e.onUpdate&&e.onUpdate(e)}}async copyTextureToBuffer(e,t,r,i,s,n){let a=this.backend.device,o=this.backend.get(e),l=o.texture,u=o.textureDescriptorGPU.format,d=this._getBytesPerTexel(u),h=i*d;h=256*Math.ceil(h/256);let c=a.createBuffer({size:i*s*d,usage:GPUBufferUsage.COPY_DST|GPUBufferUsage.MAP_READ}),p=a.createCommandEncoder();p.copyTextureToBuffer({texture:l,origin:{x:t,y:r,z:n}},{buffer:c,bytesPerRow:h},{width:i,height:s});let g=this._getTypedArrayType(u);return a.queue.submit([p.finish()]),await c.mapAsync(GPUMapMode.READ),new g(c.getMappedRange())}_isEnvironmentTexture(e){let t=e.mapping;return t===u.wfO||t===u.uV5||t===u.hy7||t===u.xFO}_getDefaultTextureGPU(e){let t=this.defaultTexture[e];if(void 0===t){let r=new u.gPd;r.minFilter=u.hxR,r.magFilter=u.hxR,this.createTexture(r,{width:1,height:1,format:e}),this.defaultTexture[e]=t=r}return this.backend.get(t).texture}_getDefaultCubeTextureGPU(e){let t=this.defaultTexture[e];if(void 0===t){let r=new u.b4q;r.minFilter=u.hxR,r.magFilter=u.hxR,this.createTexture(r,{width:1,height:1,depth:6}),this.defaultCubeTexture[e]=t=r}return this.backend.get(t).texture}_getDefaultVideoFrame(){let e=this.defaultVideoFrame;return null===e&&(this.defaultVideoFrame=e=new VideoFrame(new Uint8Array([0,0,0,255]),{timestamp:0,codedWidth:1,codedHeight:1,format:"RGBA"})),e}_copyCubeMapToTexture(e,t,r,i){for(let s=0;s<6;s++){let n=e[s],a=!0===i?ga[s]:s;n.isDataTexture?this._copyBufferToTexture(n.image,t,r,a,i):this._copyImageToTexture(n,t,r,a,i)}}_copyImageToTexture(e,t,r,i,s){this.backend.device.queue.copyExternalImageToTexture({source:e,flipY:s},{texture:t,mipLevel:0,origin:{x:0,y:0,z:i}},{width:e.width,height:e.height,depthOrArrayLayers:1})}_getPassUtils(){let e=this._passUtils;return null===e&&(this._passUtils=e=new gs(this.backend.device)),e}_generateMipmaps(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;this._getPassUtils().generateMipmaps(e,t,r)}_flipY(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;this._getPassUtils().flipY(e,t,r)}_copyBufferToTexture(e,t,r,i,s){let n=arguments.length>5&&void 0!==arguments[5]?arguments[5]:0,a=this.backend.device,o=e.data,l=this._getBytesPerTexel(r.format),u=e.width*l;a.queue.writeTexture({texture:t,mipLevel:0,origin:{x:0,y:0,z:i}},o,{offset:e.width*e.height*l*n,bytesPerRow:u},{width:e.width,height:e.height,depthOrArrayLayers:1}),!0===s&&this._flipY(t,r,i)}_copyCompressedBufferToTexture(e,t,r){let i=this.backend.device,s=this._getBlockData(r.format),n=r.size.depthOrArrayLayers>1;for(let a=0;a<e.length;a++){let o=e[a],l=o.width,u=o.height,d=n?r.size.depthOrArrayLayers:1,h=Math.ceil(l/s.width)*s.byteLength,c=h*Math.ceil(u/s.height);for(let e=0;e<d;e++)i.queue.writeTexture({texture:t,mipLevel:a,origin:{x:0,y:0,z:e}},o.data,{offset:e*c,bytesPerRow:h,rowsPerImage:Math.ceil(u/s.height)},{width:Math.ceil(l/s.width)*s.width,height:Math.ceil(u/s.height)*s.height,depthOrArrayLayers:1})}}_getBlockData(e){return e===pX.BC1RGBAUnorm||e===pX.BC1RGBAUnormSRGB?{byteLength:8,width:4,height:4}:e===pX.BC2RGBAUnorm||e===pX.BC2RGBAUnormSRGB||e===pX.BC3RGBAUnorm||e===pX.BC3RGBAUnormSRGB?{byteLength:16,width:4,height:4}:e===pX.BC4RUnorm||e===pX.BC4RSnorm?{byteLength:8,width:4,height:4}:e===pX.BC5RGUnorm||e===pX.BC5RGSnorm||e===pX.BC6HRGBUFloat||e===pX.BC6HRGBFloat||e===pX.BC7RGBAUnorm||e===pX.BC7RGBAUnormSRGB?{byteLength:16,width:4,height:4}:e===pX.ETC2RGB8Unorm||e===pX.ETC2RGB8UnormSRGB||e===pX.ETC2RGB8A1Unorm||e===pX.ETC2RGB8A1UnormSRGB?{byteLength:8,width:4,height:4}:e===pX.ETC2RGBA8Unorm||e===pX.ETC2RGBA8UnormSRGB?{byteLength:16,width:4,height:4}:e===pX.EACR11Unorm||e===pX.EACR11Snorm?{byteLength:8,width:4,height:4}:e===pX.EACRG11Unorm||e===pX.EACRG11Snorm||e===pX.ASTC4x4Unorm||e===pX.ASTC4x4UnormSRGB?{byteLength:16,width:4,height:4}:e===pX.ASTC5x4Unorm||e===pX.ASTC5x4UnormSRGB?{byteLength:16,width:5,height:4}:e===pX.ASTC5x5Unorm||e===pX.ASTC5x5UnormSRGB?{byteLength:16,width:5,height:5}:e===pX.ASTC6x5Unorm||e===pX.ASTC6x5UnormSRGB?{byteLength:16,width:6,height:5}:e===pX.ASTC6x6Unorm||e===pX.ASTC6x6UnormSRGB?{byteLength:16,width:6,height:6}:e===pX.ASTC8x5Unorm||e===pX.ASTC8x5UnormSRGB?{byteLength:16,width:8,height:5}:e===pX.ASTC8x6Unorm||e===pX.ASTC8x6UnormSRGB?{byteLength:16,width:8,height:6}:e===pX.ASTC8x8Unorm||e===pX.ASTC8x8UnormSRGB?{byteLength:16,width:8,height:8}:e===pX.ASTC10x5Unorm||e===pX.ASTC10x5UnormSRGB?{byteLength:16,width:10,height:5}:e===pX.ASTC10x6Unorm||e===pX.ASTC10x6UnormSRGB?{byteLength:16,width:10,height:6}:e===pX.ASTC10x8Unorm||e===pX.ASTC10x8UnormSRGB?{byteLength:16,width:10,height:8}:e===pX.ASTC10x10Unorm||e===pX.ASTC10x10UnormSRGB?{byteLength:16,width:10,height:10}:e===pX.ASTC12x10Unorm||e===pX.ASTC12x10UnormSRGB?{byteLength:16,width:12,height:10}:e===pX.ASTC12x12Unorm||e===pX.ASTC12x12UnormSRGB?{byteLength:16,width:12,height:12}:void 0}_convertAddressMode(e){let t=pQ.ClampToEdge;return e===u.GJx?t=pQ.Repeat:e===u.kTW&&(t=pQ.MirrorRepeat),t}_convertFilterMode(e){let t=pY.Linear;return(e===u.hxR||e===u.pHI||e===u.Cfg)&&(t=pY.Nearest),t}_getBytesPerTexel(e){return e===pX.R8Unorm||e===pX.R8Snorm||e===pX.R8Uint||e===pX.R8Sint?1:e===pX.R16Uint||e===pX.R16Sint||e===pX.R16Float||e===pX.RG8Unorm||e===pX.RG8Snorm||e===pX.RG8Uint||e===pX.RG8Sint?2:e===pX.R32Uint||e===pX.R32Sint||e===pX.R32Float||e===pX.RG16Uint||e===pX.RG16Sint||e===pX.RG16Float||e===pX.RGBA8Unorm||e===pX.RGBA8UnormSRGB||e===pX.RGBA8Snorm||e===pX.RGBA8Uint||e===pX.RGBA8Sint||e===pX.BGRA8Unorm||e===pX.BGRA8UnormSRGB||e===pX.RGB9E5UFloat||e===pX.RGB10A2Unorm||e===pX.RG11B10UFloat||e===pX.Depth32Float||e===pX.Depth24Plus||e===pX.Depth24PlusStencil8||e===pX.Depth32FloatStencil8?4:e===pX.RG32Uint||e===pX.RG32Sint||e===pX.RG32Float||e===pX.RGBA16Uint||e===pX.RGBA16Sint||e===pX.RGBA16Float?8:e===pX.RGBA32Uint||e===pX.RGBA32Sint||e===pX.RGBA32Float?16:void 0}_getTypedArrayType(e){return e===pX.R8Uint?Uint8Array:e===pX.R8Sint?Int8Array:e===pX.R8Unorm?Uint8Array:e===pX.R8Snorm?Int8Array:e===pX.RG8Uint?Uint8Array:e===pX.RG8Sint?Int8Array:e===pX.RG8Unorm?Uint8Array:e===pX.RG8Snorm?Int8Array:e===pX.RGBA8Uint?Uint8Array:e===pX.RGBA8Sint?Int8Array:e===pX.RGBA8Unorm?Uint8Array:e===pX.RGBA8Snorm?Int8Array:e===pX.R16Uint?Uint16Array:e===pX.R16Sint?Int16Array:e===pX.RG16Uint?Uint16Array:e===pX.RG16Sint?Int16Array:e===pX.RGBA16Uint?Uint16Array:e===pX.RGBA16Sint?Int16Array:e===pX.R16Float||e===pX.RG16Float||e===pX.RGBA16Float?Uint16Array:e===pX.R32Uint?Uint32Array:e===pX.R32Sint?Int32Array:e===pX.R32Float?Float32Array:e===pX.RG32Uint?Uint32Array:e===pX.RG32Sint?Int32Array:e===pX.RG32Float?Float32Array:e===pX.RGBA32Uint?Uint32Array:e===pX.RGBA32Sint?Int32Array:e===pX.RGBA32Float?Float32Array:e===pX.BGRA8Unorm||e===pX.BGRA8UnormSRGB?Uint8Array:e===pX.RGB10A2Unorm||e===pX.RGB9E5UFloat||e===pX.RG11B10UFloat?Uint32Array:e===pX.Depth32Float?Float32Array:e===pX.Depth24Plus||e===pX.Depth24PlusStencil8?Uint32Array:e===pX.Depth32FloatStencil8?Float32Array:void 0}_getDimension(e){let t;return e.isData3DTexture?p4.ThreeD:p4.TwoD}constructor(e){this.backend=e,this._passUtils=null,this.defaultTexture={},this.defaultCubeTexture={},this.defaultVideoFrame=null,this.colorBuffer=null,this.depthTexture=new u.VCu,this.depthTexture.name="depthBuffer"}}function gl(e){let t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,i=e.format,s=e.type,n=e.colorSpace;if(!0===e.isCompressedTexture||!0===e.isCompressedArrayTexture)switch(i){case u.Nz6:t=n===u.er$?pX.BC1RGBAUnormSRGB:pX.BC1RGBAUnorm;break;case u.jR7:t=n===u.er$?pX.BC2RGBAUnormSRGB:pX.BC2RGBAUnorm;break;case u.BXX:t=n===u.er$?pX.BC3RGBAUnormSRGB:pX.BC3RGBAUnorm;break;case u.Riy:t=n===u.er$?pX.ETC2RGB8UnormSRGB:pX.ETC2RGB8Unorm;break;case u.KDk:t=n===u.er$?pX.ETC2RGBA8UnormSRGB:pX.ETC2RGBA8Unorm;break;case u.qa3:t=n===u.er$?pX.ASTC4x4UnormSRGB:pX.ASTC4x4Unorm;break;case u.B_h:t=n===u.er$?pX.ASTC5x4UnormSRGB:pX.ASTC5x4Unorm;break;case u.czI:t=n===u.er$?pX.ASTC5x5UnormSRGB:pX.ASTC5x5Unorm;break;case u.rSH:t=n===u.er$?pX.ASTC6x5UnormSRGB:pX.ASTC6x5Unorm;break;case u.Qrf:t=n===u.er$?pX.ASTC6x6UnormSRGB:pX.ASTC6x6Unorm;break;case u.psI:t=n===u.er$?pX.ASTC8x5UnormSRGB:pX.ASTC8x5Unorm;break;case u.a5J:t=n===u.er$?pX.ASTC8x6UnormSRGB:pX.ASTC8x6Unorm;break;case u._QJ:t=n===u.er$?pX.ASTC8x8UnormSRGB:pX.ASTC8x8Unorm;break;case u.uB5:t=n===u.er$?pX.ASTC10x5UnormSRGB:pX.ASTC10x5Unorm;break;case u.lyL:t=n===u.er$?pX.ASTC10x6UnormSRGB:pX.ASTC10x6Unorm;break;case u.bC7:t=n===u.er$?pX.ASTC10x8UnormSRGB:pX.ASTC10x8Unorm;break;case u.y3Z:t=n===u.er$?pX.ASTC10x10UnormSRGB:pX.ASTC10x10Unorm;break;case u.ojs:t=n===u.er$?pX.ASTC12x10UnormSRGB:pX.ASTC12x10Unorm;break;case u.S$4:t=n===u.er$?pX.ASTC12x12UnormSRGB:pX.ASTC12x12Unorm;break;case u.GWd:t=n===u.er$?pX.RGBA8UnormSRGB:pX.RGBA8Unorm;break;default:console.error("WebGPURenderer: Unsupported texture format.",i)}else switch(i){case u.GWd:switch(s){case u.tJf:t=pX.RGBA8Snorm;break;case u.fBL:t=pX.RGBA16Sint;break;case u.cHt:t=pX.RGBA16Uint;break;case u.bkx:t=pX.RGBA32Uint;break;case u.Yuy:t=pX.RGBA32Sint;break;case u.OUM:t=n===u.er$?pX.RGBA8UnormSRGB:pX.RGBA8Unorm;break;case u.ix0:t=pX.RGBA16Float;break;case u.RQf:t=pX.RGBA32Float;break;default:console.error("WebGPURenderer: Unsupported texture type with RGBAFormat.",s)}break;case u.HIg:s===u.Dmk?t=pX.RGB9E5UFloat:console.error("WebGPURenderer: Unsupported texture type with RGBFormat.",s);break;case u.VT0:switch(s){case u.tJf:t=pX.R8Snorm;break;case u.fBL:t=pX.R16Sint;break;case u.cHt:t=pX.R16Uint;break;case u.bkx:t=pX.R32Uint;break;case u.Yuy:t=pX.R32Sint;break;case u.OUM:t=pX.R8Unorm;break;case u.ix0:t=pX.R16Float;break;case u.RQf:t=pX.R32Float;break;default:console.error("WebGPURenderer: Unsupported texture type with RedFormat.",s)}break;case u.paN:switch(s){case u.tJf:t=pX.RG8Snorm;break;case u.fBL:t=pX.RG16Sint;break;case u.cHt:t=pX.RG16Uint;break;case u.bkx:t=pX.RG32Uint;break;case u.Yuy:t=pX.RG32Sint;break;case u.OUM:t=pX.RG8Unorm;break;case u.ix0:t=pX.RG16Float;break;case u.RQf:t=pX.RG32Float;break;default:console.error("WebGPURenderer: Unsupported texture type with RGFormat.",s)}break;case u.zdS:switch(s){case u.cHt:t=pX.Depth16Unorm;break;case u.bkx:t=pX.Depth24Plus;break;case u.RQf:t=pX.Depth32Float;break;default:console.error("WebGPURenderer: Unsupported texture type with DepthFormat.",s)}break;case u.dcC:switch(s){case u.V3x:t=pX.Depth24PlusStencil8;break;case u.RQf:r&&!1===r.features.has(p9.Depth32FloatStencil8)&&console.error('WebGPURenderer: Depth textures with DepthStencilFormat + FloatType can only be used with the "depth32float-stencil8" GPU feature.'),t=pX.Depth32FloatStencil8;break;default:console.error("WebGPURenderer: Unsupported texture type with DepthStencilFormat.",s)}break;case u.ZQM:switch(s){case u.Yuy:t=pX.R32Sint;break;case u.bkx:t=pX.R32Uint;break;default:console.error("WebGPURenderer: Unsupported texture type with RedIntegerFormat.",s)}break;case u.TkQ:switch(s){case u.Yuy:t=pX.RG32Sint;break;case u.bkx:t=pX.RG32Uint;break;default:console.error("WebGPURenderer: Unsupported texture type with RGIntegerFormat.",s)}break;case u.c90:switch(s){case u.Yuy:t=pX.RGBA32Sint;break;case u.bkx:t=pX.RGBA32Uint;break;default:console.error("WebGPURenderer: Unsupported texture type with RGBAIntegerFormat.",s)}break;default:console.error("WebGPURenderer: Unsupported texture format.",i)}return t}let gu=/^[fn]*\s*([a-z_0-9]+)?\s*\(([\s\S]*?)\)\s*[\-\>]*\s*([a-z_0-9]+(?:<[\s\S]+?>)?)/i,gd=/([a-z_0-9]+)\s*:\s*([a-z_0-9]+(?:<[\s\S]+?>)?)/ig,gh={f32:"float",i32:"int",u32:"uint",bool:"bool","vec2<f32>":"vec2","vec2<i32>":"ivec2","vec2<u32>":"uvec2","vec2<bool>":"bvec2",vec2f:"vec2",vec2i:"ivec2",vec2u:"uvec2",vec2b:"bvec2","vec3<f32>":"vec3","vec3<i32>":"ivec3","vec3<u32>":"uvec3","vec3<bool>":"bvec3",vec3f:"vec3",vec3i:"ivec3",vec3u:"uvec3",vec3b:"bvec3","vec4<f32>":"vec4","vec4<i32>":"ivec4","vec4<u32>":"uvec4","vec4<bool>":"bvec4",vec4f:"vec4",vec4i:"ivec4",vec4u:"uvec4",vec4b:"bvec4","mat2x2<f32>":"mat2",mat2x2f:"mat2","mat3x3<f32>":"mat3",mat3x3f:"mat3","mat4x4<f32>":"mat4",mat4x4f:"mat4",sampler:"sampler",texture_1d:"texture",texture_2d:"texture",texture_2d_array:"texture",texture_multisampled_2d:"cubeTexture",texture_depth_2d:"depthTexture",texture_depth_2d_array:"depthTexture",texture_depth_multisampled_2d:"depthTexture",texture_depth_cube:"depthTexture",texture_depth_cube_array:"depthTexture",texture_3d:"texture3D",texture_cube:"cubeTexture",texture_cube_array:"cubeTexture",texture_storage_1d:"storageTexture",texture_storage_2d:"storageTexture",texture_storage_2d_array:"storageTexture",texture_storage_3d:"storageTexture"},gc=e=>{let t=(e=e.trim()).match(gu);if(null!==t&&4===t.length){let r=t[2],i=[],s=null;for(;null!==(s=gd.exec(r));)i.push({name:s[1],type:s[2]});let n=[];for(let e=0;e<i.length;e++){let{name:t,type:r}=i[e],s=r;s.startsWith("ptr")?s="pointer":(s.startsWith("texture")&&(s=r.split("<")[0]),s=gh[s]),n.push(new cg(s,t))}let a=e.substring(t[0].length),o=t[3]||"void",l=void 0!==t[1]?t[1]:"";return{type:gh[o]||o,inputs:n,name:l,inputsCode:r,blockCode:a,outputType:o}}throw Error("FunctionNode: Function is not a WGSL code.")};class gp extends cA{getCode(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.name,t="void"!==this.outputType?"-> "+this.outputType:"";return"fn ".concat(e," ( ").concat(this.inputsCode.trim()," ) ").concat(t)+this.blockCode}constructor(e){let{type:t,inputs:r,name:i,inputsCode:s,blockCode:n,outputType:a}=gc(e);super(t,r,i),this.inputsCode=s,this.blockCode=n,this.outputType=a}}class gg extends cR{parseFunction(e){return new gp(e)}}let gm="undefined"!=typeof self?self.GPUShaderStage:{VERTEX:1,FRAGMENT:2,COMPUTE:4},gf={[C.READ_ONLY]:"read",[C.WRITE_ONLY]:"write",[C.READ_WRITE]:"read_write"},gy={[u.GJx]:"repeat",[u.ghU]:"clamp",[u.kTW]:"mirror"},gx={vertex:gm?gm.VERTEX:1,fragment:gm?gm.FRAGMENT:2,compute:gm?gm.COMPUTE:4},gb={instance:!0,swizzleAssign:!1,storageBuffer:!0},gT={"^^":"tsl_xor"},gv={float:"f32",int:"i32",uint:"u32",bool:"bool",color:"vec3<f32>",vec2:"vec2<f32>",ivec2:"vec2<i32>",uvec2:"vec2<u32>",bvec2:"vec2<bool>",vec3:"vec3<f32>",ivec3:"vec3<i32>",uvec3:"vec3<u32>",bvec3:"vec3<bool>",vec4:"vec4<f32>",ivec4:"vec4<i32>",uvec4:"vec4<u32>",bvec4:"vec4<bool>",mat2:"mat2x2<f32>",mat3:"mat3x3<f32>",mat4:"mat4x4<f32>"},g_={},gN={tsl_xor:new da("fn tsl_xor( a : bool, b : bool ) -> bool { return ( a || b ) && !( a && b ); }"),mod_float:new da("fn tsl_mod_float( x : f32, y : f32 ) -> f32 { return x - y * floor( x / y ); }"),mod_vec2:new da("fn tsl_mod_vec2( x : vec2f, y : vec2f ) -> vec2f { return x - y * floor( x / y ); }"),mod_vec3:new da("fn tsl_mod_vec3( x : vec3f, y : vec3f ) -> vec3f { return x - y * floor( x / y ); }"),mod_vec4:new da("fn tsl_mod_vec4( x : vec4f, y : vec4f ) -> vec4f { return x - y * floor( x / y ); }"),equals_bool:new da("fn tsl_equals_bool( a : bool, b : bool ) -> bool { return a == b; }"),equals_bvec2:new da("fn tsl_equals_bvec2( a : vec2f, b : vec2f ) -> vec2<bool> { return vec2<bool>( a.x == b.x, a.y == b.y ); }"),equals_bvec3:new da("fn tsl_equals_bvec3( a : vec3f, b : vec3f ) -> vec3<bool> { return vec3<bool>( a.x == b.x, a.y == b.y, a.z == b.z ); }"),equals_bvec4:new da("fn tsl_equals_bvec4( a : vec4f, b : vec4f ) -> vec4<bool> { return vec4<bool>( a.x == b.x, a.y == b.y, a.z == b.z, a.w == b.w ); }"),repeatWrapping_float:new da("fn tsl_repeatWrapping_float( coord: f32 ) -> f32 { return fract( coord ); }"),mirrorWrapping_float:new da("fn tsl_mirrorWrapping_float( coord: f32 ) -> f32 { let mirrored = fract( coord * 0.5 ) * 2.0; return 1.0 - abs( 1.0 - mirrored ); }"),clampWrapping_float:new da("fn tsl_clampWrapping_float( coord: f32 ) -> f32 { return clamp( coord, 0.0, 1.0 ); }"),biquadraticTexture:new da("\nfn tsl_biquadraticTexture( map : texture_2d<f32>, coord : vec2f, iRes : vec2u, level : u32 ) -> vec4f {\n\n	let res = vec2f( iRes );\n\n	let uvScaled = coord * res;\n	let uvWrapping = ( ( uvScaled % res ) + res ) % res;\n\n	// https://www.shadertoy.com/view/WtyXRy\n\n	let uv = uvWrapping - 0.5;\n	let iuv = floor( uv );\n	let f = fract( uv );\n\n	let rg1 = textureLoad( map, vec2u( iuv + vec2( 0.5, 0.5 ) ) % iRes, level );\n	let rg2 = textureLoad( map, vec2u( iuv + vec2( 1.5, 0.5 ) ) % iRes, level );\n	let rg3 = textureLoad( map, vec2u( iuv + vec2( 0.5, 1.5 ) ) % iRes, level );\n	let rg4 = textureLoad( map, vec2u( iuv + vec2( 1.5, 1.5 ) ) % iRes, level );\n\n	return mix( mix( rg1, rg2, f.x ), mix( rg3, rg4, f.x ), f.y );\n\n}\n")},gS={dFdx:"dpdx",dFdy:"- dpdy",mod_float:"tsl_mod_float",mod_vec2:"tsl_mod_vec2",mod_vec3:"tsl_mod_vec3",mod_vec4:"tsl_mod_vec4",equals_bool:"tsl_equals_bool",equals_bvec2:"tsl_equals_bvec2",equals_bvec3:"tsl_equals_bvec3",equals_bvec4:"tsl_equals_bvec4",inversesqrt:"inverseSqrt",bitcast:"bitcast<f32>"};"undefined"!=typeof navigator&&/Windows/g.test(navigator.userAgent)&&(gN.pow_float=new da("fn tsl_pow_float( a : f32, b : f32 ) -> f32 { return select( -pow( -a, b ), pow( a, b ), a > 0.0 ); }"),gN.pow_vec2=new da("fn tsl_pow_vec2( a : vec2f, b : vec2f ) -> vec2f { return vec2f( tsl_pow_float( a.x, b.x ), tsl_pow_float( a.y, b.y ) ); }",[gN.pow_float]),gN.pow_vec3=new da("fn tsl_pow_vec3( a : vec3f, b : vec3f ) -> vec3f { return vec3f( tsl_pow_float( a.x, b.x ), tsl_pow_float( a.y, b.y ), tsl_pow_float( a.z, b.z ) ); }",[gN.pow_float]),gN.pow_vec4=new da("fn tsl_pow_vec4( a : vec4f, b : vec4f ) -> vec4f { return vec4f( tsl_pow_float( a.x, b.x ), tsl_pow_float( a.y, b.y ), tsl_pow_float( a.z, b.z ), tsl_pow_float( a.w, b.w ) ); }",[gN.pow_float]),gS.pow_float="tsl_pow_float",gS.pow_vec2="tsl_pow_vec2",gS.pow_vec3="tsl_pow_vec3",gS.pow_vec4="tsl_pow_vec4");let gR="";!0!==("undefined"!=typeof navigator&&/Firefox|Deno/g.test(navigator.userAgent))&&(gR+="diagnostic( off, derivative_uniformity );\n");class gA extends cc{needsToWorkingColorSpace(e){return!0===e.isVideoTexture&&e.colorSpace!==u.jf0}_generateTextureSample(e,t,r,i){let s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:this.shaderStage;if("fragment"===s)if(i)return"textureSample( ".concat(t,", ").concat(t,"_sampler, ").concat(r,", ").concat(i," )");else return"textureSample( ".concat(t,", ").concat(t,"_sampler, ").concat(r," )");return this.isFilteredTexture(e)?this.generateFilteredTexture(e,t,r):this.generateTextureLod(e,t,r,i,"0")}_generateVideoSample(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.shaderStage;if("fragment"===r)return"textureSampleBaseClampToEdge( ".concat(e,", ").concat(e,"_sampler, vec2<f32>( ").concat(t,".x, 1.0 - ").concat(t,".y ) )");console.error("WebGPURenderer: THREE.VideoTexture does not support ".concat(r," shader."))}_generateTextureSampleLevel(e,t,r,i,s){let n=arguments.length>5&&void 0!==arguments[5]?arguments[5]:this.shaderStage;return("fragment"===n||"compute"===n)&&!1===this.isUnfilterable(e)?"textureSampleLevel( ".concat(t,", ").concat(t,"_sampler, ").concat(r,", ").concat(i," )"):this.isFilteredTexture(e)?this.generateFilteredTexture(e,t,r,i):this.generateTextureLod(e,t,r,s,i)}generateWrapFunction(e){let t="tsl_coord_".concat(gy[e.wrapS],"S_").concat(gy[e.wrapT],"_").concat(e.isData3DTexture?"3d":"2d","T"),r=g_[t];if(void 0===r){let i=[],s=e.isData3DTexture?"vec3f":"vec2f",n="fn ".concat(t,"( coord : ").concat(s," ) -> ").concat(s," {\n\n	return ").concat(s,"(\n"),a=(e,t)=>{e===u.GJx?(i.push(gN.repeatWrapping_float),n+="		tsl_repeatWrapping_float( coord.".concat(t," )")):e===u.ghU?(i.push(gN.clampWrapping_float),n+="		tsl_clampWrapping_float( coord.".concat(t," )")):e===u.kTW?(i.push(gN.mirrorWrapping_float),n+="		tsl_mirrorWrapping_float( coord.".concat(t," )")):(n+="		coord.".concat(t),console.warn('WebGPURenderer: Unsupported texture wrap type "'.concat(e,'" for vertex shader.')))};a(e.wrapS,"x"),n+=",\n",a(e.wrapT,"y"),e.isData3DTexture&&(n+=",\n",a(e.wrapR,"z")),n+="\n	);\n\n}\n",g_[t]=r=new da(n,i)}return r.build(this),t}generateArrayDeclaration(e,t){return"array< ".concat(this.getType(e),", ").concat(t," >")}generateTextureDimension(e,t,r){let i=this.getDataFromNode(e,this.shaderStage,this.globalCache);void 0===i.dimensionsSnippet&&(i.dimensionsSnippet={});let s=i.dimensionsSnippet[r];if(void 0===i.dimensionsSnippet[r]){let n,a,{primarySamples:o}=this.renderer.backend.utils.getTextureSampleData(e);a=e.isData3DTexture?"vec3<u32>":"vec2<u32>",n=o>1||e.isVideoTexture||e.isStorageTexture?t:"".concat(t).concat(r?", u32( ".concat(r," )"):""),s=new rK(new iy("textureDimensions( ".concat(n," )"),a)),i.dimensionsSnippet[r]=s,(e.isDataArrayTexture||e.isDepthArrayTexture||e.isData3DTexture)&&(i.arrayLayerCount=new rK(new iy("textureNumLayers(".concat(t,")"),"u32"))),e.isTextureCube&&(i.cubeFaceCount=new rK(new iy("6u","u32")))}return s.build(this)}generateFilteredTexture(e,t,r){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"0u";this._include("biquadraticTexture");let s=this.generateWrapFunction(e),n=this.generateTextureDimension(e,t,i);return"tsl_biquadraticTexture( ".concat(t,", ").concat(s,"( ").concat(r," ), ").concat(n,", u32( ").concat(i," ) )")}generateTextureLod(e,t,r,i){let s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:"0u",n=this.generateWrapFunction(e),a=this.generateTextureDimension(e,t,s),o=e.isData3DTexture?"vec3":"vec2",l="".concat(o,"<u32>( ").concat(n,"( ").concat(r," ) * ").concat(o,"<f32>( ").concat(a," ) )");return this.generateTextureLoad(e,t,l,i,s)}generateTextureLoad(e,t,r,i){let s,n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:"0u";return!0===e.isVideoTexture||!0===e.isStorageTexture?s="textureLoad( ".concat(t,", ").concat(r," )"):i?s="textureLoad( ".concat(t,", ").concat(r,", ").concat(i,", u32( ").concat(n," ) )"):(s="textureLoad( ".concat(t,", ").concat(r,", u32( ").concat(n," ) )"),this.renderer.backend.compatibilityMode&&e.isDepthTexture&&(s+=".x")),s}generateTextureStore(e,t,r,i){return"textureStore( ".concat(t,", ").concat(r,", ").concat(i," )")}isSampleCompare(e){return!0===e.isDepthTexture&&null!==e.compareFunction}isUnfilterable(e){return"float"!==this.getComponentTypeFromTexture(e)||!this.isAvailable("float32Filterable")&&!0===e.isDataTexture&&e.type===u.RQf||!1===this.isSampleCompare(e)&&e.minFilter===u.hxR&&e.magFilter===u.hxR||this.renderer.backend.utils.getTextureSampleData(e).primarySamples>1}generateTexture(e,t,r,i){let s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:this.shaderStage,n=null;return!0===e.isVideoTexture?this._generateVideoSample(t,r,s):this.isUnfilterable(e)?this.generateTextureLod(e,t,r,i,"0",s):this._generateTextureSample(e,t,r,i,s)}generateTextureGrad(e,t,r,i,s){let n=arguments.length>5&&void 0!==arguments[5]?arguments[5]:this.shaderStage;if("fragment"===n)return"textureSampleGrad( ".concat(t,", ").concat(t,"_sampler, ").concat(r,",  ").concat(i[0],", ").concat(i[1]," )");console.error("WebGPURenderer: THREE.TextureNode.gradient() does not support ".concat(n," shader."))}generateTextureCompare(e,t,r,i,s){let n=arguments.length>5&&void 0!==arguments[5]?arguments[5]:this.shaderStage;if("fragment"===n)return e.isDepthArrayTexture?"textureSampleCompare( ".concat(t,", ").concat(t,"_sampler, ").concat(r,", ").concat(s,", ").concat(i," )"):"textureSampleCompare( ".concat(t,", ").concat(t,"_sampler, ").concat(r,", ").concat(i," )");console.error("WebGPURenderer: THREE.DepthTexture.compareFunction() does not support ".concat(n," shader."))}generateTextureLevel(e,t,r,i,s){let n=arguments.length>5&&void 0!==arguments[5]?arguments[5]:this.shaderStage,a=null;return!0===e.isVideoTexture?this._generateVideoSample(t,r,n):this._generateTextureSampleLevel(e,t,r,i,s,n)}generateTextureBias(e,t,r,i,s){let n=arguments.length>5&&void 0!==arguments[5]?arguments[5]:this.shaderStage;if("fragment"===n)return"textureSampleBias( ".concat(t,", ").concat(t,"_sampler, ").concat(r,", ").concat(i," )");console.error("WebGPURenderer: THREE.TextureNode.biasNode does not support ".concat(n," shader."))}getPropertyName(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.shaderStage;if(!0===e.isNodeVarying&&!0===e.needsInterpolation){if("vertex"===t)return"varyings.".concat(e.name)}else if(!0===e.isNodeUniform){let t=e.name,r=e.type;return"texture"===r||"cubeTexture"===r||"storageTexture"===r||"texture3D"===r?t:"buffer"!==r&&"storageBuffer"!==r&&"indirectStorageBuffer"!==r?e.groupNode.name+"."+t:this.isCustomStruct(e)?t:t+".value"}return super.getPropertyName(e)}getOutputStructName(){return"output"}getFunctionOperator(e){let t=gT[e];return void 0!==t?(this._include(t),t):null}getNodeAccess(e,t){return"compute"!==t?C.READ_ONLY:e.access}getStorageAccess(e,t){return gf[this.getNodeAccess(e,t)]}getUniformFromNode(e,t,r){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,s=super.getUniformFromNode(e,t,r,i),n=this.getDataFromNode(e,r,this.globalCache);if(void 0===n.uniformGPU){let a,o=e.groupNode,l=o.name,u=this.getBindGroupArray(l,r);if("texture"===t||"cubeTexture"===t||"storageTexture"===t||"texture3D"===t){let i=null,n=this.getNodeAccess(e,r);if("texture"===t||"storageTexture"===t?i=new pd(s.name,s.node,o,n):"cubeTexture"===t?i=new ph(s.name,s.node,o,n):"texture3D"===t&&(i=new pc(s.name,s.node,o,n)),i.store=!0===e.isStorageTextureNode,i.setVisibility(gx[r]),("fragment"===r||"compute"===r)&&!1===this.isUnfilterable(e.value)&&!1===i.store){let e=new ge("".concat(s.name,"_sampler"),s.node,o);e.setVisibility(gx[r]),u.push(e,i),a=[e,i]}else u.push(i),a=[i]}else if("buffer"===t||"storageBuffer"===t||"indirectStorageBuffer"===t){let n=new("buffer"===t?ps:gi)(e,o);n.setVisibility(gx[r]),u.push(n),a=n,s.name=i||"NodeBuffer_"+s.id}else{let e=this.uniformGroups[r]||(this.uniformGroups[r]={}),i=e[l];void 0===i&&((i=new po(l,o)).setVisibility(gx[r]),e[l]=i,u.push(i)),a=this.getNodeUniform(s,t),i.addUniform(a)}n.uniformGPU=a}return s}getBuiltin(e,t,r){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:this.shaderStage,s=this.builtins[i]||(this.builtins[i]=new Map);return!1===s.has(e)&&s.set(e,{name:e,property:t,type:r}),t}hasBuiltin(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.shaderStage;return void 0!==this.builtins[t]&&this.builtins[t].has(e)}getVertexIndex(){return"vertex"===this.shaderStage?this.getBuiltin("vertex_index","vertexIndex","u32","attribute"):"vertexIndex"}buildFunctionCode(e){let t=e.layout,r=this.flowShaderNode(e),i=[];for(let e of t.inputs)i.push(e.name+" : "+this.getType(e.type));let s="fn ".concat(t.name,"( ").concat(i.join(", ")," ) -> ").concat(this.getType(t.type)," {\n").concat(r.vars,"\n").concat(r.code,"\n");return r.result&&(s+="	return ".concat(r.result,";\n")),s+="\n}\n"}getInstanceIndex(){return"vertex"===this.shaderStage?this.getBuiltin("instance_index","instanceIndex","u32","attribute"):"instanceIndex"}getInvocationLocalIndex(){return this.getBuiltin("local_invocation_index","invocationLocalIndex","u32","attribute")}getSubgroupSize(){return this.enableSubGroups(),this.getBuiltin("subgroup_size","subgroupSize","u32","attribute")}getInvocationSubgroupIndex(){return this.enableSubGroups(),this.getBuiltin("subgroup_invocation_id","invocationSubgroupIndex","u32","attribute")}getSubgroupIndex(){return this.enableSubGroups(),this.getBuiltin("subgroup_id","subgroupIndex","u32","attribute")}getDrawIndex(){return null}getFrontFacing(){return this.getBuiltin("front_facing","isFront","bool")}getFragCoord(){return this.getBuiltin("position","fragCoord","vec4<f32>")+".xy"}getFragDepth(){return"output."+this.getBuiltin("frag_depth","depth","f32","output")}getClipDistance(){return"varyings.hw_clip_distances"}isFlipY(){return!1}enableDirective(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.shaderStage;(this.directives[t]||(this.directives[t]=new Set)).add(e)}getDirectives(e){let t=[],r=this.directives[e];if(void 0!==r)for(let e of r)t.push("enable ".concat(e,";"));return t.join("\n")}enableSubGroups(){this.enableDirective("subgroups")}enableSubgroupsF16(){this.enableDirective("subgroups-f16")}enableClipDistances(){this.enableDirective("clip_distances")}enableShaderF16(){this.enableDirective("f16")}enableDualSourceBlending(){this.enableDirective("dual_source_blending")}enableHardwareClipping(e){this.enableClipDistances(),this.getBuiltin("clip_distances","hw_clip_distances","array<f32, ".concat(e," >"),"vertex")}getBuiltins(e){let t=[],r=this.builtins[e];if(void 0!==r)for(let{name:e,property:i,type:s}of r.values())t.push("@builtin( ".concat(e," ) ").concat(i," : ").concat(s));return t.join(",\n	")}getScopedArray(e,t,r,i){return!1===this.scopedArrays.has(e)&&this.scopedArrays.set(e,{name:e,scope:t,bufferType:r,bufferCount:i}),e}getScopedArrays(e){if("compute"!==e)return;let t=[];for(let{name:e,scope:r,bufferType:i,bufferCount:s}of this.scopedArrays.values()){let n=this.getType(i);t.push("var<".concat(r,"> ").concat(e,": array< ").concat(n,", ").concat(s," >;"))}return t.join("\n")}getAttributes(e){let t=[];if("compute"===e&&(this.getBuiltin("global_invocation_id","globalId","vec3<u32>","attribute"),this.getBuiltin("workgroup_id","workgroupId","vec3<u32>","attribute"),this.getBuiltin("local_invocation_id","localId","vec3<u32>","attribute"),this.getBuiltin("num_workgroups","numWorkgroups","vec3<u32>","attribute"),this.renderer.hasFeature("subgroups")&&(this.enableDirective("subgroups",e),this.getBuiltin("subgroup_size","subgroupSize","u32","attribute"))),"vertex"===e||"compute"===e){let e=this.getBuiltins("attribute");e&&t.push(e);let r=this.getAttributesArray();for(let e=0,i=r.length;e<i;e++){let i=r[e],s=i.name,n=this.getType(i.type);t.push("@location( ".concat(e," ) ").concat(s," : ").concat(n))}}return t.join(",\n	")}getStructMembers(e){let t=[];for(let r of e.members){let i=e.output?"@location( "+r.index+" ) ":"",s=this.getType(r.type);r.atomic&&(s="atomic< "+s+" >"),t.push("	".concat(i+r.name," : ").concat(s))}return e.output&&t.push("	".concat(this.getBuiltins("output"))),t.join(",\n")}getStructs(e){let t="",r=this.structs[e];if(r.length>0){let e=[];for(let t of r){let r="struct ".concat(t.name," {\n");r+=this.getStructMembers(t),r+="\n};",e.push(r)}t="\n"+e.join("\n\n")+"\n"}return t}getVar(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,i="var ".concat(t," : ");return null!==r?i+=this.generateArrayDeclaration(e,r):i+=this.getType(e),i}getVars(e){let t=[],r=this.vars[e];if(void 0!==r)for(let e of r)t.push("	".concat(this.getVar(e.type,e.name,e.count),";"));return"\n".concat(t.join("\n"),"\n")}getVaryings(e){let t=[];if("vertex"===e&&this.getBuiltin("position","Vertex","vec4<f32>","vertex"),"vertex"===e||"fragment"===e){let r=this.varyings,i=this.vars[e];for(let s=0;s<r.length;s++){let n=r[s];if(n.needsInterpolation){let e="@location( ".concat(s," )");if(n.interpolationType){let t=null!==n.interpolationSampling?", ".concat(n.interpolationSampling," )"):" )";e+=" @interpolate( ".concat(n.interpolationType).concat(t)}else/^(int|uint|ivec|uvec)/.test(n.type)&&(e+=" @interpolate( ".concat(this.renderer.backend.compatibilityMode?"flat, either":"flat"," )"));t.push("".concat(e," ").concat(n.name," : ").concat(this.getType(n.type)))}else"vertex"===e&&!1===i.includes(n)&&i.push(n)}}let r=this.getBuiltins(e);r&&t.push(r);let i=t.join(",\n	");return"vertex"===e?this._getWGSLStruct("VaryingsStruct","	"+i):i}isCustomStruct(e){let t=e.value,r=e.node,i=(t.isBufferAttribute||t.isInstancedBufferAttribute)&&null!==r.structTypeNode,s=r.value&&r.value.array&&"number"==typeof r.value.itemSize&&r.value.array.length>r.value.itemSize;return i&&!s}getUniforms(e){let t=this.uniforms[e],r=[],i=[],s=[],n={};for(let s of t){let t=s.groupNode.name,a=this.bindingsIndexes[t];if("texture"===s.type||"cubeTexture"===s.type||"storageTexture"===s.type||"texture3D"===s.type){let t,i=s.node.value;("fragment"===e||"compute"===e)&&!1===this.isUnfilterable(i)&&!0!==s.node.isStorageTextureNode&&(this.isSampleCompare(i)?r.push("@binding( ".concat(a.binding++," ) @group( ").concat(a.group," ) var ").concat(s.name,"_sampler : sampler_comparison;")):r.push("@binding( ".concat(a.binding++," ) @group( ").concat(a.group," ) var ").concat(s.name,"_sampler : sampler;")));let n="",{primarySamples:o}=this.renderer.backend.utils.getTextureSampleData(i);if(o>1&&(n="_multisampled"),!0===i.isCubeTexture)t="texture_cube<f32>";else if(!0===i.isDataArrayTexture||!0===i.isCompressedArrayTexture||!0===i.isTextureArray)t="texture_2d_array<f32>";else if(!0===i.isDepthTexture)t=this.renderer.backend.compatibilityMode&&null===i.compareFunction?"texture".concat(n,"_2d<f32>"):"texture_depth".concat(n,"_2d").concat(!0===i.isDepthArrayTexture?"_array":"");else if(!0===i.isVideoTexture)t="texture_external";else if(!0===i.isData3DTexture)t="texture_3d<f32>";else if(!0===s.node.isStorageTextureNode){let r=gl(i),n=this.getStorageAccess(s.node,e);t="texture_storage_2d<".concat(r,", ").concat(n,">")}else{let e=this.getComponentTypeFromTexture(i).charAt(0);t="texture".concat(n,"_2d<").concat(e,"32>")}r.push("@binding( ".concat(a.binding++," ) @group( ").concat(a.group," ) var ").concat(s.name," : ").concat(t,";"))}else if("buffer"===s.type||"storageBuffer"===s.type||"indirectStorageBuffer"===s.type){let t=s.node,r=this.getType(t.getNodeType(this)),n=t.bufferCount,o=n>0&&"buffer"===s.type?", "+n:"",l=t.isStorageBufferNode?"storage, ".concat(this.getStorageAccess(t,e)):"uniform";if(this.isCustomStruct(s))i.push("@binding( ".concat(a.binding++," ) @group( ").concat(a.group," ) var<").concat(l,"> ").concat(s.name," : ").concat(r,";"));else{let e=t.isAtomic?"atomic<".concat(r,">"):"".concat(r),n="	value : array< ".concat(e).concat(o," >");i.push(this._getWGSLStructBinding(s.name,n,l,a.binding++,a.group))}}else{let e=this.getType(this.getVectorType(s.type)),t=s.groupNode.name;(n[t]||(n[t]={index:a.binding++,id:a.group,snippets:[]})).snippets.push("	".concat(s.name," : ").concat(e))}}for(let e in n){let t=n[e];s.push(this._getWGSLStructBinding(e,t.snippets.join(",\n"),"uniform",t.index,t.id))}let a=r.join("\n");return a+=i.join("\n"),a+=s.join("\n")}buildCode(){let e=null!==this.material?{fragment:{},vertex:{}}:{compute:{}};for(let t in this.sortBindingGroups(),e){this.shaderStage=t;let r=e[t];r.uniforms=this.getUniforms(t),r.attributes=this.getAttributes(t),r.varyings=this.getVaryings(t),r.structs=this.getStructs(t),r.vars=this.getVars(t),r.codes=this.getCodes(t),r.directives=this.getDirectives(t),r.scopedArrays=this.getScopedArrays(t);let i="// code\n\n";i+=this.flowCode[t];let s=this.flowNodes[t],n=s[s.length-1],a=n.outputNode,o=void 0!==a&&!0===a.isOutputStructNode;for(let e of s){let s=this.getFlowData(e),l=e.name;if(l&&(i.length>0&&(i+="\n"),i+="	// flow -> ".concat(l,"\n")),i+="".concat(s.code,"\n	"),e===n&&"compute"!==t){if(i+="// result\n\n	","vertex"===t)i+="varyings.Vertex = ".concat(s.result,";");else if("fragment"===t)if(o)r.returnType=a.getNodeType(this),r.structs+="var<private> output : "+r.returnType+";",i+="return ".concat(s.result,";");else{let e="	@location(0) color: vec4<f32>",t=this.getBuiltins("output");t&&(e+=",\n	"+t),r.returnType="OutputStruct",r.structs+=this._getWGSLStruct("OutputStruct",e),r.structs+="\nvar<private> output : OutputStruct;",i+="output.color = ".concat(s.result,";\n\n	return output;")}}}r.flow=i}this.shaderStage=null,null!==this.material?(this.vertexShader=this._getWGSLVertexCode(e.vertex),this.fragmentShader=this._getWGSLFragmentCode(e.fragment)):this.computeShader=this._getWGSLComputeCode(e.compute,(this.object.workgroupSize||[64]).join(", "))}getMethod(e){let t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return null!==r&&(t=this._getWGSLMethod(e+"_"+r)),void 0===t&&(t=this._getWGSLMethod(e)),t||e}getType(e){return gv[e]||e}isAvailable(e){let t=gb[e];return void 0===t&&("float32Filterable"===e?t=this.renderer.hasFeature("float32-filterable"):"clipDistance"===e&&(t=this.renderer.hasFeature("clip-distances")),gb[e]=t),t}_getWGSLMethod(e){return void 0!==gN[e]&&this._include(e),gS[e]}_include(e){let t=gN[e];return t.build(this),null!==this.currentFunctionNode&&this.currentFunctionNode.includes.push(t),t}_getWGSLVertexCode(e){return"".concat(this.getSignature(),"\n// directives\n").concat(e.directives,"\n\n// structs\n").concat(e.structs,"\n\n// uniforms\n").concat(e.uniforms,"\n\n// varyings\n").concat(e.varyings,"\nvar<private> varyings : VaryingsStruct;\n\n// codes\n").concat(e.codes,"\n\n@vertex\nfn main( ").concat(e.attributes," ) -> VaryingsStruct {\n\n	// vars\n	").concat(e.vars,"\n\n	// flow\n	").concat(e.flow,"\n\n	return varyings;\n\n}\n")}_getWGSLFragmentCode(e){return"".concat(this.getSignature(),"\n// global\n").concat(gR,"\n\n// structs\n").concat(e.structs,"\n\n// uniforms\n").concat(e.uniforms,"\n\n// codes\n").concat(e.codes,"\n\n@fragment\nfn main( ").concat(e.varyings," ) -> ").concat(e.returnType," {\n\n	// vars\n	").concat(e.vars,"\n\n	// flow\n	").concat(e.flow,"\n\n}\n")}_getWGSLComputeCode(e,t){return"".concat(this.getSignature(),"\n// directives\n").concat(e.directives,"\n\n// system\nvar<private> instanceIndex : u32;\n\n// locals\n").concat(e.scopedArrays,"\n\n// structs\n").concat(e.structs,"\n\n// uniforms\n").concat(e.uniforms,"\n\n// codes\n").concat(e.codes,"\n\n@compute @workgroup_size( ").concat(t," )\nfn main( ").concat(e.attributes," ) {\n\n	// system\n	instanceIndex = globalId.x + globalId.y * numWorkgroups.x * u32(").concat(t,") + globalId.z * numWorkgroups.x * numWorkgroups.y * u32(").concat(t,");\n\n	// vars\n	").concat(e.vars,"\n\n	// flow\n	").concat(e.flow,"\n\n}\n")}_getWGSLStruct(e,t){return"\nstruct ".concat(e," {\n").concat(t,"\n};")}_getWGSLStructBinding(e,t,r){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,n=e+"Struct",a=this._getWGSLStruct(n,t);return"".concat(a,"\n@binding( ").concat(i," ) @group( ").concat(s," )\nvar<").concat(r,"> ").concat(e," : ").concat(n,";")}constructor(e,t){super(e,t,new gg),this.uniformGroups={},this.builtins={},this.directives={},this.scopedArrays=new Map}}class gE{getCurrentDepthStencilFormat(e){let t;return null!==e.depthTexture?t=this.getTextureFormatGPU(e.depthTexture):e.depth&&e.stencil?t=pX.Depth24PlusStencil8:e.depth&&(t=pX.Depth24Plus),t}getTextureFormatGPU(e){return this.backend.get(e).format}getTextureSampleData(e){let t;if(e.isFramebufferTexture)t=1;else if(e.isDepthTexture&&!e.renderTarget){let e=this.backend.renderer,r=e.getRenderTarget();t=r?r.samples:e.samples}else e.renderTarget&&(t=e.renderTarget.samples);let r=(t=t||1)>1&&null!==e.renderTarget&&!0!==e.isDepthTexture&&!0!==e.isFramebufferTexture,i=r?1:t;return{samples:t,primarySamples:i,isMSAA:r}}getCurrentColorFormat(e){let t;return null!==e.textures?this.getTextureFormatGPU(e.textures[0]):this.getPreferredCanvasFormat()}getCurrentColorSpace(e){return null!==e.textures?e.textures[0].colorSpace:this.backend.renderer.outputColorSpace}getPrimitiveTopology(e,t){return e.isPoints?pG.PointList:e.isLineSegments||e.isMesh&&!0===t.wireframe?pG.LineList:e.isLine?pG.LineStrip:e.isMesh?pG.TriangleList:void 0}getSampleCount(e){let t=1;return e>1&&2===(t=Math.pow(2,Math.floor(Math.log2(e))))&&(t=4),t}getSampleCountRenderContext(e){return null!==e.textures?this.getSampleCount(e.sampleCount):this.getSampleCount(this.backend.renderer.samples)}getPreferredCanvasFormat(){let e=this.backend.parameters.outputType;if(void 0===e)return navigator.gpu.getPreferredCanvasFormat();if(e===u.OUM)return pX.BGRA8Unorm;if(e===u.ix0)return pX.RGBA16Float;throw Error("Unsupported outputType")}constructor(e){this.backend=e}}let gC=new Map([[Int8Array,["sint8","snorm8"]],[Uint8Array,["uint8","unorm8"]],[Int16Array,["sint16","snorm16"]],[Uint16Array,["uint16","unorm16"]],[Int32Array,["sint32","snorm32"]],[Uint32Array,["uint32","unorm32"]],[Float32Array,["float32"]]]),gw=new Map([[u.Oax,["float16"]]]),gM=new Map([[Int32Array,"sint32"],[Int16Array,"sint32"],[Uint32Array,"uint32"],[Uint16Array,"uint32"],[Float32Array,"float32"]]);class gB{createAttribute(e,t){let r=this._getBufferAttribute(e),i=this.backend,s=i.get(r),n=s.buffer;if(void 0===n){let a=i.device,o=r.array;if(!1===e.normalized){if(o.constructor===Int16Array||o.constructor===Int8Array)o=new Int32Array(o);else if((o.constructor===Uint16Array||o.constructor===Uint8Array)&&(o=new Uint32Array(o),t&GPUBufferUsage.INDEX))for(let e=0;e<o.length;e++)65535===o[e]&&(o[e]=0xffffffff)}if(r.array=o,(r.isStorageBufferAttribute||r.isStorageInstancedBufferAttribute)&&3===r.itemSize){o=new o.constructor(4*r.count);for(let e=0;e<r.count;e++)o.set(r.array.subarray(3*e,3*e+3),4*e);r.itemSize=4,r.array=o,s._force3to4BytesAlignment=!0}let l=o.byteLength+(4-o.byteLength%4)%4;n=a.createBuffer({label:r.name,size:l,usage:t,mappedAtCreation:!0}),new o.constructor(n.getMappedRange()).set(o),n.unmap(),s.buffer=n}}updateAttribute(e){let t=this._getBufferAttribute(e),r=this.backend,i=r.device,s=r.get(t),n=r.get(t).buffer,a=t.array;if(!0===s._force3to4BytesAlignment){a=new a.constructor(4*t.count);for(let e=0;e<t.count;e++)a.set(t.array.subarray(3*e,3*e+3),4*e);t.array=a}let o=this._isTypedArray(a),l=t.updateRanges;if(0===l.length)i.queue.writeBuffer(n,0,a,0);else{let e=o?1:a.BYTES_PER_ELEMENT;for(let t=0,r=l.length;t<r;t++){let r,u,d=l[t];if(!0===s._force3to4BytesAlignment){let t=Math.floor(d.start/3),i=Math.ceil(d.count/3);r=4*t*e,u=4*i*e}else r=d.start*e,u=d.count*e;let h=r*(o?a.BYTES_PER_ELEMENT:1);i.queue.writeBuffer(n,h,a,r,u)}t.clearUpdateRanges()}}createShaderVertexBuffers(e){let t=e.getAttributes(),r=new Map;for(let e=0;e<t.length;e++){let i=t[e],s=i.array.BYTES_PER_ELEMENT,n=this._getBufferAttribute(i),a=r.get(n);if(void 0===a){let e,t;!0===i.isInterleavedBufferAttribute?(e=i.data.stride*s,t=i.data.isInstancedInterleavedBuffer?p5.Instance:p5.Vertex):(e=i.itemSize*s,t=i.isInstancedBufferAttribute?p5.Instance:p5.Vertex),!1===i.normalized&&(i.array.constructor===Int16Array||i.array.constructor===Uint16Array)&&(e=4),a={arrayStride:e,attributes:[],stepMode:t},r.set(n,a)}let o=this._getVertexFormat(i),l=!0===i.isInterleavedBufferAttribute?i.offset*s:0;a.attributes.push({shaderLocation:e,offset:l,format:o})}return Array.from(r.values())}destroyAttribute(e){let t=this.backend;t.get(this._getBufferAttribute(e)).buffer.destroy(),t.delete(e)}async getArrayBufferAsync(e){let t=this.backend,r=t.device,i=t.get(this._getBufferAttribute(e)).buffer,s=i.size,n=r.createBuffer({label:"".concat(e.name,"_readback"),size:s,usage:GPUBufferUsage.COPY_DST|GPUBufferUsage.MAP_READ}),a=r.createCommandEncoder({label:"readback_encoder_".concat(e.name)});a.copyBufferToBuffer(i,0,n,0,s);let o=a.finish();r.queue.submit([o]),await n.mapAsync(GPUMapMode.READ);let l=n.getMappedRange(),u=new e.array.constructor(l.slice(0));return n.unmap(),u.buffer}_getVertexFormat(e){let t,{itemSize:r,normalized:i}=e,s=e.array.constructor,n=e.constructor;if(1===r)t=gM.get(s);else{let e=(gw.get(n)||gC.get(s))[+!!i];if(e){let i=4*Math.floor((s.BYTES_PER_ELEMENT*r+3)/4)/s.BYTES_PER_ELEMENT;if(i%1)throw Error("THREE.WebGPUAttributeUtils: Bad vertex format item size.");t="".concat(e,"x").concat(i)}}return t||console.error("THREE.WebGPUAttributeUtils: Vertex format not supported yet."),t}_isTypedArray(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}_getBufferAttribute(e){return e.isInterleavedBufferAttribute&&(e=e.data),e}constructor(e){this.backend=e}}class gP{createBindingsLayout(e){let t=this.backend,r=t.device,i=[],s=0;for(let r of e.bindings){let e={binding:s++,visibility:r.visibility};if(r.isUniformBuffer||r.isStorageBuffer){let t={};r.isStorageBuffer&&(4&r.visibility&&(r.access===C.READ_WRITE||r.access===C.WRITE_ONLY)?t.type=p0.Storage:t.type=p0.ReadOnlyStorage),e.buffer=t}else if(r.isSampler){let i={};r.texture.isDepthTexture&&(null!==r.texture.compareFunction?i.type=p2.Comparison:t.compatibilityMode&&(i.type=p2.NonFiltering)),e.sampler=i}else if(r.isSampledTexture&&r.texture.isVideoTexture)e.externalTexture={};else if(r.isSampledTexture&&r.store){let t={};t.format=this.backend.get(r.texture).texture.format;let i=r.access;i===C.READ_WRITE?t.access=p1.ReadWrite:i===C.WRITE_ONLY?t.access=p1.WriteOnly:t.access=p1.ReadOnly,e.storageTexture=t}else if(r.isSampledTexture){let i={},{primarySamples:s}=t.utils.getTextureSampleData(r.texture);if(s>1&&(i.multisampled=!0,r.texture.isDepthTexture||(i.sampleType=p3.UnfilterableFloat)),r.texture.isDepthTexture)t.compatibilityMode&&null===r.texture.compareFunction?i.sampleType=p3.UnfilterableFloat:i.sampleType=p3.Depth;else if(r.texture.isDataTexture||r.texture.isDataArrayTexture||r.texture.isData3DTexture){let e=r.texture.type;e===u.Yuy?i.sampleType=p3.SInt:e===u.bkx?i.sampleType=p3.UInt:e===u.RQf&&(this.backend.hasFeature("float32-filterable")?i.sampleType=p3.Float:i.sampleType=p3.UnfilterableFloat)}r.isSampledCubeTexture?i.viewDimension=p6.Cube:r.texture.isDataArrayTexture||r.texture.isDepthArrayTexture||r.texture.isCompressedArrayTexture?i.viewDimension=p6.TwoDArray:r.isSampledTexture3D&&(i.viewDimension=p6.ThreeD),e.texture=i}else console.error('WebGPUBindingUtils: Unsupported binding "'.concat(r,'".'));i.push(e)}return r.createBindGroupLayout({entries:i})}createBindings(e,t,r){let i,s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,{backend:n,bindGroupLayoutCache:a}=this,o=n.get(e),l=a.get(e.bindingsReference);void 0===l&&(l=this.createBindingsLayout(e),a.set(e.bindingsReference,l)),r>0&&(void 0===o.groups&&(o.groups=[],o.versions=[]),o.versions[r]===s&&(i=o.groups[r])),void 0===i&&(i=this.createBindGroup(e,l),r>0&&(o.groups[r]=i,o.versions[r]=s)),o.group=i,o.layout=l}updateBinding(e){let t=this.backend,r=t.device,i=e.buffer,s=t.get(e).buffer;r.queue.writeBuffer(s,0,i,0)}createBindGroupIndex(e,t){let r=this.backend.device,i=GPUBufferUsage.UNIFORM|GPUBufferUsage.COPY_DST,s=e[0],n=r.createBuffer({label:"bindingCameraIndex_"+s,size:16,usage:i});r.queue.writeBuffer(n,0,e,0);let a=[{binding:0,resource:{buffer:n}}];return r.createBindGroup({label:"bindGroupCameraIndex_"+s,layout:t,entries:a})}createBindGroup(e,t){let r=this.backend,i=r.device,s=0,n=[];for(let t of e.bindings){if(t.isUniformBuffer){let e=r.get(t);if(void 0===e.buffer){let r=t.byteLength,s=GPUBufferUsage.UNIFORM|GPUBufferUsage.COPY_DST;e.buffer=i.createBuffer({label:"bindingBuffer_"+t.name,size:r,usage:s})}n.push({binding:s,resource:{buffer:e.buffer}})}else if(t.isStorageBuffer){let e=r.get(t);if(void 0===e.buffer){let i=t.attribute;e.buffer=r.get(i).buffer}n.push({binding:s,resource:{buffer:e.buffer}})}else if(t.isSampler){let e=r.get(t.texture);n.push({binding:s,resource:e.sampler})}else if(t.isSampledTexture){let e,a=r.get(t.texture);if(void 0!==a.externalTexture)e=i.importExternalTexture({source:a.externalTexture});else{let r=t.store?1:a.texture.mipLevelCount,i="view-".concat(a.texture.width,"-").concat(a.texture.height,"-").concat(r);if(void 0===(e=a[i])){let s,n=p8.All;s=t.isSampledCubeTexture?p6.Cube:t.isSampledTexture3D?p6.ThreeD:t.texture.isDataArrayTexture||t.texture.isDepthArrayTexture||t.texture.isCompressedArrayTexture?p6.TwoDArray:p6.TwoD,e=a[i]=a.texture.createView({aspect:n,dimension:s,mipLevelCount:r})}}n.push({binding:s,resource:e})}s++}return i.createBindGroup({label:"bindGroup_"+e.name,layout:t,entries:n})}constructor(e){this.backend=e,this.bindGroupLayoutCache=new WeakMap}}class gF{_getSampleCount(e){return this.backend.utils.getSampleCountRenderContext(e)}createRenderPipeline(e,t){let r,{object:i,material:s,geometry:n,pipeline:a}=e,{vertexProgram:o,fragmentProgram:l}=a,d=this.backend,h=d.device,c=d.utils,p=d.get(a),g=[];for(let t of e.getBindings()){let e=d.get(t);g.push(e.layout)}let m=d.attributeUtils.createShaderVertexBuffers(e);s.blending!==u.XIg&&(s.blending!==u.NTi||!1!==s.transparent)&&(r=this._getBlending(s));let f={};!0===s.stencilWrite&&(f={compare:this._getStencilCompare(s),failOp:this._getStencilOperation(s.stencilFail),depthFailOp:this._getStencilOperation(s.stencilZFail),passOp:this._getStencilOperation(s.stencilZPass)});let y=this._getColorWriteMask(s),x=[];if(null!==e.context.textures){let t=e.context.textures;for(let e=0;e<t.length;e++){let i=c.getTextureFormatGPU(t[e]);x.push({format:i,blend:r,writeMask:y})}}else{let t=c.getCurrentColorFormat(e.context);x.push({format:t,blend:r,writeMask:y})}let b=d.get(o).module,T=d.get(l).module,v=this._getPrimitiveState(i,n,s),_=this._getDepthCompare(s),N=c.getCurrentDepthStencilFormat(e.context),S=this._getSampleCount(e.context),R={label:"renderPipeline_".concat(s.name||s.type,"_").concat(s.id),vertex:Object.assign({},b,{buffers:m}),fragment:Object.assign({},T,{targets:x}),primitive:v,multisample:{count:S,alphaToCoverageEnabled:s.alphaToCoverage&&S>1},layout:h.createPipelineLayout({bindGroupLayouts:g})},A={},E=e.context.depth,C=e.context.stencil;if((!0===E||!0===C)&&(!0===E&&(A.format=N,A.depthWriteEnabled=s.depthWrite,A.depthCompare=_),!0===C&&(A.stencilFront=f,A.stencilBack={},A.stencilReadMask=s.stencilFuncMask,A.stencilWriteMask=s.stencilWriteMask),!0===s.polygonOffset&&(A.depthBias=s.polygonOffsetUnits,A.depthBiasSlopeScale=s.polygonOffsetFactor,A.depthBiasClamp=0),R.depthStencil=A),null===t)p.pipeline=h.createRenderPipeline(R);else{let e=new Promise(e=>{h.createRenderPipelineAsync(R).then(t=>{p.pipeline=t,e()})});t.push(e)}}createBundleEncoder(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"renderBundleEncoder",{utils:r,device:i}=this.backend,s=r.getCurrentDepthStencilFormat(e),n=r.getCurrentColorFormat(e),a=this._getSampleCount(e);return i.createRenderBundleEncoder({label:t,colorFormats:[n],depthStencilFormat:s,sampleCount:a})}createComputePipeline(e,t){let r=this.backend,i=r.device,s=r.get(e.computeProgram).module,n=r.get(e),a=[];for(let e of t){let t=r.get(e);a.push(t.layout)}n.pipeline=i.createComputePipeline({compute:s,layout:i.createPipelineLayout({bindGroupLayouts:a})})}_getBlending(e){let t,r,i=e.blending,s=e.blendSrc,n=e.blendDst,a=e.blendEquation;if(i===u.bCz){let i=null!==e.blendSrcAlpha?e.blendSrcAlpha:s,o=null!==e.blendDstAlpha?e.blendDstAlpha:n,l=null!==e.blendEquationAlpha?e.blendEquationAlpha:a;t={srcFactor:this._getBlendFactor(s),dstFactor:this._getBlendFactor(n),operation:this._getBlendOperation(a)},r={srcFactor:this._getBlendFactor(i),dstFactor:this._getBlendFactor(o),operation:this._getBlendOperation(l)}}else{let s=e.premultipliedAlpha,n=(e,i,s,n)=>{t={srcFactor:e,dstFactor:i,operation:pZ.Add},r={srcFactor:s,dstFactor:n,operation:pZ.Add}};if(s)switch(i){case u.NTi:n(pK.One,pK.OneMinusSrcAlpha,pK.One,pK.OneMinusSrcAlpha);break;case u.EZo:n(pK.One,pK.One,pK.One,pK.One);break;case u.Kwu:n(pK.Zero,pK.OneMinusSrc,pK.Zero,pK.One);break;case u.EdD:n(pK.Zero,pK.Src,pK.Zero,pK.SrcAlpha)}else switch(i){case u.NTi:n(pK.SrcAlpha,pK.OneMinusSrcAlpha,pK.One,pK.OneMinusSrcAlpha);break;case u.EZo:n(pK.SrcAlpha,pK.One,pK.SrcAlpha,pK.One);break;case u.Kwu:n(pK.Zero,pK.OneMinusSrc,pK.Zero,pK.One);break;case u.EdD:n(pK.Zero,pK.Src,pK.Zero,pK.Src)}}if(void 0!==t&&void 0!==r)return{color:t,alpha:r};console.error("THREE.WebGPURenderer: Invalid blending: ",i)}_getBlendFactor(e){let t;switch(e){case u.ojh:t=pK.Zero;break;case u.qad:t=pK.One;break;case u.f4X:t=pK.Src;break;case u.LiQ:t=pK.OneMinusSrc;break;case u.ie2:t=pK.SrcAlpha;break;case u.OuU:t=pK.OneMinusSrcAlpha;break;case u.wn6:t=pK.Dst;break;case u.aEY:t=pK.OneMinusDstColor;break;case u.hdd:t=pK.DstAlpha;break;case u.Nt7:t=pK.OneMinusDstAlpha;break;case u.hgQ:t=pK.SrcAlphaSaturated;break;case 211:t=pK.Constant;break;case 212:t=pK.OneMinusConstant;break;default:console.error("THREE.WebGPURenderer: Blend factor not supported.",e)}return t}_getStencilCompare(e){let t,r=e.stencilFunc;switch(r){case u.HPb:t=pk.Never;break;case u.sKt:t=pk.Always;break;case u.kYr:t=pk.Less;break;case u.CR7:t=pk.LessEqual;break;case u.jsO:t=pk.Equal;break;case u.TMh:t=pk.GreaterEqual;break;case u.RcT:t=pk.Greater;break;case u.klZ:t=pk.NotEqual;break;default:console.error("THREE.WebGPURenderer: Invalid stencil function.",r)}return t}_getStencilOperation(e){let t;switch(e){case u.VVr:t=pJ.Keep;break;case u.kqe:t=pJ.Zero;break;case u.kG0:t=pJ.Replace;break;case u.oVO:t=pJ.Invert;break;case u.HLH:t=pJ.IncrementClamp;break;case u.ROr:t=pJ.DecrementClamp;break;case u.Ru$:t=pJ.IncrementWrap;break;case u.fJr:t=pJ.DecrementWrap;break;default:console.error("THREE.WebGPURenderer: Invalid stencil operation.",t)}return t}_getBlendOperation(e){let t;switch(e){case u.gO9:t=pZ.Add;break;case u.FXf:t=pZ.Subtract;break;case u.nST:t=pZ.ReverseSubtract;break;case u.znC:t=pZ.Min;break;case u.$ei:t=pZ.Max;break;default:console.error("THREE.WebGPUPipelineUtils: Blend equation not supported.",e)}return t}_getPrimitiveState(e,t,r){let i={};switch(i.topology=this.backend.utils.getPrimitiveTopology(e,r),null!==t.index&&!0===e.isLine&&!0!==e.isLineSegments&&(i.stripIndexFormat=t.index.array instanceof Uint16Array?pj.Uint16:pj.Uint32),r.side){case u.hB5:i.frontFace=pW.CCW,i.cullMode=pq.Back;break;case u.hsX:i.frontFace=pW.CCW,i.cullMode=pq.Front;break;case u.$EB:i.frontFace=pW.CCW,i.cullMode=pq.None;break;default:console.error("THREE.WebGPUPipelineUtils: Unknown material.side value.",r.side)}return i}_getColorWriteMask(e){return!0===e.colorWrite?p$.All:p$.None}_getDepthCompare(e){let t;if(!1===e.depthTest)t=pk.Always;else{let r=e.depthFunc;switch(r){case u.eHc:t=pk.Never;break;case u.lGu:t=pk.Always;break;case u.brA:t=pk.Less;break;case u.xSv:t=pk.LessEqual;break;case u.U3G:t=pk.Equal;break;case u.Gwm:t=pk.GreaterEqual;break;case u.K52:t=pk.Greater;break;case u.bw0:t=pk.NotEqual;break;default:console.error("THREE.WebGPUPipelineUtils: Invalid depth function.",r)}}return t}constructor(e){this.backend=e}}class gU extends pD{allocateQueriesForContext(e){if(!this.trackTimestamp||this.isDisposed)return null;if(this.currentQueryIndex+2>this.maxQueries)return(0,u.mcG)("WebGPUTimestampQueryPool [".concat(this.type,"]: Maximum number of queries exceeded, when using trackTimestamp it is necessary to resolves the queries via renderer.resolveTimestampsAsync( THREE.TimestampQuery.").concat(this.type.toUpperCase()," ).")),null;let t=this.currentQueryIndex;return this.currentQueryIndex+=2,this.queryOffsets.set(e.id,t),t}async resolveQueriesAsync(){if(!this.trackTimestamp||0===this.currentQueryIndex||this.isDisposed)return this.lastValue;if(this.pendingResolve)return this.pendingResolve;this.pendingResolve=this._resolveQueries();try{return await this.pendingResolve}finally{this.pendingResolve=null}}async _resolveQueries(){if(this.isDisposed)return this.lastValue;try{if("unmapped"!==this.resultBuffer.mapState)return this.lastValue;let e=new Map(this.queryOffsets),t=this.currentQueryIndex,r=8*t;this.currentQueryIndex=0,this.queryOffsets.clear();let i=this.device.createCommandEncoder();i.resolveQuerySet(this.querySet,0,t,this.resolveBuffer,0),i.copyBufferToBuffer(this.resolveBuffer,0,this.resultBuffer,0,r);let s=i.finish();if(this.device.queue.submit([s]),"unmapped"!==this.resultBuffer.mapState)return this.lastValue;if(await this.resultBuffer.mapAsync(GPUMapMode.READ,0,r),this.isDisposed)return"mapped"===this.resultBuffer.mapState&&this.resultBuffer.unmap(),this.lastValue;let n=new BigUint64Array(this.resultBuffer.getMappedRange(0,r)),a=0;for(let[,t]of e){let e=n[t],r=n[t+1],i=Number(r-e)/1e6;a+=i}return this.resultBuffer.unmap(),this.lastValue=a,a}catch(e){return console.error("Error resolving queries:",e),"mapped"===this.resultBuffer.mapState&&this.resultBuffer.unmap(),this.lastValue}}async dispose(){if(!this.isDisposed){if(this.isDisposed=!0,this.pendingResolve)try{await this.pendingResolve}catch(e){console.error("Error waiting for pending resolve:",e)}if(this.resultBuffer&&"mapped"===this.resultBuffer.mapState)try{this.resultBuffer.unmap()}catch(e){console.error("Error unmapping buffer:",e)}this.querySet&&(this.querySet.destroy(),this.querySet=null),this.resolveBuffer&&(this.resolveBuffer.destroy(),this.resolveBuffer=null),this.resultBuffer&&(this.resultBuffer.destroy(),this.resultBuffer=null),this.queryOffsets.clear(),this.pendingResolve=null}}constructor(e,t,r=2048){super(r),this.device=e,this.type=t,this.querySet=this.device.createQuerySet({type:"timestamp",count:this.maxQueries,label:"queryset_global_timestamp_".concat(t)});let i=8*this.maxQueries;this.resolveBuffer=this.device.createBuffer({label:"buffer_timestamp_resolve_".concat(t),size:i,usage:GPUBufferUsage.QUERY_RESOLVE|GPUBufferUsage.COPY_SRC}),this.resultBuffer=this.device.createBuffer({label:"buffer_timestamp_result_".concat(t),size:i,usage:GPUBufferUsage.COPY_DST|GPUBufferUsage.MAP_READ})}}class gL extends p_{async init(e){let t;await super.init(e);let r=this.parameters;if(void 0===r.device){let e={powerPreference:r.powerPreference,featureLevel:r.compatibilityMode?"compatibility":void 0},i="undefined"!=typeof navigator?await navigator.gpu.requestAdapter(e):null;if(null===i)throw Error("WebGPUBackend: Unable to create WebGPU adapter.");let s=Object.values(p9),n=[];for(let e of s)i.features.has(e)&&n.push(e);let a={requiredFeatures:n,requiredLimits:r.requiredLimits};t=await i.requestDevice(a)}else t=r.device;t.lost.then(t=>{let r={api:"WebGPU",message:t.message||"Unknown reason",reason:t.reason||null,originalEvent:t};e.onDeviceLost(r)});let i=void 0!==r.context?r.context:e.domElement.getContext("webgpu");this.device=t,this.context=i;let s=r.alpha?"premultiplied":"opaque";this.trackTimestamp=this.trackTimestamp&&this.hasFeature(p9.TimestampQuery),this.context.configure({device:this.device,format:this.utils.getPreferredCanvasFormat(),usage:GPUTextureUsage.RENDER_ATTACHMENT|GPUTextureUsage.COPY_SRC,alphaMode:s}),this.updateSize()}get coordinateSystem(){return u.i7u}async getArrayBufferAsync(e){return await this.attributeUtils.getArrayBufferAsync(e)}getContext(){return this.context}_getDefaultRenderPassDescriptor(){let e=this.defaultRenderPassdescriptor;if(null===e){let t=this.renderer;e={colorAttachments:[{view:null}]},(!0===this.renderer.depth||!0===this.renderer.stencil)&&(e.depthStencilAttachment={view:this.textureUtils.getDepthBuffer(t.depth,t.stencil).createView()});let r=e.colorAttachments[0];this.renderer.samples>0?r.view=this.colorBuffer.createView():r.resolveTarget=void 0,this.defaultRenderPassdescriptor=e}let t=e.colorAttachments[0];return this.renderer.samples>0?t.resolveTarget=this.context.getCurrentTexture().createView():t.view=this.context.getCurrentTexture().createView(),e}_isRenderCameraDepthArray(e){return e.depthTexture&&e.depthTexture.isDepthArrayTexture&&e.camera.isArrayCamera}_getRenderPassDescriptor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.renderTarget,i=this.get(r),s=i.descriptors;if(void 0===s||i.width!==r.width||i.height!==r.height||i.dimensions!==r.dimensions||i.activeMipmapLevel!==e.activeMipmapLevel||i.activeCubeFace!==e.activeCubeFace||i.samples!==r.samples){i.descriptors=s={};let e=()=>{r.removeEventListener("dispose",e),this.delete(r)};!1===r.hasEventListener("dispose",e)&&r.addEventListener("dispose",e)}let n=e.getCacheKey(),a=s[n];if(void 0===a){let t,o=e.textures,l=[],u=this._isRenderCameraDepthArray(e);for(let i=0;i<o.length;i++){let s=this.get(o[i]),n={label:"colorAttachment_".concat(i),baseMipLevel:e.activeMipmapLevel,mipLevelCount:1,baseArrayLayer:e.activeCubeFace,arrayLayerCount:1,dimension:p6.TwoD};if(r.isRenderTarget3D)t=e.activeCubeFace,n.baseArrayLayer=0,n.dimension=p6.ThreeD,n.depthOrArrayLayers=o[i].image.depth;else if(r.isRenderTargetArray)if(!0===u){let t=e.camera.cameras;for(let e=0;e<t.length;e++){let t={...n,baseArrayLayer:e,arrayLayerCount:1,dimension:p6.TwoD},r=s.texture.createView(t);l.push({view:r,resolveTarget:void 0,depthSlice:void 0})}}else n.dimension=p6.TwoDArray,n.depthOrArrayLayers=o[i].image.depth;if(!0!==u){let e,r,i=s.texture.createView(n);void 0!==s.msaaTexture?(e=s.msaaTexture.createView(),r=i):(e=i,r=void 0),l.push({view:e,resolveTarget:r,depthSlice:t})}}if(a={textureViews:l},e.depth){let t=this.get(e.depthTexture),r={};e.depthTexture.isDepthArrayTexture&&(r.dimension=p6.TwoD,r.arrayLayerCount=1,r.baseArrayLayer=e.activeCubeFace),a.depthStencilView=t.texture.createView(r)}s[n]=a,i.width=r.width,i.height=r.height,i.samples=r.samples,i.activeMipmapLevel=e.activeMipmapLevel,i.activeCubeFace=e.activeCubeFace,i.dimensions=r.dimensions}let o={colorAttachments:[]};for(let e=0;e<a.textureViews.length;e++){let r=a.textureViews[e],i={r:0,g:0,b:0,a:1};0===e&&t.clearValue&&(i=t.clearValue),o.colorAttachments.push({view:r.view,depthSlice:r.depthSlice,resolveTarget:r.resolveTarget,loadOp:t.loadOp||pH.Load,storeOp:t.storeOp||pz.Store,clearValue:i})}return a.depthStencilView&&(o.depthStencilAttachment={view:a.depthStencilView}),o}beginRender(e){let t,r,i=this.get(e),s=this.device,n=e.occlusionQueryCount;n>0&&(i.currentOcclusionQuerySet&&i.currentOcclusionQuerySet.destroy(),i.currentOcclusionQueryBuffer&&i.currentOcclusionQueryBuffer.destroy(),i.currentOcclusionQuerySet=i.occlusionQuerySet,i.currentOcclusionQueryBuffer=i.occlusionQueryBuffer,i.currentOcclusionQueryObjects=i.occlusionQueryObjects,i.occlusionQuerySet=t=s.createQuerySet({type:"occlusion",count:n,label:"occlusionQuerySet_".concat(e.id)}),i.occlusionQueryIndex=0,i.occlusionQueryObjects=Array(n),i.lastOcclusionObject=null),r=null===e.textures?this._getDefaultRenderPassDescriptor():this._getRenderPassDescriptor(e,{loadOp:pH.Load}),this.initTimestampQuery(e,r),r.occlusionQuerySet=t;let a=r.depthStencilAttachment;if(null!==e.textures){let t=r.colorAttachments;for(let r=0;r<t.length;r++){let i=t[r];e.clearColor?(i.clearValue=0===r?e.clearColorValue:{r:0,g:0,b:0,a:1},i.loadOp=pH.Clear):i.loadOp=pH.Load,i.storeOp=pz.Store}}else{let t=r.colorAttachments[0];e.clearColor?(t.clearValue=e.clearColorValue,t.loadOp=pH.Clear):t.loadOp=pH.Load,t.storeOp=pz.Store}e.depth&&(e.clearDepth?(a.depthClearValue=e.clearDepthValue,a.depthLoadOp=pH.Clear):a.depthLoadOp=pH.Load,a.depthStoreOp=pz.Store),e.stencil&&(e.clearStencil?(a.stencilClearValue=e.clearStencilValue,a.stencilLoadOp=pH.Clear):a.stencilLoadOp=pH.Load,a.stencilStoreOp=pz.Store);let o=s.createCommandEncoder({label:"renderContext_"+e.id});if(!0===this._isRenderCameraDepthArray(e)){let t=e.camera.cameras;i.layerDescriptors&&i.layerDescriptors.length===t.length?this._updateDepthLayerDescriptors(e,i,t):this._createDepthLayerDescriptors(e,i,r,t),i.bundleEncoders=[],i.bundleSets=[];for(let r=0;r<t.length;r++){let t=this.pipelineUtils.createBundleEncoder(e,"renderBundleArrayCamera_"+r),s={attributes:{},bindingGroups:[],pipeline:null,index:null};i.bundleEncoders.push(t),i.bundleSets.push(s)}i.currentPass=null}else{let t=o.beginRenderPass(r);if(i.currentPass=t,e.viewport&&this.updateViewport(e),e.scissor){let{x:r,y:i,width:s,height:n}=e.scissorValue;t.setScissorRect(r,i,s,n)}}i.descriptor=r,i.encoder=o,i.currentSets={attributes:{},bindingGroups:[],pipeline:null,index:null},i.renderBundles=[]}_createDepthLayerDescriptors(e,t,r,i){let s=r.depthStencilAttachment;t.layerDescriptors=[];let n=this.get(e.depthTexture);n.viewCache||(n.viewCache=[]);for(let a=0;a<i.length;a++){let i={...r,colorAttachments:[{...r.colorAttachments[0],view:r.colorAttachments[a].view}]};if(r.depthStencilAttachment){let t=a;n.viewCache[t]||(n.viewCache[t]=n.texture.createView({dimension:p6.TwoD,baseArrayLayer:a,arrayLayerCount:1})),i.depthStencilAttachment={view:n.viewCache[t],depthLoadOp:s.depthLoadOp||pH.Clear,depthStoreOp:s.depthStoreOp||pz.Store,depthClearValue:s.depthClearValue||1},e.stencil&&(i.depthStencilAttachment.stencilLoadOp=s.stencilLoadOp,i.depthStencilAttachment.stencilStoreOp=s.stencilStoreOp,i.depthStencilAttachment.stencilClearValue=s.stencilClearValue)}else i.depthStencilAttachment={...s};t.layerDescriptors.push(i)}}_updateDepthLayerDescriptors(e,t,r){for(let i=0;i<r.length;i++){let r=t.layerDescriptors[i];if(r.depthStencilAttachment){let t=r.depthStencilAttachment;e.depth&&(e.clearDepth?(t.depthClearValue=e.clearDepthValue,t.depthLoadOp=pH.Clear):t.depthLoadOp=pH.Load),e.stencil&&(e.clearStencil?(t.stencilClearValue=e.clearStencilValue,t.stencilLoadOp=pH.Clear):t.stencilLoadOp=pH.Load)}}}finishRender(e){let t=this.get(e),r=e.occlusionQueryCount;t.renderBundles.length>0&&t.currentPass.executeBundles(t.renderBundles),r>t.occlusionQueryIndex&&t.currentPass.endOcclusionQuery();let i=t.encoder;if(!0===this._isRenderCameraDepthArray(e)){let r=[];for(let e=0;e<t.bundleEncoders.length;e++){let i=t.bundleEncoders[e];r.push(i.finish())}for(let s=0;s<t.layerDescriptors.length;s++)if(s<r.length){let n=t.layerDescriptors[s],a=i.beginRenderPass(n);if(e.viewport){let{x:t,y:r,width:i,height:s,minDepth:n,maxDepth:o}=e.viewportValue;a.setViewport(t,r,i,s,n,o)}if(e.scissor){let{x:t,y:r,width:i,height:s}=e.scissorValue;a.setScissorRect(t,r,i,s)}a.executeBundles([r[s]]),a.end()}}else t.currentPass&&t.currentPass.end();if(r>0){let i=8*r,s=this.occludedResolveCache.get(i);void 0===s&&(s=this.device.createBuffer({size:i,usage:GPUBufferUsage.QUERY_RESOLVE|GPUBufferUsage.COPY_SRC}),this.occludedResolveCache.set(i,s));let n=this.device.createBuffer({size:i,usage:GPUBufferUsage.COPY_DST|GPUBufferUsage.MAP_READ});t.encoder.resolveQuerySet(t.occlusionQuerySet,0,r,s,0),t.encoder.copyBufferToBuffer(s,0,n,0,i),t.occlusionQueryBuffer=n,this.resolveOccludedAsync(e)}if(this.device.queue.submit([t.encoder.finish()]),null!==e.textures){let t=e.textures;for(let e=0;e<t.length;e++){let r=t[e];!0===r.generateMipmaps&&this.textureUtils.generateMipmaps(r)}}}isOccluded(e,t){let r=this.get(e);return r.occluded&&r.occluded.has(t)}async resolveOccludedAsync(e){let t=this.get(e),{currentOcclusionQueryBuffer:r,currentOcclusionQueryObjects:i}=t;if(r&&i){let e=new WeakSet;t.currentOcclusionQueryObjects=null,t.currentOcclusionQueryBuffer=null,await r.mapAsync(GPUMapMode.READ);let s=new BigUint64Array(r.getMappedRange());for(let t=0;t<i.length;t++)s[t]===BigInt(0)&&e.add(i[t]);r.destroy(),t.occluded=e}}updateViewport(e){let{currentPass:t}=this.get(e),{x:r,y:i,width:s,height:n,minDepth:a,maxDepth:o}=e.viewportValue;t.setViewport(r,i,s,n,a,o)}getClearColor(){let e=super.getClearColor();return!0===this.renderer.alpha&&(e.r*=e.a,e.g*=e.a,e.b*=e.a),e}clear(e,t,r){let i,s,n,a,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,l=this.device,u=this.renderer,d=[];if(e){let e=this.getClearColor();s={r:e.r,g:e.g,b:e.b,a:e.a}}if(null===o){n=u.depth,a=u.stencil;let t=this._getDefaultRenderPassDescriptor();if(e){let e=(d=t.colorAttachments)[0];e.clearValue=s,e.loadOp=pH.Clear,e.storeOp=pz.Store}(n||a)&&(i=t.depthStencilAttachment)}else{n=o.depth,a=o.stencil;let l={loadOp:e?pH.Clear:pH.Load,clearValue:e?s:void 0};n&&(l.depthLoadOp=t?pH.Clear:pH.Load,l.depthClearValue=t?u.getClearDepth():void 0,l.depthStoreOp=pz.Store),a&&(l.stencilLoadOp=r?pH.Clear:pH.Load,l.stencilClearValue=r?u.getClearStencil():void 0,l.stencilStoreOp=pz.Store);let h=this._getRenderPassDescriptor(o,l);d=h.colorAttachments,i=h.depthStencilAttachment}n&&i&&void 0===i.depthLoadOp&&(t?(i.depthLoadOp=pH.Clear,i.depthClearValue=u.getClearDepth()):i.depthLoadOp=pH.Load,i.depthStoreOp=pz.Store),a&&i&&void 0===i.stencilLoadOp&&(r?(i.stencilLoadOp=pH.Clear,i.stencilClearValue=u.getClearStencil()):i.stencilLoadOp=pH.Load,i.stencilStoreOp=pz.Store);let h=l.createCommandEncoder({label:"clear"});h.beginRenderPass({colorAttachments:d,depthStencilAttachment:i}).end(),l.queue.submit([h.finish()])}beginCompute(e){let t=this.get(e),r={label:"computeGroup_"+e.id};this.initTimestampQuery(e,r),t.cmdEncoderGPU=this.device.createCommandEncoder({label:"computeGroup_"+e.id}),t.passEncoderGPU=t.cmdEncoderGPU.beginComputePass(r)}compute(e,t,r,i){let{passEncoderGPU:s}=this.get(e),n=this.get(i).pipeline;s.setPipeline(n);for(let e=0,t=r.length;e<t;e++){let t=r[e],i=this.get(t);s.setBindGroup(e,i.group)}let a=this.device.limits.maxComputeWorkgroupsPerDimension,o=this.get(t);void 0===o.dispatchSize&&(o.dispatchSize={x:0,y:1,z:1});let{dispatchSize:l}=o;t.dispatchCount>a?(l.x=Math.min(t.dispatchCount,a),l.y=Math.ceil(t.dispatchCount/a)):l.x=t.dispatchCount,s.dispatchWorkgroups(l.x,l.y,l.z)}finishCompute(e){let t=this.get(e);t.passEncoderGPU.end(),this.device.queue.submit([t.cmdEncoderGPU.finish()])}async waitForGPU(){await this.device.queue.onSubmittedWorkDone()}draw(e,t){let{object:r,material:i,context:s,pipeline:n}=e,a=e.getBindings(),o=this.get(s),l=this.get(n).pipeline,d=e.getIndex(),h=null!==d,c=e.getDrawParameters();if(null===c)return;let p=(t,r)=>{t.setPipeline(l),r.pipeline=l;let n=r.bindingGroups;for(let e=0,r=a.length;e<r;e++){let r=a[e],i=this.get(r);n[r.index]!==r.id&&(t.setBindGroup(r.index,i.group),n[r.index]=r.id)}if(!0===h&&r.index!==d){let e=this.get(d).buffer,i=d.array instanceof Uint16Array?pj.Uint16:pj.Uint32;t.setIndexBuffer(e,i),r.index=d}let u=e.getVertexBuffers();for(let e=0,i=u.length;e<i;e++){let i=u[e];if(r.attributes[e]!==i){let s=this.get(i).buffer;t.setVertexBuffer(e,s),r.attributes[e]=i}}!0===s.stencil&&!0===i.stencilWrite&&o.currentStencilRef!==i.stencilRef&&(t.setStencilReference(i.stencilRef),o.currentStencilRef=i.stencilRef)},g=(i,s)=>{if(p(i,s),!0===r.isBatchedMesh){let e=r._multiDrawStarts,s=r._multiDrawCounts,n=r._multiDrawCount,a=r._multiDrawInstances;null!==a&&(0,u.mcG)("THREE.WebGPUBackend: renderMultiDrawInstances has been deprecated and will be removed in r184. Append to renderMultiDraw arguments and use indirection.");for(let o=0;o<n;o++){let n=a?a[o]:1,l=n>1?0:o;!0===h?i.drawIndexed(s[o],n,e[o]/d.array.BYTES_PER_ELEMENT,0,l):i.draw(s[o],n,e[o],l),t.update(r,s[o],n)}}else if(!0===h){let{vertexCount:s,instanceCount:n,firstVertex:a}=c,o=e.getIndirect();if(null!==o){let e=this.get(o).buffer;i.drawIndexedIndirect(e,0)}else i.drawIndexed(s,n,a,0,0);t.update(r,s,n)}else{let{vertexCount:s,instanceCount:n,firstVertex:a}=c,o=e.getIndirect();if(null!==o){let e=this.get(o).buffer;i.drawIndirect(e,0)}else i.draw(s,n,a,0);t.update(r,s,n)}};if(e.camera.isArrayCamera&&e.camera.cameras.length>0){let t=this.get(e.camera),i=e.camera.cameras,n=e.getBindingGroup("cameraIndex");if(void 0===t.indexesGPU||t.indexesGPU.length!==i.length){let e=this.get(n),r=[],s=new Uint32Array([0,0,0,0]);for(let t=0,n=i.length;t<n;t++){s[0]=t;let i=this.bindingUtils.createBindGroupIndex(s,e.layout);r.push(i)}t.indexesGPU=r}let a=this.renderer.getPixelRatio();for(let e=0,l=i.length;e<l;e++){let l=i[e];if(r.layers.test(l.layers)){let r=l.viewport,i=o.currentPass,u=o.currentSets;if(o.bundleEncoders){let t=o.bundleEncoders[e],r=o.bundleSets[e];i=t,u=r}r&&i.setViewport(Math.floor(r.x*a),Math.floor(r.y*a),Math.floor(r.width*a),Math.floor(r.height*a),s.viewportValue.minDepth,s.viewportValue.maxDepth),n&&t.indexesGPU&&(i.setBindGroup(n.index,t.indexesGPU[e]),u.bindingGroups[n.index]=n.id),g(i,u)}}}else if(o.currentPass){if(void 0!==o.occlusionQuerySet){let e=o.lastOcclusionObject;e!==r&&(null!==e&&!0===e.occlusionTest&&(o.currentPass.endOcclusionQuery(),o.occlusionQueryIndex++),!0===r.occlusionTest&&(o.currentPass.beginOcclusionQuery(o.occlusionQueryIndex),o.occlusionQueryObjects[o.occlusionQueryIndex]=r),o.lastOcclusionObject=r)}g(o.currentPass,o.currentSets)}}needsRenderUpdate(e){let t=this.get(e),{object:r,material:i}=e,s=this.utils,n=s.getSampleCountRenderContext(e.context),a=s.getCurrentColorSpace(e.context),o=s.getCurrentColorFormat(e.context),l=s.getCurrentDepthStencilFormat(e.context),u=s.getPrimitiveTopology(r,i),d=!1;return(t.material!==i||t.materialVersion!==i.version||t.transparent!==i.transparent||t.blending!==i.blending||t.premultipliedAlpha!==i.premultipliedAlpha||t.blendSrc!==i.blendSrc||t.blendDst!==i.blendDst||t.blendEquation!==i.blendEquation||t.blendSrcAlpha!==i.blendSrcAlpha||t.blendDstAlpha!==i.blendDstAlpha||t.blendEquationAlpha!==i.blendEquationAlpha||t.colorWrite!==i.colorWrite||t.depthWrite!==i.depthWrite||t.depthTest!==i.depthTest||t.depthFunc!==i.depthFunc||t.stencilWrite!==i.stencilWrite||t.stencilFunc!==i.stencilFunc||t.stencilFail!==i.stencilFail||t.stencilZFail!==i.stencilZFail||t.stencilZPass!==i.stencilZPass||t.stencilFuncMask!==i.stencilFuncMask||t.stencilWriteMask!==i.stencilWriteMask||t.side!==i.side||t.alphaToCoverage!==i.alphaToCoverage||t.sampleCount!==n||t.colorSpace!==a||t.colorFormat!==o||t.depthStencilFormat!==l||t.primitiveTopology!==u||t.clippingContextCacheKey!==e.clippingContextCacheKey)&&(t.material=i,t.materialVersion=i.version,t.transparent=i.transparent,t.blending=i.blending,t.premultipliedAlpha=i.premultipliedAlpha,t.blendSrc=i.blendSrc,t.blendDst=i.blendDst,t.blendEquation=i.blendEquation,t.blendSrcAlpha=i.blendSrcAlpha,t.blendDstAlpha=i.blendDstAlpha,t.blendEquationAlpha=i.blendEquationAlpha,t.colorWrite=i.colorWrite,t.depthWrite=i.depthWrite,t.depthTest=i.depthTest,t.depthFunc=i.depthFunc,t.stencilWrite=i.stencilWrite,t.stencilFunc=i.stencilFunc,t.stencilFail=i.stencilFail,t.stencilZFail=i.stencilZFail,t.stencilZPass=i.stencilZPass,t.stencilFuncMask=i.stencilFuncMask,t.stencilWriteMask=i.stencilWriteMask,t.side=i.side,t.alphaToCoverage=i.alphaToCoverage,t.sampleCount=n,t.colorSpace=a,t.colorFormat=o,t.depthStencilFormat=l,t.primitiveTopology=u,t.clippingContextCacheKey=e.clippingContextCacheKey,d=!0),d}getRenderCacheKey(e){let{object:t,material:r}=e,i=this.utils,s=e.context;return[r.transparent,r.blending,r.premultipliedAlpha,r.blendSrc,r.blendDst,r.blendEquation,r.blendSrcAlpha,r.blendDstAlpha,r.blendEquationAlpha,r.colorWrite,r.depthWrite,r.depthTest,r.depthFunc,r.stencilWrite,r.stencilFunc,r.stencilFail,r.stencilZFail,r.stencilZPass,r.stencilFuncMask,r.stencilWriteMask,r.side,i.getSampleCountRenderContext(s),i.getCurrentColorSpace(s),i.getCurrentColorFormat(s),i.getCurrentDepthStencilFormat(s),i.getPrimitiveTopology(t,r),e.getGeometryCacheKey(),e.clippingContextCacheKey].join()}createSampler(e){this.textureUtils.createSampler(e)}destroySampler(e){this.textureUtils.destroySampler(e)}createDefaultTexture(e){this.textureUtils.createDefaultTexture(e)}createTexture(e,t){this.textureUtils.createTexture(e,t)}updateTexture(e,t){this.textureUtils.updateTexture(e,t)}generateMipmaps(e){this.textureUtils.generateMipmaps(e)}destroyTexture(e){this.textureUtils.destroyTexture(e)}async copyTextureToBuffer(e,t,r,i,s,n){return this.textureUtils.copyTextureToBuffer(e,t,r,i,s,n)}initTimestampQuery(e,t){if(!this.trackTimestamp)return;let r=e.isComputeNode?"compute":"render";this.timestampQueryPool[r]||(this.timestampQueryPool[r]=new gU(this.device,r,2048));let i=this.timestampQueryPool[r],s=i.allocateQueriesForContext(e);t.timestampWrites={querySet:i.querySet,beginningOfPassWriteIndex:s,endOfPassWriteIndex:s+1}}createNodeBuilder(e,t){return new gA(e,t)}createProgram(e){this.get(e).module={module:this.device.createShaderModule({code:e.code,label:e.stage+(""!==e.name?"_".concat(e.name):"")}),entryPoint:"main"}}destroyProgram(e){this.delete(e)}createRenderPipeline(e,t){this.pipelineUtils.createRenderPipeline(e,t)}createComputePipeline(e,t){this.pipelineUtils.createComputePipeline(e,t)}beginBundle(e){let t=this.get(e);t._currentPass=t.currentPass,t._currentSets=t.currentSets,t.currentSets={attributes:{},bindingGroups:[],pipeline:null,index:null},t.currentPass=this.pipelineUtils.createBundleEncoder(e)}finishBundle(e,t){let r=this.get(e),i=r.currentPass.finish();this.get(t).bundleGPU=i,r.currentSets=r._currentSets,r.currentPass=r._currentPass}addBundle(e,t){this.get(e).renderBundles.push(this.get(t).bundleGPU)}createBindings(e,t,r,i){this.bindingUtils.createBindings(e,t,r,i)}updateBindings(e,t,r,i){this.bindingUtils.createBindings(e,t,r,i)}updateBinding(e){this.bindingUtils.updateBinding(e)}createIndexAttribute(e){this.attributeUtils.createAttribute(e,GPUBufferUsage.INDEX|GPUBufferUsage.COPY_SRC|GPUBufferUsage.COPY_DST)}createAttribute(e){this.attributeUtils.createAttribute(e,GPUBufferUsage.VERTEX|GPUBufferUsage.COPY_SRC|GPUBufferUsage.COPY_DST)}createStorageAttribute(e){this.attributeUtils.createAttribute(e,GPUBufferUsage.STORAGE|GPUBufferUsage.VERTEX|GPUBufferUsage.COPY_SRC|GPUBufferUsage.COPY_DST)}createIndirectStorageAttribute(e){this.attributeUtils.createAttribute(e,GPUBufferUsage.STORAGE|GPUBufferUsage.INDIRECT|GPUBufferUsage.COPY_SRC|GPUBufferUsage.COPY_DST)}updateAttribute(e){this.attributeUtils.updateAttribute(e)}destroyAttribute(e){this.attributeUtils.destroyAttribute(e)}updateSize(){this.colorBuffer=this.textureUtils.getColorBuffer(),this.defaultRenderPassdescriptor=null}getMaxAnisotropy(){return 16}hasFeature(e){return this.device.features.has(e)}copyTextureToTexture(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,n=arguments.length>5&&void 0!==arguments[5]?arguments[5]:0,a=0,o=0,l=0,u=0,d=0,h=0,c=e.image.width,p=e.image.height,g=1;null!==r&&(!0===r.isBox3?(u=r.min.x,d=r.min.y,h=r.min.z,c=r.max.x-r.min.x,p=r.max.y-r.min.y,g=r.max.z-r.min.z):(u=r.min.x,d=r.min.y,c=r.max.x-r.min.x,p=r.max.y-r.min.y,g=1)),null!==i&&(a=i.x,o=i.y,l=i.z||0);let m=this.device.createCommandEncoder({label:"copyTextureToTexture_"+e.id+"_"+t.id}),f=this.get(e).texture,y=this.get(t).texture;m.copyTextureToTexture({texture:f,mipLevel:s,origin:{x:u,y:d,z:h}},{texture:y,mipLevel:n,origin:{x:a,y:o,z:l}},[c,p,g]),this.device.queue.submit([m.finish()]),0===n&&t.generateMipmaps&&this.textureUtils.generateMipmaps(t)}copyFramebufferToTexture(e,t,r){let i,s=this.get(t),n=null;n=t.renderTarget?e.isDepthTexture?this.get(t.depthTexture).texture:this.get(t.textures[0]).texture:e.isDepthTexture?this.textureUtils.getDepthBuffer(t.depth,t.stencil):this.context.getCurrentTexture();let a=this.get(e).texture;if(n.format!==a.format)return void console.error("WebGPUBackend: copyFramebufferToTexture: Source and destination formats do not match.",n.format,a.format);if(s.currentPass?(s.currentPass.end(),i=s.encoder):i=this.device.createCommandEncoder({label:"copyFramebufferToTexture_"+e.id}),i.copyTextureToTexture({texture:n,origin:[r.x,r.y,0]},{texture:a},[r.z,r.w]),e.generateMipmaps&&this.textureUtils.generateMipmaps(e),s.currentPass){let{descriptor:e}=s;for(let t=0;t<e.colorAttachments.length;t++)e.colorAttachments[t].loadOp=pH.Load;if(t.depth&&(e.depthStencilAttachment.depthLoadOp=pH.Load),t.stencil&&(e.depthStencilAttachment.stencilLoadOp=pH.Load),s.currentPass=i.beginRenderPass(e),s.currentSets={attributes:{},bindingGroups:[],pipeline:null,index:null},t.viewport&&this.updateViewport(t),t.scissor){let{x:e,y:r,width:i,height:n}=t.scissorValue;s.currentPass.setScissorRect(e,r,i,n)}}else this.device.queue.submit([i.finish()])}constructor(e={}){super(e),this.isWebGPUBackend=!0,this.parameters.alpha=void 0===e.alpha||e.alpha,this.parameters.compatibilityMode=void 0!==e.compatibilityMode&&e.compatibilityMode,this.parameters.requiredLimits=void 0===e.requiredLimits?{}:e.requiredLimits,this.compatibilityMode=this.parameters.compatibilityMode,this.device=null,this.context=null,this.colorBuffer=null,this.defaultRenderPassdescriptor=null,this.utils=new gE(this),this.attributeUtils=new gB(this),this.bindingUtils=new gP(this),this.pipelineUtils=new gF(this),this.textureUtils=new go(this),this.occludedResolveCache=new Map}}class gI extends u.nCl{copy(e,t){return super.copy(e,t),this.iesMap=e.iesMap,this}constructor(e,t,r,i,s,n){super(e,t,r,i,s,n),this.iesMap=null}}class gD extends cz{constructor(){super(),this.addMaterial(aW,"MeshPhongMaterial"),this.addMaterial(lh,"MeshStandardMaterial"),this.addMaterial(lp,"MeshPhysicalMaterial"),this.addMaterial(ly,"MeshToonMaterial"),this.addMaterial(aU,"MeshBasicMaterial"),this.addMaterial(az,"MeshLambertMaterial"),this.addMaterial(aT,"MeshNormalMaterial"),this.addMaterial(lv,"MeshMatcapMaterial"),this.addMaterial(ap,"LineBasicMaterial"),this.addMaterial(am,"LineDashedMaterial"),this.addMaterial(lE,"PointsMaterial"),this.addMaterial(lR,"SpriteMaterial"),this.addMaterial(lM,"ShadowMaterial"),this.addLight(hn,u.HiM),this.addLight(cm,u.ZyN),this.addLight(cb,u.ure),this.addLight(cT,u.nCl),this.addLight(c_,u.$p8),this.addLight(cN,u.dth),this.addLight(cS,u.FZo),this.addLight(cv,gI),this.addToneMapping(u8,u.kyO),this.addToneMapping(u5,u.Mjd),this.addToneMapping(u9,u.nNL),this.addToneMapping(de,u.FV),this.addToneMapping(ds,u.LAk),this.addToneMapping(dn,u.aJ8)}}class gO extends c7{constructor(e={}){let t;e.forceWebGL?t=pV:(t=gL,e.getFallback=()=>(console.warn("THREE.WebGPURenderer: WebGPU is not available, running under WebGL2 backend."),new pV(e))),super(new t(e),e),this.library=new gD,this.isWebGPURenderer=!0,"undefined"!=typeof __THREE_DEVTOOLS__&&__THREE_DEVTOOLS__.dispatchEvent(new CustomEvent("observe",{detail:this}))}}u.YJl,u.gPd,u.aHM,u.jut,u.XTe,u.YJl}}]);