(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[309],{6234:(e,s,a)=>{"use strict";a.d(s,{A:()=>d});var i=a(7876),t=a(8230),n=a.n(t),l=a(9099),r=a(4232);function c(){let e=(0,l.useRouter)(),[s,a]=(0,r.useState)(!1),t=[{name:"Dashboard",path:"/",icon:"\uD83C\uDFE0"},{name:"Authority Matches",path:"/matches",icon:"\uD83C\uDFAF"},{name:"Job Seekers",path:"/job-seekers",icon:"\uD83D\uDC65"},{name:"Hiring Authorities",path:"/hiring-authorities",icon:"\uD83D\uDC54"},{name:"Companies",path:"/companies",icon:"\uD83C\uDFE2"},{name:"Positions",path:"/positions",icon:"\uD83D\uDCCB"},{name:"Skills",path:"/skills",icon:"\uD83D\uDEE0️"},{name:"Network View",path:"/global-view",icon:"\uD83C\uDF10"}];return(0,i.jsx)("nav",{className:"bg-white shadow-soft border-b border-candid-gray-200",children:(0,i.jsxs)("div",{className:"container-app",children:[(0,i.jsxs)("div",{className:"flex justify-between items-center h-20",children:[(0,i.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,i.jsxs)(n(),{href:"/",className:"flex items-center space-x-3 group",children:[(0,i.jsx)("div",{className:"w-12 h-12 relative group-hover:scale-105 transition-transform duration-200",children:(0,i.jsxs)("svg",{viewBox:"0 0 48 48",className:"w-full h-full",children:[(0,i.jsx)("circle",{cx:"24",cy:"24",r:"22",fill:"none",stroke:"#1e3a8a",strokeWidth:"2"}),(0,i.jsx)("circle",{cx:"24",cy:"12",r:"3",fill:"#00d4ff"}),(0,i.jsx)("circle",{cx:"36",cy:"24",r:"3",fill:"#00d4ff"}),(0,i.jsx)("circle",{cx:"24",cy:"36",r:"3",fill:"#00d4ff"}),(0,i.jsx)("circle",{cx:"12",cy:"24",r:"3",fill:"#00d4ff"}),(0,i.jsx)("circle",{cx:"24",cy:"24",r:"4",fill:"#1e3a8a"}),(0,i.jsx)("line",{x1:"24",y1:"15",x2:"24",y2:"20",stroke:"#00d4ff",strokeWidth:"2"}),(0,i.jsx)("line",{x1:"33",y1:"24",x2:"28",y2:"24",stroke:"#00d4ff",strokeWidth:"2"}),(0,i.jsx)("line",{x1:"24",y1:"33",x2:"24",y2:"28",stroke:"#00d4ff",strokeWidth:"2"}),(0,i.jsx)("line",{x1:"15",y1:"24",x2:"20",y2:"24",stroke:"#00d4ff",strokeWidth:"2"})]})}),(0,i.jsxs)("div",{className:"hidden sm:block",children:[(0,i.jsx)("h1",{className:"text-xl font-bold text-secondary-800 group-hover:text-primary-500 transition-colors duration-200",children:"Candid Connections"}),(0,i.jsx)("p",{className:"text-sm text-candid-gray-600 -mt-1",children:"Katra Platform"})]})]})}),(0,i.jsx)("div",{className:"hidden lg:block",children:(0,i.jsx)("div",{className:"flex items-center space-x-1",children:t.map(s=>(0,i.jsxs)(n(),{href:s.path,className:"flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ".concat(e.pathname===s.path?"nav-link-active":"nav-link"),children:[(0,i.jsx)("span",{className:"text-base",children:s.icon}),(0,i.jsx)("span",{children:s.name})]},s.path))})}),(0,i.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[(0,i.jsx)(n(),{href:"/admin",className:"btn-outline text-sm py-2 px-4",children:"⚙️ Admin"}),(0,i.jsx)(n(),{href:"/global-view",className:"btn-outline text-sm py-2 px-4",children:"\uD83C\uDF10 Network View"}),(0,i.jsx)(n(),{href:"https://portal.candid-connections.com/user/login",target:"_blank",rel:"noopener noreferrer",className:"btn-primary text-sm py-2 px-4",children:"Portal Login"})]}),(0,i.jsx)("div",{className:"lg:hidden",children:(0,i.jsx)("button",{onClick:()=>a(!s),className:"p-2 rounded-lg text-candid-navy-600 hover:text-primary-600 hover:bg-primary-50 transition-colors duration-200","aria-label":"Toggle mobile menu",children:(0,i.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s?(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})})]}),s&&(0,i.jsx)("div",{className:"lg:hidden border-t border-candid-gray-200 py-4",children:(0,i.jsxs)("div",{className:"space-y-2",children:[t.map(s=>(0,i.jsxs)(n(),{href:s.path,onClick:()=>a(!1),className:"flex items-center space-x-3 px-4 py-3 rounded-lg text-base font-medium transition-all duration-200 ".concat(e.pathname===s.path?"nav-link-active":"nav-link"),children:[(0,i.jsx)("span",{className:"text-lg",children:s.icon}),(0,i.jsx)("span",{children:s.name})]},s.path)),(0,i.jsxs)("div",{className:"pt-4 space-y-2",children:[(0,i.jsx)(n(),{href:"/global-view",onClick:()=>a(!1),className:"block w-full btn-outline text-center",children:"\uD83C\uDF10 Network View"}),(0,i.jsx)(n(),{href:"https://portal.candid-connections.com/user/login",target:"_blank",rel:"noopener noreferrer",className:"block w-full btn-primary text-center",children:"Portal Login"})]})]})})]})})}function d(e){let{children:s}=e;return(0,i.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,i.jsx)(c,{}),(0,i.jsx)("main",{className:"container-app section-padding",children:s})]})}},7808:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>c});var i=a(7876),t=a(4232),n=a(7328),l=a.n(n),r=a(6234);function c(){let[e,s]=(0,t.useState)(!1),[a,n]=(0,t.useState)(null),[c,d]=(0,t.useState)(null),o=async()=>{s(!0),n(null),d(null);try{let e=await fetch("/api/seed-database",{method:"POST",headers:{"Content-Type":"application/json"}}),s=await e.json();e.ok?n(s):d(s.error||"Failed to seed database")}catch(e){d("Network error: "+e.message)}finally{s(!1)}};return(0,i.jsxs)(r.A,{children:[(0,i.jsxs)(l(),{children:[(0,i.jsx)("title",{children:"Admin Panel | Candid Connections Katra"}),(0,i.jsx)("meta",{name:"description",content:"Administrative tools for managing the Candid Connections platform."})]}),(0,i.jsxs)("div",{className:"space-y-8",children:[(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("h1",{className:"text-4xl font-bold text-secondary-800 mb-4",children:"Admin Panel"}),(0,i.jsx)("p",{className:"text-xl text-candid-gray-600 max-w-3xl mx-auto",children:"Administrative tools for managing the platform and database."})]}),(0,i.jsx)("div",{className:"card",children:(0,i.jsxs)("div",{className:"card-body",children:[(0,i.jsx)("h2",{className:"text-2xl font-semibold text-secondary-800 mb-4",children:"Database Management"}),(0,i.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6",children:[(0,i.jsx)("h3",{className:"font-semibold text-yellow-800 mb-2",children:"⚠️ Warning"}),(0,i.jsx)("p",{className:"text-yellow-700 text-sm",children:"This will completely wipe the existing database and populate it with fresh mock data. This action cannot be undone."})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-secondary-700",children:"Seed Database with Mock Data"}),(0,i.jsx)("p",{className:"text-candid-gray-600",children:"This will create a comprehensive dataset for testing the hiring authority matching system:"}),(0,i.jsxs)("ul",{className:"list-disc list-inside text-candid-gray-600 space-y-1 ml-4",children:[(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"5 Companies"})," - Different sizes (Startup, Mid-size, Enterprise)"]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"8 Hiring Authorities"})," - Various levels (C-Suite, Executive, Director, Manager)"]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"20 Job Seekers"})," - Diverse skills and experience levels"]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"20 Skills"})," - Technical and soft skills with demand levels"]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"Authority Matches"})," - Generated using company size logic and skill alignment"]})]}),(0,i.jsx)("button",{onClick:o,disabled:e,className:"btn-primary ".concat(e?"opacity-50 cursor-not-allowed":""),children:e?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:"loading-spinner w-4 h-4 mr-2"}),"Seeding Database..."]}):"\uD83C\uDF31 Seed Database"})]}),a&&(0,i.jsxs)("div",{className:"mt-6 bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,i.jsx)("h4",{className:"font-semibold text-green-800 mb-2",children:"✅ Success!"}),(0,i.jsx)("p",{className:"text-green-700 mb-3",children:a.message}),(0,i.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4 text-sm",children:[(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-green-600",children:a.stats.companies}),(0,i.jsx)("div",{className:"text-green-700",children:"Companies"})]}),(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-green-600",children:a.stats.hiringAuthorities}),(0,i.jsx)("div",{className:"text-green-700",children:"Authorities"})]}),(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-green-600",children:a.stats.jobSeekers}),(0,i.jsx)("div",{className:"text-green-700",children:"Job Seekers"})]}),(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-green-600",children:a.stats.skills}),(0,i.jsx)("div",{className:"text-green-700",children:"Skills"})]}),(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-green-600",children:a.stats.matches}),(0,i.jsx)("div",{className:"text-green-700",children:"Matches"})]})]})]}),c&&(0,i.jsxs)("div",{className:"mt-6 bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,i.jsx)("h4",{className:"font-semibold text-red-800 mb-2",children:"❌ Error"}),(0,i.jsx)("p",{className:"text-red-700",children:c})]})]})}),(0,i.jsx)("div",{className:"card",children:(0,i.jsxs)("div",{className:"card-body",children:[(0,i.jsx)("h2",{className:"text-2xl font-semibold text-secondary-800 mb-4",children:"Mock Data Overview"}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-secondary-700 mb-3",children:"Company Size Logic"}),(0,i.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{className:"font-medium",children:"Startup (<100):"}),(0,i.jsx)("span",{className:"text-candid-gray-600",children:"CEO, CTO → Ultimate Power"})]}),(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{className:"font-medium",children:"Mid-size (100-1000):"}),(0,i.jsx)("span",{className:"text-candid-gray-600",children:"VP, Directors → High Power"})]}),(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{className:"font-medium",children:"Enterprise (1000+):"}),(0,i.jsx)("span",{className:"text-candid-gray-600",children:"HR, Managers → Medium Power"})]})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-secondary-700 mb-3",children:"Authority Hierarchy"}),(0,i.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{className:"font-medium",children:"C-Suite:"}),(0,i.jsx)("span",{className:"text-candid-gray-600",children:"CEO, CTO, Founder"})]}),(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{className:"font-medium",children:"Executive:"}),(0,i.jsx)("span",{className:"text-candid-gray-600",children:"VP, Creative Director"})]}),(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{className:"font-medium",children:"Director:"}),(0,i.jsx)("span",{className:"text-candid-gray-600",children:"HR Director, Product Director"})]}),(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{className:"font-medium",children:"Manager:"}),(0,i.jsx)("span",{className:"text-candid-gray-600",children:"Engineering Manager"})]})]})]})]})]})}),(0,i.jsx)("div",{className:"card",children:(0,i.jsxs)("div",{className:"card-body",children:[(0,i.jsx)("h2",{className:"text-2xl font-semibold text-secondary-800 mb-4",children:"Quick Links"}),(0,i.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,i.jsx)("a",{href:"/job-seekers",className:"btn-outline text-center",children:"\uD83D\uDC65 Job Seekers"}),(0,i.jsx)("a",{href:"/hiring-authorities",className:"btn-outline text-center",children:"\uD83D\uDC54 Hiring Authorities"}),(0,i.jsx)("a",{href:"/companies",className:"btn-outline text-center",children:"\uD83C\uDFE2 Companies"}),(0,i.jsx)("a",{href:"/matches",className:"btn-outline text-center",children:"\uD83C\uDFAF Authority Matches"})]})]})})]})]})}},8210:(e,s,a)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin",function(){return a(7808)}])}},e=>{var s=s=>e(e.s=s);e.O(0,[305,636,593,792],()=>s(8210)),_N_E=e.O()}]);