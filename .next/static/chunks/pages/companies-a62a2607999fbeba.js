(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[29],{794:(e,s,t)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/companies",function(){return t(1032)}])},1032:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c});var i=t(7876),n=t(4232),a=t(7328),r=t.n(a),o=t(6234),l=t(1566),d=t(8512);function c(){let[e,s]=(0,n.useState)([]),[t,a]=(0,n.useState)(!0),[c,m]=(0,n.useState)(null),[x,u]=(0,n.useState)(!1),[p,h]=(0,n.useState)(null),[g,y]=(0,n.useState)(""),[j,N]=(0,n.useState)("name");(0,n.useEffect)(()=>{(async()=>{try{setTimeout(()=>{s([{id:"company_1",name:"TechCorp Inc.",industry:"Technology",size:"Large (1000+ employees)",location:"San Francisco, CA",description:"Leading technology company specializing in cloud solutions and enterprise software.",openPositions:12,hiringAuthorities:[{name:"Sarah Wilson",role:"VP Engineering"},{name:"Mike Chen",role:"Director of Product"}],founded:"2010",website:"https://techcorp.com",logo:"\uD83C\uDFE2"},{id:"company_2",name:"DataFlow Systems",industry:"Data Analytics",size:"Medium (100-999 employees)",location:"Austin, TX",description:"Data analytics platform helping businesses make data-driven decisions.",openPositions:8,hiringAuthorities:[{name:"Jennifer Rodriguez",role:"Head of Engineering"},{name:"David Park",role:"CTO"}],founded:"2015",website:"https://dataflow.com",logo:"\uD83D\uDCCA"},{id:"company_3",name:"Design Studio Pro",industry:"Design & Creative",size:"Small (10-99 employees)",location:"New York, NY",description:"Creative design studio specializing in user experience and brand design.",openPositions:5,hiringAuthorities:[{name:"Alex Thompson",role:"Creative Director"},{name:"Maria Garcia",role:"Design Lead"}],founded:"2018",website:"https://designstudiopro.com",logo:"\uD83C\uDFA8"},{id:"company_4",name:"CloudTech Solutions",industry:"Cloud Infrastructure",size:"Medium (100-999 employees)",location:"Seattle, WA",description:"Cloud infrastructure and DevOps solutions for modern enterprises.",openPositions:15,hiringAuthorities:[{name:"Robert Kim",role:"VP of Engineering"},{name:"Lisa Chang",role:"Director of Operations"}],founded:"2012",website:"https://cloudtech.com",logo:"☁️"}]),h((0,d._)()),a(!1)},1e3)}catch(e){m(e.message),a(!1)}})()},[]);let f=[...e.filter(e=>e.name.toLowerCase().includes(g.toLowerCase())||e.industry.toLowerCase().includes(g.toLowerCase())||e.location.toLowerCase().includes(g.toLowerCase()))].sort((e,s)=>{switch(j){case"name":return e.name.localeCompare(s.name);case"positions":return s.openPositions-e.openPositions;case"size":return e.size.localeCompare(s.size);case"industry":return e.industry.localeCompare(s.industry);default:return 0}}),b=e=>e.includes("Large")?"bg-purple-100 text-purple-800":e.includes("Medium")?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800";return t?(0,i.jsx)(o.A,{children:(0,i.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,i.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,i.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-teal-500"})})})}):c?(0,i.jsx)(o.A,{children:(0,i.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,i.jsxs)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded",children:["Error: ",c]})})}):(0,i.jsxs)(o.A,{children:[(0,i.jsx)(r(),{children:(0,i.jsx)("title",{children:"Companies | Candid Connections Katra"})}),(0,i.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,i.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,i.jsx)("h1",{className:"text-2xl font-bold",children:"Companies"}),(0,i.jsx)("button",{onClick:()=>u(!0),className:"bg-teal-600 text-white px-4 py-2 rounded-lg hover:bg-teal-700 transition-colors",children:"\uD83C\uDF10 View Network"})]}),(0,i.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-4 mb-6",children:(0,i.jsxs)("div",{className:"flex flex-wrap gap-4 items-center",children:[(0,i.jsx)("div",{className:"flex-1 min-w-64",children:(0,i.jsx)("input",{type:"text",placeholder:"Search companies, industries, or locations...",value:g,onChange:e=>y(e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"})}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Sort by:"}),(0,i.jsxs)("select",{value:j,onChange:e=>N(e.target.value),className:"border border-gray-300 rounded-md px-3 py-1 text-sm",children:[(0,i.jsx)("option",{value:"name",children:"Company Name"}),(0,i.jsx)("option",{value:"positions",children:"Open Positions"}),(0,i.jsx)("option",{value:"size",children:"Company Size"}),(0,i.jsx)("option",{value:"industry",children:"Industry"})]})]}),(0,i.jsxs)("div",{className:"text-sm text-gray-600",children:[f.length," companies found"]})]})}),(0,i.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:f.map(e=>(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,i.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("span",{className:"text-3xl mr-3",children:e.logo}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"font-semibold text-lg text-gray-900",children:e.name}),(0,i.jsx)("p",{className:"text-gray-600 text-sm",children:e.industry})]})]}),(0,i.jsx)("div",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ".concat(b(e.size)),children:e.size})]}),(0,i.jsxs)("div",{className:"space-y-3 mb-4",children:[(0,i.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,i.jsx)("span",{className:"mr-2",children:"\uD83D\uDCCD"}),e.location]}),(0,i.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,i.jsx)("span",{className:"mr-2",children:"\uD83D\uDCC5"}),"Founded ",e.founded]}),(0,i.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,i.jsx)("span",{className:"mr-2",children:"\uD83D\uDCBC"}),e.openPositions," open positions"]})]}),(0,i.jsx)("p",{className:"text-gray-700 text-sm mb-4 line-clamp-3",children:e.description}),(0,i.jsxs)("div",{className:"mb-4",children:[(0,i.jsx)("h5",{className:"text-sm font-medium text-gray-700 mb-2",children:"Key Contacts:"}),(0,i.jsx)("div",{className:"space-y-1",children:e.hiringAuthorities.map((e,s)=>(0,i.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,i.jsx)("span",{className:"mr-2",children:"\uD83D\uDC64"}),(0,i.jsx)("span",{className:"font-medium",children:e.name}),(0,i.jsx)("span",{className:"mx-2",children:"•"}),(0,i.jsx)("span",{children:e.role})]},s))})]}),(0,i.jsxs)("div",{className:"flex justify-between items-center pt-4 border-t border-gray-100",children:[(0,i.jsx)("a",{href:e.website,target:"_blank",rel:"noopener noreferrer",className:"text-teal-600 text-sm hover:text-teal-700 transition-colors",children:"Visit Website →"}),(0,i.jsx)("button",{className:"bg-teal-600 text-white px-3 py-1 rounded text-sm hover:bg-teal-700 transition-colors",children:"View Details"})]})]},e.id))}),0===f.length&&(0,i.jsxs)("div",{className:"text-center py-12",children:[(0,i.jsx)("div",{className:"text-gray-400 text-6xl mb-4",children:"\uD83C\uDFE2"}),(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No companies found"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Try adjusting your search terms to see more results."})]})]}),(0,i.jsx)(l.A,{isOpen:x,onClose:()=>u(!1),data:p,title:"Companies Network"})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[305,70,58,636,593,792],()=>s(794)),_N_E=e.O()}]);