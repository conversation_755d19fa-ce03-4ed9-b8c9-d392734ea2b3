(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[724],{1467:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(7876),i=s(4232),n=s(7070);function r(e){let{data:t}=e,s=(0,i.useRef)(null);return(0,i.useEffect)(()=>{var e;if(!t||!s.current)return;n.Ltv(s.current).selectAll("*").remove();let a=n.Ltv(s.current).attr("width",800).attr("height",600).attr("viewBox",[0,0,800,600]),i=n.tXi(t.nodes).force("link",n.kJC(t.links).id(e=>e.id)).force("charge",n.xJS().strength(-400)).force("center",n.jTM(400,300)),r=a.append("g").selectAll("line").data(t.links).join("line").attr("stroke","#999").attr("stroke-opacity",.6).attr("stroke-width",e=>Math.sqrt(e.value||1)),l=a.append("g").selectAll("circle").data(t.nodes).join("circle").attr("r",e=>e.size||5).attr("fill",e=>{switch(e.type){case"jobSeeker":return"#3b82f6";case"company":return"#14b8a6";case"position":return"#10b981";case"skill":return"#f59e0b";default:return"#6366f1"}}).call((e=i,n.$Er().on("start",function(t){t.active||e.alphaTarget(.3).restart(),t.subject.fx=t.subject.x,t.subject.fy=t.subject.y}).on("drag",function(e){e.subject.fx=e.x,e.subject.fy=e.y}).on("end",function(t){t.active||e.alphaTarget(0),t.subject.fx=null,t.subject.fy=null})));return l.append("title").text(e=>e.name),i.on("tick",()=>{r.attr("x1",e=>e.source.x).attr("y1",e=>e.source.y).attr("x2",e=>e.target.x).attr("y2",e=>e.target.y),l.attr("cx",e=>e.x).attr("cy",e=>e.y)}),()=>{i.stop()}},[t]),(0,a.jsx)("div",{className:"border rounded-lg shadow-sm bg-white p-4",children:(0,a.jsx)("svg",{ref:s,className:"w-full h-full"})})}},3483:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var a=s(7876),i=s(4232),n=s(7328),r=s.n(n),l=s(8230),c=s.n(l),o=s(6234),d=s(8512),h=s(1467);function m(){let[e,t]=(0,i.useState)({jobSeekers:0,companies:0,positions:0,matches:0}),[s,n]=(0,i.useState)([]),[l,m]=(0,i.useState)(null),[x,p]=(0,i.useState)(!0);(0,i.useEffect)(()=>{setTimeout(()=>{t({jobSeekers:124,companies:37,positions:85,matches:213}),n([{id:1,type:"match",description:"New match: John Doe and Senior Developer at TechCorp",time:"2 hours ago"},{id:2,type:"company",description:"New company joined: InnovateTech",time:"5 hours ago"},{id:3,type:"position",description:"New position posted: Full Stack Developer at WebSolutions",time:"1 day ago"},{id:4,type:"jobSeeker",description:"New job seeker: Sarah Johnson",time:"1 day ago"},{id:5,type:"match",description:"New match: Emily Chen and UX Designer at DesignHub",time:"2 days ago"}]),m((0,d._)()),p(!1)},1e3)},[]);let u=e=>{switch(e){case"match":return"\uD83E\uDD1D";case"company":return"\uD83C\uDFE2";case"position":return"\uD83D\uDCCB";case"jobSeeker":return"\uD83D\uDC64";default:return"\uD83D\uDCC4"}};return(0,a.jsxs)(o.A,{children:[(0,a.jsx)(r(),{children:(0,a.jsx)("title",{children:"Dashboard | Candid Connections Katra"})}),(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Dashboard"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,a.jsx)("h3",{className:"text-gray-500 text-sm font-medium",children:"Job Seekers"}),(0,a.jsx)("p",{className:"text-3xl font-bold",children:e.jobSeekers}),(0,a.jsx)(c(),{href:"/job-seekers",className:"text-indigo-600 text-sm mt-2 inline-block",children:"View all →"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,a.jsx)("h3",{className:"text-gray-500 text-sm font-medium",children:"Companies"}),(0,a.jsx)("p",{className:"text-3xl font-bold",children:e.companies}),(0,a.jsx)(c(),{href:"/companies",className:"text-indigo-600 text-sm mt-2 inline-block",children:"View all →"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,a.jsx)("h3",{className:"text-gray-500 text-sm font-medium",children:"Open Positions"}),(0,a.jsx)("p",{className:"text-3xl font-bold",children:e.positions}),(0,a.jsx)(c(),{href:"/positions",className:"text-indigo-600 text-sm mt-2 inline-block",children:"View all →"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,a.jsx)("h3",{className:"text-gray-500 text-sm font-medium",children:"Matches"}),(0,a.jsx)("p",{className:"text-3xl font-bold",children:e.matches}),(0,a.jsx)(c(),{href:"/matches",className:"text-indigo-600 text-sm mt-2 inline-block",children:"View all →"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 bg-white rounded-lg shadow-md p-6",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Network Visualization"}),x?(0,a.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"})}):(0,a.jsx)("div",{className:"h-96",children:(0,a.jsx)(h.A,{data:l})}),(0,a.jsx)("div",{className:"mt-4 text-right",children:(0,a.jsx)(c(),{href:"/network",className:"text-indigo-600 text-sm",children:"View full network →"})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Recent Activity"}),x?(0,a.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"})}):(0,a.jsx)("div",{className:"space-y-4",children:s.map(e=>(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex-shrink-0 h-8 w-8 rounded-full bg-indigo-100 flex items-center justify-center text-lg",children:u(e.type)}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("p",{className:"text-sm text-gray-800",children:e.description}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:e.time})]})]},e.id))}),(0,a.jsx)("div",{className:"mt-4 text-right",children:(0,a.jsx)(c(),{href:"/activity",className:"text-indigo-600 text-sm",children:"View all activity →"})})]})]})]})]})}},5372:(e,t,s)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/dashboard",function(){return s(3483)}])},6234:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var a=s(7876),i=s(8230),n=s.n(i),r=s(9099),l=s(4232);function c(){let e=(0,r.useRouter)(),[t,s]=(0,l.useState)(!1),i=[{name:"Dashboard",path:"/",icon:"\uD83C\uDFE0"},{name:"Job Matches",path:"/matches",icon:"\uD83C\uDFAF"},{name:"Job Seekers",path:"/job-seekers",icon:"\uD83D\uDC65"},{name:"Companies",path:"/companies",icon:"\uD83C\uDFE2"},{name:"Positions",path:"/positions",icon:"\uD83D\uDCCB"},{name:"Skills",path:"/skills",icon:"\uD83D\uDEE0️"},{name:"Global View",path:"/global-view",icon:"\uD83C\uDF10"}];return(0,a.jsx)("nav",{className:"bg-white shadow-soft border-b border-candid-gray-200",children:(0,a.jsxs)("div",{className:"container-app",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center h-20",children:[(0,a.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,a.jsxs)(n(),{href:"/",className:"flex items-center space-x-3 group",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-primary-500 to-accent-500 rounded-xl flex items-center justify-center shadow-soft group-hover:shadow-medium transition-all duration-200",children:(0,a.jsx)("span",{className:"text-white font-bold text-lg",children:"C"})}),(0,a.jsxs)("div",{className:"hidden sm:block",children:[(0,a.jsx)("h1",{className:"text-xl font-bold text-candid-navy-900 group-hover:text-primary-600 transition-colors duration-200",children:"Candid Connections"}),(0,a.jsx)("p",{className:"text-sm text-candid-gray-600 -mt-1",children:"Katra Platform"})]})]})}),(0,a.jsx)("div",{className:"hidden lg:block",children:(0,a.jsx)("div",{className:"flex items-center space-x-1",children:i.map(t=>(0,a.jsxs)(n(),{href:t.path,className:"flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ".concat(e.pathname===t.path?"nav-link-active":"nav-link"),children:[(0,a.jsx)("span",{className:"text-base",children:t.icon}),(0,a.jsx)("span",{children:t.name})]},t.path))})}),(0,a.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[(0,a.jsx)(n(),{href:"/global-view",className:"btn-outline text-sm py-2 px-4",children:"\uD83C\uDF10 Network View"}),(0,a.jsx)(n(),{href:"https://portal.candid-connections.com/user/login",target:"_blank",rel:"noopener noreferrer",className:"btn-primary text-sm py-2 px-4",children:"Portal Login"})]}),(0,a.jsx)("div",{className:"lg:hidden",children:(0,a.jsx)("button",{onClick:()=>s(!t),className:"p-2 rounded-lg text-candid-navy-600 hover:text-primary-600 hover:bg-primary-50 transition-colors duration-200","aria-label":"Toggle mobile menu",children:(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:t?(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})})]}),t&&(0,a.jsx)("div",{className:"lg:hidden border-t border-candid-gray-200 py-4",children:(0,a.jsxs)("div",{className:"space-y-2",children:[i.map(t=>(0,a.jsxs)(n(),{href:t.path,onClick:()=>s(!1),className:"flex items-center space-x-3 px-4 py-3 rounded-lg text-base font-medium transition-all duration-200 ".concat(e.pathname===t.path?"nav-link-active":"nav-link"),children:[(0,a.jsx)("span",{className:"text-lg",children:t.icon}),(0,a.jsx)("span",{children:t.name})]},t.path)),(0,a.jsxs)("div",{className:"pt-4 space-y-2",children:[(0,a.jsx)(n(),{href:"/global-view",onClick:()=>s(!1),className:"block w-full btn-outline text-center",children:"\uD83C\uDF10 Network View"}),(0,a.jsx)(n(),{href:"https://portal.candid-connections.com/user/login",target:"_blank",rel:"noopener noreferrer",className:"block w-full btn-primary text-center",children:"Portal Login"})]})]})})]})})}function o(e){let{children:t}=e;return(0,a.jsxs)("div",{className:"min-h-screen bg-candid-gray-50",children:[(0,a.jsx)(c,{}),(0,a.jsx)("main",{className:"container-app section-padding",children:t})]})}},8512:(e,t,s)=>{"use strict";s.d(t,{_:()=>a});let a=()=>{let e=Array.from({length:5},(e,t)=>({id:"jobSeekers/".concat(t),name:"Job Seeker ".concat(t+1),type:"jobSeeker",size:8})),t=Array.from({length:3},(e,t)=>({id:"companies/".concat(t),name:"Company ".concat(t+1),type:"company",size:10})),s=Array.from({length:4},(e,t)=>({id:"positions/".concat(t),name:"Position ".concat(t+1),type:"position",size:9})),a=Array.from({length:8},(e,t)=>({id:"skills/".concat(t),name:"Skill ".concat(t+1),type:"skill",size:6})),i=[...e,...t,...s,...a],n=[];return e.forEach((e,s)=>{n.push({source:e.id,target:t[s%t.length].id,type:"works_for",value:1})}),t.forEach((e,t)=>{s.forEach((s,a)=>{(t+a)%2==0&&n.push({source:e.id,target:s.id,type:"posts",value:1})})}),e.forEach(e=>{let t=2+Math.floor(3*Math.random());[...a].sort(()=>.5-Math.random()).slice(0,t).forEach(t=>{n.push({source:e.id,target:t.id,type:"has_skill",value:1})})}),s.forEach(e=>{let t=2+Math.floor(2*Math.random());[...a].sort(()=>.5-Math.random()).slice(0,t).forEach(t=>{n.push({source:e.id,target:t.id,type:"requires",value:1})})}),{nodes:i,links:n}}}},e=>{var t=t=>e(e.s=t);e.O(0,[305,70,636,593,792],()=>t(5372)),_N_E=e.O()}]);