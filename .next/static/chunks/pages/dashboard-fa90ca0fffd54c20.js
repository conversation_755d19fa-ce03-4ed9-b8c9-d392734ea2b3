(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[724],{1467:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var a=s(7876),i=s(4232),r=s(7070);function n(e){let{data:t}=e,s=(0,i.useRef)(null);return(0,i.useEffect)(()=>{var e;if(!t||!s.current)return;r.Ltv(s.current).selectAll("*").remove();let a=r.Ltv(s.current).attr("width",800).attr("height",600).attr("viewBox",[0,0,800,600]),i=r.tXi(t.nodes).force("link",r.kJC(t.links).id(e=>e.id)).force("charge",r.xJS().strength(-400)).force("center",r.jTM(400,300)),n=a.append("g").selectAll("line").data(t.links).join("line").attr("stroke","#999").attr("stroke-opacity",.6).attr("stroke-width",e=>Math.sqrt(e.value||1)),l=a.append("g").selectAll("circle").data(t.nodes).join("circle").attr("r",e=>e.size||5).attr("fill",e=>{switch(e.type){case"jobSeeker":return"#3b82f6";case"company":return"#14b8a6";case"position":return"#10b981";case"skill":return"#f59e0b";default:return"#6366f1"}}).call((e=i,r.$Er().on("start",function(t){t.active||e.alphaTarget(.3).restart(),t.subject.fx=t.subject.x,t.subject.fy=t.subject.y}).on("drag",function(e){e.subject.fx=e.x,e.subject.fy=e.y}).on("end",function(t){t.active||e.alphaTarget(0),t.subject.fx=null,t.subject.fy=null})));return l.append("title").text(e=>e.name),i.on("tick",()=>{n.attr("x1",e=>e.source.x).attr("y1",e=>e.source.y).attr("x2",e=>e.target.x).attr("y2",e=>e.target.y),l.attr("cx",e=>e.x).attr("cy",e=>e.y)}),()=>{i.stop()}},[t]),(0,a.jsx)("div",{className:"border rounded-lg shadow-sm bg-white p-4",children:(0,a.jsx)("svg",{ref:s,className:"w-full h-full"})})}},3483:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var a=s(7876),i=s(4232),r=s(7328),n=s.n(r),l=s(8230),c=s.n(l),o=s(6234),d=s(8512),h=s(1467);function m(){let[e,t]=(0,i.useState)({jobSeekers:0,companies:0,positions:0,matches:0}),[s,r]=(0,i.useState)([]),[l,m]=(0,i.useState)(null),[x,u]=(0,i.useState)(!0);(0,i.useEffect)(()=>{setTimeout(()=>{t({jobSeekers:124,companies:37,positions:85,matches:213}),r([{id:1,type:"match",description:"New match: John Doe and Senior Developer at TechCorp",time:"2 hours ago"},{id:2,type:"company",description:"New company joined: InnovateTech",time:"5 hours ago"},{id:3,type:"position",description:"New position posted: Full Stack Developer at WebSolutions",time:"1 day ago"},{id:4,type:"jobSeeker",description:"New job seeker: Sarah Johnson",time:"1 day ago"},{id:5,type:"match",description:"New match: Emily Chen and UX Designer at DesignHub",time:"2 days ago"}]),m((0,d._)()),u(!1)},1e3)},[]);let p=e=>{switch(e){case"match":return"\uD83E\uDD1D";case"company":return"\uD83C\uDFE2";case"position":return"\uD83D\uDCCB";case"jobSeeker":return"\uD83D\uDC64";default:return"\uD83D\uDCC4"}};return(0,a.jsxs)(o.A,{children:[(0,a.jsx)(n(),{children:(0,a.jsx)("title",{children:"Dashboard | Candid Connections Katra"})}),(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Dashboard"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,a.jsx)("h3",{className:"text-gray-500 text-sm font-medium",children:"Job Seekers"}),(0,a.jsx)("p",{className:"text-3xl font-bold",children:e.jobSeekers}),(0,a.jsx)(c(),{href:"/job-seekers",className:"text-indigo-600 text-sm mt-2 inline-block",children:"View all →"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,a.jsx)("h3",{className:"text-gray-500 text-sm font-medium",children:"Companies"}),(0,a.jsx)("p",{className:"text-3xl font-bold",children:e.companies}),(0,a.jsx)(c(),{href:"/companies",className:"text-indigo-600 text-sm mt-2 inline-block",children:"View all →"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,a.jsx)("h3",{className:"text-gray-500 text-sm font-medium",children:"Open Positions"}),(0,a.jsx)("p",{className:"text-3xl font-bold",children:e.positions}),(0,a.jsx)(c(),{href:"/positions",className:"text-indigo-600 text-sm mt-2 inline-block",children:"View all →"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,a.jsx)("h3",{className:"text-gray-500 text-sm font-medium",children:"Matches"}),(0,a.jsx)("p",{className:"text-3xl font-bold",children:e.matches}),(0,a.jsx)(c(),{href:"/matches",className:"text-indigo-600 text-sm mt-2 inline-block",children:"View all →"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 bg-white rounded-lg shadow-md p-6",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Network Visualization"}),x?(0,a.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"})}):(0,a.jsx)("div",{className:"h-96",children:(0,a.jsx)(h.A,{data:l})}),(0,a.jsx)("div",{className:"mt-4 text-right",children:(0,a.jsx)(c(),{href:"/network",className:"text-indigo-600 text-sm",children:"View full network →"})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Recent Activity"}),x?(0,a.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"})}):(0,a.jsx)("div",{className:"space-y-4",children:s.map(e=>(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex-shrink-0 h-8 w-8 rounded-full bg-indigo-100 flex items-center justify-center text-lg",children:p(e.type)}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("p",{className:"text-sm text-gray-800",children:e.description}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:e.time})]})]},e.id))}),(0,a.jsx)("div",{className:"mt-4 text-right",children:(0,a.jsx)(c(),{href:"/activity",className:"text-indigo-600 text-sm",children:"View all activity →"})})]})]})]})]})}},5372:(e,t,s)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/dashboard",function(){return s(3483)}])},6234:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var a=s(7876),i=s(8230),r=s.n(i),n=s(9099);function l(){let e=(0,n.useRouter)();return(0,a.jsx)("nav",{className:"bg-white shadow-md",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,a.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,a.jsx)(r(),{href:"/",className:"font-bold text-xl text-indigo-600",children:"Candid Connections Katra"})}),(0,a.jsx)("div",{className:"hidden md:block",children:(0,a.jsx)("div",{className:"ml-10 flex items-center space-x-4",children:[{name:"Dashboard",path:"/"},{name:"Job Matches",path:"/matches"},{name:"Job Seekers",path:"/job-seekers"},{name:"Companies",path:"/companies"},{name:"Positions",path:"/positions"},{name:"Skills",path:"/skills"}].map(t=>(0,a.jsx)(r(),{href:t.path,className:"px-3 py-2 rounded-md text-sm font-medium ".concat(e.pathname===t.path?"bg-indigo-100 text-indigo-700":"text-gray-700 hover:bg-gray-100"),children:t.name},t.path))})})]})})})}function c(e){let{children:t}=e;return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)(l,{}),(0,a.jsx)("main",{children:t})]})}},8512:(e,t,s)=>{"use strict";s.d(t,{_:()=>a});let a=()=>{let e=Array.from({length:5},(e,t)=>({id:"jobSeekers/".concat(t),name:"Job Seeker ".concat(t+1),type:"jobSeeker",size:8})),t=Array.from({length:3},(e,t)=>({id:"companies/".concat(t),name:"Company ".concat(t+1),type:"company",size:10})),s=Array.from({length:4},(e,t)=>({id:"positions/".concat(t),name:"Position ".concat(t+1),type:"position",size:9})),a=Array.from({length:8},(e,t)=>({id:"skills/".concat(t),name:"Skill ".concat(t+1),type:"skill",size:6})),i=[...e,...t,...s,...a],r=[];return e.forEach((e,s)=>{r.push({source:e.id,target:t[s%t.length].id,type:"works_for",value:1})}),t.forEach((e,t)=>{s.forEach((s,a)=>{(t+a)%2==0&&r.push({source:e.id,target:s.id,type:"posts",value:1})})}),e.forEach(e=>{let t=2+Math.floor(3*Math.random());[...a].sort(()=>.5-Math.random()).slice(0,t).forEach(t=>{r.push({source:e.id,target:t.id,type:"has_skill",value:1})})}),s.forEach(e=>{let t=2+Math.floor(2*Math.random());[...a].sort(()=>.5-Math.random()).slice(0,t).forEach(t=>{r.push({source:e.id,target:t.id,type:"requires",value:1})})}),{nodes:i,links:r}}}},e=>{var t=t=>e(e.s=t);e.O(0,[305,70,636,593,792],()=>t(5372)),_N_E=e.O()}]);