(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[477],{234:(e,s,l)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/global-view",function(){return l(6040)}])},6040:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>o});var t=l(7876),a=l(4232),i=l(7328),n=l.n(i),d=l(6234),r=l(1566),c=l(8512);function o(){var e;let[s,l]=(0,a.useState)(null),[i,o]=(0,a.useState)(!0),[m,x]=(0,a.useState)(null),[h,g]=(0,a.useState)(!1),[b,u]=(0,a.useState)({}),[p,N]=(0,a.useState)("all"),[j,v]=(0,a.useState)({});(0,a.useEffect)(()=>{(async()=>{try{setTimeout(()=>{let e=(0,c._)();l(e);let s=e.nodes.reduce((e,s)=>(e[s.type]=(e[s.type]||0)+1,e),{}),t=e.links.reduce((e,s)=>(e[s.type]=(e[s.type]||0)+1,e),{});u({totalNodes:e.nodes.length,totalLinks:e.links.length,nodesByType:s,linksByType:t}),v({density:(2*e.links.length/(e.nodes.length*(e.nodes.length-1))*100).toFixed(2),avgConnections:(2*e.links.length/e.nodes.length).toFixed(1),clusters:Math.ceil(e.nodes.length/8),centralNodes:e.nodes.filter(e=>e.size>8).length}),o(!1)},1e3)}catch(e){x(e.message),o(!1)}})()},[]);let y=s?{nodes:"all"===p?s.nodes:s.nodes.filter(e=>e.type===p),links:"all"===p?s.links:s.links.filter(e=>{let l=s.nodes.find(s=>s.id===e.source),t=s.nodes.find(s=>s.id===e.target);return(null==l?void 0:l.type)===p||(null==t?void 0:t.type)===p})}:{nodes:[],links:[]},f=[{value:"all",label:"All Entities",icon:"\uD83C\uDF10",color:"bg-indigo-100 text-indigo-800"},{value:"jobSeeker",label:"Job Seekers",icon:"\uD83D\uDC64",color:"bg-blue-100 text-blue-800"},{value:"company",label:"Companies",icon:"\uD83C\uDFE2",color:"bg-teal-100 text-teal-800"},{value:"position",label:"Positions",icon:"\uD83D\uDCCB",color:"bg-emerald-100 text-emerald-800"},{value:"skill",label:"Skills",icon:"\uD83D\uDD27",color:"bg-amber-100 text-amber-800"}];return i?(0,t.jsx)(d.A,{children:(0,t.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,t.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"})})})}):m?(0,t.jsx)(d.A,{children:(0,t.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,t.jsxs)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded",children:["Error: ",m]})})}):(0,t.jsxs)(d.A,{children:[(0,t.jsx)(n(),{children:(0,t.jsx)("title",{children:"Global Network View | Candid Connections Katra"})}),(0,t.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold",children:"Global Network View"}),(0,t.jsx)("button",{onClick:()=>g(!0),className:"bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors",children:"\uD83C\uDF10 Open Full Visualization"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-2 bg-indigo-100 rounded-lg",children:(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDD17"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Connections"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:b.totalLinks})]})]})}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-2 bg-emerald-100 rounded-lg",children:(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCA"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Network Density"}),(0,t.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[j.density,"%"]})]})]})}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-2 bg-amber-100 rounded-lg",children:(0,t.jsx)("span",{className:"text-2xl",children:"\uD83C\uDFAF"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Avg Connections"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:j.avgConnections})]})]})}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,t.jsx)("span",{className:"text-2xl",children:"⭐"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Central Nodes"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:j.centralNodes})]})]})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Filter by Entity Type"}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4",children:f.map(e=>{var s;return(0,t.jsx)("button",{onClick:()=>N(e.value),className:"p-4 rounded-lg border-2 transition-all ".concat(p===e.value?"border-indigo-500 bg-indigo-50":"border-gray-200 hover:border-gray-300"),children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("span",{className:"text-2xl block mb-2",children:e.icon}),(0,t.jsx)("p",{className:"font-medium text-sm",children:e.label}),(0,t.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[(null==(s=b.nodesByType)?void 0:s[e.value])||b.totalNodes||0," nodes"]})]})},e.value)})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Entities by Type"}),(0,t.jsx)("div",{className:"space-y-4",children:Object.entries(b.nodesByType||{}).map(e=>{let[s,l]=e,a=f.find(e=>e.value===s),i=(l/b.totalNodes*100).toFixed(1);return(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"text-xl mr-3",children:(null==a?void 0:a.icon)||"\uD83D\uDCC4"}),(0,t.jsx)("span",{className:"font-medium",children:(null==a?void 0:a.label)||s})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-24 bg-gray-200 rounded-full h-2 mr-3",children:(0,t.jsx)("div",{className:"bg-indigo-600 h-2 rounded-full",style:{width:"".concat(i,"%")}})}),(0,t.jsx)("span",{className:"text-sm font-medium w-12 text-right",children:l})]})]},s)})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Relationships by Type"}),(0,t.jsx)("div",{className:"space-y-4",children:[{type:"works_for",label:"Employment",icon:"\uD83D\uDCBC",color:"bg-blue-500"},{type:"posts",label:"Job Postings",icon:"\uD83D\uDCDD",color:"bg-emerald-500"},{type:"requires",label:"Skill Requirements",icon:"\uD83C\uDFAF",color:"bg-amber-500"},{type:"has_skill",label:"Skill Possession",icon:"⭐",color:"bg-purple-500"},{type:"matched_to",label:"Job Matches",icon:"\uD83E\uDD1D",color:"bg-red-500"}].map(e=>{var s;let l=(null==(s=b.linksByType)?void 0:s[e.type])||0,a=b.totalLinks>0?(l/b.totalLinks*100).toFixed(1):0;return(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"text-xl mr-3",children:e.icon}),(0,t.jsx)("span",{className:"font-medium",children:e.label})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-24 bg-gray-200 rounded-full h-2 mr-3",children:(0,t.jsx)("div",{className:"h-2 rounded-full ".concat(e.color),style:{width:"".concat(a,"%")}})}),(0,t.jsx)("span",{className:"text-sm font-medium w-12 text-right",children:l})]})]},e.type)})})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Network Insights"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"text-center p-4 bg-emerald-50 rounded-lg",children:[(0,t.jsx)("span",{className:"text-3xl block mb-2",children:"\uD83C\uDFAF"}),(0,t.jsx)("h3",{className:"font-medium text-emerald-800",children:"High Match Potential"}),(0,t.jsx)("p",{className:"text-sm text-emerald-600 mt-1",children:"Strong skill alignment across the network"})]}),(0,t.jsxs)("div",{className:"text-center p-4 bg-amber-50 rounded-lg",children:[(0,t.jsx)("span",{className:"text-3xl block mb-2",children:"\uD83D\uDCC8"}),(0,t.jsx)("h3",{className:"font-medium text-amber-800",children:"Growing Connections"}),(0,t.jsx)("p",{className:"text-sm text-amber-600 mt-1",children:"Network density increasing over time"})]}),(0,t.jsxs)("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)("span",{className:"text-3xl block mb-2",children:"\uD83D\uDD0D"}),(0,t.jsx)("h3",{className:"font-medium text-blue-800",children:"Skill Gaps Identified"}),(0,t.jsx)("p",{className:"text-sm text-blue-600 mt-1",children:"Opportunities for talent development"})]})]})]})]}),(0,t.jsx)(r.A,{isOpen:h,onClose:()=>g(!1),data:y,title:"Global Network - ".concat((null==(e=f.find(e=>e.value===p))?void 0:e.label)||"All Entities")})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[305,70,58,636,593,792],()=>s(234)),_N_E=e.O()}]);