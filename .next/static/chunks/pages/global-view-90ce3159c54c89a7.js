(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[477],{234:(e,s,l)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/global-view",function(){return l(6040)}])},1566:(e,s,l)=>{"use strict";l.d(s,{A:()=>r});var t=l(7876),i=l(4232),n=l(1467);function a(e){let{data:s}=e,n=(0,i.useRef)(null);return(0,i.useEffect)(()=>{s&&n.current&&(n.current.innerHTML="",Promise.all([l.e(391),l.e(917),l.e(403),l.e(728),l.e(682)]).then(l.bind(l,4682)).then(e=>{let l=(0,e.default)().width(n.current.clientWidth).height(500).backgroundColor("#ffffff").nodeColor(e=>{switch(e.type){case"jobSeeker":return"#3b82f6";case"company":return"#14b8a6";case"position":return"#10b981";case"skill":return"#f59e0b";default:return"#6366f1"}}).nodeLabel(e=>e.name).nodeVal(e=>e.size||5).linkWidth(e=>e.value||1).linkDirectionalParticles(2).linkDirectionalParticleSpeed(.005).graphData(s)(n.current);window.addEventListener("resize",()=>{n.current&&l.width(n.current.clientWidth)})}).catch(e=>{console.error("Failed to load 3D visualization:",e),n.current&&(n.current.innerHTML='<div class="flex items-center justify-center h-full text-gray-500">3D visualization unavailable</div>')}))},[s]),(0,t.jsx)("div",{ref:n,className:"w-full h-[500px] border rounded-lg shadow-sm bg-white"})}function r(e){let{isOpen:s,onClose:l,data:r,title:d}=e,[c,o]=(0,i.useState)("2D");return s?(0,t.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,t.jsxs)("div",{className:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,t.jsx)("div",{className:"fixed inset-0 transition-opacity","aria-hidden":"true",children:(0,t.jsx)("div",{className:"absolute inset-0 bg-gray-500 opacity-75",onClick:l})}),(0,t.jsxs)("div",{className:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-5xl sm:w-full",children:[(0,t.jsx)("div",{className:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:(0,t.jsx)("div",{className:"sm:flex sm:items-start",children:(0,t.jsxs)("div",{className:"w-full",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,t.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:d||"Network Visualization"}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)("button",{type:"button",onClick:()=>o("2D"),className:"px-3 py-1 text-sm font-medium rounded-md ".concat("2D"===c?"bg-indigo-100 text-indigo-700":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:"2D View"}),(0,t.jsx)("button",{type:"button",onClick:()=>o("3D"),className:"px-3 py-1 text-sm font-medium rounded-md ".concat("3D"===c?"bg-indigo-100 text-indigo-700":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:"3D View"})]})]}),(0,t.jsx)("div",{className:"mt-2",children:"2D"===c?(0,t.jsx)(n.A,{data:r}):(0,t.jsx)(a,{data:r})})]})})}),(0,t.jsx)("div",{className:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:(0,t.jsx)("button",{type:"button",className:"btn-secondary",onClick:l,children:"Close"})})]})]})}):null}},6040:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>o});var t=l(7876),i=l(4232),n=l(7328),a=l.n(n),r=l(6234),d=l(1566),c=l(8512);function o(){var e;let[s,l]=(0,i.useState)(null),[n,o]=(0,i.useState)(!0),[m,x]=(0,i.useState)(null),[h,u]=(0,i.useState)(!1),[b,g]=(0,i.useState)({}),[p,j]=(0,i.useState)("all"),[N,v]=(0,i.useState)({});(0,i.useEffect)(()=>{(async()=>{try{setTimeout(()=>{let e=(0,c._)();l(e);let s=e.nodes.reduce((e,s)=>(e[s.type]=(e[s.type]||0)+1,e),{}),t=e.links.reduce((e,s)=>(e[s.type]=(e[s.type]||0)+1,e),{});g({totalNodes:e.nodes.length,totalLinks:e.links.length,nodesByType:s,linksByType:t}),v({density:(2*e.links.length/(e.nodes.length*(e.nodes.length-1))*100).toFixed(2),avgConnections:(2*e.links.length/e.nodes.length).toFixed(1),clusters:Math.ceil(e.nodes.length/8),centralNodes:e.nodes.filter(e=>e.size>8).length}),o(!1)},1e3)}catch(e){x(e.message),o(!1)}})()},[]);let f=s?{nodes:"all"===p?s.nodes:s.nodes.filter(e=>e.type===p),links:"all"===p?s.links:s.links.filter(e=>{let l=s.nodes.find(s=>s.id===e.source),t=s.nodes.find(s=>s.id===e.target);return(null==l?void 0:l.type)===p||(null==t?void 0:t.type)===p})}:{nodes:[],links:[]},y=[{value:"all",label:"All Entities",icon:"\uD83C\uDF10",color:"bg-indigo-100 text-indigo-800"},{value:"jobSeeker",label:"Job Seekers",icon:"\uD83D\uDC64",color:"bg-blue-100 text-blue-800"},{value:"company",label:"Companies",icon:"\uD83C\uDFE2",color:"bg-teal-100 text-teal-800"},{value:"position",label:"Positions",icon:"\uD83D\uDCCB",color:"bg-emerald-100 text-emerald-800"},{value:"skill",label:"Skills",icon:"\uD83D\uDD27",color:"bg-amber-100 text-amber-800"}];return n?(0,t.jsx)(r.A,{children:(0,t.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,t.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"})})})}):m?(0,t.jsx)(r.A,{children:(0,t.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,t.jsxs)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded",children:["Error: ",m]})})}):(0,t.jsxs)(r.A,{children:[(0,t.jsx)(a(),{children:(0,t.jsx)("title",{children:"Global Network View | Candid Connections Katra"})}),(0,t.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold",children:"Global Network View"}),(0,t.jsx)("button",{onClick:()=>u(!0),className:"bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors",children:"\uD83C\uDF10 Open Full Visualization"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-2 bg-indigo-100 rounded-lg",children:(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDD17"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Connections"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:b.totalLinks})]})]})}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-2 bg-emerald-100 rounded-lg",children:(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCA"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Network Density"}),(0,t.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[N.density,"%"]})]})]})}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-2 bg-amber-100 rounded-lg",children:(0,t.jsx)("span",{className:"text-2xl",children:"\uD83C\uDFAF"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Avg Connections"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:N.avgConnections})]})]})}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,t.jsx)("span",{className:"text-2xl",children:"⭐"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Central Nodes"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:N.centralNodes})]})]})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Filter by Entity Type"}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4",children:y.map(e=>{var s;return(0,t.jsx)("button",{onClick:()=>j(e.value),className:"p-4 rounded-lg border-2 transition-all ".concat(p===e.value?"border-indigo-500 bg-indigo-50":"border-gray-200 hover:border-gray-300"),children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("span",{className:"text-2xl block mb-2",children:e.icon}),(0,t.jsx)("p",{className:"font-medium text-sm",children:e.label}),(0,t.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[(null==(s=b.nodesByType)?void 0:s[e.value])||b.totalNodes||0," nodes"]})]})},e.value)})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Entities by Type"}),(0,t.jsx)("div",{className:"space-y-4",children:Object.entries(b.nodesByType||{}).map(e=>{let[s,l]=e,i=y.find(e=>e.value===s),n=(l/b.totalNodes*100).toFixed(1);return(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"text-xl mr-3",children:(null==i?void 0:i.icon)||"\uD83D\uDCC4"}),(0,t.jsx)("span",{className:"font-medium",children:(null==i?void 0:i.label)||s})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-24 bg-gray-200 rounded-full h-2 mr-3",children:(0,t.jsx)("div",{className:"bg-indigo-600 h-2 rounded-full",style:{width:"".concat(n,"%")}})}),(0,t.jsx)("span",{className:"text-sm font-medium w-12 text-right",children:l})]})]},s)})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Relationships by Type"}),(0,t.jsx)("div",{className:"space-y-4",children:[{type:"works_for",label:"Employment",icon:"\uD83D\uDCBC",color:"bg-blue-500"},{type:"posts",label:"Job Postings",icon:"\uD83D\uDCDD",color:"bg-emerald-500"},{type:"requires",label:"Skill Requirements",icon:"\uD83C\uDFAF",color:"bg-amber-500"},{type:"has_skill",label:"Skill Possession",icon:"⭐",color:"bg-purple-500"},{type:"matched_to",label:"Job Matches",icon:"\uD83E\uDD1D",color:"bg-red-500"}].map(e=>{var s;let l=(null==(s=b.linksByType)?void 0:s[e.type])||0,i=b.totalLinks>0?(l/b.totalLinks*100).toFixed(1):0;return(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"text-xl mr-3",children:e.icon}),(0,t.jsx)("span",{className:"font-medium",children:e.label})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-24 bg-gray-200 rounded-full h-2 mr-3",children:(0,t.jsx)("div",{className:"h-2 rounded-full ".concat(e.color),style:{width:"".concat(i,"%")}})}),(0,t.jsx)("span",{className:"text-sm font-medium w-12 text-right",children:l})]})]},e.type)})})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Network Insights"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"text-center p-4 bg-emerald-50 rounded-lg",children:[(0,t.jsx)("span",{className:"text-3xl block mb-2",children:"\uD83C\uDFAF"}),(0,t.jsx)("h3",{className:"font-medium text-emerald-800",children:"High Match Potential"}),(0,t.jsx)("p",{className:"text-sm text-emerald-600 mt-1",children:"Strong skill alignment across the network"})]}),(0,t.jsxs)("div",{className:"text-center p-4 bg-amber-50 rounded-lg",children:[(0,t.jsx)("span",{className:"text-3xl block mb-2",children:"\uD83D\uDCC8"}),(0,t.jsx)("h3",{className:"font-medium text-amber-800",children:"Growing Connections"}),(0,t.jsx)("p",{className:"text-sm text-amber-600 mt-1",children:"Network density increasing over time"})]}),(0,t.jsxs)("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)("span",{className:"text-3xl block mb-2",children:"\uD83D\uDD0D"}),(0,t.jsx)("h3",{className:"font-medium text-blue-800",children:"Skill Gaps Identified"}),(0,t.jsx)("p",{className:"text-sm text-blue-600 mt-1",children:"Opportunities for talent development"})]})]})]})]}),(0,t.jsx)(d.A,{isOpen:h,onClose:()=>u(!1),data:f,title:"Global Network - ".concat((null==(e=y.find(e=>e.value===p))?void 0:e.label)||"All Entities")})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[305,70,41,636,593,792],()=>s(234)),_N_E=e.O()}]);