(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[477],{234:(e,s,t)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/global-view",function(){return t(6040)}])},1467:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var l=t(7876),a=t(4232),n=t(7070);function i(e){let{data:s}=e,t=(0,a.useRef)(null);return(0,a.useEffect)(()=>{var e;if(!s||!t.current)return;n.Ltv(t.current).selectAll("*").remove();let l=n.Ltv(t.current).attr("width",800).attr("height",600).attr("viewBox",[0,0,800,600]),a=n.tXi(s.nodes).force("link",n.kJC(s.links).id(e=>e.id)).force("charge",n.xJS().strength(-400)).force("center",n.jTM(400,300)),i=l.append("g").selectAll("line").data(s.links).join("line").attr("stroke","#999").attr("stroke-opacity",.6).attr("stroke-width",e=>Math.sqrt(e.value||1)),r=l.append("g").selectAll("circle").data(s.nodes).join("circle").attr("r",e=>e.size||5).attr("fill",e=>{switch(e.type){case"jobSeeker":return"#3b82f6";case"company":return"#14b8a6";case"position":return"#10b981";case"skill":return"#f59e0b";default:return"#6366f1"}}).call((e=a,n.$Er().on("start",function(s){s.active||e.alphaTarget(.3).restart(),s.subject.fx=s.subject.x,s.subject.fy=s.subject.y}).on("drag",function(e){e.subject.fx=e.x,e.subject.fy=e.y}).on("end",function(s){s.active||e.alphaTarget(0),s.subject.fx=null,s.subject.fy=null})));return r.append("title").text(e=>e.name),a.on("tick",()=>{i.attr("x1",e=>e.source.x).attr("y1",e=>e.source.y).attr("x2",e=>e.target.x).attr("y2",e=>e.target.y),r.attr("cx",e=>e.x).attr("cy",e=>e.y)}),()=>{a.stop()}},[s]),(0,l.jsx)("div",{className:"border rounded-lg shadow-sm bg-white p-4",children:(0,l.jsx)("svg",{ref:t,className:"w-full h-full"})})}},1566:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var l=t(7876),a=t(4232),n=t(1467);function i(e){let{data:s}=e,n=(0,a.useRef)(null);return(0,a.useEffect)(()=>{s&&n.current&&(n.current.innerHTML="",Promise.all([t.e(391),t.e(917),t.e(403),t.e(728),t.e(682)]).then(t.bind(t,4682)).then(e=>{let t=(0,e.default)().width(n.current.clientWidth).height(500).backgroundColor("#ffffff").nodeColor(e=>{switch(e.type){case"jobSeeker":return"#3b82f6";case"company":return"#14b8a6";case"position":return"#10b981";case"skill":return"#f59e0b";default:return"#6366f1"}}).nodeLabel(e=>e.name).nodeVal(e=>e.size||5).linkWidth(e=>e.value||1).linkDirectionalParticles(2).linkDirectionalParticleSpeed(.005).graphData(s)(n.current);window.addEventListener("resize",()=>{n.current&&t.width(n.current.clientWidth)})}).catch(e=>{console.error("Failed to load 3D visualization:",e),n.current&&(n.current.innerHTML='<div class="flex items-center justify-center h-full text-gray-500">3D visualization unavailable</div>')}))},[s]),(0,l.jsx)("div",{ref:n,className:"w-full h-[500px] border rounded-lg shadow-sm bg-white"})}function r(e){let{isOpen:s,onClose:t,data:r,title:d}=e,[c,o]=(0,a.useState)("2D");return s?(0,l.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,l.jsxs)("div",{className:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,l.jsx)("div",{className:"fixed inset-0 transition-opacity","aria-hidden":"true",children:(0,l.jsx)("div",{className:"absolute inset-0 bg-gray-500 opacity-75",onClick:t})}),(0,l.jsxs)("div",{className:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-5xl sm:w-full",children:[(0,l.jsx)("div",{className:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:(0,l.jsx)("div",{className:"sm:flex sm:items-start",children:(0,l.jsxs)("div",{className:"w-full",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,l.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:d||"Network Visualization"}),(0,l.jsxs)("div",{className:"flex space-x-2",children:[(0,l.jsx)("button",{type:"button",onClick:()=>o("2D"),className:"px-3 py-1 text-sm font-medium rounded-md ".concat("2D"===c?"bg-indigo-100 text-indigo-700":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:"2D View"}),(0,l.jsx)("button",{type:"button",onClick:()=>o("3D"),className:"px-3 py-1 text-sm font-medium rounded-md ".concat("3D"===c?"bg-indigo-100 text-indigo-700":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:"3D View"})]})]}),(0,l.jsx)("div",{className:"mt-2",children:"2D"===c?(0,l.jsx)(n.A,{data:r}):(0,l.jsx)(i,{data:r})})]})})}),(0,l.jsx)("div",{className:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:(0,l.jsx)("button",{type:"button",className:"btn-secondary",onClick:t,children:"Close"})})]})]})}):null}},6040:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o});var l=t(7876),a=t(4232),n=t(7328),i=t.n(n),r=t(6234),d=t(1566),c=t(8512);function o(){var e;let[s,t]=(0,a.useState)(null),[n,o]=(0,a.useState)(!0),[m,x]=(0,a.useState)(null),[h,u]=(0,a.useState)(!1),[g,b]=(0,a.useState)({}),[p,f]=(0,a.useState)("all"),[j,N]=(0,a.useState)({});(0,a.useEffect)(()=>{(async()=>{try{setTimeout(()=>{let e=(0,c._)();t(e);let s=e.nodes.reduce((e,s)=>(e[s.type]=(e[s.type]||0)+1,e),{}),l=e.links.reduce((e,s)=>(e[s.type]=(e[s.type]||0)+1,e),{});b({totalNodes:e.nodes.length,totalLinks:e.links.length,nodesByType:s,linksByType:l}),N({density:(2*e.links.length/(e.nodes.length*(e.nodes.length-1))*100).toFixed(2),avgConnections:(2*e.links.length/e.nodes.length).toFixed(1),clusters:Math.ceil(e.nodes.length/8),centralNodes:e.nodes.filter(e=>e.size>8).length}),o(!1)},1e3)}catch(e){x(e.message),o(!1)}})()},[]);let v=s?{nodes:"all"===p?s.nodes:s.nodes.filter(e=>e.type===p),links:"all"===p?s.links:s.links.filter(e=>{let t=s.nodes.find(s=>s.id===e.source),l=s.nodes.find(s=>s.id===e.target);return(null==t?void 0:t.type)===p||(null==l?void 0:l.type)===p})}:{nodes:[],links:[]},y=[{value:"all",label:"All Entities",icon:"\uD83C\uDF10",color:"bg-indigo-100 text-indigo-800"},{value:"jobSeeker",label:"Job Seekers",icon:"\uD83D\uDC64",color:"bg-blue-100 text-blue-800"},{value:"company",label:"Companies",icon:"\uD83C\uDFE2",color:"bg-teal-100 text-teal-800"},{value:"position",label:"Positions",icon:"\uD83D\uDCCB",color:"bg-emerald-100 text-emerald-800"},{value:"skill",label:"Skills",icon:"\uD83D\uDD27",color:"bg-amber-100 text-amber-800"}];return n?(0,l.jsx)(r.A,{children:(0,l.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,l.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,l.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"})})})}):m?(0,l.jsx)(r.A,{children:(0,l.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,l.jsxs)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded",children:["Error: ",m]})})}):(0,l.jsxs)(r.A,{children:[(0,l.jsx)(i(),{children:(0,l.jsx)("title",{children:"Global Network View | Candid Connections Katra"})}),(0,l.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,l.jsx)("h1",{className:"text-2xl font-bold",children:"Global Network View"}),(0,l.jsx)("button",{onClick:()=>u(!0),className:"bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors",children:"\uD83C\uDF10 Open Full Visualization"})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,l.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"p-2 bg-indigo-100 rounded-lg",children:(0,l.jsx)("span",{className:"text-2xl",children:"\uD83D\uDD17"})}),(0,l.jsxs)("div",{className:"ml-4",children:[(0,l.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Connections"}),(0,l.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:g.totalLinks})]})]})}),(0,l.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"p-2 bg-emerald-100 rounded-lg",children:(0,l.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCA"})}),(0,l.jsxs)("div",{className:"ml-4",children:[(0,l.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Network Density"}),(0,l.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[j.density,"%"]})]})]})}),(0,l.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"p-2 bg-amber-100 rounded-lg",children:(0,l.jsx)("span",{className:"text-2xl",children:"\uD83C\uDFAF"})}),(0,l.jsxs)("div",{className:"ml-4",children:[(0,l.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Avg Connections"}),(0,l.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:j.avgConnections})]})]})}),(0,l.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,l.jsx)("span",{className:"text-2xl",children:"⭐"})}),(0,l.jsxs)("div",{className:"ml-4",children:[(0,l.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Central Nodes"}),(0,l.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:j.centralNodes})]})]})})]}),(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8",children:[(0,l.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Filter by Entity Type"}),(0,l.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4",children:y.map(e=>{var s;return(0,l.jsx)("button",{onClick:()=>f(e.value),className:"p-4 rounded-lg border-2 transition-all ".concat(p===e.value?"border-indigo-500 bg-indigo-50":"border-gray-200 hover:border-gray-300"),children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("span",{className:"text-2xl block mb-2",children:e.icon}),(0,l.jsx)("p",{className:"font-medium text-sm",children:e.label}),(0,l.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[(null==(s=g.nodesByType)?void 0:s[e.value])||g.totalNodes||0," nodes"]})]})},e.value)})})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8",children:[(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,l.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Entities by Type"}),(0,l.jsx)("div",{className:"space-y-4",children:Object.entries(g.nodesByType||{}).map(e=>{let[s,t]=e,a=y.find(e=>e.value===s),n=(t/g.totalNodes*100).toFixed(1);return(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("span",{className:"text-xl mr-3",children:(null==a?void 0:a.icon)||"\uD83D\uDCC4"}),(0,l.jsx)("span",{className:"font-medium",children:(null==a?void 0:a.label)||s})]}),(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"w-24 bg-gray-200 rounded-full h-2 mr-3",children:(0,l.jsx)("div",{className:"bg-indigo-600 h-2 rounded-full",style:{width:"".concat(n,"%")}})}),(0,l.jsx)("span",{className:"text-sm font-medium w-12 text-right",children:t})]})]},s)})})]}),(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,l.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Relationships by Type"}),(0,l.jsx)("div",{className:"space-y-4",children:[{type:"works_for",label:"Employment",icon:"\uD83D\uDCBC",color:"bg-blue-500"},{type:"posts",label:"Job Postings",icon:"\uD83D\uDCDD",color:"bg-emerald-500"},{type:"requires",label:"Skill Requirements",icon:"\uD83C\uDFAF",color:"bg-amber-500"},{type:"has_skill",label:"Skill Possession",icon:"⭐",color:"bg-purple-500"},{type:"matched_to",label:"Job Matches",icon:"\uD83E\uDD1D",color:"bg-red-500"}].map(e=>{var s;let t=(null==(s=g.linksByType)?void 0:s[e.type])||0,a=g.totalLinks>0?(t/g.totalLinks*100).toFixed(1):0;return(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("span",{className:"text-xl mr-3",children:e.icon}),(0,l.jsx)("span",{className:"font-medium",children:e.label})]}),(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"w-24 bg-gray-200 rounded-full h-2 mr-3",children:(0,l.jsx)("div",{className:"h-2 rounded-full ".concat(e.color),style:{width:"".concat(a,"%")}})}),(0,l.jsx)("span",{className:"text-sm font-medium w-12 text-right",children:t})]})]},e.type)})})]})]}),(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,l.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Network Insights"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,l.jsxs)("div",{className:"text-center p-4 bg-emerald-50 rounded-lg",children:[(0,l.jsx)("span",{className:"text-3xl block mb-2",children:"\uD83C\uDFAF"}),(0,l.jsx)("h3",{className:"font-medium text-emerald-800",children:"High Match Potential"}),(0,l.jsx)("p",{className:"text-sm text-emerald-600 mt-1",children:"Strong skill alignment across the network"})]}),(0,l.jsxs)("div",{className:"text-center p-4 bg-amber-50 rounded-lg",children:[(0,l.jsx)("span",{className:"text-3xl block mb-2",children:"\uD83D\uDCC8"}),(0,l.jsx)("h3",{className:"font-medium text-amber-800",children:"Growing Connections"}),(0,l.jsx)("p",{className:"text-sm text-amber-600 mt-1",children:"Network density increasing over time"})]}),(0,l.jsxs)("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[(0,l.jsx)("span",{className:"text-3xl block mb-2",children:"\uD83D\uDD0D"}),(0,l.jsx)("h3",{className:"font-medium text-blue-800",children:"Skill Gaps Identified"}),(0,l.jsx)("p",{className:"text-sm text-blue-600 mt-1",children:"Opportunities for talent development"})]})]})]})]}),(0,l.jsx)(d.A,{isOpen:h,onClose:()=>u(!1),data:v,title:"Global Network - ".concat((null==(e=y.find(e=>e.value===p))?void 0:e.label)||"All Entities")})]})}},6234:(e,s,t)=>{"use strict";t.d(s,{A:()=>d});var l=t(7876),a=t(8230),n=t.n(a),i=t(9099);function r(){let e=(0,i.useRouter)();return(0,l.jsx)("nav",{className:"bg-white shadow-md",children:(0,l.jsx)("div",{className:"container mx-auto px-4",children:(0,l.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,l.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,l.jsx)(n(),{href:"/",className:"font-bold text-xl text-indigo-600",children:"Candid Connections Katra"})}),(0,l.jsx)("div",{className:"hidden md:block",children:(0,l.jsx)("div",{className:"ml-10 flex items-center space-x-4",children:[{name:"Dashboard",path:"/"},{name:"Job Matches",path:"/matches"},{name:"Job Seekers",path:"/job-seekers"},{name:"Companies",path:"/companies"},{name:"Positions",path:"/positions"},{name:"Skills",path:"/skills"}].map(s=>(0,l.jsx)(n(),{href:s.path,className:"px-3 py-2 rounded-md text-sm font-medium ".concat(e.pathname===s.path?"bg-indigo-100 text-indigo-700":"text-gray-700 hover:bg-gray-100"),children:s.name},s.path))})})]})})})}function d(e){let{children:s}=e;return(0,l.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,l.jsx)(r,{}),(0,l.jsx)("main",{children:s})]})}},8512:(e,s,t)=>{"use strict";t.d(s,{_:()=>l});let l=()=>{let e=Array.from({length:5},(e,s)=>({id:"jobSeekers/".concat(s),name:"Job Seeker ".concat(s+1),type:"jobSeeker",size:8})),s=Array.from({length:3},(e,s)=>({id:"companies/".concat(s),name:"Company ".concat(s+1),type:"company",size:10})),t=Array.from({length:4},(e,s)=>({id:"positions/".concat(s),name:"Position ".concat(s+1),type:"position",size:9})),l=Array.from({length:8},(e,s)=>({id:"skills/".concat(s),name:"Skill ".concat(s+1),type:"skill",size:6})),a=[...e,...s,...t,...l],n=[];return e.forEach((e,t)=>{n.push({source:e.id,target:s[t%s.length].id,type:"works_for",value:1})}),s.forEach((e,s)=>{t.forEach((t,l)=>{(s+l)%2==0&&n.push({source:e.id,target:t.id,type:"posts",value:1})})}),e.forEach(e=>{let s=2+Math.floor(3*Math.random());[...l].sort(()=>.5-Math.random()).slice(0,s).forEach(s=>{n.push({source:e.id,target:s.id,type:"has_skill",value:1})})}),t.forEach(e=>{let s=2+Math.floor(2*Math.random());[...l].sort(()=>.5-Math.random()).slice(0,s).forEach(s=>{n.push({source:e.id,target:s.id,type:"requires",value:1})})}),{nodes:a,links:n}}}},e=>{var s=s=>e(e.s=s);e.O(0,[305,70,636,593,792],()=>s(234)),_N_E=e.O()}]);