(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[227],{3776:(e,i,s)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/hiring-authorities",function(){return s(6420)}])},6234:(e,i,s)=>{"use strict";s.d(i,{A:()=>o});var n=s(7876),a=s(8230),r=s.n(a),t=s(9099),c=s(4232);function l(){let e=(0,t.useRouter)(),[i,s]=(0,c.useState)(!1),a=[{name:"Dashboard",path:"/",icon:"\uD83C\uDFE0"},{name:"Authority Matches",path:"/matches",icon:"\uD83C\uDFAF"},{name:"Job Seekers",path:"/job-seekers",icon:"\uD83D\uDC65"},{name:"Hiring Authorities",path:"/hiring-authorities",icon:"\uD83D\uDC54"},{name:"Companies",path:"/companies",icon:"\uD83C\uDFE2"},{name:"Positions",path:"/positions",icon:"\uD83D\uDCCB"},{name:"Skills",path:"/skills",icon:"\uD83D\uDEE0️"},{name:"Visualizations",path:"/visualizations",icon:"\uD83D\uDCCA"},{name:"Network View",path:"/global-view",icon:"\uD83C\uDF10"}];return(0,n.jsx)("nav",{className:"bg-white shadow-soft border-b border-candid-gray-200",children:(0,n.jsxs)("div",{className:"container-app",children:[(0,n.jsxs)("div",{className:"flex justify-between items-center h-20",children:[(0,n.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,n.jsxs)(r(),{href:"/",className:"flex items-center space-x-3 group",children:[(0,n.jsx)("div",{className:"w-12 h-12 relative group-hover:scale-105 transition-transform duration-200",children:(0,n.jsxs)("svg",{viewBox:"0 0 48 48",className:"w-full h-full",children:[(0,n.jsx)("circle",{cx:"24",cy:"24",r:"22",fill:"none",stroke:"#1e3a8a",strokeWidth:"2"}),(0,n.jsx)("circle",{cx:"24",cy:"12",r:"3",fill:"#00d4ff"}),(0,n.jsx)("circle",{cx:"36",cy:"24",r:"3",fill:"#00d4ff"}),(0,n.jsx)("circle",{cx:"24",cy:"36",r:"3",fill:"#00d4ff"}),(0,n.jsx)("circle",{cx:"12",cy:"24",r:"3",fill:"#00d4ff"}),(0,n.jsx)("circle",{cx:"24",cy:"24",r:"4",fill:"#1e3a8a"}),(0,n.jsx)("line",{x1:"24",y1:"15",x2:"24",y2:"20",stroke:"#00d4ff",strokeWidth:"2"}),(0,n.jsx)("line",{x1:"33",y1:"24",x2:"28",y2:"24",stroke:"#00d4ff",strokeWidth:"2"}),(0,n.jsx)("line",{x1:"24",y1:"33",x2:"24",y2:"28",stroke:"#00d4ff",strokeWidth:"2"}),(0,n.jsx)("line",{x1:"15",y1:"24",x2:"20",y2:"24",stroke:"#00d4ff",strokeWidth:"2"})]})}),(0,n.jsxs)("div",{className:"hidden sm:block",children:[(0,n.jsx)("h1",{className:"text-xl font-bold text-secondary-800 group-hover:text-primary-500 transition-colors duration-200",children:"Candid Connections"}),(0,n.jsx)("p",{className:"text-sm text-candid-gray-600 -mt-1",children:"Katra Platform"})]})]})}),(0,n.jsx)("div",{className:"hidden lg:block",children:(0,n.jsx)("div",{className:"flex items-center space-x-1",children:a.map(i=>(0,n.jsxs)(r(),{href:i.path,className:"flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ".concat(e.pathname===i.path?"nav-link-active":"nav-link"),children:[(0,n.jsx)("span",{className:"text-base",children:i.icon}),(0,n.jsx)("span",{children:i.name})]},i.path))})}),(0,n.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[(0,n.jsx)(r(),{href:"/admin",className:"btn-outline text-sm py-2 px-4",children:"⚙️ Admin"}),(0,n.jsx)(r(),{href:"/global-view",className:"btn-outline text-sm py-2 px-4",children:"\uD83C\uDF10 Network View"}),(0,n.jsx)(r(),{href:"https://portal.candid-connections.com/user/login",target:"_blank",rel:"noopener noreferrer",className:"btn-primary text-sm py-2 px-4",children:"Portal Login"})]}),(0,n.jsx)("div",{className:"lg:hidden",children:(0,n.jsx)("button",{onClick:()=>s(!i),className:"p-2 rounded-lg text-candid-navy-600 hover:text-primary-600 hover:bg-primary-50 transition-colors duration-200","aria-label":"Toggle mobile menu",children:(0,n.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:i?(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})})]}),i&&(0,n.jsx)("div",{className:"lg:hidden border-t border-candid-gray-200 py-4",children:(0,n.jsxs)("div",{className:"space-y-2",children:[a.map(i=>(0,n.jsxs)(r(),{href:i.path,onClick:()=>s(!1),className:"flex items-center space-x-3 px-4 py-3 rounded-lg text-base font-medium transition-all duration-200 ".concat(e.pathname===i.path?"nav-link-active":"nav-link"),children:[(0,n.jsx)("span",{className:"text-lg",children:i.icon}),(0,n.jsx)("span",{children:i.name})]},i.path)),(0,n.jsxs)("div",{className:"pt-4 space-y-2",children:[(0,n.jsx)(r(),{href:"/global-view",onClick:()=>s(!1),className:"block w-full btn-outline text-center",children:"\uD83C\uDF10 Network View"}),(0,n.jsx)(r(),{href:"https://portal.candid-connections.com/user/login",target:"_blank",rel:"noopener noreferrer",className:"block w-full btn-primary text-center",children:"Portal Login"})]})]})})]})})}function o(e){let{children:i}=e;return(0,n.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,n.jsx)(l,{}),(0,n.jsx)("main",{className:"container-app section-padding",children:i})]})}},6420:(e,i,s)=>{"use strict";s.r(i),s.d(i,{default:()=>d});var n=s(7876),a=s(4232),r=s(7328),t=s.n(r),c=s(8230),l=s.n(c),o=s(6234);function d(){let[e,i]=(0,a.useState)([]),[s,r]=(0,a.useState)(!0),[c,d]=(0,a.useState)({role:"",companySize:"",industry:""});(0,a.useEffect)(()=>{(async()=>{try{setTimeout(()=>{i([{id:"auth_1",name:"Sarah Wilson",role:"VP Engineering",level:"Executive",company:"TechCorp Inc.",companySize:"Enterprise (1000+)",industry:"Technology",email:"<EMAIL>",hiringPower:"High",activePositions:5,skillsLookingFor:["React","Node.js","Python","AWS","Leadership"],preferredExperience:"5-10 years",decisionMaker:!0,avatar:"\uD83D\uDC69‍\uD83D\uDCBC",connectionStrength:92},{id:"auth_2",name:"Mike Chen",role:"Director of Product",level:"Director",company:"TechCorp Inc.",companySize:"Enterprise (1000+)",industry:"Technology",email:"<EMAIL>",hiringPower:"Medium",activePositions:3,skillsLookingFor:["Product Management","UX/UI","Analytics","Agile"],preferredExperience:"3-7 years",decisionMaker:!1,avatar:"\uD83D\uDC68‍\uD83D\uDCBC",connectionStrength:87},{id:"auth_3",name:"Jennifer Rodriguez",role:"CEO",level:"C-Suite",company:"StartupFlow",companySize:"Startup (<100)",industry:"FinTech",email:"<EMAIL>",hiringPower:"Ultimate",activePositions:8,skillsLookingFor:["Full Stack","Blockchain","Finance","Startup Experience"],preferredExperience:"2-8 years",decisionMaker:!0,avatar:"\uD83D\uDC69‍\uD83D\uDCBC",connectionStrength:95},{id:"auth_4",name:"David Park",role:"HR Director",level:"Director",company:"MegaCorp Industries",companySize:"Enterprise (1000+)",industry:"Manufacturing",email:"<EMAIL>",hiringPower:"Medium",activePositions:12,skillsLookingFor:["Operations","Six Sigma","Project Management","Engineering"],preferredExperience:"3-10 years",decisionMaker:!1,avatar:"\uD83D\uDC68‍\uD83D\uDCBC",connectionStrength:78},{id:"auth_5",name:"Lisa Thompson",role:"CTO",level:"C-Suite",company:"InnovateTech",companySize:"Mid-size (100-1000)",industry:"Technology",email:"<EMAIL>",hiringPower:"High",activePositions:6,skillsLookingFor:["Architecture","DevOps","Machine Learning","Team Leadership"],preferredExperience:"7-15 years",decisionMaker:!0,avatar:"\uD83D\uDC69‍\uD83D\uDCBC",connectionStrength:89}]),r(!1)},1e3)}catch(e){console.error("Error fetching authorities:",e),r(!1)}})()},[]);let h=e=>{switch(e){case"Ultimate":return"bg-red-100 text-red-800";case"High":return"bg-orange-100 text-orange-800";case"Medium":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}},x=e=>e.includes("Startup")?"bg-green-100 text-green-800":e.includes("Mid-size")?"bg-blue-100 text-blue-800":e.includes("Enterprise")?"bg-purple-100 text-purple-800":"bg-gray-100 text-gray-800",m=e.filter(e=>(""===c.role||e.level.toLowerCase().includes(c.role.toLowerCase()))&&(""===c.companySize||e.companySize.includes(c.companySize))&&(""===c.industry||e.industry.toLowerCase().includes(c.industry.toLowerCase())));return(0,n.jsxs)(o.A,{children:[(0,n.jsxs)(t(),{children:[(0,n.jsx)("title",{children:"Hiring Authorities | Candid Connections Katra"}),(0,n.jsx)("meta",{name:"description",content:"Connect with the right hiring authorities based on company hierarchy and decision-making power."})]}),(0,n.jsxs)("div",{className:"space-y-8",children:[(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("h1",{className:"text-4xl font-bold text-secondary-800 mb-4",children:"Hiring Authorities"}),(0,n.jsx)("p",{className:"text-xl text-candid-gray-600 max-w-3xl mx-auto",children:"Connect directly with decision makers. Our graph database maps company hierarchies to identify the right hiring authority for your skills and experience level."})]}),(0,n.jsx)("div",{className:"card",children:(0,n.jsxs)("div",{className:"card-body",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-secondary-800 mb-4",children:"Filter Authorities"}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"form-label",children:"Authority Level"}),(0,n.jsxs)("select",{className:"form-input",value:c.role,onChange:e=>d({...c,role:e.target.value}),children:[(0,n.jsx)("option",{value:"",children:"All Levels"}),(0,n.jsx)("option",{value:"C-Suite",children:"C-Suite"}),(0,n.jsx)("option",{value:"Executive",children:"Executive"}),(0,n.jsx)("option",{value:"Director",children:"Director"}),(0,n.jsx)("option",{value:"Manager",children:"Manager"})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"form-label",children:"Company Size"}),(0,n.jsxs)("select",{className:"form-input",value:c.companySize,onChange:e=>d({...c,companySize:e.target.value}),children:[(0,n.jsx)("option",{value:"",children:"All Sizes"}),(0,n.jsx)("option",{value:"Startup",children:"Startup (<100)"}),(0,n.jsx)("option",{value:"Mid-size",children:"Mid-size (100-1000)"}),(0,n.jsx)("option",{value:"Enterprise",children:"Enterprise (1000+)"})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"form-label",children:"Industry"}),(0,n.jsxs)("select",{className:"form-input",value:c.industry,onChange:e=>d({...c,industry:e.target.value}),children:[(0,n.jsx)("option",{value:"",children:"All Industries"}),(0,n.jsx)("option",{value:"Technology",children:"Technology"}),(0,n.jsx)("option",{value:"FinTech",children:"FinTech"}),(0,n.jsx)("option",{value:"Manufacturing",children:"Manufacturing"}),(0,n.jsx)("option",{value:"Healthcare",children:"Healthcare"})]})]})]})]})}),s?(0,n.jsxs)("div",{className:"text-center py-12",children:[(0,n.jsx)("div",{className:"loading-spinner w-8 h-8 mx-auto mb-4"}),(0,n.jsx)("p",{className:"text-candid-gray-600",children:"Loading hiring authorities..."})]}):(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:m.map(e=>(0,n.jsx)("div",{className:"card-interactive",children:(0,n.jsxs)("div",{className:"card-body",children:[(0,n.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsx)("div",{className:"text-3xl",children:e.avatar}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-semibold text-secondary-800",children:e.name}),(0,n.jsx)("p",{className:"text-sm text-candid-gray-600",children:e.role})]})]}),(0,n.jsxs)("div",{className:"text-right",children:[(0,n.jsxs)("div",{className:"text-2xl font-bold text-primary-600",children:[e.connectionStrength,"%"]}),(0,n.jsx)("div",{className:"text-xs text-candid-gray-500",children:"Match Score"})]})]}),(0,n.jsxs)("div",{className:"mb-4",children:[(0,n.jsx)("p",{className:"font-medium text-secondary-700",children:e.company}),(0,n.jsxs)("div",{className:"flex flex-wrap gap-2 mt-2",children:[(0,n.jsx)("span",{className:"badge ".concat(x(e.companySize)),children:e.companySize}),(0,n.jsxs)("span",{className:"badge ".concat(h(e.hiringPower)),children:[e.hiringPower," Power"]}),e.decisionMaker&&(0,n.jsx)("span",{className:"badge bg-green-100 text-green-800",children:"Decision Maker"})]})]}),(0,n.jsxs)("div",{className:"mb-4",children:[(0,n.jsx)("p",{className:"text-sm font-medium text-secondary-700 mb-2",children:"Looking for:"}),(0,n.jsxs)("div",{className:"flex flex-wrap gap-1",children:[e.skillsLookingFor.slice(0,3).map((e,i)=>(0,n.jsx)("span",{className:"badge badge-primary text-xs",children:e},i)),e.skillsLookingFor.length>3&&(0,n.jsxs)("span",{className:"badge badge-secondary text-xs",children:["+",e.skillsLookingFor.length-3," more"]})]})]}),(0,n.jsxs)("div",{className:"flex justify-between text-sm text-candid-gray-600 mb-4",children:[(0,n.jsxs)("span",{children:[e.activePositions," open positions"]}),(0,n.jsxs)("span",{children:[e.preferredExperience," exp."]})]}),(0,n.jsxs)("div",{className:"flex space-x-2",children:[(0,n.jsx)(l(),{href:"/hiring-authorities/".concat(e.id),className:"btn-primary text-sm py-2 px-4 flex-1 text-center",children:"View Profile"}),(0,n.jsx)("button",{className:"btn-outline text-sm py-2 px-4",children:"Connect"})]})]})},e.id))}),0===m.length&&!s&&(0,n.jsx)("div",{className:"text-center py-12",children:(0,n.jsx)("p",{className:"text-candid-gray-600",children:"No hiring authorities match your current filters."})})]})]})}}},e=>{var i=i=>e(e.s=i);e.O(0,[305,636,593,792],()=>i(3776)),_N_E=e.O()}]);