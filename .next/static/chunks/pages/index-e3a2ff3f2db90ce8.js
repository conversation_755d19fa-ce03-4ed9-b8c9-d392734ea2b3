(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[332],{2936:(e,t,a)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/",function(){return a(7915)}])},6234:(e,t,a)=>{"use strict";a.d(t,{A:()=>d});var s=a(7876),n=a(8230),i=a.n(n),r=a(9099),l=a(4232);function c(){let e=(0,r.useRouter)(),[t,a]=(0,l.useState)(!1),n=[{name:"Dashboard",path:"/",icon:"\uD83C\uDFE0"},{name:"Job Matches",path:"/matches",icon:"\uD83C\uDFAF"},{name:"Job Seekers",path:"/job-seekers",icon:"\uD83D\uDC65"},{name:"Companies",path:"/companies",icon:"\uD83C\uDFE2"},{name:"Positions",path:"/positions",icon:"\uD83D\uDCCB"},{name:"Skills",path:"/skills",icon:"\uD83D\uDEE0️"},{name:"Global View",path:"/global-view",icon:"\uD83C\uDF10"}];return(0,s.jsx)("nav",{className:"bg-white shadow-soft border-b border-candid-gray-200",children:(0,s.jsxs)("div",{className:"container-app",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center h-20",children:[(0,s.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,s.jsxs)(i(),{href:"/",className:"flex items-center space-x-3 group",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-primary-500 to-accent-500 rounded-xl flex items-center justify-center shadow-soft group-hover:shadow-medium transition-all duration-200",children:(0,s.jsx)("span",{className:"text-white font-bold text-lg",children:"C"})}),(0,s.jsxs)("div",{className:"hidden sm:block",children:[(0,s.jsx)("h1",{className:"text-xl font-bold text-candid-navy-900 group-hover:text-primary-600 transition-colors duration-200",children:"Candid Connections"}),(0,s.jsx)("p",{className:"text-sm text-candid-gray-600 -mt-1",children:"Katra Platform"})]})]})}),(0,s.jsx)("div",{className:"hidden lg:block",children:(0,s.jsx)("div",{className:"flex items-center space-x-1",children:n.map(t=>(0,s.jsxs)(i(),{href:t.path,className:"flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ".concat(e.pathname===t.path?"nav-link-active":"nav-link"),children:[(0,s.jsx)("span",{className:"text-base",children:t.icon}),(0,s.jsx)("span",{children:t.name})]},t.path))})}),(0,s.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[(0,s.jsx)(i(),{href:"/global-view",className:"btn-outline text-sm py-2 px-4",children:"\uD83C\uDF10 Network View"}),(0,s.jsx)(i(),{href:"https://portal.candid-connections.com/user/login",target:"_blank",rel:"noopener noreferrer",className:"btn-primary text-sm py-2 px-4",children:"Portal Login"})]}),(0,s.jsx)("div",{className:"lg:hidden",children:(0,s.jsx)("button",{onClick:()=>a(!t),className:"p-2 rounded-lg text-candid-navy-600 hover:text-primary-600 hover:bg-primary-50 transition-colors duration-200","aria-label":"Toggle mobile menu",children:(0,s.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:t?(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})})]}),t&&(0,s.jsx)("div",{className:"lg:hidden border-t border-candid-gray-200 py-4",children:(0,s.jsxs)("div",{className:"space-y-2",children:[n.map(t=>(0,s.jsxs)(i(),{href:t.path,onClick:()=>a(!1),className:"flex items-center space-x-3 px-4 py-3 rounded-lg text-base font-medium transition-all duration-200 ".concat(e.pathname===t.path?"nav-link-active":"nav-link"),children:[(0,s.jsx)("span",{className:"text-lg",children:t.icon}),(0,s.jsx)("span",{children:t.name})]},t.path)),(0,s.jsxs)("div",{className:"pt-4 space-y-2",children:[(0,s.jsx)(i(),{href:"/global-view",onClick:()=>a(!1),className:"block w-full btn-outline text-center",children:"\uD83C\uDF10 Network View"}),(0,s.jsx)(i(),{href:"https://portal.candid-connections.com/user/login",target:"_blank",rel:"noopener noreferrer",className:"block w-full btn-primary text-center",children:"Portal Login"})]})]})})]})})}function d(e){let{children:t}=e;return(0,s.jsxs)("div",{className:"min-h-screen bg-candid-gray-50",children:[(0,s.jsx)(c,{}),(0,s.jsx)("main",{className:"container-app section-padding",children:t})]})}},7915:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>x});var s=a(7876),n=a(7328),i=a.n(n),r=a(6234),l=a(8230),c=a.n(l),d=a(4232);function o(){let[e,t]=(0,d.useState)({}),[a,n]=(0,d.useState)(!0);(0,d.useEffect)(()=>{setTimeout(()=>{t({matches:47,jobSeekers:156,companies:23,positions:89,skills:127,globalConnections:342}),n(!1)},1e3)},[]);let i=[{title:"Job Matches",icon:"\uD83C\uDFAF",gradient:"from-primary-500 to-primary-600",path:"/matches",description:"View and manage job seeker-position matches",stat:e.matches,statLabel:"Active Matches"},{title:"Job Seekers",icon:"\uD83D\uDC65",gradient:"from-secondary-500 to-secondary-600",path:"/job-seekers",description:"Manage candidates and their skills",stat:e.jobSeekers,statLabel:"Candidates"},{title:"Companies",icon:"\uD83C\uDFE2",gradient:"from-candid-blue-500 to-candid-blue-600",path:"/companies",description:"Explore organizational hierarchies",stat:e.companies,statLabel:"Organizations"},{title:"Positions",icon:"\uD83D\uDCCB",gradient:"from-accent-500 to-accent-600",path:"/positions",description:"Browse open positions and requirements",stat:e.positions,statLabel:"Open Roles"},{title:"Skills",icon:"\uD83D\uDEE0️",gradient:"from-candid-orange-500 to-candid-orange-600",path:"/skills",description:"Analyze market demand and skill trends",stat:e.skills,statLabel:"Skills Tracked"},{title:"Global View",icon:"\uD83C\uDF10",gradient:"from-candid-navy-600 to-candid-navy-700",path:"/global-view",description:"Complete network visualization",stat:e.globalConnections,statLabel:"Connections"}];return(0,s.jsx)("div",{className:"dashboard-grid",children:i.map(e=>(0,s.jsx)(c(),{href:e.path,className:"card-interactive group",children:(0,s.jsxs)("div",{className:"card-body",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br ".concat(e.gradient," rounded-xl flex items-center justify-center shadow-soft group-hover:shadow-medium transition-all duration-200"),children:(0,s.jsx)("span",{className:"text-white text-xl",children:e.icon})}),(0,s.jsx)("div",{className:"text-right",children:a?(0,s.jsx)("div",{className:"w-8 h-8 loading-spinner"}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-candid-navy-900",children:e.stat}),(0,s.jsx)("div",{className:"text-xs text-candid-gray-500 uppercase tracking-wide",children:e.statLabel})]})})]}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-candid-navy-900 mb-2 group-hover:text-primary-600 transition-colors duration-200",children:e.title}),(0,s.jsx)("p",{className:"text-candid-gray-600 text-sm leading-relaxed",children:e.description}),(0,s.jsxs)("div",{className:"mt-4 flex items-center text-primary-600 text-sm font-medium group-hover:text-primary-700 transition-colors duration-200",children:[(0,s.jsx)("span",{children:"Explore"}),(0,s.jsx)("svg",{className:"w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform duration-200",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})]})},e.title))})}function x(){return(0,s.jsxs)(r.A,{children:[(0,s.jsxs)(i(),{children:[(0,s.jsx)("title",{children:"Candid Connections Katra | Professional Talent Matching Platform"}),(0,s.jsx)("meta",{name:"description",content:"Advanced graph-based talent matching platform connecting job seekers with opportunities through intelligent relationship mapping."}),(0,s.jsx)("meta",{name:"keywords",content:"job matching, talent platform, career connections, hiring, recruitment"})]}),(0,s.jsxs)("div",{className:"text-center mb-12",children:[(0,s.jsxs)("h1",{className:"text-4xl lg:text-5xl font-bold text-candid-navy-900 mb-4",children:["Welcome to ",(0,s.jsx)("span",{className:"text-gradient",children:"Candid Connections"})]}),(0,s.jsx)("p",{className:"text-xl text-candid-gray-600 max-w-3xl mx-auto leading-relaxed",children:"The intelligent talent matching platform that visualizes connections between job seekers, companies, positions, and skills through advanced graph database technology."}),(0,s.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-6 mt-8 max-w-4xl mx-auto",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-3xl font-bold text-primary-600",children:"156"}),(0,s.jsx)("div",{className:"text-sm text-candid-gray-500 uppercase tracking-wide",children:"Job Seekers"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-3xl font-bold text-secondary-600",children:"23"}),(0,s.jsx)("div",{className:"text-sm text-candid-gray-500 uppercase tracking-wide",children:"Companies"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-3xl font-bold text-accent-600",children:"89"}),(0,s.jsx)("div",{className:"text-sm text-candid-gray-500 uppercase tracking-wide",children:"Open Positions"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-3xl font-bold text-candid-blue-600",children:"342"}),(0,s.jsx)("div",{className:"text-sm text-candid-gray-500 uppercase tracking-wide",children:"Connections"})]})]})]}),(0,s.jsxs)("div",{className:"mb-12",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold text-candid-navy-900 mb-8 text-center",children:"Explore the Platform"}),(0,s.jsx)(o,{})]}),(0,s.jsx)("div",{className:"card",children:(0,s.jsxs)("div",{className:"card-body",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold text-candid-navy-900 mb-6 text-center",children:"Platform Features"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-soft",children:(0,s.jsx)("span",{className:"text-white text-2xl",children:"\uD83C\uDFAF"})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-candid-navy-900 mb-2",children:"Smart Matching"}),(0,s.jsx)("p",{className:"text-candid-gray-600 text-sm",children:"AI-powered algorithms analyze skills, experience, and preferences to create optimal job matches."})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-soft",children:(0,s.jsx)("span",{className:"text-white text-2xl",children:"\uD83C\uDF10"})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-candid-navy-900 mb-2",children:"Network Visualization"}),(0,s.jsx)("p",{className:"text-candid-gray-600 text-sm",children:"Interactive 2D and 3D visualizations reveal hidden connections and opportunities in the job market."})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-accent-500 to-accent-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-soft",children:(0,s.jsx)("span",{className:"text-white text-2xl",children:"\uD83D\uDCCA"})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-candid-navy-900 mb-2",children:"Market Analytics"}),(0,s.jsx)("p",{className:"text-candid-gray-600 text-sm",children:"Real-time insights into skill demand, salary trends, and market opportunities."})]})]})]})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[305,636,593,792],()=>t(2936)),_N_E=e.O()}]);