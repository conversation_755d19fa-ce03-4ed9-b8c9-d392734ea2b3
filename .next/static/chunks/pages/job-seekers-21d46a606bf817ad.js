(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[854],{73:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var a=s(7876),r=s(4232),n=s(7328),l=s.n(n),i=s(6234),c=s(1467);function d(){let[e,t]=(0,r.useState)([]),[s,n]=(0,r.useState)(!0),[d,o]=(0,r.useState)(null),[h,u]=(0,r.useState)(null);return(0,r.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/job-seekers");if(!e.ok)throw Error("Failed to fetch job seekers");let s=await e.json();t(s);let a=s.map(e=>({id:e._key,name:e.name,type:"jobSeeker",size:8})),r=[];if(a.length>1)for(let e=0;e<a.length-1;e++)r.push({source:a[e].id,target:a[e+1].id,value:1});u({nodes:a,links:r})}catch(e){o(e.message)}finally{n(!1)}})()},[]),(0,a.jsxs)(i.A,{children:[(0,a.jsx)(l(),{children:(0,a.jsx)("title",{children:"Job Seekers | Candid Connections Katra"})}),(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold mb-6",children:"Job Seekers"}),s&&(0,a.jsx)("p",{children:"Loading job seekers..."}),d&&(0,a.jsxs)("p",{className:"text-red-500",children:["Error: ",d]}),!s&&!d&&(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsx)("div",{className:"lg:col-span-1",children:(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Job Seekers List"}),0===e.length?(0,a.jsx)("p",{children:"No job seekers found."}):(0,a.jsx)("ul",{className:"divide-y",children:e.map(e=>(0,a.jsxs)("li",{className:"py-3",children:[(0,a.jsx)("p",{className:"font-medium",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:e.title})]},e._key))})]})}),(0,a.jsx)("div",{className:"lg:col-span-2",children:(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Network Visualization"}),h?(0,a.jsx)(c.A,{data:h}):(0,a.jsx)("p",{children:"No visualization data available."})]})})]})]})]})}},1467:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var a=s(7876),r=s(4232),n=s(7070);function l(e){let{data:t}=e,s=(0,r.useRef)(null);return(0,r.useEffect)(()=>{var e;if(!t||!s.current)return;n.Ltv(s.current).selectAll("*").remove();let a=n.Ltv(s.current).attr("width",800).attr("height",600).attr("viewBox",[0,0,800,600]),r=n.tXi(t.nodes).force("link",n.kJC(t.links).id(e=>e.id)).force("charge",n.xJS().strength(-400)).force("center",n.jTM(400,300)),l=a.append("g").selectAll("line").data(t.links).join("line").attr("stroke","#999").attr("stroke-opacity",.6).attr("stroke-width",e=>Math.sqrt(e.value||1)),i=a.append("g").selectAll("circle").data(t.nodes).join("circle").attr("r",e=>e.size||5).attr("fill",e=>{switch(e.type){case"jobSeeker":return"#3b82f6";case"company":return"#14b8a6";case"position":return"#10b981";case"skill":return"#f59e0b";default:return"#6366f1"}}).call((e=r,n.$Er().on("start",function(t){t.active||e.alphaTarget(.3).restart(),t.subject.fx=t.subject.x,t.subject.fy=t.subject.y}).on("drag",function(e){e.subject.fx=e.x,e.subject.fy=e.y}).on("end",function(t){t.active||e.alphaTarget(0),t.subject.fx=null,t.subject.fy=null})));return i.append("title").text(e=>e.name),r.on("tick",()=>{l.attr("x1",e=>e.source.x).attr("y1",e=>e.source.y).attr("x2",e=>e.target.x).attr("y2",e=>e.target.y),i.attr("cx",e=>e.x).attr("cy",e=>e.y)}),()=>{r.stop()}},[t]),(0,a.jsx)("div",{className:"border rounded-lg shadow-sm bg-white p-4",children:(0,a.jsx)("svg",{ref:s,className:"w-full h-full"})})}},6234:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var a=s(7876),r=s(8230),n=s.n(r),l=s(9099);function i(){let e=(0,l.useRouter)();return(0,a.jsx)("nav",{className:"bg-white shadow-md",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,a.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,a.jsx)(n(),{href:"/",className:"font-bold text-xl text-indigo-600",children:"Candid Connections Katra"})}),(0,a.jsx)("div",{className:"hidden md:block",children:(0,a.jsx)("div",{className:"ml-10 flex items-center space-x-4",children:[{name:"Dashboard",path:"/"},{name:"Job Matches",path:"/matches"},{name:"Job Seekers",path:"/job-seekers"},{name:"Companies",path:"/companies"},{name:"Positions",path:"/positions"},{name:"Skills",path:"/skills"}].map(t=>(0,a.jsx)(n(),{href:t.path,className:"px-3 py-2 rounded-md text-sm font-medium ".concat(e.pathname===t.path?"bg-indigo-100 text-indigo-700":"text-gray-700 hover:bg-gray-100"),children:t.name},t.path))})})]})})})}function c(e){let{children:t}=e;return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)(i,{}),(0,a.jsx)("main",{children:t})]})}},8428:(e,t,s)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/job-seekers",function(){return s(73)}])}},e=>{var t=t=>e(e.s=t);e.O(0,[305,70,636,593,792],()=>t(8428)),_N_E=e.O()}]);