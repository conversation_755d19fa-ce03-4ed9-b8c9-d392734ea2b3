(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[854],{73:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o});var a=t(7876),r=t(4232),n=t(7328),i=t.n(n),l=t(6234),c=t(1467);function o(){let[e,s]=(0,r.useState)([]),[t,n]=(0,r.useState)(!0),[o,d]=(0,r.useState)(null),[h,x]=(0,r.useState)(null);return(0,r.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/job-seekers");if(!e.ok)throw Error("Failed to fetch job seekers");let t=await e.json();s(t);let a=t.map(e=>({id:e._key,name:e.name,type:"jobSeeker",size:8})),r=[];if(a.length>1)for(let e=0;e<a.length-1;e++)r.push({source:a[e].id,target:a[e+1].id,value:1});x({nodes:a,links:r})}catch(e){d(e.message)}finally{n(!1)}})()},[]),(0,a.jsxs)(l.A,{children:[(0,a.jsx)(i(),{children:(0,a.jsx)("title",{children:"Job Seekers | Candid Connections Katra"})}),(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold mb-6",children:"Job Seekers"}),t&&(0,a.jsx)("p",{children:"Loading job seekers..."}),o&&(0,a.jsxs)("p",{className:"text-red-500",children:["Error: ",o]}),!t&&!o&&(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsx)("div",{className:"lg:col-span-1",children:(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Job Seekers List"}),0===e.length?(0,a.jsx)("p",{children:"No job seekers found."}):(0,a.jsx)("ul",{className:"divide-y",children:e.map(e=>(0,a.jsxs)("li",{className:"py-3",children:[(0,a.jsx)("p",{className:"font-medium",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:e.title})]},e._key))})]})}),(0,a.jsx)("div",{className:"lg:col-span-2",children:(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Network Visualization"}),h?(0,a.jsx)(c.A,{data:h}):(0,a.jsx)("p",{children:"No visualization data available."})]})})]})]})]})}},1467:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var a=t(7876),r=t(4232),n=t(4274);function i(e){let{data:s}=e,t=(0,r.useRef)(null);return(0,r.useEffect)(()=>{var e;if(!s||!t.current)return;n.Ltv(t.current).selectAll("*").remove();let a=n.Ltv(t.current).attr("width",800).attr("height",600).attr("viewBox",[0,0,800,600]),r=n.tXi(s.nodes).force("link",n.kJC(s.links).id(e=>e.id)).force("charge",n.xJS().strength(-400)).force("center",n.jTM(400,300)),i=a.append("g").selectAll("line").data(s.links).join("line").attr("stroke","#999").attr("stroke-opacity",.6).attr("stroke-width",e=>Math.sqrt(e.value||1)),l=a.append("g").selectAll("circle").data(s.nodes).join("circle").attr("r",e=>e.size||5).attr("fill",e=>{switch(e.type){case"jobSeeker":return"#3b82f6";case"company":return"#14b8a6";case"position":return"#10b981";case"skill":return"#f59e0b";default:return"#6366f1"}}).call((e=r,n.$Er().on("start",function(s){s.active||e.alphaTarget(.3).restart(),s.subject.fx=s.subject.x,s.subject.fy=s.subject.y}).on("drag",function(e){e.subject.fx=e.x,e.subject.fy=e.y}).on("end",function(s){s.active||e.alphaTarget(0),s.subject.fx=null,s.subject.fy=null})));return l.append("title").text(e=>e.name),r.on("tick",()=>{i.attr("x1",e=>e.source.x).attr("y1",e=>e.source.y).attr("x2",e=>e.target.x).attr("y2",e=>e.target.y),l.attr("cx",e=>e.x).attr("cy",e=>e.y)}),()=>{r.stop()}},[s]),(0,a.jsx)("div",{className:"border rounded-lg shadow-sm bg-white p-4",children:(0,a.jsx)("svg",{ref:t,className:"w-full h-full"})})}},6234:(e,s,t)=>{"use strict";t.d(s,{A:()=>o});var a=t(7876),r=t(8230),n=t.n(r),i=t(9099),l=t(4232);function c(){let e=(0,i.useRouter)(),[s,t]=(0,l.useState)(!1),r=[{name:"Dashboard",path:"/",icon:"\uD83C\uDFE0"},{name:"Authority Matches",path:"/matches",icon:"\uD83C\uDFAF"},{name:"Job Seekers",path:"/job-seekers",icon:"\uD83D\uDC65"},{name:"Hiring Authorities",path:"/hiring-authorities",icon:"\uD83D\uDC54"},{name:"Companies",path:"/companies",icon:"\uD83C\uDFE2"},{name:"Positions",path:"/positions",icon:"\uD83D\uDCCB"},{name:"Skills",path:"/skills",icon:"\uD83D\uDEE0️"},{name:"Visualizations",path:"/visualizations",icon:"\uD83D\uDCCA"},{name:"Network View",path:"/global-view",icon:"\uD83C\uDF10"}];return(0,a.jsx)("nav",{className:"bg-white shadow-soft border-b border-candid-gray-200",children:(0,a.jsxs)("div",{className:"container-app",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center h-20",children:[(0,a.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,a.jsxs)(n(),{href:"/",className:"flex items-center space-x-3 group",children:[(0,a.jsx)("div",{className:"w-12 h-12 relative group-hover:scale-105 transition-transform duration-200",children:(0,a.jsxs)("svg",{viewBox:"0 0 48 48",className:"w-full h-full",children:[(0,a.jsx)("circle",{cx:"24",cy:"24",r:"22",fill:"none",stroke:"#1e3a8a",strokeWidth:"2"}),(0,a.jsx)("circle",{cx:"24",cy:"12",r:"3",fill:"#00d4ff"}),(0,a.jsx)("circle",{cx:"36",cy:"24",r:"3",fill:"#00d4ff"}),(0,a.jsx)("circle",{cx:"24",cy:"36",r:"3",fill:"#00d4ff"}),(0,a.jsx)("circle",{cx:"12",cy:"24",r:"3",fill:"#00d4ff"}),(0,a.jsx)("circle",{cx:"24",cy:"24",r:"4",fill:"#1e3a8a"}),(0,a.jsx)("line",{x1:"24",y1:"15",x2:"24",y2:"20",stroke:"#00d4ff",strokeWidth:"2"}),(0,a.jsx)("line",{x1:"33",y1:"24",x2:"28",y2:"24",stroke:"#00d4ff",strokeWidth:"2"}),(0,a.jsx)("line",{x1:"24",y1:"33",x2:"24",y2:"28",stroke:"#00d4ff",strokeWidth:"2"}),(0,a.jsx)("line",{x1:"15",y1:"24",x2:"20",y2:"24",stroke:"#00d4ff",strokeWidth:"2"})]})}),(0,a.jsxs)("div",{className:"hidden sm:block",children:[(0,a.jsx)("h1",{className:"text-xl font-bold text-secondary-800 group-hover:text-primary-500 transition-colors duration-200",children:"Candid Connections"}),(0,a.jsx)("p",{className:"text-sm text-candid-gray-600 -mt-1",children:"Katra Platform"})]})]})}),(0,a.jsx)("div",{className:"hidden lg:block",children:(0,a.jsx)("div",{className:"flex items-center space-x-1",children:r.map(s=>(0,a.jsxs)(n(),{href:s.path,className:"flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ".concat(e.pathname===s.path?"nav-link-active":"nav-link"),children:[(0,a.jsx)("span",{className:"text-base",children:s.icon}),(0,a.jsx)("span",{children:s.name})]},s.path))})}),(0,a.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[(0,a.jsx)(n(),{href:"/admin",className:"btn-outline text-sm py-2 px-4",children:"⚙️ Admin"}),(0,a.jsx)(n(),{href:"/global-view",className:"btn-outline text-sm py-2 px-4",children:"\uD83C\uDF10 Network View"}),(0,a.jsx)(n(),{href:"https://portal.candid-connections.com/user/login",target:"_blank",rel:"noopener noreferrer",className:"btn-primary text-sm py-2 px-4",children:"Portal Login"})]}),(0,a.jsx)("div",{className:"lg:hidden",children:(0,a.jsx)("button",{onClick:()=>t(!s),className:"p-2 rounded-lg text-candid-navy-600 hover:text-primary-600 hover:bg-primary-50 transition-colors duration-200","aria-label":"Toggle mobile menu",children:(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s?(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})})]}),s&&(0,a.jsx)("div",{className:"lg:hidden border-t border-candid-gray-200 py-4",children:(0,a.jsxs)("div",{className:"space-y-2",children:[r.map(s=>(0,a.jsxs)(n(),{href:s.path,onClick:()=>t(!1),className:"flex items-center space-x-3 px-4 py-3 rounded-lg text-base font-medium transition-all duration-200 ".concat(e.pathname===s.path?"nav-link-active":"nav-link"),children:[(0,a.jsx)("span",{className:"text-lg",children:s.icon}),(0,a.jsx)("span",{children:s.name})]},s.path)),(0,a.jsxs)("div",{className:"pt-4 space-y-2",children:[(0,a.jsx)(n(),{href:"/global-view",onClick:()=>t(!1),className:"block w-full btn-outline text-center",children:"\uD83C\uDF10 Network View"}),(0,a.jsx)(n(),{href:"https://portal.candid-connections.com/user/login",target:"_blank",rel:"noopener noreferrer",className:"block w-full btn-primary text-center",children:"Portal Login"})]})]})})]})})}function o(e){let{children:s}=e;return(0,a.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,a.jsx)(c,{}),(0,a.jsx)("main",{className:"container-app section-padding",children:s})]})}},8428:(e,s,t)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/job-seekers",function(){return t(73)}])}},e=>{var s=s=>e(e.s=s);e.O(0,[305,274,636,593,792],()=>s(8428)),_N_E=e.O()}]);