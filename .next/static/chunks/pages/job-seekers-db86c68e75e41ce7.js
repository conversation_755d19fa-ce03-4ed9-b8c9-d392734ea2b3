(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[854],{73:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var a=s(7876),n=s(4232),r=s(7328),i=s.n(r),l=s(6234),c=s(1467);function o(){let[e,t]=(0,n.useState)([]),[s,r]=(0,n.useState)(!0),[o,d]=(0,n.useState)(null),[h,x]=(0,n.useState)(null);return(0,n.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/job-seekers");if(!e.ok)throw Error("Failed to fetch job seekers");let s=await e.json();t(s);let a=s.map(e=>({id:e._key,name:e.name,type:"jobSeeker",size:8})),n=[];if(a.length>1)for(let e=0;e<a.length-1;e++)n.push({source:a[e].id,target:a[e+1].id,value:1});x({nodes:a,links:n})}catch(e){d(e.message)}finally{r(!1)}})()},[]),(0,a.jsxs)(l.A,{children:[(0,a.jsx)(i(),{children:(0,a.jsx)("title",{children:"Job Seekers | Candid Connections Katra"})}),(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold mb-6",children:"Job Seekers"}),s&&(0,a.jsx)("p",{children:"Loading job seekers..."}),o&&(0,a.jsxs)("p",{className:"text-red-500",children:["Error: ",o]}),!s&&!o&&(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsx)("div",{className:"lg:col-span-1",children:(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Job Seekers List"}),0===e.length?(0,a.jsx)("p",{children:"No job seekers found."}):(0,a.jsx)("ul",{className:"divide-y",children:e.map(e=>(0,a.jsxs)("li",{className:"py-3",children:[(0,a.jsx)("p",{className:"font-medium",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:e.title})]},e._key))})]})}),(0,a.jsx)("div",{className:"lg:col-span-2",children:(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Network Visualization"}),h?(0,a.jsx)(c.A,{data:h}):(0,a.jsx)("p",{children:"No visualization data available."})]})})]})]})]})}},1467:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(7876),n=s(4232),r=s(7070);function i(e){let{data:t}=e,s=(0,n.useRef)(null);return(0,n.useEffect)(()=>{var e;if(!t||!s.current)return;r.Ltv(s.current).selectAll("*").remove();let a=r.Ltv(s.current).attr("width",800).attr("height",600).attr("viewBox",[0,0,800,600]),n=r.tXi(t.nodes).force("link",r.kJC(t.links).id(e=>e.id)).force("charge",r.xJS().strength(-400)).force("center",r.jTM(400,300)),i=a.append("g").selectAll("line").data(t.links).join("line").attr("stroke","#999").attr("stroke-opacity",.6).attr("stroke-width",e=>Math.sqrt(e.value||1)),l=a.append("g").selectAll("circle").data(t.nodes).join("circle").attr("r",e=>e.size||5).attr("fill",e=>{switch(e.type){case"jobSeeker":return"#3b82f6";case"company":return"#14b8a6";case"position":return"#10b981";case"skill":return"#f59e0b";default:return"#6366f1"}}).call((e=n,r.$Er().on("start",function(t){t.active||e.alphaTarget(.3).restart(),t.subject.fx=t.subject.x,t.subject.fy=t.subject.y}).on("drag",function(e){e.subject.fx=e.x,e.subject.fy=e.y}).on("end",function(t){t.active||e.alphaTarget(0),t.subject.fx=null,t.subject.fy=null})));return l.append("title").text(e=>e.name),n.on("tick",()=>{i.attr("x1",e=>e.source.x).attr("y1",e=>e.source.y).attr("x2",e=>e.target.x).attr("y2",e=>e.target.y),l.attr("cx",e=>e.x).attr("cy",e=>e.y)}),()=>{n.stop()}},[t]),(0,a.jsx)("div",{className:"border rounded-lg shadow-sm bg-white p-4",children:(0,a.jsx)("svg",{ref:s,className:"w-full h-full"})})}},6234:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var a=s(7876),n=s(8230),r=s.n(n),i=s(9099),l=s(4232);function c(){let e=(0,i.useRouter)(),[t,s]=(0,l.useState)(!1),n=[{name:"Dashboard",path:"/",icon:"\uD83C\uDFE0"},{name:"Job Matches",path:"/matches",icon:"\uD83C\uDFAF"},{name:"Job Seekers",path:"/job-seekers",icon:"\uD83D\uDC65"},{name:"Companies",path:"/companies",icon:"\uD83C\uDFE2"},{name:"Positions",path:"/positions",icon:"\uD83D\uDCCB"},{name:"Skills",path:"/skills",icon:"\uD83D\uDEE0️"},{name:"Global View",path:"/global-view",icon:"\uD83C\uDF10"}];return(0,a.jsx)("nav",{className:"bg-white shadow-soft border-b border-candid-gray-200",children:(0,a.jsxs)("div",{className:"container-app",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center h-20",children:[(0,a.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,a.jsxs)(r(),{href:"/",className:"flex items-center space-x-3 group",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-primary-500 to-accent-500 rounded-xl flex items-center justify-center shadow-soft group-hover:shadow-medium transition-all duration-200",children:(0,a.jsx)("span",{className:"text-white font-bold text-lg",children:"C"})}),(0,a.jsxs)("div",{className:"hidden sm:block",children:[(0,a.jsx)("h1",{className:"text-xl font-bold text-candid-navy-900 group-hover:text-primary-600 transition-colors duration-200",children:"Candid Connections"}),(0,a.jsx)("p",{className:"text-sm text-candid-gray-600 -mt-1",children:"Katra Platform"})]})]})}),(0,a.jsx)("div",{className:"hidden lg:block",children:(0,a.jsx)("div",{className:"flex items-center space-x-1",children:n.map(t=>(0,a.jsxs)(r(),{href:t.path,className:"flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ".concat(e.pathname===t.path?"nav-link-active":"nav-link"),children:[(0,a.jsx)("span",{className:"text-base",children:t.icon}),(0,a.jsx)("span",{children:t.name})]},t.path))})}),(0,a.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[(0,a.jsx)(r(),{href:"/global-view",className:"btn-outline text-sm py-2 px-4",children:"\uD83C\uDF10 Network View"}),(0,a.jsx)(r(),{href:"https://portal.candid-connections.com/user/login",target:"_blank",rel:"noopener noreferrer",className:"btn-primary text-sm py-2 px-4",children:"Portal Login"})]}),(0,a.jsx)("div",{className:"lg:hidden",children:(0,a.jsx)("button",{onClick:()=>s(!t),className:"p-2 rounded-lg text-candid-navy-600 hover:text-primary-600 hover:bg-primary-50 transition-colors duration-200","aria-label":"Toggle mobile menu",children:(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:t?(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})})]}),t&&(0,a.jsx)("div",{className:"lg:hidden border-t border-candid-gray-200 py-4",children:(0,a.jsxs)("div",{className:"space-y-2",children:[n.map(t=>(0,a.jsxs)(r(),{href:t.path,onClick:()=>s(!1),className:"flex items-center space-x-3 px-4 py-3 rounded-lg text-base font-medium transition-all duration-200 ".concat(e.pathname===t.path?"nav-link-active":"nav-link"),children:[(0,a.jsx)("span",{className:"text-lg",children:t.icon}),(0,a.jsx)("span",{children:t.name})]},t.path)),(0,a.jsxs)("div",{className:"pt-4 space-y-2",children:[(0,a.jsx)(r(),{href:"/global-view",onClick:()=>s(!1),className:"block w-full btn-outline text-center",children:"\uD83C\uDF10 Network View"}),(0,a.jsx)(r(),{href:"https://portal.candid-connections.com/user/login",target:"_blank",rel:"noopener noreferrer",className:"block w-full btn-primary text-center",children:"Portal Login"})]})]})})]})})}function o(e){let{children:t}=e;return(0,a.jsxs)("div",{className:"min-h-screen bg-candid-gray-50",children:[(0,a.jsx)(c,{}),(0,a.jsx)("main",{className:"container-app section-padding",children:t})]})}},8428:(e,t,s)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/job-seekers",function(){return s(73)}])}},e=>{var t=t=>e(e.s=t);e.O(0,[305,70,636,593,792],()=>t(8428)),_N_E=e.O()}]);