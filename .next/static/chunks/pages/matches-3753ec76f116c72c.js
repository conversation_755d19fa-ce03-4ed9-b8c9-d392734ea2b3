(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[999],{982:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var r=s(7876),a=s(4232),n=s(7328),i=s.n(n),l=s(6234),c=s(1566),o=s(8512),d=s(8773);function m(){let[e,t]=(0,a.useState)([]),[s,n]=(0,a.useState)(!0),[m,x]=(0,a.useState)(null),[h,u]=(0,a.useState)(!1),[p,g]=(0,a.useState)(null),[b,j]=(0,a.useState)("all"),[y,f]=(0,a.useState)("score");(0,a.useEffect)(()=>{(async()=>{try{setTimeout(()=>{let e=[{id:"match_1",jobSeeker:{id:"js_1",name:"<PERSON>",title:"Senior Frontend Developer",skills:["React","TypeScript","Node.js","GraphQL"],experience:"6 years"},hiringAuthority:{id:"auth_1",name:"Mike Wilson",role:"VP Engineering",level:"Executive",company:"TechCorp Inc.",companySize:"Enterprise (1000+)",hiringPower:"High",decisionMaker:!0},position:{id:"pos_1",title:"Lead Frontend Engineer",requirements:["React","TypeScript","Leadership","GraphQL"]},score:92,status:"pending",createdAt:new Date("2024-01-15"),matchReasons:["Skills align with authority requirements","Experience level matches hiring criteria","Direct path to decision maker"],connectionStrength:"Strong",hierarchyMatch:"Perfect - Executive level for senior role"},{id:"match_2",jobSeeker:{id:"js_2",name:"Marcus Johnson",title:"Full Stack Developer",skills:["Python","Django","PostgreSQL","AWS"]},position:{id:"pos_2",title:"Backend Engineer",company:"DataFlow Systems",requirements:["Python","Django","Database Design","Cloud Platforms"]},score:87,status:"approved",createdAt:new Date("2024-01-14"),matchReasons:["Python expertise","Django framework knowledge","Cloud experience"]},{id:"match_3",jobSeeker:{id:"js_3",name:"Emily Rodriguez",title:"UX Designer",skills:["Figma","User Research","Prototyping","Design Systems"]},position:{id:"pos_3",title:"Senior UX Designer",company:"Design Studio Pro",requirements:["Figma","User Research","Design Systems","Collaboration"]},score:95,status:"pending",createdAt:new Date("2024-01-13"),matchReasons:["Excellent design skills","Strong user research background","Design systems expertise"]},{id:"match_4",jobSeeker:{id:"js_4",name:"David Kim",title:"DevOps Engineer",skills:["Kubernetes","Docker","Terraform","Jenkins"]},position:{id:"pos_4",title:"Cloud Infrastructure Engineer",company:"CloudTech Solutions",requirements:["Kubernetes","Infrastructure as Code","CI/CD","Monitoring"]},score:78,status:"rejected",createdAt:new Date("2024-01-12"),matchReasons:["Kubernetes expertise","Infrastructure automation","CI/CD pipeline experience"]}];t(e),g((0,o._)()),n(!1)},1e3)}catch(e){x(e.message),n(!1)}})()},[]);let v=[...e.filter(e=>"all"===b||e.status===b)].sort((e,t)=>{switch(y){case"score":return t.score-e.score;case"date":return new Date(t.createdAt)-new Date(e.createdAt);case"name":return e.jobSeeker.name.localeCompare(t.jobSeeker.name);default:return 0}}),N=e=>({pending:"bg-yellow-100 text-yellow-800",approved:"bg-green-100 text-green-800",rejected:"bg-red-100 text-red-800"})[e]||"bg-gray-100 text-gray-800",w=(e,s)=>{t(t=>t.map(t=>t.id===e?{...t,status:s}:t))};return s?(0,r.jsx)(l.A,{children:(0,r.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,r.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-emerald-500"})})})}):m?(0,r.jsx)(l.A,{children:(0,r.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,r.jsxs)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded",children:["Error: ",m]})})}):(0,r.jsxs)(l.A,{children:[(0,r.jsxs)(i(),{children:[(0,r.jsx)("title",{children:"Authority Matches | Candid Connections Katra"}),(0,r.jsx)("meta",{name:"description",content:"Job seeker to hiring authority matches based on graph database connections and company hierarchy."})]}),(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-secondary-800",children:"Authority Matches"}),(0,r.jsx)("p",{className:"text-candid-gray-600 mt-2",children:"Job seekers connected to the right hiring authorities"})]}),(0,r.jsx)("button",{onClick:()=>u(!0),className:"bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors",children:"\uD83C\uDF10 View Network"})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-4 mb-6",children:(0,r.jsxs)("div",{className:"flex flex-wrap gap-4 items-center",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Status:"}),(0,r.jsxs)("select",{value:b,onChange:e=>j(e.target.value),className:"border border-gray-300 rounded-md px-3 py-1 text-sm",children:[(0,r.jsx)("option",{value:"all",children:"All"}),(0,r.jsx)("option",{value:"pending",children:"Pending"}),(0,r.jsx)("option",{value:"approved",children:"Approved"}),(0,r.jsx)("option",{value:"rejected",children:"Rejected"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Sort by:"}),(0,r.jsxs)("select",{value:y,onChange:e=>f(e.target.value),className:"border border-gray-300 rounded-md px-3 py-1 text-sm",children:[(0,r.jsx)("option",{value:"score",children:"Match Score"}),(0,r.jsx)("option",{value:"date",children:"Date Created"}),(0,r.jsx)("option",{value:"name",children:"Job Seeker Name"})]})]}),(0,r.jsxs)("div",{className:"ml-auto text-sm text-gray-600",children:[v.length," matches found"]})]})}),(0,r.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:v.map(e=>(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,r.jsxs)("div",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ".concat((0,d.zk)(e.score)),children:[e.score,"% Match"]}),(0,r.jsx)("div",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ".concat(N(e.status)),children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("h3",{className:"font-semibold text-lg text-gray-900",children:e.jobSeeker.name}),(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:e.jobSeeker.title}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-1 mt-2",children:[e.jobSeeker.skills.slice(0,3).map((e,t)=>(0,r.jsx)("span",{className:"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded",children:e},t)),e.jobSeeker.skills.length>3&&(0,r.jsxs)("span",{className:"text-xs text-gray-500",children:["+",e.jobSeeker.skills.length-3," more"]})]})]}),(0,r.jsxs)("div",{className:"mb-4 pb-4 border-b border-gray-100",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:e.position.title}),(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:e.position.company}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-1 mt-2",children:[e.position.requirements.slice(0,3).map((e,t)=>(0,r.jsx)("span",{className:"bg-emerald-100 text-emerald-800 text-xs px-2 py-1 rounded",children:e},t)),e.position.requirements.length>3&&(0,r.jsxs)("span",{className:"text-xs text-gray-500",children:["+",e.position.requirements.length-3," more"]})]})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("h5",{className:"text-sm font-medium text-gray-700 mb-2",children:"Why this is a good match:"}),(0,r.jsx)("ul",{className:"text-sm text-gray-600 space-y-1",children:e.matchReasons.map((e,t)=>(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"text-emerald-500 mr-2",children:"•"}),e]},t))})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("span",{className:"text-xs text-gray-500",children:["Created ",(0,d.Yq)(e.createdAt)]}),"pending"===e.status&&(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{onClick:()=>w(e.id,"approved"),className:"bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 transition-colors",children:"Approve"}),(0,r.jsx)("button",{onClick:()=>w(e.id,"rejected"),className:"bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700 transition-colors",children:"Reject"})]})]})]},e.id))}),0===v.length&&(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"text-gray-400 text-6xl mb-4",children:"\uD83C\uDFAF"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No matches found"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Try adjusting your filters to see more results."})]})]}),(0,r.jsx)(c.A,{isOpen:h,onClose:()=>u(!1),data:p,title:"Job Matches Network"})]})}},1566:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(7876),a=s(4232),n=s(1467);function i(e){let{data:t}=e,n=(0,a.useRef)(null);return(0,a.useEffect)(()=>{t&&n.current&&(n.current.innerHTML="",Promise.all([s.e(391),s.e(917),s.e(403),s.e(728),s.e(682)]).then(s.bind(s,4682)).then(e=>{let s=(0,e.default)().width(n.current.clientWidth).height(500).backgroundColor("#ffffff").nodeColor(e=>{switch(e.type){case"jobSeeker":return"#3b82f6";case"company":return"#14b8a6";case"position":return"#10b981";case"skill":return"#f59e0b";default:return"#6366f1"}}).nodeLabel(e=>e.name).nodeVal(e=>e.size||5).linkWidth(e=>e.value||1).linkDirectionalParticles(2).linkDirectionalParticleSpeed(.005).graphData(t)(n.current);window.addEventListener("resize",()=>{n.current&&s.width(n.current.clientWidth)})}).catch(e=>{console.error("Failed to load 3D visualization:",e),n.current&&(n.current.innerHTML='<div class="flex items-center justify-center h-full text-gray-500">3D visualization unavailable</div>')}))},[t]),(0,r.jsx)("div",{ref:n,className:"w-full h-[500px] border rounded-lg shadow-sm bg-white"})}function l(e){let{isOpen:t,onClose:s,data:l,title:c}=e,[o,d]=(0,a.useState)("2D");return t?(0,r.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,r.jsxs)("div",{className:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,r.jsx)("div",{className:"fixed inset-0 transition-opacity","aria-hidden":"true",children:(0,r.jsx)("div",{className:"absolute inset-0 bg-gray-500 opacity-75",onClick:s})}),(0,r.jsxs)("div",{className:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-5xl sm:w-full",children:[(0,r.jsx)("div",{className:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:(0,r.jsx)("div",{className:"sm:flex sm:items-start",children:(0,r.jsxs)("div",{className:"w-full",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,r.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:c||"Network Visualization"}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{type:"button",onClick:()=>d("2D"),className:"px-3 py-1 text-sm font-medium rounded-md ".concat("2D"===o?"bg-indigo-100 text-indigo-700":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:"2D View"}),(0,r.jsx)("button",{type:"button",onClick:()=>d("3D"),className:"px-3 py-1 text-sm font-medium rounded-md ".concat("3D"===o?"bg-indigo-100 text-indigo-700":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:"3D View"})]})]}),(0,r.jsx)("div",{className:"mt-2",children:"2D"===o?(0,r.jsx)(n.A,{data:l}):(0,r.jsx)(i,{data:l})})]})})}),(0,r.jsx)("div",{className:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:(0,r.jsx)("button",{type:"button",className:"btn-secondary",onClick:s,children:"Close"})})]})]})}):null}},7918:(e,t,s)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/matches",function(){return s(982)}])},8773:(e,t,s)=>{"use strict";s.d(t,{Yq:()=>r,zk:()=>a});let r=e=>e?new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}):"",a=e=>e>=80?"bg-emerald-100 text-emerald-800":e>=60?"bg-green-100 text-green-800":e>=40?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}},e=>{var t=t=>e(e.s=t);e.O(0,[305,274,41,636,593,792],()=>t(7918)),_N_E=e.O()}]);