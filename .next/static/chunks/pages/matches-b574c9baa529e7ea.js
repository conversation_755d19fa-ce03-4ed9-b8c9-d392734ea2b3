(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[999],{982:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>m});var r=t(7876),a=t(4232),n=t(7328),i=t.n(n),l=t(6234),c=t(1566),d=t(8512),o=t(8773);function m(){let[e,s]=(0,a.useState)([]),[t,n]=(0,a.useState)(!0),[m,x]=(0,a.useState)(null),[p,h]=(0,a.useState)(!1),[u,g]=(0,a.useState)(null),[j,b]=(0,a.useState)("all"),[y,N]=(0,a.useState)("score");(0,a.useEffect)(()=>{(async()=>{try{setTimeout(()=>{let e=[{id:"match_1",jobSeeker:{id:"js_1",name:"<PERSON>",title:"Senior Frontend Developer",skills:["React","TypeScript","Node.js","GraphQL"]},position:{id:"pos_1",title:"Lead Frontend Engineer",company:"TechCorp Inc.",requirements:["React","TypeScript","Leadership","GraphQL"]},score:92,status:"pending",createdAt:new Date("2024-01-15"),matchReasons:["Strong React expertise","TypeScript proficiency","GraphQL experience"]},{id:"match_2",jobSeeker:{id:"js_2",name:"Marcus Johnson",title:"Full Stack Developer",skills:["Python","Django","PostgreSQL","AWS"]},position:{id:"pos_2",title:"Backend Engineer",company:"DataFlow Systems",requirements:["Python","Django","Database Design","Cloud Platforms"]},score:87,status:"approved",createdAt:new Date("2024-01-14"),matchReasons:["Python expertise","Django framework knowledge","Cloud experience"]},{id:"match_3",jobSeeker:{id:"js_3",name:"Emily Rodriguez",title:"UX Designer",skills:["Figma","User Research","Prototyping","Design Systems"]},position:{id:"pos_3",title:"Senior UX Designer",company:"Design Studio Pro",requirements:["Figma","User Research","Design Systems","Collaboration"]},score:95,status:"pending",createdAt:new Date("2024-01-13"),matchReasons:["Excellent design skills","Strong user research background","Design systems expertise"]},{id:"match_4",jobSeeker:{id:"js_4",name:"David Kim",title:"DevOps Engineer",skills:["Kubernetes","Docker","Terraform","Jenkins"]},position:{id:"pos_4",title:"Cloud Infrastructure Engineer",company:"CloudTech Solutions",requirements:["Kubernetes","Infrastructure as Code","CI/CD","Monitoring"]},score:78,status:"rejected",createdAt:new Date("2024-01-12"),matchReasons:["Kubernetes expertise","Infrastructure automation","CI/CD pipeline experience"]}];s(e),g((0,d._)()),n(!1)},1e3)}catch(e){x(e.message),n(!1)}})()},[]);let v=[...e.filter(e=>"all"===j||e.status===j)].sort((e,s)=>{switch(y){case"score":return s.score-e.score;case"date":return new Date(s.createdAt)-new Date(e.createdAt);case"name":return e.jobSeeker.name.localeCompare(s.jobSeeker.name);default:return 0}}),f=e=>({pending:"bg-yellow-100 text-yellow-800",approved:"bg-green-100 text-green-800",rejected:"bg-red-100 text-red-800"})[e]||"bg-gray-100 text-gray-800",S=(e,t)=>{s(s=>s.map(s=>s.id===e?{...s,status:t}:s))};return t?(0,r.jsx)(l.A,{children:(0,r.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,r.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-emerald-500"})})})}):m?(0,r.jsx)(l.A,{children:(0,r.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,r.jsxs)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded",children:["Error: ",m]})})}):(0,r.jsxs)(l.A,{children:[(0,r.jsx)(i(),{children:(0,r.jsx)("title",{children:"Job Matches | Candid Connections Katra"})}),(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold",children:"Job Matches"}),(0,r.jsx)("button",{onClick:()=>h(!0),className:"bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors",children:"\uD83C\uDF10 View Network"})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-4 mb-6",children:(0,r.jsxs)("div",{className:"flex flex-wrap gap-4 items-center",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Status:"}),(0,r.jsxs)("select",{value:j,onChange:e=>b(e.target.value),className:"border border-gray-300 rounded-md px-3 py-1 text-sm",children:[(0,r.jsx)("option",{value:"all",children:"All"}),(0,r.jsx)("option",{value:"pending",children:"Pending"}),(0,r.jsx)("option",{value:"approved",children:"Approved"}),(0,r.jsx)("option",{value:"rejected",children:"Rejected"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Sort by:"}),(0,r.jsxs)("select",{value:y,onChange:e=>N(e.target.value),className:"border border-gray-300 rounded-md px-3 py-1 text-sm",children:[(0,r.jsx)("option",{value:"score",children:"Match Score"}),(0,r.jsx)("option",{value:"date",children:"Date Created"}),(0,r.jsx)("option",{value:"name",children:"Job Seeker Name"})]})]}),(0,r.jsxs)("div",{className:"ml-auto text-sm text-gray-600",children:[v.length," matches found"]})]})}),(0,r.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:v.map(e=>(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,r.jsxs)("div",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ".concat((0,o.zk)(e.score)),children:[e.score,"% Match"]}),(0,r.jsx)("div",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ".concat(f(e.status)),children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("h3",{className:"font-semibold text-lg text-gray-900",children:e.jobSeeker.name}),(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:e.jobSeeker.title}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-1 mt-2",children:[e.jobSeeker.skills.slice(0,3).map((e,s)=>(0,r.jsx)("span",{className:"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded",children:e},s)),e.jobSeeker.skills.length>3&&(0,r.jsxs)("span",{className:"text-xs text-gray-500",children:["+",e.jobSeeker.skills.length-3," more"]})]})]}),(0,r.jsxs)("div",{className:"mb-4 pb-4 border-b border-gray-100",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:e.position.title}),(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:e.position.company}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-1 mt-2",children:[e.position.requirements.slice(0,3).map((e,s)=>(0,r.jsx)("span",{className:"bg-emerald-100 text-emerald-800 text-xs px-2 py-1 rounded",children:e},s)),e.position.requirements.length>3&&(0,r.jsxs)("span",{className:"text-xs text-gray-500",children:["+",e.position.requirements.length-3," more"]})]})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("h5",{className:"text-sm font-medium text-gray-700 mb-2",children:"Why this is a good match:"}),(0,r.jsx)("ul",{className:"text-sm text-gray-600 space-y-1",children:e.matchReasons.map((e,s)=>(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"text-emerald-500 mr-2",children:"•"}),e]},s))})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("span",{className:"text-xs text-gray-500",children:["Created ",(0,o.Yq)(e.createdAt)]}),"pending"===e.status&&(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{onClick:()=>S(e.id,"approved"),className:"bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 transition-colors",children:"Approve"}),(0,r.jsx)("button",{onClick:()=>S(e.id,"rejected"),className:"bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700 transition-colors",children:"Reject"})]})]})]},e.id))}),0===v.length&&(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"text-gray-400 text-6xl mb-4",children:"\uD83C\uDFAF"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No matches found"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Try adjusting your filters to see more results."})]})]}),(0,r.jsx)(c.A,{isOpen:p,onClose:()=>h(!1),data:u,title:"Job Matches Network"})]})}},7918:(e,s,t)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/matches",function(){return t(982)}])},8773:(e,s,t)=>{"use strict";t.d(s,{Yq:()=>r,zk:()=>a});let r=e=>e?new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}):"",a=e=>e>=80?"bg-emerald-100 text-emerald-800":e>=60?"bg-green-100 text-green-800":e>=40?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}},e=>{var s=s=>e(e.s=s);e.O(0,[305,70,58,636,593,792],()=>s(7918)),_N_E=e.O()}]);