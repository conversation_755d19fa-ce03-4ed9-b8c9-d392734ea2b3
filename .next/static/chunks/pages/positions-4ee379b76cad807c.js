(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[760],{5012:(e,t,s)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/positions",function(){return s(8695)}])},8695:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var a=s(7876),r=s(4232),n=s(7328),l=s.n(n),i=s(6234),o=s(1566),c=s(8512),d=s(8773);function m(){let[e,t]=(0,r.useState)([]),[s,n]=(0,r.useState)(!0),[m,x]=(0,r.useState)(null),[u,p]=(0,r.useState)(!1),[h,g]=(0,r.useState)(null),[y,j]=(0,r.useState)(""),[b,v]=(0,r.useState)("all"),[f,N]=(0,r.useState)("all"),[w,D]=(0,r.useState)("posted");(0,r.useEffect)(()=>{(async()=>{try{setTimeout(()=>{let e=[{id:"pos_1",title:"Lead Frontend Engineer",company:"TechCorp Inc.",companyLogo:"\uD83C\uDFE2",level:"Senior",type:"Full-time",location:"San Francisco, CA",remote:!0,salary:"$140,000 - $180,000",description:"Lead a team of frontend engineers building next-generation web applications using React and TypeScript.",requirements:["React","TypeScript","Leadership","GraphQL","Node.js"],benefits:["Health Insurance","Stock Options","Remote Work","401k"],postedDate:new Date("2024-01-15"),applicants:23,status:"active"},{id:"pos_2",title:"Backend Engineer",company:"DataFlow Systems",companyLogo:"\uD83D\uDCCA",level:"Mid",type:"Full-time",location:"Austin, TX",remote:!1,salary:"$100,000 - $130,000",description:"Build scalable backend systems for our data analytics platform using Python and Django.",requirements:["Python","Django","PostgreSQL","AWS","Docker"],benefits:["Health Insurance","Flexible Hours","Learning Budget"],postedDate:new Date("2024-01-14"),applicants:18,status:"active"},{id:"pos_3",title:"Senior UX Designer",company:"Design Studio Pro",companyLogo:"\uD83C\uDFA8",level:"Senior",type:"Contract",location:"New York, NY",remote:!0,salary:"$80 - $120/hour",description:"Design intuitive user experiences for our client projects across various industries.",requirements:["Figma","User Research","Design Systems","Prototyping"],benefits:["Flexible Schedule","Creative Freedom","Portfolio Building"],postedDate:new Date("2024-01-13"),applicants:31,status:"active"},{id:"pos_4",title:"Cloud Infrastructure Engineer",company:"CloudTech Solutions",companyLogo:"☁️",level:"Senior",type:"Full-time",location:"Seattle, WA",remote:!0,salary:"$130,000 - $160,000",description:"Design and maintain cloud infrastructure for enterprise clients using Kubernetes and Terraform.",requirements:["Kubernetes","Terraform","AWS","Docker","Monitoring"],benefits:["Health Insurance","Stock Options","Remote Work","Conference Budget"],postedDate:new Date("2024-01-12"),applicants:15,status:"active"},{id:"pos_5",title:"Junior Frontend Developer",company:"TechCorp Inc.",companyLogo:"\uD83C\uDFE2",level:"Junior",type:"Full-time",location:"San Francisco, CA",remote:!1,salary:"$70,000 - $90,000",description:"Join our frontend team to build user interfaces and learn from experienced developers.",requirements:["JavaScript","React","CSS","Git"],benefits:["Health Insurance","Mentorship","Learning Budget"],postedDate:new Date("2024-01-10"),applicants:42,status:"paused"}];t(e),g((0,c._)()),n(!1)},1e3)}catch(e){x(e.message),n(!1)}})()},[]);let C=[...e.filter(e=>{let t=e.title.toLowerCase().includes(y.toLowerCase())||e.company.toLowerCase().includes(y.toLowerCase())||e.location.toLowerCase().includes(y.toLowerCase())||e.requirements.some(e=>e.toLowerCase().includes(y.toLowerCase())),s="all"===b||e.level===b,a="all"===f||e.type===f;return t&&s&&a})].sort((e,t)=>{switch(w){case"posted":return new Date(t.postedDate)-new Date(e.postedDate);case"title":return e.title.localeCompare(t.title);case"company":return e.company.localeCompare(t.company);case"applicants":return t.applicants-e.applicants;default:return 0}}),S=e=>{switch(e){case"Junior":return"bg-green-100 text-green-800";case"Mid":return"bg-blue-100 text-blue-800";case"Senior":return"bg-purple-100 text-purple-800";default:return"bg-gray-100 text-gray-800"}},L=e=>{switch(e){case"Full-time":return"bg-emerald-100 text-emerald-800";case"Part-time":return"bg-yellow-100 text-yellow-800";case"Contract":return"bg-orange-100 text-orange-800";default:return"bg-gray-100 text-gray-800"}},k=e=>{switch(e){case"active":return"bg-green-100 text-green-800";case"paused":return"bg-yellow-100 text-yellow-800";case"closed":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}};return s?(0,a.jsx)(i.A,{children:(0,a.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,a.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-emerald-500"})})})}):m?(0,a.jsx)(i.A,{children:(0,a.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,a.jsxs)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded",children:["Error: ",m]})})}):(0,a.jsxs)(i.A,{children:[(0,a.jsx)(l(),{children:(0,a.jsx)("title",{children:"Positions | Candid Connections Katra"})}),(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold",children:"Open Positions"}),(0,a.jsx)("button",{onClick:()=>p(!0),className:"bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors",children:"\uD83C\uDF10 View Network"})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex flex-wrap gap-4 items-center",children:[(0,a.jsx)("div",{className:"flex-1 min-w-64",children:(0,a.jsx)("input",{type:"text",placeholder:"Search positions, companies, skills...",value:y,onChange:e=>j(e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"})}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Level:"}),(0,a.jsxs)("select",{value:b,onChange:e=>v(e.target.value),className:"border border-gray-300 rounded-md px-3 py-1 text-sm",children:[(0,a.jsx)("option",{value:"all",children:"All Levels"}),(0,a.jsx)("option",{value:"Junior",children:"Junior"}),(0,a.jsx)("option",{value:"Mid",children:"Mid"}),(0,a.jsx)("option",{value:"Senior",children:"Senior"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Type:"}),(0,a.jsxs)("select",{value:f,onChange:e=>N(e.target.value),className:"border border-gray-300 rounded-md px-3 py-1 text-sm",children:[(0,a.jsx)("option",{value:"all",children:"All Types"}),(0,a.jsx)("option",{value:"Full-time",children:"Full-time"}),(0,a.jsx)("option",{value:"Part-time",children:"Part-time"}),(0,a.jsx)("option",{value:"Contract",children:"Contract"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Sort by:"}),(0,a.jsxs)("select",{value:w,onChange:e=>D(e.target.value),className:"border border-gray-300 rounded-md px-3 py-1 text-sm",children:[(0,a.jsx)("option",{value:"posted",children:"Date Posted"}),(0,a.jsx)("option",{value:"title",children:"Position Title"}),(0,a.jsx)("option",{value:"company",children:"Company"}),(0,a.jsx)("option",{value:"applicants",children:"Applicants"})]})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[C.length," positions found"]})]})}),(0,a.jsx)("div",{className:"space-y-4",children:C.map(e=>(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-2xl mr-3",children:e.companyLogo}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-lg text-gray-900",children:e.title}),(0,a.jsx)("p",{className:"text-gray-600",children:e.company})]})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("div",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ".concat(S(e.level)),children:e.level}),(0,a.jsx)("div",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ".concat(L(e.type)),children:e.type}),(0,a.jsx)("div",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ".concat(k(e.status)),children:e.status})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,a.jsx)("span",{className:"mr-2",children:"\uD83D\uDCCD"}),e.location," ",e.remote&&"(Remote OK)"]}),(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,a.jsx)("span",{className:"mr-2",children:"\uD83D\uDCB0"}),e.salary]}),(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,a.jsx)("span",{className:"mr-2",children:"\uD83D\uDC65"}),e.applicants," applicants"]})]}),(0,a.jsx)("p",{className:"text-gray-700 text-sm mb-4",children:e.description}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("h5",{className:"text-sm font-medium text-gray-700 mb-2",children:"Required Skills:"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-1",children:e.requirements.map((e,t)=>(0,a.jsx)("span",{className:"bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded",children:e},t))})]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("h5",{className:"text-sm font-medium text-gray-700 mb-2",children:"Benefits:"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-1",children:e.benefits.map((e,t)=>(0,a.jsx)("span",{className:"bg-emerald-100 text-emerald-800 text-xs px-2 py-1 rounded",children:e},t))})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center pt-4 border-t border-gray-100",children:[(0,a.jsxs)("span",{className:"text-xs text-gray-500",children:["Posted ",(0,d.Yq)(e.postedDate)]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{className:"bg-emerald-600 text-white px-4 py-2 rounded text-sm hover:bg-emerald-700 transition-colors",children:"View Details"}),(0,a.jsx)("button",{className:"border border-emerald-600 text-emerald-600 px-4 py-2 rounded text-sm hover:bg-emerald-50 transition-colors",children:"Find Matches"})]})]})]},e.id))}),0===C.length&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-gray-400 text-6xl mb-4",children:"\uD83D\uDCCB"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No positions found"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Try adjusting your search or filters to see more results."})]})]}),(0,a.jsx)(o.A,{isOpen:u,onClose:()=>p(!1),data:h,title:"Positions Network"})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[305,70,582,636,593,792],()=>t(5012)),_N_E=e.O()}]);