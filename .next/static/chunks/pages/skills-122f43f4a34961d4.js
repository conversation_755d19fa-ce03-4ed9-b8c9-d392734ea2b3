(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[950],{336:(e,s,a)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/skills",function(){return a(4311)}])},1566:(e,s,a)=>{"use strict";a.d(s,{A:()=>i});var t=a(7876),r=a(4232),l=a(1467);function n(e){let{data:s}=e,l=(0,r.useRef)(null);return(0,r.useEffect)(()=>{s&&l.current&&(l.current.innerHTML="",Promise.all([a.e(391),a.e(917),a.e(403),a.e(728),a.e(682)]).then(a.bind(a,4682)).then(e=>{let a=(0,e.default)().width(l.current.clientWidth).height(500).backgroundColor("#ffffff").nodeColor(e=>{switch(e.type){case"jobSeeker":return"#3b82f6";case"company":return"#14b8a6";case"position":return"#10b981";case"skill":return"#f59e0b";default:return"#6366f1"}}).nodeLabel(e=>e.name).nodeVal(e=>e.size||5).linkWidth(e=>e.value||1).linkDirectionalParticles(2).linkDirectionalParticleSpeed(.005).graphData(s)(l.current);window.addEventListener("resize",()=>{l.current&&a.width(l.current.clientWidth)})}).catch(e=>{console.error("Failed to load 3D visualization:",e),l.current&&(l.current.innerHTML='<div class="flex items-center justify-center h-full text-gray-500">3D visualization unavailable</div>')}))},[s]),(0,t.jsx)("div",{ref:l,className:"w-full h-[500px] border rounded-lg shadow-sm bg-white"})}function i(e){let{isOpen:s,onClose:a,data:i,title:d}=e,[o,c]=(0,r.useState)("2D");return s?(0,t.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,t.jsxs)("div",{className:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,t.jsx)("div",{className:"fixed inset-0 transition-opacity","aria-hidden":"true",children:(0,t.jsx)("div",{className:"absolute inset-0 bg-gray-500 opacity-75",onClick:a})}),(0,t.jsxs)("div",{className:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-5xl sm:w-full",children:[(0,t.jsx)("div",{className:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:(0,t.jsx)("div",{className:"sm:flex sm:items-start",children:(0,t.jsxs)("div",{className:"w-full",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,t.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:d||"Network Visualization"}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)("button",{type:"button",onClick:()=>c("2D"),className:"px-3 py-1 text-sm font-medium rounded-md ".concat("2D"===o?"bg-indigo-100 text-indigo-700":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:"2D View"}),(0,t.jsx)("button",{type:"button",onClick:()=>c("3D"),className:"px-3 py-1 text-sm font-medium rounded-md ".concat("3D"===o?"bg-indigo-100 text-indigo-700":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:"3D View"})]})]}),(0,t.jsx)("div",{className:"mt-2",children:"2D"===o?(0,t.jsx)(l.A,{data:i}):(0,t.jsx)(n,{data:i})})]})})}),(0,t.jsx)("div",{className:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:(0,t.jsx)("button",{type:"button",className:"btn-secondary",onClick:a,children:"Close"})})]})]})}):null}},4311:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>c});var t=a(7876),r=a(4232),l=a(7328),n=a.n(l),i=a(6234),d=a(1566),o=a(8512);function c(){let[e,s]=(0,r.useState)([]),[a,l]=(0,r.useState)(!0),[c,m]=(0,r.useState)(null),[x,p]=(0,r.useState)(!1),[u,g]=(0,r.useState)(null),[h,y]=(0,r.useState)(""),[b,j]=(0,r.useState)("all"),[f,v]=(0,r.useState)("demand");(0,r.useEffect)(()=>{(async()=>{try{setTimeout(()=>{s([{id:"skill_1",name:"React",category:"Frontend",demand:95,supply:78,averageSalary:"$105,000",jobSeekers:156,openPositions:89,growth:"+12%",description:"JavaScript library for building user interfaces",relatedSkills:["JavaScript","TypeScript","Redux","Next.js"],icon:"⚛️"},{id:"skill_2",name:"Python",category:"Backend",demand:92,supply:85,averageSalary:"$98,000",jobSeekers:203,openPositions:76,growth:"+8%",description:"High-level programming language for web development, data science, and automation",relatedSkills:["Django","Flask","FastAPI","NumPy"],icon:"\uD83D\uDC0D"},{id:"skill_3",name:"Kubernetes",category:"DevOps",demand:88,supply:45,averageSalary:"$125,000",jobSeekers:67,openPositions:52,growth:"+25%",description:"Container orchestration platform for automating deployment and scaling",relatedSkills:["Docker","Terraform","AWS","Jenkins"],icon:"☸️"},{id:"skill_4",name:"Figma",category:"Design",demand:85,supply:72,averageSalary:"$85,000",jobSeekers:134,openPositions:43,growth:"+15%",description:"Collaborative design tool for creating user interfaces and prototypes",relatedSkills:["Sketch","Adobe XD","Prototyping","User Research"],icon:"\uD83C\uDFA8"},{id:"skill_5",name:"TypeScript",category:"Frontend",demand:82,supply:65,averageSalary:"$108,000",jobSeekers:98,openPositions:67,growth:"+18%",description:"Typed superset of JavaScript that compiles to plain JavaScript",relatedSkills:["JavaScript","React","Angular","Node.js"],icon:"\uD83D\uDCD8"},{id:"skill_6",name:"AWS",category:"Cloud",demand:90,supply:58,averageSalary:"$115,000",jobSeekers:89,openPositions:78,growth:"+20%",description:"Amazon Web Services cloud computing platform",relatedSkills:["EC2","S3","Lambda","CloudFormation"],icon:"☁️"},{id:"skill_7",name:"Machine Learning",category:"Data Science",demand:87,supply:42,averageSalary:"$130,000",jobSeekers:45,openPositions:38,growth:"+30%",description:"AI technique that enables computers to learn and improve from experience",relatedSkills:["Python","TensorFlow","PyTorch","Scikit-learn"],icon:"\uD83E\uDD16"},{id:"skill_8",name:"Node.js",category:"Backend",demand:78,supply:82,averageSalary:"$95,000",jobSeekers:167,openPositions:54,growth:"+5%",description:"JavaScript runtime for building server-side applications",relatedSkills:["Express.js","MongoDB","GraphQL","REST APIs"],icon:"\uD83D\uDFE2"}]),g((0,o._)()),l(!1)},1e3)}catch(e){m(e.message),l(!1)}})()},[]);let N=[...e.filter(e=>{let s=e.name.toLowerCase().includes(h.toLowerCase())||e.category.toLowerCase().includes(h.toLowerCase())||e.description.toLowerCase().includes(h.toLowerCase()),a="all"===b||e.category===b;return s&&a})].sort((e,s)=>{switch(f){case"demand":return s.demand-e.demand;case"supply":return s.supply-e.supply;case"salary":return parseInt(s.averageSalary.replace(/[$,]/g,""))-parseInt(e.averageSalary.replace(/[$,]/g,""));case"growth":return parseInt(s.growth.replace(/[+%]/g,""))-parseInt(e.growth.replace(/[+%]/g,""));case"name":return e.name.localeCompare(s.name);default:return 0}}),w=e=>e>=90?"bg-red-100 text-red-800":e>=80?"bg-orange-100 text-orange-800":e>=70?"bg-yellow-100 text-yellow-800":"bg-green-100 text-green-800",S=e=>e>=80?"bg-green-100 text-green-800":e>=60?"bg-yellow-100 text-yellow-800":e>=40?"bg-orange-100 text-orange-800":"bg-red-100 text-red-800",k=e=>{let s=parseInt(e.replace(/[+%]/g,""));return s>=20?"bg-emerald-100 text-emerald-800":s>=10?"bg-green-100 text-green-800":s>=5?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"},C=["all",...new Set(e.map(e=>e.category))];return a?(0,t.jsx)(i.A,{children:(0,t.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,t.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-amber-500"})})})}):c?(0,t.jsx)(i.A,{children:(0,t.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,t.jsxs)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded",children:["Error: ",c]})})}):(0,t.jsxs)(i.A,{children:[(0,t.jsx)(n(),{children:(0,t.jsx)("title",{children:"Skills Analysis | Candid Connections Katra"})}),(0,t.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold",children:"Skills Analysis"}),(0,t.jsx)("button",{onClick:()=>p(!0),className:"bg-amber-600 text-white px-4 py-2 rounded-lg hover:bg-amber-700 transition-colors",children:"\uD83C\uDF10 View Network"})]}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-4 mb-6",children:(0,t.jsxs)("div",{className:"flex flex-wrap gap-4 items-center",children:[(0,t.jsx)("div",{className:"flex-1 min-w-64",children:(0,t.jsx)("input",{type:"text",placeholder:"Search skills, categories, or descriptions...",value:h,onChange:e=>y(e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"})}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Category:"}),(0,t.jsx)("select",{value:b,onChange:e=>j(e.target.value),className:"border border-gray-300 rounded-md px-3 py-1 text-sm",children:C.map(e=>(0,t.jsx)("option",{value:e,children:"all"===e?"All Categories":e},e))})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Sort by:"}),(0,t.jsxs)("select",{value:f,onChange:e=>v(e.target.value),className:"border border-gray-300 rounded-md px-3 py-1 text-sm",children:[(0,t.jsx)("option",{value:"demand",children:"Market Demand"}),(0,t.jsx)("option",{value:"supply",children:"Talent Supply"}),(0,t.jsx)("option",{value:"salary",children:"Average Salary"}),(0,t.jsx)("option",{value:"growth",children:"Growth Rate"}),(0,t.jsx)("option",{value:"name",children:"Skill Name"})]})]}),(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:[N.length," skills found"]})]})}),(0,t.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6",children:N.map(e=>(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"text-2xl mr-3",children:e.icon}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-lg text-gray-900",children:e.name}),(0,t.jsx)("p",{className:"text-gray-600 text-sm",children:e.category})]})]}),(0,t.jsx)("div",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ".concat(k(e.growth)),children:e.growth})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("div",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ".concat(w(e.demand)),children:[e.demand,"% Demand"]}),(0,t.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Market Demand"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("div",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ".concat(S(e.supply)),children:[e.supply,"% Supply"]}),(0,t.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Talent Supply"})]})]}),(0,t.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Average Salary:"}),(0,t.jsx)("span",{className:"font-medium text-emerald-600",children:e.averageSalary})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Job Seekers:"}),(0,t.jsx)("span",{className:"font-medium",children:e.jobSeekers})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Open Positions:"}),(0,t.jsx)("span",{className:"font-medium",children:e.openPositions})]})]}),(0,t.jsx)("p",{className:"text-gray-700 text-sm mb-4",children:e.description}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("h5",{className:"text-sm font-medium text-gray-700 mb-2",children:"Related Skills:"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-1",children:e.relatedSkills.map((e,s)=>(0,t.jsx)("span",{className:"bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded",children:e},s))})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)("button",{className:"flex-1 bg-amber-600 text-white px-3 py-2 rounded text-sm hover:bg-amber-700 transition-colors",children:"View Details"}),(0,t.jsx)("button",{className:"flex-1 border border-amber-600 text-amber-600 px-3 py-2 rounded text-sm hover:bg-amber-50 transition-colors",children:"Find Talent"})]})]},e.id))}),0===N.length&&(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("div",{className:"text-gray-400 text-6xl mb-4",children:"\uD83D\uDEE0️"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No skills found"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Try adjusting your search or filters to see more results."})]})]}),(0,t.jsx)(d.A,{isOpen:x,onClose:()=>p(!1),data:u,title:"Skills Network"})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[305,274,41,636,593,792],()=>s(336)),_N_E=e.O()}]);