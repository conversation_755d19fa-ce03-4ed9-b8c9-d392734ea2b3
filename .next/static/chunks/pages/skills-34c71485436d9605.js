(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[950],{336:(e,s,a)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/skills",function(){return a(6589)}])},6234:(e,s,a)=>{"use strict";a.d(s,{A:()=>c});var t=a(7876),r=a(8230),n=a.n(r),l=a(9099),i=a(4232);function o(){let e=(0,l.useRouter)(),[s,a]=(0,i.useState)(!1),r=[{name:"Dashboard",path:"/",icon:"\uD83C\uDFE0"},{name:"Authority Matches",path:"/matches",icon:"\uD83C\uDFAF"},{name:"Job Seekers",path:"/job-seekers",icon:"\uD83D\uDC65"},{name:"Hiring Authorities",path:"/hiring-authorities",icon:"\uD83D\uDC54"},{name:"Companies",path:"/companies",icon:"\uD83C\uDFE2"},{name:"Positions",path:"/positions",icon:"\uD83D\uDCCB"},{name:"Skills",path:"/skills",icon:"\uD83D\uDEE0️"},{name:"Visualizations",path:"/visualizations",icon:"\uD83D\uDCCA"},{name:"Network View",path:"/global-view",icon:"\uD83C\uDF10"}];return(0,t.jsx)("nav",{className:"bg-white shadow-soft border-b border-candid-gray-200",children:(0,t.jsxs)("div",{className:"container-app",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center h-20",children:[(0,t.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,t.jsxs)(n(),{href:"/",className:"flex items-center space-x-3 group",children:[(0,t.jsx)("div",{className:"w-12 h-12 relative group-hover:scale-105 transition-transform duration-200",children:(0,t.jsxs)("svg",{viewBox:"0 0 48 48",className:"w-full h-full",children:[(0,t.jsx)("circle",{cx:"24",cy:"24",r:"22",fill:"none",stroke:"#1e3a8a",strokeWidth:"2"}),(0,t.jsx)("circle",{cx:"24",cy:"12",r:"3",fill:"#00d4ff"}),(0,t.jsx)("circle",{cx:"36",cy:"24",r:"3",fill:"#00d4ff"}),(0,t.jsx)("circle",{cx:"24",cy:"36",r:"3",fill:"#00d4ff"}),(0,t.jsx)("circle",{cx:"12",cy:"24",r:"3",fill:"#00d4ff"}),(0,t.jsx)("circle",{cx:"24",cy:"24",r:"4",fill:"#1e3a8a"}),(0,t.jsx)("line",{x1:"24",y1:"15",x2:"24",y2:"20",stroke:"#00d4ff",strokeWidth:"2"}),(0,t.jsx)("line",{x1:"33",y1:"24",x2:"28",y2:"24",stroke:"#00d4ff",strokeWidth:"2"}),(0,t.jsx)("line",{x1:"24",y1:"33",x2:"24",y2:"28",stroke:"#00d4ff",strokeWidth:"2"}),(0,t.jsx)("line",{x1:"15",y1:"24",x2:"20",y2:"24",stroke:"#00d4ff",strokeWidth:"2"})]})}),(0,t.jsxs)("div",{className:"hidden sm:block",children:[(0,t.jsx)("h1",{className:"text-xl font-bold text-secondary-800 group-hover:text-primary-500 transition-colors duration-200",children:"Candid Connections"}),(0,t.jsx)("p",{className:"text-sm text-candid-gray-600 -mt-1",children:"Katra Platform"})]})]})}),(0,t.jsx)("div",{className:"hidden lg:block",children:(0,t.jsx)("div",{className:"flex items-center space-x-1",children:r.map(s=>(0,t.jsxs)(n(),{href:s.path,className:"flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ".concat(e.pathname===s.path?"nav-link-active":"nav-link"),children:[(0,t.jsx)("span",{className:"text-base",children:s.icon}),(0,t.jsx)("span",{children:s.name})]},s.path))})}),(0,t.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[(0,t.jsx)(n(),{href:"/admin",className:"btn-outline text-sm py-2 px-4",children:"⚙️ Admin"}),(0,t.jsx)(n(),{href:"/global-view",className:"btn-outline text-sm py-2 px-4",children:"\uD83C\uDF10 Network View"}),(0,t.jsx)(n(),{href:"https://portal.candid-connections.com/user/login",target:"_blank",rel:"noopener noreferrer",className:"btn-primary text-sm py-2 px-4",children:"Portal Login"})]}),(0,t.jsx)("div",{className:"lg:hidden",children:(0,t.jsx)("button",{onClick:()=>a(!s),className:"p-2 rounded-lg text-candid-navy-600 hover:text-primary-600 hover:bg-primary-50 transition-colors duration-200","aria-label":"Toggle mobile menu",children:(0,t.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s?(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})})]}),s&&(0,t.jsx)("div",{className:"lg:hidden border-t border-candid-gray-200 py-4",children:(0,t.jsxs)("div",{className:"space-y-2",children:[r.map(s=>(0,t.jsxs)(n(),{href:s.path,onClick:()=>a(!1),className:"flex items-center space-x-3 px-4 py-3 rounded-lg text-base font-medium transition-all duration-200 ".concat(e.pathname===s.path?"nav-link-active":"nav-link"),children:[(0,t.jsx)("span",{className:"text-lg",children:s.icon}),(0,t.jsx)("span",{children:s.name})]},s.path)),(0,t.jsxs)("div",{className:"pt-4 space-y-2",children:[(0,t.jsx)(n(),{href:"/global-view",onClick:()=>a(!1),className:"block w-full btn-outline text-center",children:"\uD83C\uDF10 Network View"}),(0,t.jsx)(n(),{href:"https://portal.candid-connections.com/user/login",target:"_blank",rel:"noopener noreferrer",className:"block w-full btn-primary text-center",children:"Portal Login"})]})]})})]})})}function c(e){let{children:s}=e;return(0,t.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,t.jsx)(o,{}),(0,t.jsx)("main",{className:"container-app section-padding",children:s})]})}},6589:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>m});var t=a(7876),r=a(4232),n=a(7328),l=a.n(n),i=a(9099),o=a(6234);function c(e){let{isOpen:s,onClose:a,title:n,children:l,size:i="md",showCloseButton:o=!0}=e;return((0,r.useEffect)(()=>{let e=e=>{"Escape"===e.key&&s&&a()};return s&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[s,a]),s)?(0,t.jsxs)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:[(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:a}),(0,t.jsx)("div",{className:"flex min-h-full items-center justify-center p-4",children:(0,t.jsxs)("div",{className:"relative bg-white rounded-xl shadow-xl w-full ".concat({sm:"max-w-md",md:"max-w-2xl",lg:"max-w-4xl",xl:"max-w-6xl",full:"max-w-7xl"}[i]," transform transition-all"),onClick:e=>e.stopPropagation(),children:[(n||o)&&(0,t.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[n&&(0,t.jsx)("h3",{className:"text-lg font-semibold text-secondary-800",children:n}),o&&(0,t.jsx)("button",{onClick:a,className:"ml-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 inline-flex items-center justify-center h-8 w-8 transition-colors","aria-label":"Close modal",children:(0,t.jsx)("svg",{className:"w-3 h-3","aria-hidden":"true",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 14 14",children:(0,t.jsx)("path",{stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"})})})]}),(0,t.jsx)("div",{className:"p-6",children:l})]})})]}):null}function d(e){let{isOpen:s,onClose:a,entity:n,entityType:l,onFindTalent:o,onFindMatches:d}=e,[m,x]=(0,r.useState)(""),[p,h]=(0,r.useState)(!1),g=(0,i.useRouter)();(0,r.useEffect)(()=>{s&&n&&u()},[s,n]);let u=async()=>{if(n){h(!0);try{let e=await fetch("/api/ai/describe",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({entity:n,entityType:l})});if(e.ok){let s=await e.json();x(s.description)}else x("Unable to generate description at this time.")}catch(e){console.error("Error generating AI description:",e),x("Unable to generate description at this time.")}finally{h(!1)}}};return n?(0,t.jsx)(c,{isOpen:s,onClose:a,title:"".concat(l.charAt(0).toUpperCase()+l.slice(1)," Details"),size:"lg",children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-secondary-800",children:n.name||n.title}),n.role&&(0,t.jsx)("p",{className:"text-candid-gray-600",children:n.role})]}),(()=>{switch(l){case"skill":return(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Category"}),(0,t.jsx)("p",{className:"text-sm text-gray-900",children:n.category||"N/A"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Market Demand"}),(0,t.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-medium rounded-full ".concat("Very High"===n.demand?"bg-red-100 text-red-800":"High"===n.demand?"bg-orange-100 text-orange-800":"Medium"===n.demand?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"),children:n.demand||"Medium"})]})]})});case"position":return(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Level"}),(0,t.jsx)("p",{className:"text-sm text-gray-900",children:n.level||"N/A"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Type"}),(0,t.jsx)("p",{className:"text-sm text-gray-900",children:n.type||"N/A"})]})]}),n.requirements&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Requirements"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-1 mt-1",children:n.requirements.map((e,s)=>(0,t.jsx)("span",{className:"badge badge-secondary text-xs",children:e},s))})]})]});case"company":return(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Industry"}),(0,t.jsx)("p",{className:"text-sm text-gray-900",children:n.industry||"N/A"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Size"}),(0,t.jsxs)("p",{className:"text-sm text-gray-900",children:[n.employeeCount," employees"]})]})]}),n.description&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Description"}),(0,t.jsx)("p",{className:"text-sm text-gray-900",children:n.description})]})]});default:return null}})(),(0,t.jsxs)("div",{className:"border-t pt-4",children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"AI Analysis"}),p?(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"loading-spinner w-4 h-4"}),(0,t.jsx)("span",{className:"text-sm text-candid-gray-600",children:"Generating intelligent analysis..."})]}):(0,t.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,t.jsx)("p",{className:"text-sm text-blue-900 leading-relaxed",children:m||"No description available."})})]}),(0,t.jsxs)("div",{className:"flex space-x-3 pt-4 border-t",children:[("skill"===l||"position"===l)&&(0,t.jsx)("button",{onClick:()=>{if(o)o(n);else{let e=n.name||n.title;g.push("/job-seekers?skill=".concat(encodeURIComponent(e)))}a()},className:"btn-primary flex-1",children:"Find Talent"}),"skill"!==l&&(0,t.jsx)("button",{onClick:()=>{if(d)d(n);else{let e=n._key||n.id;g.push("/matches?".concat(l,"=").concat(e))}a()},className:"btn-outline flex-1",children:"Find Matches"}),(0,t.jsx)("button",{onClick:a,className:"btn-outline",children:"Close"})]})]})}):null}function m(){let e=(0,i.useRouter)(),[s,a]=(0,r.useState)([]),[n,c]=(0,r.useState)(!0),[m,x]=(0,r.useState)(null),[p,h]=(0,r.useState)(null),[g,u]=(0,r.useState)(!1),[y,j]=(0,r.useState)(""),[f,b]=(0,r.useState)("all"),[v,N]=(0,r.useState)("demand");(0,r.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/skills");if(e.ok){let s=await e.json(),t=(s.skills||s).map(e=>({...e,demand:"Very High"===e.demand?95:"High"===e.demand?85:"Medium"===e.demand?70:60,supply:Math.floor(40*Math.random())+60,averageSalary:k(e.category),jobSeekers:Math.floor(100*Math.random())+50,openPositions:Math.floor(50*Math.random())+20,growth:"+".concat(Math.floor(25*Math.random())+5,"%"),description:w(e.name),relatedSkills:S(e.name),icon:C(e.category)}));a(t)}else a([{id:"skill_1",name:"React",category:"Frontend",demand:95,supply:78,averageSalary:"$105,000",jobSeekers:156,openPositions:89,growth:"+12%",description:"JavaScript library for building user interfaces",relatedSkills:["JavaScript","TypeScript","Redux","Next.js"],icon:"⚛️"},{id:"skill_2",name:"Python",category:"Backend",demand:92,supply:85,averageSalary:"$98,000",jobSeekers:203,openPositions:76,growth:"+8%",description:"High-level programming language for web development, data science, and automation",relatedSkills:["Django","Flask","FastAPI","NumPy"],icon:"\uD83D\uDC0D"},{id:"skill_3",name:"Kubernetes",category:"DevOps",demand:88,supply:45,averageSalary:"$125,000",jobSeekers:67,openPositions:52,growth:"+25%",description:"Container orchestration platform for automating deployment and scaling",relatedSkills:["Docker","Terraform","AWS","Jenkins"],icon:"☸️"},{id:"skill_4",name:"Figma",category:"Design",demand:85,supply:72,averageSalary:"$85,000",jobSeekers:134,openPositions:43,growth:"+15%",description:"Collaborative design tool for creating user interfaces and prototypes",relatedSkills:["Sketch","Adobe XD","Prototyping","User Research"],icon:"\uD83C\uDFA8"},{id:"skill_5",name:"TypeScript",category:"Frontend",demand:82,supply:65,averageSalary:"$108,000",jobSeekers:98,openPositions:67,growth:"+18%",description:"Typed superset of JavaScript that compiles to plain JavaScript",relatedSkills:["JavaScript","React","Angular","Node.js"],icon:"\uD83D\uDCD8"},{id:"skill_6",name:"AWS",category:"Cloud",demand:90,supply:58,averageSalary:"$115,000",jobSeekers:89,openPositions:78,growth:"+20%",description:"Amazon Web Services cloud computing platform",relatedSkills:["EC2","S3","Lambda","CloudFormation"],icon:"☁️"},{id:"skill_7",name:"Machine Learning",category:"Data Science",demand:87,supply:42,averageSalary:"$130,000",jobSeekers:45,openPositions:38,growth:"+30%",description:"AI technique that enables computers to learn and improve from experience",relatedSkills:["Python","TensorFlow","PyTorch","Scikit-learn"],icon:"\uD83E\uDD16"},{id:"skill_8",name:"Node.js",category:"Backend",demand:78,supply:82,averageSalary:"$95,000",jobSeekers:167,openPositions:54,growth:"+5%",description:"JavaScript runtime for building server-side applications",relatedSkills:["Express.js","MongoDB","GraphQL","REST APIs"],icon:"\uD83D\uDFE2"}]);c(!1)}catch(e){x(e.message),c(!1)}})()},[]);let k=e=>({Frontend:"$95,000",Backend:"$98,000",DevOps:"$115,000",Design:"$85,000",Cloud:"$110,000",AI:"$125,000","Data Science":"$120,000",Systems:"$105,000","Soft Skills":"$90,000",Business:"$95,000",Methodology:"$85,000"})[e]||"$95,000",w=e=>({React:"JavaScript library for building user interfaces","Node.js":"JavaScript runtime for server-side development",Python:"High-level programming language for web development and data science",TypeScript:"Typed superset of JavaScript",Kubernetes:"Container orchestration platform",Docker:"Containerization platform",Terraform:"Infrastructure as code tool",AWS:"Amazon Web Services cloud platform",Blockchain:"Distributed ledger technology",Solidity:"Programming language for smart contracts",Figma:"Collaborative design tool","User Research":"Methods for understanding user needs","Machine Learning":"AI technique for pattern recognition",TensorFlow:"Open-source machine learning framework","C++":"General-purpose programming language","Embedded Systems":"Computer systems with dedicated functions",Robotics:"Technology for automated machines",Leadership:"Ability to guide and inspire teams","Product Management":"Strategic product development and planning",Agile:"Iterative software development methodology"})[e]||"Professional skill in ".concat(e),S=e=>({React:["JavaScript","TypeScript","Redux","Next.js"],"Node.js":["Express.js","MongoDB","GraphQL","REST APIs"],Python:["Django","Flask","FastAPI","NumPy"],TypeScript:["JavaScript","React","Angular","Node.js"],Kubernetes:["Docker","Terraform","AWS","Jenkins"],Docker:["Kubernetes","CI/CD","DevOps","Containerization"],AWS:["Cloud Computing","EC2","S3","Lambda"],Figma:["Sketch","Adobe XD","Prototyping","User Research"],"Machine Learning":["Python","TensorFlow","PyTorch","Data Science"]})[e]||["Technology","Software","Development"],C=e=>({Frontend:"⚛️",Backend:"\uD83D\uDC0D",DevOps:"☸️",Design:"\uD83C\uDFA8",Cloud:"☁️",AI:"\uD83E\uDD16","Data Science":"\uD83D\uDCCA",Systems:"⚙️","Soft Skills":"\uD83D\uDC65",Business:"\uD83D\uDCBC",Methodology:"\uD83D\uDCCB",Blockchain:"⛓️",Hardware:"\uD83D\uDD27",Engineering:"\uD83C\uDFD7️"})[e]||"\uD83D\uDEE0️",A=e=>{h(e),u(!0)},D=s=>{e.push("/job-seekers?skill=".concat(encodeURIComponent(s.name)))},P=[...s.filter(e=>{let s=e.name.toLowerCase().includes(y.toLowerCase())||e.category.toLowerCase().includes(y.toLowerCase())||e.description.toLowerCase().includes(y.toLowerCase()),a="all"===f||e.category===f;return s&&a})].sort((e,s)=>{switch(v){case"demand":return s.demand-e.demand;case"supply":return s.supply-e.supply;case"salary":return parseInt(s.averageSalary.replace(/[$,]/g,""))-parseInt(e.averageSalary.replace(/[$,]/g,""));case"growth":return parseInt(s.growth.replace(/[+%]/g,""))-parseInt(e.growth.replace(/[+%]/g,""));case"name":return e.name.localeCompare(s.name);default:return 0}}),M=e=>e>=90?"bg-red-100 text-red-800":e>=80?"bg-orange-100 text-orange-800":e>=70?"bg-yellow-100 text-yellow-800":"bg-green-100 text-green-800",T=e=>e>=80?"bg-green-100 text-green-800":e>=60?"bg-yellow-100 text-yellow-800":e>=40?"bg-orange-100 text-orange-800":"bg-red-100 text-red-800",L=e=>{let s=parseInt(e.replace(/[+%]/g,""));return s>=20?"bg-emerald-100 text-emerald-800":s>=10?"bg-green-100 text-green-800":s>=5?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"},E=["all",...new Set(s.map(e=>e.category))];return n?(0,t.jsx)(o.A,{children:(0,t.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,t.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-amber-500"})})})}):m?(0,t.jsx)(o.A,{children:(0,t.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,t.jsxs)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded",children:["Error: ",m]})})}):(0,t.jsxs)(o.A,{children:[(0,t.jsx)(l(),{children:(0,t.jsx)("title",{children:"Skills Analysis | Candid Connections Katra"})}),(0,t.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold",children:"Skills Analysis"}),(0,t.jsx)("button",{onClick:()=>e.push("/visualizations"),className:"bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors",children:"\uD83D\uDCCA Visualize"})]}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-4 mb-6",children:(0,t.jsxs)("div",{className:"flex flex-wrap gap-4 items-center",children:[(0,t.jsx)("div",{className:"flex-1 min-w-64",children:(0,t.jsx)("input",{type:"text",placeholder:"Search skills, categories, or descriptions...",value:y,onChange:e=>j(e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"})}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Category:"}),(0,t.jsx)("select",{value:f,onChange:e=>b(e.target.value),className:"border border-gray-300 rounded-md px-3 py-1 text-sm",children:E.map(e=>(0,t.jsx)("option",{value:e,children:"all"===e?"All Categories":e},e))})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Sort by:"}),(0,t.jsxs)("select",{value:v,onChange:e=>N(e.target.value),className:"border border-gray-300 rounded-md px-3 py-1 text-sm",children:[(0,t.jsx)("option",{value:"demand",children:"Market Demand"}),(0,t.jsx)("option",{value:"supply",children:"Talent Supply"}),(0,t.jsx)("option",{value:"salary",children:"Average Salary"}),(0,t.jsx)("option",{value:"growth",children:"Growth Rate"}),(0,t.jsx)("option",{value:"name",children:"Skill Name"})]})]}),(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:[P.length," skills found"]})]})}),(0,t.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6",children:P.map(e=>(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"text-2xl mr-3",children:e.icon}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-lg text-gray-900",children:e.name}),(0,t.jsx)("p",{className:"text-gray-600 text-sm",children:e.category})]})]}),(0,t.jsx)("div",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ".concat(L(e.growth)),children:e.growth})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("div",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ".concat(M(e.demand)),children:[e.demand,"% Demand"]}),(0,t.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Market Demand"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("div",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ".concat(T(e.supply)),children:[e.supply,"% Supply"]}),(0,t.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Talent Supply"})]})]}),(0,t.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Average Salary:"}),(0,t.jsx)("span",{className:"font-medium text-emerald-600",children:e.averageSalary})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Job Seekers:"}),(0,t.jsx)("span",{className:"font-medium",children:e.jobSeekers})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Open Positions:"}),(0,t.jsx)("span",{className:"font-medium",children:e.openPositions})]})]}),(0,t.jsx)("p",{className:"text-gray-700 text-sm mb-4",children:e.description}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("h5",{className:"text-sm font-medium text-gray-700 mb-2",children:"Related Skills:"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-1",children:e.relatedSkills.map((e,s)=>(0,t.jsx)("span",{className:"bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded",children:e},s))})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)("button",{onClick:()=>A(e),className:"flex-1 bg-primary-600 text-white px-3 py-2 rounded text-sm hover:bg-primary-700 transition-colors",children:"View Details"}),(0,t.jsx)("button",{onClick:()=>D(e),className:"flex-1 border border-primary-600 text-primary-600 px-3 py-2 rounded text-sm hover:bg-primary-50 transition-colors",children:"Find Talent"})]})]},e.id))}),0===P.length&&(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("div",{className:"text-gray-400 text-6xl mb-4",children:"\uD83D\uDEE0️"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No skills found"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Try adjusting your search or filters to see more results."})]})]}),(0,t.jsx)(d,{isOpen:g,onClose:()=>u(!1),entity:p,entityType:"skill",onFindTalent:D})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[305,636,593,792],()=>s(336)),_N_E=e.O()}]);