(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[950],{336:(e,t,s)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/skills",function(){return s(4311)}])},1467:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var a=s(7876),r=s(4232),n=s(7070);function l(e){let{data:t}=e,s=(0,r.useRef)(null);return(0,r.useEffect)(()=>{var e;if(!t||!s.current)return;n.Ltv(s.current).selectAll("*").remove();let a=n.Ltv(s.current).attr("width",800).attr("height",600).attr("viewBox",[0,0,800,600]),r=n.tXi(t.nodes).force("link",n.kJC(t.links).id(e=>e.id)).force("charge",n.xJS().strength(-400)).force("center",n.jTM(400,300)),l=a.append("g").selectAll("line").data(t.links).join("line").attr("stroke","#999").attr("stroke-opacity",.6).attr("stroke-width",e=>Math.sqrt(e.value||1)),i=a.append("g").selectAll("circle").data(t.nodes).join("circle").attr("r",e=>e.size||5).attr("fill",e=>{switch(e.type){case"jobSeeker":return"#3b82f6";case"company":return"#14b8a6";case"position":return"#10b981";case"skill":return"#f59e0b";default:return"#6366f1"}}).call((e=r,n.$Er().on("start",function(t){t.active||e.alphaTarget(.3).restart(),t.subject.fx=t.subject.x,t.subject.fy=t.subject.y}).on("drag",function(e){e.subject.fx=e.x,e.subject.fy=e.y}).on("end",function(t){t.active||e.alphaTarget(0),t.subject.fx=null,t.subject.fy=null})));return i.append("title").text(e=>e.name),r.on("tick",()=>{l.attr("x1",e=>e.source.x).attr("y1",e=>e.source.y).attr("x2",e=>e.target.x).attr("y2",e=>e.target.y),i.attr("cx",e=>e.x).attr("cy",e=>e.y)}),()=>{r.stop()}},[t]),(0,a.jsx)("div",{className:"border rounded-lg shadow-sm bg-white p-4",children:(0,a.jsx)("svg",{ref:s,className:"w-full h-full"})})}},1566:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(7876),r=s(4232),n=s(1467);function l(e){let{data:t}=e,n=(0,r.useRef)(null);return(0,r.useEffect)(()=>{t&&n.current&&(n.current.innerHTML="",Promise.all([s.e(391),s.e(917),s.e(403),s.e(728),s.e(682)]).then(s.bind(s,4682)).then(e=>{let s=(0,e.default)().width(n.current.clientWidth).height(500).backgroundColor("#ffffff").nodeColor(e=>{switch(e.type){case"jobSeeker":return"#3b82f6";case"company":return"#14b8a6";case"position":return"#10b981";case"skill":return"#f59e0b";default:return"#6366f1"}}).nodeLabel(e=>e.name).nodeVal(e=>e.size||5).linkWidth(e=>e.value||1).linkDirectionalParticles(2).linkDirectionalParticleSpeed(.005).graphData(t)(n.current);window.addEventListener("resize",()=>{n.current&&s.width(n.current.clientWidth)})}).catch(e=>{console.error("Failed to load 3D visualization:",e),n.current&&(n.current.innerHTML='<div class="flex items-center justify-center h-full text-gray-500">3D visualization unavailable</div>')}))},[t]),(0,a.jsx)("div",{ref:n,className:"w-full h-[500px] border rounded-lg shadow-sm bg-white"})}function i(e){let{isOpen:t,onClose:s,data:i,title:o}=e,[c,d]=(0,r.useState)("2D");return t?(0,a.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,a.jsxs)("div",{className:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,a.jsx)("div",{className:"fixed inset-0 transition-opacity","aria-hidden":"true",children:(0,a.jsx)("div",{className:"absolute inset-0 bg-gray-500 opacity-75",onClick:s})}),(0,a.jsxs)("div",{className:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-5xl sm:w-full",children:[(0,a.jsx)("div",{className:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:(0,a.jsx)("div",{className:"sm:flex sm:items-start",children:(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:o||"Network Visualization"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{type:"button",onClick:()=>d("2D"),className:"px-3 py-1 text-sm font-medium rounded-md ".concat("2D"===c?"bg-indigo-100 text-indigo-700":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:"2D View"}),(0,a.jsx)("button",{type:"button",onClick:()=>d("3D"),className:"px-3 py-1 text-sm font-medium rounded-md ".concat("3D"===c?"bg-indigo-100 text-indigo-700":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:"3D View"})]})]}),(0,a.jsx)("div",{className:"mt-2",children:"2D"===c?(0,a.jsx)(n.A,{data:i}):(0,a.jsx)(l,{data:i})})]})})}),(0,a.jsx)("div",{className:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:(0,a.jsx)("button",{type:"button",className:"btn-secondary",onClick:s,children:"Close"})})]})]})}):null}},4311:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var a=s(7876),r=s(4232),n=s(7328),l=s.n(n),i=s(6234),o=s(1566),c=s(8512);function d(){let[e,t]=(0,r.useState)([]),[s,n]=(0,r.useState)(!0),[d,m]=(0,r.useState)(null),[x,u]=(0,r.useState)(!1),[p,h]=(0,r.useState)(null),[g,y]=(0,r.useState)(""),[f,b]=(0,r.useState)("all"),[j,v]=(0,r.useState)("demand");(0,r.useEffect)(()=>{(async()=>{try{setTimeout(()=>{t([{id:"skill_1",name:"React",category:"Frontend",demand:95,supply:78,averageSalary:"$105,000",jobSeekers:156,openPositions:89,growth:"+12%",description:"JavaScript library for building user interfaces",relatedSkills:["JavaScript","TypeScript","Redux","Next.js"],icon:"⚛️"},{id:"skill_2",name:"Python",category:"Backend",demand:92,supply:85,averageSalary:"$98,000",jobSeekers:203,openPositions:76,growth:"+8%",description:"High-level programming language for web development, data science, and automation",relatedSkills:["Django","Flask","FastAPI","NumPy"],icon:"\uD83D\uDC0D"},{id:"skill_3",name:"Kubernetes",category:"DevOps",demand:88,supply:45,averageSalary:"$125,000",jobSeekers:67,openPositions:52,growth:"+25%",description:"Container orchestration platform for automating deployment and scaling",relatedSkills:["Docker","Terraform","AWS","Jenkins"],icon:"☸️"},{id:"skill_4",name:"Figma",category:"Design",demand:85,supply:72,averageSalary:"$85,000",jobSeekers:134,openPositions:43,growth:"+15%",description:"Collaborative design tool for creating user interfaces and prototypes",relatedSkills:["Sketch","Adobe XD","Prototyping","User Research"],icon:"\uD83C\uDFA8"},{id:"skill_5",name:"TypeScript",category:"Frontend",demand:82,supply:65,averageSalary:"$108,000",jobSeekers:98,openPositions:67,growth:"+18%",description:"Typed superset of JavaScript that compiles to plain JavaScript",relatedSkills:["JavaScript","React","Angular","Node.js"],icon:"\uD83D\uDCD8"},{id:"skill_6",name:"AWS",category:"Cloud",demand:90,supply:58,averageSalary:"$115,000",jobSeekers:89,openPositions:78,growth:"+20%",description:"Amazon Web Services cloud computing platform",relatedSkills:["EC2","S3","Lambda","CloudFormation"],icon:"☁️"},{id:"skill_7",name:"Machine Learning",category:"Data Science",demand:87,supply:42,averageSalary:"$130,000",jobSeekers:45,openPositions:38,growth:"+30%",description:"AI technique that enables computers to learn and improve from experience",relatedSkills:["Python","TensorFlow","PyTorch","Scikit-learn"],icon:"\uD83E\uDD16"},{id:"skill_8",name:"Node.js",category:"Backend",demand:78,supply:82,averageSalary:"$95,000",jobSeekers:167,openPositions:54,growth:"+5%",description:"JavaScript runtime for building server-side applications",relatedSkills:["Express.js","MongoDB","GraphQL","REST APIs"],icon:"\uD83D\uDFE2"}]),h((0,c._)()),n(!1)},1e3)}catch(e){m(e.message),n(!1)}})()},[]);let N=[...e.filter(e=>{let t=e.name.toLowerCase().includes(g.toLowerCase())||e.category.toLowerCase().includes(g.toLowerCase())||e.description.toLowerCase().includes(g.toLowerCase()),s="all"===f||e.category===f;return t&&s})].sort((e,t)=>{switch(j){case"demand":return t.demand-e.demand;case"supply":return t.supply-e.supply;case"salary":return parseInt(t.averageSalary.replace(/[$,]/g,""))-parseInt(e.averageSalary.replace(/[$,]/g,""));case"growth":return parseInt(t.growth.replace(/[+%]/g,""))-parseInt(e.growth.replace(/[+%]/g,""));case"name":return e.name.localeCompare(t.name);default:return 0}}),k=e=>e>=90?"bg-red-100 text-red-800":e>=80?"bg-orange-100 text-orange-800":e>=70?"bg-yellow-100 text-yellow-800":"bg-green-100 text-green-800",w=e=>e>=80?"bg-green-100 text-green-800":e>=60?"bg-yellow-100 text-yellow-800":e>=40?"bg-orange-100 text-orange-800":"bg-red-100 text-red-800",S=e=>{let t=parseInt(e.replace(/[+%]/g,""));return t>=20?"bg-emerald-100 text-emerald-800":t>=10?"bg-green-100 text-green-800":t>=5?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"},C=["all",...new Set(e.map(e=>e.category))];return s?(0,a.jsx)(i.A,{children:(0,a.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,a.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-amber-500"})})})}):d?(0,a.jsx)(i.A,{children:(0,a.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,a.jsxs)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded",children:["Error: ",d]})})}):(0,a.jsxs)(i.A,{children:[(0,a.jsx)(l(),{children:(0,a.jsx)("title",{children:"Skills Analysis | Candid Connections Katra"})}),(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold",children:"Skills Analysis"}),(0,a.jsx)("button",{onClick:()=>u(!0),className:"bg-amber-600 text-white px-4 py-2 rounded-lg hover:bg-amber-700 transition-colors",children:"\uD83C\uDF10 View Network"})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex flex-wrap gap-4 items-center",children:[(0,a.jsx)("div",{className:"flex-1 min-w-64",children:(0,a.jsx)("input",{type:"text",placeholder:"Search skills, categories, or descriptions...",value:g,onChange:e=>y(e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"})}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Category:"}),(0,a.jsx)("select",{value:f,onChange:e=>b(e.target.value),className:"border border-gray-300 rounded-md px-3 py-1 text-sm",children:C.map(e=>(0,a.jsx)("option",{value:e,children:"all"===e?"All Categories":e},e))})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Sort by:"}),(0,a.jsxs)("select",{value:j,onChange:e=>v(e.target.value),className:"border border-gray-300 rounded-md px-3 py-1 text-sm",children:[(0,a.jsx)("option",{value:"demand",children:"Market Demand"}),(0,a.jsx)("option",{value:"supply",children:"Talent Supply"}),(0,a.jsx)("option",{value:"salary",children:"Average Salary"}),(0,a.jsx)("option",{value:"growth",children:"Growth Rate"}),(0,a.jsx)("option",{value:"name",children:"Skill Name"})]})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[N.length," skills found"]})]})}),(0,a.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6",children:N.map(e=>(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-2xl mr-3",children:e.icon}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-lg text-gray-900",children:e.name}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:e.category})]})]}),(0,a.jsx)("div",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ".concat(S(e.growth)),children:e.growth})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ".concat(k(e.demand)),children:[e.demand,"% Demand"]}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Market Demand"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ".concat(w(e.supply)),children:[e.supply,"% Supply"]}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Talent Supply"})]})]}),(0,a.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Average Salary:"}),(0,a.jsx)("span",{className:"font-medium text-emerald-600",children:e.averageSalary})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Job Seekers:"}),(0,a.jsx)("span",{className:"font-medium",children:e.jobSeekers})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Open Positions:"}),(0,a.jsx)("span",{className:"font-medium",children:e.openPositions})]})]}),(0,a.jsx)("p",{className:"text-gray-700 text-sm mb-4",children:e.description}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("h5",{className:"text-sm font-medium text-gray-700 mb-2",children:"Related Skills:"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-1",children:e.relatedSkills.map((e,t)=>(0,a.jsx)("span",{className:"bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded",children:e},t))})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{className:"flex-1 bg-amber-600 text-white px-3 py-2 rounded text-sm hover:bg-amber-700 transition-colors",children:"View Details"}),(0,a.jsx)("button",{className:"flex-1 border border-amber-600 text-amber-600 px-3 py-2 rounded text-sm hover:bg-amber-50 transition-colors",children:"Find Talent"})]})]},e.id))}),0===N.length&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-gray-400 text-6xl mb-4",children:"\uD83D\uDEE0️"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No skills found"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Try adjusting your search or filters to see more results."})]})]}),(0,a.jsx)(o.A,{isOpen:x,onClose:()=>u(!1),data:p,title:"Skills Network"})]})}},6234:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var a=s(7876),r=s(8230),n=s.n(r),l=s(9099);function i(){let e=(0,l.useRouter)();return(0,a.jsx)("nav",{className:"bg-white shadow-md",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,a.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,a.jsx)(n(),{href:"/",className:"font-bold text-xl text-indigo-600",children:"Candid Connections Katra"})}),(0,a.jsx)("div",{className:"hidden md:block",children:(0,a.jsx)("div",{className:"ml-10 flex items-center space-x-4",children:[{name:"Dashboard",path:"/"},{name:"Job Matches",path:"/matches"},{name:"Job Seekers",path:"/job-seekers"},{name:"Companies",path:"/companies"},{name:"Positions",path:"/positions"},{name:"Skills",path:"/skills"}].map(t=>(0,a.jsx)(n(),{href:t.path,className:"px-3 py-2 rounded-md text-sm font-medium ".concat(e.pathname===t.path?"bg-indigo-100 text-indigo-700":"text-gray-700 hover:bg-gray-100"),children:t.name},t.path))})})]})})})}function o(e){let{children:t}=e;return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)(i,{}),(0,a.jsx)("main",{children:t})]})}},8512:(e,t,s)=>{"use strict";s.d(t,{_:()=>a});let a=()=>{let e=Array.from({length:5},(e,t)=>({id:"jobSeekers/".concat(t),name:"Job Seeker ".concat(t+1),type:"jobSeeker",size:8})),t=Array.from({length:3},(e,t)=>({id:"companies/".concat(t),name:"Company ".concat(t+1),type:"company",size:10})),s=Array.from({length:4},(e,t)=>({id:"positions/".concat(t),name:"Position ".concat(t+1),type:"position",size:9})),a=Array.from({length:8},(e,t)=>({id:"skills/".concat(t),name:"Skill ".concat(t+1),type:"skill",size:6})),r=[...e,...t,...s,...a],n=[];return e.forEach((e,s)=>{n.push({source:e.id,target:t[s%t.length].id,type:"works_for",value:1})}),t.forEach((e,t)=>{s.forEach((s,a)=>{(t+a)%2==0&&n.push({source:e.id,target:s.id,type:"posts",value:1})})}),e.forEach(e=>{let t=2+Math.floor(3*Math.random());[...a].sort(()=>.5-Math.random()).slice(0,t).forEach(t=>{n.push({source:e.id,target:t.id,type:"has_skill",value:1})})}),s.forEach(e=>{let t=2+Math.floor(2*Math.random());[...a].sort(()=>.5-Math.random()).slice(0,t).forEach(t=>{n.push({source:e.id,target:t.id,type:"requires",value:1})})}),{nodes:r,links:n}}}},e=>{var t=t=>e(e.s=t);e.O(0,[305,70,636,593,792],()=>t(336)),_N_E=e.O()}]);