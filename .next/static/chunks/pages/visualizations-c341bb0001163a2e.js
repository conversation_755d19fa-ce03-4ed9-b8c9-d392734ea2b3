(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[519],{1408:(e,t,s)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/visualizations",function(){return s(7095)}])},6234:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var a=s(7876),i=s(8230),r=s.n(i),n=s(9099),l=s(4232);function c(){let e=(0,n.useRouter)(),[t,s]=(0,l.useState)(!1),i=[{name:"Dashboard",path:"/",icon:"\uD83C\uDFE0"},{name:"Authority Matches",path:"/matches",icon:"\uD83C\uDFAF"},{name:"Job Seekers",path:"/job-seekers",icon:"\uD83D\uDC65"},{name:"Hiring Authorities",path:"/hiring-authorities",icon:"\uD83D\uDC54"},{name:"Companies",path:"/companies",icon:"\uD83C\uDFE2"},{name:"Positions",path:"/positions",icon:"\uD83D\uDCCB"},{name:"Skills",path:"/skills",icon:"\uD83D\uDEE0️"},{name:"Visualizations",path:"/visualizations",icon:"\uD83D\uDCCA"},{name:"Network View",path:"/global-view",icon:"\uD83C\uDF10"}];return(0,a.jsx)("nav",{className:"bg-white shadow-soft border-b border-candid-gray-200",children:(0,a.jsxs)("div",{className:"container-app",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center h-20",children:[(0,a.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,a.jsxs)(r(),{href:"/",className:"flex items-center space-x-3 group",children:[(0,a.jsx)("div",{className:"w-12 h-12 relative group-hover:scale-105 transition-transform duration-200",children:(0,a.jsxs)("svg",{viewBox:"0 0 48 48",className:"w-full h-full",children:[(0,a.jsx)("circle",{cx:"24",cy:"24",r:"22",fill:"none",stroke:"#1e3a8a",strokeWidth:"2"}),(0,a.jsx)("circle",{cx:"24",cy:"12",r:"3",fill:"#00d4ff"}),(0,a.jsx)("circle",{cx:"36",cy:"24",r:"3",fill:"#00d4ff"}),(0,a.jsx)("circle",{cx:"24",cy:"36",r:"3",fill:"#00d4ff"}),(0,a.jsx)("circle",{cx:"12",cy:"24",r:"3",fill:"#00d4ff"}),(0,a.jsx)("circle",{cx:"24",cy:"24",r:"4",fill:"#1e3a8a"}),(0,a.jsx)("line",{x1:"24",y1:"15",x2:"24",y2:"20",stroke:"#00d4ff",strokeWidth:"2"}),(0,a.jsx)("line",{x1:"33",y1:"24",x2:"28",y2:"24",stroke:"#00d4ff",strokeWidth:"2"}),(0,a.jsx)("line",{x1:"24",y1:"33",x2:"24",y2:"28",stroke:"#00d4ff",strokeWidth:"2"}),(0,a.jsx)("line",{x1:"15",y1:"24",x2:"20",y2:"24",stroke:"#00d4ff",strokeWidth:"2"})]})}),(0,a.jsxs)("div",{className:"hidden sm:block",children:[(0,a.jsx)("h1",{className:"text-xl font-bold text-secondary-800 group-hover:text-primary-500 transition-colors duration-200",children:"Candid Connections"}),(0,a.jsx)("p",{className:"text-sm text-candid-gray-600 -mt-1",children:"Katra Platform"})]})]})}),(0,a.jsx)("div",{className:"hidden lg:block",children:(0,a.jsx)("div",{className:"flex items-center space-x-1",children:i.map(t=>(0,a.jsxs)(r(),{href:t.path,className:"flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ".concat(e.pathname===t.path?"nav-link-active":"nav-link"),children:[(0,a.jsx)("span",{className:"text-base",children:t.icon}),(0,a.jsx)("span",{children:t.name})]},t.path))})}),(0,a.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[(0,a.jsx)(r(),{href:"/admin",className:"btn-outline text-sm py-2 px-4",children:"⚙️ Admin"}),(0,a.jsx)(r(),{href:"/global-view",className:"btn-outline text-sm py-2 px-4",children:"\uD83C\uDF10 Network View"}),(0,a.jsx)(r(),{href:"https://portal.candid-connections.com/user/login",target:"_blank",rel:"noopener noreferrer",className:"btn-primary text-sm py-2 px-4",children:"Portal Login"})]}),(0,a.jsx)("div",{className:"lg:hidden",children:(0,a.jsx)("button",{onClick:()=>s(!t),className:"p-2 rounded-lg text-candid-navy-600 hover:text-primary-600 hover:bg-primary-50 transition-colors duration-200","aria-label":"Toggle mobile menu",children:(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:t?(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})})]}),t&&(0,a.jsx)("div",{className:"lg:hidden border-t border-candid-gray-200 py-4",children:(0,a.jsxs)("div",{className:"space-y-2",children:[i.map(t=>(0,a.jsxs)(r(),{href:t.path,onClick:()=>s(!1),className:"flex items-center space-x-3 px-4 py-3 rounded-lg text-base font-medium transition-all duration-200 ".concat(e.pathname===t.path?"nav-link-active":"nav-link"),children:[(0,a.jsx)("span",{className:"text-lg",children:t.icon}),(0,a.jsx)("span",{children:t.name})]},t.path)),(0,a.jsxs)("div",{className:"pt-4 space-y-2",children:[(0,a.jsx)(r(),{href:"/global-view",onClick:()=>s(!1),className:"block w-full btn-outline text-center",children:"\uD83C\uDF10 Network View"}),(0,a.jsx)(r(),{href:"https://portal.candid-connections.com/user/login",target:"_blank",rel:"noopener noreferrer",className:"block w-full btn-primary text-center",children:"Portal Login"})]})]})})]})})}function o(e){let{children:t}=e;return(0,a.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,a.jsx)(c,{}),(0,a.jsx)("main",{className:"container-app section-padding",children:t})]})}},7095:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var a=s(7876),i=s(4232),r=s(7328),n=s.n(r),l=s(6234),c=s(4274);function o(e){let{data:t,width:s=800,height:r=600}=e,n=(0,i.useRef)(),[l,o]=(0,i.useState)(null);return(0,i.useEffect)(()=>{if(!t||!t.nodes||!t.links)return;let e=c.Ltv(n.current);e.selectAll("*").remove();let a=c.tXi(t.nodes).force("link",c.kJC(t.links).id(e=>e.id).distance(100)).force("charge",c.xJS().strength(-300)).force("center",c.jTM(s/2,r/2)).force("collision",c.eRw().radius(30)),i=e.attr("width",s).attr("height",r).attr("viewBox",[0,0,s,r]),l=c.s_O().scaleExtent([.1,4]).on("zoom",e=>{d.attr("transform",e.transform)});e.call(l);let d=i.append("g");e.append("defs").selectAll("marker").data(["hiring","skill","company"]).join("marker").attr("id",e=>"arrow-".concat(e)).attr("viewBox","0 -5 10 10").attr("refX",20).attr("refY",0).attr("markerWidth",6).attr("markerHeight",6).attr("orient","auto").append("path").attr("d","M0,-5L10,0L0,5").attr("fill",e=>{switch(e){case"hiring":return"#00d4ff";case"skill":return"#f97316";case"company":return"#8b5cf6";default:return"#6b7280"}});let h=d.append("g").selectAll("line").data(t.links).join("line").attr("stroke",e=>{switch(e.type){case"hiring":return"#00d4ff";case"skill":return"#f97316";case"company":return"#8b5cf6";default:return"#6b7280"}}).attr("stroke-opacity",.6).attr("stroke-width",e=>2*Math.sqrt(e.strength||1)).attr("marker-end",e=>"url(#arrow-".concat(e.type,")")),m=d.append("g").selectAll("g").data(t.nodes).join("g").attr("class","node").style("cursor","pointer").call(c.$Er().on("start",function(e,t){e.active||a.alphaTarget(.3).restart(),t.fx=t.x,t.fy=t.y}).on("drag",function(e,t){t.fx=e.x,t.fy=e.y}).on("end",function(e,t){e.active||a.alphaTarget(0),t.fx=null,t.fy=null}));return m.append("circle").attr("r",e=>{switch(e.type){case"company":return 25;case"authority":return 20;case"jobSeeker":return 15;case"skill":return 10;default:return 12}}).attr("fill",e=>{switch(e.type){case"company":return"#8b5cf6";case"authority":return"#00d4ff";case"jobSeeker":return"#f97316";case"skill":return"#10b981";default:return"#6b7280"}}).attr("stroke","#fff").attr("stroke-width",2),m.append("text").text(e=>e.name||e.id).attr("x",0).attr("y",e=>{switch(e.type){case"company":return 35;case"authority":return 30;case"jobSeeker":return 25;case"skill":return 20;default:return 22}}).attr("text-anchor","middle").attr("font-family","Inter, system-ui, -apple-system, sans-serif").attr("font-size","11px").attr("font-weight","600").attr("fill","#1f2937").style("text-shadow","0 1px 2px rgba(255, 255, 255, 0.8)"),m.filter(e=>"authority"===e.type).append("text").text(e=>{var t;return(null==(t=e.level)?void 0:t.charAt(0))||"A"}).attr("x",0).attr("y",5).attr("text-anchor","middle").attr("font-family","Inter, system-ui, -apple-system, sans-serif").attr("font-size","10px").attr("font-weight","700").attr("fill","#ffffff").style("text-shadow","0 1px 2px rgba(0, 0, 0, 0.3)"),m.filter(e=>"company"===e.type).append("text").text(e=>e.employeeCount<100?"S":e.employeeCount<1e3?"M":"E").attr("x",0).attr("y",5).attr("text-anchor","middle").attr("font-family","Inter, system-ui, -apple-system, sans-serif").attr("font-size","12px").attr("font-weight","700").attr("fill","#ffffff").style("text-shadow","0 1px 2px rgba(0, 0, 0, 0.3)"),m.on("mouseover",function(e,t){c.Ltv(this).select("circle").transition().duration(200).attr("r",e=>1.2*("company"===e.type?25:"authority"===e.type?20:"jobSeeker"===e.type?15:10)).attr("stroke-width",3),h.attr("stroke-opacity",e=>e.source.id===t.id||e.target.id===t.id?1:.1).attr("stroke-width",e=>e.source.id===t.id||e.target.id===t.id?3*Math.sqrt(e.strength||1):2*Math.sqrt(e.strength||1))}).on("mouseout",function(e,t){c.Ltv(this).select("circle").transition().duration(200).attr("r",e=>{switch(e.type){case"company":return 25;case"authority":return 20;case"jobSeeker":return 15;case"skill":return 10;default:return 12}}).attr("stroke-width",2),h.attr("stroke-opacity",.6).attr("stroke-width",e=>2*Math.sqrt(e.strength||1))}).on("click",function(e,t){o(t)}),a.on("tick",()=>{h.attr("x1",e=>e.source.x).attr("y1",e=>e.source.y).attr("x2",e=>e.target.x).attr("y2",e=>e.target.y),m.attr("transform",e=>"translate(".concat(e.x,",").concat(e.y,")"))}),()=>{a.stop()}},[t,s,r]),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("svg",{ref:n,className:"border border-gray-200 rounded-lg bg-white"}),(0,a.jsxs)("div",{className:"absolute top-4 left-4 bg-white p-4 rounded-lg shadow-lg border",children:[(0,a.jsx)("h4",{className:"font-semibold text-sm mb-2",children:"Network Legend"}),(0,a.jsxs)("div",{className:"space-y-2 text-xs",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-4 h-4 rounded-full bg-purple-500"}),(0,a.jsx)("span",{children:"Companies (S/M/E = Size)"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-4 h-4 rounded-full bg-primary-500"}),(0,a.jsx)("span",{children:"Hiring Authorities"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-4 h-4 rounded-full bg-orange-500"}),(0,a.jsx)("span",{children:"Job Seekers"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-4 h-4 rounded-full bg-green-500"}),(0,a.jsx)("span",{children:"Skills"})]})]})]}),l&&(0,a.jsxs)("div",{className:"absolute top-4 right-4 bg-white p-4 rounded-lg shadow-lg border max-w-xs",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsx)("h4",{className:"font-semibold text-sm",children:l.name||l.id}),(0,a.jsx)("button",{onClick:()=>o(null),className:"text-gray-400 hover:text-gray-600",children:"\xd7"})]}),(0,a.jsxs)("div",{className:"text-xs space-y-1",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Type:"})," ",l.type]}),l.level&&(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Level:"})," ",l.level]}),l.hiringPower&&(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Hiring Power:"})," ",l.hiringPower]}),l.employeeCount&&(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Size:"})," ",l.employeeCount," employees"]}),l.experience&&(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Experience:"})," ",l.experience," years"]})]})]})]})}function d(){let[e,t]=(0,i.useState)({companies:[],hiringAuthorities:[],jobSeekers:[],skills:[],matches:[]}),[s,r]=(0,i.useState)(!0),[c,d]=(0,i.useState)("network"),[h,m]=(0,i.useState)({nodes:[],links:[]});return((0,i.useEffect)(()=>{(async()=>{try{r(!0);let[e,s,a,i,n]=await Promise.all([fetch("/api/companies"),fetch("/api/hiring-authorities"),fetch("/api/job-seekers"),fetch("/api/skills"),fetch("/api/matches")]),[l,c,o,d,h]=await Promise.all([e.json(),s.json(),a.json(),i.json(),n.json()]),x={companies:l.companies||l,hiringAuthorities:c.authorities||c,jobSeekers:o.jobSeekers||o,skills:d.skills||d,matches:h.matches||h};t(x);let p=function(e,t,s,a,i){let r=[],n=[];e.forEach(e=>{r.push({id:"company_".concat(e._key),name:e.name,type:"company",employeeCount:e.employeeCount,industry:e.industry,size:e.size,x:400*Math.random()+200,y:300*Math.random()+150})}),t.forEach(e=>{var t;r.push({id:"authority_".concat(e._key),name:e.name,type:"authority",role:e.role,level:e.level,hiringPower:e.hiringPower,decisionMaker:e.decisionMaker,companyId:e.companyId,x:400*Math.random()+200,y:300*Math.random()+150});let s=null==(t=e.companyId)?void 0:t.split("/")[1];s&&n.push({source:"company_".concat(s),target:"authority_".concat(e._key),type:"company",strength:"Ultimate"===e.hiringPower?3:"High"===e.hiringPower?2:1})});let l=s.slice(0,10);l.forEach(e=>{r.push({id:"jobSeeker_".concat(e._key),name:e.name,type:"jobSeeker",currentTitle:e.currentTitle,experience:e.experience,skills:e.skills,x:400*Math.random()+200,y:300*Math.random()+150})});let c=a.slice(0,8);return c.forEach(e=>{r.push({id:"skill_".concat(e._key),name:e.name,type:"skill",category:e.category,demand:e.demand,x:400*Math.random()+200,y:300*Math.random()+150})}),i.filter(e=>e.score>=75).slice(0,20).forEach(e=>{var t,s;let a=null==(t=e.jobSeekerId)?void 0:t.split("/")[1],i=null==(s=e.hiringAuthorityId)?void 0:s.split("/")[1];if(a&&i){let t=r.find(e=>e.id==="jobSeeker_".concat(a)),s=r.find(e=>e.id==="authority_".concat(i));t&&s&&n.push({source:"jobSeeker_".concat(a),target:"authority_".concat(i),type:"hiring",strength:e.score/20,score:e.score,connectionStrength:e.connectionStrength})}}),l.forEach(e=>{e.skills&&e.skills.slice(0,3).forEach(t=>{if(r.find(e=>e.id==="skill_".concat(t))){var s;n.push({source:"jobSeeker_".concat(e._key),target:"skill_".concat(t),type:"skill",strength:((null==(s=e.skillLevels)?void 0:s[t])||5)/10})}})}),t.forEach(e=>{e.skillsLookingFor&&e.skillsLookingFor.slice(0,3).forEach(t=>{let s=c.find(e=>e.name.toLowerCase()===t.toLowerCase());s&&n.push({source:"authority_".concat(e._key),target:"skill_".concat(s._key),type:"skill",strength:2})})}),{nodes:r,links:n}}(x.companies,x.hiringAuthorities,x.jobSeekers,x.skills,x.matches);m(p)}catch(e){console.error("Error fetching visualization data:",e)}finally{r(!1)}})()},[]),s)?(0,a.jsx)(l.A,{children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"loading-spinner w-12 h-12 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-candid-gray-600",children:"Loading visualization data..."})]})})}):(0,a.jsxs)(l.A,{children:[(0,a.jsxs)(n(),{children:[(0,a.jsx)("title",{children:"Data Visualizations | Candid Connections Katra"}),(0,a.jsx)("meta",{name:"description",content:"Interactive visualizations of hiring authority networks, company hierarchies, and skill connections."})]}),(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold text-secondary-800 mb-4",children:"Data Visualizations"}),(0,a.jsx)("p",{className:"text-xl text-candid-gray-600 max-w-3xl mx-auto",children:"Explore the interconnected relationships between job seekers, hiring authorities, companies, and skills through interactive visualizations."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4 mb-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:e.companies.length}),(0,a.jsx)("div",{className:"text-sm text-candid-gray-500",children:"Companies"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-primary-600",children:e.hiringAuthorities.length}),(0,a.jsx)("div",{className:"text-sm text-candid-gray-500",children:"Authorities"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:e.jobSeekers.length}),(0,a.jsx)("div",{className:"text-sm text-candid-gray-500",children:"Job Seekers"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:e.skills.length}),(0,a.jsx)("div",{className:"text-sm text-candid-gray-500",children:"Skills"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-accent-600",children:e.matches.length}),(0,a.jsx)("div",{className:"text-sm text-candid-gray-500",children:"Matches"})]})]}),(0,a.jsx)("div",{className:"border-b border-gray-200",children:(0,a.jsx)("nav",{className:"-mb-px flex space-x-8",children:[{id:"network",name:"Authority Network",icon:"\uD83C\uDF10"},{id:"hierarchy",name:"Company Hierarchy",icon:"\uD83C\uDFE2"},{id:"heatmap",name:"Match Heatmap",icon:"\uD83D\uDD25"},{id:"skills",name:"Skill Demand",icon:"\uD83D\uDCCA"}].map(e=>(0,a.jsxs)("button",{onClick:()=>d(e.id),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat(c===e.id?"border-primary-500 text-primary-600":"border-transparent text-candid-gray-500 hover:text-candid-gray-700 hover:border-candid-gray-300"),children:[(0,a.jsx)("span",{className:"mr-2",children:e.icon}),e.name]},e.id))})}),(0,a.jsx)("div",{className:"card",children:(0,a.jsxs)("div",{className:"card-body",children:["network"===c&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-secondary-800 mb-4",children:"Authority Network Graph"}),(0,a.jsx)("p",{className:"text-candid-gray-600 mb-6",children:"Interactive network showing connections between companies, hiring authorities, job seekers, and skills. Drag nodes to explore relationships, hover for details, and click for more information."}),(0,a.jsx)("div",{className:"bg-gray-50 p-4 rounded-lg",children:(0,a.jsx)(o,{data:h,width:800,height:600})})]}),"hierarchy"===c&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-secondary-800 mb-4",children:"Company Hierarchy Visualization"}),(0,a.jsx)("p",{className:"text-candid-gray-600 mb-6",children:"Organizational charts showing hiring authority levels within each company based on size and structure."}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:e.companies.map(t=>{let s=e.hiringAuthorities.filter(e=>e.companyId==="companies/".concat(t._key));return(0,a.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"text-center mb-4",children:[(0,a.jsx)("h4",{className:"font-semibold text-lg",children:t.name}),(0,a.jsxs)("p",{className:"text-sm text-candid-gray-600",children:[t.employeeCount," employees • ",t.industry]})]}),(0,a.jsx)("div",{className:"space-y-3",children:["C-Suite","Executive","Director","Manager"].map(e=>{let t=s.filter(t=>t.level===e);return 0===t.length?null:(0,a.jsxs)("div",{className:"border-l-4 border-primary-500 pl-4",children:[(0,a.jsx)("h5",{className:"font-medium text-sm text-secondary-700",children:e}),(0,a.jsx)("div",{className:"space-y-1",children:t.map(e=>(0,a.jsxs)("div",{className:"text-xs",children:[(0,a.jsx)("span",{className:"font-medium",children:e.name}),(0,a.jsxs)("span",{className:"text-candid-gray-500",children:[" - ",e.role]}),(0,a.jsx)("span",{className:"ml-2 px-2 py-1 rounded text-xs ".concat("Ultimate"===e.hiringPower?"bg-red-100 text-red-800":"High"===e.hiringPower?"bg-orange-100 text-orange-800":"bg-yellow-100 text-yellow-800"),children:e.hiringPower})]},e._key))})]},e)})})]},t._key)})})]}),"heatmap"===c&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-secondary-800 mb-4",children:"Authority Match Heatmap"}),(0,a.jsx)("p",{className:"text-candid-gray-600 mb-6",children:"Heat map showing match strength between job seekers and hiring authorities. Darker colors indicate stronger matches."}),(0,a.jsxs)("div",{className:"text-center text-candid-gray-500 py-12",children:[(0,a.jsx)("div",{className:"text-4xl mb-4",children:"\uD83D\uDD25"}),(0,a.jsx)("p",{children:"Interactive heatmap visualization coming soon..."}),(0,a.jsxs)("p",{className:"text-sm mt-2",children:["Will show ",e.matches.length," authority matches"]})]})]}),"skills"===c&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-secondary-800 mb-4",children:"Skill Demand Analysis"}),(0,a.jsx)("p",{className:"text-candid-gray-600 mb-6",children:"Analysis of skill supply and demand across job seekers and hiring authorities."}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold mb-3",children:"Most Common Skills (Job Seekers)"}),(0,a.jsx)("div",{className:"space-y-2",children:e.skills.slice(0,10).map((t,s)=>{let i=e.jobSeekers.filter(e=>{var s;return null==(s=e.skills)?void 0:s.includes(t._key)}).length;return(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm",children:t.name}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-20 bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-primary-500 h-2 rounded-full",style:{width:"".concat(i/e.jobSeekers.length*100,"%")}})}),(0,a.jsx)("span",{className:"text-xs text-candid-gray-500",children:i})]})]},t._key)})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold mb-3",children:"Most Demanded Skills (Authorities)"}),(0,a.jsx)("div",{className:"space-y-2",children:e.skills.slice(0,10).map((t,s)=>{let i=e.hiringAuthorities.filter(e=>{var s;return null==(s=e.skillsLookingFor)?void 0:s.includes(t.name)}).length;return(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm",children:t.name}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-20 bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-accent-500 h-2 rounded-full",style:{width:"".concat(i/e.hiringAuthorities.length*100,"%")}})}),(0,a.jsx)("span",{className:"text-xs text-candid-gray-500",children:i})]})]},t._key)})})]})]})]})]})}),(0,a.jsx)("div",{className:"card",children:(0,a.jsxs)("div",{className:"card-body",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-secondary-800 mb-4",children:"Key Insights"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-3xl mb-2",children:"\uD83C\uDFAF"}),(0,a.jsx)("h4",{className:"font-semibold mb-2",children:"Match Quality"}),(0,a.jsxs)("p",{className:"text-sm text-candid-gray-600",children:[e.matches.filter(e=>e.score>=80).length," high-quality matches (80%+ score) out of ",e.matches.length," total connections"]})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-3xl mb-2",children:"\uD83C\uDFE2"}),(0,a.jsx)("h4",{className:"font-semibold mb-2",children:"Company Distribution"}),(0,a.jsxs)("p",{className:"text-sm text-candid-gray-600",children:[e.companies.filter(e=>e.employeeCount<100).length," startups, "," ",e.companies.filter(e=>e.employeeCount>=100&&e.employeeCount<1e3).length," mid-size, "," ",e.companies.filter(e=>e.employeeCount>=1e3).length," enterprise"]})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-3xl mb-2",children:"\uD83D\uDC54"}),(0,a.jsx)("h4",{className:"font-semibold mb-2",children:"Authority Levels"}),(0,a.jsxs)("p",{className:"text-sm text-candid-gray-600",children:[e.hiringAuthorities.filter(e=>"Ultimate"===e.hiringPower).length," ultimate, "," ",e.hiringAuthorities.filter(e=>"High"===e.hiringPower).length," high, "," ",e.hiringAuthorities.filter(e=>"Medium"===e.hiringPower).length," medium power authorities"]})]})]})]})})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[305,274,636,593,792],()=>t(1408)),_N_E=e.O()}]);