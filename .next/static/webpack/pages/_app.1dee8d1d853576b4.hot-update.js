"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./styles/globals.css":
/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./styles/globals.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');\\n\\n*, ::before, ::after{\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\n\\n::backdrop{\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\n\\n/*\\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\\n*/\\n\\n/*\\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\\n*/\\n\\n*,\\n::before,\\n::after {\\n  box-sizing: border-box; /* 1 */\\n  border-width: 0; /* 2 */\\n  border-style: solid; /* 2 */\\n  border-color: #e5e7eb; /* 2 */\\n}\\n\\n::before,\\n::after {\\n  --tw-content: '';\\n}\\n\\n/*\\n1. Use a consistent sensible line-height in all browsers.\\n2. Prevent adjustments of font size after orientation changes in iOS.\\n3. Use a more readable tab size.\\n4. Use the user's configured `sans` font-family by default.\\n5. Use the user's configured `sans` font-feature-settings by default.\\n6. Use the user's configured `sans` font-variation-settings by default.\\n7. Disable tap highlights on iOS\\n*/\\n\\nhtml,\\n:host {\\n  line-height: 1.5; /* 1 */\\n  -webkit-text-size-adjust: 100%; /* 2 */\\n  -moz-tab-size: 4; /* 3 */\\n  -o-tab-size: 4;\\n     tab-size: 4; /* 3 */\\n  font-family: Inter, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif; /* 4 */\\n  font-feature-settings: normal; /* 5 */\\n  font-variation-settings: normal; /* 6 */\\n  -webkit-tap-highlight-color: transparent; /* 7 */\\n}\\n\\n/*\\n1. Remove the margin in all browsers.\\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\\n*/\\n\\nbody {\\n  margin: 0; /* 1 */\\n  line-height: inherit; /* 2 */\\n}\\n\\n/*\\n1. Add the correct height in Firefox.\\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n3. Ensure horizontal rules are visible by default.\\n*/\\n\\nhr {\\n  height: 0; /* 1 */\\n  color: inherit; /* 2 */\\n  border-top-width: 1px; /* 3 */\\n}\\n\\n/*\\nAdd the correct text decoration in Chrome, Edge, and Safari.\\n*/\\n\\nabbr:where([title]) {\\n  -webkit-text-decoration: underline dotted;\\n          text-decoration: underline dotted;\\n}\\n\\n/*\\nRemove the default font size and weight for headings.\\n*/\\n\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n  font-size: inherit;\\n  font-weight: inherit;\\n}\\n\\n/*\\nReset links to optimize for opt-in styling instead of opt-out.\\n*/\\n\\na {\\n  color: inherit;\\n  text-decoration: inherit;\\n}\\n\\n/*\\nAdd the correct font weight in Edge and Safari.\\n*/\\n\\nb,\\nstrong {\\n  font-weight: bolder;\\n}\\n\\n/*\\n1. Use the user's configured `mono` font-family by default.\\n2. Use the user's configured `mono` font-feature-settings by default.\\n3. Use the user's configured `mono` font-variation-settings by default.\\n4. Correct the odd `em` font sizing in all browsers.\\n*/\\n\\ncode,\\nkbd,\\nsamp,\\npre {\\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace; /* 1 */\\n  font-feature-settings: normal; /* 2 */\\n  font-variation-settings: normal; /* 3 */\\n  font-size: 1em; /* 4 */\\n}\\n\\n/*\\nAdd the correct font size in all browsers.\\n*/\\n\\nsmall {\\n  font-size: 80%;\\n}\\n\\n/*\\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\n\\nsub,\\nsup {\\n  font-size: 75%;\\n  line-height: 0;\\n  position: relative;\\n  vertical-align: baseline;\\n}\\n\\nsub {\\n  bottom: -0.25em;\\n}\\n\\nsup {\\n  top: -0.5em;\\n}\\n\\n/*\\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n3. Remove gaps between table borders by default.\\n*/\\n\\ntable {\\n  text-indent: 0; /* 1 */\\n  border-color: inherit; /* 2 */\\n  border-collapse: collapse; /* 3 */\\n}\\n\\n/*\\n1. Change the font styles in all browsers.\\n2. Remove the margin in Firefox and Safari.\\n3. Remove default padding in all browsers.\\n*/\\n\\nbutton,\\ninput,\\noptgroup,\\nselect,\\ntextarea {\\n  font-family: inherit; /* 1 */\\n  font-feature-settings: inherit; /* 1 */\\n  font-variation-settings: inherit; /* 1 */\\n  font-size: 100%; /* 1 */\\n  font-weight: inherit; /* 1 */\\n  line-height: inherit; /* 1 */\\n  letter-spacing: inherit; /* 1 */\\n  color: inherit; /* 1 */\\n  margin: 0; /* 2 */\\n  padding: 0; /* 3 */\\n}\\n\\n/*\\nRemove the inheritance of text transform in Edge and Firefox.\\n*/\\n\\nbutton,\\nselect {\\n  text-transform: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Remove default button styles.\\n*/\\n\\nbutton,\\ninput:where([type='button']),\\ninput:where([type='reset']),\\ninput:where([type='submit']) {\\n  -webkit-appearance: button; /* 1 */\\n  background-color: transparent; /* 2 */\\n  background-image: none; /* 2 */\\n}\\n\\n/*\\nUse the modern Firefox focus style for all focusable elements.\\n*/\\n\\n:-moz-focusring {\\n  outline: auto;\\n}\\n\\n/*\\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\n\\n:-moz-ui-invalid {\\n  box-shadow: none;\\n}\\n\\n/*\\nAdd the correct vertical alignment in Chrome and Firefox.\\n*/\\n\\nprogress {\\n  vertical-align: baseline;\\n}\\n\\n/*\\nCorrect the cursor style of increment and decrement buttons in Safari.\\n*/\\n\\n::-webkit-inner-spin-button,\\n::-webkit-outer-spin-button {\\n  height: auto;\\n}\\n\\n/*\\n1. Correct the odd appearance in Chrome and Safari.\\n2. Correct the outline style in Safari.\\n*/\\n\\n[type='search'] {\\n  -webkit-appearance: textfield; /* 1 */\\n  outline-offset: -2px; /* 2 */\\n}\\n\\n/*\\nRemove the inner padding in Chrome and Safari on macOS.\\n*/\\n\\n::-webkit-search-decoration {\\n  -webkit-appearance: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Change font properties to `inherit` in Safari.\\n*/\\n\\n::-webkit-file-upload-button {\\n  -webkit-appearance: button; /* 1 */\\n  font: inherit; /* 2 */\\n}\\n\\n/*\\nAdd the correct display in Chrome and Safari.\\n*/\\n\\nsummary {\\n  display: list-item;\\n}\\n\\n/*\\nRemoves the default spacing and border for appropriate elements.\\n*/\\n\\nblockquote,\\ndl,\\ndd,\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6,\\nhr,\\nfigure,\\np,\\npre {\\n  margin: 0;\\n}\\n\\nfieldset {\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nlegend {\\n  padding: 0;\\n}\\n\\nol,\\nul,\\nmenu {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n/*\\nReset default styling for dialogs.\\n*/\\n\\ndialog {\\n  padding: 0;\\n}\\n\\n/*\\nPrevent resizing textareas horizontally by default.\\n*/\\n\\ntextarea {\\n  resize: vertical;\\n}\\n\\n/*\\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n2. Set the default placeholder color to the user's configured gray 400 color.\\n*/\\n\\ninput::-moz-placeholder, textarea::-moz-placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\ninput::placeholder,\\ntextarea::placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\n/*\\nSet the default cursor for buttons.\\n*/\\n\\nbutton,\\n[role=\\\"button\\\"] {\\n  cursor: pointer;\\n}\\n\\n/*\\nMake sure disabled buttons don't get the pointer cursor.\\n*/\\n\\n:disabled {\\n  cursor: default;\\n}\\n\\n/*\\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n   This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\n\\nimg,\\nsvg,\\nvideo,\\ncanvas,\\naudio,\\niframe,\\nembed,\\nobject {\\n  display: block; /* 1 */\\n  vertical-align: middle; /* 2 */\\n}\\n\\n/*\\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\n\\nimg,\\nvideo {\\n  max-width: 100%;\\n  height: auto;\\n}\\n\\n/* Make elements with the HTML hidden attribute stay hidden by default */\\n\\n[hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n  display: none;\\n}\\n\\nhtml {\\n    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\\n    scroll-behavior: smooth;\\n    -webkit-font-smoothing: antialiased;\\n    -moz-osx-font-smoothing: grayscale;\\n  }\\n\\nbody{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(248 250 252 / var(--tw-bg-opacity, 1));\\n  line-height: 1.625;\\n  --tw-text-opacity: 1;\\n  color: rgb(30 58 138 / var(--tw-text-opacity, 1));\\n    margin: 0;\\n    padding: 0;\\n}\\n\\n/* Consistent focus styles with brand colors */\\n\\n*:focus{\\n  outline-width: 2px;\\n  outline-offset: 2px;\\n  outline-color: #00d4ff;\\n    outline-style: solid;\\n}\\n\\n/* Headings with brand typography */\\n\\nh1, h2, h3, h4, h5, h6{\\n  font-weight: 600;\\n  line-height: 1.25;\\n  --tw-text-opacity: 1;\\n  color: rgb(30 42 74 / var(--tw-text-opacity, 1));\\n}\\n\\nh1{\\n  font-size: 2.25rem;\\n  line-height: 2.5rem;\\n}\\n\\n@media (min-width: 1024px){\\n\\n  h1{\\n    font-size: 3rem;\\n    line-height: 1;\\n  }\\n}\\n\\nh2{\\n  font-size: 1.875rem;\\n  line-height: 2.25rem;\\n}\\n\\n@media (min-width: 1024px){\\n\\n  h2{\\n    font-size: 2.25rem;\\n    line-height: 2.5rem;\\n  }\\n}\\n\\nh3{\\n  font-size: 1.5rem;\\n  line-height: 2rem;\\n}\\n\\n@media (min-width: 1024px){\\n\\n  h3{\\n    font-size: 1.875rem;\\n    line-height: 2.25rem;\\n  }\\n}\\n\\nh4{\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n}\\n\\n@media (min-width: 1024px){\\n\\n  h4{\\n    font-size: 1.5rem;\\n    line-height: 2rem;\\n  }\\n}\\n\\nh5{\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n}\\n\\n@media (min-width: 1024px){\\n\\n  h5{\\n    font-size: 1.25rem;\\n    line-height: 1.75rem;\\n  }\\n}\\n\\nh6{\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n}\\n\\n@media (min-width: 1024px){\\n\\n  h6{\\n    font-size: 1.125rem;\\n    line-height: 1.75rem;\\n  }\\n}\\n\\n/* Links with brand styling */\\n\\na{\\n  --tw-text-opacity: 1;\\n  color: rgb(8 145 178 / var(--tw-text-opacity, 1));\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n}\\n\\na:hover{\\n  --tw-text-opacity: 1;\\n  color: rgb(14 116 144 / var(--tw-text-opacity, 1));\\n}\\n\\n/* Form elements */\\n\\ninput, textarea, select{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(203 213 225 / var(--tw-border-opacity, 1));\\n}\\n\\ninput:focus, textarea:focus, select:focus{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(0 212 255 / var(--tw-border-opacity, 1));\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(0 212 255 / var(--tw-ring-opacity, 1));\\n}\\n.container{\\n  width: 100%;\\n}\\n@media (min-width: 640px){\\n\\n  .container{\\n    max-width: 640px;\\n  }\\n}\\n@media (min-width: 768px){\\n\\n  .container{\\n    max-width: 768px;\\n  }\\n}\\n@media (min-width: 1024px){\\n\\n  .container{\\n    max-width: 1024px;\\n  }\\n}\\n@media (min-width: 1280px){\\n\\n  .container{\\n    max-width: 1280px;\\n  }\\n}\\n@media (min-width: 1536px){\\n\\n  .container{\\n    max-width: 1536px;\\n  }\\n}\\n/* Primary Button - Main brand blue */\\n.btn-primary{\\n  border-radius: 0.5rem;\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(8 145 178 / var(--tw-bg-opacity, 1));\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n  font-weight: 500;\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n  --tw-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);\\n  --tw-shadow-colored: 0 2px 15px -3px var(--tw-shadow-color), 0 10px 20px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n  transition-property: all;\\n  transition-duration: 200ms;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.btn-primary:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(14 116 144 / var(--tw-bg-opacity, 1));\\n  --tw-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\\n  --tw-shadow-colored: 0 4px 25px -5px var(--tw-shadow-color), 0 10px 10px -5px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.btn-primary:focus{\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(0 212 255 / var(--tw-ring-opacity, 1));\\n  --tw-ring-offset-width: 2px;\\n}\\n.btn-primary:active{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(21 94 117 / var(--tw-bg-opacity, 1));\\n}\\n/* Secondary Button - Professional navy */\\n.btn-secondary{\\n  border-radius: 0.5rem;\\n  border-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(203 213 225 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n  font-weight: 500;\\n  --tw-text-opacity: 1;\\n  color: rgb(15 22 41 / var(--tw-text-opacity, 1));\\n  --tw-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);\\n  --tw-shadow-colored: 0 2px 15px -3px var(--tw-shadow-color), 0 10px 20px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n  transition-property: all;\\n  transition-duration: 200ms;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.btn-secondary:hover{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(148 163 184 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(248 250 252 / var(--tw-bg-opacity, 1));\\n  --tw-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\\n  --tw-shadow-colored: 0 4px 25px -5px var(--tw-shadow-color), 0 10px 10px -5px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.btn-secondary:focus{\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(30 58 138 / var(--tw-ring-opacity, 1));\\n  --tw-ring-offset-width: 2px;\\n}\\n.btn-secondary:active{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(241 245 249 / var(--tw-bg-opacity, 1));\\n}\\n/* Accent Button - Orange highlight */\\n/* Outline Button */\\n.btn-outline{\\n  border-radius: 0.5rem;\\n  border-width: 2px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(8 145 178 / var(--tw-border-opacity, 1));\\n  background-color: transparent;\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n  font-weight: 500;\\n  --tw-text-opacity: 1;\\n  color: rgb(8 145 178 / var(--tw-text-opacity, 1));\\n  transition-property: all;\\n  transition-duration: 200ms;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.btn-outline:hover{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(14 116 144 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(236 254 255 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(14 116 144 / var(--tw-text-opacity, 1));\\n}\\n.btn-outline:focus{\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(0 212 255 / var(--tw-ring-opacity, 1));\\n  --tw-ring-offset-width: 2px;\\n}\\n.btn-outline:active{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(207 250 254 / var(--tw-bg-opacity, 1));\\n}\\n/* Card Components */\\n.card{\\n  border-radius: 1rem;\\n  border-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n  --tw-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);\\n  --tw-shadow-colored: 0 2px 15px -3px var(--tw-shadow-color), 0 10px 20px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n  transition-property: all;\\n  transition-duration: 300ms;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.card:hover{\\n  --tw-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\\n  --tw-shadow-colored: 0 4px 25px -5px var(--tw-shadow-color), 0 10px 10px -5px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.card-interactive{\\n  border-radius: 1rem;\\n  border-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n  --tw-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);\\n  --tw-shadow-colored: 0 2px 15px -3px var(--tw-shadow-color), 0 10px 20px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n  transition-property: all;\\n  transition-duration: 300ms;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.card-interactive:hover{\\n  --tw-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\\n  --tw-shadow-colored: 0 4px 25px -5px var(--tw-shadow-color), 0 10px 10px -5px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.card-interactive{\\n  cursor: pointer;\\n}\\n.card-interactive:hover{\\n  --tw-translate-y: -0.25rem;\\n  --tw-scale-x: 1.02;\\n  --tw-scale-y: 1.02;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.card-body{\\n  padding: 1.5rem;\\n}\\n/* Navigation Styles */\\n.nav-link{\\n  border-radius: 0.5rem;\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n  font-weight: 500;\\n  --tw-text-opacity: 1;\\n  color: rgb(2 132 199 / var(--tw-text-opacity, 1));\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n}\\n.nav-link:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(236 254 255 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(8 145 178 / var(--tw-text-opacity, 1));\\n}\\n.nav-link-active{\\n  border-radius: 0.5rem;\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n  font-weight: 500;\\n  --tw-text-opacity: 1;\\n  color: rgb(2 132 199 / var(--tw-text-opacity, 1));\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n}\\n.nav-link-active:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(236 254 255 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(8 145 178 / var(--tw-text-opacity, 1));\\n}\\n.nav-link-active{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(207 250 254 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(14 116 144 / var(--tw-text-opacity, 1));\\n}\\n/* Form Styles */\\n/* Badge Components */\\n/* Status Indicators */\\n/* Loading States */\\n@keyframes spin{\\n\\n  to{\\n    transform: rotate(360deg);\\n  }\\n}\\n.loading-spinner{\\n  animation: spin 1s linear infinite;\\n  border-radius: 9999px;\\n  border-width: 2px;\\n  border-color: rgb(203 213 225 / var(--tw-border-opacity, 1));\\n  --tw-border-opacity: 1;\\n  border-top-color: rgb(8 145 178 / var(--tw-border-opacity, 1));\\n}\\n/* Professional Layout Utilities */\\n.container-app{\\n  margin-left: auto;\\n  margin-right: auto;\\n  max-width: 80rem;\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\n@media (min-width: 640px){\\n\\n  .container-app{\\n    padding-left: 1.5rem;\\n    padding-right: 1.5rem;\\n  }\\n}\\n@media (min-width: 1024px){\\n\\n  .container-app{\\n    padding-left: 2rem;\\n    padding-right: 2rem;\\n  }\\n}\\n.section-padding{\\n  padding-top: 3rem;\\n  padding-bottom: 3rem;\\n}\\n@media (min-width: 1024px){\\n\\n  .section-padding{\\n    padding-top: 4rem;\\n    padding-bottom: 4rem;\\n  }\\n}\\n/* Dashboard Grid */\\n.dashboard-grid{\\n  display: grid;\\n  grid-template-columns: repeat(1, minmax(0, 1fr));\\n  gap: 1.5rem;\\n}\\n@media (min-width: 768px){\\n\\n  .dashboard-grid{\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n}\\n@media (min-width: 1024px){\\n\\n  .dashboard-grid{\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n}\\n/* Responsive Tables */\\n/* Cosmic/Network Theme Animations */\\n.cosmic-glow {\\n    animation: cosmic-pulse 3s ease-in-out infinite;\\n  }\\n.network-node{\\n  border-radius: 9999px;\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 300ms;\\n    animation: float 6s ease-in-out infinite;\\n}\\n.network-node:nth-child(2) {\\n    animation-delay: -2s;\\n  }\\n.network-node:nth-child(3) {\\n    animation-delay: -4s;\\n  }\\n.connection-line {\\n    animation: connection-flow 4s linear infinite;\\n  }\\n/* Floating animation for network elements */\\n@keyframes float {\\n    0%, 100% {\\n      transform: translateY(0px) scale(1);\\n    }\\n    50% {\\n      transform: translateY(-10px) scale(1.05);\\n    }\\n  }\\n/* Cosmic pulse effect */\\n@keyframes cosmic-pulse {\\n    0%, 100% {\\n      opacity: 0.6;\\n      transform: scale(1);\\n    }\\n    50% {\\n      opacity: 1;\\n      transform: scale(1.1);\\n    }\\n  }\\n/* Connection flow animation */\\n@keyframes connection-flow {\\n    0% {\\n      stroke-dasharray: 0 10;\\n    }\\n    50% {\\n      stroke-dasharray: 5 5;\\n    }\\n    100% {\\n      stroke-dasharray: 10 0;\\n    }\\n  }\\n/* Network connection background - subtle and non-distracting */\\n@keyframes network-drift {\\n    0%, 100% {\\n      background-position: 0% 0%, 100% 100%, 50% 50%;\\n    }\\n    33% {\\n      background-position: 30% 20%, 70% 80%, 60% 40%;\\n    }\\n    66% {\\n      background-position: 70% 30%, 30% 70%, 40% 60%;\\n    }\\n  }\\n/* Enhanced gradient text for cosmic theme */\\n.cosmic-gradient{\\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\\n  --tw-gradient-from: #22d3ee var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(34 211 238 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n  --tw-gradient-to: rgb(0 212 255 / 0)  var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), #00d4ff var(--tw-gradient-via-position), var(--tw-gradient-to);\\n  --tw-gradient-to: #0891b2 var(--tw-gradient-to-position);\\n  -webkit-background-clip: text;\\n          background-clip: text;\\n  color: transparent;\\n    background-size: 200% 200%;\\n    animation: gradient-shift 3s ease infinite;\\n}\\n@keyframes gradient-shift {\\n    0%, 100% {\\n      background-position: 0% 50%;\\n    }\\n    50% {\\n      background-position: 100% 50%;\\n    }\\n  }\\n.pointer-events-none{\\n  pointer-events: none;\\n}\\n.fixed{\\n  position: fixed;\\n}\\n.absolute{\\n  position: absolute;\\n}\\n.relative{\\n  position: relative;\\n}\\n.inset-0{\\n  inset: 0px;\\n}\\n.bottom-10{\\n  bottom: 2.5rem;\\n}\\n.bottom-16{\\n  bottom: 4rem;\\n}\\n.bottom-20{\\n  bottom: 5rem;\\n}\\n.left-1\\\\/4{\\n  left: 25%;\\n}\\n.left-10{\\n  left: 2.5rem;\\n}\\n.left-16{\\n  left: 4rem;\\n}\\n.left-20{\\n  left: 5rem;\\n}\\n.right-1\\\\/3{\\n  right: 33.333333%;\\n}\\n.right-10{\\n  right: 2.5rem;\\n}\\n.right-16{\\n  right: 4rem;\\n}\\n.right-20{\\n  right: 5rem;\\n}\\n.top-1\\\\/2{\\n  top: 50%;\\n}\\n.top-1\\\\/3{\\n  top: 33.333333%;\\n}\\n.top-10{\\n  top: 2.5rem;\\n}\\n.top-16{\\n  top: 4rem;\\n}\\n.top-20{\\n  top: 5rem;\\n}\\n.z-10{\\n  z-index: 10;\\n}\\n.z-50{\\n  z-index: 50;\\n}\\n.mx-2{\\n  margin-left: 0.5rem;\\n  margin-right: 0.5rem;\\n}\\n.mx-auto{\\n  margin-left: auto;\\n  margin-right: auto;\\n}\\n.-mt-1{\\n  margin-top: -0.25rem;\\n}\\n.mb-1{\\n  margin-bottom: 0.25rem;\\n}\\n.mb-12{\\n  margin-bottom: 3rem;\\n}\\n.mb-2{\\n  margin-bottom: 0.5rem;\\n}\\n.mb-4{\\n  margin-bottom: 1rem;\\n}\\n.mb-6{\\n  margin-bottom: 1.5rem;\\n}\\n.mb-8{\\n  margin-bottom: 2rem;\\n}\\n.ml-1{\\n  margin-left: 0.25rem;\\n}\\n.ml-3{\\n  margin-left: 0.75rem;\\n}\\n.ml-4{\\n  margin-left: 1rem;\\n}\\n.ml-auto{\\n  margin-left: auto;\\n}\\n.mr-2{\\n  margin-right: 0.5rem;\\n}\\n.mr-3{\\n  margin-right: 0.75rem;\\n}\\n.mt-1{\\n  margin-top: 0.25rem;\\n}\\n.mt-2{\\n  margin-top: 0.5rem;\\n}\\n.mt-4{\\n  margin-top: 1rem;\\n}\\n.line-clamp-3{\\n  overflow: hidden;\\n  display: -webkit-box;\\n  -webkit-box-orient: vertical;\\n  -webkit-line-clamp: 3;\\n}\\n.block{\\n  display: block;\\n}\\n.inline-block{\\n  display: inline-block;\\n}\\n.flex{\\n  display: flex;\\n}\\n.inline-flex{\\n  display: inline-flex;\\n}\\n.grid{\\n  display: grid;\\n}\\n.hidden{\\n  display: none;\\n}\\n.h-1{\\n  height: 0.25rem;\\n}\\n.h-10{\\n  height: 2.5rem;\\n}\\n.h-12{\\n  height: 3rem;\\n}\\n.h-14{\\n  height: 3.5rem;\\n}\\n.h-16{\\n  height: 4rem;\\n}\\n.h-2{\\n  height: 0.5rem;\\n}\\n.h-20{\\n  height: 5rem;\\n}\\n.h-3{\\n  height: 0.75rem;\\n}\\n.h-4{\\n  height: 1rem;\\n}\\n.h-5{\\n  height: 1.25rem;\\n}\\n.h-6{\\n  height: 1.5rem;\\n}\\n.h-64{\\n  height: 16rem;\\n}\\n.h-7{\\n  height: 1.75rem;\\n}\\n.h-8{\\n  height: 2rem;\\n}\\n.h-96{\\n  height: 24rem;\\n}\\n.h-\\\\[500px\\\\]{\\n  height: 500px;\\n}\\n.h-full{\\n  height: 100%;\\n}\\n.min-h-screen{\\n  min-height: 100vh;\\n}\\n.w-1{\\n  width: 0.25rem;\\n}\\n.w-10{\\n  width: 2.5rem;\\n}\\n.w-12{\\n  width: 3rem;\\n}\\n.w-14{\\n  width: 3.5rem;\\n}\\n.w-16{\\n  width: 4rem;\\n}\\n.w-2{\\n  width: 0.5rem;\\n}\\n.w-24{\\n  width: 6rem;\\n}\\n.w-3{\\n  width: 0.75rem;\\n}\\n.w-4{\\n  width: 1rem;\\n}\\n.w-5{\\n  width: 1.25rem;\\n}\\n.w-6{\\n  width: 1.5rem;\\n}\\n.w-7{\\n  width: 1.75rem;\\n}\\n.w-8{\\n  width: 2rem;\\n}\\n.w-full{\\n  width: 100%;\\n}\\n.min-w-64{\\n  min-width: 16rem;\\n}\\n.max-w-2xl{\\n  max-width: 42rem;\\n}\\n.max-w-4xl{\\n  max-width: 56rem;\\n}\\n.flex-1{\\n  flex: 1 1 0%;\\n}\\n.flex-shrink-0{\\n  flex-shrink: 0;\\n}\\n.transform{\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n@keyframes spin{\\n\\n  to{\\n    transform: rotate(360deg);\\n  }\\n}\\n.animate-spin{\\n  animation: spin 1s linear infinite;\\n}\\n.resize{\\n  resize: both;\\n}\\n.grid-cols-1{\\n  grid-template-columns: repeat(1, minmax(0, 1fr));\\n}\\n.grid-cols-2{\\n  grid-template-columns: repeat(2, minmax(0, 1fr));\\n}\\n.flex-col{\\n  flex-direction: column;\\n}\\n.flex-wrap{\\n  flex-wrap: wrap;\\n}\\n.items-start{\\n  align-items: flex-start;\\n}\\n.items-center{\\n  align-items: center;\\n}\\n.justify-end{\\n  justify-content: flex-end;\\n}\\n.justify-center{\\n  justify-content: center;\\n}\\n.justify-between{\\n  justify-content: space-between;\\n}\\n.gap-1{\\n  gap: 0.25rem;\\n}\\n.gap-4{\\n  gap: 1rem;\\n}\\n.gap-6{\\n  gap: 1.5rem;\\n}\\n.gap-8{\\n  gap: 2rem;\\n}\\n.space-x-1 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.25rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-2 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-3 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.75rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-4 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-8 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(2rem * var(--tw-space-x-reverse));\\n  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-y-1 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\\n}\\n.space-y-2 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\\n}\\n.space-y-3 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\\n}\\n.space-y-4 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\\n}\\n.divide-y > :not([hidden]) ~ :not([hidden]){\\n  --tw-divide-y-reverse: 0;\\n  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));\\n  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));\\n}\\n.overflow-hidden{\\n  overflow: hidden;\\n}\\n.overflow-y-auto{\\n  overflow-y: auto;\\n}\\n.rounded{\\n  border-radius: 0.25rem;\\n}\\n.rounded-2xl{\\n  border-radius: 1.5rem;\\n}\\n.rounded-3xl{\\n  border-radius: 1.5rem;\\n}\\n.rounded-full{\\n  border-radius: 9999px;\\n}\\n.rounded-lg{\\n  border-radius: 0.5rem;\\n}\\n.rounded-md{\\n  border-radius: 0.375rem;\\n}\\n.rounded-xl{\\n  border-radius: 1rem;\\n}\\n.rounded-b-lg{\\n  border-bottom-right-radius: 0.5rem;\\n  border-bottom-left-radius: 0.5rem;\\n}\\n.rounded-t-lg{\\n  border-top-left-radius: 0.5rem;\\n  border-top-right-radius: 0.5rem;\\n}\\n.border{\\n  border-width: 1px;\\n}\\n.border-2{\\n  border-width: 2px;\\n}\\n.border-b{\\n  border-bottom-width: 1px;\\n}\\n.border-b-2{\\n  border-bottom-width: 2px;\\n}\\n.border-t{\\n  border-top-width: 1px;\\n}\\n.border-t-2{\\n  border-top-width: 2px;\\n}\\n.border-amber-500{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(245 158 11 / var(--tw-border-opacity, 1));\\n}\\n.border-amber-600{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(217 119 6 / var(--tw-border-opacity, 1));\\n}\\n.border-candid-gray-200{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));\\n}\\n.border-emerald-500{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(16 185 129 / var(--tw-border-opacity, 1));\\n}\\n.border-emerald-600{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(5 150 105 / var(--tw-border-opacity, 1));\\n}\\n.border-gray-100{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));\\n}\\n.border-gray-200{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));\\n}\\n.border-gray-300{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\\n}\\n.border-indigo-500{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(99 102 241 / var(--tw-border-opacity, 1));\\n}\\n.border-red-400{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(248 113 113 / var(--tw-border-opacity, 1));\\n}\\n.border-teal-500{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(20 184 166 / var(--tw-border-opacity, 1));\\n}\\n.bg-accent-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));\\n}\\n.bg-accent-600{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(234 88 12 / var(--tw-bg-opacity, 1));\\n}\\n.bg-amber-100{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 243 199 / var(--tw-bg-opacity, 1));\\n}\\n.bg-amber-50{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 251 235 / var(--tw-bg-opacity, 1));\\n}\\n.bg-amber-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(245 158 11 / var(--tw-bg-opacity, 1));\\n}\\n.bg-amber-600{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(217 119 6 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-100{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-50{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));\\n}\\n.bg-emerald-100{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(209 250 229 / var(--tw-bg-opacity, 1));\\n}\\n.bg-emerald-50{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(236 253 245 / var(--tw-bg-opacity, 1));\\n}\\n.bg-emerald-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(16 185 129 / var(--tw-bg-opacity, 1));\\n}\\n.bg-emerald-600{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(5 150 105 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-100{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-200{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-50{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-100{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-600{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));\\n}\\n.bg-indigo-100{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(224 231 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-indigo-50{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(238 242 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-indigo-600{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(79 70 229 / var(--tw-bg-opacity, 1));\\n}\\n.bg-orange-100{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));\\n}\\n.bg-primary-300{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(103 232 249 / var(--tw-bg-opacity, 1));\\n}\\n.bg-primary-400{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(34 211 238 / var(--tw-bg-opacity, 1));\\n}\\n.bg-primary-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(0 212 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-primary-600{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(8 145 178 / var(--tw-bg-opacity, 1));\\n}\\n.bg-purple-100{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-purple-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));\\n}\\n.bg-red-100{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));\\n}\\n.bg-red-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));\\n}\\n.bg-red-600{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));\\n}\\n.bg-secondary-600{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(30 42 74 / var(--tw-bg-opacity, 1));\\n}\\n.bg-secondary-700{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(15 22 41 / var(--tw-bg-opacity, 1));\\n}\\n.bg-secondary-800{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(10 15 28 / var(--tw-bg-opacity, 1));\\n}\\n.bg-teal-100{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(204 251 241 / var(--tw-bg-opacity, 1));\\n}\\n.bg-teal-600{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(13 148 136 / var(--tw-bg-opacity, 1));\\n}\\n.bg-white{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-yellow-100{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gradient-to-br{\\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\\n}\\n.bg-gradient-to-r{\\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\\n}\\n.from-accent-500{\\n  --tw-gradient-from: #f97316 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(249 115 22 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-candid-navy-600{\\n  --tw-gradient-from: #0284c7 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(2 132 199 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-candid-orange-500{\\n  --tw-gradient-from: #f97316 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(249 115 22 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-orange-50{\\n  --tw-gradient-from: #fff7ed var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(255 247 237 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-primary-500{\\n  --tw-gradient-from: #00d4ff var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(0 212 255 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-secondary-500{\\n  --tw-gradient-from: #1e3a8a var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(30 58 138 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-secondary-900{\\n  --tw-gradient-from: #050812 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(5 8 18 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.via-secondary-800{\\n  --tw-gradient-to: rgb(10 15 28 / 0)  var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), #0a0f1c var(--tw-gradient-via-position), var(--tw-gradient-to);\\n}\\n.to-accent-500{\\n  --tw-gradient-to: #f97316 var(--tw-gradient-to-position);\\n}\\n.to-accent-600{\\n  --tw-gradient-to: #ea580c var(--tw-gradient-to-position);\\n}\\n.to-candid-navy-700{\\n  --tw-gradient-to: #0369a1 var(--tw-gradient-to-position);\\n}\\n.to-candid-orange-600{\\n  --tw-gradient-to: #ea580c var(--tw-gradient-to-position);\\n}\\n.to-orange-100{\\n  --tw-gradient-to: #ffedd5 var(--tw-gradient-to-position);\\n}\\n.to-primary-500{\\n  --tw-gradient-to: #00d4ff var(--tw-gradient-to-position);\\n}\\n.to-primary-600{\\n  --tw-gradient-to: #0891b2 var(--tw-gradient-to-position);\\n}\\n.to-secondary-600{\\n  --tw-gradient-to: #1e2a4a var(--tw-gradient-to-position);\\n}\\n.p-12{\\n  padding: 3rem;\\n}\\n.p-2{\\n  padding: 0.5rem;\\n}\\n.p-3{\\n  padding: 0.75rem;\\n}\\n.p-4{\\n  padding: 1rem;\\n}\\n.p-6{\\n  padding: 1.5rem;\\n}\\n.px-2{\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\n.px-3{\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n}\\n.px-4{\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\n.px-8{\\n  padding-left: 2rem;\\n  padding-right: 2rem;\\n}\\n.py-1{\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n}\\n.py-12{\\n  padding-top: 3rem;\\n  padding-bottom: 3rem;\\n}\\n.py-2{\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n}\\n.py-3{\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n}\\n.py-4{\\n  padding-top: 1rem;\\n  padding-bottom: 1rem;\\n}\\n.py-8{\\n  padding-top: 2rem;\\n  padding-bottom: 2rem;\\n}\\n.pb-20{\\n  padding-bottom: 5rem;\\n}\\n.pb-4{\\n  padding-bottom: 1rem;\\n}\\n.pt-4{\\n  padding-top: 1rem;\\n}\\n.pt-5{\\n  padding-top: 1.25rem;\\n}\\n.text-left{\\n  text-align: left;\\n}\\n.text-center{\\n  text-align: center;\\n}\\n.text-right{\\n  text-align: right;\\n}\\n.align-bottom{\\n  vertical-align: bottom;\\n}\\n.text-2xl{\\n  font-size: 1.5rem;\\n  line-height: 2rem;\\n}\\n.text-3xl{\\n  font-size: 1.875rem;\\n  line-height: 2.25rem;\\n}\\n.text-4xl{\\n  font-size: 2.25rem;\\n  line-height: 2.5rem;\\n}\\n.text-6xl{\\n  font-size: 3.75rem;\\n  line-height: 1;\\n}\\n.text-base{\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n}\\n.text-lg{\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n}\\n.text-sm{\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\n.text-xl{\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n}\\n.text-xs{\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\n.font-bold{\\n  font-weight: 700;\\n}\\n.font-medium{\\n  font-weight: 500;\\n}\\n.font-semibold{\\n  font-weight: 600;\\n}\\n.uppercase{\\n  text-transform: uppercase;\\n}\\n.leading-6{\\n  line-height: 1.5rem;\\n}\\n.leading-relaxed{\\n  line-height: 1.625;\\n}\\n.tracking-wide{\\n  letter-spacing: 0.025em;\\n}\\n.text-accent-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(234 88 12 / var(--tw-text-opacity, 1));\\n}\\n.text-amber-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(217 119 6 / var(--tw-text-opacity, 1));\\n}\\n.text-amber-800{\\n  --tw-text-opacity: 1;\\n  color: rgb(146 64 14 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-800{\\n  --tw-text-opacity: 1;\\n  color: rgb(30 64 175 / var(--tw-text-opacity, 1));\\n}\\n.text-candid-gray-500{\\n  --tw-text-opacity: 1;\\n  color: rgb(100 116 139 / var(--tw-text-opacity, 1));\\n}\\n.text-candid-gray-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(71 85 105 / var(--tw-text-opacity, 1));\\n}\\n.text-candid-navy-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(2 132 199 / var(--tw-text-opacity, 1));\\n}\\n.text-candid-navy-900{\\n  --tw-text-opacity: 1;\\n  color: rgb(30 42 74 / var(--tw-text-opacity, 1));\\n}\\n.text-emerald-500{\\n  --tw-text-opacity: 1;\\n  color: rgb(16 185 129 / var(--tw-text-opacity, 1));\\n}\\n.text-emerald-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(5 150 105 / var(--tw-text-opacity, 1));\\n}\\n.text-emerald-800{\\n  --tw-text-opacity: 1;\\n  color: rgb(6 95 70 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-300{\\n  --tw-text-opacity: 1;\\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-500{\\n  --tw-text-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-700{\\n  --tw-text-opacity: 1;\\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-800{\\n  --tw-text-opacity: 1;\\n  color: rgb(31 41 55 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-900{\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\\n}\\n.text-green-700{\\n  --tw-text-opacity: 1;\\n  color: rgb(21 128 61 / var(--tw-text-opacity, 1));\\n}\\n.text-green-800{\\n  --tw-text-opacity: 1;\\n  color: rgb(22 101 52 / var(--tw-text-opacity, 1));\\n}\\n.text-indigo-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(79 70 229 / var(--tw-text-opacity, 1));\\n}\\n.text-indigo-700{\\n  --tw-text-opacity: 1;\\n  color: rgb(67 56 202 / var(--tw-text-opacity, 1));\\n}\\n.text-indigo-800{\\n  --tw-text-opacity: 1;\\n  color: rgb(55 48 163 / var(--tw-text-opacity, 1));\\n}\\n.text-orange-800{\\n  --tw-text-opacity: 1;\\n  color: rgb(154 52 18 / var(--tw-text-opacity, 1));\\n}\\n.text-primary-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(34 211 238 / var(--tw-text-opacity, 1));\\n}\\n.text-primary-500{\\n  --tw-text-opacity: 1;\\n  color: rgb(0 212 255 / var(--tw-text-opacity, 1));\\n}\\n.text-primary-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(8 145 178 / var(--tw-text-opacity, 1));\\n}\\n.text-purple-800{\\n  --tw-text-opacity: 1;\\n  color: rgb(107 33 168 / var(--tw-text-opacity, 1));\\n}\\n.text-red-500{\\n  --tw-text-opacity: 1;\\n  color: rgb(239 68 68 / var(--tw-text-opacity, 1));\\n}\\n.text-red-700{\\n  --tw-text-opacity: 1;\\n  color: rgb(185 28 28 / var(--tw-text-opacity, 1));\\n}\\n.text-red-800{\\n  --tw-text-opacity: 1;\\n  color: rgb(153 27 27 / var(--tw-text-opacity, 1));\\n}\\n.text-secondary-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(30 42 74 / var(--tw-text-opacity, 1));\\n}\\n.text-secondary-700{\\n  --tw-text-opacity: 1;\\n  color: rgb(15 22 41 / var(--tw-text-opacity, 1));\\n}\\n.text-secondary-800{\\n  --tw-text-opacity: 1;\\n  color: rgb(10 15 28 / var(--tw-text-opacity, 1));\\n}\\n.text-secondary-900{\\n  --tw-text-opacity: 1;\\n  color: rgb(5 8 18 / var(--tw-text-opacity, 1));\\n}\\n.text-teal-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(13 148 136 / var(--tw-text-opacity, 1));\\n}\\n.text-teal-800{\\n  --tw-text-opacity: 1;\\n  color: rgb(17 94 89 / var(--tw-text-opacity, 1));\\n}\\n.text-white{\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-800{\\n  --tw-text-opacity: 1;\\n  color: rgb(133 77 14 / var(--tw-text-opacity, 1));\\n}\\n.underline{\\n  text-decoration-line: underline;\\n}\\n.opacity-30{\\n  opacity: 0.3;\\n}\\n.opacity-60{\\n  opacity: 0.6;\\n}\\n.opacity-70{\\n  opacity: 0.7;\\n}\\n.opacity-75{\\n  opacity: 0.75;\\n}\\n.opacity-80{\\n  opacity: 0.8;\\n}\\n.shadow{\\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-lg{\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-md{\\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-sm{\\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-soft{\\n  --tw-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);\\n  --tw-shadow-colored: 0 2px 15px -3px var(--tw-shadow-color), 0 10px 20px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-xl{\\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.filter{\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.transition-all{\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-colors{\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-opacity{\\n  transition-property: opacity;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-transform{\\n  transition-property: transform;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.duration-200{\\n  transition-duration: 200ms;\\n}\\n.duration-300{\\n  transition-duration: 300ms;\\n}\\n\\n/* Candid Connections Global Styles */\\n\\n/* Candid Connections Component Library */\\n\\n.hover\\\\:scale-105:hover{\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n\\n.hover\\\\:border-gray-300:hover{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\\n}\\n\\n.hover\\\\:bg-amber-50:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 251 235 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-amber-700:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(180 83 9 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-emerald-50:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(236 253 245 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-emerald-700:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(4 120 87 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-gray-100:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-gray-200:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-green-700:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-indigo-700:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(67 56 202 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-primary-50:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(236 254 255 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-red-700:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-secondary-900:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(5 8 18 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-teal-700:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(15 118 110 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:text-primary-600:hover{\\n  --tw-text-opacity: 1;\\n  color: rgb(8 145 178 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:text-teal-700:hover{\\n  --tw-text-opacity: 1;\\n  color: rgb(15 118 110 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:shadow-xl:hover{\\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n\\n.group:hover .group-hover\\\\:translate-x-1{\\n  --tw-translate-x: 0.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n\\n.group:hover .group-hover\\\\:scale-105{\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n\\n.group:hover .group-hover\\\\:text-primary-500{\\n  --tw-text-opacity: 1;\\n  color: rgb(0 212 255 / var(--tw-text-opacity, 1));\\n}\\n\\n.group:hover .group-hover\\\\:text-primary-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(8 145 178 / var(--tw-text-opacity, 1));\\n}\\n\\n.group:hover .group-hover\\\\:text-primary-700{\\n  --tw-text-opacity: 1;\\n  color: rgb(14 116 144 / var(--tw-text-opacity, 1));\\n}\\n\\n.group:hover .group-hover\\\\:shadow-medium{\\n  --tw-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\\n  --tw-shadow-colored: 0 4px 25px -5px var(--tw-shadow-color), 0 10px 10px -5px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n\\n@media (min-width: 640px){\\n\\n  .sm\\\\:my-8{\\n    margin-top: 2rem;\\n    margin-bottom: 2rem;\\n  }\\n\\n  .sm\\\\:block{\\n    display: block;\\n  }\\n\\n  .sm\\\\:flex{\\n    display: flex;\\n  }\\n\\n  .sm\\\\:w-full{\\n    width: 100%;\\n  }\\n\\n  .sm\\\\:max-w-5xl{\\n    max-width: 64rem;\\n  }\\n\\n  .sm\\\\:flex-row-reverse{\\n    flex-direction: row-reverse;\\n  }\\n\\n  .sm\\\\:items-start{\\n    align-items: flex-start;\\n  }\\n\\n  .sm\\\\:p-0{\\n    padding: 0px;\\n  }\\n\\n  .sm\\\\:p-6{\\n    padding: 1.5rem;\\n  }\\n\\n  .sm\\\\:px-6{\\n    padding-left: 1.5rem;\\n    padding-right: 1.5rem;\\n  }\\n\\n  .sm\\\\:pb-4{\\n    padding-bottom: 1rem;\\n  }\\n\\n  .sm\\\\:align-middle{\\n    vertical-align: middle;\\n  }\\n}\\n\\n@media (min-width: 768px){\\n\\n  .md\\\\:flex{\\n    display: flex;\\n  }\\n\\n  .md\\\\:grid-cols-2{\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:grid-cols-3{\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:grid-cols-4{\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\n}\\n\\n@media (min-width: 1024px){\\n\\n  .lg\\\\:col-span-1{\\n    grid-column: span 1 / span 1;\\n  }\\n\\n  .lg\\\\:col-span-2{\\n    grid-column: span 2 / span 2;\\n  }\\n\\n  .lg\\\\:block{\\n    display: block;\\n  }\\n\\n  .lg\\\\:hidden{\\n    display: none;\\n  }\\n\\n  .lg\\\\:grid-cols-2{\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-3{\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-4{\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-5{\\n    grid-template-columns: repeat(5, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:text-6xl{\\n    font-size: 3.75rem;\\n    line-height: 1;\\n  }\\n}\\n\\n@media (min-width: 1280px){\\n\\n  .xl\\\\:grid-cols-3{\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n}\", \"\",{\"version\":3,\"sources\":[\"webpack://styles/globals.css\"],\"names\":[],\"mappings\":\"AAAA,uGAAuG;;AAEvG;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;;AAAd;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;;AAAd;;CAAc;;AAAd;;;CAAc;;AAAd;;;EAAA,sBAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,mBAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,gBAAc;AAAA;;AAAd;;;;;;;;CAAc;;AAAd;;EAAA,gBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gBAAc,EAAd,MAAc;EAAd,cAAc;KAAd,WAAc,EAAd,MAAc;EAAd,8FAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,wCAAc,EAAd,MAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,yCAAc;UAAd,iCAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;EAAA,kBAAc;EAAd,oBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;EAAd,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,mBAAc;AAAA;;AAAd;;;;;CAAc;;AAAd;;;;EAAA,+GAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,cAAc;EAAd,cAAc;EAAd,kBAAc;EAAd,wBAAc;AAAA;;AAAd;EAAA,eAAc;AAAA;;AAAd;EAAA,WAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;EAAd,yBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;EAAA,oBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gCAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,uBAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,SAAc,EAAd,MAAc;EAAd,UAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,oBAAc;AAAA;;AAAd;;;CAAc;;AAAd;;;;EAAA,0BAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,aAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,YAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,6BAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,0BAAc,EAAd,MAAc;EAAd,aAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,kBAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;;;;;;;;EAAA,SAAc;AAAA;;AAAd;EAAA,SAAc;EAAd,UAAc;AAAA;;AAAd;EAAA,UAAc;AAAA;;AAAd;;;EAAA,gBAAc;EAAd,SAAc;EAAd,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,eAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;;;;EAAA,cAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;EAAd,YAAc;AAAA;;AAAd,wEAAc;;AAAd;EAAA,aAAc;AAAA;;AAAd;IAAA,kGAAc;IAAd,uBAAc;IAAd,mCAAc;IAAd,kCAAc;EAAA;;AAAd;EAAA,kBAAc;EAAd,4DAAc;EAAd,kBAAc;EAAd,oBAAc;EAAd,iDAAc;IAAd,SAAc;IAAd;AAAc;;AAAd,8CAAc;;AAAd;EAAA,kBAAc;EAAd,mBAAc;EAAd,sBAAc;IAAd;AAAc;;AAAd,mCAAc;;AAAd;EAAA,gBAAc;EAAd,iBAAc;EAAd,oBAAc;EAAd;AAAc;;AAAd;EAAA,kBAAc;EAAd;AAAc;;AAAd;;EAAA;IAAA,eAAc;IAAd;EAAc;AAAA;;AAAd;EAAA,mBAAc;EAAd;AAAc;;AAAd;;EAAA;IAAA,kBAAc;IAAd;EAAc;AAAA;;AAAd;EAAA,iBAAc;EAAd;AAAc;;AAAd;;EAAA;IAAA,mBAAc;IAAd;EAAc;AAAA;;AAAd;EAAA,kBAAc;EAAd;AAAc;;AAAd;;EAAA;IAAA,iBAAc;IAAd;EAAc;AAAA;;AAAd;EAAA,mBAAc;EAAd;AAAc;;AAAd;;EAAA;IAAA,kBAAc;IAAd;EAAc;AAAA;;AAAd;EAAA,eAAc;EAAd;AAAc;;AAAd;;EAAA;IAAA,mBAAc;IAAd;EAAc;AAAA;;AAAd,6BAAc;;AAAd;EAAA,oBAAc;EAAd,iDAAc;EAAd,+FAAc;EAAd,wDAAc;EAAd;AAAc;;AAAd;EAAA,oBAAc;EAAd;AAAc;;AAAd,kBAAc;;AAAd;EAAA,sBAAc;EAAd;AAAc;;AAAd;EAAA,sBAAc;EAAd,0DAAc;EAAd,oBAAc;EAAd;AAAc;AACd;EAAA;AAAoB;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAiDlB,qCAAqC;AAEnC;EAAA,qBAIiF;EAJjF,kBAIiF;EAJjF,0DAIiF;EAJjF,oBAIiF;EAJjF,uBAIiF;EAJjF,oBAIiF;EAJjF,qBAIiF;EAJjF,gBAIiF;EAJjF,oBAIiF;EAJjF,mDAIiF;EAJjF,sFAIiF;EAJjF,oGAIiF;EAJjF,uGAIiF;EAJjF,wBAIiF;EAJjF,0BAIiF;EAJjF;AAIiF;AAJjF;EAAA,kBAIiF;EAJjF,2DAIiF;EAJjF,qFAIiF;EAJjF,oGAIiF;EAJjF;AAIiF;AAJjF;EAAA,8BAIiF;EAJjF,mBAIiF;EAJjF,2GAIiF;EAJjF,yGAIiF;EAJjF,4FAIiF;EAJjF,oBAIiF;EAJjF,2DAIiF;EAJjF;AAIiF;AAJjF;EAAA,kBAIiF;EAJjF;AAIiF;AAGnF,yCAAyC;AAEvC;EAAA,qBAKmF;EALnF,iBAKmF;EALnF,sBAKmF;EALnF,4DAKmF;EALnF,kBAKmF;EALnF,4DAKmF;EALnF,oBAKmF;EALnF,uBAKmF;EALnF,oBAKmF;EALnF,qBAKmF;EALnF,gBAKmF;EALnF,oBAKmF;EALnF,gDAKmF;EALnF,sFAKmF;EALnF,oGAKmF;EALnF,uGAKmF;EALnF,wBAKmF;EALnF,0BAKmF;EALnF;AAKmF;AALnF;EAAA,sBAKmF;EALnF,4DAKmF;EALnF,kBAKmF;EALnF,4DAKmF;EALnF,qFAKmF;EALnF,oGAKmF;EALnF;AAKmF;AALnF;EAAA,8BAKmF;EALnF,mBAKmF;EALnF,2GAKmF;EALnF,yGAKmF;EALnF,4FAKmF;EALnF,oBAKmF;EALnF,2DAKmF;EALnF;AAKmF;AALnF;EAAA,kBAKmF;EALnF;AAKmF;AAGrF,qCAAqC;AASrC,mBAAmB;AAEjB;EAAA,qBAIiF;EAJjF,iBAIiF;EAJjF,sBAIiF;EAJjF,0DAIiF;EAJjF,6BAIiF;EAJjF,oBAIiF;EAJjF,uBAIiF;EAJjF,oBAIiF;EAJjF,qBAIiF;EAJjF,gBAIiF;EAJjF,oBAIiF;EAJjF,iDAIiF;EAJjF,wBAIiF;EAJjF,0BAIiF;EAJjF;AAIiF;AAJjF;EAAA,sBAIiF;EAJjF,2DAIiF;EAJjF,kBAIiF;EAJjF,4DAIiF;EAJjF,oBAIiF;EAJjF;AAIiF;AAJjF;EAAA,8BAIiF;EAJjF,mBAIiF;EAJjF,2GAIiF;EAJjF,yGAIiF;EAJjF,4FAIiF;EAJjF,oBAIiF;EAJjF,2DAIiF;EAJjF;AAIiF;AAJjF;EAAA,kBAIiF;EAJjF;AAIiF;AAGnF,oBAAoB;AAElB;EAAA,mBAEoC;EAFpC,iBAEoC;EAFpC,sBAEoC;EAFpC,4DAEoC;EAFpC,kBAEoC;EAFpC,4DAEoC;EAFpC,sFAEoC;EAFpC,oGAEoC;EAFpC,uGAEoC;EAFpC,wBAEoC;EAFpC,0BAEoC;EAFpC;AAEoC;AAFpC;EAAA,qFAEoC;EAFpC,oGAEoC;EAFpC;AAEoC;AAIpC;EAAA,mBAAkE;EAAlE,iBAAkE;EAAlE,sBAAkE;EAAlE,4DAAkE;EAAlE,kBAAkE;EAAlE,4DAAkE;EAAlE,sFAAkE;EAAlE,oGAAkE;EAAlE,uGAAkE;EAAlE,wBAAkE;EAAlE,0BAAkE;EAAlE;AAAkE;AAAlE;EAAA,qFAAkE;EAAlE,oGAAkE;EAAlE;AAAkE;AAAlE;EAAA;AAAkE;AAAlE;EAAA,0BAAkE;EAAlE,kBAAkE;EAAlE,kBAAkE;EAAlE;AAAkE;AAQlE;EAAA;AAAU;AAOZ,sBAAsB;AAEpB;EAAA,qBAE+C;EAF/C,kBAE+C;EAF/C,mBAE+C;EAF/C,mBAE+C;EAF/C,sBAE+C;EAF/C,gBAE+C;EAF/C,oBAE+C;EAF/C,iDAE+C;EAF/C,+FAE+C;EAF/C,wDAE+C;EAF/C;AAE+C;AAF/C;EAAA,kBAE+C;EAF/C,4DAE+C;EAF/C,oBAE+C;EAF/C;AAE+C;AAI/C;EAAA,qBAA+C;EAA/C,kBAA+C;EAA/C,mBAA+C;EAA/C,mBAA+C;EAA/C,sBAA+C;EAA/C,gBAA+C;EAA/C,oBAA+C;EAA/C,iDAA+C;EAA/C,+FAA+C;EAA/C,wDAA+C;EAA/C;AAA+C;AAA/C;EAAA,kBAA+C;EAA/C,4DAA+C;EAA/C,oBAA+C;EAA/C;AAA+C;AAA/C;EAAA,kBAA+C;EAA/C,4DAA+C;EAA/C,oBAA+C;EAA/C;AAA+C;AAGjD,gBAAgB;AAgBhB,qBAAqB;AA6BrB,sBAAsB;AAiBtB,mBAAmB;AAEjB;;EAAA;IAAA;EAAqF;AAAA;AAArF;EAAA,kCAAqF;EAArF,qBAAqF;EAArF,iBAAqF;EAArF,4DAAqF;EAArF,sBAAqF;EAArF;AAAqF;AAGvF,kCAAkC;AAEhC;EAAA,iBAA6C;EAA7C,kBAA6C;EAA7C,gBAA6C;EAA7C,kBAA6C;EAA7C;AAA6C;AAA7C;;EAAA;IAAA,oBAA6C;IAA7C;EAA6C;AAAA;AAA7C;;EAAA;IAAA,kBAA6C;IAA7C;EAA6C;AAAA;AAI7C;EAAA,iBAAqB;EAArB;AAAqB;AAArB;;EAAA;IAAA,iBAAqB;IAArB;EAAqB;AAAA;AAOvB,mBAAmB;AAEjB;EAAA,aAA2D;EAA3D,gDAA2D;EAA3D;AAA2D;AAA3D;;EAAA;IAAA;EAA2D;AAAA;AAA3D;;EAAA;IAAA;EAA2D;AAAA;AAG7D,sBAAsB;AAqBtB,oCAAoC;AACpC;IACE,+CAA+C;EACjD;AAGE;EAAA,qBAA+C;EAA/C,wBAA+C;EAA/C,wDAA+C;EAA/C,0BAA+C;IAC/C;AAD+C;AAIjD;IACE,oBAAoB;EACtB;AAEA;IACE,oBAAoB;EACtB;AAEA;IACE,6CAA6C;EAC/C;AAEA,4CAA4C;AAC5C;IACE;MACE,mCAAmC;IACrC;IACA;MACE,wCAAwC;IAC1C;EACF;AAEA,wBAAwB;AACxB;IACE;MACE,YAAY;MACZ,mBAAmB;IACrB;IACA;MACE,UAAU;MACV,qBAAqB;IACvB;EACF;AAEA,8BAA8B;AAC9B;IACE;MACE,sBAAsB;IACxB;IACA;MACE,qBAAqB;IACvB;IACA;MACE,sBAAsB;IACxB;EACF;AAEA,+DAA+D;AAU/D;IACE;MACE,8CAA8C;IAChD;IACA;MACE,8CAA8C;IAChD;IACA;MACE,8CAA8C;IAChD;EACF;AAEA,4CAA4C;AAE1C;EAAA,qEAAqG;EAArG,4DAAqG;EAArG,oEAAqG;EAArG,mEAAqG;EAArG,oEAAqG;EAArG,4GAAqG;EAArG,wDAAqG;EAArG,6BAAqG;UAArG,qBAAqG;EAArG,kBAAqG;IACrG,0BAA0B;IAC1B;AAFqG;AAKvG;IACE;MACE,2BAA2B;IAC7B;IACA;MACE,6BAA6B;IAC/B;EACF;AA9TF;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB,oBAAmB;EAAnB,4BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,sDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA,wBAAmB;EAAnB,kEAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kCAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,gEAAmB;EAAnB;AAAmB;AAAnB;EAAA,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,8FAAmB;EAAnB;AAAmB;AAAnB;EAAA,+EAAmB;EAAnB,mGAAmB;EAAnB;AAAmB;AAAnB;EAAA,6EAAmB;EAAnB,iGAAmB;EAAnB;AAAmB;AAAnB;EAAA,0CAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,sFAAmB;EAAnB,oGAAmB;EAAnB;AAAmB;AAAnB;EAAA,gFAAmB;EAAnB,oGAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,wBAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+FAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,4BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;;AAEnB,qCAAqC;;AA4CrC,yCAAyC;;AAlDzC;EAAA,kBAmUC;EAnUD,kBAmUC;EAnUD;AAmUC;;AAnUD;EAAA,sBAmUC;EAnUD;AAmUC;;AAnUD;EAAA,kBAmUC;EAnUD;AAmUC;;AAnUD;EAAA,kBAmUC;EAnUD;AAmUC;;AAnUD;EAAA,kBAmUC;EAnUD;AAmUC;;AAnUD;EAAA,kBAmUC;EAnUD;AAmUC;;AAnUD;EAAA,kBAmUC;EAnUD;AAmUC;;AAnUD;EAAA,kBAmUC;EAnUD;AAmUC;;AAnUD;EAAA,kBAmUC;EAnUD;AAmUC;;AAnUD;EAAA,kBAmUC;EAnUD;AAmUC;;AAnUD;EAAA,kBAmUC;EAnUD;AAmUC;;AAnUD;EAAA,kBAmUC;EAnUD;AAmUC;;AAnUD;EAAA,kBAmUC;EAnUD;AAmUC;;AAnUD;EAAA,kBAmUC;EAnUD;AAmUC;;AAnUD;EAAA,oBAmUC;EAnUD;AAmUC;;AAnUD;EAAA,oBAmUC;EAnUD;AAmUC;;AAnUD;EAAA,gFAmUC;EAnUD,oGAmUC;EAnUD;AAmUC;;AAnUD;EAAA,yBAmUC;EAnUD;AAmUC;;AAnUD;EAAA,kBAmUC;EAnUD,kBAmUC;EAnUD;AAmUC;;AAnUD;EAAA,oBAmUC;EAnUD;AAmUC;;AAnUD;EAAA,oBAmUC;EAnUD;AAmUC;;AAnUD;EAAA,oBAmUC;EAnUD;AAmUC;;AAnUD;EAAA,qFAmUC;EAnUD,oGAmUC;EAnUD;AAmUC;;AAnUD;;EAAA;IAAA,gBAmUC;IAnUD;EAmUC;;EAnUD;IAAA;EAmUC;;EAnUD;IAAA;EAmUC;;EAnUD;IAAA;EAmUC;;EAnUD;IAAA;EAmUC;;EAnUD;IAAA;EAmUC;;EAnUD;IAAA;EAmUC;;EAnUD;IAAA;EAmUC;;EAnUD;IAAA;EAmUC;;EAnUD;IAAA,oBAmUC;IAnUD;EAmUC;;EAnUD;IAAA;EAmUC;;EAnUD;IAAA;EAmUC;AAAA;;AAnUD;;EAAA;IAAA;EAmUC;;EAnUD;IAAA;EAmUC;;EAnUD;IAAA;EAmUC;;EAnUD;IAAA;EAmUC;AAAA;;AAnUD;;EAAA;IAAA;EAmUC;;EAnUD;IAAA;EAmUC;;EAnUD;IAAA;EAmUC;;EAnUD;IAAA;EAmUC;;EAnUD;IAAA;EAmUC;;EAnUD;IAAA;EAmUC;;EAnUD;IAAA;EAmUC;;EAnUD;IAAA;EAmUC;;EAnUD;IAAA,kBAmUC;IAnUD;EAmUC;AAAA;;AAnUD;;EAAA;IAAA;EAmUC;AAAA\",\"sourcesContent\":[\"@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');\\n\\n@tailwind base;\\n@tailwind components;\\n@tailwind utilities;\\n\\n/* Candid Connections Global Styles */\\n@layer base {\\n  html {\\n    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\\n    scroll-behavior: smooth;\\n    -webkit-font-smoothing: antialiased;\\n    -moz-osx-font-smoothing: grayscale;\\n  }\\n\\n  body {\\n    @apply bg-candid-gray-50 text-candid-navy-800 leading-relaxed;\\n    margin: 0;\\n    padding: 0;\\n  }\\n\\n  /* Consistent focus styles with brand colors */\\n  *:focus {\\n    @apply outline-2 outline-primary-500 outline-offset-2;\\n    outline-style: solid;\\n  }\\n\\n  /* Headings with brand typography */\\n  h1, h2, h3, h4, h5, h6 {\\n    @apply text-candid-navy-900 font-semibold leading-tight;\\n  }\\n\\n  h1 { @apply text-4xl lg:text-5xl; }\\n  h2 { @apply text-3xl lg:text-4xl; }\\n  h3 { @apply text-2xl lg:text-3xl; }\\n  h4 { @apply text-xl lg:text-2xl; }\\n  h5 { @apply text-lg lg:text-xl; }\\n  h6 { @apply text-base lg:text-lg; }\\n\\n  /* Links with brand styling */\\n  a {\\n    @apply text-primary-600 hover:text-primary-700 transition-colors duration-200;\\n  }\\n\\n  /* Form elements */\\n  input, textarea, select {\\n    @apply border-candid-gray-300 focus:border-primary-500 focus:ring-primary-500;\\n  }\\n}\\n\\n/* Candid Connections Component Library */\\n@layer components {\\n  /* Primary Button - Main brand blue */\\n  .btn-primary {\\n    @apply bg-primary-600 hover:bg-primary-700 active:bg-primary-800\\n           text-white font-medium py-3 px-6 rounded-lg\\n           transition-all duration-200 ease-in-out\\n           shadow-soft hover:shadow-medium\\n           focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;\\n  }\\n\\n  /* Secondary Button - Professional navy */\\n  .btn-secondary {\\n    @apply bg-white hover:bg-candid-gray-50 active:bg-candid-gray-100\\n           text-secondary-700 font-medium py-3 px-6\\n           border border-candid-gray-300 hover:border-candid-gray-400 rounded-lg\\n           transition-all duration-200 ease-in-out\\n           shadow-soft hover:shadow-medium\\n           focus:outline-none focus:ring-2 focus:ring-secondary-500 focus:ring-offset-2;\\n  }\\n\\n  /* Accent Button - Orange highlight */\\n  .btn-accent {\\n    @apply bg-accent-500 hover:bg-accent-600 active:bg-accent-700\\n           text-white font-medium py-3 px-6 rounded-lg\\n           transition-all duration-200 ease-in-out\\n           shadow-soft hover:shadow-medium\\n           focus:outline-none focus:ring-2 focus:ring-accent-500 focus:ring-offset-2;\\n  }\\n\\n  /* Outline Button */\\n  .btn-outline {\\n    @apply bg-transparent hover:bg-primary-50 active:bg-primary-100\\n           text-primary-600 hover:text-primary-700 font-medium py-3 px-6\\n           border-2 border-primary-600 hover:border-primary-700 rounded-lg\\n           transition-all duration-200 ease-in-out\\n           focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;\\n  }\\n\\n  /* Card Components */\\n  .card {\\n    @apply bg-white rounded-xl shadow-soft hover:shadow-medium\\n           transition-all duration-300 ease-in-out\\n           border border-candid-gray-200;\\n  }\\n\\n  .card-interactive {\\n    @apply card hover:scale-[1.02] hover:-translate-y-1 cursor-pointer;\\n  }\\n\\n  .card-header {\\n    @apply p-6 border-b border-candid-gray-200;\\n  }\\n\\n  .card-body {\\n    @apply p-6;\\n  }\\n\\n  .card-footer {\\n    @apply p-6 border-t border-candid-gray-200 bg-candid-gray-50 rounded-b-xl;\\n  }\\n\\n  /* Navigation Styles */\\n  .nav-link {\\n    @apply text-candid-navy-600 hover:text-primary-600\\n           font-medium transition-colors duration-200\\n           px-4 py-2 rounded-lg hover:bg-primary-50;\\n  }\\n\\n  .nav-link-active {\\n    @apply nav-link text-primary-700 bg-primary-100;\\n  }\\n\\n  /* Form Styles */\\n  .form-input {\\n    @apply w-full px-4 py-3 border border-candid-gray-300 rounded-lg\\n           focus:border-primary-500 focus:ring-1 focus:ring-primary-500\\n           transition-colors duration-200\\n           placeholder-candid-gray-400;\\n  }\\n\\n  .form-label {\\n    @apply block text-sm font-medium text-candid-navy-700 mb-2;\\n  }\\n\\n  .form-error {\\n    @apply text-red-600 text-sm mt-1;\\n  }\\n\\n  /* Badge Components */\\n  .badge {\\n    @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium;\\n  }\\n\\n  .badge-primary {\\n    @apply badge bg-primary-100 text-primary-800;\\n  }\\n\\n  .badge-secondary {\\n    @apply badge bg-secondary-100 text-secondary-800;\\n  }\\n\\n  .badge-accent {\\n    @apply badge bg-accent-100 text-accent-800;\\n  }\\n\\n  .badge-success {\\n    @apply badge bg-green-100 text-green-800;\\n  }\\n\\n  .badge-warning {\\n    @apply badge bg-yellow-100 text-yellow-800;\\n  }\\n\\n  .badge-error {\\n    @apply badge bg-red-100 text-red-800;\\n  }\\n\\n  /* Status Indicators */\\n  .status-active {\\n    @apply badge-success;\\n  }\\n\\n  .status-pending {\\n    @apply badge-warning;\\n  }\\n\\n  .status-inactive {\\n    @apply badge-secondary;\\n  }\\n\\n  .status-error {\\n    @apply badge-error;\\n  }\\n\\n  /* Loading States */\\n  .loading-spinner {\\n    @apply animate-spin rounded-full border-2 border-candid-gray-300 border-t-primary-600;\\n  }\\n\\n  /* Professional Layout Utilities */\\n  .container-app {\\n    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;\\n  }\\n\\n  .section-padding {\\n    @apply py-12 lg:py-16;\\n  }\\n\\n  .text-gradient {\\n    @apply bg-gradient-to-r from-primary-600 to-accent-500 bg-clip-text text-transparent;\\n  }\\n\\n  /* Dashboard Grid */\\n  .dashboard-grid {\\n    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;\\n  }\\n\\n  /* Responsive Tables */\\n  .table-responsive {\\n    @apply overflow-x-auto shadow-soft rounded-lg;\\n  }\\n\\n  .table {\\n    @apply min-w-full divide-y divide-candid-gray-200;\\n  }\\n\\n  .table-header {\\n    @apply bg-candid-gray-50;\\n  }\\n\\n  .table-cell {\\n    @apply px-6 py-4 whitespace-nowrap text-sm;\\n  }\\n\\n  .table-header-cell {\\n    @apply table-cell font-medium text-candid-navy-900 uppercase tracking-wider;\\n  }\\n\\n  /* Cosmic/Network Theme Animations */\\n  .cosmic-glow {\\n    animation: cosmic-pulse 3s ease-in-out infinite;\\n  }\\n\\n  .network-node {\\n    @apply rounded-full transition-all duration-300;\\n    animation: float 6s ease-in-out infinite;\\n  }\\n\\n  .network-node:nth-child(2) {\\n    animation-delay: -2s;\\n  }\\n\\n  .network-node:nth-child(3) {\\n    animation-delay: -4s;\\n  }\\n\\n  .connection-line {\\n    animation: connection-flow 4s linear infinite;\\n  }\\n\\n  /* Floating animation for network elements */\\n  @keyframes float {\\n    0%, 100% {\\n      transform: translateY(0px) scale(1);\\n    }\\n    50% {\\n      transform: translateY(-10px) scale(1.05);\\n    }\\n  }\\n\\n  /* Cosmic pulse effect */\\n  @keyframes cosmic-pulse {\\n    0%, 100% {\\n      opacity: 0.6;\\n      transform: scale(1);\\n    }\\n    50% {\\n      opacity: 1;\\n      transform: scale(1.1);\\n    }\\n  }\\n\\n  /* Connection flow animation */\\n  @keyframes connection-flow {\\n    0% {\\n      stroke-dasharray: 0 10;\\n    }\\n    50% {\\n      stroke-dasharray: 5 5;\\n    }\\n    100% {\\n      stroke-dasharray: 10 0;\\n    }\\n  }\\n\\n  /* Network connection background - subtle and non-distracting */\\n  .network-background {\\n    background:\\n      radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),\\n      radial-gradient(circle at 80% 80%, rgba(0, 212, 255, 0.08) 0%, transparent 50%),\\n      radial-gradient(circle at 40% 60%, rgba(255, 255, 255, 0.05) 0%, transparent 30%);\\n    background-size: 800px 800px, 600px 600px, 400px 400px;\\n    animation: network-drift 30s ease-in-out infinite;\\n  }\\n\\n  @keyframes network-drift {\\n    0%, 100% {\\n      background-position: 0% 0%, 100% 100%, 50% 50%;\\n    }\\n    33% {\\n      background-position: 30% 20%, 70% 80%, 60% 40%;\\n    }\\n    66% {\\n      background-position: 70% 30%, 30% 70%, 40% 60%;\\n    }\\n  }\\n\\n  /* Enhanced gradient text for cosmic theme */\\n  .cosmic-gradient {\\n    @apply bg-gradient-to-r from-primary-400 via-primary-500 to-primary-600 bg-clip-text text-transparent;\\n    background-size: 200% 200%;\\n    animation: gradient-shift 3s ease infinite;\\n  }\\n\\n  @keyframes gradient-shift {\\n    0%, 100% {\\n      background-position: 0% 50%;\\n    }\\n    50% {\\n      background-position: 100% 50%;\\n    }\\n  }\\n}\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./styles/globals.css\n"));

/***/ })

});