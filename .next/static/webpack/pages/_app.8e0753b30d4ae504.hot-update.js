"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./styles/globals.css":
/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./styles/globals.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');\\n\\n*, ::before, ::after{\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\n\\n::backdrop{\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\n\\n/*\\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\\n*/\\n\\n/*\\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\\n*/\\n\\n*,\\n::before,\\n::after {\\n  box-sizing: border-box; /* 1 */\\n  border-width: 0; /* 2 */\\n  border-style: solid; /* 2 */\\n  border-color: #e5e7eb; /* 2 */\\n}\\n\\n::before,\\n::after {\\n  --tw-content: '';\\n}\\n\\n/*\\n1. Use a consistent sensible line-height in all browsers.\\n2. Prevent adjustments of font size after orientation changes in iOS.\\n3. Use a more readable tab size.\\n4. Use the user's configured `sans` font-family by default.\\n5. Use the user's configured `sans` font-feature-settings by default.\\n6. Use the user's configured `sans` font-variation-settings by default.\\n7. Disable tap highlights on iOS\\n*/\\n\\nhtml,\\n:host {\\n  line-height: 1.5; /* 1 */\\n  -webkit-text-size-adjust: 100%; /* 2 */\\n  -moz-tab-size: 4; /* 3 */\\n  -o-tab-size: 4;\\n     tab-size: 4; /* 3 */\\n  font-family: Inter, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif; /* 4 */\\n  font-feature-settings: normal; /* 5 */\\n  font-variation-settings: normal; /* 6 */\\n  -webkit-tap-highlight-color: transparent; /* 7 */\\n}\\n\\n/*\\n1. Remove the margin in all browsers.\\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\\n*/\\n\\nbody {\\n  margin: 0; /* 1 */\\n  line-height: inherit; /* 2 */\\n}\\n\\n/*\\n1. Add the correct height in Firefox.\\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n3. Ensure horizontal rules are visible by default.\\n*/\\n\\nhr {\\n  height: 0; /* 1 */\\n  color: inherit; /* 2 */\\n  border-top-width: 1px; /* 3 */\\n}\\n\\n/*\\nAdd the correct text decoration in Chrome, Edge, and Safari.\\n*/\\n\\nabbr:where([title]) {\\n  -webkit-text-decoration: underline dotted;\\n          text-decoration: underline dotted;\\n}\\n\\n/*\\nRemove the default font size and weight for headings.\\n*/\\n\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n  font-size: inherit;\\n  font-weight: inherit;\\n}\\n\\n/*\\nReset links to optimize for opt-in styling instead of opt-out.\\n*/\\n\\na {\\n  color: inherit;\\n  text-decoration: inherit;\\n}\\n\\n/*\\nAdd the correct font weight in Edge and Safari.\\n*/\\n\\nb,\\nstrong {\\n  font-weight: bolder;\\n}\\n\\n/*\\n1. Use the user's configured `mono` font-family by default.\\n2. Use the user's configured `mono` font-feature-settings by default.\\n3. Use the user's configured `mono` font-variation-settings by default.\\n4. Correct the odd `em` font sizing in all browsers.\\n*/\\n\\ncode,\\nkbd,\\nsamp,\\npre {\\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace; /* 1 */\\n  font-feature-settings: normal; /* 2 */\\n  font-variation-settings: normal; /* 3 */\\n  font-size: 1em; /* 4 */\\n}\\n\\n/*\\nAdd the correct font size in all browsers.\\n*/\\n\\nsmall {\\n  font-size: 80%;\\n}\\n\\n/*\\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\n\\nsub,\\nsup {\\n  font-size: 75%;\\n  line-height: 0;\\n  position: relative;\\n  vertical-align: baseline;\\n}\\n\\nsub {\\n  bottom: -0.25em;\\n}\\n\\nsup {\\n  top: -0.5em;\\n}\\n\\n/*\\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n3. Remove gaps between table borders by default.\\n*/\\n\\ntable {\\n  text-indent: 0; /* 1 */\\n  border-color: inherit; /* 2 */\\n  border-collapse: collapse; /* 3 */\\n}\\n\\n/*\\n1. Change the font styles in all browsers.\\n2. Remove the margin in Firefox and Safari.\\n3. Remove default padding in all browsers.\\n*/\\n\\nbutton,\\ninput,\\noptgroup,\\nselect,\\ntextarea {\\n  font-family: inherit; /* 1 */\\n  font-feature-settings: inherit; /* 1 */\\n  font-variation-settings: inherit; /* 1 */\\n  font-size: 100%; /* 1 */\\n  font-weight: inherit; /* 1 */\\n  line-height: inherit; /* 1 */\\n  letter-spacing: inherit; /* 1 */\\n  color: inherit; /* 1 */\\n  margin: 0; /* 2 */\\n  padding: 0; /* 3 */\\n}\\n\\n/*\\nRemove the inheritance of text transform in Edge and Firefox.\\n*/\\n\\nbutton,\\nselect {\\n  text-transform: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Remove default button styles.\\n*/\\n\\nbutton,\\ninput:where([type='button']),\\ninput:where([type='reset']),\\ninput:where([type='submit']) {\\n  -webkit-appearance: button; /* 1 */\\n  background-color: transparent; /* 2 */\\n  background-image: none; /* 2 */\\n}\\n\\n/*\\nUse the modern Firefox focus style for all focusable elements.\\n*/\\n\\n:-moz-focusring {\\n  outline: auto;\\n}\\n\\n/*\\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\n\\n:-moz-ui-invalid {\\n  box-shadow: none;\\n}\\n\\n/*\\nAdd the correct vertical alignment in Chrome and Firefox.\\n*/\\n\\nprogress {\\n  vertical-align: baseline;\\n}\\n\\n/*\\nCorrect the cursor style of increment and decrement buttons in Safari.\\n*/\\n\\n::-webkit-inner-spin-button,\\n::-webkit-outer-spin-button {\\n  height: auto;\\n}\\n\\n/*\\n1. Correct the odd appearance in Chrome and Safari.\\n2. Correct the outline style in Safari.\\n*/\\n\\n[type='search'] {\\n  -webkit-appearance: textfield; /* 1 */\\n  outline-offset: -2px; /* 2 */\\n}\\n\\n/*\\nRemove the inner padding in Chrome and Safari on macOS.\\n*/\\n\\n::-webkit-search-decoration {\\n  -webkit-appearance: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Change font properties to `inherit` in Safari.\\n*/\\n\\n::-webkit-file-upload-button {\\n  -webkit-appearance: button; /* 1 */\\n  font: inherit; /* 2 */\\n}\\n\\n/*\\nAdd the correct display in Chrome and Safari.\\n*/\\n\\nsummary {\\n  display: list-item;\\n}\\n\\n/*\\nRemoves the default spacing and border for appropriate elements.\\n*/\\n\\nblockquote,\\ndl,\\ndd,\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6,\\nhr,\\nfigure,\\np,\\npre {\\n  margin: 0;\\n}\\n\\nfieldset {\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nlegend {\\n  padding: 0;\\n}\\n\\nol,\\nul,\\nmenu {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n/*\\nReset default styling for dialogs.\\n*/\\n\\ndialog {\\n  padding: 0;\\n}\\n\\n/*\\nPrevent resizing textareas horizontally by default.\\n*/\\n\\ntextarea {\\n  resize: vertical;\\n}\\n\\n/*\\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n2. Set the default placeholder color to the user's configured gray 400 color.\\n*/\\n\\ninput::-moz-placeholder, textarea::-moz-placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\ninput::placeholder,\\ntextarea::placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\n/*\\nSet the default cursor for buttons.\\n*/\\n\\nbutton,\\n[role=\\\"button\\\"] {\\n  cursor: pointer;\\n}\\n\\n/*\\nMake sure disabled buttons don't get the pointer cursor.\\n*/\\n\\n:disabled {\\n  cursor: default;\\n}\\n\\n/*\\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n   This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\n\\nimg,\\nsvg,\\nvideo,\\ncanvas,\\naudio,\\niframe,\\nembed,\\nobject {\\n  display: block; /* 1 */\\n  vertical-align: middle; /* 2 */\\n}\\n\\n/*\\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\n\\nimg,\\nvideo {\\n  max-width: 100%;\\n  height: auto;\\n}\\n\\n/* Make elements with the HTML hidden attribute stay hidden by default */\\n\\n[hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n  display: none;\\n}\\n\\nhtml {\\n    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\\n    scroll-behavior: smooth;\\n    -webkit-font-smoothing: antialiased;\\n    -moz-osx-font-smoothing: grayscale;\\n  }\\n\\nbody{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(248 250 252 / var(--tw-bg-opacity, 1));\\n  line-height: 1.625;\\n  --tw-text-opacity: 1;\\n  color: rgb(30 58 138 / var(--tw-text-opacity, 1));\\n    margin: 0;\\n    padding: 0;\\n}\\n\\n/* Consistent focus styles with brand colors */\\n\\n*:focus{\\n  outline-width: 2px;\\n  outline-offset: 2px;\\n  outline-color: #00d4ff;\\n    outline-style: solid;\\n}\\n\\n/* Headings with brand typography */\\n\\nh1, h2, h3, h4, h5, h6{\\n  font-weight: 600;\\n  line-height: 1.25;\\n  --tw-text-opacity: 1;\\n  color: rgb(30 42 74 / var(--tw-text-opacity, 1));\\n}\\n\\nh1{\\n  font-size: 2.25rem;\\n  line-height: 2.5rem;\\n}\\n\\n@media (min-width: 1024px){\\n\\n  h1{\\n    font-size: 3rem;\\n    line-height: 1;\\n  }\\n}\\n\\nh2{\\n  font-size: 1.875rem;\\n  line-height: 2.25rem;\\n}\\n\\n@media (min-width: 1024px){\\n\\n  h2{\\n    font-size: 2.25rem;\\n    line-height: 2.5rem;\\n  }\\n}\\n\\nh3{\\n  font-size: 1.5rem;\\n  line-height: 2rem;\\n}\\n\\n@media (min-width: 1024px){\\n\\n  h3{\\n    font-size: 1.875rem;\\n    line-height: 2.25rem;\\n  }\\n}\\n\\nh4{\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n}\\n\\n@media (min-width: 1024px){\\n\\n  h4{\\n    font-size: 1.5rem;\\n    line-height: 2rem;\\n  }\\n}\\n\\nh5{\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n}\\n\\n@media (min-width: 1024px){\\n\\n  h5{\\n    font-size: 1.25rem;\\n    line-height: 1.75rem;\\n  }\\n}\\n\\nh6{\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n}\\n\\n@media (min-width: 1024px){\\n\\n  h6{\\n    font-size: 1.125rem;\\n    line-height: 1.75rem;\\n  }\\n}\\n\\n/* Links with brand styling */\\n\\na{\\n  --tw-text-opacity: 1;\\n  color: rgb(8 145 178 / var(--tw-text-opacity, 1));\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n}\\n\\na:hover{\\n  --tw-text-opacity: 1;\\n  color: rgb(14 116 144 / var(--tw-text-opacity, 1));\\n}\\n\\n/* Form elements */\\n\\ninput, textarea, select{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(203 213 225 / var(--tw-border-opacity, 1));\\n}\\n\\ninput:focus, textarea:focus, select:focus{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(0 212 255 / var(--tw-border-opacity, 1));\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(0 212 255 / var(--tw-ring-opacity, 1));\\n}\\n.container{\\n  width: 100%;\\n}\\n@media (min-width: 640px){\\n\\n  .container{\\n    max-width: 640px;\\n  }\\n}\\n@media (min-width: 768px){\\n\\n  .container{\\n    max-width: 768px;\\n  }\\n}\\n@media (min-width: 1024px){\\n\\n  .container{\\n    max-width: 1024px;\\n  }\\n}\\n@media (min-width: 1280px){\\n\\n  .container{\\n    max-width: 1280px;\\n  }\\n}\\n@media (min-width: 1536px){\\n\\n  .container{\\n    max-width: 1536px;\\n  }\\n}\\n/* Primary Button - Main brand blue */\\n.btn-primary{\\n  border-radius: 0.5rem;\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(8 145 178 / var(--tw-bg-opacity, 1));\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n  font-weight: 500;\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n  --tw-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);\\n  --tw-shadow-colored: 0 2px 15px -3px var(--tw-shadow-color), 0 10px 20px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n  transition-property: all;\\n  transition-duration: 200ms;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.btn-primary:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(14 116 144 / var(--tw-bg-opacity, 1));\\n  --tw-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\\n  --tw-shadow-colored: 0 4px 25px -5px var(--tw-shadow-color), 0 10px 10px -5px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.btn-primary:focus{\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(0 212 255 / var(--tw-ring-opacity, 1));\\n  --tw-ring-offset-width: 2px;\\n}\\n.btn-primary:active{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(21 94 117 / var(--tw-bg-opacity, 1));\\n}\\n/* Secondary Button - Professional navy */\\n.btn-secondary{\\n  border-radius: 0.5rem;\\n  border-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(203 213 225 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n  font-weight: 500;\\n  --tw-text-opacity: 1;\\n  color: rgb(15 22 41 / var(--tw-text-opacity, 1));\\n  --tw-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);\\n  --tw-shadow-colored: 0 2px 15px -3px var(--tw-shadow-color), 0 10px 20px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n  transition-property: all;\\n  transition-duration: 200ms;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.btn-secondary:hover{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(148 163 184 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(248 250 252 / var(--tw-bg-opacity, 1));\\n  --tw-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\\n  --tw-shadow-colored: 0 4px 25px -5px var(--tw-shadow-color), 0 10px 10px -5px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.btn-secondary:focus{\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(30 58 138 / var(--tw-ring-opacity, 1));\\n  --tw-ring-offset-width: 2px;\\n}\\n.btn-secondary:active{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(241 245 249 / var(--tw-bg-opacity, 1));\\n}\\n/* Accent Button - Orange highlight */\\n/* Outline Button */\\n.btn-outline{\\n  border-radius: 0.5rem;\\n  border-width: 2px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(8 145 178 / var(--tw-border-opacity, 1));\\n  background-color: transparent;\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n  font-weight: 500;\\n  --tw-text-opacity: 1;\\n  color: rgb(8 145 178 / var(--tw-text-opacity, 1));\\n  transition-property: all;\\n  transition-duration: 200ms;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.btn-outline:hover{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(14 116 144 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(236 254 255 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(14 116 144 / var(--tw-text-opacity, 1));\\n}\\n.btn-outline:focus{\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(0 212 255 / var(--tw-ring-opacity, 1));\\n  --tw-ring-offset-width: 2px;\\n}\\n.btn-outline:active{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(207 250 254 / var(--tw-bg-opacity, 1));\\n}\\n/* Card Components */\\n.card{\\n  border-radius: 1rem;\\n  border-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n  --tw-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);\\n  --tw-shadow-colored: 0 2px 15px -3px var(--tw-shadow-color), 0 10px 20px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n  transition-property: all;\\n  transition-duration: 300ms;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.card:hover{\\n  --tw-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\\n  --tw-shadow-colored: 0 4px 25px -5px var(--tw-shadow-color), 0 10px 10px -5px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.card-interactive{\\n  border-radius: 1rem;\\n  border-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n  --tw-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);\\n  --tw-shadow-colored: 0 2px 15px -3px var(--tw-shadow-color), 0 10px 20px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n  transition-property: all;\\n  transition-duration: 300ms;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.card-interactive:hover{\\n  --tw-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\\n  --tw-shadow-colored: 0 4px 25px -5px var(--tw-shadow-color), 0 10px 10px -5px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.card-interactive{\\n  cursor: pointer;\\n}\\n.card-interactive:hover{\\n  --tw-translate-y: -0.25rem;\\n  --tw-scale-x: 1.02;\\n  --tw-scale-y: 1.02;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.card-body{\\n  padding: 1.5rem;\\n}\\n/* Navigation Styles */\\n.nav-link{\\n  border-radius: 0.5rem;\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n  font-weight: 500;\\n  --tw-text-opacity: 1;\\n  color: rgb(2 132 199 / var(--tw-text-opacity, 1));\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n}\\n.nav-link:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(236 254 255 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(8 145 178 / var(--tw-text-opacity, 1));\\n}\\n.nav-link-active{\\n  border-radius: 0.5rem;\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n  font-weight: 500;\\n  --tw-text-opacity: 1;\\n  color: rgb(2 132 199 / var(--tw-text-opacity, 1));\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n}\\n.nav-link-active:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(236 254 255 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(8 145 178 / var(--tw-text-opacity, 1));\\n}\\n.nav-link-active{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(207 250 254 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(14 116 144 / var(--tw-text-opacity, 1));\\n}\\n/* Form Styles */\\n.form-input{\\n  width: 100%;\\n  border-radius: 0.5rem;\\n  border-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(203 213 225 / var(--tw-border-opacity, 1));\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n}\\n.form-input::-moz-placeholder{\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(148 163 184 / var(--tw-placeholder-opacity, 1));\\n}\\n.form-input::placeholder{\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(148 163 184 / var(--tw-placeholder-opacity, 1));\\n}\\n.form-input{\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n}\\n.form-input:focus{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(0 212 255 / var(--tw-border-opacity, 1));\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(0 212 255 / var(--tw-ring-opacity, 1));\\n}\\n.form-label{\\n  margin-bottom: 0.5rem;\\n  display: block;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  font-weight: 500;\\n  --tw-text-opacity: 1;\\n  color: rgb(3 105 161 / var(--tw-text-opacity, 1));\\n}\\n/* Badge Components */\\n.badge{\\n  display: inline-flex;\\n  align-items: center;\\n  border-radius: 9999px;\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  font-weight: 500;\\n}\\n.badge-primary{\\n  display: inline-flex;\\n  align-items: center;\\n  border-radius: 9999px;\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  font-weight: 500;\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(207 250 254 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(21 94 117 / var(--tw-text-opacity, 1));\\n}\\n.badge-secondary{\\n  display: inline-flex;\\n  align-items: center;\\n  border-radius: 9999px;\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  font-weight: 500;\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(224 242 254 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(10 15 28 / var(--tw-text-opacity, 1));\\n}\\n/* Status Indicators */\\n/* Loading States */\\n@keyframes spin{\\n\\n  to{\\n    transform: rotate(360deg);\\n  }\\n}\\n.loading-spinner{\\n  animation: spin 1s linear infinite;\\n  border-radius: 9999px;\\n  border-width: 2px;\\n  border-color: rgb(203 213 225 / var(--tw-border-opacity, 1));\\n  --tw-border-opacity: 1;\\n  border-top-color: rgb(8 145 178 / var(--tw-border-opacity, 1));\\n}\\n/* Professional Layout Utilities */\\n.container-app{\\n  margin-left: auto;\\n  margin-right: auto;\\n  max-width: 80rem;\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\n@media (min-width: 640px){\\n\\n  .container-app{\\n    padding-left: 1.5rem;\\n    padding-right: 1.5rem;\\n  }\\n}\\n@media (min-width: 1024px){\\n\\n  .container-app{\\n    padding-left: 2rem;\\n    padding-right: 2rem;\\n  }\\n}\\n.section-padding{\\n  padding-top: 3rem;\\n  padding-bottom: 3rem;\\n}\\n@media (min-width: 1024px){\\n\\n  .section-padding{\\n    padding-top: 4rem;\\n    padding-bottom: 4rem;\\n  }\\n}\\n/* Dashboard Grid */\\n.dashboard-grid{\\n  display: grid;\\n  grid-template-columns: repeat(1, minmax(0, 1fr));\\n  gap: 1.5rem;\\n}\\n@media (min-width: 768px){\\n\\n  .dashboard-grid{\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n}\\n@media (min-width: 1024px){\\n\\n  .dashboard-grid{\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n}\\n/* Responsive Tables */\\n/* Cosmic/Network Theme Animations */\\n/* Floating animation for network elements */\\n@keyframes float {\\n    0%, 100% {\\n      transform: translateY(0px) scale(1);\\n    }\\n    50% {\\n      transform: translateY(-10px) scale(1.05);\\n    }\\n  }\\n/* Cosmic pulse effect */\\n@keyframes cosmic-pulse {\\n    0%, 100% {\\n      opacity: 0.6;\\n      transform: scale(1);\\n    }\\n    50% {\\n      opacity: 1;\\n      transform: scale(1.1);\\n    }\\n  }\\n/* Connection flow animation */\\n@keyframes connection-flow {\\n    0% {\\n      stroke-dasharray: 0 10;\\n    }\\n    50% {\\n      stroke-dasharray: 5 5;\\n    }\\n    100% {\\n      stroke-dasharray: 10 0;\\n    }\\n  }\\n/* Network connection background - subtle and non-distracting */\\n.network-background {\\n    background:\\n      radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),\\n      radial-gradient(circle at 80% 80%, rgba(0, 212, 255, 0.08) 0%, transparent 50%),\\n      radial-gradient(circle at 40% 60%, rgba(255, 255, 255, 0.05) 0%, transparent 30%);\\n    background-size: 800px 800px, 600px 600px, 400px 400px;\\n    animation: network-drift 30s ease-in-out infinite;\\n  }\\n@keyframes network-drift {\\n    0%, 100% {\\n      background-position: 0% 0%, 100% 100%, 50% 50%;\\n    }\\n    33% {\\n      background-position: 30% 20%, 70% 80%, 60% 40%;\\n    }\\n    66% {\\n      background-position: 70% 30%, 30% 70%, 40% 60%;\\n    }\\n  }\\n/* Enhanced gradient text for cosmic theme */\\n.cosmic-gradient{\\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\\n  --tw-gradient-from: #22d3ee var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(34 211 238 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n  --tw-gradient-to: rgb(0 212 255 / 0)  var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), #00d4ff var(--tw-gradient-via-position), var(--tw-gradient-to);\\n  --tw-gradient-to: #0891b2 var(--tw-gradient-to-position);\\n  -webkit-background-clip: text;\\n          background-clip: text;\\n  color: transparent;\\n    background-size: 200% 200%;\\n    animation: gradient-shift 3s ease infinite;\\n}\\n@keyframes gradient-shift {\\n    0%, 100% {\\n      background-position: 0% 50%;\\n    }\\n    50% {\\n      background-position: 100% 50%;\\n    }\\n  }\\n.fixed{\\n  position: fixed;\\n}\\n.absolute{\\n  position: absolute;\\n}\\n.relative{\\n  position: relative;\\n}\\n.inset-0{\\n  inset: 0px;\\n}\\n.-left-16{\\n  left: -4rem;\\n}\\n.-left-32{\\n  left: -8rem;\\n}\\n.-top-16{\\n  top: -4rem;\\n}\\n.-top-32{\\n  top: -8rem;\\n}\\n.left-0{\\n  left: 0px;\\n}\\n.left-16{\\n  left: 4rem;\\n}\\n.left-32{\\n  left: 8rem;\\n}\\n.left-4{\\n  left: 1rem;\\n}\\n.right-4{\\n  right: 1rem;\\n}\\n.top-0{\\n  top: 0px;\\n}\\n.top-16{\\n  top: 4rem;\\n}\\n.top-32{\\n  top: 8rem;\\n}\\n.top-4{\\n  top: 1rem;\\n}\\n.z-10{\\n  z-index: 10;\\n}\\n.z-50{\\n  z-index: 50;\\n}\\n.-mx-1\\\\.5{\\n  margin-left: -0.375rem;\\n  margin-right: -0.375rem;\\n}\\n.-my-1\\\\.5{\\n  margin-top: -0.375rem;\\n  margin-bottom: -0.375rem;\\n}\\n.mx-2{\\n  margin-left: 0.5rem;\\n  margin-right: 0.5rem;\\n}\\n.mx-auto{\\n  margin-left: auto;\\n  margin-right: auto;\\n}\\n.-mb-px{\\n  margin-bottom: -1px;\\n}\\n.-mt-1{\\n  margin-top: -0.25rem;\\n}\\n.mb-1{\\n  margin-bottom: 0.25rem;\\n}\\n.mb-12{\\n  margin-bottom: 3rem;\\n}\\n.mb-2{\\n  margin-bottom: 0.5rem;\\n}\\n.mb-3{\\n  margin-bottom: 0.75rem;\\n}\\n.mb-4{\\n  margin-bottom: 1rem;\\n}\\n.mb-6{\\n  margin-bottom: 1.5rem;\\n}\\n.mb-8{\\n  margin-bottom: 2rem;\\n}\\n.ml-1{\\n  margin-left: 0.25rem;\\n}\\n.ml-2{\\n  margin-left: 0.5rem;\\n}\\n.ml-3{\\n  margin-left: 0.75rem;\\n}\\n.ml-4{\\n  margin-left: 1rem;\\n}\\n.ml-auto{\\n  margin-left: auto;\\n}\\n.mr-2{\\n  margin-right: 0.5rem;\\n}\\n.mr-3{\\n  margin-right: 0.75rem;\\n}\\n.mr-4{\\n  margin-right: 1rem;\\n}\\n.mt-1{\\n  margin-top: 0.25rem;\\n}\\n.mt-2{\\n  margin-top: 0.5rem;\\n}\\n.mt-4{\\n  margin-top: 1rem;\\n}\\n.mt-6{\\n  margin-top: 1.5rem;\\n}\\n.line-clamp-3{\\n  overflow: hidden;\\n  display: -webkit-box;\\n  -webkit-box-orient: vertical;\\n  -webkit-line-clamp: 3;\\n}\\n.block{\\n  display: block;\\n}\\n.inline-block{\\n  display: inline-block;\\n}\\n.flex{\\n  display: flex;\\n}\\n.inline-flex{\\n  display: inline-flex;\\n}\\n.grid{\\n  display: grid;\\n}\\n.hidden{\\n  display: none;\\n}\\n.h-1{\\n  height: 0.25rem;\\n}\\n.h-10{\\n  height: 2.5rem;\\n}\\n.h-12{\\n  height: 3rem;\\n}\\n.h-16{\\n  height: 4rem;\\n}\\n.h-2{\\n  height: 0.5rem;\\n}\\n.h-20{\\n  height: 5rem;\\n}\\n.h-3{\\n  height: 0.75rem;\\n}\\n.h-4{\\n  height: 1rem;\\n}\\n.h-5{\\n  height: 1.25rem;\\n}\\n.h-6{\\n  height: 1.5rem;\\n}\\n.h-64{\\n  height: 16rem;\\n}\\n.h-8{\\n  height: 2rem;\\n}\\n.h-96{\\n  height: 24rem;\\n}\\n.h-\\\\[500px\\\\]{\\n  height: 500px;\\n}\\n.h-full{\\n  height: 100%;\\n}\\n.min-h-full{\\n  min-height: 100%;\\n}\\n.min-h-screen{\\n  min-height: 100vh;\\n}\\n.w-10{\\n  width: 2.5rem;\\n}\\n.w-12{\\n  width: 3rem;\\n}\\n.w-16{\\n  width: 4rem;\\n}\\n.w-20{\\n  width: 5rem;\\n}\\n.w-24{\\n  width: 6rem;\\n}\\n.w-3{\\n  width: 0.75rem;\\n}\\n.w-4{\\n  width: 1rem;\\n}\\n.w-5{\\n  width: 1.25rem;\\n}\\n.w-6{\\n  width: 1.5rem;\\n}\\n.w-8{\\n  width: 2rem;\\n}\\n.w-full{\\n  width: 100%;\\n}\\n.min-w-0{\\n  min-width: 0px;\\n}\\n.min-w-64{\\n  min-width: 16rem;\\n}\\n.max-w-2xl{\\n  max-width: 42rem;\\n}\\n.max-w-3xl{\\n  max-width: 48rem;\\n}\\n.max-w-4xl{\\n  max-width: 56rem;\\n}\\n.max-w-6xl{\\n  max-width: 72rem;\\n}\\n.max-w-7xl{\\n  max-width: 80rem;\\n}\\n.max-w-md{\\n  max-width: 28rem;\\n}\\n.max-w-xs{\\n  max-width: 20rem;\\n}\\n.flex-1{\\n  flex: 1 1 0%;\\n}\\n.flex-shrink-0{\\n  flex-shrink: 0;\\n}\\n.rotate-180{\\n  --tw-rotate: 180deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.transform{\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n@keyframes spin{\\n\\n  to{\\n    transform: rotate(360deg);\\n  }\\n}\\n.animate-spin{\\n  animation: spin 1s linear infinite;\\n}\\n.cursor-not-allowed{\\n  cursor: not-allowed;\\n}\\n.resize{\\n  resize: both;\\n}\\n.list-inside{\\n  list-style-position: inside;\\n}\\n.list-disc{\\n  list-style-type: disc;\\n}\\n.grid-cols-1{\\n  grid-template-columns: repeat(1, minmax(0, 1fr));\\n}\\n.grid-cols-2{\\n  grid-template-columns: repeat(2, minmax(0, 1fr));\\n}\\n.flex-col{\\n  flex-direction: column;\\n}\\n.flex-wrap{\\n  flex-wrap: wrap;\\n}\\n.items-start{\\n  align-items: flex-start;\\n}\\n.items-center{\\n  align-items: center;\\n}\\n.justify-end{\\n  justify-content: flex-end;\\n}\\n.justify-center{\\n  justify-content: center;\\n}\\n.justify-between{\\n  justify-content: space-between;\\n}\\n.gap-1{\\n  gap: 0.25rem;\\n}\\n.gap-2{\\n  gap: 0.5rem;\\n}\\n.gap-4{\\n  gap: 1rem;\\n}\\n.gap-6{\\n  gap: 1.5rem;\\n}\\n.gap-8{\\n  gap: 2rem;\\n}\\n.space-x-1 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.25rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-2 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-3 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.75rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-4 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-8 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(2rem * var(--tw-space-x-reverse));\\n  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-y-1 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\\n}\\n.space-y-2 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\\n}\\n.space-y-3 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\\n}\\n.space-y-4 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\\n}\\n.space-y-6 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\\n}\\n.space-y-8 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\\n}\\n.divide-y > :not([hidden]) ~ :not([hidden]){\\n  --tw-divide-y-reverse: 0;\\n  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));\\n  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));\\n}\\n.overflow-hidden{\\n  overflow: hidden;\\n}\\n.overflow-y-auto{\\n  overflow-y: auto;\\n}\\n.truncate{\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.rounded{\\n  border-radius: 0.25rem;\\n}\\n.rounded-2xl{\\n  border-radius: 1.5rem;\\n}\\n.rounded-3xl{\\n  border-radius: 1.5rem;\\n}\\n.rounded-full{\\n  border-radius: 9999px;\\n}\\n.rounded-lg{\\n  border-radius: 0.5rem;\\n}\\n.rounded-md{\\n  border-radius: 0.375rem;\\n}\\n.rounded-xl{\\n  border-radius: 1rem;\\n}\\n.rounded-b-lg{\\n  border-bottom-right-radius: 0.5rem;\\n  border-bottom-left-radius: 0.5rem;\\n}\\n.rounded-t-lg{\\n  border-top-left-radius: 0.5rem;\\n  border-top-right-radius: 0.5rem;\\n}\\n.border{\\n  border-width: 1px;\\n}\\n.border-2{\\n  border-width: 2px;\\n}\\n.border-b{\\n  border-bottom-width: 1px;\\n}\\n.border-b-2{\\n  border-bottom-width: 2px;\\n}\\n.border-l-4{\\n  border-left-width: 4px;\\n}\\n.border-t{\\n  border-top-width: 1px;\\n}\\n.border-t-2{\\n  border-top-width: 2px;\\n}\\n.border-amber-500{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(245 158 11 / var(--tw-border-opacity, 1));\\n}\\n.border-blue-200{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));\\n}\\n.border-candid-gray-200{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));\\n}\\n.border-cyan-200{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(165 243 252 / var(--tw-border-opacity, 1));\\n}\\n.border-emerald-500{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(16 185 129 / var(--tw-border-opacity, 1));\\n}\\n.border-gray-100{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));\\n}\\n.border-gray-200{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));\\n}\\n.border-gray-300{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\\n}\\n.border-green-200{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));\\n}\\n.border-indigo-500{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(99 102 241 / var(--tw-border-opacity, 1));\\n}\\n.border-orange-200{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(254 215 170 / var(--tw-border-opacity, 1));\\n}\\n.border-primary-400{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(34 211 238 / var(--tw-border-opacity, 1));\\n}\\n.border-primary-500{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(0 212 255 / var(--tw-border-opacity, 1));\\n}\\n.border-primary-600{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(8 145 178 / var(--tw-border-opacity, 1));\\n}\\n.border-purple-200{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(233 213 255 / var(--tw-border-opacity, 1));\\n}\\n.border-red-200{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));\\n}\\n.border-red-400{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(248 113 113 / var(--tw-border-opacity, 1));\\n}\\n.border-teal-500{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(20 184 166 / var(--tw-border-opacity, 1));\\n}\\n.border-transparent{\\n  border-color: transparent;\\n}\\n.border-yellow-200{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));\\n}\\n.bg-accent-300{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(253 186 116 / var(--tw-bg-opacity, 1));\\n}\\n.bg-accent-400{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(251 146 60 / var(--tw-bg-opacity, 1));\\n}\\n.bg-accent-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));\\n}\\n.bg-accent-600{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(234 88 12 / var(--tw-bg-opacity, 1));\\n}\\n.bg-amber-100{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 243 199 / var(--tw-bg-opacity, 1));\\n}\\n.bg-amber-50{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 251 235 / var(--tw-bg-opacity, 1));\\n}\\n.bg-amber-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(245 158 11 / var(--tw-bg-opacity, 1));\\n}\\n.bg-black{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-100{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-50{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));\\n}\\n.bg-cyan-50{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(236 254 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-emerald-100{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(209 250 229 / var(--tw-bg-opacity, 1));\\n}\\n.bg-emerald-50{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(236 253 245 / var(--tw-bg-opacity, 1));\\n}\\n.bg-emerald-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(16 185 129 / var(--tw-bg-opacity, 1));\\n}\\n.bg-emerald-600{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(5 150 105 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-100{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-200{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-50{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-100{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-50{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-600{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));\\n}\\n.bg-indigo-100{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(224 231 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-indigo-50{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(238 242 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-indigo-600{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(79 70 229 / var(--tw-bg-opacity, 1));\\n}\\n.bg-orange-100{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));\\n}\\n.bg-orange-50{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));\\n}\\n.bg-orange-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));\\n}\\n.bg-primary-300{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(103 232 249 / var(--tw-bg-opacity, 1));\\n}\\n.bg-primary-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(0 212 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-primary-600{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(8 145 178 / var(--tw-bg-opacity, 1));\\n}\\n.bg-purple-100{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-purple-50{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-purple-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));\\n}\\n.bg-red-100{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));\\n}\\n.bg-red-50{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));\\n}\\n.bg-red-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));\\n}\\n.bg-red-600{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));\\n}\\n.bg-secondary-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(30 58 138 / var(--tw-bg-opacity, 1));\\n}\\n.bg-secondary-600{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(30 42 74 / var(--tw-bg-opacity, 1));\\n}\\n.bg-secondary-700{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(15 22 41 / var(--tw-bg-opacity, 1));\\n}\\n.bg-secondary-800{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(10 15 28 / var(--tw-bg-opacity, 1));\\n}\\n.bg-teal-100{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(204 251 241 / var(--tw-bg-opacity, 1));\\n}\\n.bg-teal-600{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(13 148 136 / var(--tw-bg-opacity, 1));\\n}\\n.bg-white{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-yellow-100{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));\\n}\\n.bg-yellow-50{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));\\n}\\n.bg-yellow-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));\\n}\\n.bg-opacity-50{\\n  --tw-bg-opacity: 0.5;\\n}\\n.bg-gradient-to-br{\\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\\n}\\n.bg-gradient-to-r{\\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\\n}\\n.from-accent-500{\\n  --tw-gradient-from: #f97316 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(249 115 22 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-blue-50{\\n  --tw-gradient-from: #eff6ff var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-candid-navy-600{\\n  --tw-gradient-from: #0284c7 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(2 132 199 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-candid-orange-500{\\n  --tw-gradient-from: #f97316 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(249 115 22 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-cyan-50{\\n  --tw-gradient-from: #ecfeff var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(236 254 255 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-orange-50{\\n  --tw-gradient-from: #fff7ed var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(255 247 237 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-primary-50{\\n  --tw-gradient-from: #ecfeff var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(236 254 255 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-primary-500{\\n  --tw-gradient-from: #00d4ff var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(0 212 255 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-purple-50{\\n  --tw-gradient-from: #faf5ff var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(250 245 255 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-secondary-500{\\n  --tw-gradient-from: #1e3a8a var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(30 58 138 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-secondary-900{\\n  --tw-gradient-from: #050812 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(5 8 18 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.via-secondary-800{\\n  --tw-gradient-to: rgb(10 15 28 / 0)  var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), #0a0f1c var(--tw-gradient-via-position), var(--tw-gradient-to);\\n}\\n.to-accent-500{\\n  --tw-gradient-to: #f97316 var(--tw-gradient-to-position);\\n}\\n.to-accent-600{\\n  --tw-gradient-to: #ea580c var(--tw-gradient-to-position);\\n}\\n.to-candid-navy-700{\\n  --tw-gradient-to: #0369a1 var(--tw-gradient-to-position);\\n}\\n.to-candid-orange-600{\\n  --tw-gradient-to: #ea580c var(--tw-gradient-to-position);\\n}\\n.to-orange-100{\\n  --tw-gradient-to: #ffedd5 var(--tw-gradient-to-position);\\n}\\n.to-primary-100{\\n  --tw-gradient-to: #cffafe var(--tw-gradient-to-position);\\n}\\n.to-primary-500{\\n  --tw-gradient-to: #00d4ff var(--tw-gradient-to-position);\\n}\\n.to-primary-600{\\n  --tw-gradient-to: #0891b2 var(--tw-gradient-to-position);\\n}\\n.to-secondary-600{\\n  --tw-gradient-to: #1e2a4a var(--tw-gradient-to-position);\\n}\\n.to-white{\\n  --tw-gradient-to: #fff var(--tw-gradient-to-position);\\n}\\n.p-1\\\\.5{\\n  padding: 0.375rem;\\n}\\n.p-12{\\n  padding: 3rem;\\n}\\n.p-2{\\n  padding: 0.5rem;\\n}\\n.p-3{\\n  padding: 0.75rem;\\n}\\n.p-4{\\n  padding: 1rem;\\n}\\n.p-6{\\n  padding: 1.5rem;\\n}\\n.px-1{\\n  padding-left: 0.25rem;\\n  padding-right: 0.25rem;\\n}\\n.px-2{\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\n.px-3{\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n}\\n.px-4{\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\n.px-6{\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n}\\n.px-8{\\n  padding-left: 2rem;\\n  padding-right: 2rem;\\n}\\n.py-1{\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n}\\n.py-1\\\\.5{\\n  padding-top: 0.375rem;\\n  padding-bottom: 0.375rem;\\n}\\n.py-12{\\n  padding-top: 3rem;\\n  padding-bottom: 3rem;\\n}\\n.py-2{\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n}\\n.py-3{\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n}\\n.py-4{\\n  padding-top: 1rem;\\n  padding-bottom: 1rem;\\n}\\n.py-8{\\n  padding-top: 2rem;\\n  padding-bottom: 2rem;\\n}\\n.pb-20{\\n  padding-bottom: 5rem;\\n}\\n.pb-4{\\n  padding-bottom: 1rem;\\n}\\n.pl-4{\\n  padding-left: 1rem;\\n}\\n.pt-4{\\n  padding-top: 1rem;\\n}\\n.pt-5{\\n  padding-top: 1.25rem;\\n}\\n.text-left{\\n  text-align: left;\\n}\\n.text-center{\\n  text-align: center;\\n}\\n.text-right{\\n  text-align: right;\\n}\\n.align-bottom{\\n  vertical-align: bottom;\\n}\\n.text-2xl{\\n  font-size: 1.5rem;\\n  line-height: 2rem;\\n}\\n.text-3xl{\\n  font-size: 1.875rem;\\n  line-height: 2.25rem;\\n}\\n.text-4xl{\\n  font-size: 2.25rem;\\n  line-height: 2.5rem;\\n}\\n.text-6xl{\\n  font-size: 3.75rem;\\n  line-height: 1;\\n}\\n.text-base{\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n}\\n.text-lg{\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n}\\n.text-sm{\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\n.text-xl{\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n}\\n.text-xs{\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\n.font-bold{\\n  font-weight: 700;\\n}\\n.font-medium{\\n  font-weight: 500;\\n}\\n.font-semibold{\\n  font-weight: 600;\\n}\\n.uppercase{\\n  text-transform: uppercase;\\n}\\n.leading-6{\\n  line-height: 1.5rem;\\n}\\n.leading-relaxed{\\n  line-height: 1.625;\\n}\\n.tracking-wide{\\n  letter-spacing: 0.025em;\\n}\\n.text-accent-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(234 88 12 / var(--tw-text-opacity, 1));\\n}\\n.text-amber-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(217 119 6 / var(--tw-text-opacity, 1));\\n}\\n.text-amber-800{\\n  --tw-text-opacity: 1;\\n  color: rgb(146 64 14 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-700{\\n  --tw-text-opacity: 1;\\n  color: rgb(29 78 216 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-800{\\n  --tw-text-opacity: 1;\\n  color: rgb(30 64 175 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-900{\\n  --tw-text-opacity: 1;\\n  color: rgb(30 58 138 / var(--tw-text-opacity, 1));\\n}\\n.text-candid-gray-500{\\n  --tw-text-opacity: 1;\\n  color: rgb(100 116 139 / var(--tw-text-opacity, 1));\\n}\\n.text-candid-gray-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(71 85 105 / var(--tw-text-opacity, 1));\\n}\\n.text-candid-navy-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(2 132 199 / var(--tw-text-opacity, 1));\\n}\\n.text-candid-navy-900{\\n  --tw-text-opacity: 1;\\n  color: rgb(30 42 74 / var(--tw-text-opacity, 1));\\n}\\n.text-cyan-700{\\n  --tw-text-opacity: 1;\\n  color: rgb(14 116 144 / var(--tw-text-opacity, 1));\\n}\\n.text-emerald-500{\\n  --tw-text-opacity: 1;\\n  color: rgb(16 185 129 / var(--tw-text-opacity, 1));\\n}\\n.text-emerald-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(5 150 105 / var(--tw-text-opacity, 1));\\n}\\n.text-emerald-800{\\n  --tw-text-opacity: 1;\\n  color: rgb(6 95 70 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-300{\\n  --tw-text-opacity: 1;\\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-500{\\n  --tw-text-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-700{\\n  --tw-text-opacity: 1;\\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-800{\\n  --tw-text-opacity: 1;\\n  color: rgb(31 41 55 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-900{\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\\n}\\n.text-green-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(22 163 74 / var(--tw-text-opacity, 1));\\n}\\n.text-green-700{\\n  --tw-text-opacity: 1;\\n  color: rgb(21 128 61 / var(--tw-text-opacity, 1));\\n}\\n.text-green-800{\\n  --tw-text-opacity: 1;\\n  color: rgb(22 101 52 / var(--tw-text-opacity, 1));\\n}\\n.text-indigo-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(79 70 229 / var(--tw-text-opacity, 1));\\n}\\n.text-indigo-700{\\n  --tw-text-opacity: 1;\\n  color: rgb(67 56 202 / var(--tw-text-opacity, 1));\\n}\\n.text-indigo-800{\\n  --tw-text-opacity: 1;\\n  color: rgb(55 48 163 / var(--tw-text-opacity, 1));\\n}\\n.text-orange-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(234 88 12 / var(--tw-text-opacity, 1));\\n}\\n.text-orange-700{\\n  --tw-text-opacity: 1;\\n  color: rgb(194 65 12 / var(--tw-text-opacity, 1));\\n}\\n.text-orange-800{\\n  --tw-text-opacity: 1;\\n  color: rgb(154 52 18 / var(--tw-text-opacity, 1));\\n}\\n.text-primary-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(34 211 238 / var(--tw-text-opacity, 1));\\n}\\n.text-primary-500{\\n  --tw-text-opacity: 1;\\n  color: rgb(0 212 255 / var(--tw-text-opacity, 1));\\n}\\n.text-primary-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(8 145 178 / var(--tw-text-opacity, 1));\\n}\\n.text-purple-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(147 51 234 / var(--tw-text-opacity, 1));\\n}\\n.text-purple-700{\\n  --tw-text-opacity: 1;\\n  color: rgb(126 34 206 / var(--tw-text-opacity, 1));\\n}\\n.text-purple-800{\\n  --tw-text-opacity: 1;\\n  color: rgb(107 33 168 / var(--tw-text-opacity, 1));\\n}\\n.text-red-500{\\n  --tw-text-opacity: 1;\\n  color: rgb(239 68 68 / var(--tw-text-opacity, 1));\\n}\\n.text-red-700{\\n  --tw-text-opacity: 1;\\n  color: rgb(185 28 28 / var(--tw-text-opacity, 1));\\n}\\n.text-red-800{\\n  --tw-text-opacity: 1;\\n  color: rgb(153 27 27 / var(--tw-text-opacity, 1));\\n}\\n.text-secondary-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(30 42 74 / var(--tw-text-opacity, 1));\\n}\\n.text-secondary-700{\\n  --tw-text-opacity: 1;\\n  color: rgb(15 22 41 / var(--tw-text-opacity, 1));\\n}\\n.text-secondary-800{\\n  --tw-text-opacity: 1;\\n  color: rgb(10 15 28 / var(--tw-text-opacity, 1));\\n}\\n.text-secondary-900{\\n  --tw-text-opacity: 1;\\n  color: rgb(5 8 18 / var(--tw-text-opacity, 1));\\n}\\n.text-teal-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(13 148 136 / var(--tw-text-opacity, 1));\\n}\\n.text-teal-800{\\n  --tw-text-opacity: 1;\\n  color: rgb(17 94 89 / var(--tw-text-opacity, 1));\\n}\\n.text-white{\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-700{\\n  --tw-text-opacity: 1;\\n  color: rgb(161 98 7 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-800{\\n  --tw-text-opacity: 1;\\n  color: rgb(133 77 14 / var(--tw-text-opacity, 1));\\n}\\n.underline{\\n  text-decoration-line: underline;\\n}\\n.opacity-20{\\n  opacity: 0.2;\\n}\\n.opacity-50{\\n  opacity: 0.5;\\n}\\n.opacity-75{\\n  opacity: 0.75;\\n}\\n.shadow{\\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-lg{\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-md{\\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-sm{\\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-soft{\\n  --tw-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);\\n  --tw-shadow-colored: 0 2px 15px -3px var(--tw-shadow-color), 0 10px 20px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-xl{\\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.filter{\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.transition{\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-all{\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-colors{\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-opacity{\\n  transition-property: opacity;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-transform{\\n  transition-property: transform;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.duration-200{\\n  transition-duration: 200ms;\\n}\\n.duration-300{\\n  transition-duration: 300ms;\\n}\\n\\n/* Candid Connections Global Styles */\\n\\n/* Candid Connections Component Library */\\n.hover\\\\:scale-105:hover{\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.hover\\\\:border-blue-300:hover{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));\\n}\\n.hover\\\\:border-candid-gray-300:hover{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(203 213 225 / var(--tw-border-opacity, 1));\\n}\\n.hover\\\\:border-cyan-300:hover{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(103 232 249 / var(--tw-border-opacity, 1));\\n}\\n.hover\\\\:border-gray-300:hover{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\\n}\\n.hover\\\\:border-gray-400:hover{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));\\n}\\n.hover\\\\:border-orange-300:hover{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(253 186 116 / var(--tw-border-opacity, 1));\\n}\\n.hover\\\\:border-purple-300:hover{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(216 180 254 / var(--tw-border-opacity, 1));\\n}\\n.hover\\\\:bg-accent-600:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(234 88 12 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-blue-100:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-cyan-100:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(207 250 254 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-emerald-700:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(4 120 87 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-gray-100:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-gray-200:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-gray-50:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-green-600:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-green-700:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-indigo-700:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(67 56 202 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-orange-100:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-primary-400:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(34 211 238 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-primary-50:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(236 254 255 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-primary-600:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(8 145 178 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-primary-700:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(14 116 144 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-purple-100:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-red-600:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-red-700:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-secondary-900:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(5 8 18 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-teal-700:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(15 118 110 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-yellow-600:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(202 138 4 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:text-candid-gray-700:hover{\\n  --tw-text-opacity: 1;\\n  color: rgb(51 65 85 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-gray-600:hover{\\n  --tw-text-opacity: 1;\\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-primary-600:hover{\\n  --tw-text-opacity: 1;\\n  color: rgb(8 145 178 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-primary-700:hover{\\n  --tw-text-opacity: 1;\\n  color: rgb(14 116 144 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-secondary-900:hover{\\n  --tw-text-opacity: 1;\\n  color: rgb(5 8 18 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-teal-700:hover{\\n  --tw-text-opacity: 1;\\n  color: rgb(15 118 110 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:shadow-md:hover{\\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.hover\\\\:shadow-xl:hover{\\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.focus\\\\:outline-none:focus{\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.focus\\\\:ring-2:focus{\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n.focus\\\\:ring-accent-500:focus{\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(249 115 22 / var(--tw-ring-opacity, 1));\\n}\\n.focus\\\\:ring-blue-500:focus{\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));\\n}\\n.focus\\\\:ring-cyan-500:focus{\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(6 182 212 / var(--tw-ring-opacity, 1));\\n}\\n.focus\\\\:ring-gray-300:focus{\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(209 213 219 / var(--tw-ring-opacity, 1));\\n}\\n.focus\\\\:ring-gray-500:focus{\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(107 114 128 / var(--tw-ring-opacity, 1));\\n}\\n.focus\\\\:ring-green-500:focus{\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(34 197 94 / var(--tw-ring-opacity, 1));\\n}\\n.focus\\\\:ring-orange-500:focus{\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(249 115 22 / var(--tw-ring-opacity, 1));\\n}\\n.focus\\\\:ring-primary-500:focus{\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(0 212 255 / var(--tw-ring-opacity, 1));\\n}\\n.focus\\\\:ring-purple-500:focus{\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(168 85 247 / var(--tw-ring-opacity, 1));\\n}\\n.focus\\\\:ring-red-500:focus{\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));\\n}\\n.focus\\\\:ring-yellow-500:focus{\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(234 179 8 / var(--tw-ring-opacity, 1));\\n}\\n.focus\\\\:ring-offset-2:focus{\\n  --tw-ring-offset-width: 2px;\\n}\\n.group:hover .group-hover\\\\:translate-x-1{\\n  --tw-translate-x: 0.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.group:hover .group-hover\\\\:scale-105{\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.group:hover .group-hover\\\\:text-primary-500{\\n  --tw-text-opacity: 1;\\n  color: rgb(0 212 255 / var(--tw-text-opacity, 1));\\n}\\n.group:hover .group-hover\\\\:text-primary-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(8 145 178 / var(--tw-text-opacity, 1));\\n}\\n.group:hover .group-hover\\\\:text-primary-700{\\n  --tw-text-opacity: 1;\\n  color: rgb(14 116 144 / var(--tw-text-opacity, 1));\\n}\\n.group:hover .group-hover\\\\:shadow-medium{\\n  --tw-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\\n  --tw-shadow-colored: 0 4px 25px -5px var(--tw-shadow-color), 0 10px 10px -5px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n@media (min-width: 640px){\\n\\n  .sm\\\\:my-8{\\n    margin-top: 2rem;\\n    margin-bottom: 2rem;\\n  }\\n\\n  .sm\\\\:block{\\n    display: block;\\n  }\\n\\n  .sm\\\\:flex{\\n    display: flex;\\n  }\\n\\n  .sm\\\\:w-full{\\n    width: 100%;\\n  }\\n\\n  .sm\\\\:max-w-5xl{\\n    max-width: 64rem;\\n  }\\n\\n  .sm\\\\:flex-row{\\n    flex-direction: row;\\n  }\\n\\n  .sm\\\\:flex-row-reverse{\\n    flex-direction: row-reverse;\\n  }\\n\\n  .sm\\\\:items-start{\\n    align-items: flex-start;\\n  }\\n\\n  .sm\\\\:p-0{\\n    padding: 0px;\\n  }\\n\\n  .sm\\\\:p-6{\\n    padding: 1.5rem;\\n  }\\n\\n  .sm\\\\:px-6{\\n    padding-left: 1.5rem;\\n    padding-right: 1.5rem;\\n  }\\n\\n  .sm\\\\:pb-4{\\n    padding-bottom: 1rem;\\n  }\\n\\n  .sm\\\\:align-middle{\\n    vertical-align: middle;\\n  }\\n}\\n@media (min-width: 768px){\\n\\n  .md\\\\:flex{\\n    display: flex;\\n  }\\n\\n  .md\\\\:grid-cols-2{\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:grid-cols-3{\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:grid-cols-4{\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:grid-cols-5{\\n    grid-template-columns: repeat(5, minmax(0, 1fr));\\n  }\\n}\\n@media (min-width: 1024px){\\n\\n  .lg\\\\:col-span-1{\\n    grid-column: span 1 / span 1;\\n  }\\n\\n  .lg\\\\:col-span-2{\\n    grid-column: span 2 / span 2;\\n  }\\n\\n  .lg\\\\:block{\\n    display: block;\\n  }\\n\\n  .lg\\\\:hidden{\\n    display: none;\\n  }\\n\\n  .lg\\\\:grid-cols-2{\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-3{\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-4{\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-5{\\n    grid-template-columns: repeat(5, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:text-6xl{\\n    font-size: 3.75rem;\\n    line-height: 1;\\n  }\\n}\", \"\",{\"version\":3,\"sources\":[\"webpack://styles/globals.css\"],\"names\":[],\"mappings\":\"AAAA,uGAAuG;;AAEvG;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;;AAAd;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;;AAAd;;CAAc;;AAAd;;;CAAc;;AAAd;;;EAAA,sBAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,mBAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,gBAAc;AAAA;;AAAd;;;;;;;;CAAc;;AAAd;;EAAA,gBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gBAAc,EAAd,MAAc;EAAd,cAAc;KAAd,WAAc,EAAd,MAAc;EAAd,8FAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,wCAAc,EAAd,MAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,yCAAc;UAAd,iCAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;EAAA,kBAAc;EAAd,oBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;EAAd,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,mBAAc;AAAA;;AAAd;;;;;CAAc;;AAAd;;;;EAAA,+GAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,cAAc;EAAd,cAAc;EAAd,kBAAc;EAAd,wBAAc;AAAA;;AAAd;EAAA,eAAc;AAAA;;AAAd;EAAA,WAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;EAAd,yBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;EAAA,oBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gCAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,uBAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,SAAc,EAAd,MAAc;EAAd,UAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,oBAAc;AAAA;;AAAd;;;CAAc;;AAAd;;;;EAAA,0BAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,aAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,YAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,6BAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,0BAAc,EAAd,MAAc;EAAd,aAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,kBAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;;;;;;;;EAAA,SAAc;AAAA;;AAAd;EAAA,SAAc;EAAd,UAAc;AAAA;;AAAd;EAAA,UAAc;AAAA;;AAAd;;;EAAA,gBAAc;EAAd,SAAc;EAAd,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,eAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;;;;EAAA,cAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;EAAd,YAAc;AAAA;;AAAd,wEAAc;;AAAd;EAAA,aAAc;AAAA;;AAAd;IAAA,kGAAc;IAAd,uBAAc;IAAd,mCAAc;IAAd,kCAAc;EAAA;;AAAd;EAAA,kBAAc;EAAd,4DAAc;EAAd,kBAAc;EAAd,oBAAc;EAAd,iDAAc;IAAd,SAAc;IAAd;AAAc;;AAAd,8CAAc;;AAAd;EAAA,kBAAc;EAAd,mBAAc;EAAd,sBAAc;IAAd;AAAc;;AAAd,mCAAc;;AAAd;EAAA,gBAAc;EAAd,iBAAc;EAAd,oBAAc;EAAd;AAAc;;AAAd;EAAA,kBAAc;EAAd;AAAc;;AAAd;;EAAA;IAAA,eAAc;IAAd;EAAc;AAAA;;AAAd;EAAA,mBAAc;EAAd;AAAc;;AAAd;;EAAA;IAAA,kBAAc;IAAd;EAAc;AAAA;;AAAd;EAAA,iBAAc;EAAd;AAAc;;AAAd;;EAAA;IAAA,mBAAc;IAAd;EAAc;AAAA;;AAAd;EAAA,kBAAc;EAAd;AAAc;;AAAd;;EAAA;IAAA,iBAAc;IAAd;EAAc;AAAA;;AAAd;EAAA,mBAAc;EAAd;AAAc;;AAAd;;EAAA;IAAA,kBAAc;IAAd;EAAc;AAAA;;AAAd;EAAA,eAAc;EAAd;AAAc;;AAAd;;EAAA;IAAA,mBAAc;IAAd;EAAc;AAAA;;AAAd,6BAAc;;AAAd;EAAA,oBAAc;EAAd,iDAAc;EAAd,+FAAc;EAAd,wDAAc;EAAd;AAAc;;AAAd;EAAA,oBAAc;EAAd;AAAc;;AAAd,kBAAc;;AAAd;EAAA,sBAAc;EAAd;AAAc;;AAAd;EAAA,sBAAc;EAAd,0DAAc;EAAd,oBAAc;EAAd;AAAc;AACd;EAAA;AAAoB;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAiDlB,qCAAqC;AAEnC;EAAA,qBAIiF;EAJjF,kBAIiF;EAJjF,0DAIiF;EAJjF,oBAIiF;EAJjF,uBAIiF;EAJjF,oBAIiF;EAJjF,qBAIiF;EAJjF,gBAIiF;EAJjF,oBAIiF;EAJjF,mDAIiF;EAJjF,sFAIiF;EAJjF,oGAIiF;EAJjF,uGAIiF;EAJjF,wBAIiF;EAJjF,0BAIiF;EAJjF;AAIiF;AAJjF;EAAA,kBAIiF;EAJjF,2DAIiF;EAJjF,qFAIiF;EAJjF,oGAIiF;EAJjF;AAIiF;AAJjF;EAAA,8BAIiF;EAJjF,mBAIiF;EAJjF,2GAIiF;EAJjF,yGAIiF;EAJjF,4FAIiF;EAJjF,oBAIiF;EAJjF,2DAIiF;EAJjF;AAIiF;AAJjF;EAAA,kBAIiF;EAJjF;AAIiF;AAGnF,yCAAyC;AAEvC;EAAA,qBAKmF;EALnF,iBAKmF;EALnF,sBAKmF;EALnF,4DAKmF;EALnF,kBAKmF;EALnF,4DAKmF;EALnF,oBAKmF;EALnF,uBAKmF;EALnF,oBAKmF;EALnF,qBAKmF;EALnF,gBAKmF;EALnF,oBAKmF;EALnF,gDAKmF;EALnF,sFAKmF;EALnF,oGAKmF;EALnF,uGAKmF;EALnF,wBAKmF;EALnF,0BAKmF;EALnF;AAKmF;AALnF;EAAA,sBAKmF;EALnF,4DAKmF;EALnF,kBAKmF;EALnF,4DAKmF;EALnF,qFAKmF;EALnF,oGAKmF;EALnF;AAKmF;AALnF;EAAA,8BAKmF;EALnF,mBAKmF;EALnF,2GAKmF;EALnF,yGAKmF;EALnF,4FAKmF;EALnF,oBAKmF;EALnF,2DAKmF;EALnF;AAKmF;AALnF;EAAA,kBAKmF;EALnF;AAKmF;AAGrF,qCAAqC;AASrC,mBAAmB;AAEjB;EAAA,qBAIiF;EAJjF,iBAIiF;EAJjF,sBAIiF;EAJjF,0DAIiF;EAJjF,6BAIiF;EAJjF,oBAIiF;EAJjF,uBAIiF;EAJjF,oBAIiF;EAJjF,qBAIiF;EAJjF,gBAIiF;EAJjF,oBAIiF;EAJjF,iDAIiF;EAJjF,wBAIiF;EAJjF,0BAIiF;EAJjF;AAIiF;AAJjF;EAAA,sBAIiF;EAJjF,2DAIiF;EAJjF,kBAIiF;EAJjF,4DAIiF;EAJjF,oBAIiF;EAJjF;AAIiF;AAJjF;EAAA,8BAIiF;EAJjF,mBAIiF;EAJjF,2GAIiF;EAJjF,yGAIiF;EAJjF,4FAIiF;EAJjF,oBAIiF;EAJjF,2DAIiF;EAJjF;AAIiF;AAJjF;EAAA,kBAIiF;EAJjF;AAIiF;AAGnF,oBAAoB;AAElB;EAAA,mBAEoC;EAFpC,iBAEoC;EAFpC,sBAEoC;EAFpC,4DAEoC;EAFpC,kBAEoC;EAFpC,4DAEoC;EAFpC,sFAEoC;EAFpC,oGAEoC;EAFpC,uGAEoC;EAFpC,wBAEoC;EAFpC,0BAEoC;EAFpC;AAEoC;AAFpC;EAAA,qFAEoC;EAFpC,oGAEoC;EAFpC;AAEoC;AAIpC;EAAA,mBAAkE;EAAlE,iBAAkE;EAAlE,sBAAkE;EAAlE,4DAAkE;EAAlE,kBAAkE;EAAlE,4DAAkE;EAAlE,sFAAkE;EAAlE,oGAAkE;EAAlE,uGAAkE;EAAlE,wBAAkE;EAAlE,0BAAkE;EAAlE;AAAkE;AAAlE;EAAA,qFAAkE;EAAlE,oGAAkE;EAAlE;AAAkE;AAAlE;EAAA;AAAkE;AAAlE;EAAA,0BAAkE;EAAlE,kBAAkE;EAAlE,kBAAkE;EAAlE;AAAkE;AAQlE;EAAA;AAAU;AAOZ,sBAAsB;AAEpB;EAAA,qBAE+C;EAF/C,kBAE+C;EAF/C,mBAE+C;EAF/C,mBAE+C;EAF/C,sBAE+C;EAF/C,gBAE+C;EAF/C,oBAE+C;EAF/C,iDAE+C;EAF/C,+FAE+C;EAF/C,wDAE+C;EAF/C;AAE+C;AAF/C;EAAA,kBAE+C;EAF/C,4DAE+C;EAF/C,oBAE+C;EAF/C;AAE+C;AAI/C;EAAA,qBAA+C;EAA/C,kBAA+C;EAA/C,mBAA+C;EAA/C,mBAA+C;EAA/C,sBAA+C;EAA/C,gBAA+C;EAA/C,oBAA+C;EAA/C,iDAA+C;EAA/C,+FAA+C;EAA/C,wDAA+C;EAA/C;AAA+C;AAA/C;EAAA,kBAA+C;EAA/C,4DAA+C;EAA/C,oBAA+C;EAA/C;AAA+C;AAA/C;EAAA,kBAA+C;EAA/C,4DAA+C;EAA/C,oBAA+C;EAA/C;AAA+C;AAGjD,gBAAgB;AAEd;EAAA,WAGkC;EAHlC,qBAGkC;EAHlC,iBAGkC;EAHlC,sBAGkC;EAHlC,4DAGkC;EAHlC,kBAGkC;EAHlC,mBAGkC;EAHlC,oBAGkC;EAHlC;AAGkC;AAHlC;EAAA,2BAGkC;EAHlC;AAGkC;AAHlC;EAAA,2BAGkC;EAHlC;AAGkC;AAHlC;EAAA,+FAGkC;EAHlC,wDAGkC;EAHlC;AAGkC;AAHlC;EAAA,sBAGkC;EAHlC,0DAGkC;EAHlC,2GAGkC;EAHlC,yGAGkC;EAHlC,4FAGkC;EAHlC,oBAGkC;EAHlC;AAGkC;AAIlC;EAAA,qBAA0D;EAA1D,cAA0D;EAA1D,mBAA0D;EAA1D,oBAA0D;EAA1D,gBAA0D;EAA1D,oBAA0D;EAA1D;AAA0D;AAO5D,qBAAqB;AAEnB;EAAA,oBAA0E;EAA1E,mBAA0E;EAA1E,qBAA0E;EAA1E,qBAA0E;EAA1E,sBAA0E;EAA1E,oBAA0E;EAA1E,uBAA0E;EAA1E,mBAA0E;EAA1E,oBAA0E;EAA1E;AAA0E;AAI1E;EAAA,oBAA4C;EAA5C,mBAA4C;EAA5C,qBAA4C;EAA5C,qBAA4C;EAA5C,sBAA4C;EAA5C,oBAA4C;EAA5C,uBAA4C;EAA5C,mBAA4C;EAA5C,oBAA4C;EAA5C,gBAA4C;EAA5C,kBAA4C;EAA5C,4DAA4C;EAA5C,oBAA4C;EAA5C;AAA4C;AAI5C;EAAA,oBAAgD;EAAhD,mBAAgD;EAAhD,qBAAgD;EAAhD,qBAAgD;EAAhD,sBAAgD;EAAhD,oBAAgD;EAAhD,uBAAgD;EAAhD,mBAAgD;EAAhD,oBAAgD;EAAhD,gBAAgD;EAAhD,kBAAgD;EAAhD,4DAAgD;EAAhD,oBAAgD;EAAhD;AAAgD;AAmBlD,sBAAsB;AAiBtB,mBAAmB;AAEjB;;EAAA;IAAA;EAAqF;AAAA;AAArF;EAAA,kCAAqF;EAArF,qBAAqF;EAArF,iBAAqF;EAArF,4DAAqF;EAArF,sBAAqF;EAArF;AAAqF;AAGvF,kCAAkC;AAEhC;EAAA,iBAA6C;EAA7C,kBAA6C;EAA7C,gBAA6C;EAA7C,kBAA6C;EAA7C;AAA6C;AAA7C;;EAAA;IAAA,oBAA6C;IAA7C;EAA6C;AAAA;AAA7C;;EAAA;IAAA,kBAA6C;IAA7C;EAA6C;AAAA;AAI7C;EAAA,iBAAqB;EAArB;AAAqB;AAArB;;EAAA;IAAA,iBAAqB;IAArB;EAAqB;AAAA;AAOvB,mBAAmB;AAEjB;EAAA,aAA2D;EAA3D,gDAA2D;EAA3D;AAA2D;AAA3D;;EAAA;IAAA;EAA2D;AAAA;AAA3D;;EAAA;IAAA;EAA2D;AAAA;AAG7D,sBAAsB;AAqBtB,oCAAoC;AAsBpC,4CAA4C;AAC5C;IACE;MACE,mCAAmC;IACrC;IACA;MACE,wCAAwC;IAC1C;EACF;AAEA,wBAAwB;AACxB;IACE;MACE,YAAY;MACZ,mBAAmB;IACrB;IACA;MACE,UAAU;MACV,qBAAqB;IACvB;EACF;AAEA,8BAA8B;AAC9B;IACE;MACE,sBAAsB;IACxB;IACA;MACE,qBAAqB;IACvB;IACA;MACE,sBAAsB;IACxB;EACF;AAEA,+DAA+D;AAC/D;IACE;;;uFAGmF;IACnF,sDAAsD;IACtD,iDAAiD;EACnD;AAEA;IACE;MACE,8CAA8C;IAChD;IACA;MACE,8CAA8C;IAChD;IACA;MACE,8CAA8C;IAChD;EACF;AAEA,4CAA4C;AAE1C;EAAA,qEAAqG;EAArG,4DAAqG;EAArG,oEAAqG;EAArG,mEAAqG;EAArG,oEAAqG;EAArG,4GAAqG;EAArG,wDAAqG;EAArG,6BAAqG;UAArG,qBAAqG;EAArG,kBAAqG;IACrG,0BAA0B;IAC1B;AAFqG;AAKvG;IACE;MACE,2BAA2B;IAC7B;IACA;MACE,6BAA6B;IAC/B;EACF;AA9TF;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB,oBAAmB;EAAnB,4BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,sDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA,wBAAmB;EAAnB,kEAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB,uBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kCAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,gEAAmB;EAAnB;AAAmB;AAAnB;EAAA,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,8FAAmB;EAAnB;AAAmB;AAAnB;EAAA,+EAAmB;EAAnB,mGAAmB;EAAnB;AAAmB;AAAnB;EAAA,6EAAmB;EAAnB,iGAAmB;EAAnB;AAAmB;AAAnB;EAAA,0CAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,sFAAmB;EAAnB,oGAAmB;EAAnB;AAAmB;AAAnB;EAAA,gFAAmB;EAAnB,oGAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gKAAmB;EAAnB,wJAAmB;EAAnB,iLAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,wBAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+FAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,4BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;;AAEnB,qCAAqC;;AA4CrC,yCAAyC;AAlDzC;EAAA,kBAmUC;EAnUD,kBAmUC;EAnUD;AAmUC;AAnUD;EAAA,sBAmUC;EAnUD;AAmUC;AAnUD;EAAA,sBAmUC;EAnUD;AAmUC;AAnUD;EAAA,sBAmUC;EAnUD;AAmUC;AAnUD;EAAA,sBAmUC;EAnUD;AAmUC;AAnUD;EAAA,sBAmUC;EAnUD;AAmUC;AAnUD;EAAA,sBAmUC;EAnUD;AAmUC;AAnUD;EAAA,sBAmUC;EAnUD;AAmUC;AAnUD;EAAA,kBAmUC;EAnUD;AAmUC;AAnUD;EAAA,kBAmUC;EAnUD;AAmUC;AAnUD;EAAA,kBAmUC;EAnUD;AAmUC;AAnUD;EAAA,kBAmUC;EAnUD;AAmUC;AAnUD;EAAA,kBAmUC;EAnUD;AAmUC;AAnUD;EAAA,kBAmUC;EAnUD;AAmUC;AAnUD;EAAA,kBAmUC;EAnUD;AAmUC;AAnUD;EAAA,kBAmUC;EAnUD;AAmUC;AAnUD;EAAA,kBAmUC;EAnUD;AAmUC;AAnUD;EAAA,kBAmUC;EAnUD;AAmUC;AAnUD;EAAA,kBAmUC;EAnUD;AAmUC;AAnUD;EAAA,kBAmUC;EAnUD;AAmUC;AAnUD;EAAA,kBAmUC;EAnUD;AAmUC;AAnUD;EAAA,kBAmUC;EAnUD;AAmUC;AAnUD;EAAA,kBAmUC;EAnUD;AAmUC;AAnUD;EAAA,kBAmUC;EAnUD;AAmUC;AAnUD;EAAA,kBAmUC;EAnUD;AAmUC;AAnUD;EAAA,kBAmUC;EAnUD;AAmUC;AAnUD;EAAA,kBAmUC;EAnUD;AAmUC;AAnUD;EAAA,kBAmUC;EAnUD;AAmUC;AAnUD;EAAA,kBAmUC;EAnUD;AAmUC;AAnUD;EAAA,oBAmUC;EAnUD;AAmUC;AAnUD;EAAA,oBAmUC;EAnUD;AAmUC;AAnUD;EAAA,oBAmUC;EAnUD;AAmUC;AAnUD;EAAA,oBAmUC;EAnUD;AAmUC;AAnUD;EAAA,oBAmUC;EAnUD;AAmUC;AAnUD;EAAA,oBAmUC;EAnUD;AAmUC;AAnUD;EAAA,6EAmUC;EAnUD,iGAmUC;EAnUD;AAmUC;AAnUD;EAAA,gFAmUC;EAnUD,oGAmUC;EAnUD;AAmUC;AAnUD;EAAA,8BAmUC;EAnUD;AAmUC;AAnUD;EAAA,2GAmUC;EAnUD,yGAmUC;EAnUD;AAmUC;AAnUD;EAAA,oBAmUC;EAnUD;AAmUC;AAnUD;EAAA,oBAmUC;EAnUD;AAmUC;AAnUD;EAAA,oBAmUC;EAnUD;AAmUC;AAnUD;EAAA,oBAmUC;EAnUD;AAmUC;AAnUD;EAAA,oBAmUC;EAnUD;AAmUC;AAnUD;EAAA,oBAmUC;EAnUD;AAmUC;AAnUD;EAAA,oBAmUC;EAnUD;AAmUC;AAnUD;EAAA,oBAmUC;EAnUD;AAmUC;AAnUD;EAAA,oBAmUC;EAnUD;AAmUC;AAnUD;EAAA,oBAmUC;EAnUD;AAmUC;AAnUD;EAAA,oBAmUC;EAnUD;AAmUC;AAnUD;EAAA;AAmUC;AAnUD;EAAA,yBAmUC;EAnUD;AAmUC;AAnUD;EAAA,kBAmUC;EAnUD,kBAmUC;EAnUD;AAmUC;AAnUD;EAAA,oBAmUC;EAnUD;AAmUC;AAnUD;EAAA,oBAmUC;EAnUD;AAmUC;AAnUD;EAAA,oBAmUC;EAnUD;AAmUC;AAnUD;EAAA,qFAmUC;EAnUD,oGAmUC;EAnUD;AAmUC;AAnUD;;EAAA;IAAA,gBAmUC;IAnUD;EAmUC;;EAnUD;IAAA;EAmUC;;EAnUD;IAAA;EAmUC;;EAnUD;IAAA;EAmUC;;EAnUD;IAAA;EAmUC;;EAnUD;IAAA;EAmUC;;EAnUD;IAAA;EAmUC;;EAnUD;IAAA;EAmUC;;EAnUD;IAAA;EAmUC;;EAnUD;IAAA;EAmUC;;EAnUD;IAAA,oBAmUC;IAnUD;EAmUC;;EAnUD;IAAA;EAmUC;;EAnUD;IAAA;EAmUC;AAAA;AAnUD;;EAAA;IAAA;EAmUC;;EAnUD;IAAA;EAmUC;;EAnUD;IAAA;EAmUC;;EAnUD;IAAA;EAmUC;;EAnUD;IAAA;EAmUC;AAAA;AAnUD;;EAAA;IAAA;EAmUC;;EAnUD;IAAA;EAmUC;;EAnUD;IAAA;EAmUC;;EAnUD;IAAA;EAmUC;;EAnUD;IAAA;EAmUC;;EAnUD;IAAA;EAmUC;;EAnUD;IAAA;EAmUC;;EAnUD;IAAA;EAmUC;;EAnUD;IAAA,kBAmUC;IAnUD;EAmUC;AAAA\",\"sourcesContent\":[\"@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');\\n\\n@tailwind base;\\n@tailwind components;\\n@tailwind utilities;\\n\\n/* Candid Connections Global Styles */\\n@layer base {\\n  html {\\n    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\\n    scroll-behavior: smooth;\\n    -webkit-font-smoothing: antialiased;\\n    -moz-osx-font-smoothing: grayscale;\\n  }\\n\\n  body {\\n    @apply bg-candid-gray-50 text-candid-navy-800 leading-relaxed;\\n    margin: 0;\\n    padding: 0;\\n  }\\n\\n  /* Consistent focus styles with brand colors */\\n  *:focus {\\n    @apply outline-2 outline-primary-500 outline-offset-2;\\n    outline-style: solid;\\n  }\\n\\n  /* Headings with brand typography */\\n  h1, h2, h3, h4, h5, h6 {\\n    @apply text-candid-navy-900 font-semibold leading-tight;\\n  }\\n\\n  h1 { @apply text-4xl lg:text-5xl; }\\n  h2 { @apply text-3xl lg:text-4xl; }\\n  h3 { @apply text-2xl lg:text-3xl; }\\n  h4 { @apply text-xl lg:text-2xl; }\\n  h5 { @apply text-lg lg:text-xl; }\\n  h6 { @apply text-base lg:text-lg; }\\n\\n  /* Links with brand styling */\\n  a {\\n    @apply text-primary-600 hover:text-primary-700 transition-colors duration-200;\\n  }\\n\\n  /* Form elements */\\n  input, textarea, select {\\n    @apply border-candid-gray-300 focus:border-primary-500 focus:ring-primary-500;\\n  }\\n}\\n\\n/* Candid Connections Component Library */\\n@layer components {\\n  /* Primary Button - Main brand blue */\\n  .btn-primary {\\n    @apply bg-primary-600 hover:bg-primary-700 active:bg-primary-800\\n           text-white font-medium py-3 px-6 rounded-lg\\n           transition-all duration-200 ease-in-out\\n           shadow-soft hover:shadow-medium\\n           focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;\\n  }\\n\\n  /* Secondary Button - Professional navy */\\n  .btn-secondary {\\n    @apply bg-white hover:bg-candid-gray-50 active:bg-candid-gray-100\\n           text-secondary-700 font-medium py-3 px-6\\n           border border-candid-gray-300 hover:border-candid-gray-400 rounded-lg\\n           transition-all duration-200 ease-in-out\\n           shadow-soft hover:shadow-medium\\n           focus:outline-none focus:ring-2 focus:ring-secondary-500 focus:ring-offset-2;\\n  }\\n\\n  /* Accent Button - Orange highlight */\\n  .btn-accent {\\n    @apply bg-accent-500 hover:bg-accent-600 active:bg-accent-700\\n           text-white font-medium py-3 px-6 rounded-lg\\n           transition-all duration-200 ease-in-out\\n           shadow-soft hover:shadow-medium\\n           focus:outline-none focus:ring-2 focus:ring-accent-500 focus:ring-offset-2;\\n  }\\n\\n  /* Outline Button */\\n  .btn-outline {\\n    @apply bg-transparent hover:bg-primary-50 active:bg-primary-100\\n           text-primary-600 hover:text-primary-700 font-medium py-3 px-6\\n           border-2 border-primary-600 hover:border-primary-700 rounded-lg\\n           transition-all duration-200 ease-in-out\\n           focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;\\n  }\\n\\n  /* Card Components */\\n  .card {\\n    @apply bg-white rounded-xl shadow-soft hover:shadow-medium\\n           transition-all duration-300 ease-in-out\\n           border border-candid-gray-200;\\n  }\\n\\n  .card-interactive {\\n    @apply card hover:scale-[1.02] hover:-translate-y-1 cursor-pointer;\\n  }\\n\\n  .card-header {\\n    @apply p-6 border-b border-candid-gray-200;\\n  }\\n\\n  .card-body {\\n    @apply p-6;\\n  }\\n\\n  .card-footer {\\n    @apply p-6 border-t border-candid-gray-200 bg-candid-gray-50 rounded-b-xl;\\n  }\\n\\n  /* Navigation Styles */\\n  .nav-link {\\n    @apply text-candid-navy-600 hover:text-primary-600\\n           font-medium transition-colors duration-200\\n           px-4 py-2 rounded-lg hover:bg-primary-50;\\n  }\\n\\n  .nav-link-active {\\n    @apply nav-link text-primary-700 bg-primary-100;\\n  }\\n\\n  /* Form Styles */\\n  .form-input {\\n    @apply w-full px-4 py-3 border border-candid-gray-300 rounded-lg\\n           focus:border-primary-500 focus:ring-1 focus:ring-primary-500\\n           transition-colors duration-200\\n           placeholder-candid-gray-400;\\n  }\\n\\n  .form-label {\\n    @apply block text-sm font-medium text-candid-navy-700 mb-2;\\n  }\\n\\n  .form-error {\\n    @apply text-red-600 text-sm mt-1;\\n  }\\n\\n  /* Badge Components */\\n  .badge {\\n    @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium;\\n  }\\n\\n  .badge-primary {\\n    @apply badge bg-primary-100 text-primary-800;\\n  }\\n\\n  .badge-secondary {\\n    @apply badge bg-secondary-100 text-secondary-800;\\n  }\\n\\n  .badge-accent {\\n    @apply badge bg-accent-100 text-accent-800;\\n  }\\n\\n  .badge-success {\\n    @apply badge bg-green-100 text-green-800;\\n  }\\n\\n  .badge-warning {\\n    @apply badge bg-yellow-100 text-yellow-800;\\n  }\\n\\n  .badge-error {\\n    @apply badge bg-red-100 text-red-800;\\n  }\\n\\n  /* Status Indicators */\\n  .status-active {\\n    @apply badge-success;\\n  }\\n\\n  .status-pending {\\n    @apply badge-warning;\\n  }\\n\\n  .status-inactive {\\n    @apply badge-secondary;\\n  }\\n\\n  .status-error {\\n    @apply badge-error;\\n  }\\n\\n  /* Loading States */\\n  .loading-spinner {\\n    @apply animate-spin rounded-full border-2 border-candid-gray-300 border-t-primary-600;\\n  }\\n\\n  /* Professional Layout Utilities */\\n  .container-app {\\n    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;\\n  }\\n\\n  .section-padding {\\n    @apply py-12 lg:py-16;\\n  }\\n\\n  .text-gradient {\\n    @apply bg-gradient-to-r from-primary-600 to-accent-500 bg-clip-text text-transparent;\\n  }\\n\\n  /* Dashboard Grid */\\n  .dashboard-grid {\\n    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;\\n  }\\n\\n  /* Responsive Tables */\\n  .table-responsive {\\n    @apply overflow-x-auto shadow-soft rounded-lg;\\n  }\\n\\n  .table {\\n    @apply min-w-full divide-y divide-candid-gray-200;\\n  }\\n\\n  .table-header {\\n    @apply bg-candid-gray-50;\\n  }\\n\\n  .table-cell {\\n    @apply px-6 py-4 whitespace-nowrap text-sm;\\n  }\\n\\n  .table-header-cell {\\n    @apply table-cell font-medium text-candid-navy-900 uppercase tracking-wider;\\n  }\\n\\n  /* Cosmic/Network Theme Animations */\\n  .cosmic-glow {\\n    animation: cosmic-pulse 3s ease-in-out infinite;\\n  }\\n\\n  .network-node {\\n    @apply rounded-full transition-all duration-300;\\n    animation: float 6s ease-in-out infinite;\\n  }\\n\\n  .network-node:nth-child(2) {\\n    animation-delay: -2s;\\n  }\\n\\n  .network-node:nth-child(3) {\\n    animation-delay: -4s;\\n  }\\n\\n  .connection-line {\\n    animation: connection-flow 4s linear infinite;\\n  }\\n\\n  /* Floating animation for network elements */\\n  @keyframes float {\\n    0%, 100% {\\n      transform: translateY(0px) scale(1);\\n    }\\n    50% {\\n      transform: translateY(-10px) scale(1.05);\\n    }\\n  }\\n\\n  /* Cosmic pulse effect */\\n  @keyframes cosmic-pulse {\\n    0%, 100% {\\n      opacity: 0.6;\\n      transform: scale(1);\\n    }\\n    50% {\\n      opacity: 1;\\n      transform: scale(1.1);\\n    }\\n  }\\n\\n  /* Connection flow animation */\\n  @keyframes connection-flow {\\n    0% {\\n      stroke-dasharray: 0 10;\\n    }\\n    50% {\\n      stroke-dasharray: 5 5;\\n    }\\n    100% {\\n      stroke-dasharray: 10 0;\\n    }\\n  }\\n\\n  /* Network connection background - subtle and non-distracting */\\n  .network-background {\\n    background:\\n      radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),\\n      radial-gradient(circle at 80% 80%, rgba(0, 212, 255, 0.08) 0%, transparent 50%),\\n      radial-gradient(circle at 40% 60%, rgba(255, 255, 255, 0.05) 0%, transparent 30%);\\n    background-size: 800px 800px, 600px 600px, 400px 400px;\\n    animation: network-drift 30s ease-in-out infinite;\\n  }\\n\\n  @keyframes network-drift {\\n    0%, 100% {\\n      background-position: 0% 0%, 100% 100%, 50% 50%;\\n    }\\n    33% {\\n      background-position: 30% 20%, 70% 80%, 60% 40%;\\n    }\\n    66% {\\n      background-position: 70% 30%, 30% 70%, 40% 60%;\\n    }\\n  }\\n\\n  /* Enhanced gradient text for cosmic theme */\\n  .cosmic-gradient {\\n    @apply bg-gradient-to-r from-primary-400 via-primary-500 to-primary-600 bg-clip-text text-transparent;\\n    background-size: 200% 200%;\\n    animation: gradient-shift 3s ease infinite;\\n  }\\n\\n  @keyframes gradient-shift {\\n    0%, 100% {\\n      background-position: 0% 50%;\\n    }\\n    50% {\\n      background-position: 100% 50%;\\n    }\\n  }\\n}\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./styles/globals.css\n"));

/***/ })

});