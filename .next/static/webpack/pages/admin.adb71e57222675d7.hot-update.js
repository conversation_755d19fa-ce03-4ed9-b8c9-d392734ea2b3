"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/admin",{

/***/ "(pages-dir-browser)/./pages/admin.js":
/*!************************!*\
  !*** ./pages/admin.js ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Admin)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"(pages-dir-browser)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/Layout */ \"(pages-dir-browser)/./components/Layout.js\");\n\nvar _s = $RefreshSig$();\n\n\n\nfunction Admin() {\n    _s();\n    const [seeding, setSeeding] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleSeedDatabase = async ()=>{\n        setSeeding(true);\n        setResult(null);\n        setError(null);\n        try {\n            const response = await fetch('/api/seed-database', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            const data = await response.json();\n            if (response.ok) {\n                setResult(data);\n            } else {\n                setError(data.error || 'Failed to seed database');\n            }\n        } catch (err) {\n            setError('Network error: ' + err.message);\n        } finally{\n            setSeeding(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Admin Panel | Candid Connections Katra\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Administrative tools for managing the Candid Connections platform.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-bold text-secondary-800 mb-4\",\n                                children: \"Admin Panel\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                lineNumber: 47,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-candid-gray-600 max-w-3xl mx-auto\",\n                                children: \"Administrative tools for managing the platform and database.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                lineNumber: 50,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-body\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-semibold text-secondary-800 mb-4\",\n                                    children: \"Database Management\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-yellow-800 mb-2\",\n                                            children: \"⚠️ Warning\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                            lineNumber: 63,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-yellow-700 text-sm\",\n                                            children: \"This will completely wipe the existing database and populate it with fresh mock data. This action cannot be undone.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-secondary-700\",\n                                            children: \"Seed Database with Mock Data\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                            lineNumber: 71,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-candid-gray-600\",\n                                            children: \"This will create a comprehensive dataset for testing the hiring authority matching system:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                            lineNumber: 74,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"list-disc list-inside text-candid-gray-600 space-y-1 ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"5 Companies\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 79,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" - Different sizes (Startup, Mid-size, Enterprise)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                    lineNumber: 79,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"8 Hiring Authorities\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 80,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" - Various levels (C-Suite, Executive, Director, Manager)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"20 Job Seekers\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 81,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" - Diverse skills and experience levels\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"20 Skills\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 82,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" - Technical and soft skills with demand levels\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleSeedDatabase,\n                                            disabled: seeding,\n                                            className: \"btn-primary \".concat(seeding ? 'opacity-50 cursor-not-allowed' : ''),\n                                            children: seeding ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"loading-spinner w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                        lineNumber: 92,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Seeding Database...\"\n                                                ]\n                                            }, void 0, true) : '🌱 Seed Database'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                            lineNumber: 85,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, this),\n                                result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 bg-green-50 border border-green-200 rounded-lg p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-green-800 mb-2\",\n                                            children: \"✅ Success!\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                            lineNumber: 104,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-green-700 mb-3\",\n                                            children: result.message\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                            lineNumber: 105,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 md:grid-cols-5 gap-4 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-green-600\",\n                                                            children: result.stats.companies\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 108,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-green-700\",\n                                                            children: \"Companies\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 109,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-green-600\",\n                                                            children: result.stats.hiringAuthorities\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 112,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-green-700\",\n                                                            children: \"Authorities\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 113,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-green-600\",\n                                                            children: result.stats.jobSeekers\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 116,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-green-700\",\n                                                            children: \"Job Seekers\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 117,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-green-600\",\n                                                            children: result.stats.skills\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 120,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-green-700\",\n                                                            children: \"Skills\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 121,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-green-600\",\n                                                            children: result.stats.matches\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 124,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-green-700\",\n                                                            children: \"Matches\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 125,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                            lineNumber: 106,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                    lineNumber: 103,\n                                    columnNumber: 15\n                                }, this),\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 bg-red-50 border border-red-200 rounded-lg p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-red-800 mb-2\",\n                                            children: \"❌ Error\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                            lineNumber: 133,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-700\",\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                            lineNumber: 134,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                    lineNumber: 132,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-body\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-semibold text-secondary-800 mb-4\",\n                                    children: \"Mock Data Overview\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-secondary-700 mb-3\",\n                                                    children: \"Company Size Logic\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Startup (<100):\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                                    lineNumber: 152,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-candid-gray-600\",\n                                                                    children: \"CEO, CTO → Ultimate Power\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                                    lineNumber: 153,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Mid-size (100-1000):\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                                    lineNumber: 156,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-candid-gray-600\",\n                                                                    children: \"VP, Directors → High Power\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                                    lineNumber: 157,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Enterprise (1000+):\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                                    lineNumber: 160,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-candid-gray-600\",\n                                                                    children: \"HR, Managers → Medium Power\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                                    lineNumber: 161,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 159,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                            lineNumber: 148,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-secondary-700 mb-3\",\n                                                    children: \"Authority Hierarchy\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"C-Suite:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                                    lineNumber: 170,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-candid-gray-600\",\n                                                                    children: \"CEO, CTO, Founder\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                                    lineNumber: 171,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 169,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Executive:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                                    lineNumber: 174,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-candid-gray-600\",\n                                                                    children: \"VP, Creative Director\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                                    lineNumber: 175,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Director:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                                    lineNumber: 178,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-candid-gray-600\",\n                                                                    children: \"HR Director, Product Director\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                                    lineNumber: 179,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 177,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Manager:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                                    lineNumber: 182,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-candid-gray-600\",\n                                                                    children: \"Engineering Manager\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                                    lineNumber: 183,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 181,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-body\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-semibold text-secondary-800 mb-4\",\n                                    children: \"Quick Links\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                    lineNumber: 194,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/job-seekers\",\n                                            className: \"btn-outline text-center\",\n                                            children: \"\\uD83D\\uDC65 Job Seekers\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/hiring-authorities\",\n                                            className: \"btn-outline text-center\",\n                                            children: \"\\uD83D\\uDC54 Hiring Authorities\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                            lineNumber: 201,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/companies\",\n                                            className: \"btn-outline text-center\",\n                                            children: \"\\uD83C\\uDFE2 Companies\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                            lineNumber: 204,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/matches\",\n                                            className: \"btn-outline text-center\",\n                                            children: \"\\uD83C\\uDFAF Authority Matches\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                    lineNumber: 197,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n_s(Admin, \"Xy/wAbx0eQHIrUNPE0NNrGaUI1k=\");\n_c = Admin;\nvar _c;\n$RefreshReg$(_c, \"Admin\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/admin.js\n"));

/***/ })

});