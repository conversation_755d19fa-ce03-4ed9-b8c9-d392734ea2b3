"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/admin",{

/***/ "(pages-dir-browser)/./pages/admin.js":
/*!************************!*\
  !*** ./pages/admin.js ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Admin)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"(pages-dir-browser)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/Layout */ \"(pages-dir-browser)/./components/Layout.js\");\n\nvar _s = $RefreshSig$();\n\n\n\nfunction Admin() {\n    _s();\n    const [seeding, setSeeding] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleSeedDatabase = async ()=>{\n        setSeeding(true);\n        setResult(null);\n        setError(null);\n        try {\n            const response = await fetch('/api/seed-database', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            const data = await response.json();\n            if (response.ok) {\n                setResult(data);\n            } else {\n                setError(data.error || 'Failed to seed database');\n            }\n        } catch (err) {\n            setError('Network error: ' + err.message);\n        } finally{\n            setSeeding(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Admin Panel | Candid Connections Katra\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Administrative tools for managing the Candid Connections platform.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-bold text-secondary-800 mb-4\",\n                                children: \"Admin Panel\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                lineNumber: 47,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-candid-gray-600 max-w-3xl mx-auto\",\n                                children: \"Administrative tools for managing the platform and database.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                lineNumber: 50,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-body\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-semibold text-secondary-800 mb-4\",\n                                    children: \"Database Management\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-yellow-800 mb-2\",\n                                            children: \"⚠️ Warning\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                            lineNumber: 63,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-yellow-700 text-sm\",\n                                            children: \"This will completely wipe the existing database and populate it with fresh mock data. This action cannot be undone.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-secondary-700\",\n                                            children: \"Seed Database with Mock Data\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                            lineNumber: 71,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-candid-gray-600\",\n                                            children: \"This will create a comprehensive dataset for testing the hiring authority matching system:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                            lineNumber: 74,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"list-disc list-inside text-candid-gray-600 space-y-1 ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"5 Companies\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 79,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" - Different sizes (Startup, Mid-size, Enterprise)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                    lineNumber: 79,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"8 Hiring Authorities\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 80,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" - Various levels (C-Suite, Executive, Director, Manager)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"20 Job Seekers\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 81,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" - Diverse skills and experience levels\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"20 Skills\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 82,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" - Technical and soft skills with demand levels\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Authority Matches\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 83,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" - Generated using company size logic and skill alignment\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleSeedDatabase,\n                                            disabled: seeding,\n                                            className: \"btn-primary \".concat(seeding ? 'opacity-50 cursor-not-allowed' : ''),\n                                            children: seeding ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"loading-spinner w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                        lineNumber: 93,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Seeding Database...\"\n                                                ]\n                                            }, void 0, true) : '🌱 Seed Database'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                            lineNumber: 86,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, this),\n                                result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 bg-green-50 border border-green-200 rounded-lg p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-green-800 mb-2\",\n                                            children: \"✅ Success!\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                            lineNumber: 105,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-green-700 mb-3\",\n                                            children: result.message\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                            lineNumber: 106,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 md:grid-cols-5 gap-4 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-green-600\",\n                                                            children: result.stats.companies\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 109,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-green-700\",\n                                                            children: \"Companies\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 110,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-green-600\",\n                                                            children: result.stats.hiringAuthorities\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 113,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-green-700\",\n                                                            children: \"Authorities\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 114,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-green-600\",\n                                                            children: result.stats.jobSeekers\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 117,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-green-700\",\n                                                            children: \"Job Seekers\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 118,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-green-600\",\n                                                            children: result.stats.skills\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 121,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-green-700\",\n                                                            children: \"Skills\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 122,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-green-600\",\n                                                            children: result.stats.matches\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 125,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-green-700\",\n                                                            children: \"Matches\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 126,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                            lineNumber: 107,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this),\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 bg-red-50 border border-red-200 rounded-lg p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-red-800 mb-2\",\n                                            children: \"❌ Error\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                            lineNumber: 134,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-700\",\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                            lineNumber: 135,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                    lineNumber: 133,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-body\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-semibold text-secondary-800 mb-4\",\n                                    children: \"Mock Data Overview\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-secondary-700 mb-3\",\n                                                    children: \"Company Size Logic\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Startup (<100):\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                                    lineNumber: 153,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-candid-gray-600\",\n                                                                    children: \"CEO, CTO → Ultimate Power\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                                    lineNumber: 154,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 152,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Mid-size (100-1000):\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                                    lineNumber: 157,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-candid-gray-600\",\n                                                                    children: \"VP, Directors → High Power\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                                    lineNumber: 158,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Enterprise (1000+):\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                                    lineNumber: 161,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-candid-gray-600\",\n                                                                    children: \"HR, Managers → Medium Power\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                                    lineNumber: 162,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-secondary-700 mb-3\",\n                                                    children: \"Authority Hierarchy\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"C-Suite:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                                    lineNumber: 171,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-candid-gray-600\",\n                                                                    children: \"CEO, CTO, Founder\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                                    lineNumber: 172,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Executive:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                                    lineNumber: 175,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-candid-gray-600\",\n                                                                    children: \"VP, Creative Director\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                                    lineNumber: 176,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 174,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Director:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                                    lineNumber: 179,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-candid-gray-600\",\n                                                                    children: \"HR Director, Product Director\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                                    lineNumber: 180,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Manager:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                                    lineNumber: 183,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-candid-gray-600\",\n                                                                    children: \"Engineering Manager\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                                    lineNumber: 184,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-body\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-semibold text-secondary-800 mb-4\",\n                                    children: \"Quick Links\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/job-seekers\",\n                                            className: \"btn-outline text-center\",\n                                            children: \"\\uD83D\\uDC65 Job Seekers\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                            lineNumber: 199,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/hiring-authorities\",\n                                            className: \"btn-outline text-center\",\n                                            children: \"\\uD83D\\uDC54 Hiring Authorities\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                            lineNumber: 202,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/companies\",\n                                            className: \"btn-outline text-center\",\n                                            children: \"\\uD83C\\uDFE2 Companies\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/matches\",\n                                            className: \"btn-outline text-center\",\n                                            children: \"\\uD83C\\uDFAF Authority Matches\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                            lineNumber: 208,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/admin.js\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n_s(Admin, \"Xy/wAbx0eQHIrUNPE0NNrGaUI1k=\");\n_c = Admin;\nvar _c;\n$RefreshReg$(_c, \"Admin\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/admin.js\n"));

/***/ })

});