"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./components/DashboardCards.js":
/*!**************************************!*\
  !*** ./components/DashboardCards.js ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardCards)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(pages-dir-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\nvar _s = $RefreshSig$();\n\n\nfunction DashboardCards() {\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"DashboardCards.useEffect\": ()=>{\n            // Simulate loading stats\n            setTimeout({\n                \"DashboardCards.useEffect\": ()=>{\n                    setStats({\n                        matches: 47,\n                        jobSeekers: 156,\n                        companies: 23,\n                        positions: 89,\n                        skills: 127,\n                        globalConnections: 342\n                    });\n                    setLoading(false);\n                }\n            }[\"DashboardCards.useEffect\"], 1000);\n        }\n    }[\"DashboardCards.useEffect\"], []);\n    const cards = [\n        {\n            title: 'Authority Matches',\n            icon: '🎯',\n            gradient: 'from-primary-500 to-primary-600',\n            path: '/matches',\n            description: 'Job seeker to hiring authority connections',\n            stat: stats.matches,\n            statLabel: 'Active Matches'\n        },\n        {\n            title: 'Job Seekers',\n            icon: '👥',\n            gradient: 'from-secondary-500 to-secondary-600',\n            path: '/job-seekers',\n            description: 'Candidates seeking the right hiring authority',\n            stat: stats.jobSeekers,\n            statLabel: 'Candidates'\n        },\n        {\n            title: 'Hiring Authorities',\n            icon: '👔',\n            gradient: 'from-accent-500 to-accent-600',\n            path: '/hiring-authorities',\n            description: 'Decision makers across company hierarchies',\n            stat: stats.hiringAuthorities || 67,\n            statLabel: 'Authorities'\n        },\n        {\n            title: 'Companies',\n            icon: '🏢',\n            gradient: 'from-candid-blue-500 to-candid-blue-600',\n            path: '/companies',\n            description: 'Organizational structures and hierarchies',\n            stat: stats.companies,\n            statLabel: 'Organizations'\n        },\n        {\n            title: 'Positions',\n            icon: '📋',\n            gradient: 'from-candid-orange-500 to-candid-orange-600',\n            path: '/positions',\n            description: 'Roles mapped to hiring authorities',\n            stat: stats.positions,\n            statLabel: 'Open Roles'\n        },\n        {\n            title: 'Network View',\n            icon: '🌐',\n            gradient: 'from-candid-navy-600 to-candid-navy-700',\n            path: '/global-view',\n            description: 'Graph visualization of all connections',\n            stat: stats.globalConnections,\n            statLabel: 'Connections'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"dashboard-grid\",\n        children: cards.map((card)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                href: card.path,\n                className: \"card-interactive group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card-body\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 bg-gradient-to-br \".concat(card.gradient, \" rounded-xl flex items-center justify-center shadow-soft group-hover:shadow-medium transition-all duration-200\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white text-xl\",\n                                        children: card.icon\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/DashboardCards.js\",\n                                        lineNumber: 92,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/DashboardCards.js\",\n                                    lineNumber: 91,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 loading-spinner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/DashboardCards.js\",\n                                        lineNumber: 98,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-candid-navy-900\",\n                                                children: card.stat\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/DashboardCards.js\",\n                                                lineNumber: 101,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-candid-gray-500 uppercase tracking-wide\",\n                                                children: card.statLabel\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/DashboardCards.js\",\n                                                lineNumber: 104,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/DashboardCards.js\",\n                                    lineNumber: 96,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/DashboardCards.js\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-candid-navy-900 mb-2 group-hover:text-primary-600 transition-colors duration-200\",\n                            children: card.title\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/DashboardCards.js\",\n                            lineNumber: 113,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-candid-gray-600 text-sm leading-relaxed\",\n                            children: card.description\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/DashboardCards.js\",\n                            lineNumber: 118,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 flex items-center text-primary-600 text-sm font-medium group-hover:text-primary-700 transition-colors duration-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Explore\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/DashboardCards.js\",\n                                    lineNumber: 124,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform duration-200\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M9 5l7 7-7 7\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/DashboardCards.js\",\n                                        lineNumber: 126,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/DashboardCards.js\",\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/DashboardCards.js\",\n                            lineNumber: 123,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/DashboardCards.js\",\n                    lineNumber: 88,\n                    columnNumber: 11\n                }, this)\n            }, card.title, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/DashboardCards.js\",\n                lineNumber: 83,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/DashboardCards.js\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardCards, \"cwqv8aGd9cdb2PhxKeskpKfPTJA=\");\n_c = DashboardCards;\nvar _c;\n$RefreshReg$(_c, \"DashboardCards\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/DashboardCards.js\n"));

/***/ })

});