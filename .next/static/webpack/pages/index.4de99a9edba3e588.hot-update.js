"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./pages/index.js":
/*!************************!*\
  !*** ./pages/index.js ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"(pages-dir-browser)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/Layout */ \"(pages-dir-browser)/./components/Layout.js\");\n/* harmony import */ var _components_DashboardCards__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/DashboardCards */ \"(pages-dir-browser)/./components/DashboardCards.js\");\n\n\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Candid Connections Katra | Professional Talent Matching Platform\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Advanced graph-based talent matching platform connecting job seekers with opportunities through intelligent relationship mapping.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                        lineNumber: 10,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"keywords\",\n                        content: \"job matching, talent platform, career connections, hiring, recruitment\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative bg-gradient-to-br from-secondary-900 via-secondary-800 to-secondary-950 rounded-3xl p-12 mb-12 overflow-hidden network-background\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex items-center justify-center opacity-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-20 h-20 bg-primary-500 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-bold text-sm\",\n                                        children: \"COMPANY\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                        lineNumber: 21,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                    lineNumber: 20,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -top-16 -left-16 w-12 h-12 bg-accent-500 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white text-xs font-bold\",\n                                        children: \"CEO\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                        lineNumber: 26,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                    lineNumber: 25,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -top-16 left-16 w-12 h-12 bg-accent-400 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white text-xs font-bold\",\n                                        children: \"CTO\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                        lineNumber: 29,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                    lineNumber: 28,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-16 -left-16 w-12 h-12 bg-accent-600 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white text-xs font-bold\",\n                                        children: \"VP\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                        lineNumber: 32,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                    lineNumber: 31,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-16 left-16 w-12 h-12 bg-accent-300 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white text-xs font-bold\",\n                                        children: \"HR\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                        lineNumber: 35,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                    lineNumber: 34,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -top-32 left-0 w-10 h-10 bg-primary-300 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white text-xs\",\n                                        children: \"JS1\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                        lineNumber: 40,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                    lineNumber: 39,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-32 left-0 w-10 h-10 bg-primary-300 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white text-xs\",\n                                        children: \"JS2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                        lineNumber: 43,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                    lineNumber: 42,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-0 -left-32 w-10 h-10 bg-primary-300 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white text-xs\",\n                                        children: \"JS3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-0 left-32 w-10 h-10 bg-primary-300 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white text-xs\",\n                                        children: \"JS4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"absolute inset-0 w-full h-full\",\n                                    style: {\n                                        width: '200px',\n                                        height: '200px',\n                                        left: '-100px',\n                                        top: '-100px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                            x1: \"100\",\n                                            y1: \"100\",\n                                            x2: \"84\",\n                                            y2: \"84\",\n                                            stroke: \"#00d4ff\",\n                                            strokeWidth: \"2\",\n                                            opacity: \"0.6\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                            lineNumber: 54,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                            x1: \"100\",\n                                            y1: \"100\",\n                                            x2: \"116\",\n                                            y2: \"84\",\n                                            stroke: \"#00d4ff\",\n                                            strokeWidth: \"2\",\n                                            opacity: \"0.6\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                            lineNumber: 55,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                            x1: \"100\",\n                                            y1: \"100\",\n                                            x2: \"84\",\n                                            y2: \"116\",\n                                            stroke: \"#00d4ff\",\n                                            strokeWidth: \"2\",\n                                            opacity: \"0.6\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                            lineNumber: 56,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                            x1: \"100\",\n                                            y1: \"100\",\n                                            x2: \"116\",\n                                            y2: \"116\",\n                                            stroke: \"#00d4ff\",\n                                            strokeWidth: \"2\",\n                                            opacity: \"0.6\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                            x1: \"84\",\n                                            y1: \"84\",\n                                            x2: \"100\",\n                                            y2: \"68\",\n                                            stroke: \"#f97316\",\n                                            strokeWidth: \"1\",\n                                            opacity: \"0.4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                            x1: \"116\",\n                                            y1: \"84\",\n                                            x2: \"100\",\n                                            y2: \"68\",\n                                            stroke: \"#f97316\",\n                                            strokeWidth: \"1\",\n                                            opacity: \"0.4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                            x1: \"84\",\n                                            y1: \"116\",\n                                            x2: \"100\",\n                                            y2: \"132\",\n                                            stroke: \"#f97316\",\n                                            strokeWidth: \"1\",\n                                            opacity: \"0.4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                            lineNumber: 60,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                            x1: \"116\",\n                                            y1: \"116\",\n                                            x2: \"100\",\n                                            y2: \"132\",\n                                            stroke: \"#f97316\",\n                                            strokeWidth: \"1\",\n                                            opacity: \"0.4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                            lineNumber: 61,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl lg:text-6xl font-bold text-white mb-6\",\n                                children: [\n                                    \"Connect to the Right\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                        lineNumber: 68,\n                                        columnNumber: 33\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"cosmic-gradient\",\n                                        children: \"Hiring Authority\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                        lineNumber: 69,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-8\",\n                                children: [\n                                    \"Skip the job board grind. Our graph database maps your skills directly to the \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-primary-400 font-semibold\",\n                                        children: \"correct hiring authority\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                        lineNumber: 74,\n                                        columnNumber: 91\n                                    }, this),\n                                    \" based on company hierarchy, position requirements, and skill connections.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-white text-secondary-900 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-100 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105\",\n                                        children: \"Explore Network Connections\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                        lineNumber: 78,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"border-2 border-primary-400 text-primary-400 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-primary-400 hover:text-secondary-900 transition-all duration-300\",\n                                        children: \"View Hiring Authorities\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                        lineNumber: 81,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-4 gap-6 mb-12 max-w-4xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-3xl font-bold text-primary-500\",\n                                children: \"156\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                lineNumber: 91,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-candid-gray-500 uppercase tracking-wide\",\n                                children: \"Job Seekers\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-3xl font-bold text-secondary-600\",\n                                children: \"23\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-candid-gray-500 uppercase tracking-wide\",\n                                children: \"Companies\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-3xl font-bold text-accent-600\",\n                                children: \"89\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-candid-gray-500 uppercase tracking-wide\",\n                                children: \"Open Positions\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-3xl font-bold text-primary-600\",\n                                children: \"342\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-candid-gray-500 uppercase tracking-wide\",\n                                children: \"Connections\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-semibold text-candid-navy-900 mb-8 text-center\",\n                        children: \"Explore the Platform\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardCards__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-orange-50 to-orange-100 rounded-3xl p-12 mb-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-secondary-800 mb-4\",\n                                children: \"GIVE YOUR CLIENTS AND FRIENDS THE EDGE\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center items-center space-x-8 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-primary-500 rounded-full mb-2 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-white rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                                lineNumber: 127,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-20 bg-primary-600 rounded-t-lg\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                                lineNumber: 130,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-12 bg-secondary-800 rounded-b-lg\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                                lineNumber: 131,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                        lineNumber: 126,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 h-1 bg-gradient-to-r from-primary-500 to-accent-500 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                        lineNumber: 135,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-accent-500 rounded-full mb-2 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-white rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                                lineNumber: 139,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-20 bg-accent-600 rounded-t-lg\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                                lineNumber: 142,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-12 bg-secondary-800 rounded-b-lg\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                                lineNumber: 143,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                        lineNumber: 138,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 h-1 bg-gradient-to-r from-accent-500 to-primary-500 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                        lineNumber: 147,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-secondary-600 rounded-full mb-2 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-white rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                                lineNumber: 151,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-20 bg-secondary-700 rounded-t-lg\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                                lineNumber: 154,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-12 bg-secondary-800 rounded-b-lg\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                                lineNumber: 155,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                        lineNumber: 150,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-secondary-700 leading-relaxed mb-8\",\n                                children: \"No approval is necessary to become a referral partner—anyone can join! This is especially beneficial if you work with or know people in need of jobs but aren't able to assist them yourself. Let us help you make an impact and earn in the process. Simply register below as a Referral Partner. You will gain access to a referral portal where you can submit contact information for job seeking candidates. Once someone signs up through your referral, you'll earn a $50 referral fee. It's that simple—help others and get rewarded.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                lineNumber: 161,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"bg-secondary-800 text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-secondary-900 transition-all duration-300 shadow-lg hover:shadow-xl underline\",\n                                children: \"GET SIGNED UP NOW\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                lineNumber: 168,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card-body\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold text-candid-navy-900 mb-6 text-center\",\n                            children: \"Platform Features\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-soft\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-2xl\",\n                                                children: \"\\uD83C\\uDFAF\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                                lineNumber: 183,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-candid-navy-900 mb-2\",\n                                            children: \"Smart Matching\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-candid-gray-600 text-sm\",\n                                            children: \"AI-powered algorithms analyze skills, experience, and preferences to create optimal job matches.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                            lineNumber: 186,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-soft\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-2xl\",\n                                                children: \"\\uD83C\\uDF10\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                                lineNumber: 193,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-candid-navy-900 mb-2\",\n                                            children: \"Network Visualization\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-candid-gray-600 text-sm\",\n                                            children: \"Interactive 2D and 3D visualizations reveal hidden connections and opportunities in the job market.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                            lineNumber: 196,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                    lineNumber: 191,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-gradient-to-br from-accent-500 to-accent-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-soft\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-2xl\",\n                                                children: \"\\uD83D\\uDCCA\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                                lineNumber: 203,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                            lineNumber: 202,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-candid-navy-900 mb-2\",\n                                            children: \"Market Analytics\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-candid-gray-600 text-sm\",\n                                            children: \"Real-time insights into skill demand, salary trends, and market opportunities.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/pages/index.js\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/index.js\n"));

/***/ })

});