"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/skills",{

/***/ "(pages-dir-browser)/./components/Navigation.js":
/*!**********************************!*\
  !*** ./components/Navigation.js ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(pages-dir-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n\nvar _s = $RefreshSig$();\n\n\n\nfunction Navigation() {\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const navItems = [\n        {\n            name: 'Dashboard',\n            path: '/',\n            icon: '🏠'\n        },\n        {\n            name: 'Authority Matches',\n            path: '/matches',\n            icon: '🎯'\n        },\n        {\n            name: 'Job Seekers',\n            path: '/job-seekers',\n            icon: '👥'\n        },\n        {\n            name: 'Hiring Authorities',\n            path: '/hiring-authorities',\n            icon: '👔'\n        },\n        {\n            name: 'Companies',\n            path: '/companies',\n            icon: '🏢'\n        },\n        {\n            name: 'Positions',\n            path: '/positions',\n            icon: '📋'\n        },\n        {\n            name: 'Skills',\n            path: '/skills',\n            icon: '🛠️'\n        },\n        {\n            name: 'Network View',\n            path: '/global-view',\n            icon: '🌐'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-white shadow-soft border-b border-candid-gray-200\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-app\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0 flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/\",\n                                className: \"flex items-center space-x-3 group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 relative group-hover:scale-105 transition-transform duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            viewBox: \"0 0 48 48\",\n                                            className: \"w-full h-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"24\",\n                                                    cy: \"24\",\n                                                    r: \"22\",\n                                                    fill: \"none\",\n                                                    stroke: \"#1e3a8a\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 31,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"24\",\n                                                    cy: \"12\",\n                                                    r: \"3\",\n                                                    fill: \"#00d4ff\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 33,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"36\",\n                                                    cy: \"24\",\n                                                    r: \"3\",\n                                                    fill: \"#00d4ff\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 34,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"24\",\n                                                    cy: \"36\",\n                                                    r: \"3\",\n                                                    fill: \"#00d4ff\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 35,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"12\",\n                                                    cy: \"24\",\n                                                    r: \"3\",\n                                                    fill: \"#00d4ff\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 36,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"24\",\n                                                    cy: \"24\",\n                                                    r: \"4\",\n                                                    fill: \"#1e3a8a\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 37,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                    x1: \"24\",\n                                                    y1: \"15\",\n                                                    x2: \"24\",\n                                                    y2: \"20\",\n                                                    stroke: \"#00d4ff\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 39,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                    x1: \"33\",\n                                                    y1: \"24\",\n                                                    x2: \"28\",\n                                                    y2: \"24\",\n                                                    stroke: \"#00d4ff\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 40,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                    x1: \"24\",\n                                                    y1: \"33\",\n                                                    x2: \"24\",\n                                                    y2: \"28\",\n                                                    stroke: \"#00d4ff\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 41,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                    x1: \"15\",\n                                                    y1: \"24\",\n                                                    x2: \"20\",\n                                                    y2: \"24\",\n                                                    stroke: \"#00d4ff\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                    lineNumber: 42,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                            lineNumber: 29,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 28,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden sm:block\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold text-secondary-800 group-hover:text-primary-500 transition-colors duration-200\",\n                                                children: \"Candid Connections\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                lineNumber: 46,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-candid-gray-600 -mt-1\",\n                                                children: \"Katra Platform\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                lineNumber: 49,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 45,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                lineNumber: 26,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1\",\n                                children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: item.path,\n                                        className: \"flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 \".concat(router.pathname === item.path ? 'nav-link-active' : 'nav-link'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-base\",\n                                                children: item.icon\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                lineNumber: 67,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                                lineNumber: 68,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, item.path, true, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 58,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                lineNumber: 56,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/admin\",\n                                    className: \"btn-outline text-sm py-2 px-4\",\n                                    children: \"⚙️ Admin\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/global-view\",\n                                    className: \"btn-outline text-sm py-2 px-4\",\n                                    children: \"\\uD83C\\uDF10 Network View\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"https://portal.candid-connections.com/user/login\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"btn-primary text-sm py-2 px-4\",\n                                    children: \"Portal Login\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),\n                                className: \"p-2 rounded-lg text-candid-navy-600 hover:text-primary-600 hover:bg-primary-50 transition-colors duration-200\",\n                                \"aria-label\": \"Toggle mobile menu\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 107,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M4 6h16M4 12h16M4 18h16\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 109,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                    lineNumber: 105,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this),\n                isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"lg:hidden border-t border-candid-gray-200 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: item.path,\n                                    onClick: ()=>setIsMobileMenuOpen(false),\n                                    className: \"flex items-center space-x-3 px-4 py-3 rounded-lg text-base font-medium transition-all duration-200 \".concat(router.pathname === item.path ? 'nav-link-active' : 'nav-link'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg\",\n                                            children: item.icon\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                            lineNumber: 131,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                            lineNumber: 132,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, item.path, true, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                    lineNumber: 121,\n                                    columnNumber: 17\n                                }, this)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-4 space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/global-view\",\n                                        onClick: ()=>setIsMobileMenuOpen(false),\n                                        className: \"block w-full btn-outline text-center\",\n                                        children: \"\\uD83C\\uDF10 Network View\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 138,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"https://portal.candid-connections.com/user/login\",\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        className: \"block w-full btn-primary text-center\",\n                                        children: \"Portal Login\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                        lineNumber: 145,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                                lineNumber: 137,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                        lineNumber: 119,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n                    lineNumber: 118,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/workspace/candid-connections/components/Navigation.js\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n_s(Navigation, \"YbosHWVx+/a9NFMNnULRFD2hfDw=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = Navigation;\nvar _c;\n$RefreshReg$(_c, \"Navigation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/Navigation.js\n"));

/***/ })

});