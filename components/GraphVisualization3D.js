import { useEffect, useRef } from 'react'
import ForceGraph3D from '3d-force-graph'

export default function GraphVisualization3D({ data }) {
  const containerRef = useRef(null)
  
  useEffect(() => {
    if (!data || !containerRef.current) return
    
    // Clear previous visualization
    containerRef.current.innerHTML = ''
    
    // Initialize 3D force graph
    const graph = ForceGraph3D()
      .width(containerRef.current.clientWidth)
      .height(500)
      .backgroundColor('#ffffff')
      .nodeColor(node => {
        switch(node.type) {
          case 'jobSeeker': return '#3b82f6' // blue
          case 'company': return '#14b8a6' // teal
          case 'position': return '#10b981' // emerald
          case 'skill': return '#f59e0b' // amber
          default: return '#6366f1' // indigo
        }
      })
      .nodeLabel(node => node.name)
      .nodeVal(node => node.size || 5)
      .linkWidth(link => link.value || 1)
      .linkDirectionalParticles(2)
      .linkDirectionalParticleSpeed(0.005)
      .graphData(data)
      (containerRef.current)
    
    // Handle window resize
    const handleResize = () => {
      graph.width(containerRef.current.clientWidth)
    }
    
    window.addEventListener('resize', handleResize)
    
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [data])
  
  return (
    <div ref={containerRef} className="w-full h-[500px] border rounded-lg shadow-sm bg-white"></div>
  )
}