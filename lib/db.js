import { Database } from 'arangojs'

// Initialize the ArangoDB connection
const initDatabase = () => {
  const db = new Database({
    url: process.env.ARANGODB_URL || 'http://localhost:8529',
    databaseName: process.env.ARANGODB_DB_NAME || 'candid_connections',
    auth: {
      username: process.env.ARANGODB_USERNAME || 'root',
      password: process.env.ARANGODB_PASSWORD || '',
    },
  })

  return db
}

// Get collections
const getCollections = async (db) => {
  return {
    jobSeekers: db.collection('jobSeekers'),
    companies: db.collection('companies'),
    hiringAuthorities: db.collection('hiringAuthorities'),
    positions: db.collection('positions'),
    skills: db.collection('skills'),
    matches: db.collection('matches'),
    // Edge collections - using regular collection method for now
    works_for: db.collection('works_for'),
    employs: db.collection('employs'),
    posts: db.collection('posts'),
    requires: db.collection('requires'),
    has_skill: db.collection('has_skill'),
    matched_to: db.collection('matched_to'),
    reports_to: db.collection('reports_to'),
  }
}

// Initialize database and collections
export const initDb = async () => {
  const db = initDatabase()

  // Ensure database exists
  try {
    await db.createDatabase(process.env.ARANGODB_DB_NAME || 'candid_connections')
  } catch (error) {
    // Database might already exist, that's okay
    if (!error.message.includes('duplicate name')) {
      console.log('Database creation note:', error.message)
    }
  }

  const collections = await getCollections(db)
  return { db, collections }
}

export default initDb