{"name": "3d-force-graph", "version": "1.77.0", "description": "UI component for a 3D force-directed graph using ThreeJS and d3-force-3d layout engine", "type": "module", "jsdelivr": "dist/3d-force-graph.min.js", "unpkg": "dist/3d-force-graph.min.js", "main": "dist/3d-force-graph.mjs", "module": "dist/3d-force-graph.mjs", "types": "dist/3d-force-graph.d.ts", "exports": {"types": "./dist/3d-force-graph.d.ts", "umd": "./dist/3d-force-graph.min.js", "default": "./dist/3d-force-graph.mjs"}, "sideEffects": ["./src/*.css"], "repository": {"type": "git", "url": "git+https://github.com/vasturiano/3d-force-graph.git"}, "homepage": "https://github.com/vasturiano/3d-force-graph", "keywords": ["3d", "force", "graph", "three", "ngraph", "webgl"], "author": {"name": "Vasco <PERSON>turiano", "url": "https://github.com/vasturiano"}, "license": "MIT", "bugs": {"url": "https://github.com/vasturiano/3d-force-graph/issues"}, "scripts": {"build": "rimraf dist && rollup -c", "dev": "rollup -w -c rollup.config.dev.js", "prepare": "npm run build"}, "files": ["dist/**/*", "example/**/*"], "dependencies": {"accessor-fn": "1", "kapsule": "^1.16", "three": ">=0.118 <1", "three-forcegraph": "1", "three-render-objects": "^1.35"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/preset-env": "^7.26.9", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-terser": "^0.4.4", "postcss": "^8.5.3", "rimraf": "^6.0.1", "rollup": "^4.36.0", "rollup-plugin-dts": "^6.2.1", "rollup-plugin-postcss": "^4.0.2", "typescript": "^5.8.2"}, "engines": {"node": ">=12"}}