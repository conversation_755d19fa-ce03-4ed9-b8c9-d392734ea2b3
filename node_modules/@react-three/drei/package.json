{"name": "@react-three/drei", "version": "10.0.8", "private": false, "description": "useful add-ons for react-three-fiber", "keywords": ["react", "three", "threejs", "react-three-fiber"], "repository": {"type": "git", "url": "git+https://github.com/pmndrs/drei.git"}, "license": "MIT", "bugs": {"url": "https://github.com/pmndrs/drei/issues"}, "homepage": "https://github.com/pmndrs/drei", "maintainers": ["<PERSON> (https://github.com/drcmda)", "<PERSON><PERSON><PERSON><PERSON> (https://github.com/gsimone)", "<PERSON> (https://github.com/emmelleppi)", "<PERSON> (https://github.com/joshua<PERSON>s)"], "main": "index.cjs.js", "module": "index.js", "types": "index.d.ts", "react-native": "native/index.cjs.js", "sideEffects": false, "dependencies": {"@babel/runtime": "^7.26.0", "@mediapipe/tasks-vision": "0.10.17", "@monogrid/gainmap-js": "^3.0.6", "@use-gesture/react": "^10.3.1", "camera-controls": "^2.9.0", "cross-env": "^7.0.3", "detect-gpu": "^5.0.56", "glsl-noise": "^0.0.0", "hls.js": "^1.5.17", "maath": "^0.10.8", "meshline": "^3.3.1", "stats-gl": "^2.2.8", "stats.js": "^0.17.0", "suspend-react": "^0.1.3", "three-mesh-bvh": "^0.8.3", "three-stdlib": "^2.35.6", "troika-three-text": "^0.52.4", "tunnel-rat": "^0.1.2", "use-sync-external-store": "^1.4.0", "utility-types": "^3.11.0", "zustand": "^5.0.1"}, "peerDependencies": {"@react-three/fiber": "^9.0.0", "react": "^19", "react-dom": "^19", "three": ">=0.159"}, "peerDependenciesMeta": {"react-dom": {"optional": true}}, "packageManager": "yarn@4.3.1"}