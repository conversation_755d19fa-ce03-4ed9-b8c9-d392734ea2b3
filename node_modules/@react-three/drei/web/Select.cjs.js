"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("react"),n=require("three"),r=require("three-stdlib"),o=require("@react-three/fiber"),i=require("zustand/shallow");function l(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function s(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var r=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,r.get?r:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var c=l(e),u=s(t),a=s(n);const d=u.createContext([]);exports.Select=function({box:e,multiple:t,children:n,onChange:l,onChangePointerUp:s,border:f="1px solid #55aaff",backgroundColor:p="rgba(75, 160, 255, 0.1)",filter:h=e=>e,...v}){const[b,y]=u.useState(!1),{setEvents:m,camera:x,raycaster:E,gl:g,controls:w,size:j,get:C}=o.useThree(),[P,O]=u.useState(!1),[M,k]=u.useReducer(((e,{object:t,shift:n})=>void 0===t?[]:Array.isArray(t)?t:n?e.includes(t)?e.filter((e=>e!==t)):[t,...e]:e[0]===t?[]:[t]),[]);u.useEffect((()=>{b?null==l||l(M):null==s||s(M)}),[M,b]);const q=u.useCallback((e=>{e.stopPropagation(),k({object:h([e.object])[0],shift:t&&e.shiftKey})}),[]),L=u.useCallback((e=>!P&&k({})),[P]),S=u.useRef(null);return u.useEffect((()=>{if(!e||!t)return;const n=new r.SelectionBox(x,S.current),o=document.createElement("div");o.style.pointerEvents="none",o.style.border=f,o.style.backgroundColor=p,o.style.position="fixed";const l=new a.Vector2,s=new a.Vector2,c=new a.Vector2,u=C().events.enabled,d=null==w?void 0:w.enabled;let v=!1;function b(e,t){const{offsetX:n,offsetY:r}=e,{width:o,height:i}=j;t.set(n/o*2-1,-r/i*2+1)}function E(e){e.shiftKey&&(!function(e){var t;w&&(w.enabled=!1),m({enabled:!1}),y(v=!0),null==(t=g.domElement.parentElement)||t.appendChild(o),o.style.left=`${e.clientX}px`,o.style.top=`${e.clientY}px`,o.style.width="0px",o.style.height="0px",l.x=e.clientX,l.y=e.clientY}(e),b(e,n.startPoint))}let P=[];function O(e){if(v){!function(e){c.x=Math.max(l.x,e.clientX),c.y=Math.max(l.y,e.clientY),s.x=Math.min(l.x,e.clientX),s.y=Math.min(l.y,e.clientY),o.style.left=`${s.x}px`,o.style.top=`${s.y}px`,o.style.width=c.x-s.x+"px",o.style.height=c.y-s.y+"px"}(e),b(e,n.endPoint);const t=n.select().sort((e=>e.uuid)).filter((e=>e.isMesh));i.shallow(t,P)||(P=t,k({object:h(t)}))}}function M(e){var t;v&&v&&(w&&(w.enabled=d),m({enabled:u}),y(v=!1),null==(t=o.parentElement)||t.removeChild(o))}return document.addEventListener("pointerdown",E,{passive:!0}),document.addEventListener("pointermove",O,{passive:!0,capture:!0}),document.addEventListener("pointerup",M,{passive:!0}),()=>{document.removeEventListener("pointerdown",E),document.removeEventListener("pointermove",O,!0),document.removeEventListener("pointerup",M)}}),[j.width,j.height,E,x,w,g]),u.createElement("group",c.default({ref:S,onClick:q,onPointerOver:()=>O(!0),onPointerOut:()=>O(!1),onPointerMissed:L},v),u.createElement(d.Provider,{value:M},n))},exports.useSelect=function(){return u.useContext(d)};
