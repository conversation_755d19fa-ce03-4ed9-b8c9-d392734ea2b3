"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("react"),r=require("three"),n=require("@react-three/fiber"),c=require("tunnel-rat");function a(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function i(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var l=a(e),o=i(t),u=i(r),s=a(c);const f=new u.Color,d=s.default();function m(e,t){const{right:r,top:n,left:c,bottom:a,width:i,height:l}=t,o=t.bottom<0||n>e.height||r<0||t.left>e.width,u=e.top+e.height-a;return{position:{width:i,height:l,left:c-e.left,top:n,bottom:u,right:r},isOffscreen:o}}function g(e,{left:t,bottom:r,width:n,height:c}){let a;const i=n/c;var l;return(l=e.camera)&&l.isOrthographicCamera?e.camera.manual?e.camera.updateProjectionMatrix():e.camera.left===n/-2&&e.camera.right===n/2&&e.camera.top===c/2&&e.camera.bottom===c/-2||(Object.assign(e.camera,{left:n/-2,right:n/2,top:c/2,bottom:c/-2}),e.camera.updateProjectionMatrix()):e.camera.aspect!==i&&(e.camera.aspect=i,e.camera.updateProjectionMatrix()),a=e.gl.autoClear,e.gl.autoClear=!1,e.gl.setViewport(t,r,n,c),e.gl.setScissor(t,r,n,c),e.gl.setScissorTest(!0),a}function h(e,t){e.gl.setScissorTest(!1),e.gl.autoClear=t}function p(e){e.gl.getClearColor(f),e.gl.setClearColor(f,e.gl.getClearAlpha()),e.gl.clear(!0,!0)}function v({visible:e=!0,canvasSize:t,scene:r,index:c,children:a,frames:i,rect:l,track:u}){const s=n.useThree(),[f,d]=o.useState(!1);let v=0;return n.useFrame((n=>{var c;(i===1/0||v<=i)&&(u&&(l.current=null==(c=u.current)?void 0:c.getBoundingClientRect()),v++);if(l.current){const{position:c,isOffscreen:i}=m(t,l.current);if(f!==i&&d(i),e&&!f&&l.current){const e=g(n,c);n.gl.render(a?n.scene:r,n.camera),h(n,e)}}}),c),o.useLayoutEffect((()=>{const r=l.current;if(r&&(!e||!f)){const{position:e}=m(t,r),n=g(s,e);p(s),h(s,n)}}),[e,f]),o.useEffect((()=>{if(!u)return;const e=l.current,r=s.get().events.connected;return s.setEvents({connected:u.current}),()=>{if(e){const{position:r}=m(t,e),n=g(s,r);p(s),h(s,n)}s.setEvents({connected:r})}}),[u]),o.createElement(o.Fragment,null,a,o.createElement("group",{onPointerOver:()=>null}))}const b=o.forwardRef((({track:e,visible:t=!0,index:r=1,id:c,style:a,className:i,frames:s=1/0,children:f,...d},m)=>{var g,h,p,b;const E=o.useRef(null),{size:w,scene:C}=n.useThree(),[x]=o.useState((()=>new u.Scene)),[O,y]=o.useReducer((()=>!0),!1),j=o.useCallback(((t,r)=>{if(E.current&&e&&e.current&&t.target===e.current){const{width:e,height:n,left:c,top:a}=E.current,i=t.clientX-c,l=t.clientY-a;r.pointer.set(i/e*2-1,-l/n*2+1),r.raycaster.setFromCamera(r.pointer,r.camera)}}),[E,e]);return o.useEffect((()=>{var t;e&&(E.current=null==(t=e.current)?void 0:t.getBoundingClientRect()),y()}),[e]),o.createElement("group",l.default({ref:m},d),O&&n.createPortal(o.createElement(v,{visible:t,canvasSize:w,frames:s,scene:C,track:e,rect:E,index:r},f),x,{events:{compute:j,priority:r},size:{width:null==(g=E.current)?void 0:g.width,height:null==(h=E.current)?void 0:h.height,top:null==(p=E.current)?void 0:p.top,left:null==(b=E.current)?void 0:b.left}}))})),E=o.forwardRef((({as:e="div",id:t,visible:r,className:n,style:c,index:a=1,track:i,frames:u=1/0,children:s,...f},m)=>{const g=o.useId(),h=o.useRef(null);return o.useImperativeHandle(m,(()=>h.current)),o.createElement(o.Fragment,null,o.createElement(e,l.default({ref:h,id:t,className:n,style:c},f)),o.createElement(d.In,null,o.createElement(b,{visible:r,key:g,track:h,frames:u,index:a},s)))})),w=(()=>{const e=o.forwardRef(((e,t)=>o.useContext(n.context)?o.createElement(b,l.default({ref:t},e)):o.createElement(E,l.default({ref:t},e))));return e.Port=()=>o.createElement(d.Out,null),e})();exports.View=w;
