"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("./Html.cjs.js"),r=require("./CycleRaycast.cjs.js"),s=require("./useCursor.cjs.js"),o=require("./Loader.cjs.js"),t=require("./ScrollControls.cjs.js"),i=require("./PresentationControls.cjs.js"),a=require("./KeyboardControls.cjs.js"),c=require("./Select.cjs.js"),u=require("./View.cjs.js"),n=require("./pivotControls/index.cjs.js"),p=require("./ScreenVideoTexture.cjs.js"),j=require("./WebcamVideoTexture.cjs.js"),l=require("./FaceControls.cjs.js"),x=require("./DragControls.cjs.js"),d=require("./FaceLandmarker.cjs.js"),q=require("./Facemesh.cjs.js"),m=require("../core/Billboard.cjs.js"),C=require("../core/ScreenSpace.cjs.js"),h=require("../core/ScreenSizer.cjs.js"),S=require("../core/QuadraticBezierLine.cjs.js"),M=require("../core/CubicBezierLine.cjs.js"),T=require("../core/CatmullRomLine.cjs.js"),b=require("../core/Line.cjs.js"),P=require("../core/PositionalAudio.cjs.js"),f=require("../core/Text.cjs.js"),g=require("../core/Text3D.cjs.js"),v=require("../core/Effects.cjs.js"),F=require("../core/GradientTexture.cjs.js"),B=require("../core/Image.cjs.js"),A=require("../core/Edges.cjs.js"),L=require("../core/Outlines.cjs.js"),D=require("../core/Trail.cjs.js"),k=require("../core/Sampler.cjs.js"),E=require("../core/ComputedAttribute.cjs.js"),y=require("../core/Clone.cjs.js"),G=require("../core/MarchingCubes.cjs.js"),R=require("../core/Decal.cjs.js"),w=require("../core/Svg.cjs.js"),z=require("../core/Gltf.cjs.js"),I=require("../core/AsciiRenderer.cjs.js"),O=require("../core/Splat.cjs.js"),V=require("../core/OrthographicCamera.cjs.js"),H=require("../core/PerspectiveCamera.cjs.js"),K=require("../core/CubeCamera.cjs.js"),W=require("../core/DeviceOrientationControls.cjs.js"),Q=require("../core/FlyControls.cjs.js"),N=require("../core/MapControls.cjs.js"),U=require("../core/OrbitControls.cjs.js"),X=require("../core/TrackballControls.cjs.js"),_=require("../core/ArcballControls.cjs.js"),J=require("../core/TransformControls.cjs.js"),Y=require("../core/PointerLockControls.cjs.js"),Z=require("../core/FirstPersonControls.cjs.js"),$=require("../core/CameraControls.cjs.js"),ee=require("camera-controls"),re=require("../core/MotionPathControls.cjs.js"),se=require("../core/GizmoHelper.cjs.js"),oe=require("../core/GizmoViewcube.cjs.js"),te=require("../core/GizmoViewport.cjs.js"),ie=require("../core/Grid.cjs.js"),ae=require("../core/CubeTexture.cjs.js"),ce=require("../core/Fbx.cjs.js"),ue=require("../core/Ktx2.cjs.js"),ne=require("../core/Progress.cjs.js"),pe=require("../core/Texture.cjs.js"),je=require("../core/VideoTexture.cjs.js"),le=require("../core/useFont.cjs.js"),xe=require("../core/useSpriteLoader.cjs.js"),de=require("../core/Helper.cjs.js"),qe=require("../core/Stats.cjs.js"),me=require("../core/StatsGl.cjs.js"),Ce=require("../core/useDepthBuffer.cjs.js"),he=require("../core/useAspect.cjs.js"),Se=require("../core/useCamera.cjs.js"),Me=require("../core/DetectGPU.cjs.js"),Te=require("../core/Bvh.cjs.js"),be=require("../core/useContextBridge.cjs.js"),Pe=require("../core/useAnimations.cjs.js"),fe=require("../core/Fbo.cjs.js"),ge=require("../core/useIntersect.cjs.js"),ve=require("../core/useBoxProjectedEnv.cjs.js"),Fe=require("../core/BBAnchor.cjs.js"),Be=require("../core/TrailTexture.cjs.js"),Ae=require("../core/Example.cjs.js"),Le=require("../core/SpriteAnimator.cjs.js"),De=require("../core/CurveModifier.cjs.js"),ke=require("../core/MeshDistortMaterial.cjs.js"),Ee=require("../core/MeshWobbleMaterial.cjs.js"),ye=require("../core/MeshReflectorMaterial.cjs.js"),Ge=require("../core/MeshRefractionMaterial.cjs.js"),Re=require("../core/MeshTransmissionMaterial.cjs.js"),we=require("../core/MeshDiscardMaterial.cjs.js"),ze=require("../core/MultiMaterial.cjs.js"),Ie=require("../core/PointMaterial.cjs.js"),Oe=require("../core/shaderMaterial.cjs.js"),Ve=require("../core/softShadows.cjs.js"),He=require("../core/shapes.cjs.js"),Ke=require("../core/RoundedBox.cjs.js"),We=require("../core/ScreenQuad.cjs.js"),Qe=require("../core/Center.cjs.js"),Ne=require("../core/Resize.cjs.js"),Ue=require("../core/Bounds.cjs.js"),Xe=require("../core/CameraShake.cjs.js"),_e=require("../core/Float.cjs.js"),Je=require("../core/Stage.cjs.js"),Ye=require("../core/Backdrop.cjs.js"),Ze=require("../core/Shadow.cjs.js"),$e=require("../core/Caustics.cjs.js"),er=require("../core/ContactShadows.cjs.js"),rr=require("../core/AccumulativeShadows.cjs.js"),sr=require("../core/SpotLight.cjs.js"),or=require("../core/Environment.cjs.js"),tr=require("../core/Lightformer.cjs.js"),ir=require("../core/Sky.cjs.js"),ar=require("../core/Stars.cjs.js"),cr=require("../core/Cloud.cjs.js"),ur=require("../core/Sparkles.cjs.js"),nr=require("../core/useEnvironment.cjs.js"),pr=require("../core/MatcapTexture.cjs.js"),jr=require("../core/NormalTexture.cjs.js"),lr=require("../core/Wireframe.cjs.js"),xr=require("../core/ShadowAlpha.cjs.js"),dr=require("../core/Points.cjs.js"),qr=require("../core/Instances.cjs.js"),mr=require("../core/Segments.cjs.js"),Cr=require("../core/Detailed.cjs.js"),hr=require("../core/Preload.cjs.js"),Sr=require("../core/BakeShadows.cjs.js"),Mr=require("../core/meshBounds.cjs.js"),Tr=require("../core/AdaptiveDpr.cjs.js"),br=require("../core/AdaptiveEvents.cjs.js"),Pr=require("../core/PerformanceMonitor.cjs.js"),fr=require("../core/RenderTexture.cjs.js"),gr=require("../core/RenderCubeTexture.cjs.js"),vr=require("../core/Mask.cjs.js"),Fr=require("../core/Hud.cjs.js"),Br=require("../core/Fisheye.cjs.js"),Ar=require("../core/MeshPortalMaterial.cjs.js"),Lr=require("../core/calculateScaleFactor.cjs.js");function Dr(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}require("@babel/runtime/helpers/extends"),require("react"),require("react-dom/client"),require("three"),require("@react-three/fiber"),require("zustand"),require("maath"),require("@use-gesture/react"),require("zustand/middleware"),require("three-stdlib"),require("zustand/shallow"),require("tunnel-rat"),require("./pivotControls/AxisArrow.cjs.js"),require("./pivotControls/context.cjs.js"),require("./pivotControls/AxisRotator.cjs.js"),require("./pivotControls/PlaneSlider.cjs.js"),require("./pivotControls/ScalingSphere.cjs.js"),require("suspend-react"),require("hls.js"),require("troika-three-text"),require("../helpers/constants.cjs.js"),require("meshline"),require("stats.js"),require("../helpers/useEffectfulState.cjs.js"),require("stats-gl"),require("detect-gpu"),require("three-mesh-bvh"),require("../helpers/deprecated.cjs.js"),require("../materials/BlurPass.cjs.js"),require("../materials/ConvolutionMaterial.cjs.js"),require("../materials/MeshReflectorMaterial.cjs.js"),require("../materials/MeshRefractionMaterial.cjs.js"),require("../materials/DiscardMaterial.cjs.js"),require("@monogrid/gainmap-js"),require("../helpers/environment-assets.cjs.js"),require("../materials/SpotLightMaterial.cjs.js"),require("../materials/WireframeMaterial.cjs.js");var kr=Dr(ee);exports.Html=e.Html,exports.CycleRaycast=r.CycleRaycast,exports.useCursor=s.useCursor,exports.Loader=o.Loader,exports.Scroll=t.Scroll,exports.ScrollControls=t.ScrollControls,exports.useScroll=t.useScroll,exports.PresentationControls=i.PresentationControls,exports.KeyboardControls=a.KeyboardControls,exports.useKeyboardControls=a.useKeyboardControls,exports.Select=c.Select,exports.useSelect=c.useSelect,exports.View=u.View,exports.PivotControls=n.PivotControls,exports.ScreenVideoTexture=p.ScreenVideoTexture,exports.WebcamVideoTexture=j.WebcamVideoTexture,exports.FaceControls=l.FaceControls,exports.useFaceControls=l.useFaceControls,exports.DragControls=x.DragControls,exports.FaceLandmarker=d.FaceLandmarker,exports.FaceLandmarkerDefaults=d.FaceLandmarkerDefaults,exports.useFaceLandmarker=d.useFaceLandmarker,exports.Facemesh=q.Facemesh,exports.FacemeshDatas=q.FacemeshDatas,exports.FacemeshEye=q.FacemeshEye,exports.FacemeshEyeDefaults=q.FacemeshEyeDefaults,exports.Billboard=m.Billboard,exports.ScreenSpace=C.ScreenSpace,exports.ScreenSizer=h.ScreenSizer,exports.QuadraticBezierLine=S.QuadraticBezierLine,exports.CubicBezierLine=M.CubicBezierLine,exports.CatmullRomLine=T.CatmullRomLine,exports.Line=b.Line,exports.PositionalAudio=P.PositionalAudio,exports.Text=f.Text,exports.Text3D=g.Text3D,exports.Effects=v.Effects,exports.isWebGL2Available=v.isWebGL2Available,exports.GradientTexture=F.GradientTexture,exports.GradientType=F.GradientType,exports.Image=B.Image,exports.Edges=A.Edges,exports.Outlines=L.Outlines,exports.Trail=D.Trail,exports.useTrail=D.useTrail,exports.Sampler=k.Sampler,exports.useSurfaceSampler=k.useSurfaceSampler,exports.ComputedAttribute=E.ComputedAttribute,exports.Clone=y.Clone,exports.MarchingCube=G.MarchingCube,exports.MarchingCubes=G.MarchingCubes,exports.MarchingPlane=G.MarchingPlane,exports.Decal=R.Decal,exports.Svg=w.Svg,exports.Gltf=z.Gltf,exports.useGLTF=z.useGLTF,exports.AsciiRenderer=I.AsciiRenderer,exports.Splat=O.Splat,exports.OrthographicCamera=V.OrthographicCamera,exports.PerspectiveCamera=H.PerspectiveCamera,exports.CubeCamera=K.CubeCamera,exports.useCubeCamera=K.useCubeCamera,exports.DeviceOrientationControls=W.DeviceOrientationControls,exports.FlyControls=Q.FlyControls,exports.MapControls=N.MapControls,exports.OrbitControls=U.OrbitControls,exports.TrackballControls=X.TrackballControls,exports.ArcballControls=_.ArcballControls,exports.TransformControls=J.TransformControls,exports.PointerLockControls=Y.PointerLockControls,exports.FirstPersonControls=Z.FirstPersonControls,exports.CameraControls=$.CameraControls,Object.defineProperty(exports,"CameraControlsImpl",{enumerable:!0,get:function(){return kr.default}}),exports.MotionPathControls=re.MotionPathControls,exports.useMotion=re.useMotion,exports.GizmoHelper=se.GizmoHelper,exports.useGizmoContext=se.useGizmoContext,exports.GizmoViewcube=oe.GizmoViewcube,exports.GizmoViewport=te.GizmoViewport,exports.Grid=ie.Grid,exports.CubeTexture=ae.CubeTexture,exports.useCubeTexture=ae.useCubeTexture,exports.Fbx=ce.Fbx,exports.useFBX=ce.useFBX,exports.Ktx2=ue.Ktx2,exports.useKTX2=ue.useKTX2,exports.Progress=ne.Progress,exports.useProgress=ne.useProgress,exports.IsObject=pe.IsObject,exports.Texture=pe.Texture,exports.useTexture=pe.useTexture,exports.VideoTexture=je.VideoTexture,exports.useVideoTexture=je.useVideoTexture,exports.useFont=le.useFont,exports.checkIfFrameIsEmpty=xe.checkIfFrameIsEmpty,exports.getFirstFrame=xe.getFirstFrame,exports.useSpriteLoader=xe.useSpriteLoader,exports.Helper=de.Helper,exports.useHelper=de.useHelper,exports.Stats=qe.Stats,exports.StatsGl=me.StatsGl,exports.useDepthBuffer=Ce.useDepthBuffer,exports.useAspect=he.useAspect,exports.useCamera=Se.useCamera,exports.DetectGPU=Me.DetectGPU,exports.useDetectGPU=Me.useDetectGPU,exports.Bvh=Te.Bvh,exports.useBVH=Te.useBVH,exports.useContextBridge=be.useContextBridge,exports.useAnimations=Pe.useAnimations,exports.Fbo=fe.Fbo,exports.useFBO=fe.useFBO,exports.useIntersect=ge.useIntersect,exports.useBoxProjectedEnv=ve.useBoxProjectedEnv,exports.BBAnchor=Fe.BBAnchor,exports.TrailTexture=Be.TrailTexture,exports.useTrailTexture=Be.useTrailTexture,exports.Example=Ae.Example,exports.SpriteAnimator=Le.SpriteAnimator,exports.useSpriteAnimator=Le.useSpriteAnimator,exports.CurveModifier=De.CurveModifier,exports.MeshDistortMaterial=ke.MeshDistortMaterial,exports.MeshWobbleMaterial=Ee.MeshWobbleMaterial,exports.MeshReflectorMaterial=ye.MeshReflectorMaterial,exports.MeshRefractionMaterial=Ge.MeshRefractionMaterial,exports.MeshTransmissionMaterial=Re.MeshTransmissionMaterial,exports.MeshDiscardMaterial=we.MeshDiscardMaterial,exports.MultiMaterial=ze.MultiMaterial,exports.PointMaterial=Ie.PointMaterial,exports.PointMaterialImpl=Ie.PointMaterialImpl,exports.shaderMaterial=Oe.shaderMaterial,exports.SoftShadows=Ve.SoftShadows,exports.Box=He.Box,exports.Capsule=He.Capsule,exports.Circle=He.Circle,exports.Cone=He.Cone,exports.Cylinder=He.Cylinder,exports.Dodecahedron=He.Dodecahedron,exports.Extrude=He.Extrude,exports.Icosahedron=He.Icosahedron,exports.Lathe=He.Lathe,exports.Octahedron=He.Octahedron,exports.Plane=He.Plane,exports.Polyhedron=He.Polyhedron,exports.Ring=He.Ring,exports.Shape=He.Shape,exports.Sphere=He.Sphere,exports.Tetrahedron=He.Tetrahedron,exports.Torus=He.Torus,exports.TorusKnot=He.TorusKnot,exports.Tube=He.Tube,exports.RoundedBox=Ke.RoundedBox,exports.ScreenQuad=We.ScreenQuad,exports.Center=Qe.Center,exports.Resize=Ne.Resize,exports.Bounds=Ue.Bounds,exports.useBounds=Ue.useBounds,exports.CameraShake=Xe.CameraShake,exports.Float=_e.Float,exports.Stage=Je.Stage,exports.Backdrop=Ye.Backdrop,exports.Shadow=Ze.Shadow,exports.Caustics=$e.Caustics,exports.ContactShadows=er.ContactShadows,exports.AccumulativeShadows=rr.AccumulativeShadows,exports.RandomizedLight=rr.RandomizedLight,exports.accumulativeContext=rr.accumulativeContext,exports.SpotLight=sr.SpotLight,exports.SpotLightShadow=sr.SpotLightShadow,exports.Environment=or.Environment,exports.EnvironmentCube=or.EnvironmentCube,exports.EnvironmentMap=or.EnvironmentMap,exports.EnvironmentPortal=or.EnvironmentPortal,exports.Lightformer=tr.Lightformer,exports.Sky=ir.Sky,exports.calcPosFromAngles=ir.calcPosFromAngles,exports.Stars=ar.Stars,exports.Cloud=cr.Cloud,exports.CloudInstance=cr.CloudInstance,exports.Clouds=cr.Clouds,exports.Sparkles=ur.Sparkles,exports.useEnvironment=nr.useEnvironment,exports.MatcapTexture=pr.MatcapTexture,exports.useMatcapTexture=pr.useMatcapTexture,exports.NormalTexture=jr.NormalTexture,exports.useNormalTexture=jr.useNormalTexture,exports.Wireframe=lr.Wireframe,exports.ShadowAlpha=xr.ShadowAlpha,exports.Point=dr.Point,exports.Points=dr.Points,exports.PointsBuffer=dr.PointsBuffer,exports.PositionPoint=dr.PositionPoint,exports.Instance=qr.Instance,exports.InstancedAttribute=qr.InstancedAttribute,exports.Instances=qr.Instances,exports.Merged=qr.Merged,exports.PositionMesh=qr.PositionMesh,exports.createInstances=qr.createInstances,exports.Segment=mr.Segment,exports.SegmentObject=mr.SegmentObject,exports.Segments=mr.Segments,exports.Detailed=Cr.Detailed,exports.Preload=hr.Preload,exports.BakeShadows=Sr.BakeShadows,exports.meshBounds=Mr.meshBounds,exports.AdaptiveDpr=Tr.AdaptiveDpr,exports.AdaptiveEvents=br.AdaptiveEvents,exports.PerformanceMonitor=Pr.PerformanceMonitor,exports.usePerformanceMonitor=Pr.usePerformanceMonitor,exports.RenderTexture=fr.RenderTexture,exports.RenderCubeTexture=gr.RenderCubeTexture,exports.Mask=vr.Mask,exports.useMask=vr.useMask,exports.Hud=Fr.Hud,exports.Fisheye=Br.Fisheye,exports.MeshPortalMaterial=Ar.MeshPortalMaterial,exports.calculateScaleFactor=Lr.calculateScaleFactor;
