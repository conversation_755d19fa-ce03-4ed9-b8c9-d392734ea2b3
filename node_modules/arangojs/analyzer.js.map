{"version": 3, "file": "analyzer.js", "sourceRoot": "", "sources": ["../src/analyzer.ts"], "names": [], "mappings": ";;;AAYA,mCAAwC;AACxC,uCAAiD;AAEjD;;;;GAIG;AACH,SAAgB,gBAAgB,CAAC,QAAa;IAC5C,OAAO,OAAO,CAAC,QAAQ,IAAI,QAAQ,CAAC,gBAAgB,CAAC,CAAC;AACxD,CAAC;AAFD,4CAEC;AAy3BD;;GAEG;AACH,MAAa,QAAQ;IAInB;;OAEG;IACH,YAAY,EAAY,EAAE,IAAY;QACpC,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IACrC,CAAC;IAED;;;;OAIG;IACH,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED;;;;;;;;;;OAUG;IACH,KAAK,CAAC,MAAM;QACV,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,GAAG,EAAE,CAAC;YACjB,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAClB,IAAI,IAAA,qBAAa,EAAC,GAAG,CAAC,IAAI,GAAG,CAAC,QAAQ,KAAK,0BAAkB,EAAE,CAAC;gBAC9D,OAAO,KAAK,CAAC;YACf,CAAC;YACD,MAAM,GAAG,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;;;;;;;;;OAUG;IACH,GAAG;QACD,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;YACtB,IAAI,EAAE,kBAAkB,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;SACzD,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACH,MAAM,CACJ,OAAgB;QAsChB,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;YACtB,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,gBAAgB;YACtB,IAAI,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,GAAG,OAAO,EAAE;SACvC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,IAAI,CAAC,QAAiB,KAAK;QACzB,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;YACtB,MAAM,EAAE,QAAQ;YAChB,IAAI,EAAE,kBAAkB,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YACxD,EAAE,EAAE,EAAE,KAAK,EAAE;SACd,CAAC,CAAC;IACL,CAAC;CACF;AAxJD,4BAwJC", "sourcesContent": ["/**\n * ```ts\n * import type { Analyzer } from \"arangojs/analyzer\";\n * ```\n *\n * The \"analyzer\" module provides analyzer related types and interfaces\n * for TypeScript.\n *\n * @packageDocumentation\n */\nimport { ArangoApiResponse } from \"./connection\";\nimport { Database } from \"./database\";\nimport { isArangoError } from \"./error\";\nimport { ANALYZER_NOT_FOUND } from \"./lib/codes\";\n\n/**\n * Indicates whether the given value represents an {@link Analyzer}.\n *\n * @param analyzer - A value that might be an Analyzer.\n */\nexport function isArangoAnalyzer(analyzer: any): analyzer is Analyzer {\n  return Boolean(analyzer && analyzer.isArangoAnalyzer);\n}\n\n/**\n * Name of a feature enabled for an Analyzer.\n */\nexport type AnalyzerFeature = \"frequency\" | \"norm\" | \"position\" | \"offset\";\n\n/**\n * Analyzer type and its type-specific properties.\n */\nexport type CreateAnalyzerOptions =\n  | CreateIdentityAnalyzerOptions\n  | CreateDelimiterAnalyzerOptions\n  | CreateMultiDelimiterAnalyzerOptions\n  | CreateStemAnalyzerOptions\n  | CreateNormAnalyzerOptions\n  | CreateNgramAnalyzerOptions\n  | CreateTextAnalyzerOptions\n  | CreateSegmentationAnalyzerOptions\n  | CreateAqlAnalyzerOptions\n  | CreatePipelineAnalyzerOptions\n  | CreateStopwordsAnalyzerOptions\n  | CreateCollationAnalyzerOptions\n  | CreateMinHashAnalyzerOptions\n  | CreateClassificationAnalyzerOptions\n  | CreateNearestNeighborsAnalyzerOptions\n  | CreateWildcardAnalyzerOptions\n  | CreateGeoJsonAnalyzerOptions\n  | CreateGeoPointAnalyzerOptions\n  | CreateGeoS2AnalyzerOptions;\n\n/**\n * Options for creating an Identity Analyzer.\n */\nexport type CreateIdentityAnalyzerOptions = {\n  /**\n   * Type of the Analyzer.\n   */\n  type: \"identity\";\n  /**\n   * Features to enable for this Analyzer.\n   */\n  features?: AnalyzerFeature[];\n  /**\n   * Additional properties for the Analyzer.\n   *\n   * The `identity` Analyzer does not take additional properties.\n   */\n  properties?: Record<string, never>;\n};\n\n/**\n * Options for creating a Delimiter Analyzer.\n */\nexport type CreateDelimiterAnalyzerOptions = {\n  /**\n   * Type of the Analyzer.\n   */\n  type: \"delimiter\";\n  /**\n   * Features to enable for this Analyzer.\n   */\n  features?: AnalyzerFeature[];\n  /**\n   * Additional properties for the Analyzer.\n   *\n   * The value will be used as delimiter to split text into tokens as specified\n   * in RFC 4180, without starting new records on newlines.\n   */\n  properties: string | { delimiter: string };\n};\n\n/**\n * Options for creating a Multi-Delimiter Analyzer.\n */\nexport type CreateMultiDelimiterAnalyzerOptions = {\n  /**\n   * Type of the Analyzer.\n   */\n  type: \"multi_delimiter\";\n  /**\n   * Features to enable for this Analyzer.\n   */\n  features?: AnalyzerFeature[];\n  /**\n   * Additional properties for the Analyzer.\n   *\n   * The value will be used as delimiter to split text into tokens as specified\n   * in RFC 4180, without starting new records on newlines.\n   */\n  properties: { delimiters: string[] };\n};\n\n/**\n * Options for creating a Stem Analyzer.\n */\nexport type CreateStemAnalyzerOptions = {\n  /**\n   * Type of the Analyzer.\n   */\n  type: \"stem\";\n  /**\n   * Features to enable for this Analyzer.\n   */\n  features?: AnalyzerFeature[];\n  /**\n   * Additional properties for the Analyzer.\n   *\n   * The value defines the text locale.\n   *\n   * Format: `language[_COUNTRY][.encoding][@variant]`\n   */\n  properties: { locale: string };\n};\n\n/**\n * Options for creating a Norm Analyzer.\n */\nexport type CreateNormAnalyzerOptions = {\n  /**\n   * Type of the Analyzer.\n   */\n  type: \"norm\";\n  /**\n   * Features to enable for this Analyzer.\n   */\n  features?: AnalyzerFeature[];\n  /**\n   * Additional properties for the Analyzer.\n   */\n  properties: {\n    /**\n     * Text locale.\n     *\n     * Format: `language[_COUNTRY][.encoding][@variant]`\n     */\n    locale: string;\n    /**\n     * Case conversion.\n     *\n     * Default: `\"lower\"`\n     */\n    case?: \"lower\" | \"none\" | \"upper\";\n    /**\n     * Preserve accents in returned words.\n     *\n     * Default: `false`\n     */\n    accent?: boolean;\n  };\n};\n\n/**\n * Options for creating an Ngram Analyzer.\n */\nexport type CreateNgramAnalyzerOptions = {\n  /**\n   * Type of the Analyzer.\n   */\n  type: \"ngram\";\n  /**\n   * Features to enable for this Analyzer.\n   */\n  features?: AnalyzerFeature[];\n  /**\n   * Additional properties for the Analyzer.\n   */\n  properties: {\n    /**\n     * Maximum n-gram length.\n     */\n    max: number;\n    /**\n     * Minimum n-gram length.\n     */\n    min: number;\n    /**\n     * Output the original value as well.\n     */\n    preserveOriginal: boolean;\n  };\n};\n\n/**\n * Options for creating a Text Analyzer.\n */\nexport type CreateTextAnalyzerOptions = {\n  /**\n   * Type of the Analyzer.\n   */\n  type: \"text\";\n  /**\n   * Features to enable for this Analyzer.\n   */\n  features?: AnalyzerFeature[];\n  /**\n   * Additional properties for the Analyzer.\n   */\n  properties: {\n    /**\n     * Text locale.\n     *\n     * Format: `language[_COUNTRY][.encoding][@variant]`\n     */\n    locale: string;\n    /**\n     * Case conversion.\n     *\n     * Default: `\"lower\"`\n     */\n    case?: \"lower\" | \"none\" | \"upper\";\n    /**\n     * Words to omit from result.\n     *\n     * Defaults to the words loaded from the file at `stopwordsPath`.\n     */\n    stopwords?: string[];\n    /**\n     * Path with a `language` sub-directory containing files with words to omit.\n     *\n     * Defaults to the path specified in the server-side environment variable\n     * `IRESEARCH_TEXT_STOPWORD_PATH` or the current working directory of the\n     * ArangoDB process.\n     */\n    stopwordsPath?: string;\n    /**\n     * Preserve accents in returned words.\n     *\n     * Default: `false`\n     */\n    accent?: boolean;\n    /**\n     * Apply stemming on returned words.\n     *\n     * Default: `true`\n     */\n    stemming?: boolean;\n    /**\n     * If present, then edge n-grams are generated for each token (word).\n     */\n    edgeNgram?: { min?: number; max?: number; preserveOriginal?: boolean };\n  };\n};\n\n/**\n * Options for creating a Segmentation Analyzer\n */\nexport type CreateSegmentationAnalyzerOptions = {\n  /**\n   * Type of the Analyzer.\n   */\n  type: \"segmentation\";\n  /**\n   * Features to enable for this Analyzer.\n   */\n  features?: AnalyzerFeature[];\n  /**\n   * Additional properties for the Analyzer.\n   */\n  properties: {\n    /**\n     * Which tokens should be returned.\n     *\n     * Default: `\"alpha\"`\n     */\n    break?: \"all\" | \"alpha\" | \"graphic\";\n    /**\n     * What case all returned tokens should be converted to if applicable.\n     *\n     * Default: `\"none\"`\n     */\n    case?: \"lower\" | \"upper\" | \"none\";\n  };\n};\n\n/**\n * Options for creating an AQL Analyzer\n */\nexport type CreateAqlAnalyzerOptions = {\n  /**\n   * Type of the Analyzer.\n   */\n  type: \"aql\";\n  /**\n   * Features to enable for this Analyzer.\n   */\n  features?: AnalyzerFeature[];\n  /**\n   * Additional properties for the Analyzer.\n   */\n  properties: {\n    /**\n     * AQL query to be executed.\n     */\n    queryString: string;\n    /**\n     * If set to `true`, the position is set to `0` for all members of the query result array.\n     *\n     * Default: `false`\n     */\n    collapsePositions?: boolean;\n    /**\n     * If set to `false`, `null` values will be discarded from the View index.\n     *\n     * Default: `true`\n     */\n    keepNull?: boolean;\n    /**\n     * Number between `1` and `1000` that determines the batch size for reading\n     * data from the query.\n     *\n     * Default: `1`\n     */\n    batchSize?: number;\n    /**\n     * Memory limit for query execution in bytes.\n     *\n     * Default: `1048576` (1 MiB)\n     */\n    memoryLimit?: number;\n    /**\n     * Data type of the returned tokens.\n     *\n     * Default: `\"string\"`\n     */\n    returnType?: \"string\" | \"number\" | \"bool\";\n  };\n};\n\n/**\n * Options for creating a Pipeline Analyzer\n */\nexport type CreatePipelineAnalyzerOptions = {\n  /**\n   * Type of the Analyzer.\n   */\n  type: \"pipeline\";\n  /**\n   * Features to enable for this Analyzer.\n   */\n  features?: AnalyzerFeature[];\n  /**\n   * Additional properties for the Analyzer.\n   */\n  properties: {\n    /**\n     * Definitions for Analyzers to chain in this Pipeline Analyzer.\n     */\n    pipeline: Omit<CreateAnalyzerOptions, \"features\">[];\n  };\n};\n\n/**\n * Options for creating a Stopwords Analyzer\n */\nexport type CreateStopwordsAnalyzerOptions = {\n  /**\n   * Type of the Analyzer.\n   */\n  type: \"stopwords\";\n  /**\n   * Features to enable for this Analyzer.\n   */\n  features?: AnalyzerFeature[];\n  /**\n   * Additional properties for the Analyzer.\n   */\n  properties: {\n    /**\n     * Array of strings that describe the tokens to be discarded.\n     */\n    stopwords: string[];\n    /**\n     * Whether stopword values should be interpreted as hex-encoded strings.\n     *\n     * Default: `false`\n     */\n    hex?: boolean;\n  };\n};\n\n/**\n * Options for creating a Collation Analyzer\n */\nexport type CreateCollationAnalyzerOptions = {\n  /**\n   * Type of the Analyzer.\n   */\n  type: \"collation\";\n  /**\n   * Features to enable for this Analyzer.\n   */\n  features?: AnalyzerFeature[];\n  /**\n   * Additional properties for the Analyzer.\n   */\n  properties: {\n    /**\n     * Text locale.\n     *\n     * Format: `language[_COUNTRY][.encoding][@variant]`\n     */\n    locale: string;\n  };\n};\n\n/**\n * (Enterprise Edition only.) Options for creating a MinHash Analyzer\n */\nexport type CreateMinHashAnalyzerOptions = {\n  /**\n   * Type of the Analyzer.\n   */\n  type: \"minhash\";\n  /**\n   * Features to enable for this Analyzer.\n   */\n  features?: AnalyzerFeature[];\n  /**\n   * Additional properties for the Analyzer.\n   */\n  properties: {\n    /**\n     * An Analyzer definition-like object with `type` and `properties` attributes.\n     */\n    analyzer: Omit<CreateAnalyzerOptions, \"features\">;\n    /**\n     * Size of the MinHash signature.\n     */\n    numHashes: number;\n  };\n};\n\n/**\n * (Enterprise Edition only.) Options for creating a Classification Analyzer\n */\nexport type CreateClassificationAnalyzerOptions = {\n  /**\n   * Type of the Analyzer.\n   */\n  type: \"classification\";\n  /**\n   * Features to enable for this Analyzer.\n   */\n  features?: AnalyzerFeature[];\n  /**\n   * Additional properties for the Analyzer.\n   */\n  properties: {\n    /**\n     * On-disk path to the trained fastText supervised model.\n     */\n    model_location: string;\n    /**\n     * Number of class labels that will be produced per input.\n     *\n     * Default: `1`\n     */\n    top_k?: number;\n    /**\n     * Probability threshold for which a label will be assigned to an input.\n     *\n     * Default: `0.99`\n     */\n    threshold?: number;\n  };\n};\n\n/**\n * (Enterprise Edition only.) Options for creating a NearestNeighbors Analyzer.\n */\nexport type CreateNearestNeighborsAnalyzerOptions = {\n  /**\n   * Type of the Analyzer.\n   */\n  type: \"nearest_neighbors\";\n  /**\n   * Features to enable for this Analyzer.\n   */\n  features?: AnalyzerFeature[];\n  /**\n   * Additional properties for the Analyzer.\n   */\n  properties: {\n    /**\n     * On-disk path to the trained fastText supervised model.\n     */\n    model_location: string;\n    /**\n     * Number of class labels that will be produced per input.\n     *\n     * Default: `1`\n     */\n    top_k?: number;\n  };\n};\n\n/**\n * Options for creating a Wildcard Analyzer.\n */\nexport type CreateWildcardAnalyzerOptions = {\n  /**\n   * Type of the Analyzer.\n   */\n  type: \"wildcard\";\n  /**\n   * Features to enable for this Analyzer.\n   */\n  features?: AnalyzerFeature[];\n  /**\n   * Additional properties for the Analyzer.\n   */\n  properties: {\n    /**\n     * N-gram length. Must be a positive integer greater than or equal to 2.\n     */\n    ngramSize: string;\n    /**\n     * An Analyzer definition-like object with `type` and `properties` attributes.\n     */\n    analyzer?: Omit<CreateAnalyzerOptions, \"features\">;\n  };\n};\n\n/**\n * Options for creating a GeoJSON Analyzer\n */\nexport type CreateGeoJsonAnalyzerOptions = {\n  /**\n   * Type of the Analyzer.\n   */\n  type: \"geojson\";\n  /**\n   * Features to enable for this Analyzer.\n   */\n  features?: AnalyzerFeature[];\n  /**\n   * Additional properties for the Analyzer.\n   */\n  properties: {\n    /**\n     * If set to `\"centroid\"`, only the centroid of the input geometry will be\n     * computed and indexed.\n     *\n     * If set to `\"point\"` only GeoJSON objects of type Point will be indexed and\n     * all other geometry types will be ignored.\n     *\n     * Default: `\"shape\"`\n     */\n    type?: \"shape\" | \"centroid\" | \"point\";\n    /**\n     * Options for fine-tuning geo queries.\n     *\n     * Default: `{ maxCells: 20, minLevel: 4, maxLevel: 23 }`\n     */\n    options?: { maxCells?: number; minLevel?: number; maxLevel?: number };\n  };\n};\n\n/**\n * Options for creating a GeoPoint Analyzer\n */\nexport type CreateGeoPointAnalyzerOptions = {\n  /**\n   * Type of the Analyzer.\n   */\n  type: \"geopoint\";\n  /**\n   * Features to enable for this Analyzer.\n   */\n  features?: AnalyzerFeature[];\n  /**\n   * Additional properties for the Analyzer.\n   */\n  properties: {\n    /**\n     * Attribute paths of the latitude value relative to the field for which the\n     * Analyzer is defined in the View.\n     */\n    latitude?: string[];\n    /**\n     * Attribute paths of the longitude value relative to the field for which the\n     * Analyzer is defined in the View.\n     */\n    longitude?: string[];\n    /**\n     * Options for fine-tuning geo queries.\n     *\n     * Default: `{ maxCells: 20, minLevel: 4, maxLevel: 23 }`\n     */\n    options?: { minCells?: number; minLevel?: number; maxLevel?: number };\n  };\n};\n\n/**\n * (Enterprise Edition only.) Options for creating a Geo S2 Analyzer\n */\nexport type CreateGeoS2AnalyzerOptions = {\n  /**\n   * Type of the Analyzer.\n   */\n  type: \"geo_s2\";\n  /**\n   * Features to enable for this Analyzer.\n   */\n  features?: AnalyzerFeature[];\n  /**\n   * Additional properties for the Analyzer.\n   */\n  properties: {\n    /**\n     * If set to `\"centroid\"`, only the centroid of the input geometry will be\n     * computed and indexed.\n     *\n     * If set to `\"point\"` only GeoJSON objects of type Point will be indexed and\n     * all other geometry types will be ignored.\n     *\n     * Default: `\"shape\"`\n     */\n    type?: \"shape\" | \"centroid\" | \"point\";\n    /**\n     * Options for fine-tuning geo queries.\n     *\n     * Default: `{ maxCells: 20, minLevel: 4, maxLevel: 23 }`\n     */\n    options?: { maxCells?: number; minLevel?: number; maxLevel?: number };\n    /**\n     * If set to `\"latLngDouble\"`, each latitude and longitude value is stored\n     * as an 8-byte floating-point value (16 bytes per coordinate pair).\n     *\n     * If set to `\"latLngInt\"`, each latitude and longitude value is stored as\n     * a 4-byte integer value (8 bytes per coordinate pair).\n     *\n     * If set to `\"s2Point\"`, each longitude-latitude pair is stored in the\n     * native format of Google S2 (24 bytes per coordinate pair).\n     *\n     * Default: `\"latLngDouble\"`\n     */\n    format?: \"latLngDouble\" | \"latLngInt\" | \"s2Point\";\n  };\n};\n\n/**\n * Shared attributes of all Analyzer descriptions.\n */\nexport type GenericAnalyzerDescription = {\n  /**\n   * A unique name for this Analyzer.\n   */\n  name: string;\n  /**\n   * Features enabled for this Analyzer.\n   */\n  features: AnalyzerFeature[];\n};\n\n/**\n * An object describing an Analyzer.\n */\nexport type AnalyzerDescription =\n  | IdentityAnalyzerDescription\n  | DelimiterAnalyzerDescription\n  | MultiDelimiterAnalyzerDescription\n  | StemAnalyzerDescription\n  | NormAnalyzerDescription\n  | NgramAnalyzerDescription\n  | TextAnalyzerDescription\n  | SegmentationAnalyzerDescription\n  | AqlAnalyzerDescription\n  | PipelineAnalyzerDescription\n  | StopwordsAnalyzerDescription\n  | CollationAnalyzerDescription\n  | MinHashAnalyzerDescription\n  | ClassificationAnalyzerDescription\n  | NearestNeighborsAnalyzerDescription\n  | WildcardAnalyzerDescription\n  | GeoJsonAnalyzerDescription\n  | GeoPointAnalyzerDescription\n  | GeoS2AnalyzerDescription;\n\n/**\n * An object describing an Identity Analyzer.\n */\nexport type IdentityAnalyzerDescription = GenericAnalyzerDescription & {\n  type: \"identity\";\n  properties: Record<string, never>;\n};\n\n/**\n * An object describing a Delimiter Analyzer.\n */\nexport type DelimiterAnalyzerDescription = GenericAnalyzerDescription & {\n  type: \"delimiter\";\n  properties: { delimiter: string };\n};\n\n/**\n * An object describing a Multi Delimiter Analyzer.\n */\nexport type MultiDelimiterAnalyzerDescription = GenericAnalyzerDescription & {\n  type: \"multi_delimiter\";\n  properties: { delimiters: string[] };\n};\n\n/**\n * An object describing a Stem Analyzer.\n */\nexport type StemAnalyzerDescription = GenericAnalyzerDescription & {\n  type: \"stem\";\n  properties: { locale: string };\n};\n\n/**\n * An object describing a Norm Analyzer.\n */\nexport type NormAnalyzerDescription = GenericAnalyzerDescription & {\n  type: \"norm\";\n  properties: {\n    locale: string;\n    case: \"lower\" | \"none\" | \"upper\";\n    accent: boolean;\n  };\n};\n\n/**\n * An object describing an Ngram Analyzer.\n */\nexport type NgramAnalyzerDescription = GenericAnalyzerDescription & {\n  type: \"ngram\";\n  properties: {\n    max: number;\n    min: number;\n    preserveOriginal: boolean;\n  };\n};\n\n/**\n * An object describing a Text Analyzer.\n */\nexport type TextAnalyzerDescription = GenericAnalyzerDescription & {\n  type: \"text\";\n  properties: {\n    locale: string;\n    case: \"lower\" | \"none\" | \"upper\";\n    stopwords: string[];\n    stopwordsPath: string;\n    accent: boolean;\n    stemming: boolean;\n    edgeNgram: { min: number; max: number; preserveOriginal: boolean };\n  };\n};\n\n/**\n * An object describing a Segmentation Analyzer\n */\nexport type SegmentationAnalyzerDescription = GenericAnalyzerDescription & {\n  type: \"segmentation\";\n  properties: {\n    break: \"all\" | \"alpha\" | \"graphic\";\n    case: \"lower\" | \"upper\" | \"none\";\n  };\n};\n\n/**\n * An object describing an AQL Analyzer\n */\nexport type AqlAnalyzerDescription = GenericAnalyzerDescription & {\n  type: \"aql\";\n  properties: {\n    queryString: string;\n    collapsePositions: boolean;\n    keepNull: boolean;\n    batchSize: number;\n    memoryLimit: number;\n    returnType: \"string\" | \"number\" | \"bool\";\n  };\n};\n\n/**\n * An object describing a Pipeline Analyzer\n */\nexport type PipelineAnalyzerDescription = GenericAnalyzerDescription & {\n  type: \"pipeline\";\n  properties: {\n    pipeline: Omit<AnalyzerDescription, \"name\" | \"features\">[];\n  };\n};\n\n/**\n * An object describing a Stopwords Analyzer\n */\nexport type StopwordsAnalyzerDescription = GenericAnalyzerDescription & {\n  type: \"stopwords\";\n  properties: {\n    stopwords: string[];\n    hex: boolean;\n  };\n};\n\n/**\n * An object describing a Collation Analyzer\n */\nexport type CollationAnalyzerDescription = GenericAnalyzerDescription & {\n  type: \"collation\";\n  properties: {\n    locale: string;\n  };\n};\n\n/**\n * (Enterprise Edition only.) An object describing a MinHash Analyzer\n */\nexport type MinHashAnalyzerDescription = GenericAnalyzerDescription & {\n  type: \"minhash\";\n  properties: {\n    analyzer: Omit<AnalyzerDescription, \"name\" | \"features\">;\n    numHashes: number;\n  };\n};\n\n/**\n * (Enterprise Edition only.) An object describing a Classification Analyzer\n */\nexport type ClassificationAnalyzerDescription = GenericAnalyzerDescription & {\n  type: \"classification\";\n  properties: {\n    model_location: string;\n    top_k: number;\n    threshold: number;\n  };\n};\n\n/**\n * (Enterprise Edition only.) An object describing a NearestNeighbors Analyzer\n */\nexport type NearestNeighborsAnalyzerDescription = GenericAnalyzerDescription & {\n  type: \"nearest_neighbors\";\n  properties: {\n    model_location: string;\n    top_k: number;\n  };\n};\n\n/**\n * An object describing a Wildcard Analyzer\n */\nexport type WildcardAnalyzerDescription = GenericAnalyzerDescription & {\n  type: \"wildcard\";\n  properties: {\n    ngramSize: number;\n    analyzer?: Omit<AnalyzerDescription, \"name\" | \"features\">;\n  };\n};\n\n/**\n * An object describing a GeoJSON Analyzer\n */\nexport type GeoJsonAnalyzerDescription = GenericAnalyzerDescription & {\n  type: \"geojson\";\n  properties: {\n    type: \"shape\" | \"centroid\" | \"point\";\n    description: { maxCells: number; minLevel: number; maxLevel: number };\n  };\n};\n\n/**\n * An object describing a GeoPoint Analyzer\n */\nexport type GeoPointAnalyzerDescription = GenericAnalyzerDescription & {\n  type: \"geopoint\";\n  properties: {\n    latitude: string[];\n    longitude: string[];\n    description: { minCells: number; minLevel: number; maxLevel: number };\n  };\n};\n\n/**\n * (Enterprise Edition only.) An object describing a GeoS2 Analyzer\n */\nexport type GeoS2AnalyzerDescription = GenericAnalyzerDescription & {\n  type: \"geo_s2\";\n  properties: {\n    type: \"shape\" | \"centroid\" | \"point\";\n    description: { maxCells: number; minLevel: number; maxLevel: number };\n    format: \"latLngDouble\" | \"latLngInt\" | \"s2Point\";\n  };\n};\n\n/**\n * Represents an Analyzer in a {@link database.Database}.\n */\nexport class Analyzer {\n  protected _name: string;\n  protected _db: Database;\n\n  /**\n   * @internal\n   */\n  constructor(db: Database, name: string) {\n    this._db = db;\n    this._name = name.normalize(\"NFC\");\n  }\n\n  /**\n   * @internal\n   *\n   * Indicates that this object represents an ArangoDB Analyzer.\n   */\n  get isArangoAnalyzer(): true {\n    return true;\n  }\n\n  /**\n   * Name of this Analyzer.\n   *\n   * See also {@link database.Database}.\n   */\n  get name() {\n    return this._name;\n  }\n\n  /**\n   * Checks whether the Analyzer exists.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const analyzer = db.analyzer(\"some-analyzer\");\n   * const result = await analyzer.exists();\n   * // result indicates whether the Analyzer exists\n   * ```\n   */\n  async exists(): Promise<boolean> {\n    try {\n      await this.get();\n      return true;\n    } catch (err: any) {\n      if (isArangoError(err) && err.errorNum === ANALYZER_NOT_FOUND) {\n        return false;\n      }\n      throw err;\n    }\n  }\n\n  /**\n   * Retrieves the Analyzer definition for the Analyzer.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const analyzer = db.analyzer(\"some-analyzer\");\n   * const definition = await analyzer.get();\n   * // definition contains the Analyzer definition\n   * ```\n   */\n  get(): Promise<ArangoApiResponse<AnalyzerDescription>> {\n    return this._db.request({\n      path: `/_api/analyzer/${encodeURIComponent(this._name)}`,\n    });\n  }\n\n  /**\n   * Creates a new Analyzer with the given `options` and the instance's name.\n   *\n   * See also {@link database.Database#createAnalyzer}.\n   *\n   * @param options - Options for creating the Analyzer.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const analyzer = db.analyzer(\"potatoes\");\n   * await analyzer.create({ type: \"identity\" });\n   * // the identity Analyzer \"potatoes\" now exists\n   * ```\n   */\n  create<Options extends CreateAnalyzerOptions>(\n    options: Options\n  ): Promise<\n    Options extends CreateIdentityAnalyzerOptions\n      ? IdentityAnalyzerDescription\n      : Options extends CreateDelimiterAnalyzerOptions\n      ? DelimiterAnalyzerDescription\n      : Options extends CreateStemAnalyzerOptions\n      ? StemAnalyzerDescription\n      : Options extends CreateNormAnalyzerOptions\n      ? NormAnalyzerDescription\n      : Options extends CreateNgramAnalyzerOptions\n      ? NgramAnalyzerDescription\n      : Options extends CreateTextAnalyzerOptions\n      ? TextAnalyzerDescription\n      : Options extends CreateSegmentationAnalyzerOptions\n      ? SegmentationAnalyzerDescription\n      : Options extends CreateAqlAnalyzerOptions\n      ? AqlAnalyzerDescription\n      : Options extends CreatePipelineAnalyzerOptions\n      ? PipelineAnalyzerDescription\n      : Options extends CreateStopwordsAnalyzerOptions\n      ? StopwordsAnalyzerDescription\n      : Options extends CreateCollationAnalyzerOptions\n      ? CollationAnalyzerDescription\n      : Options extends CreateMinHashAnalyzerOptions\n      ? MinHashAnalyzerDescription\n      : Options extends CreateClassificationAnalyzerOptions\n      ? ClassificationAnalyzerDescription\n      : Options extends CreateNearestNeighborsAnalyzerOptions\n      ? NearestNeighborsAnalyzerDescription\n      : Options extends CreateGeoJsonAnalyzerOptions\n      ? GeoJsonAnalyzerDescription\n      : Options extends CreateGeoPointAnalyzerOptions\n      ? GeoPointAnalyzerDescription\n      : Options extends CreateGeoS2AnalyzerOptions\n      ? GeoS2AnalyzerDescription\n      : AnalyzerDescription\n  > {\n    return this._db.request({\n      method: \"POST\",\n      path: \"/_api/analyzer\",\n      body: { name: this._name, ...options },\n    });\n  }\n\n  /**\n   * Deletes the Analyzer from the database.\n   *\n   * @param force - Whether the Analyzer should still be deleted even if it\n   * is currently in use.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const analyzer = db.analyzer(\"some-analyzer\");\n   * await analyzer.drop();\n   * // the Analyzer \"some-analyzer\" no longer exists\n   * ```\n   */\n  drop(force: boolean = false): Promise<ArangoApiResponse<{ name: string }>> {\n    return this._db.request({\n      method: \"DELETE\",\n      path: `/_api/analyzer/${encodeURIComponent(this._name)}`,\n      qs: { force },\n    });\n  }\n}\n"]}