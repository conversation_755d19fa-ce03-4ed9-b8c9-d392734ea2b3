{"version": 3, "file": "aql.d.ts", "sourceRoot": "", "sources": ["../src/aql.ts"], "names": [], "mappings": "AAaA,OAAO,EAAE,gBAAgB,EAAsB,MAAM,cAAc,CAAC;AACpE,OAAO,EAAE,KAAK,EAAiB,MAAM,SAAS,CAAC;AAC/C,OAAO,EAAgB,IAAI,EAAE,MAAM,QAAQ,CAAC;AAE5C,OAAO,CAAC,MAAM,IAAI,EAAE,OAAO,MAAM,CAAC;AAElC;;;GAGG;AACH,MAAM,WAAW,QAAQ,CAAC,CAAC,GAAG,GAAG;IAC/B,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;IACjB;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IACd;;;;;OAKG;IACH,QAAQ,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CAC/B;AAED;;;;;;GAMG;AACH,MAAM,WAAW,iBAAiB,CAAC,CAAC,GAAG,GAAG,CAAE,SAAQ,QAAQ,CAAC,CAAC,CAAC;IAC7D;;OAEG;IACH,OAAO,EAAE,MAAM;QAAE,OAAO,EAAE,MAAM,EAAE,CAAC;QAAC,IAAI,EAAE,QAAQ,EAAE,CAAA;KAAE,CAAC;CACxD;AAED;;;;;;GAMG;AACH,MAAM,WAAW,UAAU;IACzB;;;;;OAKG;IACH,KAAK,EAAE,MAAM,MAAM,CAAC;CACrB;AAED;;;GAGG;AACH,MAAM,MAAM,QAAQ,GAChB,gBAAgB,GAChB,IAAI,GACJ,KAAK,GACL,iBAAiB,GACjB,UAAU,GACV,MAAM,GACN,MAAM,GACN,OAAO,GACP,IAAI,GACJ,SAAS,GACT,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GACnB,GAAG,EAAE,CAAC;AAEV;;;;GAIG;AACH,wBAAgB,UAAU,CAAC,KAAK,EAAE,GAAG,GAAG,KAAK,IAAI,QAAQ,CAExD;AAED;;;;;;GAMG;AACH,wBAAgB,mBAAmB,CAAC,KAAK,EAAE,GAAG,GAAG,KAAK,IAAI,iBAAiB,CAE1E;AAED;;;;GAIG;AACH,wBAAgB,YAAY,CAAC,OAAO,EAAE,GAAG,GAAG,OAAO,IAAI,UAAU,CAEhE;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAwFG;AACH,wBAAgB,GAAG,CAAC,CAAC,GAAG,GAAG,EACzB,eAAe,EAAE,oBAAoB,EACrC,GAAG,IAAI,EAAE,QAAQ,EAAE,GAClB,iBAAiB,CAAC,CAAC,CAAC,CA2DtB;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAkDG;AACH,wBAAgB,OAAO,CACrB,KAAK,EAAE,MAAM,GAAG,MAAM,GAAG,OAAO,GAAG,UAAU,GAAG,IAAI,GAAG,SAAS,GAC/D,UAAU,CAYZ;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAgEG;AACH,wBAAgB,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE,GAAG,GAAE,MAAY,GAAG,iBAAiB,CAQ7E"}