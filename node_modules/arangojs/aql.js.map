{"version": 3, "file": "aql.js", "sourceRoot": "", "sources": ["../src/aql.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;GAWG;AACH,yCAA8C;AAC9C,6CAAoE;AACpE,mCAA+C;AAC/C,iCAA4C;AAwE5C;;;;GAIG;AACH,SAAgB,UAAU,CAAC,KAAU;IACnC,OAAO,OAAO,CAAC,KAAK,IAAI,OAAO,KAAK,CAAC,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC;AAC7E,CAAC;AAFD,gCAEC;AAED;;;;;;GAMG;AACH,SAAgB,mBAAmB,CAAC,KAAU;IAC5C,OAAO,UAAU,CAAC,KAAK,CAAC,IAAI,OAAQ,KAAa,CAAC,OAAO,KAAK,UAAU,CAAC;AAC3E,CAAC;AAFD,kDAEC;AAED;;;;GAIG;AACH,SAAgB,YAAY,CAAC,OAAY;IACvC,OAAO,OAAO,CAAC,OAAO,IAAI,OAAO,OAAO,CAAC,KAAK,KAAK,UAAU,CAAC,CAAC;AACjE,CAAC;AAFD,oCAEC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAwFG;AACH,SAAgB,GAAG,CACjB,eAAqC,EACrC,GAAG,IAAgB;IAEnB,MAAM,OAAO,GAAG,CAAC,GAAG,eAAe,CAAC,CAAC;IACrC,MAAM,QAAQ,GAAwB,EAAE,CAAC;IACzC,MAAM,UAAU,GAAG,EAAE,CAAC;IACtB,IAAI,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;IACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACrC,MAAM,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACzB,IAAI,KAAK,GAAG,QAAQ,CAAC;QACrB,IAAI,mBAAmB,CAAC,QAAQ,CAAC,EAAE,CAAC;YAClC,MAAM,GAAG,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;YAC/B,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACpB,KAAK,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBACxB,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;gBAC/B,OAAO,CAAC,MAAM,CACZ,CAAC,EACD,CAAC,EACD,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,EAC3B,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,EACxC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAC9C,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,KAAK,IAAI,QAAQ,CAAC,KAAK,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBACzC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAClB,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,KAAK,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACrE,CAAC;YACD,CAAC,IAAI,CAAC,CAAC;YACP,SAAS;QACX,CAAC;QACD,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC3B,KAAK,IAAI,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACxB,SAAS;QACX,CAAC;QACD,IAAI,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC3B,KAAK,IAAI,GAAG,QAAQ,CAAC,KAAK,EAAE,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;YAChD,SAAS;QACX,CAAC;QACD,MAAM,KAAK,GAAG,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC3C,MAAM,OAAO,GAAG,KAAK,KAAK,CAAC,CAAC,CAAC;QAC7B,IAAI,IAAI,GAAG,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;QACzD,IACE,IAAA,+BAAkB,EAAC,QAAQ,CAAC;YAC5B,IAAA,qBAAa,EAAC,QAAQ,CAAC;YACvB,IAAA,mBAAY,EAAC,QAAQ,CAAC;YACtB,IAAA,2BAAgB,EAAC,QAAQ,CAAC,EAC1B,CAAC;YACD,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;YAClB,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC;QACxB,CAAC;QACD,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC1B,QAAQ,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QACzB,CAAC;QACD,KAAK,IAAI,IAAI,IAAI,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;IACvC,CAAC;IACD,OAAO;QACL,KAAK;QACL,QAAQ;QACR,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;KACnC,CAAC;AACJ,CAAC;AA9DD,kBA8DC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAkDG;AACH,SAAgB,OAAO,CACrB,KAAgE;IAEhE,IAAI,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;QACxB,OAAO,KAAK,CAAC;IACf,CAAC;IACD,OAAO;QACL,KAAK;YACH,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;gBACxB,OAAO,EAAE,CAAC;YACZ,CAAC;YACD,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;QACvB,CAAC;KACF,CAAC;AACJ,CAAC;AAdD,0BAcC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAgEG;AACH,SAAgB,IAAI,CAAC,MAAkB,EAAE,MAAc,GAAG;IACxD,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;QACnB,OAAO,GAAG,CAAA,EAAE,CAAC;IACf,CAAC;IACD,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACxB,OAAO,GAAG,CAAA,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;IAC3B,CAAC;IACD,OAAO,GAAG,CAAC,CAAC,EAAE,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,CAAQ,EAAE,GAAG,MAAM,CAAC,CAAC;AAChF,CAAC;AARD,oBAQC", "sourcesContent": ["/**\n * ```js\n * import { aql } from \"arangojs/aql\";\n * ```\n *\n * The \"aql\" module provides the {@link aql} template string handler and\n * helper functions, as well as associated types and interfaces for TypeScript.\n *\n * The aql function and namespace is also re-exported by the \"index\" module.\n *\n * @packageDocumentation\n */\nimport { isArangoAnalyzer } from \"./analyzer\";\nimport { ArangoCollection, isArangoCollection } from \"./collection\";\nimport { Graph, isArangoGraph } from \"./graph\";\nimport { isArangoView, View } from \"./view\";\n\ndeclare const type: unique symbol;\n\n/**\n * Generic AQL query object consisting of an AQL query string and its bind\n * parameters.\n */\nexport interface AqlQuery<T = any> {\n  [type]?: T | any;\n  /**\n   * An AQL query string.\n   */\n  query: string;\n  /**\n   * An object mapping AQL bind parameter names to their respective values.\n   *\n   * Names of parameters representing collections are prefixed with an\n   * at-symbol.\n   */\n  bindVars: Record<string, any>;\n}\n\n/**\n * Derived type representing AQL query objects generated by the AQL helper\n * functions and the AQL template string handler. These objects can be fed\n * back into these helper functions to be inlined or merged in complex queries.\n *\n * @internal\n */\nexport interface GeneratedAqlQuery<T = any> extends AqlQuery<T> {\n  /**\n   * @internal\n   */\n  _source: () => { strings: string[]; args: AqlValue[] };\n}\n\n/**\n * An object representing a trusted AQL literal that will be inlined directly\n * when used in an AQL template or passed to AQL helper functions.\n *\n * Arbitrary values can be converted to trusted AQL literals by passing them\n * to the {@link literal} helper function.\n */\nexport interface AqlLiteral {\n  /**\n   * @internal\n   *\n   * Returns a string representation of this AQL literal that can be inlined\n   * in an AQL template.\n   */\n  toAQL: () => string;\n}\n\n/**\n * A value that can be used in an AQL template string or passed to an AQL\n * helper function.\n */\nexport type AqlValue =\n  | ArangoCollection\n  | View\n  | Graph\n  | GeneratedAqlQuery\n  | AqlLiteral\n  | string\n  | number\n  | boolean\n  | null\n  | undefined\n  | Record<string, any>\n  | any[];\n\n/**\n * Indicates whether the given value is an {@link AqlQuery}.\n *\n * @param query - A value that might be an `AqlQuery`.\n */\nexport function isAqlQuery(query: any): query is AqlQuery {\n  return Boolean(query && typeof query.query === \"string\" && query.bindVars);\n}\n\n/**\n * Indicates whether the given value is a {@link GeneratedAqlQuery}.\n *\n * @param query - A value that might be a `GeneratedAqlQuery`.\n *\n * @internal\n */\nexport function isGeneratedAqlQuery(query: any): query is GeneratedAqlQuery {\n  return isAqlQuery(query) && typeof (query as any)._source === \"function\";\n}\n\n/**\n * Indicates whether the given value is an {@link AqlLiteral}.\n *\n * @param literal - A value that might be an `AqlLiteral`.\n */\nexport function isAqlLiteral(literal: any): literal is AqlLiteral {\n  return Boolean(literal && typeof literal.toAQL === \"function\");\n}\n\n/**\n * Template string handler (template tag) for AQL queries.\n *\n * The `aql` tag can be used to write complex AQL queries as multi-line strings\n * without having to worry about `bindVars` and the distinction between\n * collections and regular parameters.\n *\n * Tagged template strings will return an {@link AqlQuery} object with\n * `query` and `bindVars` attributes reflecting any interpolated values.\n *\n * Any {@link collection.ArangoCollection} instance used in a query string will\n * be recognized as a collection reference and generate an AQL collection bind\n * parameter instead of a regular AQL value bind parameter.\n *\n * **Note**: you should always use the `aql` template tag when writing\n * dynamic AQL queries instead of using untagged (normal) template strings.\n * Untagged template strings will inline any interpolated values and return\n * a plain string as result. The `aql` template tag will only inline references\n * to the interpolated values and produce an AQL query object containing both\n * the query and the values. This prevents most injection attacks when using\n * untrusted values in dynamic queries.\n *\n * @example\n * ```js\n * // Some user-supplied string that may be malicious\n * const untrustedValue = req.body.email;\n *\n * // Without aql tag: BAD! DO NOT DO THIS!\n * const badQuery = `\n *   FOR user IN users\n *   FILTER user.email == \"${untrustedValue}\"\n *   RETURN user\n * `;\n * // e.g. if untrustedValue is '\" || user.admin == true || \"':\n * // Query:\n * //   FOR user IN users\n * //   FILTER user.email == \"\" || user.admin == true || \"\"\n * //   RETURN user\n *\n * // With the aql tag: GOOD! MUCH SAFER!\n * const betterQuery = aql`\n *   FOR user IN users\n *   FILTER user.email == ${untrustedValue}\n *   RETURN user\n * `;\n * // Query:\n * //   FOR user IN users\n * //   FILTER user.email == @value0\n * //   RETURN user\n * // Bind parameters:\n * //   value0 -> untrustedValue\n * ```\n *\n * @example\n * ```js\n * const collection = db.collection(\"some-collection\");\n * const minValue = 23;\n * const result = await db.query(aql`\n *   FOR d IN ${collection}\n *   FILTER d.num > ${minValue}\n *   RETURN d\n * `);\n *\n * // Equivalent raw query object\n * const result2 = await db.query({\n *   query: `\n *     FOR d IN @@collection\n *     FILTER d.num > @minValue\n *     RETURN d\n *   `,\n *   bindVars: {\n *     \"@collection\": collection.name,\n *     minValue: minValue\n *   }\n * });\n * ```\n *\n * @example\n * ```js\n * const collection = db.collection(\"some-collection\");\n * const color = \"green\";\n * const filter = aql`FILTER d.color == ${color}'`;\n * const result = await db.query(aql`\n *   FOR d IN ${collection}\n *   ${filter}\n *   RETURN d\n * `);\n * ```\n */\nexport function aql<T = any>(\n  templateStrings: TemplateStringsArray,\n  ...args: AqlValue[]\n): GeneratedAqlQuery<T> {\n  const strings = [...templateStrings];\n  const bindVars: Record<string, any> = {};\n  const bindValues = [];\n  let query = strings[0];\n  for (let i = 0; i < args.length; i++) {\n    const rawValue = args[i];\n    let value = rawValue;\n    if (isGeneratedAqlQuery(rawValue)) {\n      const src = rawValue._source();\n      if (src.args.length) {\n        query += src.strings[0];\n        args.splice(i, 1, ...src.args);\n        strings.splice(\n          i,\n          2,\n          strings[i] + src.strings[0],\n          ...src.strings.slice(1, src.args.length),\n          src.strings[src.args.length] + strings[i + 1]\n        );\n      } else {\n        query += rawValue.query + strings[i + 1];\n        args.splice(i, 1);\n        strings.splice(i, 2, strings[i] + rawValue.query + strings[i + 1]);\n      }\n      i -= 1;\n      continue;\n    }\n    if (rawValue === undefined) {\n      query += strings[i + 1];\n      continue;\n    }\n    if (isAqlLiteral(rawValue)) {\n      query += `${rawValue.toAQL()}${strings[i + 1]}`;\n      continue;\n    }\n    const index = bindValues.indexOf(rawValue);\n    const isKnown = index !== -1;\n    let name = `value${isKnown ? index : bindValues.length}`;\n    if (\n      isArangoCollection(rawValue) ||\n      isArangoGraph(rawValue) ||\n      isArangoView(rawValue) ||\n      isArangoAnalyzer(rawValue)\n    ) {\n      name = `@${name}`;\n      value = rawValue.name;\n    }\n    if (!isKnown) {\n      bindValues.push(rawValue);\n      bindVars[name] = value;\n    }\n    query += `@${name}${strings[i + 1]}`;\n  }\n  return {\n    query,\n    bindVars,\n    _source: () => ({ strings, args }),\n  };\n}\n\n/**\n * Marks an arbitrary scalar value (i.e. a string, number or boolean) as\n * safe for being inlined directly into AQL queries when used in an `aql`\n * template string, rather than being converted into a bind parameter.\n *\n * **Note**: Nesting `aql` template strings is a much safer alternative for\n * most use cases. This low-level helper function only exists to help with\n * rare edge cases where a trusted AQL query fragment must be read from a\n * string (e.g. when reading query fragments from JSON) and should only be\n * used as a last resort.\n *\n * @example\n * ```js\n * // BAD! DO NOT DO THIS!\n * const sortDirection = literal('ASC');\n *\n * // GOOD! DO THIS INSTEAD!\n * const sortDirection = aql`ASC`;\n * ```\n *\n * @example\n * ```js\n * // BAD! DO NOT DO THIS!\n * const filterColor = literal('FILTER d.color == \"green\"');\n * const result = await db.query(aql`\n *   FOR d IN some-collection\n *   ${filterColor}\n *   RETURN d\n * `);\n *\n * // GOOD! DO THIS INSTEAD!\n * const color = \"green\";\n * const filterColor = aql`FILTER d.color === ${color}`;\n * const result = await db.query(aql`\n *   FOR d IN some-collection\n *   ${filterColor}\n *   RETURN d\n * `);\n * ```\n *\n * @example\n * ```js\n * // WARNING: We explicitly trust the environment variable to be safe!\n * const filter = literal(process.env.FILTER_STATEMENT);\n * const users = await db.query(aql`\n *   FOR user IN users\n *   ${filter}\n *   RETURN user\n * `);\n * ```\n */\nexport function literal(\n  value: string | number | boolean | AqlLiteral | null | undefined\n): AqlLiteral {\n  if (isAqlLiteral(value)) {\n    return value;\n  }\n  return {\n    toAQL() {\n      if (value === undefined) {\n        return \"\";\n      }\n      return String(value);\n    },\n  };\n}\n\n/**\n * Constructs {@link AqlQuery} objects from an array of arbitrary values.\n *\n * **Note**: Nesting `aql` template strings is a much safer alternative\n * for most use cases. This low-level helper function only exists to\n * complement the `aql` tag when constructing complex queries from dynamic\n * arrays of query fragments.\n *\n * @param values - Array of values to join. These values will behave exactly\n * like values interpolated in an `aql` template string.\n * @param sep - Seperator to insert between values. This value will behave\n * exactly like a value passed to {@link literal}, i.e. it will be\n * inlined as-is, rather than being converted into a bind parameter.\n *\n * @example\n * ```js\n * const users = db.collection(\"users\");\n * const filters = [];\n * if (adminsOnly) filters.push(aql`FILTER user.admin`);\n * if (activeOnly) filters.push(aql`FILTER user.active`);\n * const result = await db.query(aql`\n *   FOR user IN ${users}\n *   ${join(filters)}\n *   RETURN user\n * `);\n * ```\n *\n * @example\n * ```js\n * const users = db.collection(\"users\");\n * const keys = [\"jreyes\", \"ghermann\"];\n *\n * // BAD! NEEDLESSLY COMPLEX!\n * const docs = keys.map(key => aql`DOCUMENT(${users}, ${key}`));\n * const result = await db.query(aql`\n *   FOR user IN [\n *     ${join(docs, \", \")}\n *   ]\n *   RETURN user\n * `);\n * // Query:\n * //   FOR user IN [\n * //     DOCUMENT(@@value0, @value1), DOCUMENT(@@value0, @value2)\n * //   ]\n * //   RETURN user\n * // Bind parameters:\n * //   @value0 -> \"users\"\n * //   value1 -> \"jreyes\"\n * //   value2 -> \"ghermann\"\n *\n * // GOOD! MUCH SIMPLER!\n * const result = await db.query(aql`\n *   FOR key IN ${keys}\n *   LET user = DOCUMENT(${users}, key)\n *   RETURN user\n * `);\n * // Query:\n * //   FOR user IN @value0\n * //   LET user = DOCUMENT(@@value1, key)\n * //   RETURN user\n * // Bind parameters:\n * //   value0 -> [\"jreyes\", \"ghermann\"]\n * //   @value1 -> \"users\"\n * ```\n */\nexport function join(values: AqlValue[], sep: string = \" \"): GeneratedAqlQuery {\n  if (!values.length) {\n    return aql``;\n  }\n  if (values.length === 1) {\n    return aql`${values[0]}`;\n  }\n  return aql([\"\", ...Array(values.length - 1).fill(sep), \"\"] as any, ...values);\n}\n"]}