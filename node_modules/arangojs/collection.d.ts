/// <reference types="node" />
/**
 * ```ts
 * import type {
 *   DocumentCollection,
 *   EdgeCollection,
 * } from "arangojs/collection";
 * ```
 *
 * The "collection" module provides collection related types and interfaces
 * for TypeScript.
 *
 * @packageDocumentation
 */
import { AqlLiteral, AqlQuery } from "./aql";
import { ArangoApiResponse } from "./connection";
import { ArrayCursor } from "./cursor";
import { Database } from "./database";
import { Document, DocumentData, DocumentMetadata, DocumentSelector, Edge, EdgeData, ObjectWithKey, Patch } from "./documents";
import { EnsureFulltextIndexOptions, EnsureGeoIndexOptions, EnsureInvertedIndexOptions, EnsurePersistentIndexOptions, EnsureTtlIndexOptions, EnsureMdiIndexOptions, FulltextIndex, GeoIndex, Index, IndexSelector, InvertedIndex, PersistentIndex, TtlIndex, MdiIndex } from "./indexes";
import { Blob } from "./lib/blob";
/**
 * Indicates whether the given value represents an {@link ArangoCollection}.
 *
 * @param collection - A value that might be a collection.
 */
export declare function isArangoCollection(collection: any): collection is ArangoCollection;
/**
 * Coerces the given collection name or {@link ArangoCollection} object to
 * a string representing the collection name.
 *
 * @param collection - Collection name or {@link ArangoCollection} object.
 */
export declare function collectionToString(collection: string | ArangoCollection): string;
/**
 * A marker interface identifying objects that can be used in AQL template
 * strings to create references to ArangoDB collections.
 *
 * See {@link aql!aql}.
 */
export interface ArangoCollection {
    /**
     * @internal
     *
     * Indicates that this object represents an ArangoDB collection.
     */
    readonly isArangoCollection: true;
    /**
     * Name of the collection.
     */
    readonly name: string;
}
/**
 * Integer values indicating the collection type.
 */
export declare enum CollectionType {
    DOCUMENT_COLLECTION = 2,
    EDGE_COLLECTION = 3
}
/**
 * Integer values indicating the collection loading status.
 */
export declare enum CollectionStatus {
    NEWBORN = 1,
    UNLOADED = 2,
    LOADED = 3,
    UNLOADING = 4,
    DELETED = 5,
    LOADING = 6
}
/**
 * Type of key generator.
 */
export type KeyGenerator = "traditional" | "autoincrement" | "uuid" | "padded";
/**
 * Strategy for sharding a collection.
 */
export type ShardingStrategy = "hash" | "enterprise-hash-smart-edge" | "enterprise-hash-smart-vertex" | "community-compat" | "enterprise-compat" | "enterprise-smart-edge-compat";
/**
 * Type of document reference.
 *
 * See {@link DocumentCollection#list} and {@link EdgeCollection#list}.
 *
 * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be
 * replaced with AQL queries.
 */
export type SimpleQueryListType = "id" | "key" | "path";
/**
 * When a validation should be applied.
 *
 * * `"none"`: No validation.
 * * `"new"`: Newly inserted documents are validated.
 * * `"moderate"`: New and modified documents are validated unless the modified
 *   document was already invalid.
 * * `"strict"`: New and modified documents are always validated.
 */
export type ValidationLevel = "none" | "new" | "moderate" | "strict";
/**
 * Write operation that can result in a computed value being computed.
 */
export type WriteOperation = "insert" | "update" | "replace";
/**
 * Represents a bulk operation failure for an individual document.
 */
export type DocumentOperationFailure = {
    /**
     * Indicates that the operation failed.
     */
    error: true;
    /**
     * Human-readable description of the failure.
     */
    errorMessage: string;
    /**
     * Numeric representation of the failure.
     */
    errorNum: number;
};
/**
 * Metadata returned by a document operation.
 */
export type DocumentOperationMetadata = DocumentMetadata & {
    /**
     * Revision of the document that was updated or replaced by this operation.
     */
    _oldRev?: string;
};
/**
 * Properties defining a computed value.
 */
export type ComputedValueProperties = {
    /**
     * Name of the target attribute of the computed value.
     */
    name: string;
    /**
     * AQL `RETURN` expression that computes the value.
     */
    expression: string;
    /**
     * If set to `false`, the computed value will not be applied if the
     * expression evaluates to `null`.
     */
    overwrite: boolean;
    /**
     * Which operations should result in the value being computed.
     */
    computeOn: WriteOperation[];
    /**
     * If set to `false`, the field will be unset if the expression evaluates to
     * `null`. Otherwise the field will be set to the value `null`. Has no effect
     * if `overwrite` is set to `false`.
     */
    keepNull: boolean;
    /**
     * Whether the write operation should fail if the expression produces a
     * warning.
     */
    failOnWarning: boolean;
};
/**
 * General information about a collection.
 */
export type CollectionMetadata = {
    /**
     * Collection name.
     */
    name: string;
    /**
     * A globally unique identifier for this collection.
     */
    globallyUniqueId: string;
    /**
     * An integer indicating the collection loading status.
     */
    status: CollectionStatus;
    /**
     * An integer indicating the collection type.
     */
    type: CollectionType;
    /**
     * @internal
     *
     * Whether the collection is a system collection.
     */
    isSystem: boolean;
};
/**
 * An object defining the collection's key generation.
 */
export type CollectionKeyProperties = {
    /**
     * Type of key generator to use.
     */
    type: KeyGenerator;
    /**
     * Whether documents can be created with a user-specified `_key` attribute.
     */
    allowUserKeys: boolean;
    /**
     * (Autoincrement only.) How many steps to increment the key each time.
     */
    increment?: number;
    /**
     * (Autoincrement only.) Initial offset for the key.
     */
    offset?: number;
    /**
     * Most recent key that has been generated.
     */
    lastValue: number;
};
/**
 * Properties for validating documents in a collection.
 */
export type SchemaProperties = {
    /**
     * Type of document validation.
     */
    type: "json";
    /**
     * JSON Schema description of the validation schema for documents.
     */
    rule: any;
    /**
     * When validation should be applied.
     */
    level: ValidationLevel;
    /**
     * Message to be used if validation fails.
     */
    message: string;
};
/**
 * An object defining the properties of a collection.
 */
export type CollectionProperties = {
    /**
     * A human-readable representation of the collection loading status.
     */
    statusString: string;
    /**
     * Whether data should be synchronized to disk before returning from
     * a document create, update, replace or removal operation.
     */
    waitForSync: boolean;
    /**
     * An object defining the collection's key generation.
     */
    keyOptions: CollectionKeyProperties;
    /**
     * Properties for validating documents in the collection.
     */
    schema: SchemaProperties | null;
    /**
     * (Cluster only.) Write concern for this collection.
     */
    writeConcern: number;
    /**
     * (Cluster only.) Number of shards of this collection.
     */
    numberOfShards?: number;
    /**
     * (Cluster only.) Keys of this collection that will be used for
     * sharding.
     */
    shardKeys?: string[];
    /**
     * (Cluster only.) Replication factor of the collection.
     */
    replicationFactor?: number | "satellite";
    /**
     * (Cluster only.) Sharding strategy of the collection.
     */
    shardingStrategy?: ShardingStrategy;
    /**
     * (Enterprise Edition cluster only.) If set to a collection name, sharding
     * of the new collection will follow the rules for that collection. As long
     * as the new collection exists, the indicated collection can not be dropped.
     */
    distributeShardsLike?: string;
    /**
     * (Enterprise Edition cluster only.) Attribute containing the shard key
     * value of the referred-to smart join collection.
     */
    smartJoinAttribute?: string;
    /**
     * (Enterprise Edition cluster only.) Attribute used for sharding.
     */
    smartGraphAttribute?: string;
    /**
     * Computed values applied to documents in this collection.
     */
    computedValues: ComputedValueProperties[];
    /**
     * Whether the in-memory hash cache is enabled for this collection.
     */
    cacheEnabled: boolean;
    /**
     * Whether the newer revision-based replication protocol is enabled for
     * this collection.
     */
    syncByRevision: boolean;
    /**
     * (Enterprise Edition only.) Whether the collection is used in a SmartGraph or EnterpriseGraph.
     */
    isSmart?: boolean;
    /**
     * (Enterprise Edition only.) Whether the SmartGraph this collection belongs to is disjoint.
     */
    isDisjoint?: string;
};
/**
 * Options for creating a computed value.
 */
export type ComputedValueOptions = {
    /**
     * Name of the target attribute of the computed value.
     */
    name: string;
    /**
     * AQL `RETURN` expression that computes the value.
     *
     * Note that when passing an AQL query object, the `bindVars` will be ignored.
     */
    expression: string | AqlLiteral | AqlQuery;
    /**
     * If set to `false`, the computed value will not be applied if the
     * expression evaluates to `null`.
     *
     * Default: `true`
     */
    overwrite?: boolean;
    /**
     * Which operations should result in the value being computed.
     *
     * Default: `["insert", "update", "replace"]`
     */
    computeOn?: WriteOperation[];
    /**
     * If set to `false`, the field will be unset if the expression evaluates to
     * `null`. Otherwise the field will be set to the value `null`. Has no effect
     * if `overwrite` is set to `false`.
     *
     * Default: `true`
     */
    keepNull?: boolean;
    /**
     * Whether the write operation should fail if the expression produces a
     * warning.
     *
     * Default: `false`
     */
    failOnWarning?: boolean;
};
/**
 * Options for validating collection documents.
 */
export type SchemaOptions = {
    /**
     * JSON Schema description of the validation schema for documents.
     */
    rule: any;
    /**
     * When validation should be applied.
     *
     * Default: `"strict"`
     */
    level?: ValidationLevel;
    /**
     * Message to be used if validation fails.
     */
    message?: string;
};
/**
 * Options for setting a collection's properties.
 *
 * See {@link DocumentCollection#properties} and {@link EdgeCollection#properties}.
 */
export type CollectionPropertiesOptions = {
    /**
     * Whether data should be synchronized to disk before returning from
     * a document create, update, replace or removal operation.
     */
    waitForSync?: boolean;
    /**
     * (Cluster only.) How many copies of each document should be kept in the
     * cluster.
     *
     * Default: `1`
     */
    replicationFactor?: number | "satellite";
    /**
     * (Cluster only.) Write concern for this collection.
     */
    writeConcern?: number;
    /**
     * Options for validating documents in this collection.
     */
    schema?: SchemaOptions;
    /**
     * Computed values to apply to documents in this collection.
     */
    computedValues?: ComputedValueOptions[];
    /**
     * Whether the in-memory hash cache is enabled for this collection.
     *
     * Default: `false`
     */
    cacheEnabled?: boolean;
};
/**
 * Options for retrieving a collection checksum.
 */
export type CollectionChecksumOptions = {
    /**
     * If set to `true`, revision IDs will be included in the calculation
     * of the checksum.
     *
     * Default: `false`
     */
    withRevisions?: boolean;
    /**
     * If set to `true`, document data will be included in the calculation
     * of the checksum.
     *
     * Default: `false`
     */
    withData?: boolean;
};
/**
 * Options for dropping collections.
 */
export type CollectionDropOptions = {
    /**
     * Whether the collection is a system collection. If the collection is a
     * system collection, this option must be set to `true` or ArangoDB will
     * refuse to drop the collection.
     *
     * Default: `false`
     */
    isSystem?: boolean;
};
/**
 * An object defining the collection's key generation.
 */
export type CollectionKeyOptions = {
    /**
     * Type of key generator to use.
     */
    type?: KeyGenerator;
    /**
     * Unless set to `false`, documents can be created with a user-specified
     * `_key` attribute.
     *
     * Default: `true`
     */
    allowUserKeys?: boolean;
    /**
     * (Autoincrement only.) How many steps to increment the key each time.
     */
    increment?: number;
    /**
     * (Autoincrement only.) Initial offset for the key.
     */
    offset?: number;
};
/**
 * Options for creating a collection.
 *
 * See {@link database.Database#createCollection}, {@link database.Database#createEdgeCollection}
 * and {@link DocumentCollection#create} or {@link EdgeCollection#create}.
 */
export type CreateCollectionOptions = {
    /**
     * If set to `true`, data will be synchronized to disk before returning from
     * a document create, update, replace or removal operation.
     *
     * Default: `false`
     */
    waitForSync?: boolean;
    /**
     * @internal
     *
     * Whether the collection should be created as a system collection.
     *
     * Default: `false`
     */
    isSystem?: boolean;
    /**
     * An object defining the collection's key generation.
     */
    keyOptions?: CollectionKeyOptions;
    /**
     * Options for validating documents in the collection.
     */
    schema?: SchemaOptions;
    /**
     * (Cluster only.) Unless set to `false`, the server will wait for all
     * replicas to create the collection before returning.
     *
     * Default: `true`
     */
    waitForSyncReplication?: boolean;
    /**
     * (Cluster only.) Unless set to `false`, the server will check whether
     * enough replicas are available at creation time and bail out otherwise.
     *
     * Default: `true`
     */
    enforceReplicationFactor?: boolean;
    /**
     * (Cluster only.) Number of shards to distribute the collection across.
     *
     * Default: `1`
     */
    numberOfShards?: number;
    /**
     * (Cluster only.) Document attributes to use to determine the target shard
     * for each document.
     *
     * Default: `["_key"]`
     */
    shardKeys?: string[];
    /**
     * (Cluster only.) How many copies of each document should be kept in the
     * cluster.
     *
     * Default: `1`
     */
    replicationFactor?: number;
    /**
     * (Cluster only.) Write concern for this collection.
     */
    writeConcern?: number;
    /**
     * (Cluster only.) Sharding strategy to use.
     */
    shardingStrategy?: ShardingStrategy;
    /**
     * (Enterprise Edition cluster only.) If set to a collection name, sharding
     * of the new collection will follow the rules for that collection. As long
     * as the new collection exists, the indicated collection can not be dropped.
     */
    distributeShardsLike?: string;
    /**
     * (Enterprise Edition cluster only.) Attribute containing the shard key
     * value of the referred-to smart join collection.
     */
    smartJoinAttribute?: string;
    /**
     * (Enterprise Edition cluster only.) Attribute used for sharding.
     */
    smartGraphAttribute?: string;
    /**
     * Computed values to apply to documents in this collection.
     */
    computedValues?: ComputedValueOptions[];
    /**
     * Whether the in-memory hash cache is enabled for this collection.
     */
    cacheEnabled?: boolean;
};
/**
 * Options for checking whether a document exists in a collection.
 */
export type DocumentExistsOptions = {
    /**
     * If set to a document revision, the document will only match if its `_rev`
     * matches the given revision.
     */
    ifMatch?: string;
    /**
     * If set to a document revision, the document will only match if its `_rev`
     * does not match the given revision.
     */
    ifNoneMatch?: string;
};
/**
 * Options for retrieving a document from a collection.
 */
export type CollectionReadOptions = {
    /**
     * If set to `true`, `null` is returned instead of an exception being thrown
     * if the document does not exist.
     */
    graceful?: boolean;
    /**
     * If set to `true`, the request will explicitly permit ArangoDB to return a
     * potentially dirty or stale result and arangojs will load balance the
     * request without distinguishing between leaders and followers.
     */
    allowDirtyRead?: boolean;
    /**
     * If set to a document revision, the request will fail with an error if the
     * document exists but its `_rev` does not match the given revision.
     */
    ifMatch?: string;
    /**
     * If set to a document revision, the request will fail with an error if the
     * document exists and its `_rev` matches the given revision. Note that an
     * `HttpError` with code 304 will be thrown instead of an `ArangoError`.
     */
    ifNoneMatch?: string;
};
/**
 * Options for retrieving multiple documents from a collection.
 */
export type CollectionBatchReadOptions = {
    /**
     * If set to `true`, the request will explicitly permit ArangoDB to return a
     * potentially dirty or stale result and arangojs will load balance the
     * request without distinguishing between leaders and followers.
     */
    allowDirtyRead?: boolean;
};
/**
 * Options for inserting a new document into a collection.
 */
export type CollectionInsertOptions = {
    /**
     * If set to `true`, data will be synchronized to disk before returning.
     *
     * Default: `false`
     */
    waitForSync?: boolean;
    /**
     * If set to `true`, no data will be returned by the server. This option can
     * be used to reduce network traffic.
     *
     * Default: `false`
     */
    silent?: boolean;
    /**
     * If set to `true`, the complete new document will be returned as the `new`
     * property on the result object. Has no effect if `silent` is set to `true`.
     *
     * Default: `false`
     */
    returnNew?: boolean;
    /**
     * If set to `true`, the complete old document will be returned as the `old`
     * property on the result object. Has no effect if `silent` is set to `true`.
     * This option is only available when `overwriteMode` is set to `"update"` or
     * `"replace"`.
     *
     * Default: `false`
     */
    returnOld?: boolean;
    /**
     * Defines what should happen if a document with the same `_key` or `_id`
     * already exists, instead of throwing an exception.
     *
     * Default: `"conflict"
     */
    overwriteMode?: "ignore" | "update" | "replace" | "conflict";
    /**
     * If set to `false`, object properties that already exist in the old
     * document will be overwritten rather than merged when an existing document
     * with the same `_key` or `_id` is updated. This does not affect arrays.
     *
     * Default: `true`
     */
    mergeObjects?: boolean;
    /**
     * If set to `true`, new entries will be added to in-memory index caches if
     * document insertions affect the edge index or cache-enabled persistent
     * indexes.
     *
     * Default: `false`
     */
    refillIndexCaches?: boolean;
    /**
     * If set, the attribute with the name specified by the option is looked up
     * in the stored document and the attribute value is compared numerically to
     * the value of the versioning attribute in the supplied document that is
     * supposed to update/replace it.
     */
    versionAttribute?: string;
};
/**
 * Options for replacing an existing document in a collection.
 */
export type CollectionReplaceOptions = {
    /**
     * If set to `true`, data will be synchronized to disk before returning.
     *
     * Default: `false`
     */
    waitForSync?: boolean;
    /**
     * If set to `true`, no data will be returned by the server. This option can
     * be used to reduce network traffic.
     *
     * Default: `false`
     */
    silent?: boolean;
    /**
     * If set to `true`, the complete new document will be returned as the `new`
     * property on the result object. Has no effect if `silent` is set to `true`.
     *
     * Default: `false`
     */
    returnNew?: boolean;
    /**
     * If set to `false`, the existing document will only be modified if its
     * `_rev` property matches the same property on the new data.
     *
     * Default: `true`
     */
    ignoreRevs?: boolean;
    /**
     * If set to `true`, the complete old document will be returned as the `old`
     * property on the result object. Has no effect if `silent` is set to `true`.
     *
     * Default: `false`
     */
    returnOld?: boolean;
    /**
     * If set to a document revision, the document will only be replaced if its
     * `_rev` matches the given revision.
     */
    ifMatch?: string;
    /**
     * If set to `true`, existing entries in in-memory index caches will be
     * updated if document replacements affect the edge index or cache-enabled
     * persistent indexes.
     *
     * Default: `false`
     */
    refillIndexCaches?: boolean;
    /**
     * If set, the attribute with the name specified by the option is looked up
     * in the stored document and the attribute value is compared numerically to
     * the value of the versioning attribute in the supplied document that is
     * supposed to update/replace it.
     */
    versionAttribute?: string;
};
/**
 * Options for updating a document in a collection.
 */
export type CollectionUpdateOptions = {
    /**
     * If set to `true`, data will be synchronized to disk before returning.
     *
     * Default: `false`
     */
    waitForSync?: boolean;
    /**
     * If set to `true`, no data will be returned by the server. This option can
     * be used to reduce network traffic.
     *
     * Default: `false`
     */
    silent?: boolean;
    /**
     * If set to `true`, the complete new document will be returned as the `new`
     * property on the result object. Has no effect if `silent` is set to `true`.
     *
     * Default: `false`
     */
    returnNew?: boolean;
    /**
     * If set to `false`, the existing document will only be modified if its
     * `_rev` property matches the same property on the new data.
     *
     * Default: `true`
     */
    ignoreRevs?: boolean;
    /**
     * If set to `true`, the complete old document will be returned as the `old`
     * property on the result object. Has no effect if `silent` is set to `true`.
     *
     * Default: `false`
     */
    returnOld?: boolean;
    /**
     * If set to `false`, properties with a value of `null` will be removed from
     * the new document.
     *
     * Default: `true`
     */
    keepNull?: boolean;
    /**
     * If set to `false`, object properties that already exist in the old
     * document will be overwritten rather than merged. This does not affect
     * arrays.
     *
     * Default: `true`
     */
    mergeObjects?: boolean;
    /**
     * If set to a document revision, the document will only be updated if its
     * `_rev` matches the given revision.
     */
    ifMatch?: string;
    /**
     * If set to `true`, existing entries in in-memory index caches will be
     * updated if document updates affect the edge index or cache-enabled
     * persistent indexes.
     *
     * Default: `false`
     */
    refillIndexCaches?: boolean;
    /**
     * If set, the attribute with the name specified by the option is looked up
     * in the stored document and the attribute value is compared numerically to
     * the value of the versioning attribute in the supplied document that is
     * supposed to update/replace it.
     */
    versionAttribute?: string;
};
/**
 * Options for removing a document from a collection.
 */
export type CollectionRemoveOptions = {
    /**
     * If set to `true`, changes will be synchronized to disk before returning.
     *
     * Default: `false`
     */
    waitForSync?: boolean;
    /**
     * If set to `true`, the complete old document will be returned as the `old`
     * property on the result object. Has no effect if `silent` is set to `true`.
     *
     * Default: `false`
     */
    returnOld?: boolean;
    /**
     * If set to `true`, no data will be returned by the server. This option can
     * be used to reduce network traffic.
     *
     * Default: `false`
     */
    silent?: boolean;
    /**
     * If set to a document revision, the document will only be removed if its
     * `_rev` matches the given revision.
     */
    ifMatch?: string;
    /**
     * If set to `true`, existing entries in in-memory index caches will be
     * deleted if document removals affect the edge index or cache-enabled
     * persistent indexes.
     *
     * Default: `false`
     */
    refillIndexCaches?: boolean;
};
/**
 * Options for bulk importing documents into a collection.
 */
export type CollectionImportOptions = {
    /**
     * (Edge collections only.) Prefix to prepend to `_from` attribute values.
     */
    fromPrefix?: string;
    /**
     * (Edge collections only.) Prefix to prepend to `_to` attribute values.
     */
    toPrefix?: string;
    /**
     * If set to `true`, the collection is truncated before the data is imported.
     *
     * Default: `false`
     */
    overwrite?: boolean;
    /**
     * Whether to wait for the documents to have been synced to disk.
     */
    waitForSync?: boolean;
    /**
     * Controls behavior when a unique constraint is violated on the document key.
     *
     * * `"error"`: the document will not be imported.
     * * `"update`: the document will be merged into the existing document.
     * * `"replace"`: the document will replace the existing document.
     * * `"ignore"`: the document will not be imported and the unique constraint
     *   error will be ignored.
     *
     * Default: `"error"`
     */
    onDuplicate?: "error" | "update" | "replace" | "ignore";
    /**
     * If set to `true`, the import will abort if any error occurs.
     */
    complete?: boolean;
    /**
     * Whether the response should contain additional details about documents
     * that could not be imported.
     */
    details?: boolean;
};
/**
 * Options for retrieving a document's edges from a collection.
 */
export type CollectionEdgesOptions = {
    /**
     * If set to `true`, the request will explicitly permit ArangoDB to return a
     * potentially dirty or stale result and arangojs will load balance the
     * request without distinguishing between leaders and followers.
     */
    allowDirtyRead?: boolean;
};
/**
 * Options for retrieving documents by example.
 *
 * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be
 * replaced with AQL queries.
 */
export type SimpleQueryByExampleOptions = {
    /**
     * Number of documents to skip in the query.
     */
    skip?: number;
    /**
     * Maximum number of documents to return.
     */
    limit?: number;
    /**
     * Number of result values to be transferred by the server in each
     * network roundtrip (or "batch").
     *
     * Must be greater than zero.
     *
     * See also {@link database.QueryOptions}.
     */
    batchSize?: number;
    /**
     * Time-to-live for the cursor in seconds. The cursor results may be
     * garbage collected by ArangoDB after this much time has passed.
     *
     * See also {@link database.QueryOptions}.
     */
    ttl?: number;
};
/**
 * Options for retrieving all documents in a collection.
 *
 * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be
 * replaced with AQL queries.
 */
export type SimpleQueryAllOptions = {
    /**
     * Number of documents to skip in the query.
     */
    skip?: number;
    /**
     * Maximum number of documents to return.
     */
    limit?: number;
    /**
     * Number of result values to be transferred by the server in each
     * network roundtrip (or "batch").
     *
     * Must be greater than zero.
     *
     * See also {@link database.QueryOptions}.
     */
    batchSize?: number;
    /**
     * Time-to-live for the cursor in seconds. The cursor results may be
     * garbage collected by ArangoDB after this much time has passed.
     *
     * See also {@link database.QueryOptions}.
     */
    ttl?: number;
    /**
     * If set to `true`, the query will be executed as a streaming query.
     *
     * See also {@link database.QueryOptions}.
     */
    stream?: boolean;
};
/**
 * Options for updating documents by example.
 *
 * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be
 * replaced with AQL queries.
 */
export type SimpleQueryUpdateByExampleOptions = {
    /**
     * If set to `false`, properties with a value of `null` will be removed from
     * the new document.
     *
     * Default: `true`
     */
    keepNull?: boolean;
    /**
     * If set to `true`, the request will wait until all modifications have been
     * synchronized to disk before returning successfully.
     *
     * Default: `false`
     */
    waitForSync?: boolean;
    /**
     * Maximum number of documents to return.
     */
    limit?: number;
    /**
     * If set to `false`, object properties that already exist in the old
     * document will be overwritten rather than merged. This does not affect
     * arrays.
     *
     * Default: `true`
     */
    mergeObjects?: boolean;
};
/**
 * Options for removing documents by example.
 *
 * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be
 * replaced with AQL queries.
 */
export type SimpleQueryRemoveByExampleOptions = {
    /**
     * If set to `true`, the request will wait until all modifications have been
     * synchronized to disk before returning successfully.
     *
     * Default: `false`
     */
    waitForSync?: boolean;
    /**
     * Maximum number of documents to return.
     */
    limit?: number;
};
/**
 * Options for replacing documents by example.
 *
 * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be
 * replaced with AQL queries.
 */
export type SimpleQueryReplaceByExampleOptions = SimpleQueryRemoveByExampleOptions;
/**
 * Options for removing documents by keys.
 *
 * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be
 * replaced with AQL queries.
 */
export type SimpleQueryRemoveByKeysOptions = {
    /**
     * If set to `true`, the complete old document will be returned as the `old`
     * property on the result object. Has no effect if `silent` is set to `true`.
     *
     * Default: `false`
     */
    returnOld?: boolean;
    /**
     * If set to `true`, no data will be returned by the server. This option can
     * be used to reduce network traffic.
     *
     * Default: `false`
     */
    silent?: boolean;
    /**
     * If set to `true`, the request will wait until all modifications have been
     * synchronized to disk before returning successfully.
     *
     * Default: `false`
     */
    waitForSync?: boolean;
};
/**
 * Options for performing a fulltext query.
 *
 * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be
 * replaced with AQL queries.
 */
export type SimpleQueryFulltextOptions = {
    /**
     * Unique identifier of the fulltext index to use to perform the query.
     */
    index?: string;
    /**
     * Maximum number of documents to return.
     */
    limit?: number;
    /**
     * Number of documents to skip in the query.
     */
    skip?: number;
};
/**
 * Options for performing a graph traversal.
 *
 * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and are
 * no longer supported in ArangoDB 3.12. They can be replaced with AQL queries.
 */
export type TraversalOptions = {
    /**
     * A string evaluating to the body of a JavaScript function to be executed
     * on the server to initialize the traversal result object.
     *
     * The code has access to two variables: `config`, `result`.
     * The code may modify the `result` object.
     *
     * **Note**: This code will be evaluated and executed on the
     * server inside ArangoDB's embedded JavaScript environment and can not
     * access any other variables.
     *
     * See the official ArangoDB documentation for
     * [the JavaScript `@arangodb` module](https://www.arangodb.com/docs/stable/appendix-java-script-modules-arango-db.html)
     * for information about accessing the database from within ArangoDB's
     * server-side JavaScript environment.
     */
    init?: string;
    /**
     * A string evaluating to the body of a JavaScript function to be executed
     * on the server to filter nodes.
     *
     * The code has access to three variables: `config`, `vertex`, `path`.
     * The code may include a return statement for the following values:
     *
     * * `"exclude"`: The vertex will not be visited.
     * * `"prune"`: The edges of the vertex will not be followed.
     * * `""` or `undefined`: The vertex will be visited and its edges followed.
     * * an array including any of the above values.
     *
     * **Note**: This code will be evaluated and executed on the
     * server inside ArangoDB's embedded JavaScript environment and can not
     * access any other variables.
     *
     * See the official ArangoDB documentation for
     * [the JavaScript `@arangodb` module](https://www.arangodb.com/docs/stable/appendix-java-script-modules-arango-db.html)
     * for information about accessing the database from within ArangoDB's
     * server-side JavaScript environment.
     */
    filter?: string;
    /**
     * A string evaluating to the body of a JavaScript function to be executed
     * on the server to sort edges if `expander` is not set.
     *
     * The code has access to two variables representing edges: `l`, `r`.
     * The code must return `-1` if `l < r`, `1` if `l > r` or `0` if both
     * values are equal.
     *
     * **Note**: This code will be evaluated and executed on the
     * server inside ArangoDB's embedded JavaScript environment and can not
     * access any other variables.
     *
     * See the official ArangoDB documentation for
     * [the JavaScript `@arangodb` module](https://www.arangodb.com/docs/stable/appendix-java-script-modules-arango-db.html)
     * for information about accessing the database from within ArangoDB's
     * server-side JavaScript environment.
     */
    sort?: string;
    /**
     * A string evaluating to the body of a JavaScript function to be executed
     * on the server when a node is visited.
     *
     * The code has access to five variables: `config`, `result`, `vertex`,
     * `path`, `connected`.
     * The code may modify the `result` object.
     *
     * **Note**: This code will be evaluated and executed on the
     * server inside ArangoDB's embedded JavaScript environment and can not
     * access any other variables.
     *
     * See the official ArangoDB documentation for
     * [the JavaScript `@arangodb` module](https://www.arangodb.com/docs/stable/appendix-java-script-modules-arango-db.html)
     * for information about accessing the database from within ArangoDB's
     * server-side JavaScript environment.
     */
    visitor?: string;
    /**
     * A string evaluating to the body of a JavaScript function to be executed
     * on the server to use when `direction` is not set.
     *
     * The code has access to three variables: `config`, `vertex`, `path`.
     * The code must return an array of objects with `edge` and `vertex`
     * attributes representing the connections for the vertex.
     *
     * **Note**: This code will be evaluated and executed on the
     * server inside ArangoDB's embedded JavaScript environment and can not
     * access any other variables.
     *
     * See the official ArangoDB documentation for
     * [the JavaScript `@arangodb` module](https://www.arangodb.com/docs/stable/appendix-java-script-modules-arango-db.html)
     * for information about accessing the database from within ArangoDB's
     * server-side JavaScript environment.
     */
    expander?: string;
    /**
     * Direction of the traversal, relative to the starting vertex if `expander`
     * is not set.
     */
    direction?: "inbound" | "outbound" | "any";
    /**
     * Item iteration order.
     */
    itemOrder?: "forward" | "backward";
    /**
     * Traversal strategy.
     */
    strategy?: "depthfirst" | "breadthfirst";
    /**
     * Traversal order.
     */
    order?: "preorder" | "postorder" | "preorder-expander";
    /**
     * Specifies uniqueness for vertices and edges.
     */
    uniqueness?: {
        /**
         * Uniqueness for vertices.
         */
        vertices?: "none" | "global" | "path";
        /**
         * Uniqueness for edges.
         */
        edges?: "none" | "global" | "path";
    };
    /**
     * If specified, only nodes in at least this depth will be visited.
     */
    minDepth?: number;
    /**
     * If specified, only nodes in at most this depth will be visited.
     */
    maxDepth?: number;
    /**
     * Maximum number of iterations before a traversal is aborted because of a
     * potential endless loop.
     */
    maxIterations?: number;
};
/**
 * Result of a collection bulk import.
 */
export type CollectionImportResult = {
    /**
     * Whether the import failed.
     */
    error: false;
    /**
     * Number of new documents imported.
     */
    created: number;
    /**
     * Number of documents that failed with an error.
     */
    errors: number;
    /**
     * Number of empty documents.
     */
    empty: number;
    /**
     * Number of documents updated.
     */
    updated: number;
    /**
     * Number of documents that failed with an error that is ignored.
     */
    ignored: number;
    /**
     * Additional details about any errors encountered during the import.
     */
    details?: string[];
};
/**
 * Result of retrieving edges in a collection.
 */
export type CollectionEdgesResult<T extends Record<string, any> = any> = {
    edges: Edge<T>[];
    stats: {
        scannedIndex: number;
        filtered: number;
    };
};
/**
 * Result of removing documents by an example.
 *
 * See {@link DocumentCollection#removeByExample} and {@link EdgeCollection#removeByExample}.
 *
 * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be
 * replaced with AQL queries.
 */
export type SimpleQueryRemoveByExampleResult = {
    /**
     * Number of documents removed.
     */
    deleted: number;
};
/**
 * Result of replacing documents by an example.
 *
 * See {@link DocumentCollection#replaceByExample} and {@link EdgeCollection#replaceByExample}.
 *
 * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be
 * replaced with AQL queries.
 */
export type SimpleQueryReplaceByExampleResult = {
    /**
     * Number of documents replaced.
     */
    replaced: number;
};
/**
 * Result of updating documents by an example.
 *
 * See {@link DocumentCollection#updateByExample} and {@link EdgeCollection#updateByExample}.
 *
 * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be
 * replaced with AQL queries.
 */
export type SimpleQueryUpdateByExampleResult = {
    /**
     * Number of documents updated.
     */
    updated: number;
};
/**
 * Result of removing documents by keys.
 *
 * See {@link DocumentCollection#removeByKeys} and {@link EdgeCollection#removeByKeys}.
 *
 * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be
 * replaced with AQL queries.
 */
export type SimpleQueryRemoveByKeysResult<T extends Record<string, any> = any> = {
    /**
     * Number of documents removed.
     */
    removed: number;
    /**
     * Number of documents not removed.
     */
    ignored: number;
    /**
     * Documents that have been removed.
     */
    old?: DocumentMetadata[] | Document<T>[];
};
/**
 * Represents an document collection in a {@link database.Database}.
 *
 * See {@link EdgeCollection} for a variant of this interface more suited for
 * edge collections.
 *
 * When using TypeScript, collections can be cast to a specific document data
 * type to increase type safety.
 *
 * @param T - Type to use for document data. Defaults to `any`.
 *
 * @example
 * ```ts
 * interface Person {
 *   name: string;
 * }
 * const db = new Database();
 * const documents = db.collection("persons") as DocumentCollection<Person>;
 * ```
 */
export interface DocumentCollection<T extends Record<string, any> = any> extends ArangoCollection {
    /**
     * Checks whether the collection exists.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * const result = await collection.exists();
     * // result indicates whether the collection exists
     * ```
     */
    exists(): Promise<boolean>;
    /**
     * Retrieves general information about the collection.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * const data = await collection.get();
     * // data contains general information about the collection
     * ```
     */
    get(): Promise<ArangoApiResponse<CollectionMetadata>>;
    /**
     * Creates a collection with the given `options` and the instance's name.
     *
     * See also {@link database.Database#createCollection} and
     * {@link database.Database#createEdgeCollection}.
     *
     * **Note**: When called on an {@link EdgeCollection} instance in TypeScript,
     * the `type` option must still be set to the correct {@link CollectionType}.
     * Otherwise this will result in the collection being created with the
     * default type (i.e. as a document collection).
     *
     * @param options - Options for creating the collection.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("potatoes");
     * await collection.create();
     * // the document collection "potatoes" now exists
     * ```
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("friends");
     * await collection.create({ type: CollectionType.EDGE_COLLECTION });
     * // the edge collection "friends" now exists
     * ```
     *
     * @example
     * ```ts
     * interface Friend {
     *   startDate: number;
     *   endDate?: number;
     * }
     * const db = new Database();
     * const collection = db.collection("friends") as EdgeCollection<Friend>;
     * // even in TypeScript you still need to indicate the collection type
     * // if you want to create an edge collection
     * await collection.create({ type: CollectionType.EDGE_COLLECTION });
     * // the edge collection "friends" now exists
     * ```
     */
    create(options?: CreateCollectionOptions & {
        type?: CollectionType;
    }): Promise<ArangoApiResponse<CollectionMetadata & CollectionProperties>>;
    /**
     * Retrieves the collection's properties.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * const data = await collection.properties();
     * // data contains the collection's properties
     * ```
     */
    properties(): Promise<ArangoApiResponse<CollectionMetadata & CollectionProperties>>;
    /**
     * Replaces the properties of the collection.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * const result = await collection.setProperties({ waitForSync: true });
     * // the collection will now wait for data being written to disk
     * // whenever a document is changed
     * ```
     */
    properties(properties: CollectionPropertiesOptions): Promise<ArangoApiResponse<CollectionMetadata & CollectionProperties>>;
    /**
     * Retrieves information about the number of documents in a collection.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * const data = await collection.count();
     * // data contains the collection's count
     * ```
     */
    count(): Promise<ArangoApiResponse<CollectionMetadata & CollectionProperties & {
        count: number;
    }>>;
    /**
     * (RocksDB only.) Instructs ArangoDB to recalculate the collection's
     * document count to fix any inconsistencies.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("inconsistent-collection");
     * const badData = await collection.count();
     * // oh no, the collection count looks wrong -- fix it!
     * await collection.recalculateCount();
     * const goodData = await collection.count();
     * // goodData contains the collection's improved count
     * ```
     */
    recalculateCount(): Promise<boolean>;
    /**
     * Retrieves statistics for a collection.
     *
     * @param details - whether to return extended storage engine-specific details
     * to the figures, which may cause additional load and impact performance
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * const data = await collection.figures();
     * // data contains the collection's figures
     * ```
     */
    figures(details?: boolean): Promise<ArangoApiResponse<CollectionMetadata & CollectionProperties & {
        count: number;
        figures: Record<string, any>;
    }>>;
    /**
     * Retrieves the collection revision ID.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * const data = await collection.revision();
     * // data contains the collection's revision
     * ```
     */
    revision(): Promise<ArangoApiResponse<CollectionMetadata & CollectionProperties & {
        revision: string;
    }>>;
    /**
     * Retrieves the collection checksum.
     *
     * @param options - Options for retrieving the checksum.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * const data = await collection.checksum();
     * // data contains the collection's checksum
     * ```
     */
    checksum(options?: CollectionChecksumOptions): Promise<ArangoApiResponse<CollectionMetadata & {
        revision: string;
        checksum: string;
    }>>;
    /**
     * (RocksDB only.) Instructs ArangoDB to load as many indexes of the
     * collection into memory as permitted by the memory limit.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("indexed-collection");
     * await collection.loadIndexes();
     * // the indexes are now loaded into memory
     * ```
     */
    loadIndexes(): Promise<boolean>;
    /**
     * Renames the collection and updates the instance's `name` to `newName`.
     *
     * Additionally removes the instance from the {@link database.Database}'s internal
     * cache.
     *
     * **Note**: Renaming collections may not be supported when ArangoDB is
     * running in a cluster configuration.
     *
     * @param newName - The new name of the collection.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection1 = db.collection("some-collection");
     * await collection1.rename("other-collection");
     * const collection2 = db.collection("some-collection");
     * const collection3 = db.collection("other-collection");
     * // Note all three collection instances are different objects but
     * // collection1 and collection3 represent the same ArangoDB collection!
     * ```
     */
    rename(newName: string): Promise<ArangoApiResponse<CollectionMetadata>>;
    /**
     * Deletes all documents in the collection.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * await collection.truncate();
     * // millions of documents cry out in terror and are suddenly silenced,
     * // the collection "some-collection" is now empty
     * ```
     */
    truncate(): Promise<ArangoApiResponse<CollectionMetadata>>;
    /**
     * Deletes the collection from the database.
     *
     * @param options - Options for dropping the collection.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * await collection.drop();
     * // The collection "some-collection" is now an ex-collection
     * ```
     */
    drop(options?: CollectionDropOptions): Promise<ArangoApiResponse<Record<string, never>>>;
    /**
     * Retrieves the `shardId` of the shard responsible for the given document.
     *
     * @param document - Document in the collection to look up the `shardId` of.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * const responsibleShard = await collection.getResponsibleShard();
     * ```
     */
    getResponsibleShard(document: Partial<Document<T>>): Promise<string>;
    /**
     * Derives a document `_id` from the given selector for this collection.
     *
     * Throws an exception when passed a document or `_id` from a different
     * collection.
     *
     * @param selector - Document `_key`, `_id` or object with either of those
     * properties (e.g. a document from this collection).
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * const meta = await collection.save({ foo: "bar" }, { returnNew: true });
     * const doc = meta.new;
     * console.log(collection.documentId(meta)); // via meta._id
     * console.log(collection.documentId(doc)); // via doc._id
     * console.log(collection.documentId(meta._key)); // also works
     * ```
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection1 = db.collection("some-collection");
     * const collection2 = db.collection("other-collection");
     * const meta = await collection1.save({ foo: "bar" });
     * // Mixing collections is usually a mistake
     * console.log(collection1.documentId(meta)); // ok: same collection
     * console.log(collection2.documentId(meta)); // throws: wrong collection
     * console.log(collection2.documentId(meta._id)); // also throws
     * console.log(collection2.documentId(meta._key)); // ok but wrong collection
     * ```
     */
    documentId(selector: DocumentSelector): string;
    /**
     * Checks whether a document matching the given key or id exists in this
     * collection.
     *
     * Throws an exception when passed a document or `_id` from a different
     * collection.
     *
     * @param selector - Document `_key`, `_id` or object with either of those
     * properties (e.g. a document from this collection).
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * const exists = await collection.documentExists("abc123");
     * if (!exists) {
     *   console.log("Document does not exist");
     * }
     * ```
     */
    documentExists(selector: DocumentSelector, options?: DocumentExistsOptions): Promise<boolean>;
    /**
     * Retrieves the document matching the given key or id.
     *
     * Throws an exception when passed a document or `_id` from a different
     * collection.
     *
     * @param selector - Document `_key`, `_id` or object with either of those
     * properties (e.g. a document from this collection).
     * @param options - Options for retrieving the document.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * try {
     *   const document = await collection.document("abc123");
     *   console.log(document);
     * } catch (e: any) {
     *   console.error("Could not find document");
     * }
     * ```
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * const document = await collection.document("abc123", { graceful: true });
     * if (document) {
     *   console.log(document);
     * } else {
     *   console.error("Could not find document");
     * }
     * ```
     */
    document(selector: DocumentSelector, options?: CollectionReadOptions): Promise<Document<T>>;
    /**
     * Retrieves the document matching the given key or id.
     *
     * Throws an exception when passed a document or `_id` from a different
     * collection.
     *
     * @param selector - Document `_key`, `_id` or object with either of those
     * properties (e.g. a document from this collection).
     * @param graceful - If set to `true`, `null` is returned instead of an
     * exception being thrown if the document does not exist.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * try {
     *   const document = await collection.document("abc123", false);
     *   console.log(document);
     * } catch (e: any) {
     *   console.error("Could not find document");
     * }
     * ```
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * const document = await collection.document("abc123", true);
     * if (document) {
     *   console.log(document);
     * } else {
     *   console.error("Could not find document");
     * }
     * ```
     */
    document(selector: DocumentSelector, graceful: boolean): Promise<Document<T>>;
    /**
     * Retrieves the documents matching the given key or id values.
     *
     * Throws an exception when passed a document or `_id` from a different
     * collection, or if the document does not exist.
     *
     * @param selectors - Array of document `_key`, `_id` or objects with either
     * of those properties (e.g. a document from this collection).
     * @param options - Options for retrieving the documents.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * try {
     *   const documents = await collection.documents(["abc123", "xyz456"]);
     *   console.log(documents);
     * } catch (e: any) {
     *   console.error("Could not find document");
     * }
     * ```
     */
    documents(selectors: (string | ObjectWithKey)[], options?: CollectionBatchReadOptions): Promise<Document<T>[]>;
    /**
     * Inserts a new document with the given `data` into the collection.
     *
     * @param data - The contents of the new document.
     * @param options - Options for inserting the document.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * const result = await collection.save(
     *   { _key: "a", color: "blue", count: 1 },
     *   { returnNew: true }
     * );
     * console.log(result.new.color, result.new.count); // "blue" 1
     * ```
     */
    save(data: DocumentData<T>, options?: CollectionInsertOptions): Promise<DocumentOperationMetadata & {
        new?: Document<T>;
        old?: Document<T>;
    }>;
    /**
     * Inserts new documents with the given `data` into the collection.
     *
     * @param data - The contents of the new documents.
     * @param options - Options for inserting the documents.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * const result = await collection.saveAll(
     *   [
     *     { _key: "a", color: "blue", count: 1 },
     *     { _key: "b", color: "red", count: 2 },
     *   ],
     *   { returnNew: true }
     * );
     * console.log(result[0].new.color, result[0].new.count); // "blue" 1
     * console.log(result[1].new.color, result[1].new.count); // "red" 2
     * ```
     */
    saveAll(data: Array<DocumentData<T>>, options?: CollectionInsertOptions): Promise<Array<(DocumentOperationMetadata & {
        new?: Document<T>;
        old?: Document<T>;
    }) | DocumentOperationFailure>>;
    /**
     * Replaces an existing document in the collection.
     *
     * Throws an exception when passed a document or `_id` from a different
     * collection.
     *
     * @param selector - Document `_key`, `_id` or object with either of those
     * properties (e.g. a document from this collection).
     * @param newData - The contents of the new document.
     * @param options - Options for replacing the document.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * await collection.save({ _key: "a", color: "blue", count: 1 });
     * const result = await collection.replace(
     *   "a",
     *   { color: "red" },
     *   { returnNew: true }
     * );
     * console.log(result.new.color, result.new.count); // "red" undefined
     * ```
     */
    replace(selector: DocumentSelector, newData: DocumentData<T>, options?: CollectionReplaceOptions): Promise<DocumentOperationMetadata & {
        new?: Document<T>;
        old?: Document<T>;
    }>;
    /**
     * Replaces existing documents in the collection, identified by the `_key` or
     * `_id` of each document.
     *
     * @param newData - The documents to replace.
     * @param options - Options for replacing the documents.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * await collection.save({ _key: "a", color: "blue", count: 1 });
     * await collection.save({ _key: "b", color: "green", count: 3 });
     * const result = await collection.replaceAll(
     *   [
     *     { _key: "a", color: "red" },
     *     { _key: "b", color: "yellow", count: 2 }
     *   ],
     *   { returnNew: true }
     * );
     * console.log(result[0].new.color, result[0].new.count); // "red" undefined
     * console.log(result[1].new.color, result[1].new.count); // "yellow" 2
     * ```
     */
    replaceAll(newData: Array<DocumentData<T> & ({
        _key: string;
    } | {
        _id: string;
    })>, options?: Omit<CollectionReplaceOptions, "ifMatch">): Promise<Array<(DocumentOperationMetadata & {
        new?: Document<T>;
        old?: Document<T>;
    }) | DocumentOperationFailure>>;
    /**
     * Updates an existing document in the collection.
     *
     * Throws an exception when passed a document or `_id` from a different
     * collection.
     *
     * @param selector - Document `_key`, `_id` or object with either of those
     * properties (e.g. a document from this collection).
     * @param newData - The data for updating the document.
     * @param options - Options for updating the document.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * await collection.save({ _key: "a", color: "blue", count: 1 });
     * const result = await collection.update(
     *   "a",
     *   { count: 2 },
     *   { returnNew: true }
     * );
     * console.log(result.new.color, result.new.count); // "blue" 2
     * ```
     */
    update(selector: DocumentSelector, newData: Patch<DocumentData<T>>, options?: CollectionUpdateOptions): Promise<DocumentOperationMetadata & {
        new?: Document<T>;
        old?: Document<T>;
    }>;
    /**
     * Updates existing documents in the collection, identified by the `_key` or
     * `_id` of each document.
     *
     * @param newData - The data for updating the documents.
     * @param options - Options for updating the documents.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * await collection.save({ _key: "a", color: "blue", count: 1 });
     * await collection.save({ _key: "b", color: "green", count: 3 });
     * const result = await collection.updateAll(
     *   [
     *     { _key: "a", count: 2 },
     *     { _key: "b", count: 4 }
     *   ],
     *   { returnNew: true }
     * );
     * console.log(result[0].new.color, result[0].new.count); // "blue" 2
     * console.log(result[1].new.color, result[1].new.count); // "green" 4
     * ```
     */
    updateAll(newData: Array<Patch<DocumentData<T>> & ({
        _key: string;
    } | {
        _id: string;
    })>, options?: Omit<CollectionUpdateOptions, "ifMatch">): Promise<Array<(DocumentOperationMetadata & {
        new?: Document<T>;
        old?: Document<T>;
    }) | DocumentOperationFailure>>;
    /**
     * Removes an existing document from the collection.
     *
     * Throws an exception when passed a document or `_id` from a different
     * collection.
     *
     * @param selector - Document `_key`, `_id` or object with either of those
     * properties (e.g. a document from this collection).
     * @param options - Options for removing the document.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * await collection.remove("abc123");
     * // document with key "abc123" deleted
     * ```
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * const doc = await collection.document("abc123");
     * await collection.remove(doc);
     * // document with key "abc123" deleted
     * ```
     */
    remove(selector: DocumentSelector, options?: CollectionRemoveOptions): Promise<DocumentMetadata & {
        old?: Document<T>;
    }>;
    /**
     * Removes existing documents from the collection.
     *
     * Throws an exception when passed any document or `_id` from a different
     * collection.
     *
     * @param selectors - Documents `_key`, `_id` or objects with either of those
     * properties (e.g. documents from this collection).
     * @param options - Options for removing the documents.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * await collection.removeAll(["abc123", "def456"]);
     * // document with keys "abc123" and "def456" deleted
     * ```
     */
    removeAll(selectors: (string | ObjectWithKey)[], options?: Omit<CollectionRemoveOptions, "ifMatch">): Promise<Array<(DocumentMetadata & {
        old?: Document<T>;
    }) | DocumentOperationFailure>>;
    /**
     * Bulk imports the given `data` into the collection.
     *
     * @param data - The data to import, as an array of document data.
     * @param options - Options for importing the data.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * await collection.import(
     *   [
     *     { _key: "jcd", password: "bionicman" },
     *     { _key: "jreyes", password: "amigo" },
     *     { _key: "ghermann", password: "zeitgeist" }
     *   ]
     * );
     * ```
     */
    import(data: DocumentData<T>[], options?: CollectionImportOptions): Promise<CollectionImportResult>;
    /**
     * Bulk imports the given `data` into the collection.
     *
     * @param data - The data to import, as an array containing a single array of
     * attribute names followed by one or more arrays of attribute values for
     * each document.
     * @param options - Options for importing the data.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * await collection.import(
     *   [
     *     [ "_key", "password" ],
     *     [ "jcd", "bionicman" ],
     *     [ "jreyes", "amigo" ],
     *     [ "ghermann", "zeitgeist" ]
     *   ]
     * );
     * ```
     */
    import(data: any[][], options?: CollectionImportOptions): Promise<CollectionImportResult>;
    /**
     * Bulk imports the given `data` into the collection.
     *
     * If `type` is omitted, `data` must contain one JSON array per line with
     * the first array providing the attribute names and all other arrays
     * providing attribute values for each document.
     *
     * If `type` is set to `"documents"`, `data` must contain one JSON document
     * per line.
     *
     * If `type` is set to `"list"`, `data` must contain a JSON array of
     * documents.
     *
     * If `type` is set to `"auto"`, `data` can be in either of the formats
     * supported by `"documents"` or `"list"`.
     *
     * @param data - The data to import as a Buffer (Node), Blob (browser) or
     * string.
     * @param options - Options for importing the data.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * await collection.import(
     *   '{"_key":"jcd","password":"bionicman"}\r\n' +
     *   '{"_key":"jreyes","password":"amigo"}\r\n' +
     *   '{"_key":"ghermann","password":"zeitgeist"}\r\n',
     *   { type: "documents" } // or "auto"
     * );
     * ```
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * await collection.import(
     *   '[{"_key":"jcd","password":"bionicman"},' +
     *   '{"_key":"jreyes","password":"amigo"},' +
     *   '{"_key":"ghermann","password":"zeitgeist"}]',
     *   { type: "list" } // or "auto"
     * );
     * ```
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * await collection.import(
     *   '["_key","password"]\r\n' +
     *   '["jcd","bionicman"]\r\n' +
     *   '["jreyes","amigo"]\r\n' +
     *   '["ghermann","zeitgeist"]\r\n'
     * );
     * ```
     */
    import(data: Buffer | Blob | string, options?: CollectionImportOptions & {
        type?: "documents" | "list" | "auto";
    }): Promise<CollectionImportResult>;
    /**
     * Retrieves a list of references for all documents in the collection.
     *
     * @param type - The type of document reference to retrieve.
     *
     * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be
     * replaced with AQL queries.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * // const ids = await collection.list("id");
     * const ids = await db.query(aql`
     *   FOR doc IN ${collection}
     *   RETURN doc._id
     * `);
     * ```
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * // const keys = await collection.list("key");
     * const keys = await db.query(aql`
     *   FOR doc IN ${collection}
     *   RETURN doc._key
     * `);
     * ```
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * // const paths = await collection.list("path");
     * const paths = await db.query(aql`
     *   FOR doc IN ${collection}
     *   RETURN CONCAT("/_db/", CURRENT_DATABASE(), "/_api/document/", doc._id)
     * `);
     * ```
     */
    list(type?: SimpleQueryListType): Promise<ArrayCursor<string>>;
    /**
     * Retrieves all documents in the collection.
     *
     * @param options - Options for retrieving the documents.
     *
     * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be
     * replaced with AQL queries.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * // const cursor = await collection.all();
     * const cursor = await db.query(aql`
     *   FOR doc IN ${collection}
     *   RETURN doc
     * `);
     * ```
     */
    all(options?: SimpleQueryAllOptions): Promise<ArrayCursor<Document<T>>>;
    /**
     * Retrieves a random document from the collection.
     *
     * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be
     * replaced with AQL queries.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * // const doc = await collection.any();
     * const cursor = await db.query(aql`
     *   FOR doc IN ${collection}
     *   SORT RAND()
     *   LIMIT 1
     *   RETURN doc
     * `);
     * const doc = await cursor.next();
     * ```
     */
    any(): Promise<Document<T>>;
    /**
     * Retrieves all documents in the collection matching the given example.
     *
     * @param example - An object representing an example for documents.
     * @param options - Options for retrieving the documents.
     *
     * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be
     * replaced with AQL queries.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * // const cursor = await collection.byExample({ flavor: "strawberry" });
     * const cursor = await db.query(aql`
     *   FOR doc IN ${collection}
     *   FILTER doc.flavor == "strawberry"
     *   RETURN doc
     * `);
     * ```
     */
    byExample(example: Partial<DocumentData<T>>, options?: SimpleQueryByExampleOptions): Promise<ArrayCursor<Document<T>>>;
    /**
     * Retrieves a single document in the collection matching the given example.
     *
     * @param example - An object representing an example for the document.
     *
     * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be
     * replaced with AQL queries.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * // const doc = await collection.firstExample({ flavor: "strawberry" });
     * const cursor = await db.query(aql`
     *   FOR doc IN ${collection}
     *   FILTER doc.flavor == "strawberry"
     *   LIMIT 1
     *   RETURN doc
     * `);
     * const doc = await cursor.next();
     * ```
     */
    firstExample(example: Partial<DocumentData<T>>): Promise<Document<T>>;
    /**
     * Removes all documents in the collection matching the given example.
     *
     * @param example - An object representing an example for the document.
     * @param options - Options for removing the documents.
     *
     * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be
     * replaced with AQL queries.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * // const { deleted } = await collection.removeByExample({
     * //   flavor: "strawberry"
     * // });
     * const cursor = await db.query(aql`
     *   RETURN LENGTH(
     *     FOR doc IN ${collection}
     *     FILTER doc.flavor == "strawberry"
     *     REMOVE doc IN ${collection}
     *     RETURN 1
     *   )
     * `);
     * const deleted = await cursor.next();
     * ```
     */
    removeByExample(example: Partial<DocumentData<T>>, options?: SimpleQueryRemoveByExampleOptions): Promise<ArangoApiResponse<SimpleQueryRemoveByExampleResult>>;
    /**
     * Replaces all documents in the collection matching the given example.
     *
     * @param example - An object representing an example for the documents.
     * @param newValue - Document data to replace the matching documents with.
     * @param options - Options for replacing the documents.
     *
     * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be
     * replaced with AQL queries.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * const newValue = { flavor: "chocolate" };
     * // const { replaced } = await collection.replaceByExample(
     * //   { flavor: "strawberry" },
     * //   newValue
     * // );
     * const cursor = await db.query(aql`
     *   RETURN LENGTH(
     *     FOR doc IN ${collection}
     *     FILTER doc.flavor == "strawberry"
     *     REPLACE doc WITH ${newValue} IN ${collection}
     *     RETURN 1
     *   )
     * `);
     * const replaced = await cursor.next();
     * ```
     */
    replaceByExample(example: Partial<DocumentData<T>>, newValue: DocumentData<T>, options?: SimpleQueryReplaceByExampleOptions): Promise<ArangoApiResponse<SimpleQueryReplaceByExampleResult>>;
    /**
     * Updates all documents in the collection matching the given example.
     *
     * @param example - An object representing an example for the documents.
     * @param newValue - Document data to update the matching documents with.
     * @param options - Options for updating the documents.
     *
     * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be
     * replaced with AQL queries.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * const newData = { color: "red" };
     * // const { updated } = await collection.updateByExample(
     * //   { flavor: "strawberry" },
     * //   newValue
     * // );
     * const cursor = await db.query(aql`
     *   RETURN LENGTH(
     *     FOR doc IN ${collection}
     *     FILTER doc.flavor == "strawberry"
     *     UPDATE doc WITH ${newValue} IN ${collection}
     *     RETURN 1
     *   )
     * `);
     * const updated = await cursor.next();
     * ```
     */
    updateByExample(example: Partial<DocumentData<T>>, newValue: Patch<DocumentData<T>>, options?: SimpleQueryUpdateByExampleOptions): Promise<ArangoApiResponse<SimpleQueryUpdateByExampleResult>>;
    /**
     * Retrieves all documents matching the given document keys.
     *
     * @param keys - An array of document keys to look up.
     *
     * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be
     * replaced with AQL queries.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * const keys = ["a", "b", "c"];
     * // const docs = await collection.byKeys(keys);
     * const cursor = await db.query(aql`
     *   FOR key IN ${keys}
     *   LET doc = DOCUMENT(${collection}, key)
     *   RETURN doc
     * `);
     * const docs = await cursor.all();
     * ```
     */
    lookupByKeys(keys: string[]): Promise<Document<T>[]>;
    /**
     * Removes all documents matching the given document keys.
     *
     * @param keys - An array of document keys to remove.
     * @param options - Options for removing the documents.
     *
     * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be
     * replaced with AQL queries.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * const keys = ["a", "b", "c"];
     * // const { removed, ignored } = await collection.removeByKeys(keys);
     * const cursor = await db.query(aql`
     *   FOR key IN ${keys}
     *   LET doc = DOCUMENT(${collection}, key)
     *   FILTER doc
     *   REMOVE doc IN ${collection}
     *   RETURN key
     * `);
     * const removed = await cursor.all();
     * const ignored = keys.filter((key) => !removed.includes(key));
     * ```
     */
    removeByKeys(keys: string[], options?: SimpleQueryRemoveByKeysOptions): Promise<ArangoApiResponse<SimpleQueryRemoveByKeysResult<T>>>;
    /**
     * Performs a fulltext query in the given `attribute` on the collection.
     *
     * @param attribute - Name of the field to search.
     * @param query - Fulltext query string to search for.
     * @param options - Options for performing the fulltext query.
     *
     * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be
     * replaced with AQL queries.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * // const cursor = await collection.fulltext("article", "needle");
     * const cursor = await db.query(aql`
     *   FOR doc IN FULLTEXT(${collection}, "article", "needle")
     *   RETURN doc
     * `);
     * ```
     */
    fulltext(attribute: string, query: string, options?: SimpleQueryFulltextOptions): Promise<ArrayCursor<Document<T>>>;
    /**
     * Returns a list of all index descriptions for the collection.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * const indexes = await collection.indexes();
     * ```
     */
    indexes(): Promise<Index[]>;
    /**
     * Returns an index description by name or `id` if it exists.
     *
     * @param selector - Index name, id or object with either property.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * const index = await collection.index("some-index");
     * ```
     */
    index(selector: IndexSelector): Promise<Index>;
    /**
     * Creates a persistent index on the collection if it does not already exist.
     *
     * @param details - Options for creating the persistent index.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * // Create a unique index for looking up documents by username
     * await collection.ensureIndex({
     *   type: "persistent",
     *   fields: ["username"],
     *   name: "unique-usernames",
     *   unique: true
     * });
     * ```
     */
    ensureIndex(details: EnsurePersistentIndexOptions): Promise<ArangoApiResponse<PersistentIndex & {
        isNewlyCreated: boolean;
    }>>;
    /**
     * Creates a TTL index on the collection if it does not already exist.
     *
     * @param details - Options for creating the TTL index.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * // Expire documents with "createdAt" timestamp one day after creation
     * await collection.ensureIndex({
     *   type: "ttl",
     *   fields: ["createdAt"],
     *   expireAfter: 60 * 60 * 24 // 24 hours
     * });
     * ```
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * // Expire documents with "expiresAt" timestamp according to their value
     * await collection.ensureIndex({
     *   type: "ttl",
     *   fields: ["expiresAt"],
     *   expireAfter: 0 // when attribute value is exceeded
     * });
     * ```
     */
    ensureIndex(details: EnsureTtlIndexOptions): Promise<ArangoApiResponse<TtlIndex & {
        isNewlyCreated: boolean;
    }>>;
    /**
     * Creates a multi-dimensional index on the collection if it does not already exist.
     *
     * @param details - Options for creating the multi-dimensional index.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-points");
     * // Create a multi-dimensional index for the attributes x, y and z
     * await collection.ensureIndex({
     *   type: "mdi",
     *   fields: ["x", "y", "z"],
     *   fieldValueTypes: "double"
     * });
     * ```
     * ```
     */
    ensureIndex(details: EnsureMdiIndexOptions): Promise<ArangoApiResponse<MdiIndex & {
        isNewlyCreated: boolean;
    }>>;
    /**
     * Creates a fulltext index on the collection if it does not already exist.
     *
     * @param details - Options for creating the fulltext index.
     *
     * @deprecated Fulltext indexes have been deprecated in ArangoDB 3.10 and
     * should be replaced with ArangoSearch.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * // Create a fulltext index for tokens longer than or equal to 3 characters
     * await collection.ensureIndex({
     *   type: "fulltext",
     *   fields: ["description"],
     *   minLength: 3
     * });
     * ```
     */
    ensureIndex(details: EnsureFulltextIndexOptions): Promise<ArangoApiResponse<FulltextIndex & {
        isNewlyCreated: boolean;
    }>>;
    /**
     * Creates a geo index on the collection if it does not already exist.
     *
     * @param details - Options for creating the geo index.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * // Create an index for GeoJSON data
     * await collection.ensureIndex({
     *   type: "geo",
     *   fields: ["lngLat"],
     *   geoJson: true
     * });
     * ```
     */
    ensureIndex(details: EnsureGeoIndexOptions): Promise<ArangoApiResponse<GeoIndex & {
        isNewlyCreated: boolean;
    }>>;
    /**
     * Creates a inverted index on the collection if it does not already exist.
     *
     * @param details - Options for creating the inverted index.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * // Create an inverted index
     * await collection.ensureIndex({
     *   type: "inverted",
     *   fields: ["a", { name: "b", analyzer: "text_en" }]
     * });
     * ```
     */
    ensureIndex(details: EnsureInvertedIndexOptions): Promise<ArangoApiResponse<InvertedIndex & {
        isNewlyCreated: boolean;
    }>>;
    /**
     * Deletes the index with the given name or `id` from the database.
     *
     * @param selector - Index name, id or object with either property.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * await collection.dropIndex("some-index");
     * // The index "some-index" no longer exists
     * ```
     */
    dropIndex(selector: IndexSelector): Promise<ArangoApiResponse<{
        id: string;
    }>>;
    /**
     * Triggers compaction for a collection.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * await collection.compact();
     * // Background compaction is triggered on the collection
     * ```
     */
    compact(): Promise<ArangoApiResponse<Record<string, never>>>;
}
/**
 * Represents an edge collection in a {@link database.Database}.
 *
 * See {@link DocumentCollection} for a more generic variant of this interface
 * more suited for regular document collections.
 *
 * See also {@link graph.GraphEdgeCollection} for the type representing an edge
 * collection in a {@link graph.Graph}.
 *
 * When using TypeScript, collections can be cast to a specific edge document
 * data type to increase type safety.
 *
 * @param T - Type to use for edge document data. Defaults to `any`.
 *
 * @example
 * ```ts
 * interface Friend {
 *   startDate: number;
 *   endDate?: number;
 * }
 * const db = new Database();
 * const edges = db.collection("friends") as EdgeCollection<Friend>;
 * ```
 */
export interface EdgeCollection<T extends Record<string, any> = any> extends DocumentCollection<T> {
    /**
     * Retrieves the document matching the given key or id.
     *
     * Throws an exception when passed a document or `_id` from a different
     * collection, or if the document does not exist.
     *
     * @param selector - Document `_key`, `_id` or object with either of those
     * properties (e.g. a document from this collection).
     * @param options - Options for retrieving the document.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * try {
     *   const document = await collection.document("abc123");
     *   console.log(document);
     * } catch (e: any) {
     *   console.error("Could not find document");
     * }
     * ```
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * const document = await collection.document("abc123", { graceful: true });
     * if (document) {
     *   console.log(document);
     * } else {
     *   console.error("Document does not exist");
     * }
     * ```
     */
    document(selector: DocumentSelector, options?: CollectionReadOptions): Promise<Edge<T>>;
    /**
     * Retrieves the document matching the given key or id.
     *
     * Throws an exception when passed a document or `_id` from a different
     * collection, or if the document does not exist.
     *
     * @param selector - Document `_key`, `_id` or object with either of those
     * properties (e.g. a document from this collection).
     * @param graceful - If set to `true`, `null` is returned instead of an
     * exception being thrown if the document does not exist.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * try {
     *   const document = await collection.document("abc123", false);
     *   console.log(document);
     * } catch (e: any) {
     *   console.error("Could not find document");
     * }
     * ```
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * const document = await collection.document("abc123", true);
     * if (document) {
     *   console.log(document);
     * } else {
     *   console.error("Document does not exist");
     * }
     * ```
     */
    document(selector: DocumentSelector, graceful: boolean): Promise<Edge<T>>;
    /**
     * Retrieves the documents matching the given key or id values.
     *
     * Throws an exception when passed a document or `_id` from a different
     * collection, or if the document does not exist.
     *
     * @param selectors - Array of document `_key`, `_id` or objects with either
     * of those properties (e.g. a document from this collection).
     * @param options - Options for retrieving the documents.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * try {
     *   const documents = await collection.documents(["abc123", "xyz456"]);
     *   console.log(documents);
     * } catch (e: any) {
     *   console.error("Could not find document");
     * }
     * ```
     */
    documents(selectors: (string | ObjectWithKey)[], options?: CollectionBatchReadOptions): Promise<Edge<T>[]>;
    /**
     * Inserts a new document with the given `data` into the collection.
     *
     * @param data - The contents of the new document.
     * @param options - Options for inserting the document.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("friends");
     * const result = await collection.save(
     *   { _from: "users/rana", _to: "users/mudasir", active: false },
     *   { returnNew: true }
     * );
     * ```
     */
    save(data: EdgeData<T>, options?: CollectionInsertOptions): Promise<DocumentOperationMetadata & {
        new?: Edge<T>;
        old?: Edge<T>;
    }>;
    /**
     * Inserts new documents with the given `data` into the collection.
     *
     * @param data - The contents of the new documents.
     * @param options - Options for inserting the documents.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("friends");
     * const result = await collection.saveAll(
     *   [
     *     { _from: "users/rana", _to: "users/mudasir", active: false },
     *     { _from: "users/rana", _to: "users/salman", active: true }
     *   ],
     *   { returnNew: true }
     * );
     * ```
     */
    saveAll(data: Array<EdgeData<T>>, options?: CollectionInsertOptions): Promise<Array<(DocumentOperationMetadata & {
        new?: Edge<T>;
        old?: Edge<T>;
    }) | DocumentOperationFailure>>;
    /**
     * Replaces an existing document in the collection.
     *
     * Throws an exception when passed a document or `_id` from a different
     * collection.
     *
     * @param selector - Document `_key`, `_id` or object with either of those
     * properties (e.g. a document from this collection).
     * @param newData - The contents of the new document.
     * @param options - Options for replacing the document.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("friends");
     * await collection.save(
     *   {
     *     _key: "musadir",
     *     _from: "users/rana",
     *     _to: "users/mudasir",
     *     active: true,
     *     best: true
     *   }
     * );
     * const result = await collection.replace(
     *   "musadir",
     *   { active: false },
     *   { returnNew: true }
     * );
     * console.log(result.new.active, result.new.best); // false undefined
     * ```
     */
    replace(selector: DocumentSelector, newData: DocumentData<T>, options?: CollectionReplaceOptions): Promise<DocumentOperationMetadata & {
        new?: Edge<T>;
        old?: Edge<T>;
    }>;
    /**
     * Replaces existing documents in the collection, identified by the `_key` or
     * `_id` of each document.
     *
     * @param newData - The documents to replace.
     * @param options - Options for replacing the documents.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("friends");
     * await collection.save(
     *   {
     *     _key: "musadir",
     *     _from: "users/rana",
     *     _to: "users/mudasir",
     *     active: true,
     *     best: true
     *   }
     * );
     * await collection.save(
     *   {
     *     _key: "salman",
     *     _from: "users/rana",
     *     _to: "users/salman",
     *     active: false,
     *     best: false
     *   }
     * );
     * const result = await collection.replaceAll(
     *   [
     *     { _key: "musadir", active: false },
     *     { _key: "salman", active: true, best: true }
     *   ],
     *   { returnNew: true }
     * );
     * console.log(result[0].new.active, result[0].new.best); // false undefined
     * console.log(result[1].new.active, result[1].new.best); // true true
     * ```
     */
    replaceAll(newData: Array<DocumentData<T> & ({
        _key: string;
    } | {
        _id: string;
    })>, options?: CollectionReplaceOptions): Promise<Array<(DocumentOperationMetadata & {
        new?: Edge<T>;
        old?: Edge<T>;
    }) | DocumentOperationFailure>>;
    /**
     * Updates an existing document in the collection.
     *
     * Throws an exception when passed a document or `_id` from a different
     * collection.
     *
     * @param selector - Document `_key`, `_id` or object with either of those
     * properties (e.g. a document from this collection).
     * @param newData - The data for updating the document.
     * @param options - Options for updating the document.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("friends");
     * await collection.save(
     *   {
     *     _key: "musadir",
     *     _from: "users/rana",
     *     _to: "users/mudasir",
     *     active: true,
     *     best: true
     *   }
     * );
     * const result = await collection.update(
     *   "musadir",
     *   { active: false },
     *   { returnNew: true }
     * );
     * console.log(result.new.active, result.new.best); // false true
     * ```
     */
    update(selector: DocumentSelector, newData: Patch<DocumentData<T>>, options?: CollectionUpdateOptions): Promise<DocumentOperationMetadata & {
        new?: Edge<T>;
        old?: Edge<T>;
    }>;
    /**
     * Updates existing documents in the collection, identified by the `_key` or
     * `_id` of each document.
     *
     * @param newData - The data for updating the documents.
     * @param options - Options for updating the documents.
     * ```js
     * const db = new Database();
     * const collection = db.collection("friends");
     * await collection.save(
     *   {
     *     _key: "musadir",
     *     _from: "users/rana",
     *     _to: "users/mudasir",
     *     active: true,
     *     best: true
     *   }
     * );
     * await collection.save(
     *   {
     *     _key: "salman",
     *     _from: "users/rana",
     *     _to: "users/salman",
     *     active: false,
     *     best: false
     *   }
     * );
     * const result = await collection.updateAll(
     *   [
     *     { _key: "musadir", active: false },
     *     { _key: "salman", active: true, best: true }
     *   ],
     *   { returnNew: true }
     * );
     * console.log(result[0].new.active, result[0].new.best); // false true
     * console.log(result[1].new.active, result[1].new.best); // true true
     * ```
     */
    updateAll(newData: Array<Patch<DocumentData<T>> & ({
        _key: string;
    } | {
        _id: string;
    })>, options?: CollectionUpdateOptions): Promise<Array<(DocumentOperationMetadata & {
        new?: Edge<T>;
        old?: Edge<T>;
    }) | DocumentOperationFailure>>;
    /**
     * Removes an existing document from the collection.
     *
     * Throws an exception when passed a document or `_id` from a different
     * collection.
     *
     * @param selector - Document `_key`, `_id` or object with either of those
     * properties (e.g. a document from this collection).
     * @param options - Options for removing the document.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("friends");
     * const doc = await collection.document("musadir");
     * await collection.remove(doc);
     * // document with key "musadir" deleted
     * ```
     */
    remove(selector: DocumentSelector, options?: CollectionRemoveOptions): Promise<DocumentMetadata & {
        old?: Edge<T>;
    }>;
    /**
     * Removes existing documents from the collection.
     *
     * Throws an exception when passed any document or `_id` from a different
     * collection.
     *
     * @param selectors - Documents `_key`, `_id` or objects with either of those
     * properties (e.g. documents from this collection).
     * @param options - Options for removing the documents.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("friends");
     * await collection.removeAll(["musadir", "salman"]);
     * // document with keys "musadir" and "salman" deleted
     * ```
     */
    removeAll(selectors: DocumentSelector[], options?: CollectionRemoveOptions): Promise<Array<(DocumentMetadata & {
        old?: Edge<T>;
    }) | DocumentOperationFailure>>;
    /**
     * Bulk imports the given `data` into the collection.
     *
     * @param data - The data to import, as an array of edge data.
     * @param options - Options for importing the data.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * await collection.import(
     *   [
     *     { _key: "x", _from: "vertices/a", _to: "vertices/b", weight: 1 },
     *     { _key: "y", _from: "vertices/a", _to: "vertices/c", weight: 2 }
     *   ]
     * );
     * ```
     */
    import(data: EdgeData<T>[], options?: CollectionImportOptions): Promise<CollectionImportResult>;
    /**
     * Bulk imports the given `data` into the collection.
     *
     * @param data - The data to import, as an array containing a single array of
     * attribute names followed by one or more arrays of attribute values for
     * each edge document.
     * @param options - Options for importing the data.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * await collection.import(
     *   [
     *     [ "_key", "_from", "_to", "weight" ],
     *     [ "x", "vertices/a", "vertices/b", 1 ],
     *     [ "y", "vertices/a", "vertices/c", 2 ]
     *   ]
     * );
     * ```
     */
    import(data: any[][], options?: CollectionImportOptions): Promise<CollectionImportResult>;
    /**
     * Bulk imports the given `data` into the collection.
     *
     * If `type` is omitted, `data` must contain one JSON array per line with
     * the first array providing the attribute names and all other arrays
     * providing attribute values for each edge document.
     *
     * If `type` is set to `"documents"`, `data` must contain one JSON document
     * per line.
     *
     * If `type` is set to `"list"`, `data` must contain a JSON array of
     * edge documents.
     *
     * If `type` is set to `"auto"`, `data` can be in either of the formats
     * supported by `"documents"` or `"list"`.
     *
     * @param data - The data to import as a Buffer (Node), Blob (browser) or
     * string.
     * @param options - Options for importing the data.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * await collection.import(
     *   '{"_key":"x","_from":"vertices/a","_to":"vertices/b","weight":1}\r\n' +
     *   '{"_key":"y","_from":"vertices/a","_to":"vertices/c","weight":2}\r\n',
     *   { type: "documents" } // or "auto"
     * );
     * ```
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * await collection.import(
     *   '[{"_key":"x","_from":"vertices/a","_to":"vertices/b","weight":1},' +
     *   '{"_key":"y","_from":"vertices/a","_to":"vertices/c","weight":2}]',
     *   { type: "list" } // or "auto"
     * );
     * ```
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * await collection.import(
     *   '["_key","_from","_to","weight"]\r\n' +
     *   '["x","vertices/a","vertices/b",1]\r\n' +
     *   '["y","vertices/a","vertices/c",2]\r\n'
     * );
     * ```
     */
    import(data: Buffer | Blob | string, options?: CollectionImportOptions & {
        type?: "documents" | "list" | "auto";
    }): Promise<CollectionImportResult>;
    /**
     * Retrieves all documents in the collection.
     *
     * @param options - Options for retrieving the documents.
     *
     * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be
     * replaced with AQL queries.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * // const cursor = await collection.all();
     * const cursor = await db.query(aql`
     *   FOR doc IN ${collection}
     *   RETURN doc
     * `);
     * ```
     */
    all(options?: SimpleQueryAllOptions): Promise<ArrayCursor<Edge<T>>>;
    /**
     * Retrieves a random document from the collection.
     *
     * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be
     * replaced with AQL queries.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * // const doc = await collection.any();
     * const cursor = await db.query(aql`
     *   FOR doc IN ${collection}
     *   SORT RAND()
     *   LIMIT 1
     *   RETURN doc
     * `);
     * const doc = await cursor.next();
     * ```
     */
    any(): Promise<Edge<T>>;
    /**
     * Retrieves all documents in the collection matching the given example.
     *
     * @param example - An object representing an example for documents.
     * @param options - Options for retrieving the documents.
     *
     * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be
     * replaced with AQL queries.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * // const cursor = await collection.byExample({ flavor: "strawberry" });
     * const cursor = await db.query(aql`
     *   FOR doc IN ${collection}
     *   FILTER doc.flavor == "strawberry"
     *   RETURN doc
     * `);
     * ```
     */
    byExample(example: Partial<DocumentData<T>>, options?: SimpleQueryByExampleOptions): Promise<ArrayCursor<Edge<T>>>;
    /**
     * Retrieves a single document in the collection matching the given example.
     *
     * @param example - An object representing an example for the document.
     *
     * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be
     * replaced with AQL queries.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * // const doc = await collection.firstExample({ flavor: "strawberry" });
     * const cursor = await db.query(aql`
     *   FOR doc IN ${collection}
     *   FILTER doc.flavor == "strawberry"
     *   LIMIT 1
     *   RETURN doc
     * `);
     * const doc = await cursor.next();
     * ```
     */
    firstExample(example: Partial<DocumentData<T>>): Promise<Edge<T>>;
    /**
     * Retrieves all documents matching the given document keys.
     *
     * @param keys - An array of document keys to look up.
     *
     * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be
     * replaced with AQL queries.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * const keys = ["a", "b", "c"];
     * // const docs = await collection.byKeys(keys);
     * const cursor = await db.query(aql`
     *   FOR key IN ${keys}
     *   LET doc = DOCUMENT(${collection}, key)
     *   RETURN doc
     * `);
     * const docs = await cursor.all();
     * ```
     */
    lookupByKeys(keys: string[]): Promise<Edge<T>[]>;
    /**
     * Performs a fulltext query in the given `attribute` on the collection.
     *
     * @param attribute - Name of the field to search.
     * @param query - Fulltext query string to search for.
     * @param options - Options for performing the fulltext query.
     *
     * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be
     * replaced with AQL queries.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("some-collection");
     * // const cursor = await collection.fulltext("article", "needle");
     * const cursor = await db.query(aql`
     *   FOR doc IN FULLTEXT(${collection}, "article", "needle")
     *   RETURN doc
     * `);
     * ```
     */
    fulltext(attribute: string, query: string, options?: SimpleQueryFulltextOptions): Promise<ArrayCursor<Edge<T>>>;
    /**
     * Retrieves a list of all edges of the document matching the given
     * `selector`.
     *
     * Throws an exception when passed a document or `_id` from a different
     * collection.
     *
     * @param selector - Document `_key`, `_id` or object with either of those
     * properties (e.g. a document from this collection).
     * @param options - Options for retrieving the edges.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("edges");
     * await collection.import([
     *   ["_key", "_from", "_to"],
     *   ["x", "vertices/a", "vertices/b"],
     *   ["y", "vertices/a", "vertices/c"],
     *   ["z", "vertices/d", "vertices/a"],
     * ]);
     * const edges = await collection.edges("vertices/a");
     * console.log(edges.map((edge) => edge._key)); // ["x", "y", "z"]
     * ```
     */
    edges(selector: DocumentSelector, options?: CollectionEdgesOptions): Promise<ArangoApiResponse<CollectionEdgesResult<T>>>;
    /**
     * Retrieves a list of all incoming edges of the document matching the given
     * `selector`.
     *
     * Throws an exception when passed a document or `_id` from a different
     * collection.
     *
     * @param selector - Document `_key`, `_id` or object with either of those
     * properties (e.g. a document from this collection).
     * @param options - Options for retrieving the edges.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("edges");
     * await collection.import([
     *   ["_key", "_from", "_to"],
     *   ["x", "vertices/a", "vertices/b"],
     *   ["y", "vertices/a", "vertices/c"],
     *   ["z", "vertices/d", "vertices/a"],
     * ]);
     * const edges = await collection.inEdges("vertices/a");
     * console.log(edges.map((edge) => edge._key)); // ["z"]
     * ```
     */
    inEdges(selector: DocumentSelector, options?: CollectionEdgesOptions): Promise<ArangoApiResponse<CollectionEdgesResult<T>>>;
    /**
     * Retrieves a list of all outgoing edges of the document matching the given
     * `selector`.
     *
     * Throws an exception when passed a document or `_id` from a different
     * collection.
     *
     * @param selector - Document `_key`, `_id` or object with either of those
     * properties (e.g. a document from this collection).
     * @param options - Options for retrieving the edges.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("edges");
     * await collection.import([
     *   ["_key", "_from", "_to"],
     *   ["x", "vertices/a", "vertices/b"],
     *   ["y", "vertices/a", "vertices/c"],
     *   ["z", "vertices/d", "vertices/a"],
     * ]);
     * const edges = await collection.outEdges("vertices/a");
     * console.log(edges.map((edge) => edge._key)); // ["x", "y"]
     * ```
     */
    outEdges(selector: DocumentSelector, options?: CollectionEdgesOptions): Promise<ArangoApiResponse<CollectionEdgesResult<T>>>;
    /**
     * Performs a traversal starting from the given `startVertex` and following
     * edges contained in this edge collection.
     *
     * Throws an exception when passed a document or `_id` from a different
     * collection.
     *
     * See also {@link graph.Graph#traversal}.
     *
     * @param startVertex - Document `_key`, `_id` or object with either of those
     * properties (e.g. a document from this collection).
     * @param options - Options for performing the traversal.
     *
     * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and are
     * no longer supported in ArangoDB 3.12. They can be replaced with AQL queries.
     *
     * @example
     * ```js
     * const db = new Database();
     * const collection = db.collection("edges");
     * await collection.import([
     *   ["_key", "_from", "_to"],
     *   ["x", "vertices/a", "vertices/b"],
     *   ["y", "vertices/b", "vertices/c"],
     *   ["z", "vertices/c", "vertices/d"],
     * ]);
     * const startVertex = "vertices/a";
     * const cursor = await db.query(aql`
     *   FOR vertex IN OUTBOUND ${startVertex}
     *   RETURN vertex._key
     * `);
     * const result = await cursor.all();
     * console.log(result); // ["a", "b", "c", "d"]
     * ```
     */
    traversal(startVertex: DocumentSelector, options?: TraversalOptions): Promise<any>;
}
/**
 * @internal
 */
export declare class Collection<T extends Record<string, any> = any> implements EdgeCollection<T>, DocumentCollection<T> {
    protected _name: string;
    protected _db: Database;
    /**
     * @internal
     */
    constructor(db: Database, name: string);
    get isArangoCollection(): true;
    get name(): string;
    get(): Promise<any>;
    exists(): Promise<boolean>;
    create(options?: CreateCollectionOptions & {
        type?: CollectionType;
    }): Promise<any>;
    properties(properties?: CollectionPropertiesOptions): Promise<ArangoApiResponse<CollectionMetadata & CollectionProperties>>;
    count(): Promise<ArangoApiResponse<CollectionMetadata & CollectionProperties & {
        count: number;
    }>>;
    recalculateCount(): Promise<boolean>;
    figures(details?: boolean): Promise<CollectionMetadata & ArangoApiResponse<CollectionProperties & {
        count: number;
        figures: Record<string, any>;
    }>>;
    revision(): Promise<ArangoApiResponse<CollectionMetadata & CollectionProperties & {
        revision: string;
    }>>;
    checksum(options?: CollectionChecksumOptions): Promise<ArangoApiResponse<CollectionMetadata & {
        revision: string;
        checksum: string;
    }>>;
    loadIndexes(): Promise<boolean>;
    rename(newName: string): Promise<ArangoApiResponse<CollectionMetadata>>;
    truncate(): Promise<ArangoApiResponse<CollectionMetadata>>;
    drop(options?: CollectionDropOptions): Promise<any>;
    getResponsibleShard(document: Partial<Document<T>>): Promise<string>;
    documentId(selector: DocumentSelector): string;
    documentExists(selector: DocumentSelector, options?: DocumentExistsOptions): Promise<boolean>;
    documents(selectors: (string | ObjectWithKey)[], options?: CollectionBatchReadOptions): Promise<any>;
    document(selector: DocumentSelector, options?: boolean | CollectionReadOptions): Promise<any>;
    save(data: DocumentData<T>, options?: CollectionInsertOptions): Promise<any>;
    saveAll(data: Array<DocumentData<T>>, options?: CollectionInsertOptions): Promise<any>;
    replace(selector: DocumentSelector, newData: DocumentData<T>, options?: CollectionReplaceOptions): Promise<any>;
    replaceAll(newData: Array<DocumentData<T> & ({
        _key: string;
    } | {
        _id: string;
    })>, options?: CollectionReplaceOptions): Promise<any>;
    update(selector: DocumentSelector, newData: Patch<DocumentData<T>>, options?: CollectionUpdateOptions): Promise<any>;
    updateAll(newData: Array<Patch<DocumentData<T>> & ({
        _key: string;
    } | {
        _id: string;
    })>, options?: CollectionUpdateOptions): Promise<any>;
    remove(selector: DocumentSelector, options?: CollectionRemoveOptions): Promise<any>;
    removeAll(selectors: (string | ObjectWithKey)[], options?: CollectionRemoveOptions): Promise<any>;
    import(data: Buffer | Blob | string | any[], options?: CollectionImportOptions & {
        type?: "documents" | "list" | "auto";
    }): Promise<CollectionImportResult>;
    protected _edges(selector: DocumentSelector, options?: CollectionEdgesOptions, direction?: "in" | "out"): Promise<any>;
    edges(vertex: DocumentSelector, options?: CollectionEdgesOptions): Promise<any>;
    inEdges(vertex: DocumentSelector, options?: CollectionEdgesOptions): Promise<any>;
    outEdges(vertex: DocumentSelector, options?: CollectionEdgesOptions): Promise<any>;
    traversal(startVertex: DocumentSelector, options?: TraversalOptions): Promise<any>;
    list(type?: SimpleQueryListType): Promise<ArrayCursor<any>>;
    all(options?: SimpleQueryAllOptions): Promise<ArrayCursor<any>>;
    any(): Promise<any>;
    byExample(example: Partial<DocumentData<T>>, options?: SimpleQueryByExampleOptions): Promise<ArrayCursor<any>>;
    firstExample(example: Partial<DocumentData<T>>): Promise<any>;
    removeByExample(example: Partial<DocumentData<T>>, options?: SimpleQueryRemoveByExampleOptions): Promise<any>;
    replaceByExample(example: Partial<DocumentData<T>>, newValue: DocumentData<T>, options?: SimpleQueryReplaceByExampleOptions): Promise<any>;
    updateByExample(example: Partial<DocumentData<T>>, newValue: Patch<DocumentData<T>>, options?: SimpleQueryUpdateByExampleOptions): Promise<any>;
    lookupByKeys(keys: string[]): Promise<any>;
    removeByKeys(keys: string[], options?: SimpleQueryRemoveByKeysOptions): Promise<any>;
    indexes(): Promise<any>;
    index(selector: IndexSelector): Promise<any>;
    ensureIndex(options: EnsurePersistentIndexOptions | EnsureGeoIndexOptions | EnsureFulltextIndexOptions | EnsureTtlIndexOptions | EnsureMdiIndexOptions | EnsureInvertedIndexOptions): Promise<any>;
    dropIndex(selector: IndexSelector): Promise<any>;
    fulltext(attribute: string, query: string, { index, ...options }?: SimpleQueryFulltextOptions): Promise<ArrayCursor<any>>;
    compact(): Promise<any>;
}
//# sourceMappingURL=collection.d.ts.map