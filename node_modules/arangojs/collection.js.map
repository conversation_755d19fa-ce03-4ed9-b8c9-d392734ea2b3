{"version": 3, "file": "collection.js", "sourceRoot": "", "sources": ["../src/collection.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;GAYG;AACH,+BAAuE;AAEvE,qCAA2D;AAE3D,2CAUqB;AACrB,mCAAmD;AACnD,uCAgBmB;AAEnB,uCAAuE;AAEvE;;;;GAIG;AACH,SAAgB,kBAAkB,CAChC,UAAe;IAEf,OAAO,OAAO,CAAC,UAAU,IAAI,UAAU,CAAC,kBAAkB,CAAC,CAAC;AAC9D,CAAC;AAJD,gDAIC;AAED;;;;;GAKG;AACH,SAAgB,kBAAkB,CAChC,UAAqC;IAErC,IAAI,kBAAkB,CAAC,UAAU,CAAC,EAAE,CAAC;QACnC,OAAO,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;;QAAM,OAAO,MAAM,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AACpD,CAAC;AAND,gDAMC;AAqBD;;GAEG;AACH,IAAY,cAGX;AAHD,WAAY,cAAc;IACxB,iFAAuB,CAAA;IACvB,yEAAmB,CAAA;AACrB,CAAC,EAHW,cAAc,8BAAd,cAAc,QAGzB;AAED;;GAEG;AACH,IAAY,gBAOX;AAPD,WAAY,gBAAgB;IAC1B,6DAAW,CAAA;IACX,+DAAY,CAAA;IACZ,2DAAU,CAAA;IACV,iEAAa,CAAA;IACb,6DAAW,CAAA;IACX,6DAAW,CAAA;AACb,CAAC,EAPW,gBAAgB,gCAAhB,gBAAgB,QAO3B;AA81GD;;GAEG;AACH,MAAa,UAAU;IAMrB,YAAY;IAEZ;;OAEG;IACH,YAAY,EAAY,EAAE,IAAY;QACpC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACnC,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;IAChB,CAAC;IAED,kBAAkB;IAClB,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED,GAAG;QACD,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;YACtB,IAAI,EAAE,oBAAoB,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;SAC3D,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,MAAM;QACV,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,GAAG,EAAE,CAAC;YACjB,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAClB,IAAI,IAAA,qBAAa,EAAC,GAAG,CAAC,IAAI,GAAG,CAAC,QAAQ,KAAK,4BAAoB,EAAE,CAAC;gBAChE,OAAO,KAAK,CAAC;YACf,CAAC;YACD,MAAM,GAAG,CAAC;QACZ,CAAC;IACH,CAAC;IAED,MAAM,CACJ,UAEI,EAAE;QAEN,MAAM,EACJ,sBAAsB,GAAG,SAAS,EAClC,wBAAwB,GAAG,SAAS,EACpC,GAAG,IAAI,EACR,GAAG,OAAO,CAAC;QACZ,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,EAAE;gBAC9D,IAAI,IAAA,kBAAY,EAAC,aAAa,CAAC,UAAU,CAAC,EAAE,CAAC;oBAC3C,OAAO;wBACL,GAAG,aAAa;wBAChB,UAAU,EAAE,aAAa,CAAC,UAAU,CAAC,KAAK,EAAE;qBAC7C,CAAC;gBACJ,CAAC;gBACD,IAAI,IAAA,gBAAU,EAAC,aAAa,CAAC,UAAU,CAAC,EAAE,CAAC;oBACzC,OAAO;wBACL,GAAG,aAAa;wBAChB,UAAU,EAAE,aAAa,CAAC,UAAU,CAAC,KAAK;qBAC3C,CAAC;gBACJ,CAAC;gBACD,OAAO,aAAa,CAAC;YACvB,CAAC,CAAC,CAAC;QACL,CAAC;QACD,MAAM,EAAE,GAAW,EAAE,CAAC;QACtB,IAAI,OAAO,sBAAsB,KAAK,SAAS,EAAE,CAAC;YAChD,EAAE,CAAC,sBAAsB,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7D,CAAC;QACD,IAAI,OAAO,wBAAwB,KAAK,SAAS,EAAE,CAAC;YAClD,EAAE,CAAC,wBAAwB,GAAG,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjE,CAAC;QACD,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;YACtB,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,kBAAkB;YACxB,EAAE;YACF,IAAI,EAAE;gBACJ,GAAG,IAAI;gBACP,IAAI,EAAE,IAAI,CAAC,KAAK;aACjB;SACF,CAAC,CAAC;IACL,CAAC;IAED,UAAU,CACR,UAAwC;QAExC,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;gBACtB,IAAI,EAAE,oBAAoB,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa;aACtE,CAAC,CAAC;QACL,CAAC;QACD,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;YACtB,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,oBAAoB,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa;YACrE,IAAI,EAAE,UAAU;SACjB,CAAC,CAAC;IACL,CAAC;IAED,KAAK;QAKH,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;YACtB,IAAI,EAAE,oBAAoB,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ;SACjE,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,gBAAgB;QACpB,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,oBAAoB,kBAAkB,CAC1C,IAAI,CAAC,KAAK,CACX,mBAAmB;SACrB,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CACzB,CAAC;IACJ,CAAC;IAED,OAAO,CACL,OAAO,GAAG,KAAK;QAOf,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;YACtB,IAAI,EAAE,oBAAoB,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU;YAClE,EAAE,EAAE,EAAE,OAAO,EAAE;SAChB,CAAC,CAAC;IACL,CAAC;IAED,QAAQ;QAKN,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;YACtB,IAAI,EAAE,oBAAoB,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW;SACpE,CAAC,CAAC;IACL,CAAC;IAED,QAAQ,CACN,OAAmC;QAMnC,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;YACtB,IAAI,EAAE,oBAAoB,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW;YACnE,EAAE,EAAE,OAAO;SACZ,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,WAAW;QACf,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,oBAAoB,kBAAkB,CAC1C,IAAI,CAAC,KAAK,CACX,wBAAwB;SAC1B,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CACzB,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,OAAe;QAC1B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QACpE,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACtC,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,QAAQ;QACN,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;YACtB,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,oBAAoB,IAAI,CAAC,KAAK,WAAW;SAChD,CAAC,CAAC;IACL,CAAC;IAED,IAAI,CAAC,OAA+B;QAClC,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;YACtB,MAAM,EAAE,QAAQ;YAChB,IAAI,EAAE,oBAAoB,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YAC1D,EAAE,EAAE,OAAO;SACZ,CAAC,CAAC;IACL,CAAC;IACD,YAAY;IAEZ,cAAc;IACd,mBAAmB,CAAC,QAA8B;QAChD,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,oBAAoB,kBAAkB,CAC1C,IAAI,CAAC,KAAK,CACX,mBAAmB;YACpB,IAAI,EAAE,QAAQ;SACf,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAC1B,CAAC;IACJ,CAAC;IAED,UAAU,CAAC,QAA0B;QACnC,OAAO,IAAA,2BAAe,EAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;IAC/C,CAAC;IAED,KAAK,CAAC,cAAc,CAClB,QAA0B,EAC1B,UAAiC,EAAE;QAEnC,MAAM,EAAE,OAAO,GAAG,SAAS,EAAE,WAAW,GAAG,SAAS,EAAE,GAAG,OAAO,CAAC;QACjE,MAAM,OAAO,GAAG,EAA4B,CAAC;QAC7C,IAAI,OAAO;YAAE,OAAO,CAAC,UAAU,CAAC,GAAG,OAAO,CAAC;QAC3C,IAAI,WAAW;YAAE,OAAO,CAAC,eAAe,CAAC,GAAG,WAAW,CAAC;QACxD,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAC3B;gBACE,MAAM,EAAE,MAAM;gBACd,IAAI,EAAE,kBAAkB,SAAS,CAC/B,IAAA,2BAAe,EAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CACtC,EAAE;gBACH,OAAO;aACR,EACD,CAAC,GAAG,EAAE,EAAE;gBACN,IAAI,WAAW,IAAI,GAAG,CAAC,UAAU,KAAK,GAAG,EAAE,CAAC;oBAC1C,MAAM,IAAI,iBAAS,CAAC,GAAG,CAAC,CAAC;gBAC3B,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC,CACF,CAAC;QACJ,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAClB,IAAI,GAAG,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;gBACrB,OAAO,KAAK,CAAC;YACf,CAAC;YACD,MAAM,GAAG,CAAC;QACZ,CAAC;IACH,CAAC;IAED,SAAS,CACP,SAAqC,EACrC,UAAsC,EAAE;QAExC,MAAM,EAAE,cAAc,GAAG,SAAS,EAAE,GAAG,OAAO,CAAC;QAC/C,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;YACtB,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,kBAAkB,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YACxD,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE;YACrB,cAAc;YACd,IAAI,EAAE,SAAS;SAChB,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,QAAQ,CACZ,QAA0B,EAC1B,UAA2C,EAAE;QAE7C,IAAI,OAAO,OAAO,KAAK,SAAS,EAAE,CAAC;YACjC,OAAO,GAAG,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC;QAClC,CAAC;QACD,MAAM,EACJ,cAAc,GAAG,SAAS,EAC1B,QAAQ,GAAG,KAAK,EAChB,OAAO,GAAG,SAAS,EACnB,WAAW,GAAG,SAAS,GACxB,GAAG,OAAO,CAAC;QACZ,MAAM,OAAO,GAAG,EAA4B,CAAC;QAC7C,IAAI,OAAO;YAAE,OAAO,CAAC,UAAU,CAAC,GAAG,OAAO,CAAC;QAC3C,IAAI,WAAW;YAAE,OAAO,CAAC,eAAe,CAAC,GAAG,WAAW,CAAC;QACxD,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAC7B;YACE,IAAI,EAAE,kBAAkB,SAAS,CAC/B,IAAA,2BAAe,EAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CACtC,EAAE;YACH,OAAO;YACP,cAAc;SACf,EACD,CAAC,GAAG,EAAE,EAAE;YACN,IAAI,WAAW,IAAI,GAAG,CAAC,UAAU,KAAK,GAAG,EAAE,CAAC;gBAC1C,MAAM,IAAI,iBAAS,CAAC,GAAG,CAAC,CAAC;YAC3B,CAAC;YACD,OAAO,GAAG,CAAC,IAAI,CAAC;QAClB,CAAC,CACF,CAAC;QACF,IAAI,CAAC,QAAQ;YAAE,OAAO,MAAM,CAAC;QAC7B,IAAI,CAAC;YACH,OAAO,MAAM,MAAM,CAAC;QACtB,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAClB,IAAI,IAAA,qBAAa,EAAC,GAAG,CAAC,IAAI,GAAG,CAAC,QAAQ,KAAK,0BAAkB,EAAE,CAAC;gBAC9D,OAAO,IAAI,CAAC;YACd,CAAC;YACD,MAAM,GAAG,CAAC;QACZ,CAAC;IACH,CAAC;IAED,IAAI,CAAC,IAAqB,EAAE,OAAiC;QAC3D,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,kBAAkB,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YACxD,IAAI,EAAE,IAAI;YACV,EAAE,EAAE,OAAO;SACZ,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAClD,CAAC;IACJ,CAAC;IAED,OAAO,CAAC,IAA4B,EAAE,OAAiC;QACrE,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,kBAAkB,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YACxD,IAAI,EAAE,IAAI;YACV,EAAE,EAAE,OAAO;SACZ,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAClD,CAAC;IACJ,CAAC;IAED,OAAO,CACL,QAA0B,EAC1B,OAAwB,EACxB,UAAoC,EAAE;QAEtC,MAAM,EAAE,OAAO,GAAG,SAAS,EAAE,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;QACjD,MAAM,OAAO,GAAG,EAA4B,CAAC;QAC7C,IAAI,OAAO;YAAE,OAAO,CAAC,UAAU,CAAC,GAAG,OAAO,CAAC;QAC3C,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,kBAAkB,SAAS,CAC/B,IAAA,2BAAe,EAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CACtC,EAAE;YACH,OAAO;YACP,IAAI,EAAE,OAAO;YACb,EAAE,EAAE,IAAI;SACT,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAClD,CAAC;IACJ,CAAC;IAED,UAAU,CACR,OAAsE,EACtE,OAAkC;QAElC,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,kBAAkB,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YACxD,IAAI,EAAE,OAAO;YACb,EAAE,EAAE,OAAO;SACZ,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAClD,CAAC;IACJ,CAAC;IAED,MAAM,CACJ,QAA0B,EAC1B,OAA+B,EAC/B,UAAmC,EAAE;QAErC,MAAM,EAAE,OAAO,GAAG,SAAS,EAAE,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;QACjD,MAAM,OAAO,GAAG,EAA4B,CAAC;QAC7C,IAAI,OAAO;YAAE,OAAO,CAAC,UAAU,CAAC,GAAG,OAAO,CAAC;QAC3C,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,OAAO;YACf,IAAI,EAAE,kBAAkB,SAAS,CAC/B,IAAA,2BAAe,EAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CACtC,EAAE;YACH,OAAO;YACP,IAAI,EAAE,OAAO;YACb,EAAE,EAAE,IAAI;SACT,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAClD,CAAC;IACJ,CAAC;IAED,SAAS,CACP,OAEC,EACD,OAAiC;QAEjC,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,OAAO;YACf,IAAI,EAAE,kBAAkB,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YACxD,IAAI,EAAE,OAAO;YACb,EAAE,EAAE,OAAO;SACZ,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAClD,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,QAA0B,EAAE,UAAmC,EAAE;QACtE,MAAM,EAAE,OAAO,GAAG,SAAS,EAAE,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;QACjD,MAAM,OAAO,GAAG,EAA4B,CAAC;QAC7C,IAAI,OAAO;YAAE,OAAO,CAAC,UAAU,CAAC,GAAG,OAAO,CAAC;QAC3C,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,QAAQ;YAChB,IAAI,EAAE,kBAAkB,SAAS,CAC/B,IAAA,2BAAe,EAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CACtC,EAAE;YACH,OAAO;YACP,EAAE,EAAE,IAAI;SACT,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAClD,CAAC;IACJ,CAAC;IAED,SAAS,CACP,SAAqC,EACrC,OAAiC;QAEjC,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,QAAQ;YAChB,IAAI,EAAE,kBAAkB,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YACxD,IAAI,EAAE,SAAS;YACf,EAAE,EAAE,OAAO;SACZ,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAClD,CAAC;IACJ,CAAC;IAED,MAAM,CACJ,IAAoC,EACpC,UAEI,EAAE;QAEN,MAAM,EAAE,GAAG,EAAE,GAAG,OAAO,EAAE,UAAU,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC;QAClD,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACxB,EAAE,CAAC,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC;YAC3D,MAAM,KAAK,GAAG,IAAa,CAAC;YAC5B,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;QACzE,CAAC;QACD,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;YACtB,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,cAAc;YACpB,IAAI,EAAE,IAAI;YACV,QAAQ,EAAE,IAAI;YACd,EAAE;SACH,CAAC,CAAC;IACL,CAAC;IACD,YAAY;IAEZ,eAAe;IACL,MAAM,CACd,QAA0B,EAC1B,UAAkC,EAAE,EACpC,SAAwB;QAExB,MAAM,EAAE,cAAc,GAAG,SAAS,EAAE,GAAG,OAAO,CAAC;QAC/C,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;YACtB,IAAI,EAAE,eAAe,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YACrD,cAAc;YACd,EAAE,EAAE;gBACF,SAAS;gBACT,MAAM,EAAE,IAAA,2BAAe,EAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC;aACrD;SACF,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,MAAwB,EAAE,OAAgC;QAC9D,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACtC,CAAC;IAED,OAAO,CAAC,MAAwB,EAAE,OAAgC;QAChE,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IAC5C,CAAC;IAED,QAAQ,CAAC,MAAwB,EAAE,OAAgC;QACjE,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;IAC7C,CAAC;IAED,SAAS,CAAC,WAA6B,EAAE,OAA0B;QACjE,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,iBAAiB;YACvB,IAAI,EAAE;gBACJ,GAAG,OAAO;gBACV,WAAW;gBACX,cAAc,EAAE,IAAI,CAAC,KAAK;aAC3B;SACF,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CACzB,CAAC;IACJ,CAAC;IACD,YAAY;IAEZ,wBAAwB;IACxB,IAAI,CAAC,OAA4B,IAAI;QACnC,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,uBAAuB;YAC7B,IAAI,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,KAAK,EAAE;SACvC,EACD,CAAC,GAAG,EAAE,EAAE,CACN,IAAI,2BAAkB,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,eAAe,CAAC,CAAC,KAAK,CACxE,CAAC;IACJ,CAAC;IAED,GAAG,CAAC,OAA+B;QACjC,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,kBAAkB;YACxB,IAAI,EAAE;gBACJ,GAAG,OAAO;gBACV,UAAU,EAAE,IAAI,CAAC,KAAK;aACvB;SACF,EACD,CAAC,GAAG,EAAE,EAAE,CACN,IAAI,2BAAkB,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,eAAe,CAAC,CAAC,KAAK,CACxE,CAAC;IACJ,CAAC;IAED,GAAG;QACD,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,kBAAkB;YACxB,IAAI,EAAE,EAAE,UAAU,EAAE,IAAI,CAAC,KAAK,EAAE;SACjC,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAC3B,CAAC;IACJ,CAAC;IAED,SAAS,CACP,OAAiC,EACjC,OAAqC;QAErC,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,yBAAyB;YAC/B,IAAI,EAAE;gBACJ,GAAG,OAAO;gBACV,OAAO;gBACP,UAAU,EAAE,IAAI,CAAC,KAAK;aACvB;SACF,EACD,CAAC,GAAG,EAAE,EAAE,CACN,IAAI,2BAAkB,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,eAAe,CAAC,CAAC,KAAK,CACxE,CAAC;IACJ,CAAC;IAED,YAAY,CAAC,OAAiC;QAC5C,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,4BAA4B;YAClC,IAAI,EAAE;gBACJ,OAAO;gBACP,UAAU,EAAE,IAAI,CAAC,KAAK;aACvB;SACF,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAC3B,CAAC;IACJ,CAAC;IAED,eAAe,CACb,OAAiC,EACjC,OAA2C;QAE3C,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;YACtB,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,gCAAgC;YACtC,IAAI,EAAE;gBACJ,GAAG,OAAO;gBACV,OAAO;gBACP,UAAU,EAAE,IAAI,CAAC,KAAK;aACvB;SACF,CAAC,CAAC;IACL,CAAC;IAED,gBAAgB,CACd,OAAiC,EACjC,QAAyB,EACzB,OAA4C;QAE5C,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;YACtB,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,iCAAiC;YACvC,IAAI,EAAE;gBACJ,GAAG,OAAO;gBACV,OAAO;gBACP,QAAQ;gBACR,UAAU,EAAE,IAAI,CAAC,KAAK;aACvB;SACF,CAAC,CAAC;IACL,CAAC;IAED,eAAe,CACb,OAAiC,EACjC,QAAgC,EAChC,OAA2C;QAE3C,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;YACtB,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,gCAAgC;YACtC,IAAI,EAAE;gBACJ,GAAG,OAAO;gBACV,OAAO;gBACP,QAAQ;gBACR,UAAU,EAAE,IAAI,CAAC,KAAK;aACvB;SACF,CAAC,CAAC;IACL,CAAC;IAED,YAAY,CAAC,IAAc;QACzB,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,6BAA6B;YACnC,IAAI,EAAE;gBACJ,IAAI;gBACJ,UAAU,EAAE,IAAI,CAAC,KAAK;aACvB;SACF,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAC5B,CAAC;IACJ,CAAC;IAED,YAAY,CAAC,IAAc,EAAE,OAAwC;QACnE,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;YACtB,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,6BAA6B;YACnC,IAAI,EAAE;gBACJ,OAAO,EAAE,OAAO;gBAChB,IAAI;gBACJ,UAAU,EAAE,IAAI,CAAC,KAAK;aACvB;SACF,CAAC,CAAC;IACL,CAAC;IACD,YAAY;IAEZ,iBAAiB;IACjB,OAAO;QACL,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,IAAI,EAAE,aAAa;YACnB,EAAE,EAAE,EAAE,UAAU,EAAE,IAAI,CAAC,KAAK,EAAE;SAC/B,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAC1B,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,QAAuB;QAC3B,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;YACtB,IAAI,EAAE,eAAe,SAAS,CAAC,IAAA,sBAAY,EAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;SACrE,CAAC,CAAC;IACL,CAAC;IAED,WAAW,CACT,OAM8B;QAE9B,MAAM,IAAI,GAAG,EAAE,GAAG,OAAO,EAAE,CAAC;QAC5B,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACzC,CAAC;QACD,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;YACtB,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,aAAa;YACnB,IAAI,EAAE,OAAO;YACb,EAAE,EAAE,EAAE,UAAU,EAAE,IAAI,CAAC,KAAK,EAAE;SAC/B,CAAC,CAAC;IACL,CAAC;IAED,SAAS,CAAC,QAAuB;QAC/B,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;YACtB,MAAM,EAAE,QAAQ;YAChB,IAAI,EAAE,eAAe,SAAS,CAAC,IAAA,sBAAY,EAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;SACrE,CAAC,CAAC;IACL,CAAC;IAED,QAAQ,CACN,SAAiB,EACjB,KAAa,EACb,EAAE,KAAK,EAAE,GAAG,OAAO,KAAiC,EAAE;QAEtD,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,uBAAuB;YAC7B,IAAI,EAAE;gBACJ,GAAG,OAAO;gBACV,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,IAAA,sBAAY,EAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS;gBAC1D,SAAS;gBACT,KAAK;gBACL,UAAU,EAAE,IAAI,CAAC,KAAK;aACvB;SACF,EACD,CAAC,GAAG,EAAE,EAAE,CACN,IAAI,2BAAkB,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,eAAe,CAAC,CAAC,KAAK,CACxE,CAAC;IACJ,CAAC;IAED,OAAO;QACL,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,oBAAoB,IAAI,CAAC,KAAK,UAAU;SAC/C,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAClB,CAAC;IACJ,CAAC;CAEF;AAttBD,gCAstBC", "sourcesContent": ["/**\n * ```ts\n * import type {\n *   DocumentCollection,\n *   EdgeCollection,\n * } from \"arangojs/collection\";\n * ```\n *\n * The \"collection\" module provides collection related types and interfaces\n * for TypeScript.\n *\n * @packageDocumentation\n */\nimport { AqlLiteral, AqlQuery, isAqlLiteral, isAqlQuery } from \"./aql\";\nimport { ArangoApiResponse, Params } from \"./connection\";\nimport { ArrayCursor, BatchedArrayCursor } from \"./cursor\";\nimport { Database } from \"./database\";\nimport {\n  Document,\n  DocumentData,\n  DocumentMetadata,\n  DocumentSelector,\n  Edge,\n  EdgeData,\n  ObjectWithKey,\n  Patch,\n  _documentHandle,\n} from \"./documents\";\nimport { HttpError, isArangoError } from \"./error\";\nimport {\n  EnsureFulltextIndexOptions,\n  EnsureGeoIndexOptions,\n  EnsureInvertedIndexOptions,\n  EnsurePersistentIndexOptions,\n  EnsureTtlIndexOptions,\n  EnsureMdiIndexOptions,\n  FulltextIndex,\n  GeoIndex,\n  Index,\n  IndexSelector,\n  InvertedIndex,\n  PersistentIndex,\n  TtlIndex,\n  MdiIndex,\n  _indexHandle,\n} from \"./indexes\";\nimport { Blob } from \"./lib/blob\";\nimport { COLLECTION_NOT_FOUND, DOCUMENT_NOT_FOUND } from \"./lib/codes\";\n\n/**\n * Indicates whether the given value represents an {@link ArangoCollection}.\n *\n * @param collection - A value that might be a collection.\n */\nexport function isArangoCollection(\n  collection: any\n): collection is ArangoCollection {\n  return Boolean(collection && collection.isArangoCollection);\n}\n\n/**\n * Coerces the given collection name or {@link ArangoCollection} object to\n * a string representing the collection name.\n *\n * @param collection - Collection name or {@link ArangoCollection} object.\n */\nexport function collectionToString(\n  collection: string | ArangoCollection\n): string {\n  if (isArangoCollection(collection)) {\n    return String(collection.name);\n  } else return String(collection).normalize(\"NFC\");\n}\n\n/**\n * A marker interface identifying objects that can be used in AQL template\n * strings to create references to ArangoDB collections.\n *\n * See {@link aql!aql}.\n */\nexport interface ArangoCollection {\n  /**\n   * @internal\n   *\n   * Indicates that this object represents an ArangoDB collection.\n   */\n  readonly isArangoCollection: true;\n  /**\n   * Name of the collection.\n   */\n  readonly name: string;\n}\n\n/**\n * Integer values indicating the collection type.\n */\nexport enum CollectionType {\n  DOCUMENT_COLLECTION = 2,\n  EDGE_COLLECTION = 3,\n}\n\n/**\n * Integer values indicating the collection loading status.\n */\nexport enum CollectionStatus {\n  NEWBORN = 1,\n  UNLOADED = 2,\n  LOADED = 3,\n  UNLOADING = 4,\n  DELETED = 5,\n  LOADING = 6,\n}\n\n/**\n * Type of key generator.\n */\nexport type KeyGenerator = \"traditional\" | \"autoincrement\" | \"uuid\" | \"padded\";\n\n/**\n * Strategy for sharding a collection.\n */\nexport type ShardingStrategy =\n  | \"hash\"\n  | \"enterprise-hash-smart-edge\"\n  | \"enterprise-hash-smart-vertex\"\n  | \"community-compat\"\n  | \"enterprise-compat\"\n  | \"enterprise-smart-edge-compat\";\n\n/**\n * Type of document reference.\n *\n * See {@link DocumentCollection#list} and {@link EdgeCollection#list}.\n *\n * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be\n * replaced with AQL queries.\n */\nexport type SimpleQueryListType = \"id\" | \"key\" | \"path\";\n\n/**\n * When a validation should be applied.\n *\n * * `\"none\"`: No validation.\n * * `\"new\"`: Newly inserted documents are validated.\n * * `\"moderate\"`: New and modified documents are validated unless the modified\n *   document was already invalid.\n * * `\"strict\"`: New and modified documents are always validated.\n */\nexport type ValidationLevel = \"none\" | \"new\" | \"moderate\" | \"strict\";\n\n/**\n * Write operation that can result in a computed value being computed.\n */\nexport type WriteOperation = \"insert\" | \"update\" | \"replace\";\n\n/**\n * Represents a bulk operation failure for an individual document.\n */\nexport type DocumentOperationFailure = {\n  /**\n   * Indicates that the operation failed.\n   */\n  error: true;\n  /**\n   * Human-readable description of the failure.\n   */\n  errorMessage: string;\n  /**\n   * Numeric representation of the failure.\n   */\n  errorNum: number;\n};\n\n/**\n * Metadata returned by a document operation.\n */\nexport type DocumentOperationMetadata = DocumentMetadata & {\n  /**\n   * Revision of the document that was updated or replaced by this operation.\n   */\n  _oldRev?: string;\n};\n\n/**\n * Properties defining a computed value.\n */\nexport type ComputedValueProperties = {\n  /**\n   * Name of the target attribute of the computed value.\n   */\n  name: string;\n  /**\n   * AQL `RETURN` expression that computes the value.\n   */\n  expression: string;\n  /**\n   * If set to `false`, the computed value will not be applied if the\n   * expression evaluates to `null`.\n   */\n  overwrite: boolean;\n  /**\n   * Which operations should result in the value being computed.\n   */\n  computeOn: WriteOperation[];\n  /**\n   * If set to `false`, the field will be unset if the expression evaluates to\n   * `null`. Otherwise the field will be set to the value `null`. Has no effect\n   * if `overwrite` is set to `false`.\n   */\n  keepNull: boolean;\n  /**\n   * Whether the write operation should fail if the expression produces a\n   * warning.\n   */\n  failOnWarning: boolean;\n};\n\n/**\n * General information about a collection.\n */\nexport type CollectionMetadata = {\n  /**\n   * Collection name.\n   */\n  name: string;\n  /**\n   * A globally unique identifier for this collection.\n   */\n  globallyUniqueId: string;\n  /**\n   * An integer indicating the collection loading status.\n   */\n  status: CollectionStatus;\n  /**\n   * An integer indicating the collection type.\n   */\n  type: CollectionType;\n  /**\n   * @internal\n   *\n   * Whether the collection is a system collection.\n   */\n  isSystem: boolean;\n};\n\n/**\n * An object defining the collection's key generation.\n */\nexport type CollectionKeyProperties = {\n  /**\n   * Type of key generator to use.\n   */\n  type: KeyGenerator;\n  /**\n   * Whether documents can be created with a user-specified `_key` attribute.\n   */\n  allowUserKeys: boolean;\n  /**\n   * (Autoincrement only.) How many steps to increment the key each time.\n   */\n  increment?: number;\n  /**\n   * (Autoincrement only.) Initial offset for the key.\n   */\n  offset?: number;\n  /**\n   * Most recent key that has been generated.\n   */\n  lastValue: number;\n};\n\n/**\n * Properties for validating documents in a collection.\n */\nexport type SchemaProperties = {\n  /**\n   * Type of document validation.\n   */\n  type: \"json\";\n  /**\n   * JSON Schema description of the validation schema for documents.\n   */\n  rule: any;\n  /**\n   * When validation should be applied.\n   */\n  level: ValidationLevel;\n  /**\n   * Message to be used if validation fails.\n   */\n  message: string;\n};\n\n/**\n * An object defining the properties of a collection.\n */\nexport type CollectionProperties = {\n  /**\n   * A human-readable representation of the collection loading status.\n   */\n  statusString: string;\n  /**\n   * Whether data should be synchronized to disk before returning from\n   * a document create, update, replace or removal operation.\n   */\n  waitForSync: boolean;\n  /**\n   * An object defining the collection's key generation.\n   */\n  keyOptions: CollectionKeyProperties;\n  /**\n   * Properties for validating documents in the collection.\n   */\n  schema: SchemaProperties | null;\n  /**\n   * (Cluster only.) Write concern for this collection.\n   */\n  writeConcern: number;\n  /**\n   * (Cluster only.) Number of shards of this collection.\n   */\n  numberOfShards?: number;\n  /**\n   * (Cluster only.) Keys of this collection that will be used for\n   * sharding.\n   */\n  shardKeys?: string[];\n  /**\n   * (Cluster only.) Replication factor of the collection.\n   */\n  replicationFactor?: number | \"satellite\";\n  /**\n   * (Cluster only.) Sharding strategy of the collection.\n   */\n  shardingStrategy?: ShardingStrategy;\n  /**\n   * (Enterprise Edition cluster only.) If set to a collection name, sharding\n   * of the new collection will follow the rules for that collection. As long\n   * as the new collection exists, the indicated collection can not be dropped.\n   */\n  distributeShardsLike?: string;\n  /**\n   * (Enterprise Edition cluster only.) Attribute containing the shard key\n   * value of the referred-to smart join collection.\n   */\n  smartJoinAttribute?: string;\n  /**\n   * (Enterprise Edition cluster only.) Attribute used for sharding.\n   */\n  smartGraphAttribute?: string;\n  /**\n   * Computed values applied to documents in this collection.\n   */\n  computedValues: ComputedValueProperties[];\n  /**\n   * Whether the in-memory hash cache is enabled for this collection.\n   */\n  cacheEnabled: boolean;\n  /**\n   * Whether the newer revision-based replication protocol is enabled for\n   * this collection.\n   */\n  syncByRevision: boolean;\n  /**\n   * (Enterprise Edition only.) Whether the collection is used in a SmartGraph or EnterpriseGraph.\n   */\n  isSmart?: boolean;\n  /**\n   * (Enterprise Edition only.) Whether the SmartGraph this collection belongs to is disjoint.\n   */\n  isDisjoint?: string;\n};\n\n// Options\n\n/**\n * Options for creating a computed value.\n */\nexport type ComputedValueOptions = {\n  /**\n   * Name of the target attribute of the computed value.\n   */\n  name: string;\n  /**\n   * AQL `RETURN` expression that computes the value.\n   *\n   * Note that when passing an AQL query object, the `bindVars` will be ignored.\n   */\n  expression: string | AqlLiteral | AqlQuery;\n  /**\n   * If set to `false`, the computed value will not be applied if the\n   * expression evaluates to `null`.\n   *\n   * Default: `true`\n   */\n  overwrite?: boolean;\n  /**\n   * Which operations should result in the value being computed.\n   *\n   * Default: `[\"insert\", \"update\", \"replace\"]`\n   */\n  computeOn?: WriteOperation[];\n  /**\n   * If set to `false`, the field will be unset if the expression evaluates to\n   * `null`. Otherwise the field will be set to the value `null`. Has no effect\n   * if `overwrite` is set to `false`.\n   *\n   * Default: `true`\n   */\n  keepNull?: boolean;\n  /**\n   * Whether the write operation should fail if the expression produces a\n   * warning.\n   *\n   * Default: `false`\n   */\n  failOnWarning?: boolean;\n};\n\n/**\n * Options for validating collection documents.\n */\nexport type SchemaOptions = {\n  /**\n   * JSON Schema description of the validation schema for documents.\n   */\n  rule: any;\n  /**\n   * When validation should be applied.\n   *\n   * Default: `\"strict\"`\n   */\n  level?: ValidationLevel;\n  /**\n   * Message to be used if validation fails.\n   */\n  message?: string;\n};\n\n/**\n * Options for setting a collection's properties.\n *\n * See {@link DocumentCollection#properties} and {@link EdgeCollection#properties}.\n */\nexport type CollectionPropertiesOptions = {\n  /**\n   * Whether data should be synchronized to disk before returning from\n   * a document create, update, replace or removal operation.\n   */\n  waitForSync?: boolean;\n  /**\n   * (Cluster only.) How many copies of each document should be kept in the\n   * cluster.\n   *\n   * Default: `1`\n   */\n  replicationFactor?: number | \"satellite\";\n  /**\n   * (Cluster only.) Write concern for this collection.\n   */\n  writeConcern?: number;\n  /**\n   * Options for validating documents in this collection.\n   */\n  schema?: SchemaOptions;\n  /**\n   * Computed values to apply to documents in this collection.\n   */\n  computedValues?: ComputedValueOptions[];\n  /**\n   * Whether the in-memory hash cache is enabled for this collection.\n   *\n   * Default: `false`\n   */\n  cacheEnabled?: boolean;\n};\n\n/**\n * Options for retrieving a collection checksum.\n */\nexport type CollectionChecksumOptions = {\n  /**\n   * If set to `true`, revision IDs will be included in the calculation\n   * of the checksum.\n   *\n   * Default: `false`\n   */\n  withRevisions?: boolean;\n  /**\n   * If set to `true`, document data will be included in the calculation\n   * of the checksum.\n   *\n   * Default: `false`\n   */\n  withData?: boolean;\n};\n\n/**\n * Options for dropping collections.\n */\nexport type CollectionDropOptions = {\n  /**\n   * Whether the collection is a system collection. If the collection is a\n   * system collection, this option must be set to `true` or ArangoDB will\n   * refuse to drop the collection.\n   *\n   * Default: `false`\n   */\n  isSystem?: boolean;\n};\n\n/**\n * An object defining the collection's key generation.\n */\nexport type CollectionKeyOptions = {\n  /**\n   * Type of key generator to use.\n   */\n  type?: KeyGenerator;\n  /**\n   * Unless set to `false`, documents can be created with a user-specified\n   * `_key` attribute.\n   *\n   * Default: `true`\n   */\n  allowUserKeys?: boolean;\n  /**\n   * (Autoincrement only.) How many steps to increment the key each time.\n   */\n  increment?: number;\n  /**\n   * (Autoincrement only.) Initial offset for the key.\n   */\n  offset?: number;\n};\n\n/**\n * Options for creating a collection.\n *\n * See {@link database.Database#createCollection}, {@link database.Database#createEdgeCollection}\n * and {@link DocumentCollection#create} or {@link EdgeCollection#create}.\n */\nexport type CreateCollectionOptions = {\n  /**\n   * If set to `true`, data will be synchronized to disk before returning from\n   * a document create, update, replace or removal operation.\n   *\n   * Default: `false`\n   */\n  waitForSync?: boolean;\n  /**\n   * @internal\n   *\n   * Whether the collection should be created as a system collection.\n   *\n   * Default: `false`\n   */\n  isSystem?: boolean;\n  /**\n   * An object defining the collection's key generation.\n   */\n  keyOptions?: CollectionKeyOptions;\n  /**\n   * Options for validating documents in the collection.\n   */\n  schema?: SchemaOptions;\n  /**\n   * (Cluster only.) Unless set to `false`, the server will wait for all\n   * replicas to create the collection before returning.\n   *\n   * Default: `true`\n   */\n  waitForSyncReplication?: boolean;\n  /**\n   * (Cluster only.) Unless set to `false`, the server will check whether\n   * enough replicas are available at creation time and bail out otherwise.\n   *\n   * Default: `true`\n   */\n  enforceReplicationFactor?: boolean;\n  /**\n   * (Cluster only.) Number of shards to distribute the collection across.\n   *\n   * Default: `1`\n   */\n  numberOfShards?: number;\n  /**\n   * (Cluster only.) Document attributes to use to determine the target shard\n   * for each document.\n   *\n   * Default: `[\"_key\"]`\n   */\n  shardKeys?: string[];\n  /**\n   * (Cluster only.) How many copies of each document should be kept in the\n   * cluster.\n   *\n   * Default: `1`\n   */\n  replicationFactor?: number;\n  /**\n   * (Cluster only.) Write concern for this collection.\n   */\n  writeConcern?: number;\n  /**\n   * (Cluster only.) Sharding strategy to use.\n   */\n  shardingStrategy?: ShardingStrategy;\n  /**\n   * (Enterprise Edition cluster only.) If set to a collection name, sharding\n   * of the new collection will follow the rules for that collection. As long\n   * as the new collection exists, the indicated collection can not be dropped.\n   */\n  distributeShardsLike?: string;\n  /**\n   * (Enterprise Edition cluster only.) Attribute containing the shard key\n   * value of the referred-to smart join collection.\n   */\n  smartJoinAttribute?: string;\n  /**\n   * (Enterprise Edition cluster only.) Attribute used for sharding.\n   */\n  smartGraphAttribute?: string;\n  /**\n   * Computed values to apply to documents in this collection.\n   */\n  computedValues?: ComputedValueOptions[];\n  /**\n   * Whether the in-memory hash cache is enabled for this collection.\n   */\n  cacheEnabled?: boolean;\n};\n\n/**\n * Options for checking whether a document exists in a collection.\n */\nexport type DocumentExistsOptions = {\n  /**\n   * If set to a document revision, the document will only match if its `_rev`\n   * matches the given revision.\n   */\n  ifMatch?: string;\n  /**\n   * If set to a document revision, the document will only match if its `_rev`\n   * does not match the given revision.\n   */\n  ifNoneMatch?: string;\n};\n\n/**\n * Options for retrieving a document from a collection.\n */\nexport type CollectionReadOptions = {\n  /**\n   * If set to `true`, `null` is returned instead of an exception being thrown\n   * if the document does not exist.\n   */\n  graceful?: boolean;\n  /**\n   * If set to `true`, the request will explicitly permit ArangoDB to return a\n   * potentially dirty or stale result and arangojs will load balance the\n   * request without distinguishing between leaders and followers.\n   */\n  allowDirtyRead?: boolean;\n  /**\n   * If set to a document revision, the request will fail with an error if the\n   * document exists but its `_rev` does not match the given revision.\n   */\n  ifMatch?: string;\n  /**\n   * If set to a document revision, the request will fail with an error if the\n   * document exists and its `_rev` matches the given revision. Note that an\n   * `HttpError` with code 304 will be thrown instead of an `ArangoError`.\n   */\n  ifNoneMatch?: string;\n};\n\n/**\n * Options for retrieving multiple documents from a collection.\n */\nexport type CollectionBatchReadOptions = {\n  /**\n   * If set to `true`, the request will explicitly permit ArangoDB to return a\n   * potentially dirty or stale result and arangojs will load balance the\n   * request without distinguishing between leaders and followers.\n   */\n  allowDirtyRead?: boolean;\n};\n\n/**\n * Options for inserting a new document into a collection.\n */\nexport type CollectionInsertOptions = {\n  /**\n   * If set to `true`, data will be synchronized to disk before returning.\n   *\n   * Default: `false`\n   */\n  waitForSync?: boolean;\n  /**\n   * If set to `true`, no data will be returned by the server. This option can\n   * be used to reduce network traffic.\n   *\n   * Default: `false`\n   */\n  silent?: boolean;\n  /**\n   * If set to `true`, the complete new document will be returned as the `new`\n   * property on the result object. Has no effect if `silent` is set to `true`.\n   *\n   * Default: `false`\n   */\n  returnNew?: boolean;\n  /**\n   * If set to `true`, the complete old document will be returned as the `old`\n   * property on the result object. Has no effect if `silent` is set to `true`.\n   * This option is only available when `overwriteMode` is set to `\"update\"` or\n   * `\"replace\"`.\n   *\n   * Default: `false`\n   */\n  returnOld?: boolean;\n  /**\n   * Defines what should happen if a document with the same `_key` or `_id`\n   * already exists, instead of throwing an exception.\n   *\n   * Default: `\"conflict\"\n   */\n  overwriteMode?: \"ignore\" | \"update\" | \"replace\" | \"conflict\";\n  /**\n   * If set to `false`, object properties that already exist in the old\n   * document will be overwritten rather than merged when an existing document\n   * with the same `_key` or `_id` is updated. This does not affect arrays.\n   *\n   * Default: `true`\n   */\n  mergeObjects?: boolean;\n  /**\n   * If set to `true`, new entries will be added to in-memory index caches if\n   * document insertions affect the edge index or cache-enabled persistent\n   * indexes.\n   *\n   * Default: `false`\n   */\n  refillIndexCaches?: boolean;\n  /**\n   * If set, the attribute with the name specified by the option is looked up\n   * in the stored document and the attribute value is compared numerically to\n   * the value of the versioning attribute in the supplied document that is\n   * supposed to update/replace it.\n   */\n  versionAttribute?: string;\n};\n\n/**\n * Options for replacing an existing document in a collection.\n */\nexport type CollectionReplaceOptions = {\n  /**\n   * If set to `true`, data will be synchronized to disk before returning.\n   *\n   * Default: `false`\n   */\n  waitForSync?: boolean;\n  /**\n   * If set to `true`, no data will be returned by the server. This option can\n   * be used to reduce network traffic.\n   *\n   * Default: `false`\n   */\n  silent?: boolean;\n  /**\n   * If set to `true`, the complete new document will be returned as the `new`\n   * property on the result object. Has no effect if `silent` is set to `true`.\n   *\n   * Default: `false`\n   */\n  returnNew?: boolean;\n  /**\n   * If set to `false`, the existing document will only be modified if its\n   * `_rev` property matches the same property on the new data.\n   *\n   * Default: `true`\n   */\n  ignoreRevs?: boolean;\n  /**\n   * If set to `true`, the complete old document will be returned as the `old`\n   * property on the result object. Has no effect if `silent` is set to `true`.\n   *\n   * Default: `false`\n   */\n  returnOld?: boolean;\n  /**\n   * If set to a document revision, the document will only be replaced if its\n   * `_rev` matches the given revision.\n   */\n  ifMatch?: string;\n  /**\n   * If set to `true`, existing entries in in-memory index caches will be\n   * updated if document replacements affect the edge index or cache-enabled\n   * persistent indexes.\n   *\n   * Default: `false`\n   */\n  refillIndexCaches?: boolean;\n  /**\n   * If set, the attribute with the name specified by the option is looked up\n   * in the stored document and the attribute value is compared numerically to\n   * the value of the versioning attribute in the supplied document that is\n   * supposed to update/replace it.\n   */\n  versionAttribute?: string;\n};\n\n/**\n * Options for updating a document in a collection.\n */\nexport type CollectionUpdateOptions = {\n  /**\n   * If set to `true`, data will be synchronized to disk before returning.\n   *\n   * Default: `false`\n   */\n  waitForSync?: boolean;\n  /**\n   * If set to `true`, no data will be returned by the server. This option can\n   * be used to reduce network traffic.\n   *\n   * Default: `false`\n   */\n  silent?: boolean;\n  /**\n   * If set to `true`, the complete new document will be returned as the `new`\n   * property on the result object. Has no effect if `silent` is set to `true`.\n   *\n   * Default: `false`\n   */\n  returnNew?: boolean;\n  /**\n   * If set to `false`, the existing document will only be modified if its\n   * `_rev` property matches the same property on the new data.\n   *\n   * Default: `true`\n   */\n  ignoreRevs?: boolean;\n  /**\n   * If set to `true`, the complete old document will be returned as the `old`\n   * property on the result object. Has no effect if `silent` is set to `true`.\n   *\n   * Default: `false`\n   */\n  returnOld?: boolean;\n  /**\n   * If set to `false`, properties with a value of `null` will be removed from\n   * the new document.\n   *\n   * Default: `true`\n   */\n  keepNull?: boolean;\n  /**\n   * If set to `false`, object properties that already exist in the old\n   * document will be overwritten rather than merged. This does not affect\n   * arrays.\n   *\n   * Default: `true`\n   */\n  mergeObjects?: boolean;\n  /**\n   * If set to a document revision, the document will only be updated if its\n   * `_rev` matches the given revision.\n   */\n  ifMatch?: string;\n  /**\n   * If set to `true`, existing entries in in-memory index caches will be\n   * updated if document updates affect the edge index or cache-enabled\n   * persistent indexes.\n   *\n   * Default: `false`\n   */\n  refillIndexCaches?: boolean;\n  /**\n   * If set, the attribute with the name specified by the option is looked up\n   * in the stored document and the attribute value is compared numerically to\n   * the value of the versioning attribute in the supplied document that is\n   * supposed to update/replace it.\n   */\n  versionAttribute?: string;\n};\n\n/**\n * Options for removing a document from a collection.\n */\nexport type CollectionRemoveOptions = {\n  /**\n   * If set to `true`, changes will be synchronized to disk before returning.\n   *\n   * Default: `false`\n   */\n  waitForSync?: boolean;\n  /**\n   * If set to `true`, the complete old document will be returned as the `old`\n   * property on the result object. Has no effect if `silent` is set to `true`.\n   *\n   * Default: `false`\n   */\n  returnOld?: boolean;\n  /**\n   * If set to `true`, no data will be returned by the server. This option can\n   * be used to reduce network traffic.\n   *\n   * Default: `false`\n   */\n  silent?: boolean;\n  /**\n   * If set to a document revision, the document will only be removed if its\n   * `_rev` matches the given revision.\n   */\n  ifMatch?: string;\n  /**\n   * If set to `true`, existing entries in in-memory index caches will be\n   * deleted if document removals affect the edge index or cache-enabled\n   * persistent indexes.\n   *\n   * Default: `false`\n   */\n  refillIndexCaches?: boolean;\n};\n\n/**\n * Options for bulk importing documents into a collection.\n */\nexport type CollectionImportOptions = {\n  /**\n   * (Edge collections only.) Prefix to prepend to `_from` attribute values.\n   */\n  fromPrefix?: string;\n  /**\n   * (Edge collections only.) Prefix to prepend to `_to` attribute values.\n   */\n  toPrefix?: string;\n  /**\n   * If set to `true`, the collection is truncated before the data is imported.\n   *\n   * Default: `false`\n   */\n  overwrite?: boolean;\n  /**\n   * Whether to wait for the documents to have been synced to disk.\n   */\n  waitForSync?: boolean;\n  /**\n   * Controls behavior when a unique constraint is violated on the document key.\n   *\n   * * `\"error\"`: the document will not be imported.\n   * * `\"update`: the document will be merged into the existing document.\n   * * `\"replace\"`: the document will replace the existing document.\n   * * `\"ignore\"`: the document will not be imported and the unique constraint\n   *   error will be ignored.\n   *\n   * Default: `\"error\"`\n   */\n  onDuplicate?: \"error\" | \"update\" | \"replace\" | \"ignore\";\n  /**\n   * If set to `true`, the import will abort if any error occurs.\n   */\n  complete?: boolean;\n  /**\n   * Whether the response should contain additional details about documents\n   * that could not be imported.\n   */\n  details?: boolean;\n};\n\n/**\n * Options for retrieving a document's edges from a collection.\n */\nexport type CollectionEdgesOptions = {\n  /**\n   * If set to `true`, the request will explicitly permit ArangoDB to return a\n   * potentially dirty or stale result and arangojs will load balance the\n   * request without distinguishing between leaders and followers.\n   */\n  allowDirtyRead?: boolean;\n};\n\n/**\n * Options for retrieving documents by example.\n *\n * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be\n * replaced with AQL queries.\n */\nexport type SimpleQueryByExampleOptions = {\n  /**\n   * Number of documents to skip in the query.\n   */\n  skip?: number;\n  /**\n   * Maximum number of documents to return.\n   */\n  limit?: number;\n  /**\n   * Number of result values to be transferred by the server in each\n   * network roundtrip (or \"batch\").\n   *\n   * Must be greater than zero.\n   *\n   * See also {@link database.QueryOptions}.\n   */\n  batchSize?: number;\n  /**\n   * Time-to-live for the cursor in seconds. The cursor results may be\n   * garbage collected by ArangoDB after this much time has passed.\n   *\n   * See also {@link database.QueryOptions}.\n   */\n  ttl?: number;\n};\n\n/**\n * Options for retrieving all documents in a collection.\n *\n * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be\n * replaced with AQL queries.\n */\nexport type SimpleQueryAllOptions = {\n  /**\n   * Number of documents to skip in the query.\n   */\n  skip?: number;\n  /**\n   * Maximum number of documents to return.\n   */\n  limit?: number;\n  /**\n   * Number of result values to be transferred by the server in each\n   * network roundtrip (or \"batch\").\n   *\n   * Must be greater than zero.\n   *\n   * See also {@link database.QueryOptions}.\n   */\n  batchSize?: number;\n  /**\n   * Time-to-live for the cursor in seconds. The cursor results may be\n   * garbage collected by ArangoDB after this much time has passed.\n   *\n   * See also {@link database.QueryOptions}.\n   */\n  ttl?: number;\n  /**\n   * If set to `true`, the query will be executed as a streaming query.\n   *\n   * See also {@link database.QueryOptions}.\n   */\n  stream?: boolean;\n};\n\n/**\n * Options for updating documents by example.\n *\n * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be\n * replaced with AQL queries.\n */\nexport type SimpleQueryUpdateByExampleOptions = {\n  /**\n   * If set to `false`, properties with a value of `null` will be removed from\n   * the new document.\n   *\n   * Default: `true`\n   */\n  keepNull?: boolean;\n  /**\n   * If set to `true`, the request will wait until all modifications have been\n   * synchronized to disk before returning successfully.\n   *\n   * Default: `false`\n   */\n  waitForSync?: boolean;\n  /**\n   * Maximum number of documents to return.\n   */\n  limit?: number;\n  /**\n   * If set to `false`, object properties that already exist in the old\n   * document will be overwritten rather than merged. This does not affect\n   * arrays.\n   *\n   * Default: `true`\n   */\n  mergeObjects?: boolean;\n};\n\n/**\n * Options for removing documents by example.\n *\n * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be\n * replaced with AQL queries.\n */\nexport type SimpleQueryRemoveByExampleOptions = {\n  /**\n   * If set to `true`, the request will wait until all modifications have been\n   * synchronized to disk before returning successfully.\n   *\n   * Default: `false`\n   */\n  waitForSync?: boolean;\n  /**\n   * Maximum number of documents to return.\n   */\n  limit?: number;\n};\n\n/**\n * Options for replacing documents by example.\n *\n * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be\n * replaced with AQL queries.\n */\nexport type SimpleQueryReplaceByExampleOptions =\n  SimpleQueryRemoveByExampleOptions;\n\n/**\n * Options for removing documents by keys.\n *\n * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be\n * replaced with AQL queries.\n */\nexport type SimpleQueryRemoveByKeysOptions = {\n  /**\n   * If set to `true`, the complete old document will be returned as the `old`\n   * property on the result object. Has no effect if `silent` is set to `true`.\n   *\n   * Default: `false`\n   */\n  returnOld?: boolean;\n  /**\n   * If set to `true`, no data will be returned by the server. This option can\n   * be used to reduce network traffic.\n   *\n   * Default: `false`\n   */\n  silent?: boolean;\n  /**\n   * If set to `true`, the request will wait until all modifications have been\n   * synchronized to disk before returning successfully.\n   *\n   * Default: `false`\n   */\n  waitForSync?: boolean;\n};\n\n/**\n * Options for performing a fulltext query.\n *\n * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be\n * replaced with AQL queries.\n */\nexport type SimpleQueryFulltextOptions = {\n  /**\n   * Unique identifier of the fulltext index to use to perform the query.\n   */\n  index?: string;\n  /**\n   * Maximum number of documents to return.\n   */\n  limit?: number;\n  /**\n   * Number of documents to skip in the query.\n   */\n  skip?: number;\n};\n\n/**\n * Options for performing a graph traversal.\n *\n * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and are\n * no longer supported in ArangoDB 3.12. They can be replaced with AQL queries.\n */\nexport type TraversalOptions = {\n  /**\n   * A string evaluating to the body of a JavaScript function to be executed\n   * on the server to initialize the traversal result object.\n   *\n   * The code has access to two variables: `config`, `result`.\n   * The code may modify the `result` object.\n   *\n   * **Note**: This code will be evaluated and executed on the\n   * server inside ArangoDB's embedded JavaScript environment and can not\n   * access any other variables.\n   *\n   * See the official ArangoDB documentation for\n   * [the JavaScript `@arangodb` module](https://www.arangodb.com/docs/stable/appendix-java-script-modules-arango-db.html)\n   * for information about accessing the database from within ArangoDB's\n   * server-side JavaScript environment.\n   */\n  init?: string;\n  /**\n   * A string evaluating to the body of a JavaScript function to be executed\n   * on the server to filter nodes.\n   *\n   * The code has access to three variables: `config`, `vertex`, `path`.\n   * The code may include a return statement for the following values:\n   *\n   * * `\"exclude\"`: The vertex will not be visited.\n   * * `\"prune\"`: The edges of the vertex will not be followed.\n   * * `\"\"` or `undefined`: The vertex will be visited and its edges followed.\n   * * an array including any of the above values.\n   *\n   * **Note**: This code will be evaluated and executed on the\n   * server inside ArangoDB's embedded JavaScript environment and can not\n   * access any other variables.\n   *\n   * See the official ArangoDB documentation for\n   * [the JavaScript `@arangodb` module](https://www.arangodb.com/docs/stable/appendix-java-script-modules-arango-db.html)\n   * for information about accessing the database from within ArangoDB's\n   * server-side JavaScript environment.\n   */\n  filter?: string;\n  /**\n   * A string evaluating to the body of a JavaScript function to be executed\n   * on the server to sort edges if `expander` is not set.\n   *\n   * The code has access to two variables representing edges: `l`, `r`.\n   * The code must return `-1` if `l < r`, `1` if `l > r` or `0` if both\n   * values are equal.\n   *\n   * **Note**: This code will be evaluated and executed on the\n   * server inside ArangoDB's embedded JavaScript environment and can not\n   * access any other variables.\n   *\n   * See the official ArangoDB documentation for\n   * [the JavaScript `@arangodb` module](https://www.arangodb.com/docs/stable/appendix-java-script-modules-arango-db.html)\n   * for information about accessing the database from within ArangoDB's\n   * server-side JavaScript environment.\n   */\n  sort?: string;\n  /**\n   * A string evaluating to the body of a JavaScript function to be executed\n   * on the server when a node is visited.\n   *\n   * The code has access to five variables: `config`, `result`, `vertex`,\n   * `path`, `connected`.\n   * The code may modify the `result` object.\n   *\n   * **Note**: This code will be evaluated and executed on the\n   * server inside ArangoDB's embedded JavaScript environment and can not\n   * access any other variables.\n   *\n   * See the official ArangoDB documentation for\n   * [the JavaScript `@arangodb` module](https://www.arangodb.com/docs/stable/appendix-java-script-modules-arango-db.html)\n   * for information about accessing the database from within ArangoDB's\n   * server-side JavaScript environment.\n   */\n  visitor?: string;\n  /**\n   * A string evaluating to the body of a JavaScript function to be executed\n   * on the server to use when `direction` is not set.\n   *\n   * The code has access to three variables: `config`, `vertex`, `path`.\n   * The code must return an array of objects with `edge` and `vertex`\n   * attributes representing the connections for the vertex.\n   *\n   * **Note**: This code will be evaluated and executed on the\n   * server inside ArangoDB's embedded JavaScript environment and can not\n   * access any other variables.\n   *\n   * See the official ArangoDB documentation for\n   * [the JavaScript `@arangodb` module](https://www.arangodb.com/docs/stable/appendix-java-script-modules-arango-db.html)\n   * for information about accessing the database from within ArangoDB's\n   * server-side JavaScript environment.\n   */\n  expander?: string;\n  /**\n   * Direction of the traversal, relative to the starting vertex if `expander`\n   * is not set.\n   */\n  direction?: \"inbound\" | \"outbound\" | \"any\";\n  /**\n   * Item iteration order.\n   */\n  itemOrder?: \"forward\" | \"backward\";\n  /**\n   * Traversal strategy.\n   */\n  strategy?: \"depthfirst\" | \"breadthfirst\";\n  /**\n   * Traversal order.\n   */\n  order?: \"preorder\" | \"postorder\" | \"preorder-expander\";\n  /**\n   * Specifies uniqueness for vertices and edges.\n   */\n  uniqueness?: {\n    /**\n     * Uniqueness for vertices.\n     */\n    vertices?: \"none\" | \"global\" | \"path\";\n    /**\n     * Uniqueness for edges.\n     */\n    edges?: \"none\" | \"global\" | \"path\";\n  };\n  /**\n   * If specified, only nodes in at least this depth will be visited.\n   */\n  minDepth?: number;\n  /**\n   * If specified, only nodes in at most this depth will be visited.\n   */\n  maxDepth?: number;\n  /**\n   * Maximum number of iterations before a traversal is aborted because of a\n   * potential endless loop.\n   */\n  maxIterations?: number;\n};\n\n// Results\n\n/**\n * Result of a collection bulk import.\n */\nexport type CollectionImportResult = {\n  /**\n   * Whether the import failed.\n   */\n  error: false;\n  /**\n   * Number of new documents imported.\n   */\n  created: number;\n  /**\n   * Number of documents that failed with an error.\n   */\n  errors: number;\n  /**\n   * Number of empty documents.\n   */\n  empty: number;\n  /**\n   * Number of documents updated.\n   */\n  updated: number;\n  /**\n   * Number of documents that failed with an error that is ignored.\n   */\n  ignored: number;\n  /**\n   * Additional details about any errors encountered during the import.\n   */\n  details?: string[];\n};\n\n/**\n * Result of retrieving edges in a collection.\n */\nexport type CollectionEdgesResult<T extends Record<string, any> = any> = {\n  edges: Edge<T>[];\n  stats: {\n    scannedIndex: number;\n    filtered: number;\n  };\n};\n\n/**\n * Result of removing documents by an example.\n *\n * See {@link DocumentCollection#removeByExample} and {@link EdgeCollection#removeByExample}.\n *\n * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be\n * replaced with AQL queries.\n */\nexport type SimpleQueryRemoveByExampleResult = {\n  /**\n   * Number of documents removed.\n   */\n  deleted: number;\n};\n\n/**\n * Result of replacing documents by an example.\n *\n * See {@link DocumentCollection#replaceByExample} and {@link EdgeCollection#replaceByExample}.\n *\n * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be\n * replaced with AQL queries.\n */\nexport type SimpleQueryReplaceByExampleResult = {\n  /**\n   * Number of documents replaced.\n   */\n  replaced: number;\n};\n\n/**\n * Result of updating documents by an example.\n *\n * See {@link DocumentCollection#updateByExample} and {@link EdgeCollection#updateByExample}.\n *\n * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be\n * replaced with AQL queries.\n */\nexport type SimpleQueryUpdateByExampleResult = {\n  /**\n   * Number of documents updated.\n   */\n  updated: number;\n};\n\n/**\n * Result of removing documents by keys.\n *\n * See {@link DocumentCollection#removeByKeys} and {@link EdgeCollection#removeByKeys}.\n *\n * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be\n * replaced with AQL queries.\n */\nexport type SimpleQueryRemoveByKeysResult<T extends Record<string, any> = any> =\n  {\n    /**\n     * Number of documents removed.\n     */\n    removed: number;\n    /**\n     * Number of documents not removed.\n     */\n    ignored: number;\n    /**\n     * Documents that have been removed.\n     */\n    old?: DocumentMetadata[] | Document<T>[];\n  };\n\n// Collections\n\n/**\n * Represents an document collection in a {@link database.Database}.\n *\n * See {@link EdgeCollection} for a variant of this interface more suited for\n * edge collections.\n *\n * When using TypeScript, collections can be cast to a specific document data\n * type to increase type safety.\n *\n * @param T - Type to use for document data. Defaults to `any`.\n *\n * @example\n * ```ts\n * interface Person {\n *   name: string;\n * }\n * const db = new Database();\n * const documents = db.collection(\"persons\") as DocumentCollection<Person>;\n * ```\n */\nexport interface DocumentCollection<T extends Record<string, any> = any>\n  extends ArangoCollection {\n  /**\n   * Checks whether the collection exists.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * const result = await collection.exists();\n   * // result indicates whether the collection exists\n   * ```\n   */\n  exists(): Promise<boolean>;\n  /**\n   * Retrieves general information about the collection.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * const data = await collection.get();\n   * // data contains general information about the collection\n   * ```\n   */\n  get(): Promise<ArangoApiResponse<CollectionMetadata>>;\n  /**\n   * Creates a collection with the given `options` and the instance's name.\n   *\n   * See also {@link database.Database#createCollection} and\n   * {@link database.Database#createEdgeCollection}.\n   *\n   * **Note**: When called on an {@link EdgeCollection} instance in TypeScript,\n   * the `type` option must still be set to the correct {@link CollectionType}.\n   * Otherwise this will result in the collection being created with the\n   * default type (i.e. as a document collection).\n   *\n   * @param options - Options for creating the collection.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"potatoes\");\n   * await collection.create();\n   * // the document collection \"potatoes\" now exists\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"friends\");\n   * await collection.create({ type: CollectionType.EDGE_COLLECTION });\n   * // the edge collection \"friends\" now exists\n   * ```\n   *\n   * @example\n   * ```ts\n   * interface Friend {\n   *   startDate: number;\n   *   endDate?: number;\n   * }\n   * const db = new Database();\n   * const collection = db.collection(\"friends\") as EdgeCollection<Friend>;\n   * // even in TypeScript you still need to indicate the collection type\n   * // if you want to create an edge collection\n   * await collection.create({ type: CollectionType.EDGE_COLLECTION });\n   * // the edge collection \"friends\" now exists\n   * ```\n   */\n  create(\n    options?: CreateCollectionOptions & {\n      type?: CollectionType;\n    }\n  ): Promise<ArangoApiResponse<CollectionMetadata & CollectionProperties>>;\n  /**\n   * Retrieves the collection's properties.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * const data = await collection.properties();\n   * // data contains the collection's properties\n   * ```\n   */\n  properties(): Promise<\n    ArangoApiResponse<CollectionMetadata & CollectionProperties>\n  >;\n  /**\n   * Replaces the properties of the collection.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * const result = await collection.setProperties({ waitForSync: true });\n   * // the collection will now wait for data being written to disk\n   * // whenever a document is changed\n   * ```\n   */\n  properties(\n    properties: CollectionPropertiesOptions\n  ): Promise<ArangoApiResponse<CollectionMetadata & CollectionProperties>>;\n  /**\n   * Retrieves information about the number of documents in a collection.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * const data = await collection.count();\n   * // data contains the collection's count\n   * ```\n   */\n  count(): Promise<\n    ArangoApiResponse<\n      CollectionMetadata & CollectionProperties & { count: number }\n    >\n  >;\n  /**\n   * (RocksDB only.) Instructs ArangoDB to recalculate the collection's\n   * document count to fix any inconsistencies.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"inconsistent-collection\");\n   * const badData = await collection.count();\n   * // oh no, the collection count looks wrong -- fix it!\n   * await collection.recalculateCount();\n   * const goodData = await collection.count();\n   * // goodData contains the collection's improved count\n   * ```\n   */\n  recalculateCount(): Promise<boolean>;\n  /**\n   * Retrieves statistics for a collection.\n   *\n   * @param details - whether to return extended storage engine-specific details\n   * to the figures, which may cause additional load and impact performance\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * const data = await collection.figures();\n   * // data contains the collection's figures\n   * ```\n   */\n  figures(\n    details?: boolean\n  ): Promise<\n    ArangoApiResponse<\n      CollectionMetadata &\n        CollectionProperties & { count: number; figures: Record<string, any> }\n    >\n  >;\n  /**\n   * Retrieves the collection revision ID.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * const data = await collection.revision();\n   * // data contains the collection's revision\n   * ```\n   */\n  revision(): Promise<\n    ArangoApiResponse<\n      CollectionMetadata & CollectionProperties & { revision: string }\n    >\n  >;\n  /**\n   * Retrieves the collection checksum.\n   *\n   * @param options - Options for retrieving the checksum.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * const data = await collection.checksum();\n   * // data contains the collection's checksum\n   * ```\n   */\n  checksum(\n    options?: CollectionChecksumOptions\n  ): Promise<\n    ArangoApiResponse<\n      CollectionMetadata & { revision: string; checksum: string }\n    >\n  >;\n  /**\n   * (RocksDB only.) Instructs ArangoDB to load as many indexes of the\n   * collection into memory as permitted by the memory limit.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"indexed-collection\");\n   * await collection.loadIndexes();\n   * // the indexes are now loaded into memory\n   * ```\n   */\n  loadIndexes(): Promise<boolean>;\n  /**\n   * Renames the collection and updates the instance's `name` to `newName`.\n   *\n   * Additionally removes the instance from the {@link database.Database}'s internal\n   * cache.\n   *\n   * **Note**: Renaming collections may not be supported when ArangoDB is\n   * running in a cluster configuration.\n   *\n   * @param newName - The new name of the collection.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection1 = db.collection(\"some-collection\");\n   * await collection1.rename(\"other-collection\");\n   * const collection2 = db.collection(\"some-collection\");\n   * const collection3 = db.collection(\"other-collection\");\n   * // Note all three collection instances are different objects but\n   * // collection1 and collection3 represent the same ArangoDB collection!\n   * ```\n   */\n  rename(newName: string): Promise<ArangoApiResponse<CollectionMetadata>>;\n  /**\n   * Deletes all documents in the collection.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * await collection.truncate();\n   * // millions of documents cry out in terror and are suddenly silenced,\n   * // the collection \"some-collection\" is now empty\n   * ```\n   */\n  truncate(): Promise<ArangoApiResponse<CollectionMetadata>>;\n  /**\n   * Deletes the collection from the database.\n   *\n   * @param options - Options for dropping the collection.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * await collection.drop();\n   * // The collection \"some-collection\" is now an ex-collection\n   * ```\n   */\n  drop(\n    options?: CollectionDropOptions\n  ): Promise<ArangoApiResponse<Record<string, never>>>;\n\n  //#region crud\n  /**\n   * Retrieves the `shardId` of the shard responsible for the given document.\n   *\n   * @param document - Document in the collection to look up the `shardId` of.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * const responsibleShard = await collection.getResponsibleShard();\n   * ```\n   */\n  getResponsibleShard(document: Partial<Document<T>>): Promise<string>;\n  /**\n   * Derives a document `_id` from the given selector for this collection.\n   *\n   * Throws an exception when passed a document or `_id` from a different\n   * collection.\n   *\n   * @param selector - Document `_key`, `_id` or object with either of those\n   * properties (e.g. a document from this collection).\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * const meta = await collection.save({ foo: \"bar\" }, { returnNew: true });\n   * const doc = meta.new;\n   * console.log(collection.documentId(meta)); // via meta._id\n   * console.log(collection.documentId(doc)); // via doc._id\n   * console.log(collection.documentId(meta._key)); // also works\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection1 = db.collection(\"some-collection\");\n   * const collection2 = db.collection(\"other-collection\");\n   * const meta = await collection1.save({ foo: \"bar\" });\n   * // Mixing collections is usually a mistake\n   * console.log(collection1.documentId(meta)); // ok: same collection\n   * console.log(collection2.documentId(meta)); // throws: wrong collection\n   * console.log(collection2.documentId(meta._id)); // also throws\n   * console.log(collection2.documentId(meta._key)); // ok but wrong collection\n   * ```\n   */\n  documentId(selector: DocumentSelector): string;\n  /**\n   * Checks whether a document matching the given key or id exists in this\n   * collection.\n   *\n   * Throws an exception when passed a document or `_id` from a different\n   * collection.\n   *\n   * @param selector - Document `_key`, `_id` or object with either of those\n   * properties (e.g. a document from this collection).\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * const exists = await collection.documentExists(\"abc123\");\n   * if (!exists) {\n   *   console.log(\"Document does not exist\");\n   * }\n   * ```\n   */\n  documentExists(\n    selector: DocumentSelector,\n    options?: DocumentExistsOptions\n  ): Promise<boolean>;\n  /**\n   * Retrieves the document matching the given key or id.\n   *\n   * Throws an exception when passed a document or `_id` from a different\n   * collection.\n   *\n   * @param selector - Document `_key`, `_id` or object with either of those\n   * properties (e.g. a document from this collection).\n   * @param options - Options for retrieving the document.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * try {\n   *   const document = await collection.document(\"abc123\");\n   *   console.log(document);\n   * } catch (e: any) {\n   *   console.error(\"Could not find document\");\n   * }\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * const document = await collection.document(\"abc123\", { graceful: true });\n   * if (document) {\n   *   console.log(document);\n   * } else {\n   *   console.error(\"Could not find document\");\n   * }\n   * ```\n   */\n  document(\n    selector: DocumentSelector,\n    options?: CollectionReadOptions\n  ): Promise<Document<T>>;\n  /**\n   * Retrieves the document matching the given key or id.\n   *\n   * Throws an exception when passed a document or `_id` from a different\n   * collection.\n   *\n   * @param selector - Document `_key`, `_id` or object with either of those\n   * properties (e.g. a document from this collection).\n   * @param graceful - If set to `true`, `null` is returned instead of an\n   * exception being thrown if the document does not exist.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * try {\n   *   const document = await collection.document(\"abc123\", false);\n   *   console.log(document);\n   * } catch (e: any) {\n   *   console.error(\"Could not find document\");\n   * }\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * const document = await collection.document(\"abc123\", true);\n   * if (document) {\n   *   console.log(document);\n   * } else {\n   *   console.error(\"Could not find document\");\n   * }\n   * ```\n   */\n  document(selector: DocumentSelector, graceful: boolean): Promise<Document<T>>;\n  /**\n   * Retrieves the documents matching the given key or id values.\n   *\n   * Throws an exception when passed a document or `_id` from a different\n   * collection, or if the document does not exist.\n   *\n   * @param selectors - Array of document `_key`, `_id` or objects with either\n   * of those properties (e.g. a document from this collection).\n   * @param options - Options for retrieving the documents.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * try {\n   *   const documents = await collection.documents([\"abc123\", \"xyz456\"]);\n   *   console.log(documents);\n   * } catch (e: any) {\n   *   console.error(\"Could not find document\");\n   * }\n   * ```\n   */\n  documents(\n    selectors: (string | ObjectWithKey)[],\n    options?: CollectionBatchReadOptions\n  ): Promise<Document<T>[]>;\n  /**\n   * Inserts a new document with the given `data` into the collection.\n   *\n   * @param data - The contents of the new document.\n   * @param options - Options for inserting the document.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * const result = await collection.save(\n   *   { _key: \"a\", color: \"blue\", count: 1 },\n   *   { returnNew: true }\n   * );\n   * console.log(result.new.color, result.new.count); // \"blue\" 1\n   * ```\n   */\n  save(\n    data: DocumentData<T>,\n    options?: CollectionInsertOptions\n  ): Promise<\n    DocumentOperationMetadata & { new?: Document<T>; old?: Document<T> }\n  >;\n  /**\n   * Inserts new documents with the given `data` into the collection.\n   *\n   * @param data - The contents of the new documents.\n   * @param options - Options for inserting the documents.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * const result = await collection.saveAll(\n   *   [\n   *     { _key: \"a\", color: \"blue\", count: 1 },\n   *     { _key: \"b\", color: \"red\", count: 2 },\n   *   ],\n   *   { returnNew: true }\n   * );\n   * console.log(result[0].new.color, result[0].new.count); // \"blue\" 1\n   * console.log(result[1].new.color, result[1].new.count); // \"red\" 2\n   * ```\n   */\n  saveAll(\n    data: Array<DocumentData<T>>,\n    options?: CollectionInsertOptions\n  ): Promise<\n    Array<\n      | (DocumentOperationMetadata & { new?: Document<T>; old?: Document<T> })\n      | DocumentOperationFailure\n    >\n  >;\n  /**\n   * Replaces an existing document in the collection.\n   *\n   * Throws an exception when passed a document or `_id` from a different\n   * collection.\n   *\n   * @param selector - Document `_key`, `_id` or object with either of those\n   * properties (e.g. a document from this collection).\n   * @param newData - The contents of the new document.\n   * @param options - Options for replacing the document.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * await collection.save({ _key: \"a\", color: \"blue\", count: 1 });\n   * const result = await collection.replace(\n   *   \"a\",\n   *   { color: \"red\" },\n   *   { returnNew: true }\n   * );\n   * console.log(result.new.color, result.new.count); // \"red\" undefined\n   * ```\n   */\n  replace(\n    selector: DocumentSelector,\n    newData: DocumentData<T>,\n    options?: CollectionReplaceOptions\n  ): Promise<\n    DocumentOperationMetadata & { new?: Document<T>; old?: Document<T> }\n  >;\n  /**\n   * Replaces existing documents in the collection, identified by the `_key` or\n   * `_id` of each document.\n   *\n   * @param newData - The documents to replace.\n   * @param options - Options for replacing the documents.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * await collection.save({ _key: \"a\", color: \"blue\", count: 1 });\n   * await collection.save({ _key: \"b\", color: \"green\", count: 3 });\n   * const result = await collection.replaceAll(\n   *   [\n   *     { _key: \"a\", color: \"red\" },\n   *     { _key: \"b\", color: \"yellow\", count: 2 }\n   *   ],\n   *   { returnNew: true }\n   * );\n   * console.log(result[0].new.color, result[0].new.count); // \"red\" undefined\n   * console.log(result[1].new.color, result[1].new.count); // \"yellow\" 2\n   * ```\n   */\n  replaceAll(\n    newData: Array<DocumentData<T> & ({ _key: string } | { _id: string })>,\n    options?: Omit<CollectionReplaceOptions, \"ifMatch\">\n  ): Promise<\n    Array<\n      | (DocumentOperationMetadata & { new?: Document<T>; old?: Document<T> })\n      | DocumentOperationFailure\n    >\n  >;\n  /**\n   * Updates an existing document in the collection.\n   *\n   * Throws an exception when passed a document or `_id` from a different\n   * collection.\n   *\n   * @param selector - Document `_key`, `_id` or object with either of those\n   * properties (e.g. a document from this collection).\n   * @param newData - The data for updating the document.\n   * @param options - Options for updating the document.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * await collection.save({ _key: \"a\", color: \"blue\", count: 1 });\n   * const result = await collection.update(\n   *   \"a\",\n   *   { count: 2 },\n   *   { returnNew: true }\n   * );\n   * console.log(result.new.color, result.new.count); // \"blue\" 2\n   * ```\n   */\n  update(\n    selector: DocumentSelector,\n    newData: Patch<DocumentData<T>>,\n    options?: CollectionUpdateOptions\n  ): Promise<\n    DocumentOperationMetadata & { new?: Document<T>; old?: Document<T> }\n  >;\n  /**\n   * Updates existing documents in the collection, identified by the `_key` or\n   * `_id` of each document.\n   *\n   * @param newData - The data for updating the documents.\n   * @param options - Options for updating the documents.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * await collection.save({ _key: \"a\", color: \"blue\", count: 1 });\n   * await collection.save({ _key: \"b\", color: \"green\", count: 3 });\n   * const result = await collection.updateAll(\n   *   [\n   *     { _key: \"a\", count: 2 },\n   *     { _key: \"b\", count: 4 }\n   *   ],\n   *   { returnNew: true }\n   * );\n   * console.log(result[0].new.color, result[0].new.count); // \"blue\" 2\n   * console.log(result[1].new.color, result[1].new.count); // \"green\" 4\n   * ```\n   */\n  updateAll(\n    newData: Array<\n      Patch<DocumentData<T>> & ({ _key: string } | { _id: string })\n    >,\n    options?: Omit<CollectionUpdateOptions, \"ifMatch\">\n  ): Promise<\n    Array<\n      | (DocumentOperationMetadata & { new?: Document<T>; old?: Document<T> })\n      | DocumentOperationFailure\n    >\n  >;\n  /**\n   * Removes an existing document from the collection.\n   *\n   * Throws an exception when passed a document or `_id` from a different\n   * collection.\n   *\n   * @param selector - Document `_key`, `_id` or object with either of those\n   * properties (e.g. a document from this collection).\n   * @param options - Options for removing the document.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * await collection.remove(\"abc123\");\n   * // document with key \"abc123\" deleted\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * const doc = await collection.document(\"abc123\");\n   * await collection.remove(doc);\n   * // document with key \"abc123\" deleted\n   * ```\n   */\n  remove(\n    selector: DocumentSelector,\n    options?: CollectionRemoveOptions\n  ): Promise<DocumentMetadata & { old?: Document<T> }>;\n  /**\n   * Removes existing documents from the collection.\n   *\n   * Throws an exception when passed any document or `_id` from a different\n   * collection.\n   *\n   * @param selectors - Documents `_key`, `_id` or objects with either of those\n   * properties (e.g. documents from this collection).\n   * @param options - Options for removing the documents.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * await collection.removeAll([\"abc123\", \"def456\"]);\n   * // document with keys \"abc123\" and \"def456\" deleted\n   * ```\n   */\n  removeAll(\n    selectors: (string | ObjectWithKey)[],\n    options?: Omit<CollectionRemoveOptions, \"ifMatch\">\n  ): Promise<\n    Array<(DocumentMetadata & { old?: Document<T> }) | DocumentOperationFailure>\n  >;\n  /**\n   * Bulk imports the given `data` into the collection.\n   *\n   * @param data - The data to import, as an array of document data.\n   * @param options - Options for importing the data.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * await collection.import(\n   *   [\n   *     { _key: \"jcd\", password: \"bionicman\" },\n   *     { _key: \"jreyes\", password: \"amigo\" },\n   *     { _key: \"ghermann\", password: \"zeitgeist\" }\n   *   ]\n   * );\n   * ```\n   */\n  import(\n    data: DocumentData<T>[],\n    options?: CollectionImportOptions\n  ): Promise<CollectionImportResult>;\n  /**\n   * Bulk imports the given `data` into the collection.\n   *\n   * @param data - The data to import, as an array containing a single array of\n   * attribute names followed by one or more arrays of attribute values for\n   * each document.\n   * @param options - Options for importing the data.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * await collection.import(\n   *   [\n   *     [ \"_key\", \"password\" ],\n   *     [ \"jcd\", \"bionicman\" ],\n   *     [ \"jreyes\", \"amigo\" ],\n   *     [ \"ghermann\", \"zeitgeist\" ]\n   *   ]\n   * );\n   * ```\n   */\n  import(\n    data: any[][],\n    options?: CollectionImportOptions\n  ): Promise<CollectionImportResult>;\n  /**\n   * Bulk imports the given `data` into the collection.\n   *\n   * If `type` is omitted, `data` must contain one JSON array per line with\n   * the first array providing the attribute names and all other arrays\n   * providing attribute values for each document.\n   *\n   * If `type` is set to `\"documents\"`, `data` must contain one JSON document\n   * per line.\n   *\n   * If `type` is set to `\"list\"`, `data` must contain a JSON array of\n   * documents.\n   *\n   * If `type` is set to `\"auto\"`, `data` can be in either of the formats\n   * supported by `\"documents\"` or `\"list\"`.\n   *\n   * @param data - The data to import as a Buffer (Node), Blob (browser) or\n   * string.\n   * @param options - Options for importing the data.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * await collection.import(\n   *   '{\"_key\":\"jcd\",\"password\":\"bionicman\"}\\r\\n' +\n   *   '{\"_key\":\"jreyes\",\"password\":\"amigo\"}\\r\\n' +\n   *   '{\"_key\":\"ghermann\",\"password\":\"zeitgeist\"}\\r\\n',\n   *   { type: \"documents\" } // or \"auto\"\n   * );\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * await collection.import(\n   *   '[{\"_key\":\"jcd\",\"password\":\"bionicman\"},' +\n   *   '{\"_key\":\"jreyes\",\"password\":\"amigo\"},' +\n   *   '{\"_key\":\"ghermann\",\"password\":\"zeitgeist\"}]',\n   *   { type: \"list\" } // or \"auto\"\n   * );\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * await collection.import(\n   *   '[\"_key\",\"password\"]\\r\\n' +\n   *   '[\"jcd\",\"bionicman\"]\\r\\n' +\n   *   '[\"jreyes\",\"amigo\"]\\r\\n' +\n   *   '[\"ghermann\",\"zeitgeist\"]\\r\\n'\n   * );\n   * ```\n   */\n  import(\n    data: Buffer | Blob | string,\n    options?: CollectionImportOptions & {\n      type?: \"documents\" | \"list\" | \"auto\";\n    }\n  ): Promise<CollectionImportResult>;\n  //#endregion\n\n  //#region simple queries\n\n  /**\n   * Retrieves a list of references for all documents in the collection.\n   *\n   * @param type - The type of document reference to retrieve.\n   *\n   * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be\n   * replaced with AQL queries.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * // const ids = await collection.list(\"id\");\n   * const ids = await db.query(aql`\n   *   FOR doc IN ${collection}\n   *   RETURN doc._id\n   * `);\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * // const keys = await collection.list(\"key\");\n   * const keys = await db.query(aql`\n   *   FOR doc IN ${collection}\n   *   RETURN doc._key\n   * `);\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * // const paths = await collection.list(\"path\");\n   * const paths = await db.query(aql`\n   *   FOR doc IN ${collection}\n   *   RETURN CONCAT(\"/_db/\", CURRENT_DATABASE(), \"/_api/document/\", doc._id)\n   * `);\n   * ```\n   */\n  list(type?: SimpleQueryListType): Promise<ArrayCursor<string>>;\n\n  /**\n   * Retrieves all documents in the collection.\n   *\n   * @param options - Options for retrieving the documents.\n   *\n   * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be\n   * replaced with AQL queries.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * // const cursor = await collection.all();\n   * const cursor = await db.query(aql`\n   *   FOR doc IN ${collection}\n   *   RETURN doc\n   * `);\n   * ```\n   */\n  all(options?: SimpleQueryAllOptions): Promise<ArrayCursor<Document<T>>>;\n\n  /**\n   * Retrieves a random document from the collection.\n   *\n   * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be\n   * replaced with AQL queries.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * // const doc = await collection.any();\n   * const cursor = await db.query(aql`\n   *   FOR doc IN ${collection}\n   *   SORT RAND()\n   *   LIMIT 1\n   *   RETURN doc\n   * `);\n   * const doc = await cursor.next();\n   * ```\n   */\n  any(): Promise<Document<T>>;\n\n  /**\n   * Retrieves all documents in the collection matching the given example.\n   *\n   * @param example - An object representing an example for documents.\n   * @param options - Options for retrieving the documents.\n   *\n   * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be\n   * replaced with AQL queries.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * // const cursor = await collection.byExample({ flavor: \"strawberry\" });\n   * const cursor = await db.query(aql`\n   *   FOR doc IN ${collection}\n   *   FILTER doc.flavor == \"strawberry\"\n   *   RETURN doc\n   * `);\n   * ```\n   */\n  byExample(\n    example: Partial<DocumentData<T>>,\n    options?: SimpleQueryByExampleOptions\n  ): Promise<ArrayCursor<Document<T>>>;\n\n  /**\n   * Retrieves a single document in the collection matching the given example.\n   *\n   * @param example - An object representing an example for the document.\n   *\n   * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be\n   * replaced with AQL queries.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * // const doc = await collection.firstExample({ flavor: \"strawberry\" });\n   * const cursor = await db.query(aql`\n   *   FOR doc IN ${collection}\n   *   FILTER doc.flavor == \"strawberry\"\n   *   LIMIT 1\n   *   RETURN doc\n   * `);\n   * const doc = await cursor.next();\n   * ```\n   */\n  firstExample(example: Partial<DocumentData<T>>): Promise<Document<T>>;\n\n  /**\n   * Removes all documents in the collection matching the given example.\n   *\n   * @param example - An object representing an example for the document.\n   * @param options - Options for removing the documents.\n   *\n   * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be\n   * replaced with AQL queries.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * // const { deleted } = await collection.removeByExample({\n   * //   flavor: \"strawberry\"\n   * // });\n   * const cursor = await db.query(aql`\n   *   RETURN LENGTH(\n   *     FOR doc IN ${collection}\n   *     FILTER doc.flavor == \"strawberry\"\n   *     REMOVE doc IN ${collection}\n   *     RETURN 1\n   *   )\n   * `);\n   * const deleted = await cursor.next();\n   * ```\n   */\n  removeByExample(\n    example: Partial<DocumentData<T>>,\n    options?: SimpleQueryRemoveByExampleOptions\n  ): Promise<ArangoApiResponse<SimpleQueryRemoveByExampleResult>>;\n\n  /**\n   * Replaces all documents in the collection matching the given example.\n   *\n   * @param example - An object representing an example for the documents.\n   * @param newValue - Document data to replace the matching documents with.\n   * @param options - Options for replacing the documents.\n   *\n   * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be\n   * replaced with AQL queries.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * const newValue = { flavor: \"chocolate\" };\n   * // const { replaced } = await collection.replaceByExample(\n   * //   { flavor: \"strawberry\" },\n   * //   newValue\n   * // );\n   * const cursor = await db.query(aql`\n   *   RETURN LENGTH(\n   *     FOR doc IN ${collection}\n   *     FILTER doc.flavor == \"strawberry\"\n   *     REPLACE doc WITH ${newValue} IN ${collection}\n   *     RETURN 1\n   *   )\n   * `);\n   * const replaced = await cursor.next();\n   * ```\n   */\n  replaceByExample(\n    example: Partial<DocumentData<T>>,\n    newValue: DocumentData<T>,\n    options?: SimpleQueryReplaceByExampleOptions\n  ): Promise<ArangoApiResponse<SimpleQueryReplaceByExampleResult>>;\n\n  /**\n   * Updates all documents in the collection matching the given example.\n   *\n   * @param example - An object representing an example for the documents.\n   * @param newValue - Document data to update the matching documents with.\n   * @param options - Options for updating the documents.\n   *\n   * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be\n   * replaced with AQL queries.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * const newData = { color: \"red\" };\n   * // const { updated } = await collection.updateByExample(\n   * //   { flavor: \"strawberry\" },\n   * //   newValue\n   * // );\n   * const cursor = await db.query(aql`\n   *   RETURN LENGTH(\n   *     FOR doc IN ${collection}\n   *     FILTER doc.flavor == \"strawberry\"\n   *     UPDATE doc WITH ${newValue} IN ${collection}\n   *     RETURN 1\n   *   )\n   * `);\n   * const updated = await cursor.next();\n   * ```\n   */\n  updateByExample(\n    example: Partial<DocumentData<T>>,\n    newValue: Patch<DocumentData<T>>,\n    options?: SimpleQueryUpdateByExampleOptions\n  ): Promise<ArangoApiResponse<SimpleQueryUpdateByExampleResult>>;\n\n  /**\n   * Retrieves all documents matching the given document keys.\n   *\n   * @param keys - An array of document keys to look up.\n   *\n   * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be\n   * replaced with AQL queries.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * const keys = [\"a\", \"b\", \"c\"];\n   * // const docs = await collection.byKeys(keys);\n   * const cursor = await db.query(aql`\n   *   FOR key IN ${keys}\n   *   LET doc = DOCUMENT(${collection}, key)\n   *   RETURN doc\n   * `);\n   * const docs = await cursor.all();\n   * ```\n   */\n  lookupByKeys(keys: string[]): Promise<Document<T>[]>;\n\n  /**\n   * Removes all documents matching the given document keys.\n   *\n   * @param keys - An array of document keys to remove.\n   * @param options - Options for removing the documents.\n   *\n   * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be\n   * replaced with AQL queries.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * const keys = [\"a\", \"b\", \"c\"];\n   * // const { removed, ignored } = await collection.removeByKeys(keys);\n   * const cursor = await db.query(aql`\n   *   FOR key IN ${keys}\n   *   LET doc = DOCUMENT(${collection}, key)\n   *   FILTER doc\n   *   REMOVE doc IN ${collection}\n   *   RETURN key\n   * `);\n   * const removed = await cursor.all();\n   * const ignored = keys.filter((key) => !removed.includes(key));\n   * ```\n   */\n  removeByKeys(\n    keys: string[],\n    options?: SimpleQueryRemoveByKeysOptions\n  ): Promise<ArangoApiResponse<SimpleQueryRemoveByKeysResult<T>>>;\n\n  /**\n   * Performs a fulltext query in the given `attribute` on the collection.\n   *\n   * @param attribute - Name of the field to search.\n   * @param query - Fulltext query string to search for.\n   * @param options - Options for performing the fulltext query.\n   *\n   * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be\n   * replaced with AQL queries.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * // const cursor = await collection.fulltext(\"article\", \"needle\");\n   * const cursor = await db.query(aql`\n   *   FOR doc IN FULLTEXT(${collection}, \"article\", \"needle\")\n   *   RETURN doc\n   * `);\n   * ```\n   */\n  fulltext(\n    attribute: string,\n    query: string,\n    options?: SimpleQueryFulltextOptions\n  ): Promise<ArrayCursor<Document<T>>>;\n  //#endregion\n\n  //#region indexes\n  /**\n   * Returns a list of all index descriptions for the collection.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * const indexes = await collection.indexes();\n   * ```\n   */\n  indexes(): Promise<Index[]>;\n  /**\n   * Returns an index description by name or `id` if it exists.\n   *\n   * @param selector - Index name, id or object with either property.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * const index = await collection.index(\"some-index\");\n   * ```\n   */\n  index(selector: IndexSelector): Promise<Index>;\n  /**\n   * Creates a persistent index on the collection if it does not already exist.\n   *\n   * @param details - Options for creating the persistent index.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * // Create a unique index for looking up documents by username\n   * await collection.ensureIndex({\n   *   type: \"persistent\",\n   *   fields: [\"username\"],\n   *   name: \"unique-usernames\",\n   *   unique: true\n   * });\n   * ```\n   */\n  ensureIndex(\n    details: EnsurePersistentIndexOptions\n  ): Promise<ArangoApiResponse<PersistentIndex & { isNewlyCreated: boolean }>>;\n  /**\n   * Creates a TTL index on the collection if it does not already exist.\n   *\n   * @param details - Options for creating the TTL index.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * // Expire documents with \"createdAt\" timestamp one day after creation\n   * await collection.ensureIndex({\n   *   type: \"ttl\",\n   *   fields: [\"createdAt\"],\n   *   expireAfter: 60 * 60 * 24 // 24 hours\n   * });\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * // Expire documents with \"expiresAt\" timestamp according to their value\n   * await collection.ensureIndex({\n   *   type: \"ttl\",\n   *   fields: [\"expiresAt\"],\n   *   expireAfter: 0 // when attribute value is exceeded\n   * });\n   * ```\n   */\n  ensureIndex(\n    details: EnsureTtlIndexOptions\n  ): Promise<ArangoApiResponse<TtlIndex & { isNewlyCreated: boolean }>>;\n  /**\n   * Creates a multi-dimensional index on the collection if it does not already exist.\n   *\n   * @param details - Options for creating the multi-dimensional index.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-points\");\n   * // Create a multi-dimensional index for the attributes x, y and z\n   * await collection.ensureIndex({\n   *   type: \"mdi\",\n   *   fields: [\"x\", \"y\", \"z\"],\n   *   fieldValueTypes: \"double\"\n   * });\n   * ```\n   * ```\n   */\n  ensureIndex(\n    details: EnsureMdiIndexOptions\n  ): Promise<ArangoApiResponse<MdiIndex & { isNewlyCreated: boolean }>>;\n  /**\n   * Creates a fulltext index on the collection if it does not already exist.\n   *\n   * @param details - Options for creating the fulltext index.\n   *\n   * @deprecated Fulltext indexes have been deprecated in ArangoDB 3.10 and\n   * should be replaced with ArangoSearch.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * // Create a fulltext index for tokens longer than or equal to 3 characters\n   * await collection.ensureIndex({\n   *   type: \"fulltext\",\n   *   fields: [\"description\"],\n   *   minLength: 3\n   * });\n   * ```\n   */\n  ensureIndex(\n    details: EnsureFulltextIndexOptions\n  ): Promise<ArangoApiResponse<FulltextIndex & { isNewlyCreated: boolean }>>;\n  /**\n   * Creates a geo index on the collection if it does not already exist.\n   *\n   * @param details - Options for creating the geo index.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * // Create an index for GeoJSON data\n   * await collection.ensureIndex({\n   *   type: \"geo\",\n   *   fields: [\"lngLat\"],\n   *   geoJson: true\n   * });\n   * ```\n   */\n  ensureIndex(\n    details: EnsureGeoIndexOptions\n  ): Promise<ArangoApiResponse<GeoIndex & { isNewlyCreated: boolean }>>;\n  /**\n   * Creates a inverted index on the collection if it does not already exist.\n   *\n   * @param details - Options for creating the inverted index.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * // Create an inverted index\n   * await collection.ensureIndex({\n   *   type: \"inverted\",\n   *   fields: [\"a\", { name: \"b\", analyzer: \"text_en\" }]\n   * });\n   * ```\n   */\n  ensureIndex(\n    details: EnsureInvertedIndexOptions\n  ): Promise<ArangoApiResponse<InvertedIndex & { isNewlyCreated: boolean }>>;\n  /**\n   * Deletes the index with the given name or `id` from the database.\n   *\n   * @param selector - Index name, id or object with either property.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * await collection.dropIndex(\"some-index\");\n   * // The index \"some-index\" no longer exists\n   * ```\n   */\n  dropIndex(\n    selector: IndexSelector\n  ): Promise<ArangoApiResponse<{ id: string }>>;\n  /**\n   * Triggers compaction for a collection.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * await collection.compact();\n   * // Background compaction is triggered on the collection\n   * ```\n   */\n  compact(): Promise<ArangoApiResponse<Record<string, never>>>;\n  //#endregion\n}\n\n/**\n * Represents an edge collection in a {@link database.Database}.\n *\n * See {@link DocumentCollection} for a more generic variant of this interface\n * more suited for regular document collections.\n *\n * See also {@link graph.GraphEdgeCollection} for the type representing an edge\n * collection in a {@link graph.Graph}.\n *\n * When using TypeScript, collections can be cast to a specific edge document\n * data type to increase type safety.\n *\n * @param T - Type to use for edge document data. Defaults to `any`.\n *\n * @example\n * ```ts\n * interface Friend {\n *   startDate: number;\n *   endDate?: number;\n * }\n * const db = new Database();\n * const edges = db.collection(\"friends\") as EdgeCollection<Friend>;\n * ```\n */\nexport interface EdgeCollection<T extends Record<string, any> = any>\n  extends DocumentCollection<T> {\n  /**\n   * Retrieves the document matching the given key or id.\n   *\n   * Throws an exception when passed a document or `_id` from a different\n   * collection, or if the document does not exist.\n   *\n   * @param selector - Document `_key`, `_id` or object with either of those\n   * properties (e.g. a document from this collection).\n   * @param options - Options for retrieving the document.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * try {\n   *   const document = await collection.document(\"abc123\");\n   *   console.log(document);\n   * } catch (e: any) {\n   *   console.error(\"Could not find document\");\n   * }\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * const document = await collection.document(\"abc123\", { graceful: true });\n   * if (document) {\n   *   console.log(document);\n   * } else {\n   *   console.error(\"Document does not exist\");\n   * }\n   * ```\n   */\n  document(\n    selector: DocumentSelector,\n    options?: CollectionReadOptions\n  ): Promise<Edge<T>>;\n  /**\n   * Retrieves the document matching the given key or id.\n   *\n   * Throws an exception when passed a document or `_id` from a different\n   * collection, or if the document does not exist.\n   *\n   * @param selector - Document `_key`, `_id` or object with either of those\n   * properties (e.g. a document from this collection).\n   * @param graceful - If set to `true`, `null` is returned instead of an\n   * exception being thrown if the document does not exist.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * try {\n   *   const document = await collection.document(\"abc123\", false);\n   *   console.log(document);\n   * } catch (e: any) {\n   *   console.error(\"Could not find document\");\n   * }\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * const document = await collection.document(\"abc123\", true);\n   * if (document) {\n   *   console.log(document);\n   * } else {\n   *   console.error(\"Document does not exist\");\n   * }\n   * ```\n   */\n  document(selector: DocumentSelector, graceful: boolean): Promise<Edge<T>>;\n  /**\n   * Retrieves the documents matching the given key or id values.\n   *\n   * Throws an exception when passed a document or `_id` from a different\n   * collection, or if the document does not exist.\n   *\n   * @param selectors - Array of document `_key`, `_id` or objects with either\n   * of those properties (e.g. a document from this collection).\n   * @param options - Options for retrieving the documents.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * try {\n   *   const documents = await collection.documents([\"abc123\", \"xyz456\"]);\n   *   console.log(documents);\n   * } catch (e: any) {\n   *   console.error(\"Could not find document\");\n   * }\n   * ```\n   */\n  documents(\n    selectors: (string | ObjectWithKey)[],\n    options?: CollectionBatchReadOptions\n  ): Promise<Edge<T>[]>;\n  /**\n   * Inserts a new document with the given `data` into the collection.\n   *\n   * @param data - The contents of the new document.\n   * @param options - Options for inserting the document.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"friends\");\n   * const result = await collection.save(\n   *   { _from: \"users/rana\", _to: \"users/mudasir\", active: false },\n   *   { returnNew: true }\n   * );\n   * ```\n   */\n  save(\n    data: EdgeData<T>,\n    options?: CollectionInsertOptions\n  ): Promise<DocumentOperationMetadata & { new?: Edge<T>; old?: Edge<T> }>;\n  /**\n   * Inserts new documents with the given `data` into the collection.\n   *\n   * @param data - The contents of the new documents.\n   * @param options - Options for inserting the documents.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"friends\");\n   * const result = await collection.saveAll(\n   *   [\n   *     { _from: \"users/rana\", _to: \"users/mudasir\", active: false },\n   *     { _from: \"users/rana\", _to: \"users/salman\", active: true }\n   *   ],\n   *   { returnNew: true }\n   * );\n   * ```\n   */\n  saveAll(\n    data: Array<EdgeData<T>>,\n    options?: CollectionInsertOptions\n  ): Promise<\n    Array<\n      | (DocumentOperationMetadata & { new?: Edge<T>; old?: Edge<T> })\n      | DocumentOperationFailure\n    >\n  >;\n  /**\n   * Replaces an existing document in the collection.\n   *\n   * Throws an exception when passed a document or `_id` from a different\n   * collection.\n   *\n   * @param selector - Document `_key`, `_id` or object with either of those\n   * properties (e.g. a document from this collection).\n   * @param newData - The contents of the new document.\n   * @param options - Options for replacing the document.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"friends\");\n   * await collection.save(\n   *   {\n   *     _key: \"musadir\",\n   *     _from: \"users/rana\",\n   *     _to: \"users/mudasir\",\n   *     active: true,\n   *     best: true\n   *   }\n   * );\n   * const result = await collection.replace(\n   *   \"musadir\",\n   *   { active: false },\n   *   { returnNew: true }\n   * );\n   * console.log(result.new.active, result.new.best); // false undefined\n   * ```\n   */\n  replace(\n    selector: DocumentSelector,\n    newData: DocumentData<T>,\n    options?: CollectionReplaceOptions\n  ): Promise<DocumentOperationMetadata & { new?: Edge<T>; old?: Edge<T> }>;\n  /**\n   * Replaces existing documents in the collection, identified by the `_key` or\n   * `_id` of each document.\n   *\n   * @param newData - The documents to replace.\n   * @param options - Options for replacing the documents.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"friends\");\n   * await collection.save(\n   *   {\n   *     _key: \"musadir\",\n   *     _from: \"users/rana\",\n   *     _to: \"users/mudasir\",\n   *     active: true,\n   *     best: true\n   *   }\n   * );\n   * await collection.save(\n   *   {\n   *     _key: \"salman\",\n   *     _from: \"users/rana\",\n   *     _to: \"users/salman\",\n   *     active: false,\n   *     best: false\n   *   }\n   * );\n   * const result = await collection.replaceAll(\n   *   [\n   *     { _key: \"musadir\", active: false },\n   *     { _key: \"salman\", active: true, best: true }\n   *   ],\n   *   { returnNew: true }\n   * );\n   * console.log(result[0].new.active, result[0].new.best); // false undefined\n   * console.log(result[1].new.active, result[1].new.best); // true true\n   * ```\n   */\n  replaceAll(\n    newData: Array<DocumentData<T> & ({ _key: string } | { _id: string })>,\n    options?: CollectionReplaceOptions\n  ): Promise<\n    Array<\n      | (DocumentOperationMetadata & { new?: Edge<T>; old?: Edge<T> })\n      | DocumentOperationFailure\n    >\n  >;\n  /**\n   * Updates an existing document in the collection.\n   *\n   * Throws an exception when passed a document or `_id` from a different\n   * collection.\n   *\n   * @param selector - Document `_key`, `_id` or object with either of those\n   * properties (e.g. a document from this collection).\n   * @param newData - The data for updating the document.\n   * @param options - Options for updating the document.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"friends\");\n   * await collection.save(\n   *   {\n   *     _key: \"musadir\",\n   *     _from: \"users/rana\",\n   *     _to: \"users/mudasir\",\n   *     active: true,\n   *     best: true\n   *   }\n   * );\n   * const result = await collection.update(\n   *   \"musadir\",\n   *   { active: false },\n   *   { returnNew: true }\n   * );\n   * console.log(result.new.active, result.new.best); // false true\n   * ```\n   */\n  update(\n    selector: DocumentSelector,\n    newData: Patch<DocumentData<T>>,\n    options?: CollectionUpdateOptions\n  ): Promise<DocumentOperationMetadata & { new?: Edge<T>; old?: Edge<T> }>;\n  /**\n   * Updates existing documents in the collection, identified by the `_key` or\n   * `_id` of each document.\n   *\n   * @param newData - The data for updating the documents.\n   * @param options - Options for updating the documents.\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"friends\");\n   * await collection.save(\n   *   {\n   *     _key: \"musadir\",\n   *     _from: \"users/rana\",\n   *     _to: \"users/mudasir\",\n   *     active: true,\n   *     best: true\n   *   }\n   * );\n   * await collection.save(\n   *   {\n   *     _key: \"salman\",\n   *     _from: \"users/rana\",\n   *     _to: \"users/salman\",\n   *     active: false,\n   *     best: false\n   *   }\n   * );\n   * const result = await collection.updateAll(\n   *   [\n   *     { _key: \"musadir\", active: false },\n   *     { _key: \"salman\", active: true, best: true }\n   *   ],\n   *   { returnNew: true }\n   * );\n   * console.log(result[0].new.active, result[0].new.best); // false true\n   * console.log(result[1].new.active, result[1].new.best); // true true\n   * ```\n   */\n  updateAll(\n    newData: Array<\n      Patch<DocumentData<T>> & ({ _key: string } | { _id: string })\n    >,\n    options?: CollectionUpdateOptions\n  ): Promise<\n    Array<\n      | (DocumentOperationMetadata & { new?: Edge<T>; old?: Edge<T> })\n      | DocumentOperationFailure\n    >\n  >;\n  /**\n   * Removes an existing document from the collection.\n   *\n   * Throws an exception when passed a document or `_id` from a different\n   * collection.\n   *\n   * @param selector - Document `_key`, `_id` or object with either of those\n   * properties (e.g. a document from this collection).\n   * @param options - Options for removing the document.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"friends\");\n   * const doc = await collection.document(\"musadir\");\n   * await collection.remove(doc);\n   * // document with key \"musadir\" deleted\n   * ```\n   */\n  remove(\n    selector: DocumentSelector,\n    options?: CollectionRemoveOptions\n  ): Promise<DocumentMetadata & { old?: Edge<T> }>;\n  /**\n   * Removes existing documents from the collection.\n   *\n   * Throws an exception when passed any document or `_id` from a different\n   * collection.\n   *\n   * @param selectors - Documents `_key`, `_id` or objects with either of those\n   * properties (e.g. documents from this collection).\n   * @param options - Options for removing the documents.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"friends\");\n   * await collection.removeAll([\"musadir\", \"salman\"]);\n   * // document with keys \"musadir\" and \"salman\" deleted\n   * ```\n   */\n  removeAll(\n    selectors: DocumentSelector[],\n    options?: CollectionRemoveOptions\n  ): Promise<\n    Array<(DocumentMetadata & { old?: Edge<T> }) | DocumentOperationFailure>\n  >;\n  /**\n   * Bulk imports the given `data` into the collection.\n   *\n   * @param data - The data to import, as an array of edge data.\n   * @param options - Options for importing the data.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * await collection.import(\n   *   [\n   *     { _key: \"x\", _from: \"vertices/a\", _to: \"vertices/b\", weight: 1 },\n   *     { _key: \"y\", _from: \"vertices/a\", _to: \"vertices/c\", weight: 2 }\n   *   ]\n   * );\n   * ```\n   */\n  import(\n    data: EdgeData<T>[],\n    options?: CollectionImportOptions\n  ): Promise<CollectionImportResult>;\n  /**\n   * Bulk imports the given `data` into the collection.\n   *\n   * @param data - The data to import, as an array containing a single array of\n   * attribute names followed by one or more arrays of attribute values for\n   * each edge document.\n   * @param options - Options for importing the data.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * await collection.import(\n   *   [\n   *     [ \"_key\", \"_from\", \"_to\", \"weight\" ],\n   *     [ \"x\", \"vertices/a\", \"vertices/b\", 1 ],\n   *     [ \"y\", \"vertices/a\", \"vertices/c\", 2 ]\n   *   ]\n   * );\n   * ```\n   */\n  import(\n    data: any[][],\n    options?: CollectionImportOptions\n  ): Promise<CollectionImportResult>;\n  /**\n   * Bulk imports the given `data` into the collection.\n   *\n   * If `type` is omitted, `data` must contain one JSON array per line with\n   * the first array providing the attribute names and all other arrays\n   * providing attribute values for each edge document.\n   *\n   * If `type` is set to `\"documents\"`, `data` must contain one JSON document\n   * per line.\n   *\n   * If `type` is set to `\"list\"`, `data` must contain a JSON array of\n   * edge documents.\n   *\n   * If `type` is set to `\"auto\"`, `data` can be in either of the formats\n   * supported by `\"documents\"` or `\"list\"`.\n   *\n   * @param data - The data to import as a Buffer (Node), Blob (browser) or\n   * string.\n   * @param options - Options for importing the data.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * await collection.import(\n   *   '{\"_key\":\"x\",\"_from\":\"vertices/a\",\"_to\":\"vertices/b\",\"weight\":1}\\r\\n' +\n   *   '{\"_key\":\"y\",\"_from\":\"vertices/a\",\"_to\":\"vertices/c\",\"weight\":2}\\r\\n',\n   *   { type: \"documents\" } // or \"auto\"\n   * );\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * await collection.import(\n   *   '[{\"_key\":\"x\",\"_from\":\"vertices/a\",\"_to\":\"vertices/b\",\"weight\":1},' +\n   *   '{\"_key\":\"y\",\"_from\":\"vertices/a\",\"_to\":\"vertices/c\",\"weight\":2}]',\n   *   { type: \"list\" } // or \"auto\"\n   * );\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * await collection.import(\n   *   '[\"_key\",\"_from\",\"_to\",\"weight\"]\\r\\n' +\n   *   '[\"x\",\"vertices/a\",\"vertices/b\",1]\\r\\n' +\n   *   '[\"y\",\"vertices/a\",\"vertices/c\",2]\\r\\n'\n   * );\n   * ```\n   */\n  import(\n    data: Buffer | Blob | string,\n    options?: CollectionImportOptions & {\n      type?: \"documents\" | \"list\" | \"auto\";\n    }\n  ): Promise<CollectionImportResult>;\n  //#endregion\n\n  //#region simple queries\n\n  /**\n   * Retrieves all documents in the collection.\n   *\n   * @param options - Options for retrieving the documents.\n   *\n   * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be\n   * replaced with AQL queries.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * // const cursor = await collection.all();\n   * const cursor = await db.query(aql`\n   *   FOR doc IN ${collection}\n   *   RETURN doc\n   * `);\n   * ```\n   */\n  all(options?: SimpleQueryAllOptions): Promise<ArrayCursor<Edge<T>>>;\n\n  /**\n   * Retrieves a random document from the collection.\n   *\n   * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be\n   * replaced with AQL queries.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * // const doc = await collection.any();\n   * const cursor = await db.query(aql`\n   *   FOR doc IN ${collection}\n   *   SORT RAND()\n   *   LIMIT 1\n   *   RETURN doc\n   * `);\n   * const doc = await cursor.next();\n   * ```\n   */\n  any(): Promise<Edge<T>>;\n\n  /**\n   * Retrieves all documents in the collection matching the given example.\n   *\n   * @param example - An object representing an example for documents.\n   * @param options - Options for retrieving the documents.\n   *\n   * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be\n   * replaced with AQL queries.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * // const cursor = await collection.byExample({ flavor: \"strawberry\" });\n   * const cursor = await db.query(aql`\n   *   FOR doc IN ${collection}\n   *   FILTER doc.flavor == \"strawberry\"\n   *   RETURN doc\n   * `);\n   * ```\n   */\n  byExample(\n    example: Partial<DocumentData<T>>,\n    options?: SimpleQueryByExampleOptions\n  ): Promise<ArrayCursor<Edge<T>>>;\n\n  /**\n   * Retrieves a single document in the collection matching the given example.\n   *\n   * @param example - An object representing an example for the document.\n   *\n   * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be\n   * replaced with AQL queries.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * // const doc = await collection.firstExample({ flavor: \"strawberry\" });\n   * const cursor = await db.query(aql`\n   *   FOR doc IN ${collection}\n   *   FILTER doc.flavor == \"strawberry\"\n   *   LIMIT 1\n   *   RETURN doc\n   * `);\n   * const doc = await cursor.next();\n   * ```\n   */\n  firstExample(example: Partial<DocumentData<T>>): Promise<Edge<T>>;\n\n  /**\n   * Retrieves all documents matching the given document keys.\n   *\n   * @param keys - An array of document keys to look up.\n   *\n   * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be\n   * replaced with AQL queries.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * const keys = [\"a\", \"b\", \"c\"];\n   * // const docs = await collection.byKeys(keys);\n   * const cursor = await db.query(aql`\n   *   FOR key IN ${keys}\n   *   LET doc = DOCUMENT(${collection}, key)\n   *   RETURN doc\n   * `);\n   * const docs = await cursor.all();\n   * ```\n   */\n  lookupByKeys(keys: string[]): Promise<Edge<T>[]>;\n\n  /**\n   * Performs a fulltext query in the given `attribute` on the collection.\n   *\n   * @param attribute - Name of the field to search.\n   * @param query - Fulltext query string to search for.\n   * @param options - Options for performing the fulltext query.\n   *\n   * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and can be\n   * replaced with AQL queries.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * // const cursor = await collection.fulltext(\"article\", \"needle\");\n   * const cursor = await db.query(aql`\n   *   FOR doc IN FULLTEXT(${collection}, \"article\", \"needle\")\n   *   RETURN doc\n   * `);\n   * ```\n   */\n  fulltext(\n    attribute: string,\n    query: string,\n    options?: SimpleQueryFulltextOptions\n  ): Promise<ArrayCursor<Edge<T>>>;\n  //#endregion\n\n  //#region edges\n  /**\n   * Retrieves a list of all edges of the document matching the given\n   * `selector`.\n   *\n   * Throws an exception when passed a document or `_id` from a different\n   * collection.\n   *\n   * @param selector - Document `_key`, `_id` or object with either of those\n   * properties (e.g. a document from this collection).\n   * @param options - Options for retrieving the edges.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"edges\");\n   * await collection.import([\n   *   [\"_key\", \"_from\", \"_to\"],\n   *   [\"x\", \"vertices/a\", \"vertices/b\"],\n   *   [\"y\", \"vertices/a\", \"vertices/c\"],\n   *   [\"z\", \"vertices/d\", \"vertices/a\"],\n   * ]);\n   * const edges = await collection.edges(\"vertices/a\");\n   * console.log(edges.map((edge) => edge._key)); // [\"x\", \"y\", \"z\"]\n   * ```\n   */\n  edges(\n    selector: DocumentSelector,\n    options?: CollectionEdgesOptions\n  ): Promise<ArangoApiResponse<CollectionEdgesResult<T>>>;\n  /**\n   * Retrieves a list of all incoming edges of the document matching the given\n   * `selector`.\n   *\n   * Throws an exception when passed a document or `_id` from a different\n   * collection.\n   *\n   * @param selector - Document `_key`, `_id` or object with either of those\n   * properties (e.g. a document from this collection).\n   * @param options - Options for retrieving the edges.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"edges\");\n   * await collection.import([\n   *   [\"_key\", \"_from\", \"_to\"],\n   *   [\"x\", \"vertices/a\", \"vertices/b\"],\n   *   [\"y\", \"vertices/a\", \"vertices/c\"],\n   *   [\"z\", \"vertices/d\", \"vertices/a\"],\n   * ]);\n   * const edges = await collection.inEdges(\"vertices/a\");\n   * console.log(edges.map((edge) => edge._key)); // [\"z\"]\n   * ```\n   */\n  inEdges(\n    selector: DocumentSelector,\n    options?: CollectionEdgesOptions\n  ): Promise<ArangoApiResponse<CollectionEdgesResult<T>>>;\n  /**\n   * Retrieves a list of all outgoing edges of the document matching the given\n   * `selector`.\n   *\n   * Throws an exception when passed a document or `_id` from a different\n   * collection.\n   *\n   * @param selector - Document `_key`, `_id` or object with either of those\n   * properties (e.g. a document from this collection).\n   * @param options - Options for retrieving the edges.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"edges\");\n   * await collection.import([\n   *   [\"_key\", \"_from\", \"_to\"],\n   *   [\"x\", \"vertices/a\", \"vertices/b\"],\n   *   [\"y\", \"vertices/a\", \"vertices/c\"],\n   *   [\"z\", \"vertices/d\", \"vertices/a\"],\n   * ]);\n   * const edges = await collection.outEdges(\"vertices/a\");\n   * console.log(edges.map((edge) => edge._key)); // [\"x\", \"y\"]\n   * ```\n   */\n  outEdges(\n    selector: DocumentSelector,\n    options?: CollectionEdgesOptions\n  ): Promise<ArangoApiResponse<CollectionEdgesResult<T>>>;\n\n  /**\n   * Performs a traversal starting from the given `startVertex` and following\n   * edges contained in this edge collection.\n   *\n   * Throws an exception when passed a document or `_id` from a different\n   * collection.\n   *\n   * See also {@link graph.Graph#traversal}.\n   *\n   * @param startVertex - Document `_key`, `_id` or object with either of those\n   * properties (e.g. a document from this collection).\n   * @param options - Options for performing the traversal.\n   *\n   * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and are\n   * no longer supported in ArangoDB 3.12. They can be replaced with AQL queries.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"edges\");\n   * await collection.import([\n   *   [\"_key\", \"_from\", \"_to\"],\n   *   [\"x\", \"vertices/a\", \"vertices/b\"],\n   *   [\"y\", \"vertices/b\", \"vertices/c\"],\n   *   [\"z\", \"vertices/c\", \"vertices/d\"],\n   * ]);\n   * const startVertex = \"vertices/a\";\n   * const cursor = await db.query(aql`\n   *   FOR vertex IN OUTBOUND ${startVertex}\n   *   RETURN vertex._key\n   * `);\n   * const result = await cursor.all();\n   * console.log(result); // [\"a\", \"b\", \"c\", \"d\"]\n   * ```\n   */\n  traversal(\n    startVertex: DocumentSelector,\n    options?: TraversalOptions\n  ): Promise<any>;\n  //#endregion\n}\n\n/**\n * @internal\n */\nexport class Collection<T extends Record<string, any> = any>\n  implements EdgeCollection<T>, DocumentCollection<T>\n{\n  //#region attributes\n  protected _name: string;\n  protected _db: Database;\n  //#endregion\n\n  /**\n   * @internal\n   */\n  constructor(db: Database, name: string) {\n    this._name = name.normalize(\"NFC\");\n    this._db = db;\n  }\n\n  //#region metadata\n  get isArangoCollection(): true {\n    return true;\n  }\n\n  get name() {\n    return this._name;\n  }\n\n  get() {\n    return this._db.request({\n      path: `/_api/collection/${encodeURIComponent(this._name)}`,\n    });\n  }\n\n  async exists() {\n    try {\n      await this.get();\n      return true;\n    } catch (err: any) {\n      if (isArangoError(err) && err.errorNum === COLLECTION_NOT_FOUND) {\n        return false;\n      }\n      throw err;\n    }\n  }\n\n  create(\n    options: CreateCollectionOptions & {\n      type?: CollectionType;\n    } = {}\n  ) {\n    const {\n      waitForSyncReplication = undefined,\n      enforceReplicationFactor = undefined,\n      ...opts\n    } = options;\n    if (opts.computedValues) {\n      opts.computedValues = opts.computedValues.map((computedValue) => {\n        if (isAqlLiteral(computedValue.expression)) {\n          return {\n            ...computedValue,\n            expression: computedValue.expression.toAQL(),\n          };\n        }\n        if (isAqlQuery(computedValue.expression)) {\n          return {\n            ...computedValue,\n            expression: computedValue.expression.query,\n          };\n        }\n        return computedValue;\n      });\n    }\n    const qs: Params = {};\n    if (typeof waitForSyncReplication === \"boolean\") {\n      qs.waitForSyncReplication = waitForSyncReplication ? 1 : 0;\n    }\n    if (typeof enforceReplicationFactor === \"boolean\") {\n      qs.enforceReplicationFactor = enforceReplicationFactor ? 1 : 0;\n    }\n    return this._db.request({\n      method: \"POST\",\n      path: \"/_api/collection\",\n      qs,\n      body: {\n        ...opts,\n        name: this._name,\n      },\n    });\n  }\n\n  properties(\n    properties?: CollectionPropertiesOptions\n  ): Promise<ArangoApiResponse<CollectionMetadata & CollectionProperties>> {\n    if (!properties) {\n      return this._db.request({\n        path: `/_api/collection/${encodeURIComponent(this._name)}/properties`,\n      });\n    }\n    return this._db.request({\n      method: \"PUT\",\n      path: `/_api/collection/${encodeURIComponent(this._name)}/properties`,\n      body: properties,\n    });\n  }\n\n  count(): Promise<\n    ArangoApiResponse<\n      CollectionMetadata & CollectionProperties & { count: number }\n    >\n  > {\n    return this._db.request({\n      path: `/_api/collection/${encodeURIComponent(this._name)}/count`,\n    });\n  }\n\n  async recalculateCount(): Promise<boolean> {\n    return this._db.request(\n      {\n        method: \"PUT\",\n        path: `/_api/collection/${encodeURIComponent(\n          this._name\n        )}/recalculateCount`,\n      },\n      (res) => res.body.result\n    );\n  }\n\n  figures(\n    details = false\n  ): Promise<\n    CollectionMetadata &\n      ArangoApiResponse<\n        CollectionProperties & { count: number; figures: Record<string, any> }\n      >\n  > {\n    return this._db.request({\n      path: `/_api/collection/${encodeURIComponent(this._name)}/figures`,\n      qs: { details },\n    });\n  }\n\n  revision(): Promise<\n    ArangoApiResponse<\n      CollectionMetadata & CollectionProperties & { revision: string }\n    >\n  > {\n    return this._db.request({\n      path: `/_api/collection/${encodeURIComponent(this._name)}/revision`,\n    });\n  }\n\n  checksum(\n    options?: CollectionChecksumOptions\n  ): Promise<\n    ArangoApiResponse<\n      CollectionMetadata & { revision: string; checksum: string }\n    >\n  > {\n    return this._db.request({\n      path: `/_api/collection/${encodeURIComponent(this._name)}/checksum`,\n      qs: options,\n    });\n  }\n\n  async loadIndexes(): Promise<boolean> {\n    return this._db.request(\n      {\n        method: \"PUT\",\n        path: `/_api/collection/${encodeURIComponent(\n          this._name\n        )}/loadIndexesIntoMemory`,\n      },\n      (res) => res.body.result\n    );\n  }\n\n  async rename(newName: string) {\n    const result = await this._db.renameCollection(this._name, newName);\n    this._name = newName.normalize(\"NFC\");\n    return result;\n  }\n\n  truncate(): Promise<ArangoApiResponse<CollectionMetadata>> {\n    return this._db.request({\n      method: \"PUT\",\n      path: `/_api/collection/${this._name}/truncate`,\n    });\n  }\n\n  drop(options?: CollectionDropOptions) {\n    return this._db.request({\n      method: \"DELETE\",\n      path: `/_api/collection/${encodeURIComponent(this._name)}`,\n      qs: options,\n    });\n  }\n  //#endregion\n\n  //#region crud\n  getResponsibleShard(document: Partial<Document<T>>): Promise<string> {\n    return this._db.request(\n      {\n        method: \"PUT\",\n        path: `/_api/collection/${encodeURIComponent(\n          this._name\n        )}/responsibleShard`,\n        body: document,\n      },\n      (res) => res.body.shardId\n    );\n  }\n\n  documentId(selector: DocumentSelector): string {\n    return _documentHandle(selector, this._name);\n  }\n\n  async documentExists(\n    selector: DocumentSelector,\n    options: DocumentExistsOptions = {}\n  ): Promise<boolean> {\n    const { ifMatch = undefined, ifNoneMatch = undefined } = options;\n    const headers = {} as Record<string, string>;\n    if (ifMatch) headers[\"if-match\"] = ifMatch;\n    if (ifNoneMatch) headers[\"if-none-match\"] = ifNoneMatch;\n    try {\n      return await this._db.request(\n        {\n          method: \"HEAD\",\n          path: `/_api/document/${encodeURI(\n            _documentHandle(selector, this._name)\n          )}`,\n          headers,\n        },\n        (res) => {\n          if (ifNoneMatch && res.statusCode === 304) {\n            throw new HttpError(res);\n          }\n          return true;\n        }\n      );\n    } catch (err: any) {\n      if (err.code === 404) {\n        return false;\n      }\n      throw err;\n    }\n  }\n\n  documents(\n    selectors: (string | ObjectWithKey)[],\n    options: CollectionBatchReadOptions = {}\n  ) {\n    const { allowDirtyRead = undefined } = options;\n    return this._db.request({\n      method: \"PUT\",\n      path: `/_api/document/${encodeURIComponent(this._name)}`,\n      qs: { onlyget: true },\n      allowDirtyRead,\n      body: selectors,\n    });\n  }\n\n  async document(\n    selector: DocumentSelector,\n    options: boolean | CollectionReadOptions = {}\n  ) {\n    if (typeof options === \"boolean\") {\n      options = { graceful: options };\n    }\n    const {\n      allowDirtyRead = undefined,\n      graceful = false,\n      ifMatch = undefined,\n      ifNoneMatch = undefined,\n    } = options;\n    const headers = {} as Record<string, string>;\n    if (ifMatch) headers[\"if-match\"] = ifMatch;\n    if (ifNoneMatch) headers[\"if-none-match\"] = ifNoneMatch;\n    const result = this._db.request(\n      {\n        path: `/_api/document/${encodeURI(\n          _documentHandle(selector, this._name)\n        )}`,\n        headers,\n        allowDirtyRead,\n      },\n      (res) => {\n        if (ifNoneMatch && res.statusCode === 304) {\n          throw new HttpError(res);\n        }\n        return res.body;\n      }\n    );\n    if (!graceful) return result;\n    try {\n      return await result;\n    } catch (err: any) {\n      if (isArangoError(err) && err.errorNum === DOCUMENT_NOT_FOUND) {\n        return null;\n      }\n      throw err;\n    }\n  }\n\n  save(data: DocumentData<T>, options?: CollectionInsertOptions) {\n    return this._db.request(\n      {\n        method: \"POST\",\n        path: `/_api/document/${encodeURIComponent(this._name)}`,\n        body: data,\n        qs: options,\n      },\n      (res) => (options?.silent ? undefined : res.body)\n    );\n  }\n\n  saveAll(data: Array<DocumentData<T>>, options?: CollectionInsertOptions) {\n    return this._db.request(\n      {\n        method: \"POST\",\n        path: `/_api/document/${encodeURIComponent(this._name)}`,\n        body: data,\n        qs: options,\n      },\n      (res) => (options?.silent ? undefined : res.body)\n    );\n  }\n\n  replace(\n    selector: DocumentSelector,\n    newData: DocumentData<T>,\n    options: CollectionReplaceOptions = {}\n  ) {\n    const { ifMatch = undefined, ...opts } = options;\n    const headers = {} as Record<string, string>;\n    if (ifMatch) headers[\"if-match\"] = ifMatch;\n    return this._db.request(\n      {\n        method: \"PUT\",\n        path: `/_api/document/${encodeURI(\n          _documentHandle(selector, this._name)\n        )}`,\n        headers,\n        body: newData,\n        qs: opts,\n      },\n      (res) => (options?.silent ? undefined : res.body)\n    );\n  }\n\n  replaceAll(\n    newData: Array<DocumentData<T> & ({ _key: string } | { _id: string })>,\n    options?: CollectionReplaceOptions\n  ) {\n    return this._db.request(\n      {\n        method: \"PUT\",\n        path: `/_api/document/${encodeURIComponent(this._name)}`,\n        body: newData,\n        qs: options,\n      },\n      (res) => (options?.silent ? undefined : res.body)\n    );\n  }\n\n  update(\n    selector: DocumentSelector,\n    newData: Patch<DocumentData<T>>,\n    options: CollectionUpdateOptions = {}\n  ) {\n    const { ifMatch = undefined, ...opts } = options;\n    const headers = {} as Record<string, string>;\n    if (ifMatch) headers[\"if-match\"] = ifMatch;\n    return this._db.request(\n      {\n        method: \"PATCH\",\n        path: `/_api/document/${encodeURI(\n          _documentHandle(selector, this._name)\n        )}`,\n        headers,\n        body: newData,\n        qs: opts,\n      },\n      (res) => (options?.silent ? undefined : res.body)\n    );\n  }\n\n  updateAll(\n    newData: Array<\n      Patch<DocumentData<T>> & ({ _key: string } | { _id: string })\n    >,\n    options?: CollectionUpdateOptions\n  ) {\n    return this._db.request(\n      {\n        method: \"PATCH\",\n        path: `/_api/document/${encodeURIComponent(this._name)}`,\n        body: newData,\n        qs: options,\n      },\n      (res) => (options?.silent ? undefined : res.body)\n    );\n  }\n\n  remove(selector: DocumentSelector, options: CollectionRemoveOptions = {}) {\n    const { ifMatch = undefined, ...opts } = options;\n    const headers = {} as Record<string, string>;\n    if (ifMatch) headers[\"if-match\"] = ifMatch;\n    return this._db.request(\n      {\n        method: \"DELETE\",\n        path: `/_api/document/${encodeURI(\n          _documentHandle(selector, this._name)\n        )}`,\n        headers,\n        qs: opts,\n      },\n      (res) => (options?.silent ? undefined : res.body)\n    );\n  }\n\n  removeAll(\n    selectors: (string | ObjectWithKey)[],\n    options?: CollectionRemoveOptions\n  ) {\n    return this._db.request(\n      {\n        method: \"DELETE\",\n        path: `/_api/document/${encodeURIComponent(this._name)}`,\n        body: selectors,\n        qs: options,\n      },\n      (res) => (options?.silent ? undefined : res.body)\n    );\n  }\n\n  import(\n    data: Buffer | Blob | string | any[],\n    options: CollectionImportOptions & {\n      type?: \"documents\" | \"list\" | \"auto\";\n    } = {}\n  ): Promise<CollectionImportResult> {\n    const qs = { ...options, collection: this._name };\n    if (Array.isArray(data)) {\n      qs.type = Array.isArray(data[0]) ? undefined : \"documents\";\n      const lines = data as any[];\n      data = lines.map((line) => JSON.stringify(line)).join(\"\\r\\n\") + \"\\r\\n\";\n    }\n    return this._db.request({\n      method: \"POST\",\n      path: \"/_api/import\",\n      body: data,\n      isBinary: true,\n      qs,\n    });\n  }\n  //#endregion\n\n  //#region edges\n  protected _edges(\n    selector: DocumentSelector,\n    options: CollectionEdgesOptions = {},\n    direction?: \"in\" | \"out\"\n  ) {\n    const { allowDirtyRead = undefined } = options;\n    return this._db.request({\n      path: `/_api/edges/${encodeURIComponent(this._name)}`,\n      allowDirtyRead,\n      qs: {\n        direction,\n        vertex: _documentHandle(selector, this._name, false),\n      },\n    });\n  }\n\n  edges(vertex: DocumentSelector, options?: CollectionEdgesOptions) {\n    return this._edges(vertex, options);\n  }\n\n  inEdges(vertex: DocumentSelector, options?: CollectionEdgesOptions) {\n    return this._edges(vertex, options, \"in\");\n  }\n\n  outEdges(vertex: DocumentSelector, options?: CollectionEdgesOptions) {\n    return this._edges(vertex, options, \"out\");\n  }\n\n  traversal(startVertex: DocumentSelector, options?: TraversalOptions) {\n    return this._db.request(\n      {\n        method: \"POST\",\n        path: \"/_api/traversal\",\n        body: {\n          ...options,\n          startVertex,\n          edgeCollection: this._name,\n        },\n      },\n      (res) => res.body.result\n    );\n  }\n  //#endregion\n\n  //#region simple queries\n  list(type: SimpleQueryListType = \"id\") {\n    return this._db.request(\n      {\n        method: \"PUT\",\n        path: \"/_api/simple/all-keys\",\n        body: { type, collection: this._name },\n      },\n      (res) =>\n        new BatchedArrayCursor(this._db, res.body, res.arangojsHostUrl).items\n    );\n  }\n\n  all(options?: SimpleQueryAllOptions) {\n    return this._db.request(\n      {\n        method: \"PUT\",\n        path: \"/_api/simple/all\",\n        body: {\n          ...options,\n          collection: this._name,\n        },\n      },\n      (res) =>\n        new BatchedArrayCursor(this._db, res.body, res.arangojsHostUrl).items\n    );\n  }\n\n  any() {\n    return this._db.request(\n      {\n        method: \"PUT\",\n        path: \"/_api/simple/any\",\n        body: { collection: this._name },\n      },\n      (res) => res.body.document\n    );\n  }\n\n  byExample(\n    example: Partial<DocumentData<T>>,\n    options?: SimpleQueryByExampleOptions\n  ) {\n    return this._db.request(\n      {\n        method: \"PUT\",\n        path: \"/_api/simple/by-example\",\n        body: {\n          ...options,\n          example,\n          collection: this._name,\n        },\n      },\n      (res) =>\n        new BatchedArrayCursor(this._db, res.body, res.arangojsHostUrl).items\n    );\n  }\n\n  firstExample(example: Partial<DocumentData<T>>) {\n    return this._db.request(\n      {\n        method: \"PUT\",\n        path: \"/_api/simple/first-example\",\n        body: {\n          example,\n          collection: this._name,\n        },\n      },\n      (res) => res.body.document\n    );\n  }\n\n  removeByExample(\n    example: Partial<DocumentData<T>>,\n    options?: SimpleQueryRemoveByExampleOptions\n  ) {\n    return this._db.request({\n      method: \"PUT\",\n      path: \"/_api/simple/remove-by-example\",\n      body: {\n        ...options,\n        example,\n        collection: this._name,\n      },\n    });\n  }\n\n  replaceByExample(\n    example: Partial<DocumentData<T>>,\n    newValue: DocumentData<T>,\n    options?: SimpleQueryReplaceByExampleOptions\n  ) {\n    return this._db.request({\n      method: \"PUT\",\n      path: \"/_api/simple/replace-by-example\",\n      body: {\n        ...options,\n        example,\n        newValue,\n        collection: this._name,\n      },\n    });\n  }\n\n  updateByExample(\n    example: Partial<DocumentData<T>>,\n    newValue: Patch<DocumentData<T>>,\n    options?: SimpleQueryUpdateByExampleOptions\n  ) {\n    return this._db.request({\n      method: \"PUT\",\n      path: \"/_api/simple/update-by-example\",\n      body: {\n        ...options,\n        example,\n        newValue,\n        collection: this._name,\n      },\n    });\n  }\n\n  lookupByKeys(keys: string[]) {\n    return this._db.request(\n      {\n        method: \"PUT\",\n        path: \"/_api/simple/lookup-by-keys\",\n        body: {\n          keys,\n          collection: this._name,\n        },\n      },\n      (res) => res.body.documents\n    );\n  }\n\n  removeByKeys(keys: string[], options?: SimpleQueryRemoveByKeysOptions) {\n    return this._db.request({\n      method: \"PUT\",\n      path: \"/_api/simple/remove-by-keys\",\n      body: {\n        options: options,\n        keys,\n        collection: this._name,\n      },\n    });\n  }\n  //#endregion\n\n  //#region indexes\n  indexes() {\n    return this._db.request(\n      {\n        path: \"/_api/index\",\n        qs: { collection: this._name },\n      },\n      (res) => res.body.indexes\n    );\n  }\n\n  index(selector: IndexSelector) {\n    return this._db.request({\n      path: `/_api/index/${encodeURI(_indexHandle(selector, this._name))}`,\n    });\n  }\n\n  ensureIndex(\n    options:\n      | EnsurePersistentIndexOptions\n      | EnsureGeoIndexOptions\n      | EnsureFulltextIndexOptions\n      | EnsureTtlIndexOptions\n      | EnsureMdiIndexOptions\n      | EnsureInvertedIndexOptions\n  ) {\n    const opts = { ...options };\n    if (opts.name) {\n      opts.name = opts.name.normalize(\"NFC\");\n    }\n    return this._db.request({\n      method: \"POST\",\n      path: \"/_api/index\",\n      body: options,\n      qs: { collection: this._name },\n    });\n  }\n\n  dropIndex(selector: IndexSelector) {\n    return this._db.request({\n      method: \"DELETE\",\n      path: `/_api/index/${encodeURI(_indexHandle(selector, this._name))}`,\n    });\n  }\n\n  fulltext(\n    attribute: string,\n    query: string,\n    { index, ...options }: SimpleQueryFulltextOptions = {}\n  ) {\n    return this._db.request(\n      {\n        method: \"PUT\",\n        path: \"/_api/simple/fulltext\",\n        body: {\n          ...options,\n          index: index ? _indexHandle(index, this._name) : undefined,\n          attribute,\n          query,\n          collection: this._name,\n        },\n      },\n      (res) =>\n        new BatchedArrayCursor(this._db, res.body, res.arangojsHostUrl).items\n    );\n  }\n\n  compact() {\n    return this._db.request(\n      {\n        method: \"PUT\",\n        path: `/_api/collection/${this._name}/compact`,\n      },\n      (res) => res.body\n    );\n  }\n  //#endregion\n}\n"]}