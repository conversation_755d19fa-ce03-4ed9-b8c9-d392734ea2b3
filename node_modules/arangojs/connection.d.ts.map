{"version": 3, "file": "connection.d.ts", "sourceRoot": "", "sources": ["../src/connection.ts"], "names": [], "mappings": ";;AAAA;;;;;;;;;GASG;AACH,OAAO,EAAE,aAAa,EAAE,MAAM,MAAM,CAAC;AACrC,OAAO,EAAE,YAAY,IAAI,gBAAgB,EAAE,MAAM,OAAO,CAAC;AACzD,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC3C,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAgBtC,OAAO,EACL,aAAa,EACb,gBAAgB,EAGhB,eAAe,EAChB,MAAM,eAAe,CAAC;AAKvB;;;;;;;;;;;GAWG;AACH,MAAM,MAAM,qBAAqB,GAAG,MAAM,GAAG,aAAa,GAAG,YAAY,CAAC;AAE1E;;;;;GAKG;AACH,MAAM,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAE7C;;;GAGG;AACH,MAAM,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;AAEzC;;GAEG;AACH,MAAM,MAAM,sBAAsB,GAAG;IACnC;;OAEG;IACH,KAAK,EAAE,KAAK,CAAC;IACb;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;CACd,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,iBAAiB,CAAC,CAAC,IAAI,CAAC,GAAG,sBAAsB,CAAC;AAE9D;;GAEG;AACH,MAAM,MAAM,oBAAoB,GAAG;IACjC;;OAEG;IACH,QAAQ,EAAE,MAAM,CAAC;IACjB;;OAEG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;CACnB,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,qBAAqB,GAAG;IAClC;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;CACf,CAAC;AAqBF;;GAEG;AACH,KAAK,OAAO,GAAG;IACb,YAAY,CAAC,EAAE,OAAO,CAAC;IACvB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,EAAE,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC;CACtB,CAAC;AAEF;;;;;;GAMG;AACH,MAAM,MAAM,UAAU,GAAG;IACvB;;;;OAIG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB;;;;OAIG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB;;;;;OAKG;IACH,UAAU,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,KAAK,IAAI,CAAC;IACtC;;OAEG;IACH,GAAG,CAAC,EAAE,GAAG,CAAC;IACV;;;;OAIG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB;;;;;OAKG;IACH,eAAe,CAAC,EAAE,OAAO,CAAC;CAC3B,CAAC;AAEF;;;GAGG;AACH,MAAM,MAAM,mBAAmB,GAAG;IAChC;;;;;OAKG;IACH,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,aAAa,KAAK,IAAI,CAAC;IACtC;;;;;;;;;OASG;IACH,KAAK,CAAC,EAAE,CAAC,GAAG,EAAE,aAAa,GAAG,IAAI,EAAE,GAAG,CAAC,EAAE,gBAAgB,KAAK,IAAI,CAAC;CACrE,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,cAAc,GAAG;IAC3B;;;;OAIG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB;;;;OAIG;IACH,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB;;OAEG;IACH,IAAI,CAAC,EAAE,GAAG,CAAC;IACX;;;OAGG;IACH,YAAY,CAAC,EAAE,OAAO,CAAC;IACvB;;;OAGG;IACH,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB;;;;OAIG;IACH,cAAc,CAAC,EAAE,OAAO,CAAC;IACzB;;;;;OAKG;IACH,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB;;;OAGG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB;;;;;OAKG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB;;OAEG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IACd;;OAEG;IACH,EAAE,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC;CACtB,CAAC;AAEF;;GAEG;AACH,KAAK,IAAI,GAAG;IACV,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,KAAK,CAAC,EAAE,MAAM,MAAM,CAAC;IACrB,cAAc,EAAE,OAAO,CAAC;IACxB,eAAe,EAAE,MAAM,CAAC;IACxB,OAAO,EAAE,CAAC,MAAM,EAAE,GAAG,KAAK,IAAI,CAAC;IAC/B,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,CAAC;IAC/B,SAAS,CAAC,EAAE,CAAC,GAAG,EAAE,gBAAgB,KAAK,GAAG,CAAC;IAC3C,OAAO,EAAE,MAAM,CAAC;IAChB,OAAO,EAAE;QACP,MAAM,EAAE,MAAM,CAAC;QACf,YAAY,EAAE,OAAO,CAAC;QACtB,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,GAAG,EAAE;YAAE,QAAQ,EAAE,MAAM,CAAC;YAAC,MAAM,CAAC,EAAE,MAAM,CAAA;SAAE,CAAC;QAC3C,OAAO,EAAE,OAAO,CAAC;QACjB,IAAI,EAAE,GAAG,CAAC;KACX,CAAC;CACH,CAAC;AAEF;;;;;;;;;;GAUG;AACH,MAAM,MAAM,YAAY,GAAG,gBAAgB,GAAG,UAAU,CAAC;AAEzD;;GAEG;AACH,MAAM,MAAM,MAAM,GAAG;IACnB;;;;OAIG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA4BG;IACH,GAAG,CAAC,EAAE,MAAM,GAAG,MAAM,EAAE,CAAC;IACxB;;;;;;;OAOG;IACH,IAAI,CAAC,EAAE,oBAAoB,GAAG,qBAAqB,CAAC;IACpD;;;;;;;;;;OAUG;IACH,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB;;;;;;;;;;;;;OAaG;IACH,qBAAqB,CAAC,EAAE,qBAAqB,CAAC;IAC9C;;;;;;;;;;;;;;;;;;;;;;;;;;;OA2BG;IACH,UAAU,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC;IAC5B;;;;;OAKG;IACH,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB;;;;;;;;;;OAUG;IACH,KAAK,CAAC,EAAE,GAAG,CAAC;IACZ;;;;;;;;;;;;;;;;;;;OAmBG;IACH,YAAY,CAAC,EAAE,YAAY,GAAG,mBAAmB,CAAC;IAClD;;;;;;OAMG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB;;;;;;;OAOG;IACH,qBAAqB,CAAC,EAAE,OAAO,CAAC;IAChC;;;;;;;OAOG;IACH,wBAAwB,CAAC,EAAE,MAAM,CAAC;CACnC,CAAC;AAEF;;;;;;GAMG;AACH,wBAAgB,kBAAkB,CAAC,UAAU,EAAE,GAAG,GAAG,UAAU,IAAI,UAAU,CAE5E;AAED;;;;GAIG;AACH,qBAAa,UAAU;IACrB,SAAS,CAAC,YAAY,EAAE,MAAM,CAAK;IACnC,SAAS,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC;IACvB,SAAS,CAAC,aAAa,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAE,CAAC;IAChD,SAAS,CAAC,cAAc,EAAE,MAAM,CAAS;IACzC,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC;IAC5B,SAAS,CAAC,sBAAsB,EAAE,qBAAqB,CAAC;IACxD,SAAS,CAAC,WAAW,EAAE,MAAM,GAAG,KAAK,CAAC;IACtC,SAAS,CAAC,gBAAgB,EAAE,MAAM,CAAC;IACnC,SAAS,CAAC,SAAS,EAAE,MAAM,CAAC;IAC5B,SAAS,CAAC,MAAM,mBAA0B;IAC1C,SAAS,CAAC,UAAU,wBAA+B;IACnD,SAAS,CAAC,MAAM,EAAE,eAAe,EAAE,CAAM;IACzC,SAAS,CAAC,SAAS,EAAE,MAAM,EAAE,CAAM;IACnC,SAAS,CAAC,cAAc,EAAE,MAAM,CAAC;IACjC,SAAS,CAAC,mBAAmB,EAAE,MAAM,CAAC;IACtC,SAAS,CAAC,cAAc,EAAE,MAAM,GAAG,IAAI,CAAQ;IAC/C,SAAS,CAAC,sBAAsB,EAAE,OAAO,CAAC;IAC1C,SAAS,CAAC,WAAW,+BAAsC;IAC3D,SAAS,CAAC,yBAAyB,EAAE,MAAM,CAAC;IAE5C;;;;;;;OAOG;gBACS,MAAM,GAAE,IAAI,CAAC,MAAM,EAAE,cAAc,CAAM;IA0DrD;;;;OAIG;IACH,IAAI,kBAAkB,IAAI,IAAI,CAE7B;IAED,IAAI,SAAS;;;;MAYZ;IAED,SAAS,CAAC,SAAS;IA8HnB,SAAS,CAAC,SAAS,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,OAAO;;;;;;;IAUnD,aAAa,CAAC,IAAI,EAAE,qBAAqB;IAIzC,YAAY,CAAC,IAAI,EAAE,oBAAoB;IAOvC,2BAA2B,CAAC,wBAAwB,EAAE,MAAM;IAU5D;;;;;;;OAOG;IACH,QAAQ,CAAC,YAAY,EAAE,MAAM,GAAG,QAAQ,GAAG,SAAS;IACpD;;;;;;;;OAQG;IACH,QAAQ,CAAC,YAAY,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,GAAG,QAAQ;IAC5D;;;;;;;;OAQG;IACH,QAAQ,CAAC,YAAY,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,GAAG,SAAS;IAgBzD;;;;;;;;OAQG;IACH,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,IAAI;IAcjC;;;;;;;;OAQG;IACH,aAAa,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE;IAgBhD;;;;;;;;;;;;OAYG;IACH,gBAAgB,CAAC,aAAa,EAAE,MAAM;IAItC;;;;OAIG;IACH,kBAAkB;IAIlB;;;;;;;;OAQG;IACH,SAAS,CAAC,UAAU,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,IAAI;IAQlD;;;;;;OAMG;IACH,KAAK;IAML;;;;;;;;;OASG;IACG,kBAAkB,CAAC,OAAO,EAAE,cAAc,EAAE,OAAO,SAAW;IA4BpE;;;;OAIG;IACH,OAAO,CAAC,CAAC,GAAG,gBAAgB,EAC1B,EACE,OAAO,EACP,MAAc,EACd,IAAI,EACJ,YAAoB,EACpB,QAAgB,EAChB,cAAsB,EACtB,eAAuC,EACvC,OAAW,EACX,OAAO,EACP,GAAG,OAAO,EACX,EAAE,cAAc,EACjB,SAAS,CAAC,EAAE,CAAC,GAAG,EAAE,gBAAgB,KAAK,CAAC,GACvC,OAAO,CAAC,CAAC,CAAC;CA8Dd"}