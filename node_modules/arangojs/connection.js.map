{"version": 3, "file": "connection.js", "sourceRoot": "", "sources": ["../src/connection.ts"], "names": [], "mappings": ";;;AAYA,iDAA2C;AAE3C,mCAMiB;AACjB,qCAA0C;AAC1C,uCAGqB;AAErB,qDAAkD;AAClD,yDAAsD;AACtD,2CAMuB;AAEvB,MAAM,SAAS,GAAG,2BAA2B,CAAC;AAC9C,MAAM,sBAAsB,GAAG,mBAAmB,CAAC;AAyEnD,SAAS,YAAY,CAAC,IAAS;IAC7B,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;AACtC,CAAC;AAED;;GAEG;AACH,SAAS,kBAAkB;IACzB,IAAI,GAAG,GAAG,IAAI,KAAK,EAAE,CAAC;IACtB,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;QACf,IAAI,CAAC;YACH,MAAM,GAAG,CAAC;QACZ,CAAC;QAAC,OAAO,CAAM,EAAE,CAAC;YAChB,GAAG,GAAG,CAAC,CAAC;QACV,CAAC;IACH,CAAC;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAwWD;;;;;;GAMG;AACH,SAAgB,kBAAkB,CAAC,UAAe;IAChD,OAAO,OAAO,CAAC,UAAU,IAAI,UAAU,CAAC,kBAAkB,CAAC,CAAC;AAC9D,CAAC;AAFD,gDAEC;AAED;;;;GAIG;AACH,MAAa,UAAU;IAqBrB;;;;;;;OAOG;IACH,YAAY,SAAuC,EAAE;QA5B3C,iBAAY,GAAW,CAAC,CAAC;QAGzB,mBAAc,GAAW,KAAK,CAAC;QAM/B,WAAM,GAAG,IAAI,0BAAU,EAAQ,CAAC;QAChC,eAAU,GAAG,IAAI,GAAG,EAAoB,CAAC;QACzC,WAAM,GAAsB,EAAE,CAAC;QAC/B,cAAS,GAAa,EAAE,CAAC;QAGzB,mBAAc,GAAkB,IAAI,CAAC;QAErC,gBAAW,GAAG,IAAI,0BAAU,EAAoB,CAAC;QAYzD,MAAM,IAAI,GAAG,MAAM,CAAC,GAAG;YACrB,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC;gBACzB,CAAC,CAAC,MAAM,CAAC,GAAG;gBACZ,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC;YAChB,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC;QAC9B,MAAM,WAAW,GACf,CAAC,GAAG,CAAC,MAAM,CAAC,qBAAqB,KAAK,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAEzE,IAAI,MAAM,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YACvC,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,aAAa,CAAC;QAC7C,CAAC;QACD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC;QAC3B,IAAI,CAAC,aAAa,GAAG,mBAAS;YAC5B,CAAC,CAAC,EAAE,UAAU,EAAE,WAAW,EAAE,GAAG,MAAM,CAAC,YAAY,EAAE;YACrD,CAAC,CAAC;gBACE,UAAU,EAAE,WAAW;gBACvB,SAAS,EAAE,IAAI;gBACf,cAAc,EAAE,IAAI;gBACpB,UAAU,EAAE,MAAM;gBAClB,GAAG,MAAM,CAAC,YAAY;aACvB,CAAC;QACN,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC;QAC/C,IAAI,CAAC,QAAQ,GAAG,EAAE,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC;QACtC,IAAI,CAAC,sBAAsB,GAAG,MAAM,CAAC,qBAAqB,IAAI,MAAM,CAAC;QACrE,IAAI,CAAC,sBAAsB,GAAG,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;QACpE,IAAI,CAAC,yBAAyB,GAAG,MAAM,CAAC,wBAAwB,IAAI,EAAE,CAAC;QACvE,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC,eAAe,IAAI,CAAC,CAAC;QACpD,IAAI,IAAI,CAAC,yBAAyB,GAAG,CAAC,EAAE,CAAC;YACvC,IAAI,CAAC,yBAAyB,GAAG,QAAQ,CAAC;QAC5C,CAAC;QACD,IAAI,MAAM,CAAC,UAAU,KAAK,KAAK,EAAE,CAAC;YAChC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QAC3B,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,IAAI,CAAC,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAEzB,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;YAChB,IAAI,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC9B,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAClC,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACjC,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,sBAAsB,KAAK,YAAY,EAAE,CAAC;YACjD,IAAI,CAAC,cAAc;gBACjB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;YACpE,IAAI,CAAC,mBAAmB;gBACtB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;QACtE,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YACxC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,SAAS;QACX,OAAO;YACL,SAAS,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;YAChD,SAAS,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YACtD,MAAM,EAAE,GAAG,EAAE;gBACX,IAAI,GAAG,GAAG,CAAC,CAAC;gBACZ,KAAK,MAAM,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;oBAC7C,GAAG,IAAI,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACzC,CAAC;gBACD,OAAO,GAAG,CAAC;YACb,CAAC;SACF,CAAC;IACJ,CAAC;IAES,SAAS;QACjB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,SAAS;YAAE,OAAO;QACvE,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,EAAG,CAAC;QAClC,IAAI,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC;QAClC,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;YAC/B,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QACzB,CAAC;aAAM,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YAC/B,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC;YACnC,IAAI,CAAC,mBAAmB;gBACtB,IAAI,CAAC,SAAS,CACZ,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;oBACpD,IAAI,CAAC,SAAS,CAAC,MAAM,CACxB,CAAC;YACJ,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,2BAA2B,CAAC,GAAG,MAAM,CAAC;QAC7D,CAAC;aAAM,IAAI,IAAI,CAAC,sBAAsB,KAAK,aAAa,EAAE,CAAC;YACzD,IAAI,CAAC,cAAc;gBACjB,IAAI,CAAC,SAAS,CACZ,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;oBAC/C,IAAI,CAAC,SAAS,CAAC,MAAM,CACxB,CAAC;QACN,CAAC;QACD,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC;QACvB,MAAM,QAAQ,GAA8B,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YACvD,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC;YACvB,IAAI,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC;gBAChB,IAAI,GAAG,CAAC,UAAU,KAAK,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,sBAAsB,CAAC,EAAE,CAAC;oBAClE,MAAM,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,sBAAsB,CAAE,CAAC;oBACjD,MAAM,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;oBAC3C,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC;oBACxB,IAAI,IAAI,CAAC,cAAc,KAAK,OAAO,EAAE,CAAC;wBACpC,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC;oBACjC,CAAC;oBACD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACzB,CAAC;qBAAM,CAAC;oBACN,GAAG,CAAC,eAAe,GAAG,OAAO,CAAC;oBAC9B,MAAM,WAAW,GAAG,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;oBAChD,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,6BAA6B,CAAC,CAAC;oBAC7D,IAAI,SAAS,EAAE,CAAC;wBACd,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;wBACvD,OAAO,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;4BAChE,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;wBAC3B,CAAC;oBACH,CAAC;oBACD,IAAI,UAAU,GAAQ,SAAS,CAAC;oBAChC,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,IAAI,WAAW,IAAI,WAAW,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC;wBACnE,IAAI,CAAC;4BACH,UAAU,GAAG,GAAG,CAAC,IAAI,CAAC;4BACtB,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;wBACtC,CAAC;wBAAC,OAAO,CAAM,EAAE,CAAC;4BAChB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;gCAC/B,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE,CAAC;oCACnC,UAAU,GAAG,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;gCAC1C,CAAC;gCACD,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC;gCACZ,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;oCACf,CAAC,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;gCAC1B,CAAC;gCACD,QAAQ,CAAC,CAAC,CAAC,CAAC;gCACZ,OAAO;4BACT,CAAC;wBACH,CAAC;oBACH,CAAC;yBAAM,IAAI,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;wBAClD,UAAU,GAAG,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;oBAC1C,CAAC;yBAAM,CAAC;wBACN,UAAU,GAAG,GAAG,CAAC,IAAI,CAAC;oBACxB,CAAC;oBACD,IAAI,IAAA,6BAAqB,EAAC,UAAU,CAAC,EAAE,CAAC;wBACtC,GAAG,CAAC,IAAI,GAAG,UAAU,CAAC;wBACtB,GAAG,GAAG,IAAI,mBAAW,CAAC,GAAG,CAAC,CAAC;oBAC7B,CAAC;yBAAM,IAAI,GAAG,CAAC,UAAU,IAAI,GAAG,CAAC,UAAU,IAAI,GAAG,EAAE,CAAC;wBACnD,GAAG,CAAC,IAAI,GAAG,UAAU,CAAC;wBACtB,GAAG,GAAG,IAAI,iBAAS,CAAC,GAAG,CAAC,CAAC;oBAC3B,CAAC;yBAAM,CAAC;wBACN,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY;4BAAE,GAAG,CAAC,IAAI,GAAG,UAAU,CAAC;wBACtD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE,GAAW,CAAC,CAAC;oBACpE,CAAC;gBACH,CAAC;YACH,CAAC;YACD,IAAI,GAAG,EAAE,CAAC;gBACR,IACE,CAAC,IAAI,CAAC,cAAc;oBACpB,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC;oBACtB,IAAI,CAAC,cAAc,KAAK,OAAO;oBAC/B,IAAI,CAAC,sBAAsB,KAAK,aAAa,EAC7C,CAAC;oBACD,IAAI,CAAC,cAAc;wBACjB,IAAI,CAAC,SAAS,CACZ,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;4BAC/C,IAAI,CAAC,SAAS,CAAC,MAAM,CACxB,CAAC;gBACN,CAAC;gBACD,IACE,IAAA,qBAAa,EAAC,GAAG,CAAC;oBAClB,GAAG,CAAC,QAAQ,KAAK,6BAAqB;oBACtC,IAAI,CAAC,eAAe,GAAG,CAAC,EACxB,CAAC;oBACD,IAAI,CAAC,eAAe,IAAI,CAAC,CAAC;oBAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACzB,CAAC;qBAAM,IACL,CAAC,CAAC,IAAA,qBAAa,EAAC,GAAG,CAAC;oBAClB,GAAG,CAAC,OAAO,KAAK,SAAS;oBACzB,GAAG,CAAC,IAAI,KAAK,cAAc,CAAC;oBAC5B,CAAC,IAAA,qBAAa,EAAC,GAAG,CAAC;wBACjB,GAAG,CAAC,QAAQ,KAAK,qCAA6B,CAAC,CAAC;oBACpD,IAAI,CAAC,OAAO,KAAK,SAAS;oBAC1B,IAAI,CAAC,WAAW,KAAK,KAAK;oBAC1B,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,EAC3D,CAAC;oBACD,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC;oBAClB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACzB,CAAC;qBAAM,CAAC;oBACN,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;wBACf,GAAG,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;oBAC5B,CAAC;oBACD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBACnB,CAAC;YACH,CAAC;YACD,IAAI,CAAC,SAAS,EAAE,CAAC;QACnB,CAAC,CAAC;QACF,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QACvE,CAAC;QAAC,OAAO,CAAM,EAAE,CAAC;YAChB,QAAQ,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;IACH,CAAC;IAES,SAAS,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,EAAW;QACjD,MAAM,QAAQ,GAAG,GAAG,QAAQ,IAAI,EAAE,GAAG,IAAI,IAAI,EAAE,EAAE,CAAC;QAClD,IAAI,MAAM,CAAC;QACX,IAAI,EAAE,EAAE,CAAC;YACP,IAAI,OAAO,EAAE,KAAK,QAAQ;gBAAE,MAAM,GAAG,IAAI,EAAE,EAAE,CAAC;;gBACzC,MAAM,GAAG,IAAI,IAAA,+BAAc,EAAC,EAAE,CAAC,EAAE,CAAC;QACzC,CAAC;QACD,OAAO,MAAM,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC;IACtD,CAAC;IAED,aAAa,CAAC,IAA2B;QACvC,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,UAAU,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;IAC1D,CAAC;IAED,YAAY,CAAC,IAA0B;QACrC,IAAI,CAAC,SAAS,CACZ,eAAe,EACf,SAAS,IAAA,mBAAY,EAAC,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,CAC7D,CAAC;IACJ,CAAC;IAED,2BAA2B,CAAC,wBAAgC;QAC1D,IAAI,wBAAwB,GAAG,CAAC,EAAE,CAAC;YACjC,wBAAwB,GAAG,QAAQ,CAAC;QACtC,CAAC;QACD,IAAI,CAAC,yBAAyB,GAAG,wBAAwB,CAAC;QAC1D,OAAO,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YAChE,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;QAC3B,CAAC;IACH,CAAC;IA+BD,QAAQ,CACN,YAAoB,EACpB,QAA0B;QAE1B,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;YACtB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YACrC,OAAO,SAAS,CAAC;QACnB,CAAC;QACD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAC3C,CAAC;QACD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;QAC5C,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;;;;;OAQG;IACH,WAAW,CAAC,IAAc;QACxB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,IAAA,2BAAY,EAAC,GAAG,CAAC,CAAC,CAAC;QACvD,IAAI,CAAC,MAAM,CAAC,MAAM,CAChB,CAAC,EACD,IAAI,CAAC,MAAM,CAAC,MAAM,EAClB,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;YACvB,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YACtC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAAE,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACpC,OAAO,IAAA,uBAAa,EAAC,GAAG,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAC7D,CAAC,CAAC,CACH,CAAC;QACF,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,GAAG,SAAS,CAAC,CAAC;IAChE,CAAC;IAED;;;;;;;;OAQG;IACH,aAAa,CAAC,IAAuB;QACnC,MAAM,SAAS,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAClE,IAAA,2BAAY,EAAC,GAAG,CAAC,CAClB,CAAC;QACF,MAAM,OAAO,GAAG,SAAS,CAAC,MAAM,CAC9B,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAC5C,CAAC;QACF,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;QAChC,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,GAAW,EAAE,EAAE,CAC7B,IAAA,uBAAa,EAAC,GAAG,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,CACpD,CACF,CAAC;QACF,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,gBAAgB,CAAC,aAAqB;QACpC,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;IACtC,CAAC;IAED;;;;OAIG;IACH,kBAAkB;QAChB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC7B,CAAC;IAED;;;;;;;;OAQG;IACH,SAAS,CAAC,UAAkB,EAAE,KAAoB;QAChD,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QACnC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,KAAK,CAAC;QACpC,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACH,KAAK;QACH,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAC/B,IAAI,IAAI,CAAC,KAAK;gBAAE,IAAI,CAAC,KAAK,EAAE,CAAC;QAC/B,CAAC;IACH,CAAC;IAED;;;;;;;;;OASG;IACH,KAAK,CAAC,kBAAkB,CAAC,OAAuB,EAAE,OAAO,GAAG,QAAQ;QAClE,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QACpC,MAAM,UAAU,GAAG,EAAc,CAAC;QAClC,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC3B,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,OAAO,IAAI,EAAE,CAAC;YACZ,IAAI,UAAU,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;gBACnC,OAAO;YACT,CAAC;YACD,OAAO,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;gBAClD,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC;YACjC,CAAC;YACD,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACtC,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;YAC9C,CAAC;YAAC,OAAO,CAAM,EAAE,CAAC;gBAChB,IAAI,OAAO,GAAG,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;oBACnC,MAAM,CAAC,CAAC;gBACV,CAAC;gBACD,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;gBAC1D,SAAS;YACX,CAAC;YACD,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBAClC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC3B,CAAC;QACH,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,OAAO,CACL,EACE,OAAO,EACP,MAAM,GAAG,KAAK,EACd,IAAI,EACJ,YAAY,GAAG,KAAK,EACpB,QAAQ,GAAG,KAAK,EAChB,cAAc,GAAG,KAAK,EACtB,eAAe,GAAG,IAAI,CAAC,gBAAgB,EACvC,OAAO,GAAG,CAAC,EACX,OAAO,EACP,GAAG,OAAO,EACK,EACjB,SAAwC;QAExC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,WAAW,GAAG,YAAY,CAAC;YAC/B,IAAI,QAAQ,EAAE,CAAC;gBACb,WAAW,GAAG,0BAA0B,CAAC;YAC3C,CAAC;iBAAM,IAAI,IAAI,EAAE,CAAC;gBAChB,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;oBAC7B,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;oBAC5B,WAAW,GAAG,kBAAkB,CAAC;gBACnC,CAAC;qBAAM,CAAC;oBACN,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;gBACtB,CAAC;YACH,CAAC;YAED,MAAM,YAAY,GAAY;gBAC5B,GAAG,IAAI,CAAC,QAAQ;gBAChB,cAAc,EAAE,WAAW;gBAC3B,kBAAkB,EAAE,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC;gBAC/C,iBAAiB,EAAE,YAAY,OAAO,CAAC,GAAG,CAAC,gBAAgB,UAAU;aACtE,CAAC;YAEF,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACxB,YAAY,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;YACxD,CAAC;YAED,MAAM,IAAI,GAAS;gBACjB,OAAO,EAAE,CAAC;gBACV,OAAO;gBACP,cAAc;gBACd,eAAe;gBACf,OAAO,EAAE;oBACP,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;oBAC5B,OAAO,EAAE,EAAE,GAAG,YAAY,EAAE,GAAG,OAAO,EAAE;oBACxC,OAAO;oBACP,MAAM;oBACN,YAAY;oBACZ,IAAI;iBACL;gBACD,MAAM;gBACN,OAAO;gBACP,SAAS;aACV,CAAC;YAEF,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;gBAChC,IAAI,OAAO,KAAK,CAAC,iBAAiB,KAAK,UAAU,EAAE,CAAC;oBAClD,MAAM,OAAO,GAAG,EAAgC,CAAC;oBACjD,KAAK,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;oBACjC,IAAI,CAAC,KAAK,GAAG,GAAG,EAAE,CAChB,KAAK,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;gBACzD,CAAC;qBAAM,CAAC;oBACN,MAAM,OAAO,GAAG,kBAAkB,EAAgC,CAAC;oBACnE,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC;wBAC3D,IAAI,CAAC,KAAK,GAAG,GAAG,EAAE,CAChB,KAAK,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;oBACzD,CAAC;gBACH,CAAC;YACH,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvB,IAAI,CAAC,SAAS,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAthBD,gCAshBC", "sourcesContent": ["/**\n * ```ts\n * import type { Config } from \"arangojs/connection\";\n * ```\n *\n * The \"connection\" module provides connection and configuration related types\n * for TypeScript.\n *\n * @packageDocumentation\n */\nimport { ClientRequest } from \"http\";\nimport { AgentOptions as NodeAgentOptions } from \"https\";\nimport { LinkedList } from \"x3-linkedlist\";\nimport { Database } from \"./database\";\nimport {\n  ArangoError,\n  HttpError,\n  isArangoError,\n  isArangoErrorResponse,\n  isSystemError,\n} from \"./error\";\nimport { base64Encode } from \"./lib/btoa\";\nimport {\n  ERROR_ARANGO_CONFLICT,\n  ERROR_ARANGO_MAINTENANCE_MODE,\n} from \"./lib/codes\";\nimport { Errback } from \"./lib/errback\";\nimport { normalizeUrl } from \"./lib/normalizeUrl\";\nimport { querystringify } from \"./lib/querystringify\";\nimport {\n  ArangojsError,\n  ArangojsResponse,\n  createRequest,\n  isBrowser,\n  RequestFunction,\n} from \"./lib/request\";\n\nconst MIME_JSON = /\\/(json|javascript)(\\W|$)/;\nconst LEADER_ENDPOINT_HEADER = \"x-arango-endpoint\";\n\n/**\n * Determines the behavior when multiple URLs are used:\n *\n * - `\"NONE\"`: No load balancing. All requests will be handled by the first\n *   URL in the list until a network error is encountered. On network error,\n *   arangojs will advance to using the next URL in the list.\n *\n * - `\"ONE_RANDOM\"`: Randomly picks one URL from the list initially, then\n *   behaves like `\"NONE\"`.\n *\n * - `\"ROUND_ROBIN\"`: Every sequential request uses the next URL in the list.\n */\nexport type LoadBalancingStrategy = \"NONE\" | \"ROUND_ROBIN\" | \"ONE_RANDOM\";\n\n/**\n * An arbitrary object with string values representing HTTP headers and their\n * values.\n *\n * Header names should always be lowercase.\n */\nexport type Headers = Record<string, string>;\n\n/**\n * An arbitrary object with scalar values representing query string parameters\n * and their values.\n */\nexport type Params = Record<string, any>;\n\n/**\n * Generic properties shared by all ArangoDB HTTP API responses.\n */\nexport type ArangoResponseMetadata = {\n  /**\n   * Indicates that the request was successful.\n   */\n  error: false;\n  /**\n   * Response status code, typically `200`.\n   */\n  code: number;\n};\n\n/**\n * Extends the given base type `T` with the generic HTTP API response properties.\n */\nexport type ArangoApiResponse<T> = T & ArangoResponseMetadata;\n\n/**\n * Credentials for HTTP Basic authentication.\n */\nexport type BasicAuthCredentials = {\n  /**\n   * Username to use for authentication, e.g. `\"root\"`.\n   */\n  username: string;\n  /**\n   * Password to use for authentication. Defaults to an empty string.\n   */\n  password?: string;\n};\n\n/**\n * Credentials for HTTP Bearer token authentication.\n */\nexport type BearerAuthCredentials = {\n  /**\n   * Bearer token to use for authentication.\n   */\n  token: string;\n};\n\nfunction isBearerAuth(auth: any): auth is BearerAuthCredentials {\n  return auth.hasOwnProperty(\"token\");\n}\n\n/**\n * @internal\n */\nfunction generateStackTrace() {\n  let err = new Error();\n  if (!err.stack) {\n    try {\n      throw err;\n    } catch (e: any) {\n      err = e;\n    }\n  }\n  return err;\n}\n\n/**\n * @internal\n */\ntype UrlInfo = {\n  absolutePath?: boolean;\n  basePath?: string;\n  path?: string;\n  qs?: string | Params;\n};\n\n/**\n * Options of the `xhr` module that can be set using `agentOptions` when using\n * arangojs in the browser. Additionally `maxSockets` can be used to control\n * the maximum number of parallel requests.\n *\n * See also: [`xhr` on npm](https://www.npmjs.com/package/xhr).\n */\nexport type XhrOptions = {\n  /**\n   * Maximum number of parallel requests arangojs will perform. If any\n   * additional requests are attempted, they will be enqueued until one of the\n   * active requests has completed.\n   */\n  maxSockets?: number;\n  /**\n   * Number of milliseconds to wait for a response.\n   *\n   * Default: `0` (disabled)\n   */\n  timeout?: number;\n  /**\n   * Callback that will be invoked immediately before the `send` method of the\n   * request is called.\n   *\n   * See also {@link RequestInterceptors}.\n   */\n  beforeSend?: (xhrObject: any) => void;\n  /**\n   * `XMLHttpRequest` object to use instead of the native implementation.\n   */\n  xhr?: any;\n  /**\n   * (Internet Explorer 10 and lower only.) Whether `XDomainRequest` should be\n   * used instead of `XMLHttpRequest`. Only required for performing\n   * cross-domain requests in older versions of Internet Explorer.\n   */\n  useXdr?: boolean;\n  /**\n   * Specifies whether browser credentials (e.g. cookies) should be sent if\n   * performing a cross-domain request.\n   *\n   * See [`XMLHttpRequest.withCredentials`](https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/withCredentials).\n   */\n  withCredentials?: boolean;\n};\n\n/**\n * Additional options for intercepting the request/response. These methods\n * are primarily intended for tracking network related metrics.\n */\nexport type RequestInterceptors = {\n  /**\n   * Callback that will be invoked with the finished request object before it\n   * is finalized. In the browser the request may already have been sent.\n   *\n   * @param req - Request object or XHR instance used for this request.\n   */\n  before?: (req: ClientRequest) => void;\n  /**\n   * Callback that will be invoked when the server response has been received\n   * and processed or when the request has been failed without a response.\n   *\n   * The originating request will be available as the `request` property\n   * on either the error or response object.\n   *\n   * @param err - Error encountered when handling this request or `null`.\n   * @param res - Response object for this request, if no error occurred.\n   */\n  after?: (err: ArangojsError | null, res?: ArangojsResponse) => void;\n};\n\n/**\n * Options for performing a request with arangojs.\n */\nexport type RequestOptions = {\n  /**\n   * @internal\n   *\n   * Identifier of a specific ArangoDB host to use when more than one is known.\n   */\n  hostUrl?: string;\n  /**\n   * HTTP method to use in order to perform the request.\n   *\n   * Default: `\"GET\"`\n   */\n  method?: string;\n  /**\n   * Request body data.\n   */\n  body?: any;\n  /**\n   * If set to `true`, the response body will not be interpreted as JSON and\n   * instead passed as-is.\n   */\n  expectBinary?: boolean;\n  /**\n   * If set to `true`, the request body will not be converted to JSON and\n   * instead passed as-is.\n   */\n  isBinary?: boolean;\n  /**\n   * Whether ArangoDB is allowed to perform a dirty read to respond to this\n   * request. If set to `true`, the response may reflect a dirty state from\n   * a non-authoritative server.\n   */\n  allowDirtyRead?: boolean;\n  /**\n   * If set to a positive number, the request will automatically be retried at\n   * most this many times if it results in a write-write conflict.\n   *\n   * Default: `config.retryOnConflict`\n   */\n  retryOnConflict?: number;\n  /**\n   * HTTP headers to pass along with this request in addition to the default\n   * headers generated by arangojs.\n   */\n  headers?: Headers;\n  /**\n   * Time in milliseconds after which arangojs will abort the request if the\n   * socket has not already timed out.\n   *\n   * See also `agentOptions.timeout` in {@link Config}.\n   */\n  timeout?: number;\n  /**\n   * Optional prefix path to prepend to the `path`.\n   */\n  basePath?: string;\n  /**\n   * URL path, relative to the `basePath` and server domain.\n   */\n  path?: string;\n  /**\n   * URL parameters to pass as part of the query string.\n   */\n  qs?: string | Params;\n};\n\n/**\n * @internal\n */\ntype Task = {\n  hostUrl?: string;\n  stack?: () => string;\n  allowDirtyRead: boolean;\n  retryOnConflict: number;\n  resolve: (result: any) => void;\n  reject: (error: Error) => void;\n  transform?: (res: ArangojsResponse) => any;\n  retries: number;\n  options: {\n    method: string;\n    expectBinary: boolean;\n    timeout?: number;\n    url: { pathname: string; search?: string };\n    headers: Headers;\n    body: any;\n  };\n};\n\n/**\n * Options for creating the Node.js `http.Agent` or `https.Agent`.\n *\n * In browser environments this option can be used to pass additional options\n * to the underlying calls of the\n * [xhr module](https://www.npmjs.com/package/xhr).\n *\n * See also [`http.Agent`](https://nodejs.org/api/http.html#http_new_agent_options)\n * and [`https.Agent`](https://nodejs.org/api/https.html#https_new_agent_options)\n * (when using TLS).\n */\nexport type AgentOptions = NodeAgentOptions | XhrOptions;\n\n/**\n * Options for configuring arangojs.\n */\nexport type Config = {\n  /**\n   * Name of the database to use.\n   *\n   * Default: `\"_system\"`\n   */\n  databaseName?: string;\n  /**\n   * Base URL of the ArangoDB server or list of server URLs.\n   *\n   * When working with a cluster, the method {@link database.Database#acquireHostList}\n   * can be used to automatically pick up additional coordinators/followers at\n   * any point.\n   *\n   * When running ArangoDB on a unix socket, e.g. `/tmp/arangodb.sock`, the\n   * following URL formats are supported for unix sockets:\n   *\n   * - `unix:///tmp/arangodb.sock` (no SSL)\n   * - `http+unix:///tmp/arangodb.sock` (or `https+unix://` for SSL)\n   * - `http://unix:/tmp/arangodb.sock` (or `https://unix:` for SSL)\n   *\n   * Additionally `ssl` and `tls` are treated as synonymous with `https` and\n   * `tcp` is treated as synonymous with `http`, so the following URLs are\n   * considered identical:\n   *\n   * - `tcp://127.0.0.1:8529` and `http://127.0.0.1:8529`\n   * - `ssl://127.0.0.1:8529` and `https://127.0.0.1:8529`\n   * - `tcp+unix:///tmp/arangodb.sock` and `http+unix:///tmp/arangodb.sock`\n   * - `ssl+unix:///tmp/arangodb.sock` and `https+unix:///tmp/arangodb.sock`\n   * - `tcp://unix:/tmp/arangodb.sock` and `http://unix:/tmp/arangodb.sock`\n   * - `ssl://unix:/tmp/arangodb.sock` and `https://unix:/tmp/arangodb.sock`\n   *\n   * See also `auth` for passing authentication credentials.\n   *\n   * Default: `\"http://127.0.0.1:8529\"`\n   */\n  url?: string | string[];\n  /**\n   * Credentials to use for authentication.\n   *\n   * See also {@link database.Database#useBasicAuth} and\n   * {@link database.Database#useBearerAuth}.\n   *\n   * Default: `{ username: \"root\", password: \"\" }`\n   */\n  auth?: BasicAuthCredentials | BearerAuthCredentials;\n  /**\n   * Numeric representation of the ArangoDB version the driver should expect.\n   * The format is defined as `XYYZZ` where `X` is the major version, `Y` is\n   * the zero-filled two-digit minor version and `Z` is the zero-filled two-digit\n   * bugfix version, e.g. `30102` for 3.1.2, `20811` for 2.8.11.\n   *\n   * Depending on this value certain methods may become unavailable or change\n   * their behavior to remain compatible with different versions of ArangoDB.\n   *\n   * Default: `30900`\n   */\n  arangoVersion?: number;\n  /**\n   * Determines the behavior when multiple URLs are provided:\n   *\n   * - `\"NONE\"`: No load balancing. All requests will be handled by the first\n   *   URL in the list until a network error is encountered. On network error,\n   *   arangojs will advance to using the next URL in the list.\n   *\n   * - `\"ONE_RANDOM\"`: Randomly picks one URL from the list initially, then\n   *   behaves like `\"NONE\"`.\n   *\n   * - `\"ROUND_ROBIN\"`: Every sequential request uses the next URL in the list.\n   *\n   * Default: `\"NONE\"`\n   */\n  loadBalancingStrategy?: LoadBalancingStrategy;\n  /**\n   * Determines the behavior when a request fails because the underlying\n   * connection to the server could not be opened\n   * (i.e. [`ECONNREFUSED` in Node.js](https://nodejs.org/api/errors.html#errors_common_system_errors)):\n   *\n   * - `false`: the request fails immediately.\n   *\n   * - `0`: the request is retried until a server can be reached but only a\n   *   total number of times matching the number of known servers (including\n   *   the initial failed request).\n   *\n   * - any other number: the request is retried until a server can be reached\n   *   or the request has been retried a total of `maxRetries` number of times\n   *   (not including the initial failed request).\n   *\n   * When working with a single server, the retries (if any) will be made to\n   * the same server.\n   *\n   * This setting currently has no effect when using arangojs in a browser.\n   *\n   * **Note**: Requests bound to a specific server (e.g. fetching query results)\n   * will never be retried automatically and ignore this setting.\n   *\n   * **Note**: To set the number of retries when a write-write conflict is\n   * encountered, see `retryOnConflict` instead.\n   *\n   * Default: `0`\n   */\n  maxRetries?: false | number;\n  /**\n   * If set to a positive number, requests will automatically be retried at\n   * most this many times if they result in a write-write conflict.\n   *\n   * Default: `0`\n   */\n  retryOnConflict?: number;\n  /**\n   * An http `Agent` instance to use for connections.\n   *\n   * By default a new `Agent` instance will be created using the `agentOptions`.\n   *\n   * This option has no effect when using the browser version of arangojs.\n   *\n   * See also [`http.Agent`](https://nodejs.org/api/http.html#http_new_agent_options)\n   * and [`https.Agent`](https://nodejs.org/api/https.html#https_new_agent_options)\n   * (when using TLS).\n   */\n  agent?: any;\n  /**\n   * Options used to create that underlying HTTP/HTTPS `Agent` (or the `xhr`\n   * module when using arangojs in the browser). This will be ignored if\n   * `agent` is also provided.\n   *\n   * The option `maxSockets` is also used to limit how many requests\n   * arangojs will perform concurrently. The maximum number of requests is\n   * equal to `maxSockets`.\n   *\n   * **Note:** arangojs will limit the number of concurrent requests based on\n   * this value even if an `agent` is provided.\n   *\n   * **Note:** when using `ROUND_ROBIN` load balancing and passing an array of\n   * URLs in the `url` option, the default value of `maxSockets` will be set\n   * to `3 * url.length` instead of `3`.\n   *\n   * Default (Node.js): `{ maxSockets: 3, keepAlive: true, keepAliveMsecs: 1000 }`\n   *\n   * Default (browser): `{ maxSockets: 3, useXDR: true, withCredentials: true }`\n   */\n  agentOptions?: AgentOptions & RequestInterceptors;\n  /**\n   * An object with additional headers to send with every request.\n   *\n   * If an `\"authorization\"` header is provided, it will be overridden when\n   * using {@link database.Database#useBasicAuth}, {@link database.Database#useBearerAuth} or\n   * the `auth` configuration option.\n   */\n  headers?: Headers;\n  /**\n   * If set to `true`, arangojs will generate stack traces every time a request\n   * is initiated and augment the stack traces of any errors it generates.\n   *\n   * **Warning**: This will cause arangojs to generate stack traces in advance\n   * even if the request does not result in an error. Generating stack traces\n   * may negatively impact performance.\n   */\n  precaptureStackTraces?: boolean;\n  /**\n   * Limits the number of values of server-reported response queue times that\n   * will be stored and accessible using {@link database.Database#queueTime}. If set to\n   * a finite value, older values will be discarded to make room for new values\n   * when that limit is reached.\n   *\n   * Default: `10`\n   */\n  responseQueueTimeSamples?: number;\n};\n\n/**\n * Indicates whether the given value represents a {@link Connection}.\n *\n * @param connection - A value that might be a connection.\n *\n * @internal\n */\nexport function isArangoConnection(connection: any): connection is Connection {\n  return Boolean(connection && connection.isArangoConnection);\n}\n\n/**\n * Represents a connection pool shared by one or more databases.\n *\n * @internal\n */\nexport class Connection {\n  protected _activeTasks: number = 0;\n  protected _agent?: any;\n  protected _agentOptions: { [key: string]: any };\n  protected _arangoVersion: number = 30900;\n  protected _headers: Headers;\n  protected _loadBalancingStrategy: LoadBalancingStrategy;\n  protected _maxRetries: number | false;\n  protected _retryOnConflict: number;\n  protected _maxTasks: number;\n  protected _queue = new LinkedList<Task>();\n  protected _databases = new Map<string, Database>();\n  protected _hosts: RequestFunction[] = [];\n  protected _hostUrls: string[] = [];\n  protected _activeHostUrl: string;\n  protected _activeDirtyHostUrl: string;\n  protected _transactionId: string | null = null;\n  protected _precaptureStackTraces: boolean;\n  protected _queueTimes = new LinkedList<[number, number]>();\n  protected _responseQueueTimeSamples: number;\n\n  /**\n   * @internal\n   *\n   * Creates a new `Connection` instance.\n   *\n   * @param config - An object with configuration options.\n   *\n   */\n  constructor(config: Omit<Config, \"databaseName\"> = {}) {\n    const URLS = config.url\n      ? Array.isArray(config.url)\n        ? config.url\n        : [config.url]\n      : [\"http://127.0.0.1:8529\"];\n    const MAX_SOCKETS =\n      3 * (config.loadBalancingStrategy === \"ROUND_ROBIN\" ? URLS.length : 1);\n\n    if (config.arangoVersion !== undefined) {\n      this._arangoVersion = config.arangoVersion;\n    }\n    this._agent = config.agent;\n    this._agentOptions = isBrowser\n      ? { maxSockets: MAX_SOCKETS, ...config.agentOptions }\n      : {\n          maxSockets: MAX_SOCKETS,\n          keepAlive: true,\n          keepAliveMsecs: 1000,\n          scheduling: \"lifo\",\n          ...config.agentOptions,\n        };\n    this._maxTasks = this._agentOptions.maxSockets;\n    this._headers = { ...config.headers };\n    this._loadBalancingStrategy = config.loadBalancingStrategy ?? \"NONE\";\n    this._precaptureStackTraces = Boolean(config.precaptureStackTraces);\n    this._responseQueueTimeSamples = config.responseQueueTimeSamples ?? 10;\n    this._retryOnConflict = config.retryOnConflict ?? 0;\n    if (this._responseQueueTimeSamples < 0) {\n      this._responseQueueTimeSamples = Infinity;\n    }\n    if (config.maxRetries === false) {\n      this._maxRetries = false;\n    } else {\n      this._maxRetries = Number(config.maxRetries ?? 0);\n    }\n\n    this.addToHostList(URLS);\n\n    if (config.auth) {\n      if (isBearerAuth(config.auth)) {\n        this.setBearerAuth(config.auth);\n      } else {\n        this.setBasicAuth(config.auth);\n      }\n    }\n\n    if (this._loadBalancingStrategy === \"ONE_RANDOM\") {\n      this._activeHostUrl =\n        this._hostUrls[Math.floor(Math.random() * this._hostUrls.length)];\n      this._activeDirtyHostUrl =\n        this._hostUrls[Math.floor(Math.random() * this._hostUrls.length)];\n    } else {\n      this._activeHostUrl = this._hostUrls[0];\n      this._activeDirtyHostUrl = this._hostUrls[0];\n    }\n  }\n\n  /**\n   * @internal\n   *\n   * Indicates that this object represents an ArangoDB connection.\n   */\n  get isArangoConnection(): true {\n    return true;\n  }\n\n  get queueTime() {\n    return {\n      getLatest: () => this._queueTimes.last?.value[1],\n      getValues: () => Array.from(this._queueTimes.values()),\n      getAvg: () => {\n        let avg = 0;\n        for (const [, [, value]] of this._queueTimes) {\n          avg += value / this._queueTimes.length;\n        }\n        return avg;\n      },\n    };\n  }\n\n  protected _runQueue() {\n    if (!this._queue.length || this._activeTasks >= this._maxTasks) return;\n    const task = this._queue.shift()!;\n    let hostUrl = this._activeHostUrl;\n    if (task.hostUrl !== undefined) {\n      hostUrl = task.hostUrl;\n    } else if (task.allowDirtyRead) {\n      hostUrl = this._activeDirtyHostUrl;\n      this._activeDirtyHostUrl =\n        this._hostUrls[\n          (this._hostUrls.indexOf(this._activeDirtyHostUrl) + 1) %\n            this._hostUrls.length\n        ];\n      task.options.headers[\"x-arango-allow-dirty-read\"] = \"true\";\n    } else if (this._loadBalancingStrategy === \"ROUND_ROBIN\") {\n      this._activeHostUrl =\n        this._hostUrls[\n          (this._hostUrls.indexOf(this._activeHostUrl) + 1) %\n            this._hostUrls.length\n        ];\n    }\n    this._activeTasks += 1;\n    const callback: Errback<ArangojsResponse> = (err, res) => {\n      this._activeTasks -= 1;\n      if (!err && res) {\n        if (res.statusCode === 503 && res.headers[LEADER_ENDPOINT_HEADER]) {\n          const url = res.headers[LEADER_ENDPOINT_HEADER]!;\n          const [cleanUrl] = this.addToHostList(url);\n          task.hostUrl = cleanUrl;\n          if (this._activeHostUrl === hostUrl) {\n            this._activeHostUrl = cleanUrl;\n          }\n          this._queue.push(task);\n        } else {\n          res.arangojsHostUrl = hostUrl;\n          const contentType = res.headers[\"content-type\"];\n          const queueTime = res.headers[\"x-arango-queue-time-seconds\"];\n          if (queueTime) {\n            this._queueTimes.push([Date.now(), Number(queueTime)]);\n            while (this._responseQueueTimeSamples < this._queueTimes.length) {\n              this._queueTimes.shift();\n            }\n          }\n          let parsedBody: any = undefined;\n          if (res.body.length && contentType && contentType.match(MIME_JSON)) {\n            try {\n              parsedBody = res.body;\n              parsedBody = JSON.parse(parsedBody);\n            } catch (e: any) {\n              if (!task.options.expectBinary) {\n                if (typeof parsedBody !== \"string\") {\n                  parsedBody = res.body.toString(\"utf-8\");\n                }\n                e.res = res;\n                if (task.stack) {\n                  e.stack += task.stack();\n                }\n                callback(e);\n                return;\n              }\n            }\n          } else if (res.body && !task.options.expectBinary) {\n            parsedBody = res.body.toString(\"utf-8\");\n          } else {\n            parsedBody = res.body;\n          }\n          if (isArangoErrorResponse(parsedBody)) {\n            res.body = parsedBody;\n            err = new ArangoError(res);\n          } else if (res.statusCode && res.statusCode >= 400) {\n            res.body = parsedBody;\n            err = new HttpError(res);\n          } else {\n            if (!task.options.expectBinary) res.body = parsedBody;\n            task.resolve(task.transform ? task.transform(res) : (res as any));\n          }\n        }\n      }\n      if (err) {\n        if (\n          !task.allowDirtyRead &&\n          this._hosts.length > 1 &&\n          this._activeHostUrl === hostUrl &&\n          this._loadBalancingStrategy !== \"ROUND_ROBIN\"\n        ) {\n          this._activeHostUrl =\n            this._hostUrls[\n              (this._hostUrls.indexOf(this._activeHostUrl) + 1) %\n                this._hostUrls.length\n            ];\n        }\n        if (\n          isArangoError(err) &&\n          err.errorNum === ERROR_ARANGO_CONFLICT &&\n          task.retryOnConflict > 0\n        ) {\n          task.retryOnConflict -= 1;\n          this._queue.push(task);\n        } else if (\n          ((isSystemError(err) &&\n            err.syscall === \"connect\" &&\n            err.code === \"ECONNREFUSED\") ||\n            (isArangoError(err) &&\n              err.errorNum === ERROR_ARANGO_MAINTENANCE_MODE)) &&\n          task.hostUrl === undefined &&\n          this._maxRetries !== false &&\n          task.retries < (this._maxRetries || this._hosts.length - 1)\n        ) {\n          task.retries += 1;\n          this._queue.push(task);\n        } else {\n          if (task.stack) {\n            err.stack += task.stack();\n          }\n          task.reject(err);\n        }\n      }\n      this._runQueue();\n    };\n    try {\n      this._hosts[this._hostUrls.indexOf(hostUrl)](task.options, callback);\n    } catch (e: any) {\n      callback(e);\n    }\n  }\n\n  protected _buildUrl({ basePath, path, qs }: UrlInfo) {\n    const pathname = `${basePath || \"\"}${path || \"\"}`;\n    let search;\n    if (qs) {\n      if (typeof qs === \"string\") search = `?${qs}`;\n      else search = `?${querystringify(qs)}`;\n    }\n    return search ? { pathname, search } : { pathname };\n  }\n\n  setBearerAuth(auth: BearerAuthCredentials) {\n    this.setHeader(\"authorization\", `Bearer ${auth.token}`);\n  }\n\n  setBasicAuth(auth: BasicAuthCredentials) {\n    this.setHeader(\n      \"authorization\",\n      `Basic ${base64Encode(`${auth.username}:${auth.password}`)}`\n    );\n  }\n\n  setResponseQueueTimeSamples(responseQueueTimeSamples: number) {\n    if (responseQueueTimeSamples < 0) {\n      responseQueueTimeSamples = Infinity;\n    }\n    this._responseQueueTimeSamples = responseQueueTimeSamples;\n    while (this._responseQueueTimeSamples < this._queueTimes.length) {\n      this._queueTimes.shift();\n    }\n  }\n\n  /**\n   * @internal\n   *\n   * Fetches a {@link database.Database} instance for the given database name from the\n   * internal cache, if available.\n   *\n   * @param databaseName - Name of the database.\n   */\n  database(databaseName: string): Database | undefined;\n  /**\n   * @internal\n   *\n   * Adds a {@link database.Database} instance for the given database name to the\n   * internal cache.\n   *\n   * @param databaseName - Name of the database.\n   * @param database - Database instance to add to the cache.\n   */\n  database(databaseName: string, database: Database): Database;\n  /**\n   * @internal\n   *\n   * Clears any {@link database.Database} instance stored for the given database name\n   * from the internal cache, if present.\n   *\n   * @param databaseName - Name of the database.\n   * @param database - Must be `null`.\n   */\n  database(databaseName: string, database: null): undefined;\n  database(\n    databaseName: string,\n    database?: Database | null\n  ): Database | undefined {\n    if (database === null) {\n      this._databases.delete(databaseName);\n      return undefined;\n    }\n    if (!database) {\n      return this._databases.get(databaseName);\n    }\n    this._databases.set(databaseName, database);\n    return database;\n  }\n\n  /**\n   * @internal\n   *\n   * Replaces the host list with the given URLs.\n   *\n   * See {@link Connection#acquireHostList}.\n   *\n   * @param urls - URLs to use as host list.\n   */\n  setHostList(urls: string[]): void {\n    const cleanUrls = urls.map((url) => normalizeUrl(url));\n    this._hosts.splice(\n      0,\n      this._hosts.length,\n      ...cleanUrls.map((url) => {\n        const i = this._hostUrls.indexOf(url);\n        if (i !== -1) return this._hosts[i];\n        return createRequest(url, this._agentOptions, this._agent);\n      })\n    );\n    this._hostUrls.splice(0, this._hostUrls.length, ...cleanUrls);\n  }\n\n  /**\n   * @internal\n   *\n   * Adds the given URL or URLs to the host list.\n   *\n   * See {@link Connection#acquireHostList}.\n   *\n   * @param urls - URL or URLs to add.\n   */\n  addToHostList(urls: string | string[]): string[] {\n    const cleanUrls = (Array.isArray(urls) ? urls : [urls]).map((url) =>\n      normalizeUrl(url)\n    );\n    const newUrls = cleanUrls.filter(\n      (url) => this._hostUrls.indexOf(url) === -1\n    );\n    this._hostUrls.push(...newUrls);\n    this._hosts.push(\n      ...newUrls.map((url: string) =>\n        createRequest(url, this._agentOptions, this._agent)\n      )\n    );\n    return cleanUrls;\n  }\n\n  /**\n   * @internal\n   *\n   * Sets the connection's active `transactionId`.\n   *\n   * While set, all requests will use this ID, ensuring the requests are executed\n   * within the transaction if possible. Setting the ID manually may cause\n   * unexpected behavior.\n   *\n   * See also {@link Connection#clearTransactionId}.\n   *\n   * @param transactionId - ID of the active transaction.\n   */\n  setTransactionId(transactionId: string) {\n    this._transactionId = transactionId;\n  }\n\n  /**\n   * @internal\n   *\n   * Clears the connection's active `transactionId`.\n   */\n  clearTransactionId() {\n    this._transactionId = null;\n  }\n\n  /**\n   * @internal\n   *\n   * Sets the header `headerName` with the given `value` or clears the header if\n   * `value` is `null`.\n   *\n   * @param headerName - Name of the header to set.\n   * @param value - Value of the header.\n   */\n  setHeader(headerName: string, value: string | null) {\n    if (value === null) {\n      delete this._headers[headerName];\n    } else {\n      this._headers[headerName] = value;\n    }\n  }\n\n  /**\n   * @internal\n   *\n   * Closes all open connections.\n   *\n   * See {@link database.Database#close}.\n   */\n  close() {\n    for (const host of this._hosts) {\n      if (host.close) host.close();\n    }\n  }\n\n  /**\n   * @internal\n   *\n   * Waits for propagation.\n   *\n   * See {@link database.Database#waitForPropagation}.\n   *\n   * @param request - Request to perform against each coordinator.\n   * @param timeout - Maximum number of milliseconds to wait for propagation.\n   */\n  async waitForPropagation(request: RequestOptions, timeout = Infinity) {\n    const numHosts = this._hosts.length;\n    const propagated = [] as string[];\n    const started = Date.now();\n    let index = 0;\n    while (true) {\n      if (propagated.length === numHosts) {\n        return;\n      }\n      while (propagated.includes(this._hostUrls[index])) {\n        index = (index + 1) % numHosts;\n      }\n      const hostUrl = this._hostUrls[index];\n      try {\n        await this.request({ ...request, hostUrl });\n      } catch (e: any) {\n        if (started + timeout < Date.now()) {\n          throw e;\n        }\n        await new Promise((resolve) => setTimeout(resolve, 1000));\n        continue;\n      }\n      if (!propagated.includes(hostUrl)) {\n        propagated.push(hostUrl);\n      }\n    }\n  }\n\n  /**\n   * @internal\n   *\n   * Performs a request using the arangojs connection pool.\n   */\n  request<T = ArangojsResponse>(\n    {\n      hostUrl,\n      method = \"GET\",\n      body,\n      expectBinary = false,\n      isBinary = false,\n      allowDirtyRead = false,\n      retryOnConflict = this._retryOnConflict,\n      timeout = 0,\n      headers,\n      ...urlInfo\n    }: RequestOptions,\n    transform?: (res: ArangojsResponse) => T\n  ): Promise<T> {\n    return new Promise((resolve, reject) => {\n      let contentType = \"text/plain\";\n      if (isBinary) {\n        contentType = \"application/octet-stream\";\n      } else if (body) {\n        if (typeof body === \"object\") {\n          body = JSON.stringify(body);\n          contentType = \"application/json\";\n        } else {\n          body = String(body);\n        }\n      }\n\n      const extraHeaders: Headers = {\n        ...this._headers,\n        \"content-type\": contentType,\n        \"x-arango-version\": String(this._arangoVersion),\n        \"x-arango-driver\": `arangojs/${process.env.ARANGOJS_VERSION} (cloud)`,\n      };\n\n      if (this._transactionId) {\n        extraHeaders[\"x-arango-trx-id\"] = this._transactionId;\n      }\n\n      const task: Task = {\n        retries: 0,\n        hostUrl,\n        allowDirtyRead,\n        retryOnConflict,\n        options: {\n          url: this._buildUrl(urlInfo),\n          headers: { ...extraHeaders, ...headers },\n          timeout,\n          method,\n          expectBinary,\n          body,\n        },\n        reject,\n        resolve,\n        transform,\n      };\n\n      if (this._precaptureStackTraces) {\n        if (typeof Error.captureStackTrace === \"function\") {\n          const capture = {} as { readonly stack: string };\n          Error.captureStackTrace(capture);\n          task.stack = () =>\n            `\\n${capture.stack.split(\"\\n\").slice(3).join(\"\\n\")}`;\n        } else {\n          const capture = generateStackTrace() as { readonly stack: string };\n          if (Object.prototype.hasOwnProperty.call(capture, \"stack\")) {\n            task.stack = () =>\n              `\\n${capture.stack.split(\"\\n\").slice(4).join(\"\\n\")}`;\n          }\n        }\n      }\n\n      this._queue.push(task);\n      this._runQueue();\n    });\n  }\n}\n"]}