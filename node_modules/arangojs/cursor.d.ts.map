{"version": 3, "file": "cursor.d.ts", "sourceRoot": "", "sources": ["../src/cursor.ts"], "names": [], "mappings": "AAAA;;;;;;;;GAQG;AACH,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC3C,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAEtC;;GAEG;AACH,MAAM,WAAW,YAAY;IAC3B;;OAEG;IACH,QAAQ,EAAE;QACR,IAAI,EAAE,MAAM,CAAC;QACb,OAAO,EAAE,MAAM,CAAC;KACjB,EAAE,CAAC;IACJ;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAC3B;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACjC;;OAEG;IACH,KAAK,CAAC,EAAE,WAAW,CAAC;CACrB;AAED;;GAEG;AACH,MAAM,WAAW,WAAW;IAC1B;;;OAGG;IACH,SAAS,EAAE,MAAM,CAAC;IAClB;;;OAGG;IACH,WAAW,EAAE,MAAM,CAAC;IACpB;;OAEG;IACH,cAAc,EAAE,MAAM,CAAC;IACvB;;OAEG;IACH,cAAc,EAAE,MAAM,CAAC;IACvB;;OAEG;IACH,cAAc,EAAE,MAAM,CAAC;IACvB;;OAEG;IACH,aAAa,EAAE,MAAM,CAAC;IACtB;;OAEG;IACH,WAAW,EAAE,MAAM,CAAC;IACpB;;OAEG;IACH,YAAY,EAAE,MAAM,CAAC;IACrB;;OAEG;IACH,QAAQ,EAAE,MAAM,CAAC;IACjB;;OAEG;IACH,eAAe,EAAE,MAAM,CAAC;IACxB;;OAEG;IACH,aAAa,EAAE,MAAM,CAAC;IACtB;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB;;OAEG;IACH,YAAY,EAAE,MAAM,CAAC;IACrB;;OAEG;IACH,KAAK,CAAC,EAAE;QACN;;WAEG;QACH,EAAE,EAAE,MAAM,CAAC;QACX;;WAEG;QACH,KAAK,EAAE,MAAM,CAAC;QACd;;WAEG;QACH,KAAK,EAAE,MAAM,CAAC;QACd,MAAM,EAAE,MAAM,CAAC;QACf;;WAEG;QACH,OAAO,EAAE,MAAM,CAAC;KACjB,EAAE,CAAC;CACL;AAED,UAAU,SAAS,CAAC,CAAC,GAAG,GAAG;IACzB,OAAO,EAAE,OAAO,CAAC;IACjB,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IACtB,KAAK,IAAI,CAAC,GAAG,SAAS,CAAC;CACxB;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BG;AACH,qBAAa,kBAAkB,CAAC,CAAC,GAAG,GAAG;IACrC,SAAS,CAAC,GAAG,EAAE,QAAQ,CAAC;IACxB,SAAS,CAAC,QAAQ,EAAE,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;IAChD,SAAS,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC;IAC1B,SAAS,CAAC,MAAM,EAAE,YAAY,CAAC;IAC/B,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC;IAC5B,SAAS,CAAC,YAAY,CAAC,EAAE,MAAM,CAAC;IAChC,SAAS,CAAC,GAAG,EAAE,MAAM,GAAG,SAAS,CAAC;IAClC,SAAS,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC;IAC5B,SAAS,CAAC,eAAe,CAAC,EAAE,OAAO,CAAC;IACpC,SAAS,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;IAEvC;;OAEG;gBAED,EAAE,EAAE,QAAQ,EACZ,IAAI,EAAE;QACJ,KAAK,EAAE,GAAG,CAAC;QACX,MAAM,EAAE,CAAC,EAAE,CAAC;QACZ,OAAO,EAAE,OAAO,CAAC;QACjB,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,EAAE,EAAE,MAAM,CAAC;QACX,KAAK,EAAE,MAAM,CAAC;KACf,EACD,OAAO,CAAC,EAAE,MAAM,EAChB,cAAc,CAAC,EAAE,OAAO;cAiCV,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAetC;;;;OAIG;IACH,IAAI,KAAK,mBAER;IAED;;OAEG;IACH,IAAI,KAAK,IAAI,QAAQ,CAAC,YAAY,CAAC,CAElC;IAED;;;OAGG;IACH,IAAI,KAAK,IAAI,MAAM,GAAG,SAAS,CAE9B;IAED;;;;;OAKG;IACH,IAAI,OAAO,IAAI,OAAO,CAErB;IAED;;;OAGG;IACH,IAAI,OAAO,IAAI,OAAO,CAErB;IAED;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACI,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,cAAc,CAAC,CAAC,EAAE,EAAE,SAAS,EAAE,SAAS,CAAC;IAO1E;;;;;;;;;;;;;;;;;;;;;OAqBG;IACG,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;IAM9B;;;;;;;;;;;;;OAaG;IACG,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC;IAI3B;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACG,IAAI,IAAI,OAAO,CAAC,CAAC,EAAE,GAAG,SAAS,CAAC;IActC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA8CG;IACG,OAAO,CACX,QAAQ,EAAE,CAAC,YAAY,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,KAAK,KAAK,GAAG,IAAI,GACvE,OAAO,CAAC,OAAO,CAAC;IAYnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA4BG;IACG,GAAG,CAAC,CAAC,EACT,QAAQ,EAAE,CAAC,YAAY,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,KAAK,CAAC,GAC5D,OAAO,CAAC,CAAC,EAAE,CAAC;IAWf;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA2CG;IACG,OAAO,CAAC,CAAC,EACb,QAAQ,EAAE,CAAC,YAAY,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,GAClE,OAAO,CAAC,CAAC,EAAE,CAAC;IAgBf;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAoEG;IACG,MAAM,CAAC,CAAC,EACZ,OAAO,EAAE,CACP,WAAW,EAAE,CAAC,EACd,YAAY,EAAE,CAAC,EAAE,EACjB,KAAK,EAAE,MAAM,EACb,IAAI,EAAE,IAAI,KACP,CAAC,EACN,YAAY,EAAE,CAAC,GACd,OAAO,CAAC,CAAC,CAAC;IAEb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA8BG;IACG,MAAM,CAAC,CAAC,EACZ,OAAO,EAAE,CACP,WAAW,EAAE,CAAC,EAAE,GAAG,CAAC,EACpB,YAAY,EAAE,CAAC,EAAE,EACjB,KAAK,EAAE,MAAM,EACb,IAAI,EAAE,IAAI,KACP,CAAC,GACL,OAAO,CAAC,CAAC,GAAG,SAAS,CAAC;IAyBzB;;;;;;;;;;;;;;;;;;OAkBG;IACG,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC;CAmB5B;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BG;AACH,qBAAa,WAAW,CAAC,CAAC,GAAG,GAAG;IAC9B,SAAS,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAC,CAAC,CAAC;IAC1C,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;IAE9B;;OAEG;gBACS,aAAa,EAAE,kBAAkB,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;IAKjE;;;;;OAKG;IACH,IAAI,OAAO,0BAEV;IAED;;OAEG;IACH,IAAI,KAAK,IAAI,YAAY,CAExB;IAED;;;OAGG;IACH,IAAI,KAAK,IAAI,MAAM,GAAG,SAAS,CAE9B;IAED;;;OAGG;IACH,IAAI,OAAO,IAAI,OAAO,CAErB;IAED;;;;;;;;;;;;;;;;;;;;OAoBG;IACI,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,cAAc,CAAC,CAAC,EAAE,SAAS,EAAE,SAAS,CAAC;IAOxE;;;;;;;;;;OAUG;IACG,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC;IAIzB;;;;;;;;;;;;;;;;;OAiBG;IACG,IAAI,IAAI,OAAO,CAAC,CAAC,GAAG,SAAS,CAAC;IAUpC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAoCG;IACG,OAAO,CACX,QAAQ,EAAE,CAAC,YAAY,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,KAAK,KAAK,GAAG,IAAI,GACrE,OAAO,CAAC,OAAO,CAAC;IAWnB;;;;;;;;;;;;;;;;;;;;;;;;;OAyBG;IACG,GAAG,CAAC,CAAC,EACT,QAAQ,EAAE,CAAC,YAAY,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,KAAK,CAAC,GAC1D,OAAO,CAAC,CAAC,EAAE,CAAC;IAWf;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAqCG;IACG,OAAO,CAAC,CAAC,EACb,QAAQ,EAAE,CAAC,YAAY,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,GAChE,OAAO,CAAC,CAAC,EAAE,CAAC;IAgBf;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA6DG;IACG,MAAM,CAAC,CAAC,EACZ,OAAO,EAAE,CAAC,WAAW,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,KAAK,CAAC,EAC1E,YAAY,EAAE,CAAC,GACd,OAAO,CAAC,CAAC,CAAC;IACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA4BG;IACG,MAAM,CAAC,CAAC,EACZ,OAAO,EAAE,CACP,WAAW,EAAE,CAAC,GAAG,CAAC,EAClB,YAAY,EAAE,CAAC,EACf,KAAK,EAAE,MAAM,EACb,IAAI,EAAE,IAAI,KACP,CAAC,GACL,OAAO,CAAC,CAAC,GAAG,SAAS,CAAC;IAqBzB;;;;;;;;;;;;;;;;;;OAkBG;IACG,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC;CAG5B"}