{"version": 3, "file": "cursor.js", "sourceRoot": "", "sources": ["../src/cursor.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;GAQG;AACH,iDAA2C;AAoH3C;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BG;AACH,MAAa,kBAAkB;IAY7B;;OAEG;IACH,YACE,EAAY,EACZ,IAOC,EACD,OAAgB,EAChB,cAAwB;QAExB,MAAM,OAAO,GAAG,IAAI,0BAAU,CAC5B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,0BAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CACxD,CAAC;QACF,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC;QACnB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC;QACjD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QACtC,IAAI,CAAC,YAAY,GAAG,IAAI,WAAW,CAAC,IAAI,EAAE;YACxC,IAAI,OAAO;gBACT,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC;YACzB,CAAC;YACD,IAAI,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE;YACxB,KAAK,EAAE,GAAG,EAAE;gBACV,IAAI,KAAK,GAAG,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;gBACjC,OAAO,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;oBAC9B,OAAO,CAAC,KAAK,EAAE,CAAC;oBAChB,KAAK,GAAG,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;gBAC/B,CAAC;gBACD,IAAI,CAAC,KAAK;oBAAE,OAAO,SAAS,CAAC;gBAC7B,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;gBAC5B,IAAI,CAAC,KAAK,CAAC,MAAM;oBAAE,OAAO,CAAC,KAAK,EAAE,CAAC;gBACnC,OAAO,KAAK,CAAC;YACf,CAAC;SACF,CAAC,CAAC;IACL,CAAC;IAES,KAAK,CAAC,KAAK;QACnB,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO;YAAE,OAAO;QACvC,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;YAClC,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,IAAI,CAAC,YAAY;gBACrB,CAAC,CAAC,gBAAgB,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,YAAY,EAAE;gBACrE,CAAC,CAAC,gBAAgB,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;YAClD,OAAO,EAAE,IAAI,CAAC,QAAQ;YACtB,cAAc,EAAE,IAAI,CAAC,eAAe;SACrC,CAAC,CAAC;QACH,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,0BAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;QAChD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC;IACvC,CAAC;IAED;;;;OAIG;IACH,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED;;;OAGG;IACH,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED;;;;;OAKG;IACH,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED;;;OAGG;IACH,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACvD,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACH,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC;QAC3B,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;YACpB,MAAM,IAAI,CAAC,IAAI,EAAkB,CAAC;QACpC,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,KAAK,CAAC,OAAO;QACX,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;YACrB,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;QACrB,CAAC;IACH,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,KAAK,CAAC,GAAG;QACP,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;IACpC,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACH,KAAK,CAAC,IAAI;QACR,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YAC7C,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;QACrB,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;YAC1B,OAAO,SAAS,CAAC;QACnB,CAAC;QACD,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QACpC,IAAI,CAAC,KAAK;YAAE,OAAO,SAAS,CAAC;QAC7B,MAAM,MAAM,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;QACnC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAClB,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA8CG;IACH,KAAK,CAAC,OAAO,CACX,QAAwE;QAExE,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;YACpB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;YACvC,MAAM,MAAM,GAAG,QAAQ,CAAC,YAAa,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;YACpD,KAAK,EAAE,CAAC;YACR,IAAI,MAAM,KAAK,KAAK;gBAAE,OAAO,MAAM,CAAC;YACpC,IAAI,IAAI,CAAC,OAAO;gBAAE,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;QACvC,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA4BG;IACH,KAAK,CAAC,GAAG,CACP,QAA6D;QAE7D,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,MAAM,MAAM,GAAU,EAAE,CAAC;QACzB,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;YACpB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;YACvC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAa,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;YAClD,KAAK,EAAE,CAAC;QACV,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA2CG;IACH,KAAK,CAAC,OAAO,CACX,QAAmE;QAEnE,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,MAAM,MAAM,GAAU,EAAE,CAAC;QACzB,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;YACpB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;YACvC,MAAM,KAAK,GAAG,QAAQ,CAAC,YAAa,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;YACnD,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;gBACzB,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;YACxB,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrB,CAAC;YACD,KAAK,EAAE,CAAC;QACV,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAwHD,KAAK,CAAC,MAAM,CACV,OAKM,EACN,YAAgB;QAEhB,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,CAAC,IAAI,CAAC,OAAO;YAAE,OAAO,YAAY,CAAC;QACvC,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;YAC/B,YAAY,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,CAAQ,CAAC;YAC1C,KAAK,IAAI,CAAC,CAAC;QACb,CAAC;QACD,IAAI,KAAK,GAAG,YAAiB,CAAC;QAC9B,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;YACpB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;YACvC,KAAK,GAAG,OAAO,CAAC,KAAK,EAAE,YAAa,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;YACnD,KAAK,EAAE,CAAC;QACV,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;;;;;;;;;;;;;;;OAkBG;IACH,KAAK,CAAC,IAAI;QACR,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;YACzB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC;gBAC3C,KAAK,CAAC,KAAK,EAAE,CAAC;YAChB,CAAC;YACD,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QACxB,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,OAAO;YAAE,OAAO,SAAS,CAAC;QACpC,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,QAAQ;YAChB,IAAI,EAAE,gBAAgB,kBAAkB,CAAC,IAAI,CAAC,GAAI,CAAC,EAAE;SACtD,EACD,GAAG,EAAE;YACH,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;YACtB,OAAO,SAAS,CAAC;QACnB,CAAC,CACF,CAAC;IACJ,CAAC;CACF;AA9jBD,gDA8jBC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BG;AACH,MAAa,WAAW;IAItB;;OAEG;IACH,YAAY,aAAiC,EAAE,IAAkB;QAC/D,IAAI,CAAC,QAAQ,GAAG,aAAa,CAAC;QAC9B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IACpB,CAAC;IAED;;;;;OAKG;IACH,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACH,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACH,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;IAC9B,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC;QAC3B,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;YACpB,MAAM,IAAI,CAAC,IAAI,EAAgB,CAAC;QAClC,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;;;;;;;;;OAUG;IACH,KAAK,CAAC,GAAG;QACP,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IACxC,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG;IACH,KAAK,CAAC,IAAI;QACR,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YAClD,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;QAC1B,CAAC;QACD,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;YACvB,OAAO,SAAS,CAAC;QACnB,CAAC;QACD,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;IAC5B,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAoCG;IACH,KAAK,CAAC,OAAO,CACX,QAAsE;QAEtE,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;YACpB,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;YAChC,MAAM,MAAM,GAAG,QAAQ,CAAC,KAAM,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;YAC7C,KAAK,EAAE,CAAC;YACR,IAAI,MAAM,KAAK,KAAK;gBAAE,OAAO,MAAM,CAAC;QACtC,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;OAyBG;IACH,KAAK,CAAC,GAAG,CACP,QAA2D;QAE3D,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,MAAM,MAAM,GAAU,EAAE,CAAC;QACzB,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;YACpB,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;YAChC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAM,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;YAC3C,KAAK,EAAE,CAAC;QACV,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAqCG;IACH,KAAK,CAAC,OAAO,CACX,QAAiE;QAEjE,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,MAAM,MAAM,GAAU,EAAE,CAAC;QACzB,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;YACpB,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;YAChC,MAAM,IAAI,GAAG,QAAQ,CAAC,KAAM,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;YAC3C,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;gBACxB,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;YACvB,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpB,CAAC;YACD,KAAK,EAAE,CAAC;QACV,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAyGD,KAAK,CAAC,MAAM,CACV,OAA0E,EAC1E,YAAgB;QAEhB,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,CAAC,IAAI,CAAC,OAAO;YAAE,OAAO,YAAY,CAAC;QACvC,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;YAC/B,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,CAAQ,CAAC;YACzC,YAAY,GAAG,KAAU,CAAC;YAC1B,KAAK,IAAI,CAAC,CAAC;QACb,CAAC;QACD,IAAI,KAAK,GAAG,YAAY,CAAC;QACzB,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;YACpB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;YAC/B,KAAK,GAAG,OAAO,CAAC,KAAK,EAAE,IAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;YAC3C,KAAK,EAAE,CAAC;QACV,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;;;;;;;;;;;;;;;OAkBG;IACH,KAAK,CAAC,IAAI;QACR,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;IAC7B,CAAC;CACF;AAtZD,kCAsZC", "sourcesContent": ["/**\n * ```ts\n * import type { ArrayCursor, BatchedArrayCursor } from \"arangojs/cursor\";\n * ```\n *\n * The \"cursor\" module provides cursor-related interfaces for TypeScript.\n *\n * @packageDocumentation\n */\nimport { LinkedList } from \"x3-linkedlist\";\nimport { Database } from \"./database\";\n\n/**\n * Additional information about the cursor.\n */\nexport interface CursorExtras {\n  /**\n   * Warnings encountered while executing the query.\n   */\n  warnings: {\n    code: number;\n    message: string;\n  }[];\n  /**\n   * Query execution plan for the executed query.\n   */\n  plan?: Record<string, any>;\n  /**\n   * Additional profiling information for the executed query.\n   */\n  profile?: Record<string, number>;\n  /**\n   * Additional statistics about the query execution.\n   */\n  stats?: CursorStats;\n}\n\n/**\n * Additional statics about the query execution of the cursor.\n */\nexport interface CursorStats {\n  /**\n   * Total number of index entries read from in-memory caches for indexes of\n   * type edge or persistent.\n   */\n  cacheHits: number;\n  /**\n   * Total number of cache read attempts for index entries that could not be\n   * served from in-memory caches for indexes of type edge or persistent.\n   */\n  cacheMisses: number;\n  /**\n   * Total number of cursor objects created during query execution.\n   */\n  cursorsCreated: number;\n  /**\n   * Total number of times an existing cursor object was repurposed.\n   */\n  cursorsRearmed: number;\n  /**\n   * Total number of data-modification operations successfully executed.\n   */\n  writesExecuted: number;\n  /**\n   * Total number of data-modification operations that were unsuccessful, but have been ignored because of query option ignoreErrors.\n   */\n  writesIgnored: number;\n  /**\n   * Total number of documents iterated over when scanning a collection without an index.\n   */\n  scannedFull: number;\n  /**\n   * Total number of documents iterated over when scanning a collection using an index.\n   */\n  scannedIndex: number;\n  /**\n   * Total number of documents that were removed after executing a filter condition in a FilterNode.\n   */\n  filtered: number;\n  /**\n   * Maximum memory usage of the query while it was running.\n   */\n  peakMemoryUsage: number;\n  /**\n   * Execution time of the query in seconds.\n   */\n  executionTime: number;\n  /**\n   * Total number of documents that matched the search condition if the query’s final top-level LIMIT statement were not present.\n   */\n  fullCount?: number;\n  /**\n   * Total number of cluster-internal HTTP requests performed.\n   */\n  httpRequests: number;\n  /**\n   * Runtime statistics per query execution node if `profile` was set to `2` or greater.\n   */\n  nodes?: {\n    /**\n     * Execution node ID to correlate this node with nodes in the `extra.plan`.\n     */\n    id: number;\n    /**\n     * Number of calls in this node.\n     */\n    calls: number;\n    /**\n     * Number of temporary result items returned by this node.\n     */\n    items: number;\n    filter: number;\n    /**\n     * Execution time of this node in seconds.\n     */\n    runtime: number;\n  }[];\n}\n\ninterface BatchView<T = any> {\n  isEmpty: boolean;\n  more(): Promise<void>;\n  shift(): T | undefined;\n}\n\n/**\n * The `BatchedArrayCursor` provides a batch-wise API to an {@link ArrayCursor}.\n *\n * When using TypeScript, cursors can be cast to a specific item type in order\n * to increase type safety.\n *\n * @param T - Type to use for each item. Defaults to `any`.\n *\n * @example\n * ```ts\n * const db = new Database();\n * const query = aql`FOR x IN 1..5 RETURN x`;\n * const cursor = await db.query(query) as ArrayCursor<number>;\n * const batches = cursor.batches;\n * ```\n *\n * @example\n * ```js\n * const db = new Database();\n * const query = aql`FOR x IN 1..10000 RETURN x`;\n * const cursor = await db.query(query, { batchSize: 10 });\n * for await (const batch of cursor.batches) {\n *   // Process all values in a batch in parallel\n *   await Promise.all(batch.map(\n *     value => asyncProcessValue(value)\n *   ));\n * }\n * ```\n */\nexport class BatchedArrayCursor<T = any> {\n  protected _db: Database;\n  protected _batches: LinkedList<LinkedList<any>>;\n  protected _count?: number;\n  protected _extra: CursorExtras;\n  protected _hasMore: boolean;\n  protected _nextBatchId?: string;\n  protected _id: string | undefined;\n  protected _hostUrl?: string;\n  protected _allowDirtyRead?: boolean;\n  protected _itemsCursor: ArrayCursor<T>;\n\n  /**\n   * @internal\n   */\n  constructor(\n    db: Database,\n    body: {\n      extra: any;\n      result: T[];\n      hasMore: boolean;\n      nextBatchId?: string;\n      id: string;\n      count: number;\n    },\n    hostUrl?: string,\n    allowDirtyRead?: boolean\n  ) {\n    const batches = new LinkedList(\n      body.result.length ? [new LinkedList(body.result)] : []\n    );\n    this._db = db;\n    this._batches = batches;\n    this._id = body.id;\n    this._hasMore = Boolean(body.id && body.hasMore);\n    this._nextBatchId = body.nextBatchId;\n    this._hostUrl = hostUrl;\n    this._count = body.count;\n    this._extra = body.extra;\n    this._allowDirtyRead = allowDirtyRead;\n    this._itemsCursor = new ArrayCursor(this, {\n      get isEmpty() {\n        return !batches.length;\n      },\n      more: () => this._more(),\n      shift: () => {\n        let batch = batches.first?.value;\n        while (batch && !batch.length) {\n          batches.shift();\n          batch = batches.first?.value;\n        }\n        if (!batch) return undefined;\n        const value = batch.shift();\n        if (!batch.length) batches.shift();\n        return value;\n      },\n    });\n  }\n\n  protected async _more(): Promise<void> {\n    if (!this._id || !this.hasMore) return;\n    const body = await this._db.request({\n      method: \"POST\",\n      path: this._nextBatchId\n        ? `/_api/cursor/${encodeURIComponent(this._id)}/${this._nextBatchId}`\n        : `/_api/cursor/${encodeURIComponent(this._id)}`,\n      hostUrl: this._hostUrl,\n      allowDirtyRead: this._allowDirtyRead,\n    });\n    this._batches.push(new LinkedList(body.result));\n    this._hasMore = body.hasMore;\n    this._nextBatchId = body.nextBatchId;\n  }\n\n  /**\n   * An {@link ArrayCursor} providing item-wise access to the cursor result set.\n   *\n   * See also {@link ArrayCursor#batches}.\n   */\n  get items() {\n    return this._itemsCursor;\n  }\n\n  /**\n   * Additional information about the cursor.\n   */\n  get extra(): Readonly<CursorExtras> {\n    return this._extra;\n  }\n\n  /**\n   * Total number of documents in the query result. Only available if the\n   * `count` option was used.\n   */\n  get count(): number | undefined {\n    return this._count;\n  }\n\n  /**\n   * Whether the cursor has any remaining batches that haven't yet been\n   * fetched. If set to `false`, all batches have been fetched and no\n   * additional requests to the server will be made when consuming any\n   * remaining batches from this cursor.\n   */\n  get hasMore(): boolean {\n    return this._hasMore;\n  }\n\n  /**\n   * Whether the cursor has more batches. If set to `false`, the cursor has\n   * already been depleted and contains no more batches.\n   */\n  get hasNext(): boolean {\n    return this.hasMore || Boolean(this._batches.length);\n  }\n\n  /**\n   * Enables use with `for await` to deplete the cursor by asynchronously\n   * yielding every batch in the cursor's remaining result set.\n   *\n   * **Note**: If the result set spans multiple batches, any remaining batches\n   * will only be fetched on demand. Depending on the cursor's TTL and the\n   * processing speed, this may result in the server discarding the cursor\n   * before it is fully depleted.\n   *\n   * @example\n   * ```js\n   * const cursor = await db.query(aql`\n   *   FOR user IN users\n   *   FILTER user.isActive\n   *   RETURN user\n   * `);\n   * for await (const users of cursor.batches) {\n   *   for (const user of users) {\n   *     console.log(user.email, user.isAdmin);\n   *   }\n   * }\n   * ```\n   */\n  async *[Symbol.asyncIterator](): AsyncGenerator<T[], undefined, undefined> {\n    while (this.hasNext) {\n      yield this.next() as Promise<T[]>;\n    }\n    return undefined;\n  }\n\n  /**\n   * Loads all remaining batches from the server.\n   *\n   * **Warning**: This may impact memory use when working with very large\n   * query result sets.\n   *\n   * @example\n   * ```js\n   * const cursor = await db.query(\n   *   aql`FOR x IN 1..5 RETURN x`,\n   *   { batchSize: 1 }\n   * );\n   * console.log(cursor.hasMore); // true\n   * await cursor.batches.loadAll();\n   * console.log(cursor.hasMore); // false\n   * console.log(cursor.hasNext); // true\n   * for await (const item of cursor) {\n   *   console.log(item);\n   *   // No server roundtrips necessary any more\n   * }\n   * ```\n   */\n  async loadAll(): Promise<void> {\n    while (this._hasMore) {\n      await this._more();\n    }\n  }\n\n  /**\n   * Depletes the cursor, then returns an array containing all batches in the\n   * cursor's remaining result list.\n   *\n   * @example\n   * ```js\n   * const cursor = await db.query(\n   *   aql`FOR x IN 1..5 RETURN x`,\n   *   { batchSize: 2 }\n   * );\n   * const result = await cursor.batches.all(); // [[1, 2], [3, 4], [5]]\n   * console.log(cursor.hasNext); // false\n   * ```\n   */\n  async all(): Promise<T[][]> {\n    return this.map((batch) => batch);\n  }\n\n  /**\n   * Advances the cursor and returns all remaining values in the cursor's\n   * current batch. If the current batch has already been exhausted, fetches\n   * the next batch from the server and returns it, or `undefined` if the\n   * cursor has been depleted.\n   *\n   * **Note**: If the result set spans multiple batches, any remaining batches\n   * will only be fetched on demand. Depending on the cursor's TTL and the\n   * processing speed, this may result in the server discarding the cursor\n   * before it is fully depleted.\n   *\n   * @example\n   * ```js\n   * const cursor = await db.query(\n   *   aql`FOR i IN 1..10 RETURN i`,\n   *   { batchSize: 5 }\n   * );\n   * const firstBatch = await cursor.batches.next(); // [1, 2, 3, 4, 5]\n   * await cursor.next(); // 6\n   * const lastBatch = await cursor.batches.next(); // [7, 8, 9, 10]\n   * console.log(cursor.hasNext); // false\n   * ```\n   */\n  async next(): Promise<T[] | undefined> {\n    while (!this._batches.length && this.hasNext) {\n      await this._more();\n    }\n    if (!this._batches.length) {\n      return undefined;\n    }\n    const batch = this._batches.shift();\n    if (!batch) return undefined;\n    const values = [...batch.values()];\n    batch.clear(true);\n    return values;\n  }\n\n  /**\n   * Advances the cursor by applying the `callback` function to each item in\n   * the cursor's remaining result list until the cursor is depleted or\n   * `callback` returns the exact value `false`. Returns a promise that\n   * evalues to `true` unless the function returned `false`.\n   *\n   * **Note**: If the result set spans multiple batches, any remaining batches\n   * will only be fetched on demand. Depending on the cursor's TTL and the\n   * processing speed, this may result in the server discarding the cursor\n   * before it is fully depleted.\n   *\n   * See also:\n   * [`Array.prototype.forEach`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/forEach).\n   *\n   * @param callback - Function to execute on each element.\n   *\n   * @example\n   * ```js\n   * const cursor = await db.query(\n   *   aql`FOR x IN 1..5 RETURN x`,\n   *   { batchSize: 2 }\n   * );\n   * const result = await cursor.batches.forEach((currentBatch) => {\n   *   for (const value of currentBatch) {\n   *     console.log(value);\n   *   }\n   * });\n   * console.log(result) // true\n   * console.log(cursor.hasNext); // false\n   * ```\n   *\n   * @example\n   * ```js\n   * const cursor = await db.query(\n   *   aql`FOR x IN 1..5 RETURN x`,\n   *   { batchSize: 2 }\n   * );\n   * const result = await cursor.batches.forEach((currentBatch) => {\n   *   for (const value of currentBatch) {\n   *     console.log(value);\n   *   }\n   *   return false; // stop after the first batch\n   * });\n   * console.log(result); // false\n   * console.log(cursor.hasNext); // true\n   * ```\n   */\n  async forEach(\n    callback: (currentBatch: T[], index: number, self: this) => false | void\n  ): Promise<boolean> {\n    let index = 0;\n    while (this.hasNext) {\n      const currentBatch = await this.next();\n      const result = callback(currentBatch!, index, this);\n      index++;\n      if (result === false) return result;\n      if (this.hasNext) await this._more();\n    }\n    return true;\n  }\n\n  /**\n   * Depletes the cursor by applying the `callback` function to each batch in\n   * the cursor's remaining result list. Returns an array containing the\n   * return values of `callback` for each batch.\n   *\n   * **Note**: This creates an array of all return values, which may impact\n   * memory use when working with very large query result sets. Consider using\n   * {@link BatchedArrayCursor#forEach}, {@link BatchedArrayCursor#reduce} or\n   * {@link BatchedArrayCursor#flatMap} instead.\n   *\n   * See also:\n   * [`Array.prototype.map`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/map).\n   *\n   * @param R - Return type of the `callback` function.\n   * @param callback - Function to execute on each element.\n   *\n   * @example\n   * ```js\n   * const cursor = await db.query(\n   *   aql`FOR x IN 1..5 RETURN x`,\n   *   { batchSize: 2 }\n   * );\n   * const squares = await cursor.batches.map((currentBatch) => {\n   *   return currentBatch.map((value) => value ** 2);\n   * });\n   * console.log(squares); // [[1, 4], [9, 16], [25]]\n   * console.log(cursor.hasNext); // false\n   * ```\n   */\n  async map<R>(\n    callback: (currentBatch: T[], index: number, self: this) => R\n  ): Promise<R[]> {\n    let index = 0;\n    const result: any[] = [];\n    while (this.hasNext) {\n      const currentBatch = await this.next();\n      result.push(callback(currentBatch!, index, this));\n      index++;\n    }\n    return result;\n  }\n\n  /**\n   * Depletes the cursor by applying the `callback` function to each batch in\n   * the cursor's remaining result list. Returns an array containing the\n   * return values of `callback` for each batch, flattened to a depth of 1.\n   *\n   * **Note**: If the result set spans multiple batches, any remaining batches\n   * will only be fetched on demand. Depending on the cursor's TTL and the\n   * processing speed, this may result in the server discarding the cursor\n   * before it is fully depleted.\n   *\n   * See also:\n   * [`Array.prototype.flatMap`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/flatMap).\n   *\n   * @param R - Return type of the `callback` function.\n   * @param callback - Function to execute on each element.\n   *\n   * @example\n   * ```js\n   * const cursor = await db.query(\n   *   aql`FOR x IN 1..5 RETURN x`,\n   *   { batchSize: 2 }\n   * );\n   * const squares = await cursor.batches.flatMap((currentBatch) => {\n   *   return currentBatch.map((value) => value ** 2);\n   * });\n   * console.log(squares); // [1, 1, 2, 4, 3, 9, 4, 16, 5, 25]\n   * console.log(cursor.hasNext); // false\n   * ```\n   *\n   * @example\n   * ```js\n   * const cursor = await db.query(\n   *   aql`FOR x IN 1..5 RETURN x`,\n   *   { batchSize: 1 }\n   * );\n   * const odds = await cursor.batches.flatMap((currentBatch) => {\n   *   if (currentBatch[0] % 2 === 0) {\n   *     return []; // empty array flattens into nothing\n   *   }\n   *   return currentBatch;\n   * });\n   * console.logs(odds); // [1, 3, 5]\n   * ```\n   */\n  async flatMap<R>(\n    callback: (currentBatch: T[], index: number, self: this) => R | R[]\n  ): Promise<R[]> {\n    let index = 0;\n    const result: any[] = [];\n    while (this.hasNext) {\n      const currentBatch = await this.next();\n      const value = callback(currentBatch!, index, this);\n      if (Array.isArray(value)) {\n        result.push(...value);\n      } else {\n        result.push(value);\n      }\n      index++;\n    }\n    return result;\n  }\n\n  /**\n   * Depletes the cursor by applying the `reducer` function to each batch in\n   * the cursor's remaining result list. Returns the return value of `reducer`\n   * for the last batch.\n   *\n   * **Note**: Most complex uses of the `reduce` method can be replaced with\n   * simpler code using {@link BatchedArrayCursor#forEach} or the `for await`\n   * syntax.\n   *\n   * **Note**: If the result set spans multiple batches, any remaining batches\n   * will only be fetched on demand. Depending on the cursor's TTL and the\n   * processing speed, this may result in the server discarding the cursor\n   * before it is fully depleted.\n   *\n   * See also:\n   * [`Array.prototype.reduce`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/reduce).\n   *\n   * @param R - Return type of the `reducer` function.\n   * @param reducer - Function to execute on each element.\n   * @param initialValue - Initial value of the `accumulator` value passed to\n   * the `reducer` function.\n   *\n   * @example\n   * ```js\n   * function largestValue(baseline, values) {\n   *   return Math.max(baseline, ...values);\n   * }\n   * const cursor = await db.query(\n   *   aql`FOR x IN 1..5 RETURN x`,\n   *   { batchSize: 3 }\n   * );\n   * const result = await cursor.batches.reduce(largestValue, 0);\n   * console.log(result); // 5\n   * console.log(cursor.hasNext); // false\n   * const emptyResult = await cursor.batches.reduce(largestValue, 0);\n   * console.log(emptyResult); // 0\n   * ```\n   *\n   * @example\n   * ```js\n   * // BAD! NEEDLESSLY COMPLEX!\n   * const cursor = await db.query(\n   *   aql`FOR x IN 1..5 RETURN x`,\n   *   { batchSize: 1 }\n   * );\n   * const result = await cursor.reduce((accumulator, currentBatch) => {\n   *   if (currentBatch[0] % 2 === 0) {\n   *     accumulator.even.push(...currentBatch);\n   *   } else {\n   *     accumulator.odd.push(...currentBatch);\n   *   }\n   *   return accumulator;\n   * }, { odd: [], even: [] });\n   * console.log(result); // { odd: [1, 3, 5], even: [2, 4] }\n   *\n   * // GOOD! MUCH SIMPLER!\n   * const cursor = await db.query(aql`FOR x IN 1..5 RETURN x`);\n   * const odd = [];\n   * const even = [];\n   * for await (const currentBatch of cursor) {\n   *   if (currentBatch[0] % 2 === 0) {\n   *     even.push(...currentBatch);\n   *   } else {\n   *     odd.push(...currentBatch);\n   *   }\n   * }\n   * console.log({ odd, even }); // { odd: [1, 3, 5], even: [2, 4] }\n   * ```\n   */\n  async reduce<R>(\n    reducer: (\n      accumulator: R,\n      currentBatch: T[],\n      index: number,\n      self: this\n    ) => R,\n    initialValue: R\n  ): Promise<R>;\n\n  /**\n   * Depletes the cursor by applying the `reducer` function to each batch in\n   * the cursor's remaining result list. Returns the return value of `reducer`\n   * for the last batch.\n   *\n   * **Note**: If the result set spans multiple batches, any remaining batches\n   * will only be fetched on demand. Depending on the cursor's TTL and the\n   * processing speed, this may result in the server discarding the cursor\n   * before it is fully depleted.\n   *\n   * See also:\n   * [`Array.prototype.reduce`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/reduce).\n   *\n   * @param R - Return type of the `reducer` function.\n   * @param reducer - Function to execute on each element.\n   *\n   * @example\n   * ```js\n   * function largestValue(values1, values2) {\n   *   return [Math.max(...values1, ...values2)];\n   * }\n   * const cursor = await db.query(\n   *   aql`FOR x IN 1..5 RETURN x`,\n   *   { batchSize: 3 }\n   * );\n   * const result = await cursor.batches.reduce(largestValue);\n   * console.log(result); // [5]\n   * console.log(cursor.hasNext); // false\n   * ```\n   *\n   */\n  async reduce<R>(\n    reducer: (\n      accumulator: T[] | R,\n      currentBatch: T[],\n      index: number,\n      self: this\n    ) => R\n  ): Promise<R | undefined>;\n  async reduce<R>(\n    reducer: (\n      accumulator: R,\n      currentBatch: T[],\n      index: number,\n      self: this\n    ) => R,\n    initialValue?: R\n  ): Promise<R | undefined> {\n    let index = 0;\n    if (!this.hasNext) return initialValue;\n    if (initialValue === undefined) {\n      initialValue = (await this.next()) as any;\n      index += 1;\n    }\n    let value = initialValue as R;\n    while (this.hasNext) {\n      const currentBatch = await this.next();\n      value = reducer(value, currentBatch!, index, this);\n      index++;\n    }\n    return value;\n  }\n\n  /**\n   * Drains the cursor and frees up associated database resources.\n   *\n   * This method has no effect if all batches have already been consumed.\n   *\n   * @example\n   * ```js\n   * const cursor1 = await db.query(aql`FOR x IN 1..5 RETURN x`);\n   * console.log(cursor1.hasMore); // false\n   * await cursor1.kill(); // no effect\n   *\n   * const cursor2 = await db.query(\n   *   aql`FOR x IN 1..5 RETURN x`,\n   *   { batchSize: 2 }\n   * );\n   * console.log(cursor2.hasMore); // true\n   * await cursor2.kill(); // cursor is depleted\n   * ```\n   */\n  async kill(): Promise<void> {\n    if (this._batches.length) {\n      for (const batch of this._batches.values()) {\n        batch.clear();\n      }\n      this._batches.clear();\n    }\n    if (!this.hasNext) return undefined;\n    return this._db.request(\n      {\n        method: \"DELETE\",\n        path: `/_api/cursor/${encodeURIComponent(this._id!)}`,\n      },\n      () => {\n        this._hasMore = false;\n        return undefined;\n      }\n    );\n  }\n}\n\n/**\n * The `ArrayCursor` type represents a cursor returned from a\n * {@link database.Database#query}.\n *\n * When using TypeScript, cursors can be cast to a specific item type in order\n * to increase type safety.\n *\n * See also {@link BatchedArrayCursor}.\n *\n * @param T - Type to use for each item. Defaults to `any`.\n *\n * @example\n * ```ts\n * const db = new Database();\n * const query = aql`FOR x IN 1..5 RETURN x`;\n * const result = await db.query(query) as ArrayCursor<number>;\n * ```\n *\n * @example\n * ```js\n * const db = new Database();\n * const query = aql`FOR x IN 1..10 RETURN x`;\n * const cursor = await db.query(query);\n * for await (const value of cursor) {\n *   // Process each value asynchronously\n *   await processValue(value);\n * }\n * ```\n */\nexport class ArrayCursor<T = any> {\n  protected _batches: BatchedArrayCursor<T>;\n  protected _view: BatchView<T>;\n\n  /**\n   * @internal\n   */\n  constructor(batchedCursor: BatchedArrayCursor, view: BatchView<T>) {\n    this._batches = batchedCursor;\n    this._view = view;\n  }\n\n  /**\n   * A {@link BatchedArrayCursor} providing batch-wise access to the cursor\n   * result set.\n   *\n   * See also {@link BatchedArrayCursor#items}.\n   */\n  get batches() {\n    return this._batches;\n  }\n\n  /**\n   * Additional information about the cursor.\n   */\n  get extra(): CursorExtras {\n    return this.batches.extra;\n  }\n\n  /**\n   * Total number of documents in the query result. Only available if the\n   * `count` option was used.\n   */\n  get count(): number | undefined {\n    return this.batches.count;\n  }\n\n  /**\n   * Whether the cursor has more values. If set to `false`, the cursor has\n   * already been depleted and contains no more items.\n   */\n  get hasNext(): boolean {\n    return this.batches.hasNext;\n  }\n\n  /**\n   * Enables use with `for await` to deplete the cursor by asynchronously\n   * yielding every value in the cursor's remaining result set.\n   *\n   * **Note**: If the result set spans multiple batches, any remaining batches\n   * will only be fetched on demand. Depending on the cursor's TTL and the\n   * processing speed, this may result in the server discarding the cursor\n   * before it is fully depleted.\n   *\n   * @example\n   * ```js\n   * const cursor = await db.query(aql`\n   *   FOR user IN users\n   *   FILTER user.isActive\n   *   RETURN user\n   * `);\n   * for await (const user of cursor) {\n   *   console.log(user.email, user.isAdmin);\n   * }\n   * ```\n   */\n  async *[Symbol.asyncIterator](): AsyncGenerator<T, undefined, undefined> {\n    while (this.hasNext) {\n      yield this.next() as Promise<T>;\n    }\n    return undefined;\n  }\n\n  /**\n   * Depletes the cursor, then returns an array containing all values in the\n   * cursor's remaining result list.\n   *\n   * @example\n   * ```js\n   * const cursor = await db.query(aql`FOR x IN 1..5 RETURN x`);\n   * const result = await cursor.all(); // [1, 2, 3, 4, 5]\n   * console.log(cursor.hasNext); // false\n   * ```\n   */\n  async all(): Promise<T[]> {\n    return this.batches.flatMap((v) => v);\n  }\n\n  /**\n   * Advances the cursor and returns the next value in the cursor's remaining\n   * result list, or `undefined` if the cursor has been depleted.\n   *\n   * **Note**: If the result set spans multiple batches, any remaining batches\n   * will only be fetched on demand. Depending on the cursor's TTL and the\n   * processing speed, this may result in the server discarding the cursor\n   * before it is fully depleted.\n   *\n   * @example\n   * ```js\n   * const cursor = await db.query(aql`FOR x IN 1..3 RETURN x`);\n   * const one = await cursor.next(); // 1\n   * const two = await cursor.next(); // 2\n   * const three = await cursor.next(); // 3\n   * const empty = await cursor.next(); // undefined\n   * ```\n   */\n  async next(): Promise<T | undefined> {\n    while (this._view.isEmpty && this.batches.hasMore) {\n      await this._view.more();\n    }\n    if (this._view.isEmpty) {\n      return undefined;\n    }\n    return this._view.shift();\n  }\n\n  /**\n   * Advances the cursor by applying the `callback` function to each item in\n   * the cursor's remaining result list until the cursor is depleted or\n   * `callback` returns the exact value `false`. Returns a promise that\n   * evalues to `true` unless the function returned `false`.\n   *\n   * **Note**: If the result set spans multiple batches, any remaining batches\n   * will only be fetched on demand. Depending on the cursor's TTL and the\n   * processing speed, this may result in the server discarding the cursor\n   * before it is fully depleted.\n   *\n   * See also:\n   * [`Array.prototype.forEach`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/forEach).\n   *\n   * @param callback - Function to execute on each element.\n   *\n   * @example\n   * ```js\n   * const cursor = await db.query(aql`FOR x IN 1..5 RETURN x`);\n   * const result = await cursor.forEach((currentValue) => {\n   *   console.log(currentValue);\n   * });\n   * console.log(result) // true\n   * console.log(cursor.hasNext); // false\n   * ```\n   *\n   * @example\n   * ```js\n   * const cursor = await db.query(aql`FOR x IN 1..5 RETURN x`);\n   * const result = await cursor.forEach((currentValue) => {\n   *   console.log(currentValue);\n   *   return false; // stop after the first item\n   * });\n   * console.log(result); // false\n   * console.log(cursor.hasNext); // true\n   * ```\n   */\n  async forEach(\n    callback: (currentValue: T, index: number, self: this) => false | void\n  ): Promise<boolean> {\n    let index = 0;\n    while (this.hasNext) {\n      const value = await this.next();\n      const result = callback(value!, index, this);\n      index++;\n      if (result === false) return result;\n    }\n    return true;\n  }\n\n  /**\n   * Depletes the cursor by applying the `callback` function to each item in\n   * the cursor's remaining result list. Returns an array containing the\n   * return values of `callback` for each item.\n   *\n   * **Note**: This creates an array of all return values, which may impact\n   * memory use when working with very large query result sets. Consider using\n   * {@link ArrayCursor#forEach}, {@link ArrayCursor#reduce} or\n   * {@link ArrayCursor#flatMap} instead.\n   *\n   * See also:\n   * [`Array.prototype.map`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/map).\n   *\n   * @param R - Return type of the `callback` function.\n   * @param callback - Function to execute on each element.\n   *\n   * @example\n   * ```js\n   * const cursor = await db.query(aql`FOR x IN 1..5 RETURN x`);\n   * const squares = await cursor.map((currentValue) => {\n   *   return currentValue ** 2;\n   * });\n   * console.log(squares); // [1, 4, 9, 16, 25]\n   * console.log(cursor.hasNext); // false\n   * ```\n   */\n  async map<R>(\n    callback: (currentValue: T, index: number, self: this) => R\n  ): Promise<R[]> {\n    let index = 0;\n    const result: any[] = [];\n    while (this.hasNext) {\n      const value = await this.next();\n      result.push(callback(value!, index, this));\n      index++;\n    }\n    return result;\n  }\n\n  /**\n   * Depletes the cursor by applying the `callback` function to each item in\n   * the cursor's remaining result list. Returns an array containing the\n   * return values of `callback` for each item, flattened to a depth of 1.\n   *\n   * **Note**: If the result set spans multiple batches, any remaining batches\n   * will only be fetched on demand. Depending on the cursor's TTL and the\n   * processing speed, this may result in the server discarding the cursor\n   * before it is fully depleted.\n   *\n   * See also:\n   * [`Array.prototype.flatMap`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/flatMap).\n   *\n   * @param R - Return type of the `callback` function.\n   * @param callback - Function to execute on each element.\n   *\n   * @example\n   * ```js\n   * const cursor = await db.query(aql`FOR x IN 1..5 RETURN x`);\n   * const squares = await cursor.flatMap((currentValue) => {\n   *   return [currentValue, currentValue ** 2];\n   * });\n   * console.log(squares); // [1, 1, 2, 4, 3, 9, 4, 16, 5, 25]\n   * console.log(cursor.hasNext); // false\n   * ```\n   *\n   * @example\n   * ```js\n   * const cursor = await db.query(aql`FOR x IN 1..5 RETURN x`);\n   * const odds = await cursor.flatMap((currentValue) => {\n   *   if (currentValue % 2 === 0) {\n   *     return []; // empty array flattens into nothing\n   *   }\n   *   return currentValue; // or [currentValue]\n   * });\n   * console.logs(odds); // [1, 3, 5]\n   * ```\n   */\n  async flatMap<R>(\n    callback: (currentValue: T, index: number, self: this) => R | R[]\n  ): Promise<R[]> {\n    let index = 0;\n    const result: any[] = [];\n    while (this.hasNext) {\n      const value = await this.next();\n      const item = callback(value!, index, this);\n      if (Array.isArray(item)) {\n        result.push(...item);\n      } else {\n        result.push(item);\n      }\n      index++;\n    }\n    return result;\n  }\n\n  /**\n   * Depletes the cursor by applying the `reducer` function to each item in\n   * the cursor's remaining result list. Returns the return value of `reducer`\n   * for the last item.\n   *\n   * **Note**: Most complex uses of the `reduce` method can be replaced with\n   * simpler code using {@link ArrayCursor#forEach} or the `for await` syntax.\n   *\n   * **Note**: If the result set spans multiple batches, any remaining batches\n   * will only be fetched on demand. Depending on the cursor's TTL and the\n   * processing speed, this may result in the server discarding the cursor\n   * before it is fully depleted.\n   *\n   * See also:\n   * [`Array.prototype.reduce`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/reduce).\n   *\n   * @param R - Return type of the `reducer` function.\n   * @param reducer - Function to execute on each element.\n   * @param initialValue - Initial value of the `accumulator` value passed to\n   * the `reducer` function.\n   *\n   * @example\n   * ```js\n   * function largestOfTwo(one, two) {\n   *   return Math.max(one, two);\n   * }\n   * const cursor = await db.query(aql`FOR x IN 1..5 RETURN x`);\n   * const result = await cursor.reduce(largestOfTwo, 0);\n   * console.log(result); // 5\n   * console.log(cursor.hasNext); // false\n   * const emptyResult = await cursor.reduce(largestOfTwo, 0);\n   * console.log(emptyResult); // 0\n   * ```\n   *\n   * @example\n   * ```js\n   * // BAD! NEEDLESSLY COMPLEX!\n   * const cursor = await db.query(aql`FOR x IN 1..5 RETURN x`);\n   * const result = await cursor.reduce((accumulator, currentValue) => {\n   *   if (currentValue % 2 === 0) {\n   *     accumulator.even.push(...currentValue);\n   *   } else {\n   *     accumulator.odd.push(...currentValue);\n   *   }\n   *   return accumulator;\n   * }, { odd: [], even: [] });\n   * console.log(result); // { odd: [1, 3, 5], even: [2, 4] }\n   *\n   * // GOOD! MUCH SIMPLER!\n   * const cursor = await db.query(aql`FOR x IN 1..5 RETURN x`);\n   * const odd = [];\n   * const even = [];\n   * for await (const currentValue of cursor) {\n   *   if (currentValue % 2 === 0) {\n   *     even.push(currentValue);\n   *   } else {\n   *     odd.push(currentValue);\n   *   }\n   * }\n   * console.log({ odd, even }); // { odd: [1, 3, 5], even: [2, 4] }\n   * ```\n   */\n  async reduce<R>(\n    reducer: (accumulator: R, currentValue: T, index: number, self: this) => R,\n    initialValue: R\n  ): Promise<R>;\n  /**\n   * Depletes the cursor by applying the `reducer` function to each item in\n   * the cursor's remaining result list. Returns the return value of `reducer`\n   * for the last item.\n   *\n   * **Note**: If the result set spans multiple batches, any remaining batches\n   * will only be fetched on demand. Depending on the cursor's TTL and the\n   * processing speed, this may result in the server discarding the cursor\n   * before it is fully depleted.\n   *\n   * See also:\n   * [`Array.prototype.reduce`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/reduce).\n   *\n   * @param R - Return type of the `reducer` function.\n   * @param reducer - Function to execute on each element.\n   *\n   * @example\n   * ```js\n   * function largestOfTwo(one, two) {\n   *   return Math.max(one, two);\n   * }\n   * const cursor = await db.query(aql`FOR x IN 1..5 RETURN x`);\n   * const result = await cursor.reduce(largestOfTwo);\n   * console.log(result); // 5\n   * console.log(cursor.hasNext); // false\n   * const emptyResult = await cursor.reduce(largestOfTwo);\n   * console.log(emptyResult); // undefined\n   * ```\n   */\n  async reduce<R>(\n    reducer: (\n      accumulator: T | R,\n      currentValue: T,\n      index: number,\n      self: this\n    ) => R\n  ): Promise<R | undefined>;\n  async reduce<R>(\n    reducer: (accumulator: R, currentValue: T, index: number, self: this) => R,\n    initialValue?: R\n  ): Promise<R | undefined> {\n    let index = 0;\n    if (!this.hasNext) return initialValue;\n    if (initialValue === undefined) {\n      const value = (await this.next()) as any;\n      initialValue = value as R;\n      index += 1;\n    }\n    let value = initialValue;\n    while (this.hasNext) {\n      const item = await this.next();\n      value = reducer(value, item!, index, this);\n      index++;\n    }\n    return value;\n  }\n\n  /**\n   * Kills the cursor and frees up associated database resources.\n   *\n   * This method has no effect if all batches have already been fetched.\n   *\n   * @example\n   * ```js\n   * const cursor1 = await db.query(aql`FOR x IN 1..5 RETURN x`);\n   * console.log(cursor1.hasMore); // false\n   * await cursor1.kill(); // no effect\n   *\n   * const cursor2 = await db.query(\n   *   aql`FOR x IN 1..5 RETURN x`,\n   *   { batchSize: 2 }\n   * );\n   * console.log(cursor2.hasMore); // true\n   * await cursor2.kill(); // cursor is depleted\n   * ```\n   */\n  async kill(): Promise<void> {\n    return this.batches.kill();\n  }\n}\n"]}