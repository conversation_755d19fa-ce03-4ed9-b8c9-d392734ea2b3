{"version": 3, "file": "database.js", "sourceRoot": "", "sources": ["../src/database.ts"], "names": [], "mappings": ";;;AAaA,yCAIoB;AACpB,+BAAuE;AACvE,6CAUsB;AACtB,6CAMsB;AACtB,qCAA2D;AAC3D,mCAAwC;AAExC,mCAKiB;AACjB,+BAA4B;AAE5B,uCAAiD;AACjD,+CAAyC;AAEzC,mCAAgC;AAChC,+CAA4C;AAC5C,iCAAkE;AAElE;;;;GAIG;AACH,SAAgB,gBAAgB,CAAC,QAAa;IAC5C,OAAO,OAAO,CAAC,QAAQ,IAAI,QAAQ,CAAC,gBAAgB,CAAC,CAAC;AACxD,CAAC;AAFD,4CAEC;AAED;;GAEG;AACH,SAAS,4BAA4B,CACnC,WAIoB;IAEpB,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE,CAAC;QACpC,OAAO,EAAE,KAAK,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC;IAClC,CAAC;IACD,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;QAC/B,OAAO,EAAE,KAAK,EAAE,WAAW,CAAC,GAAG,CAAC,+BAAkB,CAAC,EAAE,CAAC;IACxD,CAAC;IACD,IAAI,IAAA,+BAAkB,EAAC,WAAW,CAAC,EAAE,CAAC;QACpC,OAAO,EAAE,KAAK,EAAE,IAAA,+BAAkB,EAAC,WAAW,CAAC,EAAE,CAAC;IACpD,CAAC;IACD,MAAM,IAAI,GAAkC,EAAE,CAAC;IAC/C,IAAI,WAAW,EAAE,CAAC;QAChB,IAAI,WAAW,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YAC5C,IAAI,CAAC,aAAa,GAAG,WAAW,CAAC,aAAa,CAAC;QACjD,CAAC;QACD,IAAI,WAAW,CAAC,IAAI,EAAE,CAAC;YACrB,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC;gBACzC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,+BAAkB,CAAC;gBAC1C,CAAC,CAAC,IAAA,+BAAkB,EAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAC3C,CAAC;QACD,IAAI,WAAW,CAAC,KAAK,EAAE,CAAC;YACtB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC;gBAC3C,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,+BAAkB,CAAC;gBAC3C,CAAC,CAAC,IAAA,+BAAkB,EAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC5C,CAAC;QACD,IAAI,WAAW,CAAC,SAAS,EAAE,CAAC;YAC1B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC;gBACnD,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,+BAAkB,CAAC;gBAC/C,CAAC,CAAC,IAAA,+BAAkB,EAAC,WAAW,CAAC,SAAS,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AA2hDD;;GAEG;AACH,IAAY,QAMX;AAND,WAAY,QAAQ;IAClB,yCAAK,CAAA;IACL,yCAAK,CAAA;IACL,6CAAO,CAAA;IACP,uCAAI,CAAA;IACJ,yCAAK,CAAA;AACP,CAAC,EANW,QAAQ,wBAAR,QAAQ,QAMnB;AA2FD;;;GAGG;AACH,MAAa,QAAQ;IA6CnB,YACE,mBAA0D,EAAE,EAC5D,IAAa;QA5CL,eAAU,GAAG,IAAI,GAAG,EAAoB,CAAC;QACzC,iBAAY,GAAG,IAAI,GAAG,EAAsB,CAAC;QAC7C,YAAO,GAAG,IAAI,GAAG,EAAiB,CAAC;QACnC,WAAM,GAAG,IAAI,GAAG,EAAgB,CAAC;QA2CzC,IAAI,gBAAgB,CAAC,gBAAgB,CAAC,EAAE,CAAC;YACvC,MAAM,UAAU,GAAG,gBAAgB,CAAC,WAAW,CAAC;YAChD,MAAM,YAAY,GAAG,CAAC,IAAI,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACtE,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;YAC9B,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC;YAC1B,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YACnD,IAAI,QAAQ;gBAAE,OAAO,QAAQ,CAAC;QAChC,CAAC;aAAM,CAAC;YACN,MAAM,MAAM,GAAG,gBAAgB,CAAC;YAChC,MAAM,EAAE,YAAY,EAAE,GAAG,OAAO,EAAE,GAChC,OAAO,MAAM,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;gBACjD,CAAC,CAAC,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE;gBACrC,CAAC,CAAC,MAAM,CAAC;YACb,IAAI,CAAC,WAAW,GAAG,IAAI,uBAAU,CAAC,OAAO,CAAC,CAAC;YAC3C,IAAI,CAAC,KAAK,GAAG,YAAY,EAAE,SAAS,CAAC,KAAK,CAAC,IAAI,SAAS,CAAC;QAC3D,CAAC;IACH,CAAC;IAED,cAAc;IACd;;;;OAIG;IACH,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACH,OAAO,CAAC,OAAiB;QACvB,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,eAAe;YACrB,EAAE,EAAE,EAAE,OAAO,EAAE;SAChB,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,IAAI;QACF,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,IAAI,EAAE,cAAc;SACrB,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAC9B,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,KAAK,CAAC,IAAa,EAAE,OAAiB;QACpC,OAAO,IAAI,aAAK,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IACxC,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,KAAK,CAAC,SAAS,CAAI,QAA0B;QAC3C,MAAM,IAAI,GAAG,IAAI,OAAO,CAAgC,CAAC,WAAW,EAAE,EAAE;YACtE,IAAI,CAAC,YAAY,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QACH,MAAM,cAAc,GAAG,QAAQ,EAAE,CAAC;QAClC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC;QAC3B,IAAI,OAAO,CAAC,KAAK;YAAE,OAAO,cAA8B,CAAC;QACzD,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAyB,CAAC;QACjE,OAAO,IAAI,SAAG,CACZ,IAAI,EACJ,KAAK,EACL,CAAC,GAAG,EAAE,EAAE;YACN,SAAS,CAAC,GAAG,CAAC,CAAC;YACf,OAAO,cAAc,CAAC;QACxB,CAAC,EACD,CAAC,CAAC,EAAE,EAAE;YACJ,QAAQ,CAAC,CAAC,CAAC,CAAC;YACZ,OAAO,cAAc,CAAC;QACxB,CAAC,CACF,CAAC;IACJ,CAAC;IAmCD,KAAK,CAAC,OAAO,CACX,EACE,YAAY,GAAG,KAAK,EACpB,QAAQ,EACR,GAAG,IAAI,EACqC,EAC9C,YAAoD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI;QAErE,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,QAAQ,GAAG,QAAQ,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,QAAQ,IAAI,EAAE,EAAE,CAAC;QACvE,CAAC;QACD,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC;YAC/B,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;YAC9B,OAAO,IAAI,OAAO,CAAI,KAAK,EAAE,cAAc,EAAE,aAAa,EAAE,EAAE;gBAC5D,MAAM,OAAO,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;gBAC5B,OAAO,CAAC,OAAO,GAAG,EAAE,GAAG,OAAO,CAAC,OAAO,EAAE,gBAAgB,EAAE,OAAO,EAAE,CAAC;gBACpE,IAAI,MAAwB,CAAC;gBAC7B,IAAI,CAAC;oBACH,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,CAAC,CAAC;gBACpE,CAAC;gBAAC,OAAO,CAAC,EAAE,CAAC;oBACX,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;oBACtB,aAAa,CAAC,CAAC,CAAC,CAAC;oBACjB,OAAO;gBACT,CAAC;gBACD,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAW,CAAC;gBAC5D,IAAI,CAAC;oBACH,KAAK;oBACL,SAAS,EAAE,CAAC,GAAG,EAAE,EAAE;wBACjB,MAAM,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE,GAAS,CAAC;wBACvD,cAAc,CAAC,MAAM,CAAC,CAAC;wBACvB,OAAO,MAAM,CAAC;oBAChB,CAAC;oBACD,QAAQ,EAAE,CAAC,GAAG,EAAE,EAAE;wBAChB,aAAa,CAAC,GAAG,CAAC,CAAC;wBACnB,MAAM,GAAG,CAAC;oBACZ,CAAC;iBACF,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAC7B,EAAE,QAAQ,EAAE,GAAG,IAAI,EAAE,EACrB,SAAS,IAAI,SAAS,CACvB,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;OAyBG;IACH,KAAK,CAAC,eAAe,CAAC,SAAS,GAAG,KAAK;QACrC,MAAM,IAAI,GAAa,MAAM,IAAI,CAAC,OAAO,CACvC,EAAE,IAAI,EAAE,yBAAyB,EAAE,EACnC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,QAAa,EAAE,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CACtE,CAAC;QACF,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpB,IAAI,SAAS;gBAAE,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;;gBAC7C,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACH,KAAK;QACH,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,MAAM,EAAE,QAAQ;YAChB,IAAI,EAAE,kBAAkB;SACzB,EACD,GAAG,EAAE,CAAC,SAAS,CAChB,CAAC;IACJ,CAAC;IA+BD,KAAK,CAAC,kBAAkB,CACtB,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAkB,EACxC,OAAgB;QAEhB,MAAM,IAAI,CAAC,WAAW,CAAC,kBAAkB,CACvC;YACE,GAAG,OAAO;YACV,QAAQ,EAAE,QAAQ,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,QAAQ,IAAI,EAAE,EAAE;SACpE,EACD,OAAO,CACR,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC;IACpC,CAAC;IAED;;;;;;OAMG;IACH,2BAA2B,CAAC,wBAAgC;QAC1D,IAAI,CAAC,WAAW,CAAC,2BAA2B,CAAC,wBAAwB,CAAC,CAAC;IACzE,CAAC;IAED,YAAY;IAEZ,cAAc;IACd;;;;;;;;;;;;;;OAcG;IACH,YAAY,CAAC,WAAmB,MAAM,EAAE,WAAmB,EAAE;QAC3D,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAC;QACtD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,aAAa,CAAC,KAAa;QACzB,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;QAC1C,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACH,KAAK,CAAC,WAAmB,MAAM,EAAE,WAAmB,EAAE;QACpD,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,aAAa;YACnB,IAAI,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE;SAC7B,EACD,CAAC,GAAG,EAAE,EAAE;YACN,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACjC,OAAO,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;QACtB,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,mBAAmB;SAC1B,EACD,CAAC,GAAG,EAAE,EAAE;YACN,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG;gBAAE,OAAO,IAAI,CAAC;YAC/B,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACjC,OAAO,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;QACtB,CAAC,CACF,CAAC;IACJ,CAAC;IACD,YAAY;IAEZ,qBAAqB;IACrB;;;;;;;;OAQG;IACH,mBAAmB;QACjB,OAAO,IAAI,CAAC,OAAO,CACjB,EAAE,IAAI,EAAE,2BAA2B,EAAE,EACrC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CACzB,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACH,uBAAuB,CACrB,IAA6B;QAE7B,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,2BAA2B;YACjC,IAAI,EAAE;gBACJ,OAAO,EAAE,CAAC;gBACV,GAAG,IAAI;aACR;SACF,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CACzB,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACH,uBAAuB,CAAC,KAA6B;QACnD,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,mCAAmC;YACzC,IAAI,EAAE;gBACJ,OAAO,EAAE,CAAC;gBACV,KAAK;aACN;SACF,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,gBAAgB,CACd,IAA6B;QAE7B,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,2BAA2B;YACjC,IAAI,EAAE;gBACJ,OAAO,EAAE,CAAC;gBACV,GAAG,IAAI;aACR;SACF,CAAC,CAAC;IACL,CAAC;IACD,YAAY;IAEZ,mBAAmB;IACnB;;;;;;;;;;;;;OAaG;IACH,QAAQ,CAAC,YAAoB;QAC3B,OAAO,IAAI,QAAQ,CAAC,IAAW,EAAE,YAAY,CAAC,CAAC;IACjD,CAAC;IAED;;;;;;;;;OASG;IACH,GAAG;QACD,OAAO,IAAI,CAAC,OAAO,CACjB,EAAE,IAAI,EAAE,wBAAwB,EAAE,EAClC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CACzB,CAAC;IACJ,CAAC;IAED;;;;;;;;;OASG;IACH,KAAK,CAAC,MAAM;QACV,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,GAAG,EAAE,CAAC;YACjB,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAClB,IAAI,IAAA,qBAAa,EAAC,GAAG,CAAC,IAAI,GAAG,CAAC,QAAQ,KAAK,0BAAkB,EAAE,CAAC;gBAC9D,OAAO,KAAK,CAAC;YACf,CAAC;YACD,MAAM,GAAG,CAAC;QACZ,CAAC;IACH,CAAC;IAwCD,cAAc,CACZ,YAAoB,EACpB,iBAA+D,EAAE;QAEjE,MAAM,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,GAAG,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC;YACzD,CAAC,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE;YAC3B,CAAC,CAAC,cAAc,CAAC;QACnB,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,gBAAgB;YACtB,IAAI,EAAE,EAAE,IAAI,EAAE,YAAY,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE;SAC9D,EACD,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAClC,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,aAAa;QACX,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC5E,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,iBAAiB;QACf,OAAO,IAAI,CAAC,OAAO,CACjB,EAAE,IAAI,EAAE,qBAAqB,EAAE,EAC/B,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CACzB,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,SAAS;QACP,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CACrD,GAAG,CAAC,IAAI,CAAC,MAAmB,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,EAAE,CACjD,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAC5B,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,aAAa;QACX,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,qBAAqB,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAC1D,GAAG,CAAC,IAAI,CAAC,MAAmB,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,EAAE,CACjD,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAC5B,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;OAWG;IACH,YAAY,CAAC,YAAoB;QAC/B,YAAY,GAAG,YAAY,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAC7C,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,MAAM,EAAE,QAAQ;YAChB,IAAI,EAAE,kBAAkB,kBAAkB,CAAC,YAAY,CAAC,EAAE;SAC3D,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CACzB,CAAC;IACJ,CAAC;IACD,YAAY;IAEZ,qBAAqB;IACrB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAsCG;IACH,UAAU,CACR,cAAsB;QAEtB,cAAc,GAAG,cAAc,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACjD,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC;YAC3C,IAAI,CAAC,YAAY,CAAC,GAAG,CACnB,cAAc,EACd,IAAI,uBAAU,CAAC,IAAI,EAAE,cAAc,CAAC,CACrC,CAAC;QACJ,CAAC;QACD,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,cAAc,CAAE,CAAC;IAChD,CAAC;IAkED,KAAK,CAAC,gBAAgB,CACpB,cAAsB,EACtB,OAA6D;QAE7D,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;QACnD,MAAM,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACjC,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;OA2BG;IACH,KAAK,CAAC,oBAAoB,CACxB,cAAsB,EACtB,OAAiC;QAEjC,OAAO,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE;YAC3C,GAAG,OAAO;YACV,IAAI,EAAE,2BAAc,CAAC,eAAe;SACrC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;OAWG;IACH,KAAK,CAAC,gBAAgB,CACpB,cAAsB,EACtB,OAAe;QAEf,cAAc,GAAG,cAAc,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACjD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC;YAChC,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,oBAAoB,kBAAkB,CAAC,cAAc,CAAC,SAAS;YACrE,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;SACzC,CAAC,CAAC;QACH,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;QACzC,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;OAuBG;IACH,eAAe,CACb,gBAAyB,IAAI;QAE7B,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,IAAI,EAAE,kBAAkB;YACxB,EAAE,EAAE,EAAE,aAAa,EAAE;SACtB,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CACzB,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;OA2BG;IACH,KAAK,CAAC,WAAW,CACf,gBAAyB,IAAI;QAE7B,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;QAC9D,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC/D,CAAC;IACD,YAAY;IAEZ,gBAAgB;IAChB;;;;;;;;;;;OAWG;IACH,KAAK,CAAC,SAAiB;QACrB,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACvC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YACjC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,aAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;QAC1D,CAAC;QACD,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAE,CAAC;IACtC,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,WAAW,CACf,SAAiB,EACjB,eAAwC,EACxC,OAA4B;QAE5B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;QACrD,MAAM,KAAK,CAAC,MAAM,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;QAC7C,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,UAAU;QACR,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC3E,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,KAAK,CAAC,MAAM;QACV,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QACvC,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC1D,CAAC;IACD,YAAY;IAEZ,eAAe;IACf;;;;;;;;;;OAUG;IACH,IAAI,CAAC,QAAgB;QACnB,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACrC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC/B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,WAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;QACtD,CAAC;QACD,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC;IACpC,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,KAAK,CAAC,UAAU,CACd,QAAgB,EAChB,OAA0B;QAE1B,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;QAClD,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC3B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;;;;;;OAWG;IACH,KAAK,CAAC,UAAU,CACd,QAAgB,EAChB,OAAe;QAEf,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACrC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC;YAChC,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,cAAc,kBAAkB,CAAC,QAAQ,CAAC,SAAS;YACzD,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;SACzC,CAAC,CAAC;QACH,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC7B,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,SAAS;QACP,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACxE,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,KAAK,CAAC,KAAK;QACT,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;QACrC,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACnD,CAAC;IACD,YAAY;IAEZ,mBAAmB;IACnB;;;;;;;;;;OAUG;IACH,QAAQ,CAAC,YAAoB;QAC3B,YAAY,GAAG,YAAY,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAC7C,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC;YACvC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,mBAAQ,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC;QACtE,CAAC;QACD,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,YAAY,CAAE,CAAC;IAC5C,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,KAAK,CAAC,cAAc,CAClB,YAAoB,EACpB,OAA8B;QAE9B,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QAC7C,MAAM,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC/B,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,aAAa;QACX,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC5E,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,KAAK,CAAC,SAAS;QACb,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;QAC7C,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3D,CAAC;IACD,YAAY;IAEZ;;;;;;;;;;OAUG;IACH,SAAS;QACP,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,IAAI,EAAE,YAAY;SACnB,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CACzB,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;OAWG;IACH,OAAO,CAAC,QAAgB;QACtB,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,IAAI,EAAE,cAAc,kBAAkB,CAAC,QAAQ,CAAC,EAAE;SACnD,CAAC,CAAC;IACL,CAAC;IAoCD,UAAU,CACR,QAAgB,EAChB,OAA6B;QAE7B,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;YAChC,OAAO,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC;QAChC,CAAC;QACD,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,YAAY;YAClB,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;SACrC,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAClB,CAAC;IACJ,CAAC;IAoCD,UAAU,CACR,QAAgB,EAChB,OAAsC;QAEtC,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;YAChC,OAAO,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC;QAChC,CAAC;QACD,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,MAAM,EAAE,OAAO;YACf,IAAI,EAAE,cAAc,kBAAkB,CAAC,QAAQ,CAAC,EAAE;YAClD,IAAI,EAAE,OAAO;SACd,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAClB,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,WAAW,CACT,QAAgB,EAChB,OAAoB;QAEpB,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;YAChC,OAAO,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC;QAChC,CAAC;QACD,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,cAAc,kBAAkB,CAAC,QAAQ,CAAC,EAAE;YAClD,IAAI,EAAE,OAAO;SACd,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAClB,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;OAWG;IACH,UAAU,CACR,QAAgB;QAEhB,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,MAAM,EAAE,QAAQ;YAChB,IAAI,EAAE,cAAc,kBAAkB,CAAC,QAAQ,CAAC,EAAE;SACnD,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAClB,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAoEG;IACH,kBAAkB,CAChB,QAAgB,EAChB,EAAE,QAAQ,EAAE,UAAU,EAA0B;QAEhD,MAAM,YAAY,GAAG,gBAAgB,CAAC,QAAQ,CAAC;YAC7C,CAAC,CAAC,QAAQ,CAAC,IAAI;YACf,CAAC,CAAC,QAAQ,EAAE,SAAS,CAAC,KAAK,CAAC;gBAC1B,CAAC,IAAA,+BAAkB,EAAC,UAAU,CAAC;oBAC7B,CAAC,CAAG,UAAkB,CAAC,GAAgB,CAAC,IAAI;oBAC5C,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpB,MAAM,MAAM,GAAG,UAAU;YACvB,CAAC,CAAC,IAAI,kBAAkB,CACpB,IAAA,+BAAkB,EAAC,UAAU,CAAC;gBAC5B,CAAC,CAAC,UAAU,CAAC,IAAI;gBACjB,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,CAChC,EAAE;YACL,CAAC,CAAC,EAAE,CAAC;QACP,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,IAAI,EAAE,cAAc,kBAAkB,CACpC,QAAQ,CACT,aAAa,kBAAkB,CAAC,YAAY,CAAC,GAAG,MAAM,EAAE;SAC1D,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CACzB,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAuEG;IACH,kBAAkB,CAChB,QAAgB,EAChB,EACE,QAAQ,EACR,UAAU,EACV,KAAK,GAC2C;QAElD,MAAM,YAAY,GAAG,gBAAgB,CAAC,QAAQ,CAAC;YAC7C,CAAC,CAAC,QAAQ,CAAC,IAAI;YACf,CAAC,CAAC,QAAQ,EAAE,SAAS,CAAC,KAAK,CAAC;gBAC1B,CAAC,IAAA,+BAAkB,EAAC,UAAU,CAAC;oBAC7B,CAAC,CAAG,UAAkB,CAAC,GAAgB,CAAC,IAAI;oBAC5C,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpB,MAAM,MAAM,GAAG,UAAU;YACvB,CAAC,CAAC,IAAI,kBAAkB,CACpB,IAAA,+BAAkB,EAAC,UAAU,CAAC;gBAC5B,CAAC,CAAC,UAAU,CAAC,IAAI;gBACjB,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,CAChC,EAAE;YACL,CAAC,CAAC,EAAE,CAAC;QACP,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,cAAc,kBAAkB,CACpC,QAAQ,CACT,aAAa,kBAAkB,CAAC,YAAY,CAAC,GAAG,MAAM,EAAE;YACzD,IAAI,EAAE,EAAE,KAAK,EAAE;SAChB,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAClB,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA8DG;IACH,oBAAoB,CAClB,QAAgB,EAChB,EAAE,QAAQ,EAAE,UAAU,EAA0B;QAEhD,MAAM,YAAY,GAAG,gBAAgB,CAAC,QAAQ,CAAC;YAC7C,CAAC,CAAC,QAAQ,CAAC,IAAI;YACf,CAAC,CAAC,QAAQ,EAAE,SAAS,CAAC,KAAK,CAAC;gBAC1B,CAAC,IAAA,+BAAkB,EAAC,UAAU,CAAC;oBAC7B,CAAC,CAAG,UAAkB,CAAC,GAAgB,CAAC,IAAI;oBAC5C,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpB,MAAM,MAAM,GAAG,UAAU;YACvB,CAAC,CAAC,IAAI,kBAAkB,CACpB,IAAA,+BAAkB,EAAC,UAAU,CAAC;gBAC5B,CAAC,CAAC,UAAU,CAAC,IAAI;gBACjB,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,CAChC,EAAE;YACL,CAAC,CAAC,EAAE,CAAC;QACP,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,MAAM,EAAE,QAAQ;YAChB,IAAI,EAAE,cAAc,kBAAkB,CACpC,QAAQ,CACT,aAAa,kBAAkB,CAAC,YAAY,CAAC,GAAG,MAAM,EAAE;SAC1D,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAClB,CAAC;IACJ,CAAC;IAsDD,gBAAgB,CAAC,QAAgB,EAAE,IAAc;QAC/C,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,IAAI,EAAE,cAAc,kBAAkB,CAAC,QAAQ,CAAC,WAAW;YAC3D,EAAE,EAAE,EAAE,IAAI,EAAE;SACb,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CACzB,CAAC;IACJ,CAAC;IA4JD,kBAAkB,CAChB,WAIoB,EACpB,MAAc,EACd,UAAiD,EAAE;QAEnD,MAAM,EAAE,cAAc,GAAG,SAAS,EAAE,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;QACxD,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,mBAAmB;YACzB,cAAc;YACd,IAAI,EAAE;gBACJ,WAAW,EAAE,4BAA4B,CAAC,WAAW,CAAC;gBACtD,MAAM;gBACN,GAAG,IAAI;aACR;SACF,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CACzB,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACH,WAAW,CAAC,aAAqB;QAC/B,OAAO,IAAI,yBAAW,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;IAC9C,CAAC;IA8FD,gBAAgB,CACd,WAIoB,EACpB,UAA8B,EAAE;QAEhC,MAAM,EAAE,cAAc,GAAG,SAAS,EAAE,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;QACxD,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,yBAAyB;YAC/B,cAAc;YACd,IAAI,EAAE;gBACJ,WAAW,EAAE,4BAA4B,CAAC,WAAW,CAAC;gBACtD,GAAG,IAAI;aACR;SACF,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,yBAAW,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CACnD,CAAC;IACJ,CAAC;IAiHD,KAAK,CAAC,eAAe,CACnB,WAIoB,EACpB,QAAmD,EACnD,UAA8B,EAAE;QAEhC,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,gBAAgB,CACrC,WAAqC,EACrC,OAAO,CACR,CAAC;QACF,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;YACpD,MAAM,GAAG,CAAC,MAAM,EAAE,CAAC;YACnB,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,IAAI,CAAC;gBACH,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC;YACpB,CAAC;YAAC,MAAM,CAAC,CAAA,CAAC;YACV,MAAM,CAAC,CAAC;QACV,CAAC;IACH,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,gBAAgB;QACd,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAC7B,EAAE,IAAI,EAAE,mBAAmB,EAAE,EAC7B,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAC/B,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,KAAK,CAAC,YAAY;QAChB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACnD,OAAO,YAAY,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/D,CAAC;IAgHD,KAAK,CACH,KAAqC,EACrC,QAA8B,EAC9B,UAAwB,EAAE;QAE1B,IAAI,IAAA,gBAAU,EAAC,KAAK,CAAC,EAAE,CAAC;YACtB,OAAO,GAAG,QAAQ,IAAI,EAAE,CAAC;YACzB,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;YAC1B,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;QACtB,CAAC;aAAM,IAAI,IAAA,kBAAY,EAAC,KAAK,CAAC,EAAE,CAAC;YAC/B,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;QACxB,CAAC;QACD,MAAM,EACJ,cAAc,EACd,eAAe,EACf,KAAK,EACL,SAAS,EACT,KAAK,EACL,WAAW,EACX,GAAG,EACH,OAAO,EACP,GAAG,IAAI,EACR,GAAG,OAAO,CAAC;QACZ,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,cAAc;YACpB,IAAI,EAAE;gBACJ,KAAK;gBACL,QAAQ;gBACR,KAAK;gBACL,SAAS;gBACT,KAAK;gBACL,WAAW;gBACX,GAAG;gBACH,OAAO,EAAE,IAAI;aACd;YACD,cAAc;YACd,eAAe;YACf,OAAO;SACR,EACD,CAAC,GAAG,EAAE,EAAE,CACN,IAAI,2BAAkB,CACpB,IAAI,EACJ,GAAG,CAAC,IAAI,EACR,GAAG,CAAC,eAAe,EACnB,cAAc,CACf,CAAC,KAAK,CACV,CAAC;IACJ,CAAC;IAoHD,OAAO,CACL,KAAqC,EACrC,QAA8B,EAC9B,OAAwB;QAExB,IAAI,IAAA,gBAAU,EAAC,KAAK,CAAC,EAAE,CAAC;YACtB,OAAO,GAAG,QAAQ,CAAC;YACnB,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;YAC1B,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;QACtB,CAAC;aAAM,IAAI,IAAA,kBAAY,EAAC,KAAK,CAAC,EAAE,CAAC;YAC/B,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;QACxB,CAAC;QACD,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,eAAe;YACrB,IAAI,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE;SACnC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,KAAK,CAAC,KAAqC;QACzC,IAAI,IAAA,gBAAU,EAAC,KAAK,CAAC,EAAE,CAAC;YACtB,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;QACtB,CAAC;aAAM,IAAI,IAAA,kBAAY,EAAC,KAAK,CAAC,EAAE,CAAC;YAC/B,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;QACxB,CAAC;QACD,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,aAAa;YACnB,IAAI,EAAE,EAAE,KAAK,EAAE;SAChB,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;OAWG;IACH,UAAU;QACR,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,IAAI,EAAE,mBAAmB;SAC1B,CAAC,CAAC;IACL,CAAC;IA+BD,aAAa,CAAC,OAA8B;QAC1C,OAAO,IAAI,CAAC,OAAO,CACjB,OAAO;YACL,CAAC,CAAC;gBACE,MAAM,EAAE,KAAK;gBACb,IAAI,EAAE,wBAAwB;gBAC9B,IAAI,EAAE,OAAO;aACd;YACH,CAAC,CAAC;gBACE,MAAM,EAAE,KAAK;gBACb,IAAI,EAAE,wBAAwB;aAC/B,CACN,CAAC;IACJ,CAAC;IAED;;;;;;;;;;OAUG;IACH,kBAAkB;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,qBAAqB;SAC5B,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,eAAe;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,kBAAkB;SACzB,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;OAWG;IACH,gBAAgB;QACd,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,MAAM,EAAE,QAAQ;YAChB,IAAI,EAAE,kBAAkB;SACzB,EACD,GAAG,EAAE,CAAC,SAAS,CAChB,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;;;OAmBG;IACH,SAAS,CAAC,OAAe;QACvB,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,MAAM,EAAE,QAAQ;YAChB,IAAI,EAAE,eAAe,kBAAkB,CAAC,OAAO,CAAC,EAAE;SACnD,EACD,GAAG,EAAE,CAAC,SAAS,CAChB,CAAC;IACJ,CAAC;IACD,YAAY;IAEZ,mBAAmB;IACnB;;;;;;;;;OASG;IACH,aAAa;QACX,OAAO,IAAI,CAAC,OAAO,CACjB,EAAE,IAAI,EAAE,mBAAmB,EAAE,EAC7B,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CACzB,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA8BG;IACH,cAAc,CACZ,IAAY,EACZ,IAAY,EACZ,kBAA2B,KAAK;QAEhC,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,mBAAmB;YACzB,IAAI,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,eAAe,EAAE;SACtC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACH,YAAY,CACV,IAAY,EACZ,QAAiB,KAAK;QAEtB,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,MAAM,EAAE,QAAQ;YAChB,IAAI,EAAE,qBAAqB,kBAAkB,CAAC,IAAI,CAAC,EAAE;YACrD,EAAE,EAAE,EAAE,KAAK,EAAE;SACd,CAAC,CAAC;IACL,CAAC;IACD,YAAY;IAEZ,kBAAkB;IAClB;;;;;;;;;;;;;;;;OAgBG;IACH,YAAY,CAAC,gBAAyB,IAAI;QACxC,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,IAAI,EAAE,YAAY;YAClB,EAAE,EAAE,EAAE,aAAa,EAAE;SACtB,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA+BG;IACH,KAAK,CAAC,cAAc,CAClB,KAAa,EACb,MAAyC,EACzC,UAAiC,EAAE;QAEnC,MAAM,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;QACvD,MAAM,GAAG,GAAG,MAAM,IAAA,kBAAM,EAAC;YACvB,aAAa;YACb,YAAY;YACZ,MAAM;SACP,CAAC,CAAC;QACH,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC;YACxB,GAAG,GAAG;YACN,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,YAAY;YAClB,QAAQ,EAAE,IAAI;YACd,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,KAAK,EAAE;SACrB,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAgCG;IACH,KAAK,CAAC,cAAc,CAClB,KAAa,EACb,MAAyC,EACzC,UAAiC,EAAE;QAEnC,MAAM,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;QACvD,MAAM,GAAG,GAAG,MAAM,IAAA,kBAAM,EAAC;YACvB,aAAa;YACb,YAAY;YACZ,MAAM;SACP,CAAC,CAAC;QACH,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC;YACxB,GAAG,GAAG;YACN,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,oBAAoB;YAC1B,QAAQ,EAAE,IAAI;YACd,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,KAAK,EAAE;SACrB,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAgCG;IACH,KAAK,CAAC,cAAc,CAClB,KAAa,EACb,MAAyC,EACzC,UAAiC,EAAE;QAEnC,MAAM,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;QACvD,MAAM,GAAG,GAAG,MAAM,IAAA,kBAAM,EAAC;YACvB,aAAa;YACb,YAAY;YACZ,MAAM;SACP,CAAC,CAAC;QACH,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC;YACxB,GAAG,GAAG;YACN,MAAM,EAAE,OAAO;YACf,IAAI,EAAE,oBAAoB;YAC1B,QAAQ,EAAE,IAAI;YACd,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,KAAK,EAAE;SACrB,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;OAWG;IACH,gBAAgB,CACd,KAAa,EACb,OAAiC;QAEjC,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,MAAM,EAAE,QAAQ;YAChB,IAAI,EAAE,oBAAoB;YAC1B,EAAE,EAAE,EAAE,GAAG,OAAO,EAAE,KAAK,EAAE;SAC1B,EACD,GAAG,EAAE,CAAC,SAAS,CAChB,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;OAWG;IACH,UAAU,CAAC,KAAa;QACtB,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,IAAI,EAAE,oBAAoB;YAC1B,EAAE,EAAE,EAAE,KAAK,EAAE;SACd,CAAC,CAAC;IACL,CAAC;IAoDD,uBAAuB,CAAC,KAAa,EAAE,UAAmB,KAAK;QAC7D,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,IAAI,EAAE,0BAA0B;YAChC,EAAE,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE;SACvB,CAAC,CAAC;IACL,CAAC;IA+DD,2BAA2B,CACzB,KAAa,EACb,GAAwB,EACxB,UAAmB,KAAK;QAExB,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,0BAA0B;YAChC,IAAI,EAAE,GAAG;YACT,EAAE,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE;SACvB,CAAC,CAAC;IACL,CAAC;IA+DD,0BAA0B,CACxB,KAAa,EACb,GAAwB,EACxB,UAAmB,KAAK;QAExB,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,MAAM,EAAE,OAAO;YACf,IAAI,EAAE,0BAA0B;YAChC,IAAI,EAAE,GAAG;YACT,EAAE,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE;SACvB,CAAC,CAAC;IACL,CAAC;IAoDD,sBAAsB,CAAC,KAAa,EAAE,UAAmB,KAAK;QAC5D,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,IAAI,EAAE,yBAAyB;YAC/B,EAAE,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE;SACvB,CAAC,CAAC;IACL,CAAC;IAwED,0BAA0B,CACxB,KAAa,EACb,IAA4B,EAC5B,UAAmB,KAAK;QAExB,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,yBAAyB;YAC/B,IAAI,EAAE,IAAI;YACV,EAAE,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE;SACvB,CAAC,CAAC;IACL,CAAC;IAwED,yBAAyB,CACvB,KAAa,EACb,IAA4B,EAC5B,UAAmB,KAAK;QAExB,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,MAAM,EAAE,OAAO;YACf,IAAI,EAAE,yBAAyB;YAC/B,IAAI,EAAE,IAAI;YACV,EAAE,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE;SACvB,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACH,yBAAyB,CACvB,KAAa,EACb,UAAmB,IAAI;QAEvB,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ;YACnC,IAAI,EAAE,wBAAwB;YAC9B,EAAE,EAAE,EAAE,KAAK,EAAE;SACd,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACH,kBAAkB,CAAC,KAAa;QAC9B,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,IAAI,EAAE,oBAAoB;YAC1B,EAAE,EAAE,EAAE,KAAK,EAAE;SACd,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;OAuBG;IACH,gBAAgB,CAAC,KAAa,EAAE,IAAY,EAAE,MAAY;QACxD,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,sBAAsB,kBAAkB,CAAC,IAAI,CAAC,EAAE;YACtD,IAAI,EAAE,MAAM;YACZ,EAAE,EAAE,EAAE,KAAK,EAAE;SACd,CAAC,CAAC;IACL,CAAC;IAwQD,eAAe,CACb,KAAa,EACb,OAIC;QAED,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,kBAAkB;YACxB,EAAE,EAAE;gBACF,GAAG,OAAO;gBACV,KAAK;aACN;SACF,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACH,gBAAgB,CAAC,KAAa;QAC5B,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,IAAI,EAAE,mBAAmB;YACzB,EAAE,EAAE,EAAE,KAAK,EAAE;SACd,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,uBAAuB,CAAC,KAAa;QACnC,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,IAAI,EAAE,oBAAoB;YAC1B,EAAE,EAAE,EAAE,KAAK,EAAE;SACd,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,eAAe,CAAC,KAAa;QAC3B,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,qBAAqB;YAC3B,EAAE,EAAE,EAAE,KAAK,EAAE;YACb,YAAY,EAAE,IAAI;SACnB,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;OAmBG;IACH,uBAAuB,CAAC,UAAmB,KAAK;QAC9C,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,mBAAmB;YACzB,EAAE,EAAE,EAAE,OAAO,EAAE;SAChB,EACD,GAAG,EAAE,CAAC,SAAS,CAChB,CAAC;IACJ,CAAC;IACD,YAAY;IACZ,qBAAqB;IACrB;;;;;;;;;;;;;OAaG;IACH,eAAe,CAAC,UAA4B,EAAE;QAC5C,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,uBAAuB;YAC7B,IAAI,EAAE,OAAO;SACd,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CACzB,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACH,cAAc,CAAC,EAAsB;QACnC,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,qBAAqB;YAC3B,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS;SAC9B,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CACzB,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,gBAAgB,CAAC,EAAU;QACzB,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,wBAAwB;YAC9B,IAAI,EAAE,EAAE,EAAE,EAAE;SACb,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAClC,CAAC;IACJ,CAAC;IAED;;;;;;;;;;OAUG;IACH,eAAe,CAAC,EAAU;QACxB,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,uBAAuB;YAC7B,IAAI,EAAE,EAAE,EAAE,EAAE;SACb,EACD,GAAG,EAAE,CAAC,SAAS,CAChB,CAAC;IACJ,CAAC;IACD,YAAY;IACZ,cAAc;IACd;;;;;;;;;;;;;;OAcG;IACH,aAAa,CAAC,OAA2B;QACvC,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,IAAI,EAAE,qBAAqB;YAC3B,EAAE,EAAE,OAAO;SACZ,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAClB,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACH,cAAc,CAAC,OAA2B;QACxC,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,IAAI,EAAE,aAAa;YACnB,EAAE,EAAE,OAAO;SACZ,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAC3B,CAAC;IACJ,CAAC;IAED;;;;;;;;OAQG;IACH,WAAW;QACT,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,IAAI,EAAE,mBAAmB;SAC1B,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,WAAW,CACT,MAAuC;QAEvC,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,mBAAmB;YACzB,IAAI,EAAE,MAAM;SACb,CAAC,CAAC;IACL,CAAC;IACD,YAAY;IACZ,oBAAoB;IACpB;;;;;;;;;;OAUG;IACH,GAAG,CAAC,KAAa;QACf,OAAO,IAAI,SAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC9B,CAAC;IAED;;;;;;;;;OASG;IACH,eAAe;QACb,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,IAAI,EAAE,mBAAmB;SAC1B,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAClB,CAAC;IACJ,CAAC;IAED;;;;;;;;;OASG;IACH,iBAAiB;QACf,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,IAAI,EAAE,gBAAgB;SACvB,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAClB,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,uBAAuB,CAAC,SAAiB;QACvC,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,MAAM,EAAE,QAAQ;YAChB,IAAI,EAAE,mBAAmB;YACzB,EAAE,EAAE,EAAE,KAAK,EAAE,SAAS,GAAG,IAAI,EAAE;SAChC,EACD,GAAG,EAAE,CAAC,SAAS,CAChB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,MAAM,EAAE,QAAQ;YAChB,IAAI,EAAE,eAAe;SACtB,EACD,GAAG,EAAE,CAAC,SAAS,CAChB,CAAC;IACJ,CAAC;CAEF;AA/vID,4BA+vIC", "sourcesContent": ["/**\n * ```js\n * import { Database } from \"arangojs/database\";\n * ```\n *\n * The \"database\" module provides the {@link Database} class and associated\n * types and interfaces for TypeScript.\n *\n * The Database class is also re-exported by the \"index\" module.\n *\n * @packageDocumentation\n */\nimport { Readable } from \"stream\";\nimport {\n  Analyzer,\n  AnalyzerDescription,\n  CreateAnalyzerOptions,\n} from \"./analyzer\";\nimport { AqlLiteral, AqlQuery, isAqlLiteral, isAqlQuery } from \"./aql\";\nimport {\n  ArangoCollection,\n  Collection,\n  CollectionMetadata,\n  collectionToString,\n  CollectionType,\n  CreateCollectionOptions,\n  DocumentCollection,\n  EdgeCollection,\n  isArangoCollection,\n} from \"./collection\";\nimport {\n  ArangoApiResponse,\n  Config,\n  Connection,\n  Headers,\n  RequestOptions,\n} from \"./connection\";\nimport { ArrayCursor, BatchedArrayCursor } from \"./cursor\";\nimport { isArangoError } from \"./error\";\nimport { FoxxManifest } from \"./foxx-manifest\";\nimport {\n  CreateGraphOptions,\n  EdgeDefinitionOptions,\n  Graph,\n  GraphInfo,\n} from \"./graph\";\nimport { Job } from \"./job\";\nimport { Blob } from \"./lib/blob\";\nimport { DATABASE_NOT_FOUND } from \"./lib/codes\";\nimport { toForm } from \"./lib/multipart\";\nimport { ArangojsResponse } from \"./lib/request\";\nimport { Route } from \"./route\";\nimport { Transaction } from \"./transaction\";\nimport { CreateViewOptions, View, ViewDescription } from \"./view\";\n\n/**\n * Indicates whether the given value represents a {@link Database}.\n *\n * @param database - A value that might be a database.\n */\nexport function isArangoDatabase(database: any): database is Database {\n  return Boolean(database && database.isArangoDatabase);\n}\n\n/**\n * @internal\n */\nfunction coerceTransactionCollections(\n  collections:\n    | (TransactionCollections & { allowImplicit?: boolean })\n    | (string | ArangoCollection)[]\n    | string\n    | ArangoCollection\n): CoercedTransactionCollections {\n  if (typeof collections === \"string\") {\n    return { write: [collections] };\n  }\n  if (Array.isArray(collections)) {\n    return { write: collections.map(collectionToString) };\n  }\n  if (isArangoCollection(collections)) {\n    return { write: collectionToString(collections) };\n  }\n  const cols: CoercedTransactionCollections = {};\n  if (collections) {\n    if (collections.allowImplicit !== undefined) {\n      cols.allowImplicit = collections.allowImplicit;\n    }\n    if (collections.read) {\n      cols.read = Array.isArray(collections.read)\n        ? collections.read.map(collectionToString)\n        : collectionToString(collections.read);\n    }\n    if (collections.write) {\n      cols.write = Array.isArray(collections.write)\n        ? collections.write.map(collectionToString)\n        : collectionToString(collections.write);\n    }\n    if (collections.exclusive) {\n      cols.exclusive = Array.isArray(collections.exclusive)\n        ? collections.exclusive.map(collectionToString)\n        : collectionToString(collections.exclusive);\n    }\n  }\n  return cols;\n}\n\n/**\n * @internal\n */\ntype CoercedTransactionCollections = {\n  allowImplicit?: boolean;\n  exclusive?: string | string[];\n  write?: string | string[];\n  read?: string | string[];\n};\n\n/**\n * Collections involved in a transaction.\n */\nexport type TransactionCollections = {\n  /**\n   * An array of collections or a single collection that will be read from or\n   * written to during the transaction with no other writes being able to run\n   * in parallel.\n   */\n  exclusive?: (string | ArangoCollection)[] | string | ArangoCollection;\n  /**\n   * An array of collections or a single collection that will be read from or\n   * written to during the transaction.\n   */\n  write?: (string | ArangoCollection)[] | string | ArangoCollection;\n  /**\n   * An array of collections or a single collection that will be read from\n   * during the transaction.\n   */\n  read?: (string | ArangoCollection)[] | string | ArangoCollection;\n};\n\n/**\n * Options for how the transaction should be performed.\n */\nexport type TransactionOptions = {\n  /**\n   * Whether the transaction may read from collections not specified for this\n   * transaction. If set to `false`, accessing any collections not specified\n   * will result in the transaction being aborted to avoid potential deadlocks.\n   *\n   * Default: `true`.\n   */\n  allowImplicit?: boolean;\n  /**\n   * If set to `true`, the request will explicitly permit ArangoDB to return a\n   * potentially dirty or stale result and arangojs will load balance the\n   * request without distinguishing between leaders and followers.\n   */\n  allowDirtyRead?: boolean;\n  /**\n   * Determines whether to force the transaction to write all data to disk\n   * before returning.\n   */\n  waitForSync?: boolean;\n  /**\n   * Determines how long the database will wait while attempting to gain locks\n   * on collections used by the transaction before timing out.\n   */\n  lockTimeout?: number;\n  /**\n   * (RocksDB only.) Determines the transaction size limit in bytes.\n   */\n  maxTransactionSize?: number;\n};\n\n/**\n * Options for executing a query.\n *\n * See {@link Database#query}.\n */\nexport type QueryOptions = {\n  /**\n   * If set to `true`, the query will be executed with support for dirty reads\n   * enabled, permitting ArangoDB to return a potentially dirty or stale result\n   * and arangojs will load balance the request without distinguishing between\n   * leaders and followers.\n   *\n   * Note that dirty reads are only supported for read-only queries, not data\n   * modification queries (e.g. using `INSERT`, `UPDATE`, `REPLACE` or\n   * `REMOVE`) and only when using ArangoDB 3.4 or later.\n   *\n   * Default: `false`\n   */\n  allowDirtyRead?: boolean;\n  /**\n   * If set to `true`, cursor results will be stored by ArangoDB in such a way\n   * that batch reads can be retried in the case of a communication error.\n   *\n   * Default: `false`\n   */\n  allowRetry?: boolean;\n  /**\n   * Maximum time in milliseconds arangojs will wait for a server response.\n   * Exceeding this value will result in the request being cancelled.\n   *\n   * **Note**: Setting a timeout for the client does not guarantee the query\n   * will be killed by ArangoDB if it is already being executed. See the\n   * `maxRuntime` option for limiting the execution time within ArangoDB.\n   */\n  timeout?: number;\n  /**\n   * If set to a positive number, the query will automatically be retried at\n   * most this many times if it results in a write-write conflict.\n   *\n   * Default: `0`\n   */\n  retryOnConflict?: number;\n  /**\n   * Unless set to `false`, the number of result values in the result set will\n   * be returned in the `count` attribute. This may be disabled by default in\n   * a future version of ArangoDB if calculating this value has a performance\n   * impact for some queries.\n   *\n   * Default: `true`.\n   */\n  count?: boolean;\n  /**\n   * Number of result values to be transferred by the server in each\n   * network roundtrip (or \"batch\").\n   *\n   * Must be greater than zero.\n   */\n  batchSize?: number;\n  /**\n   * If set to `false`, the AQL query results cache lookup will be skipped for\n   * this query.\n   *\n   * Default: `true`\n   */\n  cache?: boolean;\n  /**\n   * Maximum memory size in bytes that the query is allowed to use.\n   * Exceeding this value will result in the query failing with an error.\n   *\n   * If set to `0`, the memory limit is disabled.\n   *\n   * Default: `0`\n   */\n  memoryLimit?: number;\n  /**\n   * Maximum allowed execution time before the query will be killed in seconds.\n   *\n   * If set to `0`, the query will be allowed to run indefinitely.\n   *\n   * Default: `0`\n   */\n  maxRuntime?: number;\n  /**\n   * Time-to-live for the cursor in seconds. The cursor results may be\n   * garbage collected by ArangoDB after this much time has passed.\n   *\n   * Default: `30`\n   */\n  ttl?: number;\n  /**\n   * If set to `true`, the query will throw an exception and abort if it would\n    otherwise produce a warning.\n   */\n  failOnWarning?: boolean;\n  /**\n   * If set to `1` or `true`, additional query profiling information will be\n   * returned in the `extra.profile` attribute if the query is not served from\n   * the result cache.\n   *\n   * If set to `2`, the query will return execution stats per query plan node\n   * in the `extra.stats.nodes` attribute. Additionally the query plan is\n   * returned in `extra.plan`.\n   */\n  profile?: boolean | number;\n  /**\n   * If set to `true`, the query will be executed as a streaming query.\n   */\n  stream?: boolean;\n  /**\n   * Limits the maximum number of warnings a query will return.\n   */\n  maxWarningsCount?: number;\n  /**\n   * If set to `true` and the query has a `LIMIT` clause, the total number of\n   * values matched before the last top-level `LIMIT` in the query was applied\n   * will be returned in the `extra.stats.fullCount` attribute.\n   */\n  fullCount?: boolean;\n  /**\n   * If set to `false`, the query data will not be stored in the RocksDB block\n   * cache. This can be used to avoid thrashing he block cache when reading a\n   * lot of data.\n   */\n  fillBlockCache?: boolean;\n  /**\n   * An object with a `rules` property specifying a list of optimizer rules to\n   * be included or excluded by the optimizer for this query. Prefix a rule\n   * name with `+` to include it, or `-` to exclude it. The name `all` acts as\n   * an alias matching all optimizer rules.\n   */\n  optimizer?: { rules: string[] };\n  /**\n   * Limits the maximum number of plans that will be created by the AQL query\n   * optimizer.\n   */\n  maxPlans?: number;\n  /**\n   * Controls after how many execution nodes in a query a stack split should be\n   * performed.\n   *\n   * Default: `250` (`200` on macOS)\n   */\n  maxNodesPerCallstack?: number;\n  /**\n   * (RocksDB only.) Maximum size of transactions in bytes.\n   */\n  maxTransactionSize?: number;\n  /**\n   * (RocksDB only.) Maximum number of operations after which an intermediate\n   * commit is automatically performed.\n   */\n  intermediateCommitCount?: number;\n  /**\n   * (RocksDB only.) Maximum total size of operations in bytes after which an\n   * intermediate commit is automatically performed.\n   */\n  intermediateCommitSize?: number;\n  /**\n   * (Enterprise Edition cluster only.) If set to `true`, collections\n   * inaccessible to current user will result in an access error instead\n   * of being treated as empty.\n   */\n  skipInaccessibleCollections?: boolean;\n  /**\n   * (Enterprise Edition cluster only.) Limits the maximum time in seconds a\n   * DBServer will wait to bring satellite collections involved in the query\n   * into sync. Exceeding this value will result in the query being stopped.\n   *\n   * Default: `60`\n   */\n  satelliteSyncWait?: number;\n};\n\n/**\n * Options for explaining a query.\n *\n * See {@link Database#explain}.\n */\nexport type ExplainOptions = {\n  /**\n   * An object with a `rules` property specifying a list of optimizer rules to\n   * be included or excluded by the optimizer for this query. Prefix a rule\n   * name with `+` to include it, or `-` to exclude it. The name `all` acts as\n   * an alias matching all optimizer rules.\n   */\n  optimizer?: { rules: string[] };\n  /**\n   * Maximum number of plans that the optimizer is allowed to generate.\n   * Setting this to a low value limits the amount of work the optimizer does.\n   */\n  maxNumberOfPlans?: number;\n  /**\n   * If set to true, all possible execution plans will be returned as the\n   * `plans` property. Otherwise only the optimal execution plan will be\n   * returned as the `plan` property.\n   *\n   * Default: `false`\n   */\n  allPlans?: boolean;\n};\n\n/**\n * Details for a transaction.\n *\n * See also {@link transaction.TransactionStatus}.\n */\nexport type TransactionDetails = {\n  /**\n   * Unique identifier of the transaction.\n   */\n  id: string;\n  /**\n   * Status (or \"state\") of the transaction.\n   */\n  state: \"running\" | \"committed\" | \"aborted\";\n};\n\n/**\n * Plan explaining query execution.\n */\nexport type ExplainPlan = {\n  /**\n   * Execution nodes in this plan.\n   */\n  nodes: {\n    [key: string]: any;\n    type: string;\n    id: number;\n    dependencies: number[];\n    estimatedCost: number;\n    estimatedNrItems: number;\n  }[];\n  /**\n   * Rules applied by the optimizer.\n   */\n  rules: string[];\n  /**\n   * Information about collections involved in the query.\n   */\n  collections: {\n    name: string;\n    type: \"read\" | \"write\";\n  }[];\n  /**\n   * Variables used in the query.\n   */\n  variables: {\n    id: number;\n    name: string;\n  }[];\n  /**\n   * Total estimated cost of the plan.\n   */\n  estimatedCost: number;\n  /**\n   * Estimated number of items returned by the query.\n   */\n  estimatedNrItems: number;\n  /**\n   * Whether the query is a data modification query.\n   */\n  isModificationQuery: boolean;\n};\n\n/**\n * Optimizer statistics for an explained query.\n */\nexport type ExplainStats = {\n  /**\n   * Total number of rules executed for this query.\n   */\n  rulesExecuted: number;\n  /**\n   * Number of rules skipped for this query.\n   */\n  rulesSkipped: number;\n  /**\n   * Total number of plans created.\n   */\n  plansCreated: number;\n  /**\n   * Maximum memory usage in bytes of the query during explain.\n   */\n  peakMemoryUsage: number;\n  /**\n   * Time in seconds needed to explain the query.\n   */\n  executionTime: number;\n};\n\n/**\n * Result of explaining a query with a single plan.\n */\nexport type SingleExplainResult = {\n  /**\n   * Query plan.\n   */\n  plan: ExplainPlan;\n  /**\n   * Whether it would be possible to cache the query.\n   */\n  cacheable: boolean;\n  /**\n   * Warnings encountered while planning the query execution.\n   */\n  warnings: { code: number; message: string }[];\n  /**\n   * Optimizer statistics for the explained query.\n   */\n  stats: ExplainStats;\n};\n\n/**\n * Result of explaining a query with multiple plans.\n */\nexport type MultiExplainResult = {\n  /**\n   * Query plans.\n   */\n  plans: ExplainPlan[];\n  /**\n   * Whether it would be possible to cache the query.\n   */\n  cacheable: boolean;\n  /**\n   * Warnings encountered while planning the query execution.\n   */\n  warnings: { code: number; message: string }[];\n  /**\n   * Optimizer statistics for the explained query.\n   */\n  stats: ExplainStats;\n};\n\n/**\n * Node in an AQL abstract syntax tree (AST).\n */\nexport type AstNode = {\n  [key: string]: any;\n  type: string;\n  subNodes: AstNode[];\n};\n\n/**\n * Result of parsing a query.\n */\nexport type ParseResult = {\n  /**\n   * Whether the query was parsed.\n   */\n  parsed: boolean;\n  /**\n   * Names of all collections involved in the query.\n   */\n  collections: string[];\n  /**\n   * Names of all bind parameters used in the query.\n   */\n  bindVars: string[];\n  /**\n   * Abstract syntax tree (AST) of the query.\n   */\n  ast: AstNode[];\n};\n\n/**\n * Optimizer rule for AQL queries.\n */\nexport type QueryOptimizerRule = {\n  name: string;\n  flags: {\n    hidden: boolean;\n    clusterOnly: boolean;\n    canBeDisabled: boolean;\n    canCreateAdditionalPlans: boolean;\n    disabledByDefault: boolean;\n    enterpriseOnly: boolean;\n  };\n};\n\n/**\n * Information about query tracking.\n */\nexport type QueryTracking = {\n  /**\n   * Whether query tracking is enabled.\n   */\n  enabled: boolean;\n  /**\n   * Maximum query string length in bytes that is kept in the list.\n   */\n  maxQueryStringLength: number;\n  /**\n   * Maximum number of slow queries that is kept in the list.\n   */\n  maxSlowQueries: number;\n  /**\n   * Threshold execution time in seconds for when a query is\n   * considered slow.\n   */\n  slowQueryThreshold: number;\n  /**\n   * Whether bind parameters are being tracked along with queries.\n   */\n  trackBindVars: boolean;\n  /**\n   * Whether slow queries are being tracked.\n   */\n  trackSlowQueries: boolean;\n};\n\n/**\n * Options for query tracking.\n *\n * See {@link Database#queryTracking}.\n */\nexport type QueryTrackingOptions = {\n  /**\n   * If set to `false`, neither queries nor slow queries will be tracked.\n   */\n  enabled?: boolean;\n  /**\n   * Maximum query string length in bytes that will be kept in the list.\n   */\n  maxQueryStringLength?: number;\n  /**\n   * Maximum number of slow queries to be kept in the list.\n   */\n  maxSlowQueries?: number;\n  /**\n   * Threshold execution time in seconds for when a query will be\n   * considered slow.\n   */\n  slowQueryThreshold?: number;\n  /**\n   * If set to `true`, bind parameters will be tracked along with queries.\n   */\n  trackBindVars?: boolean;\n  /**\n   * If set to `true` and `enabled` is also set to `true`, slow queries will be\n   * tracked if their execution time exceeds `slowQueryThreshold`.\n   */\n  trackSlowQueries?: boolean;\n};\n\n/**\n * Object describing a query.\n */\nexport type QueryInfo = {\n  /**\n   * Unique identifier for this query.\n   */\n  id: string;\n  /**\n   * Name of the database the query runs in.\n   */\n  database: string;\n  /**\n   * Name of the user that started the query.\n   */\n  user: string;\n  /**\n   * Query string (potentially truncated).\n   */\n  query: string;\n  /**\n   * Bind parameters used in the query.\n   */\n  bindVars: Record<string, any>;\n  /**\n   * Date and time the query was started.\n   */\n  started: string;\n  /**\n   * Query's running time in seconds.\n   */\n  runTime: number;\n  /**\n   * Maximum memory usage in bytes of the query.\n   */\n  peakMemoryUsage: number;\n  /**\n   * Query's current execution state.\n   */\n  state: \"executing\" | \"finished\" | \"killed\";\n  /**\n   * Whether the query uses a streaming cursor.\n   */\n  stream: boolean;\n};\n\n/**\n * Information about a cluster imbalance.\n */\nexport type ClusterImbalanceInfo = {\n  /**\n   * Information about the leader imbalance.\n   */\n  leader: {\n    /**\n     * The weight of leader shards per DB-Server. A leader has a weight of 1 by default but it is higher if collections can only be moved together because of `distributeShardsLike`.\n     */\n    weightUsed: number[];\n    /**\n     * The ideal weight of leader shards per DB-Server.\n     */\n    targetWeight: number[];\n    /**\n     * The number of leader shards per DB-Server.\n     */\n    numberShards: number[];\n    /**\n     * The measure of the leader shard distribution. The higher the number, the worse the distribution.\n     */\n    leaderDupl: number[];\n    /**\n     * The sum of all weights.\n     */\n    totalWeight: number;\n    /**\n     * The measure of the total imbalance. A high value indicates a high imbalance.\n     */\n    imbalance: number;\n    /**\n     * The sum of shards, counting leader shards only.\n     */\n    totalShards: number;\n  };\n  /**\n   * Information about the shard imbalance.\n   */\n  shards: {\n    /**\n     * The size of shards per DB-Server.\n     */\n    sizeUsed: number[];\n    /**\n     * The ideal size of shards per DB-Server.\n     */\n    targetSize: number[];\n    /**\n     * The number of leader and follower shards per DB-Server.\n     */\n    numberShards: number[];\n    /**\n     * The sum of the sizes.\n     */\n    totalUsed: number;\n    /**\n     * The sum of shards, counting leader and follower shards.\n     */\n    totalShards: number;\n    /**\n     * The sum of system collection shards, counting leader shards only.\n     */\n    totalShardsFromSystemCollections: number;\n    /**\n     * The measure of the total imbalance. A high value indicates a high imbalance.\n     */\n    imbalance: number;\n  };\n};\n\n/**\n * Information about the current state of the cluster imbalance.\n */\nexport type ClusterRebalanceState = ClusterImbalanceInfo & {\n  /**\n   * The number of pending move shard operations.\n   */\n  pendingMoveShards: number;\n  /**\n   * The number of planned move shard operations.\n   */\n  todoMoveShards: number;\n};\n\n/**\n * Options for rebalancing the cluster.\n */\nexport type ClusterRebalanceOptions = {\n  /**\n   * Maximum number of moves to be computed.\n   *\n   * Default: `1000`\n   */\n  maximumNumberOfMoves?: number;\n  /**\n   * Allow leader changes without moving data.\n   *\n   * Default: `true`\n   */\n  leaderChanges?: boolean;\n  /**\n   * Allow moving leaders.\n   *\n   * Default: `false`\n   */\n  moveLeaders?: boolean;\n  /**\n   * Allow moving followers.\n   *\n   * Default: `false`\n   */\n  moveFollowers?: boolean;\n  /**\n   * Ignore system collections in the rebalance plan.\n   *\n   * Default: `false`\n   */\n  excludeSystemCollections?: boolean;\n  /**\n   * Default: `256**6`\n   */\n  piFactor?: number;\n  /**\n   * A list of database names to exclude from the analysis.\n   *\n   * Default: `[]`\n   */\n  databasesExcluded?: string[];\n};\n\nexport type ClusterRebalanceMove = {\n  /**\n   * The server name from which to move.\n   */\n  from: string;\n  /**\n   * The ID of the destination server.\n   */\n  to: string;\n  /**\n   * Shard ID of the shard to be moved.\n   */\n  shard: string;\n  /**\n   * Collection ID of the collection the shard belongs to.\n   */\n  collection: number;\n  /**\n   * True if this is a leader move shard operation.\n   */\n  isLeader: boolean;\n};\n\nexport type ClusterRebalanceResult = {\n  /**\n   * Imbalance before the suggested move shard operations are applied.\n   */\n  imbalanceBefore: ClusterImbalanceInfo;\n  /**\n   * Expected imbalance after the suggested move shard operations are applied.\n   */\n  imbalanceAfter: ClusterImbalanceInfo;\n  /**\n   * Suggested move shard operations.\n   */\n  moves: ClusterRebalanceMove[];\n};\n\n/**\n * Database user to create with a database.\n */\nexport type CreateDatabaseUser = {\n  /**\n   * Username of the user to create.\n   */\n  username: string;\n  /**\n   * Password of the user to create.\n   *\n   * Default: `\"\"`\n   */\n  passwd?: string;\n  /**\n   * Whether the user is active.\n   *\n   * Default: `true`\n   */\n  active?: boolean;\n  /**\n   * Additional data to store with the user object.\n   */\n  extra?: Record<string, any>;\n};\n\n/**\n * Options for creating a database.\n *\n * See {@link Database#createDatabase}.\n */\nexport type CreateDatabaseOptions = {\n  /**\n   * Database users to create with the database.\n   */\n  users?: CreateDatabaseUser[];\n  /**\n   * (Cluster only.) The sharding method to use for new collections in the\n   * database.\n   */\n  sharding?: \"\" | \"flexible\" | \"single\";\n  /**\n   * (Cluster only.) Default replication factor for new collections in this\n   * database.\n   *\n   * Setting this to `1` disables replication. Setting this to `\"satellite\"`\n   * will replicate to every DBServer.\n   */\n  replicationFactor?: \"satellite\" | number;\n  /**\n   * (Cluster only.) Default write concern for new collections created in this\n   * database.\n   */\n  writeConcern?: number;\n};\n\n/**\n * Object describing a database.\n *\n * See {@link Database#get}.\n */\nexport type DatabaseInfo = {\n  /**\n   * Name of the database.\n   */\n  name: string;\n  /**\n   * Unique identifier of the database.\n   */\n  id: string;\n  /**\n   * File system path of the database.\n   */\n  path: string;\n  /**\n   * Whether the database is the system database.\n   */\n  isSystem: boolean;\n  /**\n   * (Cluster only.) The sharding method to use for new collections in the\n   * database.\n   */\n  sharding?: \"\" | \"flexible\" | \"single\";\n  /**\n   * (Cluster only.) Default replication factor for new collections in this\n   * database.\n   */\n  replicationFactor?: \"satellite\" | number;\n  /**\n   * (Cluster only.) Default write concern for new collections created in this\n   * database.\n   */\n  writeConcern?: number;\n};\n\n/**\n * Result of retrieving database version information.\n */\nexport type VersionInfo = {\n  /**\n   * Value identifying the server type, i.e. `\"arango\"`.\n   */\n  server: string;\n  /**\n   * ArangoDB license type or \"edition\".\n   */\n  license: \"community\" | \"enterprise\";\n  /**\n   * ArangoDB server version.\n   */\n  version: string;\n  /**\n   * Additional information about the ArangoDB server.\n   */\n  details?: { [key: string]: string };\n};\n\n/**\n * Definition of an AQL User Function.\n */\nexport type AqlUserFunction = {\n  /**\n   * Name of the AQL User Function.\n   */\n  name: string;\n  /**\n   * Implementation of the AQL User Function.\n   */\n  code: string;\n  /**\n   * Whether the function is deterministic.\n   *\n   * See {@link Database#createFunction}.\n   */\n  isDeterministic: boolean;\n};\n\n/**\n * Options for installing the service.\n *\n * See {@link Database#installService}.\n */\nexport type InstallServiceOptions = {\n  /**\n   * An object mapping configuration option names to values.\n   *\n   * See also {@link Database#getServiceConfiguration}.\n   */\n  configuration?: Record<string, any>;\n  /**\n   * An object mapping dependency aliases to mount points.\n   *\n   * See also {@link Database#getServiceDependencies}.\n   */\n  dependencies?: Record<string, string>;\n  /**\n   * Whether the service should be installed in development mode.\n   *\n   * See also {@link Database#setServiceDevelopmentMode}.\n   *\n   * Default: `false`\n   */\n  development?: boolean;\n  /**\n   * Whether the service should be installed in legacy compatibility mode\n   *\n   * This overrides the `engines` option in the service manifest (if any).\n   *\n   * Default: `false`\n   */\n  legacy?: boolean;\n  /**\n   * Whether the \"setup\" script should be executed.\n   *\n   * Default: `true`\n   */\n  setup?: boolean;\n};\n\n/**\n * Options for replacing a service.\n *\n * See {@link Database#replaceService}.\n */\nexport type ReplaceServiceOptions = {\n  /**\n   * An object mapping configuration option names to values.\n   *\n   * See also {@link Database#getServiceConfiguration}.\n   */\n  configuration?: Record<string, any>;\n  /**\n   * An object mapping dependency aliases to mount points.\n   *\n   * See also {@link Database#getServiceDependencies}.\n   */\n  dependencies?: Record<string, string>;\n  /**\n   * Whether the service should be installed in development mode.\n   *\n   * See also {@link Database#setServiceDevelopmentMode}.\n   *\n   * Default: `false`\n   */\n  development?: boolean;\n  /**\n   * Whether the service should be installed in legacy compatibility mode\n   *\n   * This overrides the `engines` option in the service manifest (if any).\n   *\n   * Default: `false`\n   */\n  legacy?: boolean;\n  /**\n   * Whether the \"setup\" script should be executed.\n   *\n   * Default: `true`\n   */\n  setup?: boolean;\n  /**\n   * Whether the existing service's \"teardown\" script should be executed\n   * prior to removing that service.\n   *\n   * Default: `true`\n   */\n  teardown?: boolean;\n  /**\n   * If set to `true`, replacing a service that does not already exist will\n   * fall back to installing the new service.\n   *\n   * Default: `false`\n   */\n  force?: boolean;\n};\n\n/**\n * Options for upgrading a service.\n *\n * See {@link Database#upgradeService}.\n */\nexport type UpgradeServiceOptions = {\n  /**\n   * An object mapping configuration option names to values.\n   *\n   * See also {@link Database#getServiceConfiguration}.\n   */\n  configuration?: Record<string, any>;\n  /**\n   * An object mapping dependency aliases to mount points.\n   *\n   * See also {@link Database#getServiceDependencies}.\n   */\n  dependencies?: Record<string, string>;\n  /**\n   * Whether the service should be installed in development mode.\n   *\n   * See also {@link Database#setServiceDevelopmentMode}.\n   *\n   * Default: `false`\n   */\n  development?: boolean;\n  /**\n   * Whether the service should be installed in legacy compatibility mode\n   *\n   * This overrides the `engines` option in the service manifest (if any).\n   *\n   * Default: `false`\n   */\n  legacy?: boolean;\n  /**\n   * Whether the \"setup\" script should be executed.\n   *\n   * Default: `true`\n   */\n  setup?: boolean;\n  /**\n   * Whether the existing service's \"teardown\" script should be executed\n   * prior to upgrading that service.\n   *\n   * Default: `false`\n   */\n  teardown?: boolean;\n  /**\n   * Unless set to `true`, upgrading a service that does not already exist will\n   * fall back to installing the new service.\n   *\n   * Default: `false`\n   */\n  force?: boolean;\n};\n\n/**\n * Options for uninstalling a service.\n *\n * See {@link Database#uninstallService}.\n */\nexport type UninstallServiceOptions = {\n  /**\n   * Whether the service's \"teardown\" script should be executed\n   * prior to removing that service.\n   *\n   * Default: `true`\n   */\n  teardown?: boolean;\n  /**\n   * If set to `true`, uninstalling a service that does not already exist\n   * will be considered successful.\n   *\n   * Default: `false`\n   */\n  force?: boolean;\n};\n\n/**\n * Object briefly describing a Foxx service.\n */\nexport type ServiceSummary = {\n  /**\n   * Service mount point, relative to the database.\n   */\n  mount: string;\n  /**\n   * Name defined in the service manifest.\n   */\n  name?: string;\n  /**\n   * Version defined in the service manifest.\n   */\n  version?: string;\n  /**\n   * Service dependencies the service expects to be able to match as a mapping\n   * from dependency names to versions the service is compatible with.\n   */\n  provides: Record<string, string>;\n  /**\n   * Whether development mode is enabled for this service.\n   */\n  development: boolean;\n  /**\n   * Whether the service is running in legacy compatibility mode.\n   */\n  legacy: boolean;\n};\n\n/**\n * Object describing a Foxx service in detail.\n */\nexport type ServiceInfo = {\n  /**\n   * Service mount point, relative to the database.\n   */\n  mount: string;\n  /**\n   * File system path of the service.\n   */\n  path: string;\n  /**\n   * Name defined in the service manifest.\n   */\n  name?: string;\n  /**\n   * Version defined in the service manifest.\n   */\n  version?: string;\n  /**\n   * Whether development mode is enabled for this service.\n   */\n  development: boolean;\n  /**\n   * Whether the service is running in legacy compatibility mode.\n   */\n  legacy: boolean;\n  /**\n   * Content of the service manifest of this service.\n   */\n  manifest: FoxxManifest;\n  /**\n   * Internal checksum of the service's initial source bundle.\n   */\n  checksum: string;\n  /**\n   * Options for this service.\n   */\n  options: {\n    /**\n     * Configuration values set for this service.\n     */\n    configuration: Record<string, any>;\n    /**\n     * Service dependency configuration of this service.\n     */\n    dependencies: Record<string, string>;\n  };\n};\n\n/**\n * Object describing a configuration option of a Foxx service.\n */\nexport type ServiceConfiguration = {\n  /**\n   * Data type of the configuration value.\n   *\n   * **Note**: `\"int\"` and `\"bool\"` are historical synonyms for `\"integer\"` and\n   * `\"boolean\"`. The `\"password\"` type is synonymous with `\"string\"` but can\n   * be used to distinguish values which should not be displayed in plain text\n   * by software when managing the service.\n   */\n  type:\n    | \"integer\"\n    | \"boolean\"\n    | \"string\"\n    | \"number\"\n    | \"json\"\n    | \"password\"\n    | \"int\"\n    | \"bool\";\n  /**\n   * Current value of the configuration option as stored internally.\n   */\n  currentRaw: any;\n  /**\n   * Processed current value of the configuration option as exposed in the\n   * service code.\n   */\n  current: any;\n  /**\n   * Formatted name of the configuration option.\n   */\n  title: string;\n  /**\n   * Human-readable description of the configuration option.\n   */\n  description?: string;\n  /**\n   * Whether the configuration option must be set in order for the service\n   * to be operational.\n   */\n  required: boolean;\n  /**\n   * Default value of the configuration option.\n   */\n  default?: any;\n};\n\n/**\n * Object describing a single-service dependency defined by a Foxx service.\n */\nexport type SingleServiceDependency = {\n  /**\n   * Whether this is a multi-service dependency.\n   */\n  multiple: false;\n  /**\n   * Current mount point the dependency is resolved to.\n   */\n  current?: string;\n  /**\n   * Formatted name of the dependency.\n   */\n  title: string;\n  /**\n   * Name of the service the dependency expects to match.\n   */\n  name: string;\n  /**\n   * Version of the service the dependency expects to match.\n   */\n  version: string;\n  /**\n   * Human-readable description of the dependency.\n   */\n  description?: string;\n  /**\n   * Whether the dependency must be matched in order for the service\n   * to be operational.\n   */\n  required: boolean;\n};\n\n/**\n * Object describing a multi-service dependency defined by a Foxx service.\n */\nexport type MultiServiceDependency = {\n  /**\n   * Whether this is a multi-service dependency.\n   */\n  multiple: true;\n  /**\n   * Current mount points the dependency is resolved to.\n   */\n  current?: string[];\n  /**\n   * Formatted name of the dependency.\n   */\n  title: string;\n  /**\n   * Name of the service the dependency expects to match.\n   */\n  name: string;\n  /**\n   * Version of the service the dependency expects to match.\n   */\n  version: string;\n  /**\n   * Human-readable description of the dependency.\n   */\n  description?: string;\n  /**\n   * Whether the dependency must be matched in order for the service\n   * to be operational.\n   */\n  required: boolean;\n};\n\n/**\n * Test stats for a Foxx service's tests.\n */\nexport type ServiceTestStats = {\n  /**\n   * Total number of tests found.\n   */\n  tests: number;\n  /**\n   * Number of tests that ran successfully.\n   */\n  passes: number;\n  /**\n   * Number of tests that failed.\n   */\n  failures: number;\n  /**\n   * Number of tests skipped or not executed.\n   */\n  pending: number;\n  /**\n   * Total test duration in milliseconds.\n   */\n  duration: number;\n};\n\n/**\n * Test results for a single test case using the stream reporter.\n */\nexport type ServiceTestStreamTest = {\n  title: string;\n  fullTitle: string;\n  duration: number;\n  err?: string;\n};\n\n/**\n * Test results for a Foxx service's tests using the stream reporter.\n */\nexport type ServiceTestStreamReport = (\n  | [\"start\", { total: number }]\n  | [\"pass\", ServiceTestStreamTest]\n  | [\"fail\", ServiceTestStreamTest]\n  | [\"end\", ServiceTestStats]\n)[];\n\n/**\n * Test results for a single test case using the suite reporter.\n */\nexport type ServiceTestSuiteTest = {\n  result: \"pending\" | \"pass\" | \"fail\";\n  title: string;\n  duration: number;\n  err?: any;\n};\n\n/**\n * Test results for a single test suite using the suite reporter.\n */\nexport type ServiceTestSuite = {\n  title: string;\n  suites: ServiceTestSuite[];\n  tests: ServiceTestSuiteTest[];\n};\n\n/**\n * Test results for a Foxx service's tests using the suite reporter.\n */\nexport type ServiceTestSuiteReport = {\n  stats: ServiceTestStats;\n  suites: ServiceTestSuite[];\n  tests: ServiceTestSuiteTest[];\n};\n\n/**\n * Test results for a single test case in XUnit format using the JSONML\n * representation.\n */\nexport type ServiceTestXunitTest =\n  | [\"testcase\", { classname: string; name: string; time: number }]\n  | [\n      \"testcase\",\n      { classname: string; name: string; time: number },\n      [\"failure\", { message: string; type: string }, string]\n    ];\n\n/**\n * Test results for a Foxx service's tests in XUnit format using the JSONML\n * representation.\n */\nexport type ServiceTestXunitReport = [\n  \"testsuite\",\n  {\n    timestamp: number;\n    tests: number;\n    errors: number;\n    failures: number;\n    skip: number;\n    time: number;\n  },\n  ...ServiceTestXunitTest[]\n];\n\n/**\n * Test results for a Foxx service's tests in TAP format.\n */\nexport type ServiceTestTapReport = string[];\n\n/**\n * Test results for a single test case using the default reporter.\n */\nexport type ServiceTestDefaultTest = {\n  title: string;\n  fullTitle: string;\n  duration: number;\n  err?: string;\n};\n\n/**\n * Test results for a Foxx service's tests using the default reporter.\n */\nexport type ServiceTestDefaultReport = {\n  stats: ServiceTestStats;\n  tests: ServiceTestDefaultTest[];\n  pending: ServiceTestDefaultTest[];\n  failures: ServiceTestDefaultTest[];\n  passes: ServiceTestDefaultTest[];\n};\n\n/**\n * OpenAPI 2.0 description of a Foxx service.\n */\nexport type SwaggerJson = {\n  [key: string]: any;\n  info: {\n    title: string;\n    description: string;\n    version: string;\n    license: string;\n  };\n  path: {\n    [key: string]: any;\n  };\n};\n\n/**\n * Access level for an ArangoDB user's access to a collection or database.\n */\nexport type AccessLevel = \"rw\" | \"ro\" | \"none\";\n\n/**\n * Properties of an ArangoDB user object.\n */\nexport type ArangoUser = {\n  /**\n   * ArangoDB username of the user.\n   */\n  user: string;\n  /**\n   * Whether the ArangoDB user account is enabled and can authenticate.\n   */\n  active: boolean;\n  /**\n   * Additional information to store about this user.\n   */\n  extra: Record<string, any>;\n};\n\n/**\n * Options for creating an ArangoDB user.\n */\nexport type CreateUserOptions = {\n  /**\n   * ArangoDB username of the user.\n   */\n  user: string;\n  /**\n   * Password the ArangoDB user will use for authentication.\n   */\n  passwd: string;\n  /**\n   * Whether the ArangoDB user account is enabled and can authenticate.\n   *\n   * Default: `true`\n   */\n  active?: boolean;\n  /**\n   * Additional information to store about this user.\n   *\n   * Default: `{}`\n   */\n  extra?: Record<string, any>;\n};\n\n/**\n * Options for modifying an ArangoDB user.\n */\nexport type UserOptions = {\n  /**\n   * Password the ArangoDB user will use for authentication.\n   */\n  passwd: string;\n  /**\n   * Whether the ArangoDB user account is enabled and can authenticate.\n   *\n   * Default: `true`\n   */\n  active?: boolean;\n  /**\n   * Additional information to store about this user.\n   *\n   * Default: `{}`\n   */\n  extra?: Record<string, any>;\n};\n\n/**\n * Options for accessing or manipulating access levels.\n */\nexport type UserAccessLevelOptions = {\n  /**\n   * The database to access or manipulate the access level of.\n   *\n   * If `collection` is an `ArangoCollection`, this option defaults to the\n   * database the collection is contained in. Otherwise this option defaults to\n   * the current database.\n   */\n  database?: Database | string;\n  /**\n   * The collection to access or manipulate the access level of.\n   */\n  collection?: ArangoCollection | string;\n};\n\n/**\n * An object providing methods for accessing queue time metrics of the most\n * recently received server responses if the server supports this feature.\n */\nexport type QueueTimeMetrics = {\n  /**\n   * Returns the queue time of the most recently received response in seconds.\n   */\n  getLatest: () => number | undefined;\n  /**\n   * Returns a list of the most recently received queue time values as tuples\n   * of the timestamp of the response being processed in milliseconds and the\n   * queue time in seconds.\n   */\n  getValues: () => [number, number][];\n  /**\n   * Returns the average queue time of the most recently received responses\n   * in seconds.\n   */\n  getAvg: () => number;\n};\n\n/**\n * (Enterprise Edition only.) Options for creating a hot backup.\n */\nexport type HotBackupOptions = {\n  /**\n   * If set to `true` and no global transaction lock can be acquired within the\n   * given timeout, a possibly inconsistent backup is taken.\n   *\n   * Default: `false`\n   */\n  allowInconsistent?: boolean;\n  /**\n   * (Enterprise Edition cluster only.) If set to `true` and no global\n   * transaction lock can be acquired within the given timeout, all running\n   * transactions are forcefully aborted to ensure that a consistent backup\n   * can be created.\n   *\n   * Default: `false`.\n   */\n  force?: boolean;\n  /**\n   * Label to appended to the backup's identifier.\n   *\n   * Default: If omitted or empty, a UUID will be generated.\n   */\n  label?: string;\n  /**\n   * Time in seconds that the operation will attempt to get a consistent\n   * snapshot.\n   *\n   * Default: `120`.\n   */\n  timeout?: number;\n};\n\n/**\n * (Enterprise Edition only.) Result of a hot backup.\n */\nexport type HotBackupResult = {\n  id: string;\n  potentiallyInconsistent: boolean;\n  sizeInBytes: number;\n  datetime: string;\n  nrDBServers: number;\n  nrFiles: number;\n};\n\n/**\n * (Enterprise Edition only.) List of known hot backups.\n */\nexport type HotBackupList = {\n  server: string;\n  list: Record<\n    string,\n    HotBackupResult & {\n      version: string;\n      keys: any[];\n      available: boolean;\n      nrPiecesPresent: number;\n      countIncludesFilesOnly: boolean;\n    }\n  >;\n};\n\n/**\n * Numeric representation of the logging level of a log entry.\n */\nexport enum LogLevel {\n  FATAL,\n  ERROR,\n  WARNING,\n  INFO,\n  DEBUG,\n}\n\n/**\n * String representation of the logging level of a log entry.\n */\nexport type LogLevelLabel = \"FATAL\" | \"ERROR\" | \"WARNING\" | \"INFO\" | \"DEBUG\";\n\n/**\n * Logging level setting.\n */\nexport type LogLevelSetting = LogLevelLabel | \"DEFAULT\";\n\n/**\n * Log sorting direction, ascending or descending.\n */\nexport type LogSortDirection = \"asc\" | \"desc\";\n\n/**\n * Options for retrieving log entries.\n */\nexport type LogEntriesOptions = {\n  /**\n   * Maximum log level of the entries to retrieve.\n   *\n   * Default: `INFO`.\n   */\n  upto?: LogLevel | LogLevelLabel | Lowercase<LogLevelLabel>;\n  /**\n   * If set, only log entries with this log level will be returned.\n   */\n  level?: LogLevel | LogLevelLabel | Lowercase<LogLevelLabel>;\n  /**\n   * If set, only log entries with an `lid` greater than or equal to this value\n   * will be returned.\n   */\n  start?: number;\n  /**\n   * If set, only this many entries will be returned.\n   */\n  size?: number;\n  /**\n   * If set, this many log entries will be skipped.\n   */\n  offset?: number;\n  /**\n   * If set, only log entries containing the specified text will be returned.\n   */\n  search?: string;\n  /**\n   * If set to `\"desc\"`, log entries will be returned in reverse chronological\n   * order.\n   *\n   * Default: `\"asc\"`.\n   */\n  sort?: LogSortDirection;\n};\n\n/**\n * An object representing a single log entry.\n */\nexport type LogMessage = {\n  id: number;\n  topic: string;\n  level: LogLevelLabel;\n  date: string;\n  message: string;\n};\n\n/**\n * An object representing a list of log entries.\n */\nexport type LogEntries = {\n  totalAmount: number;\n  lid: number[];\n  topic: string[];\n  level: LogLevel[];\n  timestamp: number[];\n  text: string[];\n};\n\ntype TrappedError = {\n  error: true;\n};\n\ntype TrappedRequest = {\n  error?: false;\n  jobId: string;\n  onResolve: (res: ArangojsResponse) => void;\n  onReject: (error: any) => void;\n};\n\n/**\n * An object representing a single ArangoDB database. All arangojs collections,\n * cursors, analyzers and so on are linked to a `Database` object.\n */\nexport class Database {\n  protected _connection: Connection;\n  protected _name: string;\n  protected _analyzers = new Map<string, Analyzer>();\n  protected _collections = new Map<string, Collection>();\n  protected _graphs = new Map<string, Graph>();\n  protected _views = new Map<string, View>();\n  protected _trapRequest?: (trapped: TrappedError | TrappedRequest) => void;\n\n  /**\n   * Creates a new `Database` instance with its own connection pool.\n   *\n   * See also {@link Database#database}.\n   *\n   * @param config - An object with configuration options.\n   *\n   * @example\n   * ```js\n   * const db = new Database({\n   *   url: \"http://127.0.0.1:8529\",\n   *   databaseName: \"my_database\",\n   *   auth: { username: \"admin\", password: \"hunter2\" },\n   * });\n   * ```\n   */\n  constructor(config?: Config);\n  /**\n   * Creates a new `Database` instance with its own connection pool.\n   *\n   * See also {@link Database#database}.\n   *\n   * @param url - Base URL of the ArangoDB server or list of server URLs.\n   * Equivalent to the `url` option in {@link connection.Config}.\n   *\n   * @example\n   * ```js\n   * const db = new Database(\"http://127.0.0.1:8529\", \"my_database\");\n   * db.useBasicAuth(\"admin\", \"hunter2\");\n   * ```\n   */\n  constructor(url: string | string[], name?: string);\n  /**\n   * @internal\n   */\n  constructor(database: Database, name?: string);\n  constructor(\n    configOrDatabase: string | string[] | Config | Database = {},\n    name?: string\n  ) {\n    if (isArangoDatabase(configOrDatabase)) {\n      const connection = configOrDatabase._connection;\n      const databaseName = (name || configOrDatabase.name).normalize(\"NFC\");\n      this._connection = connection;\n      this._name = databaseName;\n      const database = connection.database(databaseName);\n      if (database) return database;\n    } else {\n      const config = configOrDatabase;\n      const { databaseName, ...options } =\n        typeof config === \"string\" || Array.isArray(config)\n          ? { databaseName: name, url: config }\n          : config;\n      this._connection = new Connection(options);\n      this._name = databaseName?.normalize(\"NFC\") || \"_system\";\n    }\n  }\n\n  //#region misc\n  /**\n   * @internal\n   *\n   * Indicates that this object represents an ArangoDB database.\n   */\n  get isArangoDatabase(): true {\n    return true;\n  }\n\n  /**\n   * Name of the ArangoDB database this instance represents.\n   */\n  get name() {\n    return this._name;\n  }\n\n  /**\n   * Fetches version information from the ArangoDB server.\n   *\n   * @param details - If set to `true`, additional information about the\n   * ArangoDB server will be available as the `details` property.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const version = await db.version();\n   * // the version object contains the ArangoDB version information.\n   * // license: \"community\" or \"enterprise\"\n   * // version: ArangoDB version number\n   * // server: description of the server\n   * ```\n   */\n  version(details?: boolean): Promise<VersionInfo> {\n    return this.request({\n      method: \"GET\",\n      path: \"/_api/version\",\n      qs: { details },\n    });\n  }\n\n  /**\n   * Retrives the server's current system time in milliseconds with microsecond\n   * precision.\n   */\n  time(): Promise<number> {\n    return this.request(\n      {\n        path: \"/_admin/time\",\n      },\n      (res) => res.body.time * 1000\n    );\n  }\n\n  /**\n   * Returns a new {@link route.Route} instance for the given path (relative to the\n   * database) that can be used to perform arbitrary HTTP requests.\n   *\n   * @param path - The database-relative URL of the route. Defaults to the\n   * database API root.\n   * @param headers - Default headers that should be sent with each request to\n   * the route.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const myFoxxService = db.route(\"my-foxx-service\");\n   * const response = await myFoxxService.post(\"users\", {\n   *   username: \"admin\",\n   *   password: \"hunter2\"\n   * });\n   * // response.body is the result of\n   * // POST /_db/_system/my-foxx-service/users\n   * // with JSON request body '{\"username\": \"admin\", \"password\": \"hunter2\"}'\n   * ```\n   */\n  route(path?: string, headers?: Headers): Route {\n    return new Route(this, path, headers);\n  }\n\n  /**\n   * Creates an async job by executing the given callback function. The first\n   * database request performed by the callback will be marked for asynchronous\n   * execution and its result will be made available as an async job.\n   *\n   * Returns a {@link Job} instance that can be used to retrieve the result\n   * of the callback function once the request has been executed.\n   *\n   * @param callback - Callback function to execute as an async job.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const job = await db.createJob(() => db.collections());\n   * while (!job.isLoaded) {\n   *  await timeout(1000);\n   *  await job.load();\n   * }\n   * // job.result is a list of Collection instances\n   * ```\n   */\n  async createJob<T>(callback: () => Promise<T>): Promise<Job<T>> {\n    const trap = new Promise<TrappedError | TrappedRequest>((resolveTrap) => {\n      this._trapRequest = (trapped) => resolveTrap(trapped);\n    });\n    const eventualResult = callback();\n    const trapped = await trap;\n    if (trapped.error) return eventualResult as Promise<any>;\n    const { jobId, onResolve, onReject } = trapped as TrappedRequest;\n    return new Job(\n      this,\n      jobId,\n      (res) => {\n        onResolve(res);\n        return eventualResult;\n      },\n      (e) => {\n        onReject(e);\n        return eventualResult;\n      }\n    );\n  }\n\n  /**\n   * @internal\n   *\n   * Performs an arbitrary HTTP request against the database.\n   *\n   * If `absolutePath` is set to `true`, the database path will not be\n   * automatically prepended to the `basePath`.\n   *\n   * @param T - Return type to use. Defaults to the response object type.\n   * @param options - Options for this request.\n   * @param transform - An optional function to transform the low-level\n   * response object to a more useful return value.\n   */\n  async request<T = any>(\n    options: RequestOptions & { absolutePath?: boolean },\n    transform?: (res: ArangojsResponse) => T\n  ): Promise<T>;\n  /**\n   * @internal\n   *\n   * Performs an arbitrary HTTP request against the database.\n   *\n   * If `absolutePath` is set to `true`, the database path will not be\n   * automatically prepended to the `basePath`.\n   *\n   * @param options - Options for this request.\n   * @param transform - If set to `false`, the raw response object will be\n   * returned.\n   */\n  async request(\n    options: RequestOptions & { absolutePath?: boolean },\n    transform: false\n  ): Promise<ArangojsResponse>;\n  async request<T = any>(\n    {\n      absolutePath = false,\n      basePath,\n      ...opts\n    }: RequestOptions & { absolutePath?: boolean },\n    transform: false | ((res: ArangojsResponse) => T) = (res) => res.body\n  ): Promise<T> {\n    if (!absolutePath) {\n      basePath = `/_db/${encodeURIComponent(this._name)}${basePath || \"\"}`;\n    }\n    if (this._trapRequest) {\n      const trap = this._trapRequest;\n      this._trapRequest = undefined;\n      return new Promise<T>(async (resolveRequest, rejectRequest) => {\n        const options = { ...opts };\n        options.headers = { ...options.headers, \"x-arango-async\": \"store\" };\n        let jobRes: ArangojsResponse;\n        try {\n          jobRes = await this._connection.request({ basePath, ...options });\n        } catch (e) {\n          trap({ error: true });\n          rejectRequest(e);\n          return;\n        }\n        const jobId = jobRes.headers[\"x-arango-async-id\"] as string;\n        trap({\n          jobId,\n          onResolve: (res) => {\n            const result = transform ? transform(res) : (res as T);\n            resolveRequest(result);\n            return result;\n          },\n          onReject: (err) => {\n            rejectRequest(err);\n            throw err;\n          },\n        });\n      });\n    }\n    return this._connection.request(\n      { basePath, ...opts },\n      transform || undefined\n    );\n  }\n\n  /**\n   * Updates the URL list by requesting a list of all coordinators in the\n   * cluster and adding any endpoints not initially specified in the\n   * {@link connection.Config}.\n   *\n   * For long-running processes communicating with an ArangoDB cluster it is\n   * recommended to run this method periodically (e.g. once per hour) to make\n   * sure new coordinators are picked up correctly and can be used for\n   * fail-over or load balancing.\n   *\n   * @param overwrite - If set to `true`, the existing host list will be\n   * replaced instead of extended.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const interval = setInterval(\n   *   () => db.acquireHostList(),\n   *   5 * 60 * 1000 // every 5 minutes\n   * );\n   *\n   * // later\n   * clearInterval(interval);\n   * system.close();\n   * ```\n   */\n  async acquireHostList(overwrite = false): Promise<void> {\n    const urls: string[] = await this.request(\n      { path: \"/_api/cluster/endpoints\" },\n      (res) => res.body.endpoints.map((endpoint: any) => endpoint.endpoint)\n    );\n    if (urls.length > 0) {\n      if (overwrite) this._connection.setHostList(urls);\n      else this._connection.addToHostList(urls);\n    }\n  }\n\n  /**\n   * Closes all active connections of this database instance.\n   *\n   * Can be used to clean up idling connections during longer periods of\n   * inactivity.\n   *\n   * **Note**: This method currently has no effect in the browser version of\n   * arangojs.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const sessions = db.collection(\"sessions\");\n   * // Clean up expired sessions once per hour\n   * setInterval(async () => {\n   *   await db.query(aql`\n   *     FOR session IN ${sessions}\n   *     FILTER session.expires < DATE_NOW()\n   *     REMOVE session IN ${sessions}\n   *   `);\n   *   // Making sure to close the connections because they're no longer used\n   *   system.close();\n   * }, 1000 * 60 * 60);\n   * ```\n   */\n  close(): void {\n    this._connection.close();\n  }\n\n  /**\n   * Attempts to initiate a clean shutdown of the server.\n   */\n  shutdown(): Promise<void> {\n    return this.request(\n      {\n        method: \"DELETE\",\n        path: \"/_admin/shutdown\",\n      },\n      () => undefined\n    );\n  }\n\n  /**\n   * Performs a request against every known coordinator and returns when the\n   * request has succeeded against every coordinator or the timeout is reached.\n   *\n   * **Note**: This method is primarily intended to make database setup easier\n   * in cluster scenarios and requires all coordinators to be known to arangojs\n   * before the method is invoked. The method is not useful in single-server or\n   * leader-follower replication scenarios.\n   *\n   * @example\n   * ```js\n   * const db = new Database({ loadBalancingStrategy: \"ROUND_ROBIN\" });\n   * await system.acquireHostList();\n   * const analyzer = db.analyzer(\"my-analyzer\");\n   * await analyzer.create();\n   * await db.waitForPropagation(\n   *   { path: `/_api/analyzer/${encodeURIComponent(analyzer.name)}` },\n   *   30000\n   * );\n   * // Analyzer has been propagated to all coordinators and can safely be used\n   * ```\n   *\n   * @param request - Request to perform against each known coordinator.\n   * @param timeout - Maximum number of milliseconds to wait for propagation.\n   */\n  async waitForPropagation(\n    request: RequestOptions,\n    timeout?: number\n  ): Promise<void>;\n  async waitForPropagation(\n    { basePath, ...request }: RequestOptions,\n    timeout?: number\n  ): Promise<void> {\n    await this._connection.waitForPropagation(\n      {\n        ...request,\n        basePath: `/_db/${encodeURIComponent(this._name)}${basePath || \"\"}`,\n      },\n      timeout\n    );\n  }\n\n  /**\n   * Methods for accessing the server-reported queue times of the mostly\n   * recently received responses.\n   */\n  get queueTime(): QueueTimeMetrics {\n    return this._connection.queueTime;\n  }\n\n  /**\n   * Sets the limit for the number of values of the most recently received\n   * server-reported queue times that can be accessed using\n   * {@link Database#queueTime}.\n   *\n   * @param responseQueueTimeSamples - Number of values to maintain.\n   */\n  setResponseQueueTimeSamples(responseQueueTimeSamples: number) {\n    this._connection.setResponseQueueTimeSamples(responseQueueTimeSamples);\n  }\n\n  //#endregion\n\n  //#region auth\n  /**\n   * Updates the underlying connection's `authorization` header to use Basic\n   * authentication with the given `username` and `password`, then returns\n   * itself.\n   *\n   * @param username - The username to authenticate with.\n   * @param password - The password to authenticate with.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * db.useBasicAuth(\"admin\", \"hunter2\");\n   * // with the username \"admin\" and password \"hunter2\".\n   * ```\n   */\n  useBasicAuth(username: string = \"root\", password: string = \"\"): this {\n    this._connection.setBasicAuth({ username, password });\n    return this;\n  }\n\n  /**\n   * Updates the underlying connection's `authorization` header to use Bearer\n   * authentication with the given authentication `token`, then returns itself.\n   *\n   * @param token - The token to authenticate with.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * db.useBearerAuth(\"keyboardcat\");\n   * // The database instance now uses Bearer authentication.\n   * ```\n   */\n  useBearerAuth(token: string): this {\n    this._connection.setBearerAuth({ token });\n    return this;\n  }\n\n  /**\n   * Validates the given database credentials and exchanges them for an\n   * authentication token, then uses the authentication token for future\n   * requests and returns it.\n   *\n   * @param username - The username to authenticate with.\n   * @param password - The password to authenticate with.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * await db.login(\"admin\", \"hunter2\");\n   * // with an authentication token for the \"admin\" user.\n   * ```\n   */\n  login(username: string = \"root\", password: string = \"\"): Promise<string> {\n    return this.request(\n      {\n        method: \"POST\",\n        path: \"/_open/auth\",\n        body: { username, password },\n      },\n      (res) => {\n        this.useBearerAuth(res.body.jwt);\n        return res.body.jwt;\n      }\n    );\n  }\n\n  /**\n   * Attempts to renew the authentication token passed to {@link Database#useBearerAuth}\n   * or returned and used by {@link Database#login}. If a new authentication\n   * token is issued, it will be used for future requests and returned.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * await db.login(\"admin\", \"hunter2\");\n   * // ... later ...\n   * const newToken = await db.renewAuthToken();\n   * if (!newToken) // no new token issued\n   * ```\n   */\n  renewAuthToken(): Promise<string | null> {\n    return this.request(\n      {\n        method: \"POST\",\n        path: \"/_open/auth/renew\",\n      },\n      (res) => {\n        if (!res.body.jwt) return null;\n        this.useBearerAuth(res.body.jwt);\n        return res.body.jwt;\n      }\n    );\n  }\n  //#endregion\n\n  //#region rebalancing\n  /**\n   * Computes the current cluster imbalance.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const imbalance = await db.getClusterImbalance();\n   * ```\n   */\n  getClusterImbalance(): Promise<ClusterRebalanceState> {\n    return this.request(\n      { path: \"/_admin/cluster/rebalance\" },\n      (res) => res.body.result\n    );\n  }\n\n  /**\n   * Computes a set of move shard operations to rebalance the cluster.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const result = await db.computerClusterRebalance({\n   *   moveLeaders: true,\n   *   moveFollowers: true\n   * });\n   * if (result.moves.length) {\n   *   await db.executeClusterRebalance(result.moves);\n   * }\n   * ```\n   */\n  computeClusterRebalance(\n    opts: ClusterRebalanceOptions\n  ): Promise<ClusterRebalanceResult> {\n    return this.request(\n      {\n        method: \"POST\",\n        path: \"/_admin/cluster/rebalance\",\n        body: {\n          version: 1,\n          ...opts,\n        },\n      },\n      (res) => res.body.result\n    );\n  }\n\n  /**\n   * Executes the given cluster move shard operations.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const result = await db.computerClusterRebalance({\n   *   moveLeaders: true,\n   *   moveFollowers: true\n   * });\n   * if (result.moves.length) {\n   *   await db.executeClusterRebalance(result.moves);\n   * }\n   * ```\n   */\n  executeClusterRebalance(moves: ClusterRebalanceMove[]): Promise<unknown> {\n    return this.request({\n      method: \"POST\",\n      path: \"/_admin/cluster/rebalance/execute\",\n      body: {\n        version: 1,\n        moves,\n      },\n    });\n  }\n\n  /**\n   * Computes a set of move shard operations to rebalance the cluster and\n   * executes them.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const result = await db.rebalanceCluster({\n   *   moveLeaders: true,\n   *   moveFollowers: true\n   * });\n   * // The cluster is now rebalanced.\n   * ```\n   */\n  rebalanceCluster(\n    opts: ClusterRebalanceOptions\n  ): Promise<ClusterRebalanceResult> {\n    return this.request({\n      method: \"PUT\",\n      path: \"/_admin/cluster/rebalance\",\n      body: {\n        version: 1,\n        ...opts,\n      },\n    });\n  }\n  //#endregion\n\n  //#region databases\n  /**\n   * Creates a new `Database` instance for the given `databaseName` that\n   * shares this database's connection pool.\n   *\n   * See also {@link Database:constructor}.\n   *\n   * @param databaseName - Name of the database.\n   *\n   * @example\n   * ```js\n   * const systemDb = new Database();\n   * const myDb = system.database(\"my_database\");\n   * ```\n   */\n  database(databaseName: string) {\n    return new Database(this as any, databaseName);\n  }\n\n  /**\n   * Fetches the database description for the active database from the server.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const info = await db.get();\n   * // the database exists\n   * ```\n   */\n  get(): Promise<DatabaseInfo> {\n    return this.request(\n      { path: \"/_api/database/current\" },\n      (res) => res.body.result\n    );\n  }\n\n  /**\n   * Checks whether the database exists.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const result = await db.exists();\n   * // result indicates whether the database exists\n   * ```\n   */\n  async exists(): Promise<boolean> {\n    try {\n      await this.get();\n      return true;\n    } catch (err: any) {\n      if (isArangoError(err) && err.errorNum === DATABASE_NOT_FOUND) {\n        return false;\n      }\n      throw err;\n    }\n  }\n\n  /**\n   * Creates a new database with the given `databaseName` with the given\n   * `options` and returns a `Database` instance for that database.\n   *\n   * @param databaseName - Name of the database to create.\n   * @param options - Options for creating the database.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const info = await db.createDatabase(\"mydb\", {\n   *   users: [{ username: \"root\" }]\n   * });\n   * // the database has been created\n   * ```\n   */\n  createDatabase(\n    databaseName: string,\n    options?: CreateDatabaseOptions\n  ): Promise<Database>;\n  /**\n   * Creates a new database with the given `databaseName` with the given\n   * `users` and returns a `Database` instance for that database.\n   *\n   * @param databaseName - Name of the database to create.\n   * @param users - Database users to create with the database.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const info = await db.createDatabase(\"mydb\", [{ username: \"root\" }]);\n   * // the database has been created\n   * ```\n   */\n  createDatabase(\n    databaseName: string,\n    users: CreateDatabaseUser[]\n  ): Promise<Database>;\n  createDatabase(\n    databaseName: string,\n    usersOrOptions: CreateDatabaseUser[] | CreateDatabaseOptions = {}\n  ): Promise<Database> {\n    const { users, ...options } = Array.isArray(usersOrOptions)\n      ? { users: usersOrOptions }\n      : usersOrOptions;\n    return this.request(\n      {\n        method: \"POST\",\n        path: \"/_api/database\",\n        body: { name: databaseName.normalize(\"NFC\"), users, options },\n      },\n      () => this.database(databaseName)\n    );\n  }\n\n  /**\n   * Fetches all databases from the server and returns an array of their names.\n   *\n   * See also {@link Database#databases} and\n   * {@link Database#listUserDatabases}.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const names = await db.listDatabases();\n   * // databases is an array of database names\n   * ```\n   */\n  listDatabases(): Promise<string[]> {\n    return this.request({ path: \"/_api/database\" }, (res) => res.body.result);\n  }\n\n  /**\n   * Fetches all databases accessible to the active user from the server and\n   * returns an array of their names.\n   *\n   * See also {@link Database#userDatabases} and\n   * {@link Database#listDatabases}.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const names = await db.listUserDatabases();\n   * // databases is an array of database names\n   * ```\n   */\n  listUserDatabases(): Promise<string[]> {\n    return this.request(\n      { path: \"/_api/database/user\" },\n      (res) => res.body.result\n    );\n  }\n\n  /**\n   * Fetches all databases from the server and returns an array of `Database`\n   * instances for those databases.\n   *\n   * See also {@link Database#listDatabases} and\n   * {@link Database#userDatabases}.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const names = await db.databases();\n   * // databases is an array of databases\n   * ```\n   */\n  databases(): Promise<Database[]> {\n    return this.request({ path: \"/_api/database\" }, (res) =>\n      (res.body.result as string[]).map((databaseName) =>\n        this.database(databaseName)\n      )\n    );\n  }\n\n  /**\n   * Fetches all databases accessible to the active user from the server and\n   * returns an array of `Database` instances for those databases.\n   *\n   * See also {@link Database#listUserDatabases} and\n   * {@link Database#databases}.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const names = await db.userDatabases();\n   * // databases is an array of databases\n   * ```\n   */\n  userDatabases(): Promise<Database[]> {\n    return this.request({ path: \"/_api/database/user\" }, (res) =>\n      (res.body.result as string[]).map((databaseName) =>\n        this.database(databaseName)\n      )\n    );\n  }\n\n  /**\n   * Deletes the database with the given `databaseName` from the server.\n   *\n   * @param databaseName - Name of the database to delete.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * await db.dropDatabase(\"mydb\");\n   * // database \"mydb\" no longer exists\n   * ```\n   */\n  dropDatabase(databaseName: string): Promise<boolean> {\n    databaseName = databaseName.normalize(\"NFC\");\n    return this.request(\n      {\n        method: \"DELETE\",\n        path: `/_api/database/${encodeURIComponent(databaseName)}`,\n      },\n      (res) => res.body.result\n    );\n  }\n  //#endregion\n\n  //#region collections\n  /**\n   * Returns a `Collection` instance for the given collection name.\n   *\n   * In TypeScript the collection implements both the\n   * {@link collection.DocumentCollection} and {@link collection.EdgeCollection}\n   * interfaces and can be cast to either type to enforce a stricter API.\n   *\n   * @param T - Type to use for document data. Defaults to `any`.\n   * @param collectionName - Name of the edge collection.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"potatoes\");\n   * ```\n   *\n   * @example\n   * ```ts\n   * interface Person {\n   *   name: string;\n   * }\n   * const db = new Database();\n   * const persons = db.collection<Person>(\"persons\");\n   * ```\n   *\n   * @example\n   * ```ts\n   * interface Person {\n   *   name: string;\n   * }\n   * interface Friend {\n   *   startDate: number;\n   *   endDate?: number;\n   * }\n   * const db = new Database();\n   * const documents = db.collection(\"persons\") as DocumentCollection<Person>;\n   * const edges = db.collection(\"friends\") as EdgeCollection<Friend>;\n   * ```\n   */\n  collection<T extends Record<string, any> = any>(\n    collectionName: string\n  ): DocumentCollection<T> & EdgeCollection<T> {\n    collectionName = collectionName.normalize(\"NFC\");\n    if (!this._collections.has(collectionName)) {\n      this._collections.set(\n        collectionName,\n        new Collection(this, collectionName)\n      );\n    }\n    return this._collections.get(collectionName)!;\n  }\n\n  /**\n   * Creates a new collection with the given `collectionName` and `options`,\n   * then returns a {@link collection.DocumentCollection} instance for the new collection.\n   *\n   * @param T - Type to use for document data. Defaults to `any`.\n   * @param collectionName - Name of the new collection.\n   * @param options - Options for creating the collection.\n   *\n   * @example\n   * ```ts\n   * const db = new Database();\n   * const documents = db.createCollection(\"persons\");\n   * ```\n   *\n   * @example\n   * ```ts\n   * interface Person {\n   *   name: string;\n   * }\n   * const db = new Database();\n   * const documents = db.createCollection<Person>(\"persons\");\n   * ```\n   */\n  async createCollection<T extends Record<string, any> = any>(\n    collectionName: string,\n    options?: CreateCollectionOptions & {\n      type?: CollectionType.DOCUMENT_COLLECTION;\n    }\n  ): Promise<DocumentCollection<T>>;\n  /**\n   * Creates a new edge collection with the given `collectionName` and\n   * `options`, then returns an {@link collection.EdgeCollection} instance for the new\n   * edge collection.\n   *\n   * @param T - Type to use for edge document data. Defaults to `any`.\n   * @param collectionName - Name of the new collection.\n   * @param options - Options for creating the collection.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const edges = db.createCollection(\"friends\", {\n   *   type: CollectionType.EDGE_COLLECTION\n   * });\n   * ```\n   *\n   * @example\n   * ```ts\n   * interface Friend {\n   *   startDate: number;\n   *   endDate?: number;\n   * }\n   * const db = new Database();\n   * const edges = db.createCollection<Friend>(\"friends\", {\n   *   type: CollectionType.EDGE_COLLECTION\n   * });\n   * ```\n   */\n  async createCollection<T extends Record<string, any> = any>(\n    collectionName: string,\n    options: CreateCollectionOptions & {\n      type: CollectionType.EDGE_COLLECTION;\n    }\n  ): Promise<EdgeCollection<T>>;\n  async createCollection<T extends Record<string, any> = any>(\n    collectionName: string,\n    options?: CreateCollectionOptions & { type?: CollectionType }\n  ): Promise<DocumentCollection<T> & EdgeCollection<T>> {\n    const collection = this.collection(collectionName);\n    await collection.create(options);\n    return collection;\n  }\n\n  /**\n   * Creates a new edge collection with the given `collectionName` and\n   * `options`, then returns an {@link collection.EdgeCollection} instance for the new\n   * edge collection.\n   *\n   * This is a convenience method for calling {@link Database#createCollection}\n   * with `options.type` set to `EDGE_COLLECTION`.\n   *\n   * @param T - Type to use for edge document data. Defaults to `any`.\n   * @param collectionName - Name of the new collection.\n   * @param options - Options for creating the collection.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const edges = db.createEdgeCollection(\"friends\");\n   * ```\n   *\n   * @example\n   * ```ts\n   * interface Friend {\n   *   startDate: number;\n   *   endDate?: number;\n   * }\n   * const db = new Database();\n   * const edges = db.createEdgeCollection<Friend>(\"friends\");\n   * ```\n   */\n  async createEdgeCollection<T extends Record<string, any> = any>(\n    collectionName: string,\n    options?: CreateCollectionOptions\n  ): Promise<EdgeCollection<T>> {\n    return this.createCollection(collectionName, {\n      ...options,\n      type: CollectionType.EDGE_COLLECTION,\n    });\n  }\n\n  /**\n   * Renames the collection `collectionName` to `newName`.\n   *\n   * Additionally removes any stored `Collection` instance for\n   * `collectionName` from the `Database` instance's internal cache.\n   *\n   * **Note**: Renaming collections may not be supported when ArangoDB is\n   * running in a cluster configuration.\n   *\n   * @param collectionName - Current name of the collection.\n   * @param newName - The new name of the collection.\n   */\n  async renameCollection(\n    collectionName: string,\n    newName: string\n  ): Promise<ArangoApiResponse<CollectionMetadata>> {\n    collectionName = collectionName.normalize(\"NFC\");\n    const result = await this.request({\n      method: \"PUT\",\n      path: `/_api/collection/${encodeURIComponent(collectionName)}/rename`,\n      body: { name: newName.normalize(\"NFC\") },\n    });\n    this._collections.delete(collectionName);\n    return result;\n  }\n\n  /**\n   * Fetches all collections from the database and returns an array of\n   * collection descriptions.\n   *\n   * See also {@link Database#collections}.\n   *\n   * @param excludeSystem - Whether system collections should be excluded.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collections = await db.listCollections();\n   * // collections is an array of collection descriptions\n   * // not including system collections\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collections = await db.listCollections(false);\n   * // collections is an array of collection descriptions\n   * // including system collections\n   * ```\n   */\n  listCollections(\n    excludeSystem: boolean = true\n  ): Promise<CollectionMetadata[]> {\n    return this.request(\n      {\n        path: \"/_api/collection\",\n        qs: { excludeSystem },\n      },\n      (res) => res.body.result\n    );\n  }\n\n  /**\n   * Fetches all collections from the database and returns an array of\n   * `Collection` instances.\n   *\n   * In TypeScript these instances implement both the\n   * {@link collection.DocumentCollection} and {@link collection.EdgeCollection}\n   * interfaces and can be cast to either type to enforce a stricter API.\n   *\n   * See also {@link Database#listCollections}.\n   *\n   * @param excludeSystem - Whether system collections should be excluded.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collections = await db.collections();\n   * // collections is an array of DocumentCollection and EdgeCollection\n   * // instances not including system collections\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collections = await db.collections(false);\n   * // collections is an array of DocumentCollection and EdgeCollection\n   * // instances including system collections\n   * ```\n   */\n  async collections(\n    excludeSystem: boolean = true\n  ): Promise<Array<DocumentCollection & EdgeCollection>> {\n    const collections = await this.listCollections(excludeSystem);\n    return collections.map((data) => this.collection(data.name));\n  }\n  //#endregion\n\n  //#region graphs\n  /**\n   * Returns a {@link graph.Graph} instance representing the graph with the given\n   * `graphName`.\n   *\n   * @param graphName - Name of the graph.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const graph = db.graph(\"some-graph\");\n   * ```\n   */\n  graph(graphName: string): Graph {\n    graphName = graphName.normalize(\"NFC\");\n    if (!this._graphs.has(graphName)) {\n      this._graphs.set(graphName, new Graph(this, graphName));\n    }\n    return this._graphs.get(graphName)!;\n  }\n\n  /**\n   * Creates a graph with the given `graphName` and `edgeDefinitions`, then\n   * returns a {@link graph.Graph} instance for the new graph.\n   *\n   * @param graphName - Name of the graph to be created.\n   * @param edgeDefinitions - An array of edge definitions.\n   * @param options - An object defining the properties of the graph.\n   */\n  async createGraph(\n    graphName: string,\n    edgeDefinitions: EdgeDefinitionOptions[],\n    options?: CreateGraphOptions\n  ): Promise<Graph> {\n    const graph = this.graph(graphName.normalize(\"NFC\"));\n    await graph.create(edgeDefinitions, options);\n    return graph;\n  }\n\n  /**\n   * Fetches all graphs from the database and returns an array of graph\n   * descriptions.\n   *\n   * See also {@link Database#graphs}.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const graphs = await db.listGraphs();\n   * // graphs is an array of graph descriptions\n   * ```\n   */\n  listGraphs(): Promise<GraphInfo[]> {\n    return this.request({ path: \"/_api/gharial\" }, (res) => res.body.graphs);\n  }\n\n  /**\n   * Fetches all graphs from the database and returns an array of {@link graph.Graph}\n   * instances for those graphs.\n   *\n   * See also {@link Database#listGraphs}.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const graphs = await db.graphs();\n   * // graphs is an array of Graph instances\n   * ```\n   */\n  async graphs(): Promise<Graph[]> {\n    const graphs = await this.listGraphs();\n    return graphs.map((data: any) => this.graph(data._key));\n  }\n  //#endregion\n\n  //#region views\n  /**\n   * Returns a {@link view.View} instance for the given `viewName`.\n   *\n   * @param viewName - Name of the ArangoSearch or SearchAlias View.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const view = db.view(\"potatoes\");\n   * ```\n   */\n  view(viewName: string): View {\n    viewName = viewName.normalize(\"NFC\");\n    if (!this._views.has(viewName)) {\n      this._views.set(viewName, new View(this, viewName));\n    }\n    return this._views.get(viewName)!;\n  }\n\n  /**\n   * Creates a new View with the given `viewName` and `options`, then returns a\n   * {@link view.View} instance for the new View.\n   *\n   * @param viewName - Name of the View.\n   * @param options - An object defining the properties of the View.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const view = await db.createView(\"potatoes\", { type: \"arangosearch\" });\n   * // the ArangoSearch View \"potatoes\" now exists\n   * ```\n   */\n  async createView(\n    viewName: string,\n    options: CreateViewOptions\n  ): Promise<View> {\n    const view = this.view(viewName.normalize(\"NFC\"));\n    await view.create(options);\n    return view;\n  }\n\n  /**\n   * Renames the view `viewName` to `newName`.\n   *\n   * Additionally removes any stored {@link view.View} instance for `viewName` from\n   * the `Database` instance's internal cache.\n   *\n   * **Note**: Renaming views may not be supported when ArangoDB is running in\n   * a cluster configuration.\n   *\n   * @param viewName - Current name of the view.\n   * @param newName - The new name of the view.\n   */\n  async renameView(\n    viewName: string,\n    newName: string\n  ): Promise<ArangoApiResponse<ViewDescription>> {\n    viewName = viewName.normalize(\"NFC\");\n    const result = await this.request({\n      method: \"PUT\",\n      path: `/_api/view/${encodeURIComponent(viewName)}/rename`,\n      body: { name: newName.normalize(\"NFC\") },\n    });\n    this._views.delete(viewName);\n    return result;\n  }\n\n  /**\n   * Fetches all Views from the database and returns an array of View\n   * descriptions.\n   *\n   * See also {@link Database#views}.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   *\n   * const views = await db.listViews();\n   * // views is an array of View descriptions\n   * ```\n   */\n  listViews(): Promise<ViewDescription[]> {\n    return this.request({ path: \"/_api/view\" }, (res) => res.body.result);\n  }\n\n  /**\n   * Fetches all Views from the database and returns an array of\n   * {@link view.View} instances\n   * for the Views.\n   *\n   * See also {@link Database#listViews}.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const views = await db.views();\n   * // views is an array of ArangoSearch View instances\n   * ```\n   */\n  async views(): Promise<View[]> {\n    const views = await this.listViews();\n    return views.map((data) => this.view(data.name));\n  }\n  //#endregion\n\n  //#region analyzers\n  /**\n   * Returns an {@link analyzer.Analyzer} instance representing the Analyzer with the\n   * given `analyzerName`.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const analyzer = db.analyzer(\"some-analyzer\");\n   * const info = await analyzer.get();\n   * ```\n   */\n  analyzer(analyzerName: string): Analyzer {\n    analyzerName = analyzerName.normalize(\"NFC\");\n    if (!this._analyzers.has(analyzerName)) {\n      this._analyzers.set(analyzerName, new Analyzer(this, analyzerName));\n    }\n    return this._analyzers.get(analyzerName)!;\n  }\n\n  /**\n   * Creates a new Analyzer with the given `analyzerName` and `options`, then\n   * returns an {@link analyzer.Analyzer} instance for the new Analyzer.\n   *\n   * @param analyzerName - Name of the Analyzer.\n   * @param options - An object defining the properties of the Analyzer.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const analyzer = await db.createAnalyzer(\"potatoes\", { type: \"identity\" });\n   * // the identity Analyzer \"potatoes\" now exists\n   * ```\n   */\n  async createAnalyzer(\n    analyzerName: string,\n    options: CreateAnalyzerOptions\n  ): Promise<Analyzer> {\n    const analyzer = this.analyzer(analyzerName);\n    await analyzer.create(options);\n    return analyzer;\n  }\n\n  /**\n   * Fetches all Analyzers visible in the database and returns an array of\n   * Analyzer descriptions.\n   *\n   * See also {@link Database#analyzers}.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const analyzers = await db.listAnalyzers();\n   * // analyzers is an array of Analyzer descriptions\n   * ```\n   */\n  listAnalyzers(): Promise<AnalyzerDescription[]> {\n    return this.request({ path: \"/_api/analyzer\" }, (res) => res.body.result);\n  }\n\n  /**\n   * Fetches all Analyzers visible in the database and returns an array of\n   * {@link analyzer.Analyzer} instances for those Analyzers.\n   *\n   * See also {@link Database#listAnalyzers}.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const analyzers = await db.analyzers();\n   * // analyzers is an array of Analyzer instances\n   * ```\n   */\n  async analyzers(): Promise<Analyzer[]> {\n    const analyzers = await this.listAnalyzers();\n    return analyzers.map((data) => this.analyzer(data.name));\n  }\n  //#endregion\n\n  /**\n   * Fetches all ArangoDB users visible to the authenticated user and returns\n   * an array of user objects.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const users = await db.listUsers();\n   * // users is an array of user objects\n   * ```\n   */\n  listUsers(): Promise<ArangoUser[]> {\n    return this.request(\n      {\n        path: \"/_api/user\",\n      },\n      (res) => res.body.result\n    );\n  }\n\n  /**\n   * Fetches the user data of a single ArangoDB user.\n   *\n   * @param username - Name of the ArangoDB user to fetch.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const user = await db.getUser(\"steve\");\n   * // user is the user object for the user named \"steve\"\n   * ```\n   */\n  getUser(username: string): Promise<ArangoApiResponse<ArangoUser>> {\n    return this.request({\n      path: `/_api/user/${encodeURIComponent(username)}`,\n    });\n  }\n\n  /**\n   * Creates a new ArangoDB user with the given password.\n   *\n   * @param username - Name of the ArangoDB user to create.\n   * @param passwd - Password of the new ArangoDB user.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const user = await db.createUser(\"steve\", \"hunter2\");\n   * // The user \"steve\" has been created\n   * ```\n   */\n  createUser(\n    username: string,\n    passwd: string\n  ): Promise<ArangoApiResponse<ArangoUser>>;\n  /**\n   * Creates a new ArangoDB user with the given options.\n   *\n   * @param username - Name of the ArangoDB user to create.\n   * @param options - Additional options for creating the ArangoDB user.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const user = await db.createUser(\"steve\", { passwd: \"hunter2\" });\n   * // The user \"steve\" has been created\n   * ```\n   */\n  createUser(\n    username: string,\n    options: UserOptions\n  ): Promise<ArangoApiResponse<ArangoUser>>;\n  createUser(\n    username: string,\n    options: string | UserOptions\n  ): Promise<ArangoApiResponse<ArangoUser>> {\n    if (typeof options === \"string\") {\n      options = { passwd: options };\n    }\n    return this.request(\n      {\n        method: \"POST\",\n        path: \"/_api/user\",\n        body: { user: username, ...options },\n      },\n      (res) => res.body\n    );\n  }\n\n  /**\n   * Sets the password of a given ArangoDB user to the new value.\n   *\n   * @param username - Name of the ArangoDB user to change the password for.\n   * @param passwd - New password for the ArangoDB user.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const user = await db.updateUser(\"steve\", \"hunter2\");\n   * // The user \"steve\" has received a new password\n   * ```\n   */\n  updateUser(\n    username: string,\n    passwd: string\n  ): Promise<ArangoApiResponse<ArangoUser>>;\n  /**\n   * Updates the ArangoDB user with the new options.\n   *\n   * @param username - Name of the ArangoDB user to modify.\n   * @param options - Options of the ArangoDB user to modify.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const user = await db.updateUser(\"steve\", { active: false });\n   * // The user \"steve\" has been set to inactive\n   * ```\n   */\n  updateUser(\n    username: string,\n    options: Partial<UserOptions>\n  ): Promise<ArangoApiResponse<ArangoUser>>;\n  updateUser(\n    username: string,\n    options: string | Partial<UserOptions>\n  ): Promise<ArangoApiResponse<ArangoUser>> {\n    if (typeof options === \"string\") {\n      options = { passwd: options };\n    }\n    return this.request(\n      {\n        method: \"PATCH\",\n        path: `/_api/user/${encodeURIComponent(username)}`,\n        body: options,\n      },\n      (res) => res.body\n    );\n  }\n\n  /**\n   * Replaces the ArangoDB user's option with the new options.\n   *\n   * @param username - Name of the ArangoDB user to modify.\n   * @param options - New options to replace the user's existing options.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const user = await db.replaceUser(\"steve\", { passwd: \"\", active: false });\n   * // The user \"steve\" has been set to inactive with an empty password\n   * ```\n   */\n  replaceUser(\n    username: string,\n    options: UserOptions\n  ): Promise<ArangoApiResponse<ArangoUser>> {\n    if (typeof options === \"string\") {\n      options = { passwd: options };\n    }\n    return this.request(\n      {\n        method: \"PUT\",\n        path: `/_api/user/${encodeURIComponent(username)}`,\n        body: options,\n      },\n      (res) => res.body\n    );\n  }\n\n  /**\n   * Removes the ArangoDB user with the given username from the server.\n   *\n   * @param username - Name of the ArangoDB user to remove.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * await db.removeUser(\"steve\");\n   * // The user \"steve\" has been removed\n   * ```\n   */\n  removeUser(\n    username: string\n  ): Promise<ArangoApiResponse<Record<string, never>>> {\n    return this.request(\n      {\n        method: \"DELETE\",\n        path: `/_api/user/${encodeURIComponent(username)}`,\n      },\n      (res) => res.body\n    );\n  }\n\n  /**\n   * Fetches the given ArangoDB user's access level for the database, or the\n   * given collection in the given database.\n   *\n   * @param username - Name of the ArangoDB user to fetch the access level for.\n   * @param database - Database to fetch the access level for.\n   * @param collection - Collection to fetch the access level for.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const accessLevel = await db.getUserAccessLevel(\"steve\");\n   * // The access level of the user \"steve\" has been fetched for the current\n   * // database.\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const accessLevel = await db.getUserAccessLevel(\"steve\", {\n   *   database: \"staging\"\n   * });\n   * // The access level of the user \"steve\" has been fetched for the \"staging\"\n   * // database.\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const accessLevel = await db.getUserAccessLevel(\"steve\", {\n   *   collection: \"pokemons\"\n   * });\n   * // The access level of the user \"steve\" has been fetched for the\n   * // \"pokemons\" collection in the current database.\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const accessLevel = await db.getUserAccessLevel(\"steve\", {\n   *   database: \"staging\",\n   *   collection: \"pokemons\"\n   * });\n   * // The access level of the user \"steve\" has been fetched for the\n   * // \"pokemons\" collection in the \"staging\" database.\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const staging = db.database(\"staging\");\n   * const accessLevel = await db.getUserAccessLevel(\"steve\", {\n   *   database: staging\n   * });\n   * // The access level of the user \"steve\" has been fetched for the \"staging\"\n   * // database.\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const staging = db.database(\"staging\");\n   * const accessLevel = await db.getUserAccessLevel(\"steve\", {\n   *   collection: staging.collection(\"pokemons\")\n   * });\n   * // The access level of the user \"steve\" has been fetched for the\n   * // \"pokemons\" collection in database \"staging\".\n   * ```\n   */\n  getUserAccessLevel(\n    username: string,\n    { database, collection }: UserAccessLevelOptions\n  ): Promise<AccessLevel> {\n    const databaseName = isArangoDatabase(database)\n      ? database.name\n      : database?.normalize(\"NFC\") ??\n        (isArangoCollection(collection)\n          ? ((collection as any)._db as Database).name\n          : this._name);\n    const suffix = collection\n      ? `/${encodeURIComponent(\n          isArangoCollection(collection)\n            ? collection.name\n            : collection.normalize(\"NFC\")\n        )}`\n      : \"\";\n    return this.request(\n      {\n        path: `/_api/user/${encodeURIComponent(\n          username\n        )}/database/${encodeURIComponent(databaseName)}${suffix}`,\n      },\n      (res) => res.body.result\n    );\n  }\n\n  /**\n   * Sets the given ArangoDB user's access level for the database, or the\n   * given collection in the given database.\n   *\n   * @param username - Name of the ArangoDB user to set the access level for.\n   * @param database - Database to set the access level for.\n   * @param collection - Collection to set the access level for.\n   * @param grant - Access level to set for the given user.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * await db.setUserAccessLevel(\"steve\", { grant: \"rw\" });\n   * // The user \"steve\" now has read-write access to the current database.\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * await db.setUserAccessLevel(\"steve\", {\n   *   database: \"staging\",\n   *   grant: \"rw\"\n   * });\n   * // The user \"steve\" now has read-write access to the \"staging\" database.\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * await db.setUserAccessLevel(\"steve\", {\n   *   collection: \"pokemons\",\n   *   grant: \"rw\"\n   * });\n   * // The user \"steve\" now has read-write access to the \"pokemons\" collection\n   * // in the current database.\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * await db.setUserAccessLevel(\"steve\", {\n   *   database: \"staging\",\n   *   collection: \"pokemons\",\n   *   grant: \"rw\"\n   * });\n   * // The user \"steve\" now has read-write access to the \"pokemons\" collection\n   * // in the \"staging\" database.\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const staging = db.database(\"staging\");\n   * await db.setUserAccessLevel(\"steve\", {\n   *   database: staging,\n   *   grant: \"rw\"\n   * });\n   * // The user \"steve\" now has read-write access to the \"staging\" database.\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const staging = db.database(\"staging\");\n   * await db.setUserAccessLevel(\"steve\", {\n   *   collection: staging.collection(\"pokemons\"),\n   *   grant: \"rw\"\n   * });\n   * // The user \"steve\" now has read-write access to the \"pokemons\" collection\n   * // in database \"staging\".\n   * ```\n   */\n  setUserAccessLevel(\n    username: string,\n    {\n      database,\n      collection,\n      grant,\n    }: UserAccessLevelOptions & { grant: AccessLevel }\n  ): Promise<ArangoApiResponse<Record<string, AccessLevel>>> {\n    const databaseName = isArangoDatabase(database)\n      ? database.name\n      : database?.normalize(\"NFC\") ??\n        (isArangoCollection(collection)\n          ? ((collection as any)._db as Database).name\n          : this._name);\n    const suffix = collection\n      ? `/${encodeURIComponent(\n          isArangoCollection(collection)\n            ? collection.name\n            : collection.normalize(\"NFC\")\n        )}`\n      : \"\";\n    return this.request(\n      {\n        method: \"PUT\",\n        path: `/_api/user/${encodeURIComponent(\n          username\n        )}/database/${encodeURIComponent(databaseName)}${suffix}`,\n        body: { grant },\n      },\n      (res) => res.body\n    );\n  }\n\n  /**\n   * Clears the given ArangoDB user's access level for the database, or the\n   * given collection in the given database.\n   *\n   * @param username - Name of the ArangoDB user to clear the access level for.\n   * @param database - Database to clear the access level for.\n   * @param collection - Collection to clear the access level for.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * await db.clearUserAccessLevel(\"steve\");\n   * // The access level of the user \"steve\" has been cleared for the current\n   * // database.\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * await db.clearUserAccessLevel(\"steve\", { database: \"staging\" });\n   * // The access level of the user \"steve\" has been cleared for the \"staging\"\n   * // database.\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * await db.clearUserAccessLevel(\"steve\", { collection: \"pokemons\" });\n   * // The access level of the user \"steve\" has been cleared for the\n   * // \"pokemons\" collection in the current database.\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * await db.clearUserAccessLevel(\"steve\", {\n   *   database: \"staging\",\n   *   collection: \"pokemons\"\n   * });\n   * // The access level of the user \"steve\" has been cleared for the\n   * // \"pokemons\" collection in the \"staging\" database.\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const staging = db.database(\"staging\");\n   * await db.clearUserAccessLevel(\"steve\", { database: staging });\n   * // The access level of the user \"steve\" has been cleared for the \"staging\"\n   * // database.\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const staging = db.database(\"staging\");\n   * await db.clearUserAccessLevel(\"steve\", {\n   *   collection: staging.collection(\"pokemons\")\n   * });\n   * // The access level of the user \"steve\" has been cleared for the\n   * // \"pokemons\" collection in database \"staging\".\n   * ```\n   */\n  clearUserAccessLevel(\n    username: string,\n    { database, collection }: UserAccessLevelOptions\n  ): Promise<ArangoApiResponse<Record<string, AccessLevel>>> {\n    const databaseName = isArangoDatabase(database)\n      ? database.name\n      : database?.normalize(\"NFC\") ??\n        (isArangoCollection(collection)\n          ? ((collection as any)._db as Database).name\n          : this._name);\n    const suffix = collection\n      ? `/${encodeURIComponent(\n          isArangoCollection(collection)\n            ? collection.name\n            : collection.normalize(\"NFC\")\n        )}`\n      : \"\";\n    return this.request(\n      {\n        method: \"DELETE\",\n        path: `/_api/user/${encodeURIComponent(\n          username\n        )}/database/${encodeURIComponent(databaseName)}${suffix}`,\n      },\n      (res) => res.body\n    );\n  }\n\n  /**\n   * Fetches an object mapping names of databases to the access level of the\n   * given ArangoDB user for those databases.\n   *\n   * @param username - Name of the ArangoDB user to fetch the access levels for.\n   * @param full - Whether access levels for collections should be included.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const accessLevels = await db.getUserDatabases(\"steve\");\n   * for (const [databaseName, accessLevel] of Object.entries(accessLevels)) {\n   *   console.log(`${databaseName}: ${accessLevel}`);\n   * }\n   * ```\n   */\n  getUserDatabases(\n    username: string,\n    full?: false\n  ): Promise<Record<string, AccessLevel>>;\n  /**\n   * Fetches an object mapping names of databases to the access level of the\n   * given ArangoDB user for those databases and the collections within each\n   * database.\n   *\n   * @param username - Name of the ArangoDB user to fetch the access levels for.\n   * @param full - Whether access levels for collections should be included.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const accessLevels = await db.getUserDatabases(\"steve\", true);\n   * for (const [databaseName, obj] of Object.entries(accessLevels)) {\n   *   console.log(`${databaseName}: ${obj.permission}`);\n   *   for (const [collectionName, accessLevel] of Object.entries(obj.collections)) {\n   *     console.log(`${databaseName}/${collectionName}: ${accessLevel}`);\n   *   }\n   * }\n   * ```\n   */\n  getUserDatabases(\n    username: string,\n    full: true\n  ): Promise<\n    Record<\n      string,\n      {\n        permission: AccessLevel;\n        collections: Record<string, AccessLevel | \"undefined\">;\n      }\n    >\n  >;\n  getUserDatabases(username: string, full?: boolean) {\n    return this.request(\n      {\n        path: `/_api/user/${encodeURIComponent(username)}/database`,\n        qs: { full },\n      },\n      (res) => res.body.result\n    );\n  }\n  //#endregion\n\n  //#region transactions\n  /**\n   * Performs a server-side JavaScript transaction and returns its return\n   * value.\n   *\n   * Collections can be specified as collection names (strings) or objects\n   * implementing the {@link collection.ArangoCollection} interface: `Collection`,\n   * {@link graph.GraphVertexCollection}, {@link graph.GraphEdgeCollection} as well as\n   * (in TypeScript) {@link collection.DocumentCollection} and {@link collection.EdgeCollection}.\n   *\n   * **Note**: The `action` function will be evaluated and executed on the\n   * server inside ArangoDB's embedded JavaScript environment and can not\n   * access any values other than those passed via the `params` option.\n   *\n   * See the official ArangoDB documentation for\n   * [the JavaScript `@arangodb` module](https://www.arangodb.com/docs/stable/appendix-java-script-modules-arango-db.html)\n   * for information about accessing the database from within ArangoDB's\n   * server-side JavaScript environment.\n   *\n   * @param collections - Collections involved in the transaction.\n   * @param action - A string evaluating to a JavaScript function to be\n   * executed on the server.\n   * @param options - Options for the transaction. If `options.allowImplicit`\n   * is specified, it will be used if `collections.allowImplicit` was not\n   * specified.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   *\n   * const action = `\n   *   function(params) {\n   *     // This code will be executed inside ArangoDB!\n   *     const { query } = require(\"@arangodb\");\n   *     return query\\`\n   *         FOR user IN _users\n   *         FILTER user.age > ${params.age}\n   *         RETURN u.user\n   *       \\`.toArray();\n   *   }\n   * `);\n   *\n   * const result = await db.executeTransaction({\n   *   read: [\"_users\"]\n   * }, action, {\n   *   params: { age: 12 }\n   * });\n   * // result contains the return value of the action\n   * ```\n   */\n  executeTransaction(\n    collections: TransactionCollections & { allowImplicit?: boolean },\n    action: string,\n    options?: TransactionOptions & { params?: any }\n  ): Promise<any>;\n  /**\n   * Performs a server-side transaction and returns its return value.\n   *\n   * Collections can be specified as collection names (strings) or objects\n   * implementing the {@link collection.ArangoCollection} interface: `Collection`,\n   * {@link graph.GraphVertexCollection}, {@link graph.GraphEdgeCollection} as well as\n   * (in TypeScript) {@link collection.DocumentCollection} and {@link collection.EdgeCollection}.\n   *\n   * **Note**: The `action` function will be evaluated and executed on the\n   * server inside ArangoDB's embedded JavaScript environment and can not\n   * access any values other than those passed via the `params` option.\n   * See the official ArangoDB documentation for\n   * [the JavaScript `@arangodb` module](https://www.arangodb.com/docs/stable/appendix-java-script-modules-arango-db.html)\n   * for information about accessing the database from within ArangoDB's\n   * server-side JavaScript environment.\n   *\n   * @param collections - Collections that can be read from and written to\n   * during the transaction.\n   * @param action - A string evaluating to a JavaScript function to be\n   * executed on the server.\n   * @param options - Options for the transaction.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   *\n   * const action = `\n   *   function(params) {\n   *     // This code will be executed inside ArangoDB!\n   *     const { query } = require(\"@arangodb\");\n   *     return query\\`\n   *         FOR user IN _users\n   *         FILTER user.age > ${params.age}\n   *         RETURN u.user\n   *       \\`.toArray();\n   *   }\n   * `);\n   *\n   * const result = await db.executeTransaction([\"_users\"], action, {\n   *   params: { age: 12 }\n   * });\n   * // result contains the return value of the action\n   * ```\n   */\n  executeTransaction(\n    collections: (string | ArangoCollection)[],\n    action: string,\n    options?: TransactionOptions & { params?: any }\n  ): Promise<any>;\n  /**\n   * Performs a server-side transaction and returns its return value.\n   *\n   * The Collection can be specified as a collection name (string) or an object\n   * implementing the {@link collection.ArangoCollection} interface: `Collection`,\n   * {@link graph.GraphVertexCollection}, {@link graph.GraphEdgeCollection} as well as\n   * (in TypeScript) {@link collection.DocumentCollection} and {@link collection.EdgeCollection}.\n   *\n   * **Note**: The `action` function will be evaluated and executed on the\n   * server inside ArangoDB's embedded JavaScript environment and can not\n   * access any values other than those passed via the `params` option.\n   * See the official ArangoDB documentation for\n   * [the JavaScript `@arangodb` module](https://www.arangodb.com/docs/stable/appendix-java-script-modules-arango-db.html)\n   * for information about accessing the database from within ArangoDB's\n   * server-side JavaScript environment.\n   *\n   * @param collection - A collection that can be read from and written to\n   * during the transaction.\n   * @param action - A string evaluating to a JavaScript function to be\n   * executed on the server.\n   * @param options - Options for the transaction.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   *\n   * const action = `\n   *   function(params) {\n   *     // This code will be executed inside ArangoDB!\n   *     const { query } = require(\"@arangodb\");\n   *     return query\\`\n   *         FOR user IN _users\n   *         FILTER user.age > ${params.age}\n   *         RETURN u.user\n   *       \\`.toArray();\n   *   }\n   * `);\n   *\n   * const result = await db.executeTransaction(\"_users\", action, {\n   *   params: { age: 12 }\n   * });\n   * // result contains the return value of the action\n   * ```\n   */\n  executeTransaction(\n    collection: string | ArangoCollection,\n    action: string,\n    options?: TransactionOptions & { params?: any }\n  ): Promise<any>;\n  executeTransaction(\n    collections:\n      | (TransactionCollections & { allowImplicit?: boolean })\n      | (string | ArangoCollection)[]\n      | string\n      | ArangoCollection,\n    action: string,\n    options: TransactionOptions & { params?: any } = {}\n  ): Promise<any> {\n    const { allowDirtyRead = undefined, ...opts } = options;\n    return this.request(\n      {\n        method: \"POST\",\n        path: \"/_api/transaction\",\n        allowDirtyRead,\n        body: {\n          collections: coerceTransactionCollections(collections),\n          action,\n          ...opts,\n        },\n      },\n      (res) => res.body.result\n    );\n  }\n\n  /**\n   * Returns a {@link transaction.Transaction} instance for an existing streaming\n   * transaction with the given `id`.\n   *\n   * See also {@link Database#beginTransaction}.\n   *\n   * @param id - The `id` of an existing stream transaction.\n   *\n   * @example\n   * ```js\n   * const trx1 = await db.beginTransaction(collections);\n   * const id = trx1.id;\n   * // later\n   * const trx2 = db.transaction(id);\n   * await trx2.commit();\n   * ```\n   */\n  transaction(transactionId: string): Transaction {\n    return new Transaction(this, transactionId);\n  }\n\n  /**\n   * Begins a new streaming transaction for the given collections, then returns\n   * a {@link transaction.Transaction} instance for the transaction.\n   *\n   * Collections can be specified as collection names (strings) or objects\n   * implementing the {@link collection.ArangoCollection} interface: `Collection`,\n   * {@link graph.GraphVertexCollection}, {@link graph.GraphEdgeCollection} as\n   * well as (in TypeScript) {@link collection.DocumentCollection} and\n   * {@link collection.EdgeCollection}.\n   *\n   * @param collections - Collections involved in the transaction.\n   * @param options - Options for the transaction.\n   *\n   * @example\n   * ```js\n   * const vertices = db.collection(\"vertices\");\n   * const edges = db.collection(\"edges\");\n   * const trx = await db.beginTransaction({\n   *   read: [\"vertices\"],\n   *   write: [edges] // collection instances can be passed directly\n   * });\n   * const start = await trx.step(() => vertices.document(\"a\"));\n   * const end = await trx.step(() => vertices.document(\"b\"));\n   * await trx.step(() => edges.save({ _from: start._id, _to: end._id }));\n   * await trx.commit();\n   * ```\n   */\n  beginTransaction(\n    collections: TransactionCollections,\n    options?: TransactionOptions\n  ): Promise<Transaction>;\n  /**\n   * Begins a new streaming transaction for the given collections, then returns\n   * a {@link transaction.Transaction} instance for the transaction.\n   *\n   * Collections can be specified as collection names (strings) or objects\n   * implementing the {@link collection.ArangoCollection} interface: `Collection`,\n   * {@link graph.GraphVertexCollection}, {@link graph.GraphEdgeCollection} as well as\n   * (in TypeScript) {@link collection.DocumentCollection} and {@link collection.EdgeCollection}.\n   *\n   * @param collections - Collections that can be read from and written to\n   * during the transaction.\n   * @param options - Options for the transaction.\n   *\n   * @example\n   * ```js\n   * const vertices = db.collection(\"vertices\");\n   * const edges = db.collection(\"edges\");\n   * const trx = await db.beginTransaction([\n   *   \"vertices\",\n   *   edges // collection instances can be passed directly\n   * ]);\n   * const start = await trx.step(() => vertices.document(\"a\"));\n   * const end = await trx.step(() => vertices.document(\"b\"));\n   * await trx.step(() => edges.save({ _from: start._id, _to: end._id }));\n   * await trx.commit();\n   * ```\n   */\n  beginTransaction(\n    collections: (string | ArangoCollection)[],\n    options?: TransactionOptions\n  ): Promise<Transaction>;\n  /**\n   * Begins a new streaming transaction for the given collections, then returns\n   * a {@link transaction.Transaction} instance for the transaction.\n   *\n   * The Collection can be specified as a collection name (string) or an object\n   * implementing the {@link collection.ArangoCollection} interface: `Collection`,\n   * {@link graph.GraphVertexCollection}, {@link graph.GraphEdgeCollection} as well as\n   * (in TypeScript) {@link collection.DocumentCollection} and {@link collection.EdgeCollection}.\n   *\n   * @param collection - A collection that can be read from and written to\n   * during the transaction.\n   * @param options - Options for the transaction.\n   *\n   * @example\n   * ```js\n   * const vertices = db.collection(\"vertices\");\n   * const start = vertices.document(\"a\");\n   * const end = vertices.document(\"b\");\n   * const edges = db.collection(\"edges\");\n   * const trx = await db.beginTransaction(\n   *   edges // collection instances can be passed directly\n   * );\n   * await trx.step(() => edges.save({ _from: start._id, _to: end._id }));\n   * await trx.commit();\n   * ```\n   */\n  beginTransaction(\n    collection: string | ArangoCollection,\n    options?: TransactionOptions\n  ): Promise<Transaction>;\n  beginTransaction(\n    collections:\n      | TransactionCollections\n      | (string | ArangoCollection)[]\n      | string\n      | ArangoCollection,\n    options: TransactionOptions = {}\n  ): Promise<Transaction> {\n    const { allowDirtyRead = undefined, ...opts } = options;\n    return this.request(\n      {\n        method: \"POST\",\n        path: \"/_api/transaction/begin\",\n        allowDirtyRead,\n        body: {\n          collections: coerceTransactionCollections(collections),\n          ...opts,\n        },\n      },\n      (res) => new Transaction(this, res.body.result.id)\n    );\n  }\n\n  /**\n   * Begins and commits a transaction using the given callback. Individual\n   * requests that are part of the transaction need to be wrapped in the step\n   * function passed into the callback. If the promise returned by the callback\n   * is rejected, the transaction will be aborted.\n   *\n   * Collections can be specified as collection names (strings) or objects\n   * implementing the {@link collection.ArangoCollection} interface: `Collection`,\n   * {@link graph.GraphVertexCollection}, {@link graph.GraphEdgeCollection} as\n   * well as (in TypeScript) {@link collection.DocumentCollection} and\n   * {@link collection.EdgeCollection}.\n   *\n   * @param collections - Collections involved in the transaction.\n   * @param callback - Callback function executing the transaction steps.\n   * @param options - Options for the transaction.\n   *\n   * @example\n   * ```js\n   * const vertices = db.collection(\"vertices\");\n   * const edges = db.collection(\"edges\");\n   * await db.withTransaction(\n   *   {\n   *     read: [\"vertices\"],\n   *     write: [edges] // collection instances can be passed directly\n   *   },\n   *   async (step) => {\n   *     const start = await step(() => vertices.document(\"a\"));\n   *     const end = await step(() => vertices.document(\"b\"));\n   *     await step(() => edges.save({ _from: start._id, _to: end._id }));\n   *   }\n   * );\n   * ```\n   */\n  withTransaction<T>(\n    collections: TransactionCollections,\n    callback: (step: Transaction[\"step\"]) => Promise<T>,\n    options?: TransactionOptions\n  ): Promise<T>;\n  /**\n   * Begins and commits a transaction using the given callback. Individual\n   * requests that are part of the transaction need to be wrapped in the step\n   * function passed into the callback. If the promise returned by the callback\n   * is rejected, the transaction will be aborted.\n   *\n   * Collections can be specified as collection names (strings) or objects\n   * implementing the {@link collection.ArangoCollection} interface: `Collection`,\n   * {@link graph.GraphVertexCollection}, {@link graph.GraphEdgeCollection} as well as\n   * (in TypeScript) {@link collection.DocumentCollection} and {@link collection.EdgeCollection}.\n   *\n   * @param collections - Collections that can be read from and written to\n   * during the transaction.\n   * @param callback - Callback function executing the transaction steps.\n   * @param options - Options for the transaction.\n   *\n   * @example\n   * ```js\n   * const vertices = db.collection(\"vertices\");\n   * const edges = db.collection(\"edges\");\n   * await db.withTransaction(\n   *   [\n   *     \"vertices\",\n   *     edges, // collection instances can be passed directly\n   *   ],\n   *   async (step) => {\n   *     const start = await step(() => vertices.document(\"a\"));\n   *     const end = await step(() => vertices.document(\"b\"));\n   *     await step(() => edges.save({ _from: start._id, _to: end._id }));\n   *   }\n   * );\n   * ```\n   */\n  withTransaction<T>(\n    collections: (string | ArangoCollection)[],\n    callback: (step: Transaction[\"step\"]) => Promise<T>,\n    options?: TransactionOptions\n  ): Promise<T>;\n  /**\n   * Begins and commits a transaction using the given callback. Individual\n   * requests that are part of the transaction need to be wrapped in the step\n   * function passed into the callback. If the promise returned by the callback\n   * is rejected, the transaction will be aborted.\n   *\n   * The Collection can be specified as a collection name (string) or an object\n   * implementing the {@link collection.ArangoCollection} interface: `Collection`,\n   * {@link graph.GraphVertexCollection}, {@link graph.GraphEdgeCollection} as well as\n   * (in TypeScript) {@link collection.DocumentCollection} and {@link collection.EdgeCollection}.\n   *\n   * @param collection - A collection that can be read from and written to\n   * during the transaction.\n   * @param callback - Callback function executing the transaction steps.\n   * @param options - Options for the transaction.\n   *\n   * @example\n   * ```js\n   * const vertices = db.collection(\"vertices\");\n   * const start = vertices.document(\"a\");\n   * const end = vertices.document(\"b\");\n   * const edges = db.collection(\"edges\");\n   * await db.withTransaction(\n   *   edges, // collection instances can be passed directly\n   *   async (step) => {\n   *     await step(() => edges.save({ _from: start._id, _to: end._id }));\n   *   }\n   * );\n   * ```\n   */\n  withTransaction<T>(\n    collection: string | ArangoCollection,\n    callback: (step: Transaction[\"step\"]) => Promise<T>,\n    options?: TransactionOptions\n  ): Promise<T>;\n  async withTransaction<T>(\n    collections:\n      | TransactionCollections\n      | (string | ArangoCollection)[]\n      | string\n      | ArangoCollection,\n    callback: (step: Transaction[\"step\"]) => Promise<T>,\n    options: TransactionOptions = {}\n  ): Promise<T> {\n    const trx = await this.beginTransaction(\n      collections as TransactionCollections,\n      options\n    );\n    try {\n      const result = await callback((fn) => trx.step(fn));\n      await trx.commit();\n      return result;\n    } catch (e) {\n      try {\n        await trx.abort();\n      } catch {}\n      throw e;\n    }\n  }\n\n  /**\n   * Fetches all active transactions from the database and returns an array of\n   * transaction descriptions.\n   *\n   * See also {@link Database#transactions}.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const transactions = await db.listTransactions();\n   * // transactions is an array of transaction descriptions\n   * ```\n   */\n  listTransactions(): Promise<TransactionDetails[]> {\n    return this._connection.request(\n      { path: \"/_api/transaction\" },\n      (res) => res.body.transactions\n    );\n  }\n\n  /**\n   * Fetches all active transactions from the database and returns an array of\n   * {@link transaction.Transaction} instances for those transactions.\n   *\n   * See also {@link Database#listTransactions}.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const transactions = await db.transactions();\n   * // transactions is an array of transactions\n   * ```\n   */\n  async transactions(): Promise<Transaction[]> {\n    const transactions = await this.listTransactions();\n    return transactions.map((data) => this.transaction(data.id));\n  }\n  //#endregion\n\n  //#region queries\n  /**\n   * Performs a database query using the given `query`, then returns a new\n   * {@link cursor.ArrayCursor} instance for the result set.\n   *\n   * See the {@link aql!aql} template string handler for information about how\n   * to create a query string without manually defining bind parameters nor\n   * having to worry about escaping variables.\n   *\n   * **Note**: When executing a query in a streaming transaction using the\n   * `step` method, the resulting cursor will be bound to that transaction and\n   * you do not need to use the `step` method to consume it.\n   *\n   * @param query - An object containing an AQL query string and bind\n   * parameters, e.g. the object returned from an {@link aql!aql} template string.\n   * @param options - Options for the query execution.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const active = true;\n   * const Users = db.collection(\"_users\");\n   *\n   * // Using an aql template string:\n   * // Bind parameters are automatically extracted and arangojs collections\n   * // are automatically passed as collection bind parameters.\n   * const cursor = await db.query(aql`\n   *   FOR u IN ${Users}\n   *   FILTER u.authData.active == ${active}\n   *   RETURN u.user\n   * `);\n   * // cursor is a cursor for the query result\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const active = true;\n   * const Users = db.collection(\"_users\");\n   *\n   * // Using an object with a regular multi-line string\n   * const cursor = await db.query({\n   *   query: `\n   *     FOR u IN @@users\n   *     FILTER u.authData.active == @active\n   *     RETURN u.user\n   *   `,\n   *   bindVars: { active: active, \"@users\": Users.name }\n   * });\n   * ```\n   */\n  query<T = any>(\n    query: AqlQuery<T>,\n    options?: QueryOptions\n  ): Promise<ArrayCursor<T>>;\n  /**\n   * Performs a database query using the given `query` and `bindVars`, then\n   * returns a new {@link cursor.ArrayCursor} instance for the result set.\n   *\n   * See the {@link aql!aql} template string handler for a safer and easier\n   * alternative to passing strings directly.\n   *\n   * **Note**: When executing a query in a streaming transaction using the\n   * `step` method, the resulting cursor will be bound to that transaction and\n   * you do not need to use the `step` method to consume it.\n   *\n   * @param query - An AQL query string.\n   * @param bindVars - An object defining bind parameters for the query.\n   * @param options - Options for the query execution.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const active = true;\n   * const Users = db.collection(\"_users\");\n   *\n   * const cursor = await db.query(\n   *   // A normal multi-line string\n   *   `\n   *     FOR u IN @@users\n   *     FILTER u.authData.active == @active\n   *     RETURN u.user\n   *   `,\n   *   { active: active, \"@users\": Users.name }\n   * );\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const active = true;\n   * const Users = db.collection(\"_users\");\n   *\n   * const cursor = await db.query(\n   *   // An AQL literal created from a normal multi-line string\n   *   aql.literal(`\n   *     FOR u IN @@users\n   *     FILTER u.authData.active == @active\n   *     RETURN u.user\n   *   `),\n   *   { active: active, \"@users\": Users.name }\n   * );\n   * ```\n   */\n  query<T = any>(\n    query: string | AqlLiteral,\n    bindVars?: Record<string, any>,\n    options?: QueryOptions\n  ): Promise<ArrayCursor<T>>;\n  query<T = any>(\n    query: string | AqlQuery | AqlLiteral,\n    bindVars?: Record<string, any>,\n    options: QueryOptions = {}\n  ): Promise<ArrayCursor<T>> {\n    if (isAqlQuery(query)) {\n      options = bindVars ?? {};\n      bindVars = query.bindVars;\n      query = query.query;\n    } else if (isAqlLiteral(query)) {\n      query = query.toAQL();\n    }\n    const {\n      allowDirtyRead,\n      retryOnConflict,\n      count,\n      batchSize,\n      cache,\n      memoryLimit,\n      ttl,\n      timeout,\n      ...opts\n    } = options;\n    return this.request(\n      {\n        method: \"POST\",\n        path: \"/_api/cursor\",\n        body: {\n          query,\n          bindVars,\n          count,\n          batchSize,\n          cache,\n          memoryLimit,\n          ttl,\n          options: opts,\n        },\n        allowDirtyRead,\n        retryOnConflict,\n        timeout,\n      },\n      (res) =>\n        new BatchedArrayCursor<T>(\n          this,\n          res.body,\n          res.arangojsHostUrl,\n          allowDirtyRead\n        ).items\n    );\n  }\n\n  /**\n   * Explains a database query using the given `query`.\n   *\n   * See the {@link aql!aql} template string handler for information about how\n   * to create a query string without manually defining bind parameters nor\n   * having to worry about escaping variables.\n   *\n   * @param query - An object containing an AQL query string and bind\n   * parameters, e.g. the object returned from an {@link aql!aql} template string.\n   * @param options - Options for explaining the query.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * const explanation = await db.explain(aql`\n   *   FOR doc IN ${collection}\n   *   FILTER doc.flavor == \"strawberry\"\n   *   RETURN doc._key\n   * `);\n   * ```\n   */\n  explain(\n    query: AqlQuery,\n    options?: ExplainOptions & { allPlans?: false }\n  ): Promise<ArangoApiResponse<SingleExplainResult>>;\n  /**\n   * Explains a database query using the given `query`.\n   *\n   * See the {@link aql!aql} template string handler for information about how\n   * to create a query string without manually defining bind parameters nor\n   * having to worry about escaping variables.\n   *\n   * @param query - An object containing an AQL query string and bind\n   * parameters, e.g. the object returned from an {@link aql!aql} template string.\n   * @param options - Options for explaining the query.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * const explanation = await db.explain(\n   *   aql`\n   *     FOR doc IN ${collection}\n   *     FILTER doc.flavor == \"strawberry\"\n   *     RETURN doc._key\n   *   `,\n   *   { allPlans: true }\n   * );\n   * ```\n   */\n  explain(\n    query: AqlQuery,\n    options?: ExplainOptions & { allPlans: true }\n  ): Promise<ArangoApiResponse<MultiExplainResult>>;\n  /**\n   * Explains a database query using the given `query` and `bindVars`.\n   *\n   * See the {@link aql!aql} template string handler for a safer and easier\n   * alternative to passing strings directly.\n   *\n   * @param query - An AQL query string.\n   * @param bindVars - An object defining bind parameters for the query.\n   * @param options - Options for explaining the query.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * const explanation = await db.explain(\n   *   `\n   *     FOR doc IN @@collection\n   *     FILTER doc.flavor == \"strawberry\"\n   *     RETURN doc._key\n   *   `,\n   *   { \"@collection\": collection.name }\n   * );\n   * ```\n   */\n  explain(\n    query: string | AqlLiteral,\n    bindVars?: Record<string, any>,\n    options?: ExplainOptions & { allPlans?: false }\n  ): Promise<ArangoApiResponse<SingleExplainResult>>;\n  /**\n   * Explains a database query using the given `query` and `bindVars`.\n   *\n   * See the {@link aql!aql} template string handler for a safer and easier\n   * alternative to passing strings directly.\n   *\n   * @param query - An AQL query string.\n   * @param bindVars - An object defining bind parameters for the query.\n   * @param options - Options for explaining the query.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * const explanation = await db.explain(\n   *   `\n   *     FOR doc IN @@collection\n   *     FILTER doc.flavor == \"strawberry\"\n   *     RETURN doc._key\n   *   `,\n   *   { \"@collection\": collection.name },\n   *   { allPlans: true }\n   * );\n   * ```\n   */\n  explain(\n    query: string | AqlLiteral,\n    bindVars?: Record<string, any>,\n    options?: ExplainOptions & { allPlans: true }\n  ): Promise<ArangoApiResponse<MultiExplainResult>>;\n  explain(\n    query: string | AqlQuery | AqlLiteral,\n    bindVars?: Record<string, any>,\n    options?: ExplainOptions\n  ): Promise<ArangoApiResponse<SingleExplainResult | MultiExplainResult>> {\n    if (isAqlQuery(query)) {\n      options = bindVars;\n      bindVars = query.bindVars;\n      query = query.query;\n    } else if (isAqlLiteral(query)) {\n      query = query.toAQL();\n    }\n    return this.request({\n      method: \"POST\",\n      path: \"/_api/explain\",\n      body: { query, bindVars, options },\n    });\n  }\n\n  /**\n   * Parses the given query and returns the result.\n   *\n   * See the {@link aql!aql} template string handler for information about how\n   * to create a query string without manually defining bind parameters nor\n   * having to worry about escaping variables.\n   *\n   * @param query - An AQL query string or an object containing an AQL query\n   * string and bind parameters, e.g. the object returned from an {@link aql!aql}\n   * template string.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"some-collection\");\n   * const ast = await db.parse(aql`\n   *   FOR doc IN ${collection}\n   *   FILTER doc.flavor == \"strawberry\"\n   *   RETURN doc._key\n   * `);\n   * ```\n   */\n  parse(query: string | AqlQuery | AqlLiteral): Promise<ParseResult> {\n    if (isAqlQuery(query)) {\n      query = query.query;\n    } else if (isAqlLiteral(query)) {\n      query = query.toAQL();\n    }\n    return this.request({\n      method: \"POST\",\n      path: \"/_api/query\",\n      body: { query },\n    });\n  }\n\n  /**\n   * Fetches the available optimizer rules.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const rules = await db.queryRules();\n   * for (const rule of rules) {\n   *   console.log(rule.name);\n   * }\n   * ```\n   */\n  queryRules(): Promise<QueryOptimizerRule[]> {\n    return this.request({\n      path: \"/_api/query/rules\",\n    });\n  }\n\n  /**\n   * Fetches the query tracking properties.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const tracking = await db.queryTracking();\n   * console.log(tracking.enabled);\n   * ```\n   */\n  queryTracking(): Promise<QueryTracking>;\n  /**\n   * Modifies the query tracking properties.\n   *\n   * @param options - Options for query tracking.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * // track up to 5 slow queries exceeding 5 seconds execution time\n   * await db.setQueryTracking({\n   *   enabled: true,\n   *   trackSlowQueries: true,\n   *   maxSlowQueries: 5,\n   *   slowQueryThreshold: 5\n   * });\n   * ```\n   */\n  queryTracking(options: QueryTrackingOptions): Promise<QueryTracking>;\n  queryTracking(options?: QueryTrackingOptions): Promise<QueryTracking> {\n    return this.request(\n      options\n        ? {\n            method: \"PUT\",\n            path: \"/_api/query/properties\",\n            body: options,\n          }\n        : {\n            method: \"GET\",\n            path: \"/_api/query/properties\",\n          }\n    );\n  }\n\n  /**\n   * Fetches a list of information for all currently running queries.\n   *\n   * See also {@link Database#listSlowQueries} and {@link Database#killQuery}.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const queries = await db.listRunningQueries();\n   * ```\n   */\n  listRunningQueries(): Promise<QueryInfo[]> {\n    return this.request({\n      method: \"GET\",\n      path: \"/_api/query/current\",\n    });\n  }\n\n  /**\n   * Fetches a list of information for all recent slow queries.\n   *\n   * See also {@link Database#listRunningQueries} and\n   * {@link Database#clearSlowQueries}.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const queries = await db.listSlowQueries();\n   * // Only works if slow query tracking is enabled\n   * ```\n   */\n  listSlowQueries(): Promise<QueryInfo[]> {\n    return this.request({\n      method: \"GET\",\n      path: \"/_api/query/slow\",\n    });\n  }\n\n  /**\n   * Clears the list of recent slow queries.\n   *\n   * See also {@link Database#listSlowQueries}.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * await db.clearSlowQueries();\n   * // Slow query list is now cleared\n   * ```\n   */\n  clearSlowQueries(): Promise<void> {\n    return this.request(\n      {\n        method: \"DELETE\",\n        path: \"/_api/query/slow\",\n      },\n      () => undefined\n    );\n  }\n\n  /**\n   * Kills a running query with the given `queryId`.\n   *\n   * See also {@link Database#listRunningQueries}.\n   *\n   * @param queryId - The ID of a currently running query.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const queries = await db.listRunningQueries();\n   * await Promise.all(queries.map(\n   *   async (query) => {\n   *     if (query.state === \"executing\") {\n   *       await db.killQuery(query.id);\n   *     }\n   *   }\n   * ));\n   * ```\n   */\n  killQuery(queryId: string): Promise<void> {\n    return this.request(\n      {\n        method: \"DELETE\",\n        path: `/_api/query/${encodeURIComponent(queryId)}`,\n      },\n      () => undefined\n    );\n  }\n  //#endregion\n\n  //#region functions\n  /**\n   * Fetches a list of all AQL user functions registered with the database.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const functions = await db.listFunctions();\n   * const names = functions.map(fn => fn.name);\n   * ```\n   */\n  listFunctions(): Promise<AqlUserFunction[]> {\n    return this.request(\n      { path: \"/_api/aqlfunction\" },\n      (res) => res.body.result\n    );\n  }\n\n  /**\n   * Creates an AQL user function with the given _name_ and _code_ if it does\n   * not already exist or replaces it if a function with the same name already\n   * existed.\n   *\n   * @param name - A valid AQL function name. The function name must consist\n   * of at least two alphanumeric identifiers separated with double colons.\n   * @param code - A string evaluating to a JavaScript function (not a\n   * JavaScript function object).\n   * @param isDeterministic - If set to `true`, the function is expected to\n   * always return the same result for equivalent inputs. This option currently\n   * has no effect but may allow for optimizations in the future.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * await db.createFunction(\n   *   \"ACME::ACCOUNTING::CALCULATE_VAT\",\n   *   \"(price) => price * 0.19\"\n   * );\n   * // Use the new function in an AQL query with template handler:\n   * const cursor = await db.query(aql`\n   *   FOR product IN products\n   *   RETURN MERGE(\n   *     { vat: ACME::ACCOUNTING::CALCULATE_VAT(product.price) },\n   *     product\n   *   )\n   * `);\n   * // cursor is a cursor for the query result\n   * ```\n   */\n  createFunction(\n    name: string,\n    code: string,\n    isDeterministic: boolean = false\n  ): Promise<ArangoApiResponse<{ isNewlyCreated: boolean }>> {\n    return this.request({\n      method: \"POST\",\n      path: \"/_api/aqlfunction\",\n      body: { name, code, isDeterministic },\n    });\n  }\n\n  /**\n   * Deletes the AQL user function with the given name from the database.\n   *\n   * @param name - The name of the user function to drop.\n   * @param group - If set to `true`, all functions with a name starting with\n   * `name` will be deleted, otherwise only the function with the exact name\n   * will be deleted.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * await db.dropFunction(\"ACME::ACCOUNTING::CALCULATE_VAT\");\n   * // the function no longer exists\n   * ```\n   */\n  dropFunction(\n    name: string,\n    group: boolean = false\n  ): Promise<ArangoApiResponse<{ deletedCount: number }>> {\n    return this.request({\n      method: \"DELETE\",\n      path: `/_api/aqlfunction/${encodeURIComponent(name)}`,\n      qs: { group },\n    });\n  }\n  //#endregion\n\n  //#region services\n  /**\n   * Fetches a list of all installed service.\n   *\n   * @param excludeSystem - Whether system services should be excluded.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const services = await db.listServices();\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const services = await db.listServices(false); // all services\n   * ```\n   */\n  listServices(excludeSystem: boolean = true): Promise<ServiceSummary[]> {\n    return this.request({\n      path: \"/_api/foxx\",\n      qs: { excludeSystem },\n    });\n  }\n\n  /**\n   * Installs a new service.\n   *\n   * @param mount - The service's mount point, relative to the database.\n   * @param source - The service bundle to install.\n   * @param options - Options for installing the service.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * // Using a node.js file stream as source\n   * const source = fs.createReadStream(\"./my-foxx-service.zip\");\n   * const info = await db.installService(\"/hello\", source);\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * // Using a node.js Buffer as source\n   * const source = fs.readFileSync(\"./my-foxx-service.zip\");\n   * const info = await db.installService(\"/hello\", source);\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * // Using a File (Blob) from a browser file input\n   * const element = document.getElementById(\"my-file-input\");\n   * const source = element.files[0];\n   * const info = await db.installService(\"/hello\", source);\n   * ```\n   */\n  async installService(\n    mount: string,\n    source: Readable | Buffer | Blob | string,\n    options: InstallServiceOptions = {}\n  ): Promise<ServiceInfo> {\n    const { configuration, dependencies, ...qs } = options;\n    const req = await toForm({\n      configuration,\n      dependencies,\n      source,\n    });\n    return await this.request({\n      ...req,\n      method: \"POST\",\n      path: \"/_api/foxx\",\n      isBinary: true,\n      qs: { ...qs, mount },\n    });\n  }\n\n  /**\n   * Replaces an existing service with a new service by completely removing the\n   * old service and installing a new service at the same mount point.\n   *\n   * @param mount - The service's mount point, relative to the database.\n   * @param source - The service bundle to install.\n   * @param options - Options for replacing the service.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * // Using a node.js file stream as source\n   * const source = fs.createReadStream(\"./my-foxx-service.zip\");\n   * const info = await db.replaceService(\"/hello\", source);\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * // Using a node.js Buffer as source\n   * const source = fs.readFileSync(\"./my-foxx-service.zip\");\n   * const info = await db.replaceService(\"/hello\", source);\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * // Using a File (Blob) from a browser file input\n   * const element = document.getElementById(\"my-file-input\");\n   * const source = element.files[0];\n   * const info = await db.replaceService(\"/hello\", source);\n   * ```\n   */\n  async replaceService(\n    mount: string,\n    source: Readable | Buffer | Blob | string,\n    options: ReplaceServiceOptions = {}\n  ): Promise<ServiceInfo> {\n    const { configuration, dependencies, ...qs } = options;\n    const req = await toForm({\n      configuration,\n      dependencies,\n      source,\n    });\n    return await this.request({\n      ...req,\n      method: \"PUT\",\n      path: \"/_api/foxx/service\",\n      isBinary: true,\n      qs: { ...qs, mount },\n    });\n  }\n\n  /**\n   * Replaces an existing service with a new service while retaining the old\n   * service's configuration and dependencies.\n   *\n   * @param mount - The service's mount point, relative to the database.\n   * @param source - The service bundle to install.\n   * @param options - Options for upgrading the service.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * // Using a node.js file stream as source\n   * const source = fs.createReadStream(\"./my-foxx-service.zip\");\n   * const info = await db.upgradeService(\"/hello\", source);\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * // Using a node.js Buffer as source\n   * const source = fs.readFileSync(\"./my-foxx-service.zip\");\n   * const info = await db.upgradeService(\"/hello\", source);\n   * ```\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * // Using a File (Blob) from a browser file input\n   * const element = document.getElementById(\"my-file-input\");\n   * const source = element.files[0];\n   * const info = await db.upgradeService(\"/hello\", source);\n   * ```\n   */\n  async upgradeService(\n    mount: string,\n    source: Readable | Buffer | Blob | string,\n    options: UpgradeServiceOptions = {}\n  ): Promise<ServiceInfo> {\n    const { configuration, dependencies, ...qs } = options;\n    const req = await toForm({\n      configuration,\n      dependencies,\n      source,\n    });\n    return await this.request({\n      ...req,\n      method: \"PATCH\",\n      path: \"/_api/foxx/service\",\n      isBinary: true,\n      qs: { ...qs, mount },\n    });\n  }\n\n  /**\n   * Completely removes a service from the database.\n   *\n   * @param mount - The service's mount point, relative to the database.\n   * @param options - Options for uninstalling the service.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * await db.uninstallService(\"/my-foxx\");\n   * ```\n   */\n  uninstallService(\n    mount: string,\n    options?: UninstallServiceOptions\n  ): Promise<void> {\n    return this.request(\n      {\n        method: \"DELETE\",\n        path: \"/_api/foxx/service\",\n        qs: { ...options, mount },\n      },\n      () => undefined\n    );\n  }\n\n  /**\n   * Retrieves information about a mounted service.\n   *\n   * @param mount - The service's mount point, relative to the database.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const info = await db.getService(\"/my-service\");\n   * // info contains detailed information about the service\n   * ```\n   */\n  getService(mount: string): Promise<ServiceInfo> {\n    return this.request({\n      path: \"/_api/foxx/service\",\n      qs: { mount },\n    });\n  }\n\n  /**\n   * Retrieves information about the service's configuration options and their\n   * current values.\n   *\n   * See also {@link Database#replaceServiceConfiguration} and\n   * {@link Database#updateServiceConfiguration}.\n   *\n   * @param mount - The service's mount point, relative to the database.\n   * @param minimal - If set to `true`, the result will only include each\n   * configuration option's current value. Otherwise it will include the full\n   * definition for each option.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const config = await db.getServiceConfiguration(\"/my-service\");\n   * for (const [key, option] of Object.entries(config)) {\n   *   console.log(`${option.title} (${key}): ${option.current}`);\n   * }\n   * ```\n   */\n  getServiceConfiguration(\n    mount: string,\n    minimal?: false\n  ): Promise<Record<string, ServiceConfiguration>>;\n  /**\n   * Retrieves information about the service's configuration options and their\n   * current values.\n   *\n   * See also {@link Database#replaceServiceConfiguration} and\n   * {@link Database#updateServiceConfiguration}.\n   *\n   * @param mount - The service's mount point, relative to the database.\n   * @param minimal - If set to `true`, the result will only include each\n   * configuration option's current value. Otherwise it will include the full\n   * definition for each option.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const config = await db.getServiceConfiguration(\"/my-service\", true);\n   * for (const [key, value] of Object.entries(config)) {\n   *   console.log(`${key}: ${value}`);\n   * }\n   * ```\n   */\n  getServiceConfiguration(\n    mount: string,\n    minimal: true\n  ): Promise<Record<string, any>>;\n  getServiceConfiguration(mount: string, minimal: boolean = false) {\n    return this.request({\n      path: \"/_api/foxx/configuration\",\n      qs: { mount, minimal },\n    });\n  }\n\n  /**\n   * Replaces the configuration of the given service, discarding any existing\n   * values for options not specified.\n   *\n   * See also {@link Database#updateServiceConfiguration} and\n   * {@link Database#getServiceConfiguration}.\n   *\n   * @param mount - The service's mount point, relative to the database.\n   * @param cfg - An object mapping configuration option names to values.\n   * @param minimal - If set to `true`, the result will only include each\n   * configuration option's current value and warning (if any).\n   * Otherwise it will include the full definition for each option.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const config = { currency: \"USD\", locale: \"en-US\" };\n   * const info = await db.replaceServiceConfiguration(\"/my-service\", config);\n   * for (const [key, option] of Object.entries(info)) {\n   *   console.log(`${option.title} (${key}): ${option.value}`);\n   *   if (option.warning) console.warn(`Warning: ${option.warning}`);\n   * }\n   * ```\n   */\n  replaceServiceConfiguration(\n    mount: string,\n    cfg: Record<string, any>,\n    minimal?: false\n  ): Promise<Record<string, ServiceConfiguration & { warning?: string }>>;\n  /**\n   * Replaces the configuration of the given service, discarding any existing\n   * values for options not specified.\n   *\n   * See also {@link Database#updateServiceConfiguration} and\n   * {@link Database#getServiceConfiguration}.\n   *\n   * @param mount - The service's mount point, relative to the database.\n   * @param cfg - An object mapping configuration option names to values.\n   * @param minimal - If set to `true`, the result will only include each\n   * configuration option's current value and warning (if any).\n   * Otherwise it will include the full definition for each option.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const config = { currency: \"USD\", locale: \"en-US\" };\n   * const info = await db.replaceServiceConfiguration(\"/my-service\", config);\n   * for (const [key, value] of Object.entries(info.values)) {\n   *   console.log(`${key}: ${value}`);\n   *   if (info.warnings[key]) console.warn(`Warning: ${info.warnings[key]}`);\n   * }\n   * ```\n   */\n  replaceServiceConfiguration(\n    mount: string,\n    cfg: Record<string, any>,\n    minimal: true\n  ): Promise<{\n    values: Record<string, any>;\n    warnings: Record<string, string>;\n  }>;\n  replaceServiceConfiguration(\n    mount: string,\n    cfg: Record<string, any>,\n    minimal: boolean = false\n  ) {\n    return this.request({\n      method: \"PUT\",\n      path: \"/_api/foxx/configuration\",\n      body: cfg,\n      qs: { mount, minimal },\n    });\n  }\n\n  /**\n   * Updates the configuration of the given service while maintaining any\n   * existing values for options not specified.\n   *\n   * See also {@link Database#replaceServiceConfiguration} and\n   * {@link Database#getServiceConfiguration}.\n   *\n   * @param mount - The service's mount point, relative to the database.\n   * @param cfg - An object mapping configuration option names to values.\n   * @param minimal - If set to `true`, the result will only include each\n   * configuration option's current value and warning (if any).\n   * Otherwise it will include the full definition for each option.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const config = { currency: \"USD\", locale: \"en-US\" };\n   * const info = await db.updateServiceConfiguration(\"/my-service\", config);\n   * for (const [key, option] of Object.entries(info)) {\n   *   console.log(`${option.title} (${key}): ${option.value}`);\n   *   if (option.warning) console.warn(`Warning: ${option.warning}`);\n   * }\n   * ```\n   */\n  updateServiceConfiguration(\n    mount: string,\n    cfg: Record<string, any>,\n    minimal?: false\n  ): Promise<Record<string, ServiceConfiguration & { warning?: string }>>;\n  /**\n   * Updates the configuration of the given service while maintaining any\n   * existing values for options not specified.\n   *\n   * See also {@link Database#replaceServiceConfiguration} and\n   * {@link Database#getServiceConfiguration}.\n   *\n   * @param mount - The service's mount point, relative to the database.\n   * @param cfg - An object mapping configuration option names to values.\n   * @param minimal - If set to `true`, the result will only include each\n   * configuration option's current value and warning (if any).\n   * Otherwise it will include the full definition for each option.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const config = { currency: \"USD\", locale: \"en-US\" };\n   * const info = await db.updateServiceConfiguration(\"/my-service\", config);\n   * for (const [key, value] of Object.entries(info.values)) {\n   *   console.log(`${key}: ${value}`);\n   *   if (info.warnings[key]) console.warn(`Warning: ${info.warnings[key]}`);\n   * }\n   * ```\n   */\n  updateServiceConfiguration(\n    mount: string,\n    cfg: Record<string, any>,\n    minimal: true\n  ): Promise<{\n    values: Record<string, any>;\n    warnings: Record<string, string>;\n  }>;\n  updateServiceConfiguration(\n    mount: string,\n    cfg: Record<string, any>,\n    minimal: boolean = false\n  ) {\n    return this.request({\n      method: \"PATCH\",\n      path: \"/_api/foxx/configuration\",\n      body: cfg,\n      qs: { mount, minimal },\n    });\n  }\n\n  /**\n   * Retrieves information about the service's dependencies and their current\n   * mount points.\n   *\n   * See also {@link Database#replaceServiceDependencies} and\n   * {@link Database#updateServiceDependencies}.\n   *\n   * @param mount - The service's mount point, relative to the database.\n   * @param minimal - If set to `true`, the result will only include each\n   * dependency's current mount point. Otherwise it will include the full\n   * definition for each dependency.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const deps = await db.getServiceDependencies(\"/my-service\");\n   * for (const [key, dep] of Object.entries(deps)) {\n   *   console.log(`${dep.title} (${key}): ${dep.current}`);\n   * }\n   * ```\n   */\n  getServiceDependencies(\n    mount: string,\n    minimal?: false\n  ): Promise<Record<string, SingleServiceDependency | MultiServiceDependency>>;\n  /**\n   * Retrieves information about the service's dependencies and their current\n   * mount points.\n   *\n   * See also {@link Database#replaceServiceDependencies} and\n   * {@link Database#updateServiceDependencies}.\n   *\n   * @param mount - The service's mount point, relative to the database.\n   * @param minimal - If set to `true`, the result will only include each\n   * dependency's current mount point. Otherwise it will include the full\n   * definition for each dependency.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const deps = await db.getServiceDependencies(\"/my-service\", true);\n   * for (const [key, value] of Object.entries(deps)) {\n   *   console.log(`${key}: ${value}`);\n   * }\n   * ```\n   */\n  getServiceDependencies(\n    mount: string,\n    minimal: true\n  ): Promise<Record<string, string | string[]>>;\n  getServiceDependencies(mount: string, minimal: boolean = false) {\n    return this.request({\n      path: \"/_api/foxx/dependencies\",\n      qs: { mount, minimal },\n    });\n  }\n\n  /**\n   * Replaces the dependencies of the given service, discarding any existing\n   * mount points for dependencies not specified.\n   *\n   * See also {@link Database#updateServiceDependencies} and\n   * {@link Database#getServiceDependencies}.\n   *\n   * @param mount - The service's mount point, relative to the database.\n   * @param cfg - An object mapping dependency aliases to mount points.\n   * @param minimal - If set to `true`, the result will only include each\n   * dependency's current mount point. Otherwise it will include the full\n   * definition for each dependency.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const deps = { mailer: \"/mailer-api\", auth: \"/remote-auth\" };\n   * const info = await db.replaceServiceDependencies(\"/my-service\", deps);\n   * for (const [key, dep] of Object.entries(info)) {\n   *   console.log(`${dep.title} (${key}): ${dep.current}`);\n   *   if (dep.warning) console.warn(`Warning: ${dep.warning}`);\n   * }\n   * ```\n   */\n  replaceServiceDependencies(\n    mount: string,\n    deps: Record<string, string>,\n    minimal?: false\n  ): Promise<\n    Record<\n      string,\n      (SingleServiceDependency | MultiServiceDependency) & { warning?: string }\n    >\n  >;\n  /**\n   * Replaces the dependencies of the given service, discarding any existing\n   * mount points for dependencies not specified.\n   *\n   * See also {@link Database#updateServiceDependencies} and\n   * {@link Database#getServiceDependencies}.\n   *\n   * @param mount - The service's mount point, relative to the database.\n   * @param cfg - An object mapping dependency aliases to mount points.\n   * @param minimal - If set to `true`, the result will only include each\n   * dependency's current mount point. Otherwise it will include the full\n   * definition for each dependency.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const deps = { mailer: \"/mailer-api\", auth: \"/remote-auth\" };\n   * const info = await db.replaceServiceDependencies(\n   *   \"/my-service\",\n   *   deps,\n   *   true\n   * );\n   * for (const [key, value] of Object.entries(info)) {\n   *   console.log(`${key}: ${value}`);\n   *   if (info.warnings[key]) console.warn(`Warning: ${info.warnings[key]}`);\n   * }\n   * ```\n   */\n  replaceServiceDependencies(\n    mount: string,\n    deps: Record<string, string>,\n    minimal: true\n  ): Promise<{\n    values: Record<string, string>;\n    warnings: Record<string, string>;\n  }>;\n  replaceServiceDependencies(\n    mount: string,\n    deps: Record<string, string>,\n    minimal: boolean = false\n  ) {\n    return this.request({\n      method: \"PUT\",\n      path: \"/_api/foxx/dependencies\",\n      body: deps,\n      qs: { mount, minimal },\n    });\n  }\n\n  /**\n   * Updates the dependencies of the given service while maintaining any\n   * existing mount points for dependencies not specified.\n   *\n   * See also {@link Database#replaceServiceDependencies} and\n   * {@link Database#getServiceDependencies}.\n   *\n   * @param mount - The service's mount point, relative to the database.\n   * @param cfg - An object mapping dependency aliases to mount points.\n   * @param minimal - If set to `true`, the result will only include each\n   * dependency's current mount point. Otherwise it will include the full\n   * definition for each dependency.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const deps = { mailer: \"/mailer-api\", auth: \"/remote-auth\" };\n   * const info = await db.updateServiceDependencies(\"/my-service\", deps);\n   * for (const [key, dep] of Object.entries(info)) {\n   *   console.log(`${dep.title} (${key}): ${dep.current}`);\n   *   if (dep.warning) console.warn(`Warning: ${dep.warning}`);\n   * }\n   * ```\n   */\n  updateServiceDependencies(\n    mount: string,\n    deps: Record<string, string>,\n    minimal?: false\n  ): Promise<\n    Record<\n      string,\n      (SingleServiceDependency | MultiServiceDependency) & { warning?: string }\n    >\n  >;\n  /**\n   * Updates the dependencies of the given service while maintaining any\n   * existing mount points for dependencies not specified.\n   *\n   * See also {@link Database#replaceServiceDependencies} and\n   * {@link Database#getServiceDependencies}.\n   *\n   * @param mount - The service's mount point, relative to the database.\n   * @param cfg - An object mapping dependency aliases to mount points.\n   * @param minimal - If set to `true`, the result will only include each\n   * dependency's current mount point. Otherwise it will include the full\n   * definition for each dependency.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const deps = { mailer: \"/mailer-api\", auth: \"/remote-auth\" };\n   * const info = await db.updateServiceDependencies(\n   *   \"/my-service\",\n   *   deps,\n   *   true\n   * );\n   * for (const [key, value] of Object.entries(info)) {\n   *   console.log(`${key}: ${value}`);\n   *   if (info.warnings[key]) console.warn(`Warning: ${info.warnings[key]}`);\n   * }\n   * ```\n   */\n  updateServiceDependencies(\n    mount: string,\n    deps: Record<string, string>,\n    minimal: true\n  ): Promise<{\n    values: Record<string, string>;\n    warnings: Record<string, string>;\n  }>;\n  updateServiceDependencies(\n    mount: string,\n    deps: Record<string, string>,\n    minimal: boolean = false\n  ) {\n    return this.request({\n      method: \"PATCH\",\n      path: \"/_api/foxx/dependencies\",\n      body: deps,\n      qs: { mount, minimal },\n    });\n  }\n\n  /**\n   * Enables or disables development mode for the given service.\n   *\n   * @param mount - The service's mount point, relative to the database.\n   * @param enabled - Whether development mode should be enabled or disabled.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * await db.setServiceDevelopmentMode(\"/my-service\", true);\n   * // the service is now in development mode\n   * await db.setServiceDevelopmentMode(\"/my-service\", false);\n   * // the service is now in production mode\n   * ```\n   */\n  setServiceDevelopmentMode(\n    mount: string,\n    enabled: boolean = true\n  ): Promise<ServiceInfo> {\n    return this.request({\n      method: enabled ? \"POST\" : \"DELETE\",\n      path: \"/_api/foxx/development\",\n      qs: { mount },\n    });\n  }\n\n  /**\n   * Retrieves a list of scripts defined in the service manifest's \"scripts\"\n   * section mapped to their human readable representations.\n   *\n   * @param mount - The service's mount point, relative to the database.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const scripts = await db.listServiceScripts(\"/my-service\");\n   * for (const [name, title] of Object.entries(scripts)) {\n   *   console.log(`${name}: ${title}`);\n   * }\n   * ```\n   */\n  listServiceScripts(mount: string): Promise<Record<string, string>> {\n    return this.request({\n      path: \"/_api/foxx/scripts\",\n      qs: { mount },\n    });\n  }\n\n  /**\n   * Executes a service script and retrieves its result exposed as\n   * `module.exports` (if any).\n   *\n   * @param mount - The service's mount point, relative to the database.\n   * @param name - Name of the service script to execute as defined in the\n   * service manifest.\n   * @param params - Arbitrary value that will be exposed to the script as\n   * `argv[0]` in the service context (e.g. `module.context.argv[0]`).\n   * Must be serializable to JSON.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const result = await db.runServiceScript(\n   *   \"/my-service\",\n   *   \"create-user\",\n   *   {\n   *     username: \"service_admin\",\n   *     password: \"hunter2\"\n   *   }\n   * );\n   * ```\n   */\n  runServiceScript(mount: string, name: string, params?: any): Promise<any> {\n    return this.request({\n      method: \"POST\",\n      path: `/_api/foxx/scripts/${encodeURIComponent(name)}`,\n      body: params,\n      qs: { mount },\n    });\n  }\n\n  /**\n   * Runs the tests of a given service and returns the results using the\n   * \"default\" reporter.\n   *\n   * @param mount - The service's mount point, relative to the database.\n   * @param options - Options for running the tests.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const testReport = await db.runServiceTests(\"/my-foxx\");\n   * ```\n   */\n  runServiceTests(\n    mount: string,\n    options?: {\n      reporter?: \"default\";\n      /**\n       * Whether the reporter should use \"idiomatic\" mode. Has no effect when\n       * using the \"default\" or \"suite\" reporters.\n       */\n      idiomatic?: false;\n      /**\n       * If set, only tests with full names including this exact string will be\n       * executed.\n       */\n      filter?: string;\n    }\n  ): Promise<ServiceTestDefaultReport>;\n  /**\n   * Runs the tests of a given service and returns the results using the\n   * \"suite\" reporter, which groups the test result by test suite.\n   *\n   * @param mount - The service's mount point, relative to the database.\n   * @param options - Options for running the tests.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const suiteReport = await db.runServiceTests(\n   *   \"/my-foxx\",\n   *   { reporter: \"suite\" }\n   * );\n   * ```\n   */\n  runServiceTests(\n    mount: string,\n    options: {\n      reporter: \"suite\";\n      /**\n       * Whether the reporter should use \"idiomatic\" mode. Has no effect when\n       * using the \"default\" or \"suite\" reporters.\n       */\n      idiomatic?: false;\n      /**\n       * If set, only tests with full names including this exact string will be\n       * executed.\n       */\n      filter?: string;\n    }\n  ): Promise<ServiceTestSuiteReport>;\n  /**\n   * Runs the tests of a given service and returns the results using the\n   * \"stream\" reporter, which represents the results as a sequence of tuples\n   * representing events.\n   *\n   * @param mount - The service's mount point, relative to the database.\n   * @param options - Options for running the tests.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const streamEvents = await db.runServiceTests(\n   *   \"/my-foxx\",\n   *   { reporter: \"stream\" }\n   * );\n   * ```\n   */\n  runServiceTests(\n    mount: string,\n    options: {\n      reporter: \"stream\";\n      /**\n       * Whether the reporter should use \"idiomatic\" mode. If set to `true`,\n       * the results will be returned as a formatted string.\n       */\n      idiomatic?: false;\n      /**\n       * If set, only tests with full names including this exact string will be\n       * executed.\n       */\n      filter?: string;\n    }\n  ): Promise<ServiceTestStreamReport>;\n  /**\n   * Runs the tests of a given service and returns the results using the\n   * \"tap\" reporter, which represents the results as an array of strings using\n   * the \"tap\" format.\n   *\n   * @param mount - The service's mount point, relative to the database.\n   * @param options - Options for running the tests.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const tapLines = await db.runServiceTests(\n   *   \"/my-foxx\",\n   *   { reporter: \"tap\" }\n   * );\n   * ```\n   */\n  runServiceTests(\n    mount: string,\n    options: {\n      reporter: \"tap\";\n      /**\n       * Whether the reporter should use \"idiomatic\" mode. If set to `true`,\n       * the results will be returned as a formatted string.\n       */\n      idiomatic?: false;\n      /**\n       * If set, only tests with full names including this exact string will be\n       * executed.\n       */\n      filter?: string;\n    }\n  ): Promise<ServiceTestTapReport>;\n  /**\n   * Runs the tests of a given service and returns the results using the\n   * \"xunit\" reporter, which represents the results as an XML document using\n   * the JSONML exchange format.\n   *\n   * @param mount - The service's mount point, relative to the database.\n   * @param options - Options for running the tests.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const jsonML = await db.runServiceTests(\n   *   \"/my-foxx\",\n   *   { reporter: \"xunit\" }\n   * );\n   * ```\n   */\n  runServiceTests(\n    mount: string,\n    options: {\n      reporter: \"xunit\";\n      /**\n       * Whether the reporter should use \"idiomatic\" mode. If set to `true`,\n       * the results will be returned as a formatted string.\n       */\n      idiomatic?: false;\n      /**\n       * If set, only tests with full names including this exact string will be\n       * executed.\n       */\n      filter?: string;\n    }\n  ): Promise<ServiceTestXunitReport>;\n  /**\n   * Runs the tests of a given service and returns the results as a string\n   * using the \"stream\" reporter in \"idiomatic\" mode, which represents the\n   * results as a line-delimited JSON stream of tuples representing events.\n   *\n   * @param mount - The service's mount point, relative to the database.\n   * @param options - Options for running the tests.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const streamReport = await db.runServiceTests(\n   *   \"/my-foxx\",\n   *   { reporter: \"stream\", idiomatic: true }\n   * );\n   * ```\n   */\n  runServiceTests(\n    mount: string,\n    options: {\n      reporter: \"stream\";\n      /**\n       * Whether the reporter should use \"idiomatic\" mode. If set to `false`,\n       * the results will be returned as an array of tuples instead of a\n       * string.\n       */\n      idiomatic: true;\n      /**\n       * If set, only tests with full names including this exact string will be\n       * executed.\n       */\n      filter?: string;\n    }\n  ): Promise<string>;\n  /**\n   * Runs the tests of a given service and returns the results as a string\n   * using the \"tap\" reporter in \"idiomatic\" mode, which represents the\n   * results using the \"tap\" format.\n   *\n   * @param mount - The service's mount point, relative to the database.\n   * @param options - Options for running the tests.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const tapReport = await db.runServiceTests(\n   *   \"/my-foxx\",\n   *   { reporter: \"tap\", idiomatic: true }\n   * );\n   * ```\n   */\n  runServiceTests(\n    mount: string,\n    options: {\n      reporter: \"tap\";\n      /**\n       * Whether the reporter should use \"idiomatic\" mode. If set to `false`,\n       * the results will be returned as an array of strings instead of a\n       * single string.\n       */\n      idiomatic: true;\n      /**\n       * If set, only tests with full names including this exact string will be\n       * executed.\n       */\n      filter?: string;\n    }\n  ): Promise<string>;\n  /**\n   * Runs the tests of a given service and returns the results as a string\n   * using the \"xunit\" reporter in \"idiomatic\" mode, which represents the\n   * results as an XML document.\n   *\n   * @param mount - The service's mount point, relative to the database.\n   * @param options - Options for running the tests.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const xml = await db.runServiceTests(\n   *   \"/my-foxx\",\n   *   { reporter: \"xunit\", idiomatic: true }\n   * );\n   * ```\n   */\n  runServiceTests(\n    mount: string,\n    options: {\n      reporter: \"xunit\";\n      /**\n       * Whether the reporter should use \"idiomatic\" mode. If set to `false`,\n       * the results will be returned using the JSONML exchange format\n       * instead of a string.\n       */\n      idiomatic: true;\n      /**\n       * If set, only tests with full names including this exact string will be\n       * executed.\n       */\n      filter?: string;\n    }\n  ): Promise<string>;\n  runServiceTests(\n    mount: string,\n    options?: {\n      reporter?: string;\n      idiomatic?: boolean;\n      filter?: string;\n    }\n  ) {\n    return this.request({\n      method: \"POST\",\n      path: \"/_api/foxx/tests\",\n      qs: {\n        ...options,\n        mount,\n      },\n    });\n  }\n\n  /**\n   * Retrieves the text content of the service's `README` or `README.md` file.\n   *\n   * Returns `undefined` if no such file could be found.\n   *\n   * @param mount - The service's mount point, relative to the database.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const readme = await db.getServiceReadme(\"/my-service\");\n   * if (readme !== undefined) console.log(readme);\n   * else console.warn(`No README found.`)\n   * ```\n   */\n  getServiceReadme(mount: string): Promise<string | undefined> {\n    return this.request({\n      path: \"/_api/foxx/readme\",\n      qs: { mount },\n    });\n  }\n\n  /**\n   * Retrieves an Open API compatible Swagger API description object for the\n   * service installed at the given mount point.\n   *\n   * @param mount - The service's mount point, relative to the database.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const spec = await db.getServiceDocumentation(\"/my-service\");\n   * // spec is a Swagger API description of the service\n   * ```\n   */\n  getServiceDocumentation(mount: string): Promise<SwaggerJson> {\n    return this.request({\n      path: \"/_api/foxx/swagger\",\n      qs: { mount },\n    });\n  }\n\n  /**\n   * Retrieves a zip bundle containing the service files.\n   *\n   * Returns a `Buffer` in node.js or `Blob` in the browser.\n   *\n   * @param mount - The service's mount point, relative to the database.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const serviceBundle = await db.downloadService(\"/my-foxx\");\n   * ```\n   */\n  downloadService(mount: string): Promise<Buffer | Blob> {\n    return this.request({\n      method: \"POST\",\n      path: \"/_api/foxx/download\",\n      qs: { mount },\n      expectBinary: true,\n    });\n  }\n\n  /**\n   * Writes all locally available services to the database and updates any\n   * service bundles missing in the database.\n   *\n   * @param replace - If set to `true`, outdated services will also be\n   * committed. This can be used to solve some consistency problems when\n   * service bundles are missing in the database or were deleted manually.\n   *\n   * @example\n   * ```js\n   * await db.commitLocalServiceState();\n   * // all services available on the coordinator have been written to the db\n   * ```\n   *\n   * @example\n   * ```js\n   * await db.commitLocalServiceState(true);\n   * // all service conflicts have been resolved in favor of this coordinator\n   * ```\n   */\n  commitLocalServiceState(replace: boolean = false): Promise<void> {\n    return this.request(\n      {\n        method: \"POST\",\n        path: \"/_api/foxx/commit\",\n        qs: { replace },\n      },\n      () => undefined\n    );\n  }\n  //#endregion\n  //#region hot backups\n  /**\n   * (Enterprise Edition only.) Creates a hot backup of the entire ArangoDB\n   * deployment including all databases, collections, etc.\n   *\n   * Returns an object describing the backup result.\n   *\n   * @param options - Options for creating the backup.\n   *\n   * @example\n   * ```js\n   * const info = await db.createHotBackup();\n   * // a hot backup has been created\n   * ```\n   */\n  createHotBackup(options: HotBackupOptions = {}): Promise<HotBackupResult> {\n    return this.request(\n      {\n        method: \"POST\",\n        path: \"/_admin/backup/create\",\n        body: options,\n      },\n      (res) => res.body.result\n    );\n  }\n\n  /**\n   * (Enterprise Edition only.) Retrieves a list of all locally found hot\n   * backups.\n   *\n   * @param id - If specified, only the backup with the given ID will be\n   * returned.\n   *\n   * @example\n   * ```js\n   * const backups = await db.listHotBackups();\n   * for (const backup of backups) {\n   *   console.log(backup.id);\n   * }\n   * ```\n   */\n  listHotBackups(id?: string | string[]): Promise<HotBackupList> {\n    return this.request(\n      {\n        method: \"POST\",\n        path: \"/_admin/backup/list\",\n        body: id ? { id } : undefined,\n      },\n      (res) => res.body.result\n    );\n  }\n\n  /**\n   * (Enteprise Edition only.) Restores a consistent local hot backup.\n   *\n   * Returns the directory path of the restored backup.\n   *\n   * @param id - The ID of the backup to restore.\n   *\n   * @example\n   * ```js\n   * await db.restoreHotBackup(\"2023-09-19T15.38.21Z_example\");\n   * // the backup has been restored\n   * ```\n   */\n  restoreHotBackup(id: string): Promise<string> {\n    return this.request(\n      {\n        method: \"POST\",\n        path: \"/_admin/backup/restore\",\n        body: { id },\n      },\n      (res) => res.body.result.previous\n    );\n  }\n\n  /**\n   * (Enterprise Edition only.) Deletes a local hot backup.\n   *\n   * @param id - The ID of the backup to delete.\n   *\n   * @example\n   * ```js\n   * await db.deleteHotBackup(\"2023-09-19T15.38.21Z_example\");\n   * // the backup has been deleted\n   * ```\n   */\n  deleteHotBackup(id: string): Promise<void> {\n    return this.request(\n      {\n        method: \"POST\",\n        path: \"/_admin/backup/delete\",\n        body: { id },\n      },\n      () => undefined\n    );\n  }\n  //#endregion\n  //#region logs\n  /**\n   * Retrieves the log messages from the server's global log.\n   *\n   * @param options - Options for retrieving the log entries.\n   *\n   * @example\n   * ```js\n   * const log = await db.getLogEntries();\n   * for (let i = 0; i < log.totalAmount; i++) {\n   *   console.log(`${\n   *     new Date(log.timestamp[i] * 1000).toISOString()\n   *   } - [${LogLevel[log.level[i]]}] ${log.text[i]} (#${log.lid[i]})`);\n   * }\n   * ```\n   */\n  getLogEntries(options?: LogEntriesOptions): Promise<LogEntries> {\n    return this.request(\n      {\n        path: \"/_admin/log/entries\",\n        qs: options,\n      },\n      (res) => res.body\n    );\n  }\n\n  /**\n   * Retrieves the log messages from the server's global log.\n   *\n   * @param options - Options for retrieving the log entries.\n   *\n   * @deprecated This endpoint has been deprecated in ArangoDB 3.8.\n   * Use {@link Database#getLogEntries} instead.\n   *\n   * @example\n   * ```js\n   * const messages = await db.getLogMessages();\n   * for (const m of messages) {\n   *   console.log(`${m.date} - [${m.level}] ${m.message} (#${m.id})`);\n   * }\n   * ```\n   */\n  getLogMessages(options?: LogEntriesOptions): Promise<LogMessage[]> {\n    return this.request(\n      {\n        path: \"/_admin/log\",\n        qs: options,\n      },\n      (res) => res.body.messages\n    );\n  }\n\n  /**\n   * Retrieves the server's current log level for each topic.\n   *\n   * @example\n   * ```js\n   * const levels = await db.getLogLevel();\n   * console.log(levels.request); // log level for incoming requests\n   * ```\n   */\n  getLogLevel(): Promise<Record<string, LogLevelSetting>> {\n    return this.request({\n      path: \"/_admin/log/level\",\n    });\n  }\n\n  /**\n   * Sets the server's log level for each of the given topics to the given level.\n   *\n   * Any omitted topics will be left unchanged.\n   *\n   * @param levels - An object mapping topic names to log levels.\n   *\n   * @example\n   * ```js\n   * await db.setLogLevel({ request: \"debug\" });\n   * // Debug information will now be logged for each request\n   * ```\n   */\n  setLogLevel(\n    levels: Record<string, LogLevelSetting>\n  ): Promise<Record<string, LogLevelSetting>> {\n    return this.request({\n      method: \"PUT\",\n      path: \"/_admin/log/level\",\n      body: levels,\n    });\n  }\n  //#endregion\n  //#region async jobs\n  /**\n   * Returns a {@link job.Job} instance for the given `jobId`.\n   *\n   * @param jobId - ID of the async job.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const job = db.job(\"12345\");\n   * ```\n   */\n  job(jobId: string): Job {\n    return new Job(this, jobId);\n  }\n\n  /**\n   * Returns a list of the IDs of all currently pending async jobs.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const pendingJobs = await db.listPendingJobs();\n   * console.log(pendingJobs); // e.g. [\"12345\", \"67890\"]\n   * ```\n   */\n  listPendingJobs(): Promise<string[]> {\n    return this.request(\n      {\n        path: \"/_api/job/pending\",\n      },\n      (res) => res.body\n    );\n  }\n\n  /**\n   * Returns a list of the IDs of all currently available completed async jobs.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const completedJobs = await db.listCompletedJobs();\n   * console.log(completedJobs); // e.g. [\"12345\", \"67890\"]\n   * ```\n   */\n  listCompletedJobs(): Promise<string[]> {\n    return this.request(\n      {\n        path: \"/_api/job/done\",\n      },\n      (res) => res.body\n    );\n  }\n\n  /**\n   * Deletes the results of all completed async jobs created before the given\n   * threshold.\n   *\n   * @param threshold - The expiration timestamp in milliseconds.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const ONE_WEEK = 7 * 24 * 60 * 60 * 1000;\n   * await db.deleteExpiredJobResults(Date.now() - ONE_WEEK);\n   * // all job results older than a week have been deleted\n   * ```\n   */\n  deleteExpiredJobResults(threshold: number): Promise<void> {\n    return this.request(\n      {\n        method: \"DELETE\",\n        path: `/_api/job/expired`,\n        qs: { stamp: threshold / 1000 },\n      },\n      () => undefined\n    );\n  }\n\n  /**\n   * Deletes the results of all completed async jobs.\n   */\n  deleteAllJobResults(): Promise<void> {\n    return this.request(\n      {\n        method: \"DELETE\",\n        path: `/_api/job/all`,\n      },\n      () => undefined\n    );\n  }\n  //#endregion\n}\n"]}