{"version": 3, "file": "documents.js", "sourceRoot": "", "sources": ["../src/documents.ts"], "names": [], "mappings": ";AAAA;;;;;;;;GAQG;;;AAwGH;;GAEG;AACH,SAAgB,eAAe,CAC7B,QAA0B,EAC1B,cAAsB,EACtB,SAAkB,IAAI;IAEtB,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;QACjC,IAAI,QAAQ,CAAC,GAAG,EAAE,CAAC;YACjB,OAAO,eAAe,CAAC,QAAQ,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;QACvD,CAAC;QACD,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;YAClB,OAAO,eAAe,CAAC,QAAQ,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;QACxD,CAAC;QACD,MAAM,IAAI,KAAK,CACb,4EAA4E,CAC7E,CAAC;IACJ,CAAC;IACD,IAAI,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;QAC3B,MAAM,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC5C,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAC7C,IAAI,MAAM,IAAI,cAAc,KAAK,cAAc,EAAE,CAAC;YAChD,MAAM,IAAI,KAAK,CACb,gBAAgB,QAAQ,qCAAqC,cAAc,GAAG,CAC/E,CAAC;QACJ,CAAC;QACD,OAAO,CAAC,cAAc,EAAE,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC7C,CAAC;IACD,OAAO,GAAG,cAAc,IAAI,QAAQ,EAAE,CAAC;AACzC,CAAC;AA3BD,0CA2BC", "sourcesContent": ["/**\n * ```ts\n * import type { Document, Edge } from \"arangojs/documents\";\n * ```\n *\n * The \"documents\" module provides document/edge related types for TypeScript.\n *\n * @packageDocumentation\n */\n\n/**\n * Common ArangoDB metadata properties of a document.\n */\nexport type DocumentMetadata = {\n  /**\n   * Key of the document, which uniquely identifies the document within its\n   * collection.\n   */\n  _key: string;\n  /**\n   * Unique ID of the document, which is composed of the collection name\n   * and the document `_key`.\n   */\n  _id: string;\n  /**\n   * Revision of the document data.\n   */\n  _rev: string;\n};\n\n/**\n * ArangoDB metadata defining the relations of an edge document.\n */\nexport type EdgeMetadata = {\n  /**\n   * Unique ID of the document that acts as the edge's start vertex.\n   */\n  _from: string;\n  /**\n   * Unique ID of the document that acts as the edge's end vertex.\n   */\n  _to: string;\n};\n\n/**\n * Type representing an object that can be stored in a collection.\n */\nexport type DocumentData<T extends Record<string, any> = any> = T &\n  Partial<DocumentMetadata> &\n  Partial<EdgeMetadata>;\n\n/**\n * Type representing an object that can be stored in an edge collection.\n */\nexport type EdgeData<T extends Record<string, any> = any> = T &\n  Partial<DocumentMetadata> &\n  EdgeMetadata;\n\n/**\n * Type representing a document stored in a collection.\n */\nexport type Document<T extends Record<string, any> = any> = T &\n  DocumentMetadata &\n  Partial<EdgeMetadata>;\n\n/**\n * Type representing an edge document stored in an edge collection.\n */\nexport type Edge<T extends Record<string, any> = any> = T &\n  DocumentMetadata &\n  EdgeMetadata;\n\n/**\n * Type representing patch data for a given object type to represent a payload\n * ArangoDB can apply in a document PATCH request (i.e. a partial update).\n *\n * This differs from `Partial` in that it also applies itself to any nested\n * objects recursively.\n */\nexport type Patch<T = Record<string, any>> = {\n  [K in keyof T]?: T[K] | Patch<T[K]>;\n};\n\n/**\n * An object with an ArangoDB document `_id` property.\n *\n * See {@link documents.DocumentMetadata}.\n */\nexport type ObjectWithId = {\n  [key: string]: any;\n  _id: string;\n};\n\n/**\n * An object with an ArangoDB document `_key` property.\n *\n * See {@link documents.DocumentMetadata}.\n */\nexport type ObjectWithKey = {\n  [key: string]: any;\n  _key: string;\n};\n\n/**\n * A value that can be used to identify a document within a collection in\n * arangojs methods, i.e. a partial ArangoDB document or the value of a\n * document's `_key` or `_id`.\n *\n * See {@link documents.DocumentMetadata}.\n */\nexport type DocumentSelector = ObjectWithId | ObjectWithKey | string;\n\n/**\n * @internal\n */\nexport function _documentHandle(\n  selector: DocumentSelector,\n  collectionName: string,\n  strict: boolean = true\n): string {\n  if (typeof selector !== \"string\") {\n    if (selector._id) {\n      return _documentHandle(selector._id, collectionName);\n    }\n    if (selector._key) {\n      return _documentHandle(selector._key, collectionName);\n    }\n    throw new Error(\n      \"Document handle must be a string or an object with a _key or _id attribute\"\n    );\n  }\n  if (selector.includes(\"/\")) {\n    const [head, ...tail] = selector.split(\"/\");\n    const normalizedHead = head.normalize(\"NFC\");\n    if (strict && normalizedHead !== collectionName) {\n      throw new Error(\n        `Document ID \"${selector}\" does not match collection name \"${collectionName}\"`\n      );\n    }\n    return [normalizedHead, ...tail].join(\"/\");\n  }\n  return `${collectionName}/${selector}`;\n}\n"]}