{"version": 3, "file": "error.js", "sourceRoot": "", "sources": ["../src/error.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;GASG;;;AAEH,MAAM,QAAQ,GAA8B;IAC1C,CAAC,EAAE,eAAe;IAClB,GAAG,EAAE,cAAc;IACnB,GAAG,EAAE,aAAa;IAClB,GAAG,EAAE,cAAc;IACnB,GAAG,EAAE,kBAAkB;IACvB,GAAG,EAAE,WAAW;IAChB,GAAG,EAAE,WAAW;IAChB,GAAG,EAAE,oBAAoB;IACzB,GAAG,EAAE,gBAAgB;IACrB,GAAG,EAAE,+BAA+B;IACpC,GAAG,EAAE,iBAAiB;IACtB,GAAG,EAAE,UAAU;IACf,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,iBAAiB;IACtB,GAAG,EAAE,qBAAqB;IAC1B,GAAG,EAAE,mBAAmB;IACxB,GAAG,EAAE,sBAAsB;IAC3B,GAAG,EAAE,wBAAwB;IAC7B,GAAG,EAAE,iCAAiC;IACtC,GAAG,EAAE,oBAAoB;IACzB,GAAG,EAAE,cAAc;IACnB,GAAG,EAAE,qBAAqB;IAC1B,GAAG,EAAE,sBAAsB;IAC3B,GAAG,EAAE,QAAQ;IACb,GAAG,EAAE,mBAAmB;IACxB,GAAG,EAAE,kBAAkB;IACvB,GAAG,EAAE,uBAAuB;IAC5B,GAAG,EAAE,mBAAmB;IACxB,GAAG,EAAE,iCAAiC;IACtC,GAAG,EAAE,oCAAoC;IACzC,GAAG,EAAE,+BAA+B;IACpC,GAAG,EAAE,uBAAuB;IAC5B,GAAG,EAAE,uBAAuB;IAC5B,GAAG,EAAE,iBAAiB;IACtB,GAAG,EAAE,aAAa;IAClB,GAAG,EAAE,qBAAqB;IAC1B,GAAG,EAAE,iBAAiB;IACtB,GAAG,EAAE,4BAA4B;IACjC,GAAG,EAAE,yBAAyB;IAC9B,GAAG,EAAE,sBAAsB;IAC3B,GAAG,EAAE,eAAe;IACpB,GAAG,EAAE,cAAc;IACnB,GAAG,EAAE,iCAAiC;IACtC,GAAG,EAAE,+BAA+B;CACrC,CAAC;AAEF,MAAM,eAAe,GAAG;IACtB,UAAU;IACV,YAAY;IACZ,cAAc;IACd,OAAO;IACP,aAAa;IACb,QAAQ;CACU,CAAC;AAErB;;;;GAIG;AACH,SAAgB,aAAa,CAAC,KAAU;IACtC,OAAO,OAAO,CAAC,KAAK,IAAI,KAAK,CAAC,aAAa,CAAC,CAAC;AAC/C,CAAC;AAFD,sCAEC;AAED;;;;GAIG;AACH,SAAgB,qBAAqB,CAAC,IAAS;IAC7C,OAAO,CACL,IAAI;QACJ,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;QAC5B,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;QAC3B,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC;QACnC,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAChC,CAAC;AACJ,CAAC;AARD,sDAQC;AAED;;GAEG;AACH,SAAgB,aAAa,CAAC,GAAQ;IACpC,OAAO,CACL,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,KAAK,CAAC,SAAS;QAC9C,GAAG,CAAC,cAAc,CAAC,MAAM,CAAC;QAC1B,GAAG,CAAC,cAAc,CAAC,OAAO,CAAC;QAC3B,GAAG,CAAC,cAAc,CAAC,SAAS,CAAC,CAC9B,CAAC;AACJ,CAAC;AAPD,sCAOC;AAWD;;GAEG;AACH,MAAa,WAAY,SAAQ,KAAK;IAiBpC;;OAEG;IACH,YAAY,QAAa;QACvB,KAAK,EAAE,CAAC;QApBV,SAAI,GAAG,aAAa,CAAC;QAqBnB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC;QAC1C,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC;QACvC,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;QAC/B,MAAM,GAAG,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACpC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACrB,KAAK,MAAM,GAAG,IAAI,eAAe,EAAE,CAAC;YAClC,IAAI,GAAG,CAAC,GAAG,CAAC;gBAAE,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAE,CAAC;QACtC,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,IAAI,aAAa;QACf,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM;QACJ,OAAO;YACL,KAAK,EAAE,IAAI;YACX,YAAY,EAAE,IAAI,CAAC,OAAO;YAC1B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,IAAI,EAAE,IAAI,CAAC,IAAI;SAChB,CAAC;IACJ,CAAC;CACF;AAlDD,kCAkDC;AAED;;GAEG;AACH,MAAa,SAAU,SAAQ,KAAK;IAWlC;;OAEG;IACH,YAAY,QAAa;QACvB,KAAK,EAAE,CAAC;QAdV,SAAI,GAAG,WAAW,CAAC;QAejB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,UAAU,IAAI,GAAG,CAAC;QACvC,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,GAAG,CAAC,CAAC;QACpD,MAAM,GAAG,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACpC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACrB,KAAK,MAAM,GAAG,IAAI,eAAe,EAAE,CAAC;YAClC,IAAI,GAAG,CAAC,GAAG,CAAC;gBAAE,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAE,CAAC;QACtC,CAAC;IACH,CAAC;IAED,MAAM;QACJ,OAAO;YACL,KAAK,EAAE,IAAI;YACX,IAAI,EAAE,IAAI,CAAC,IAAI;SAChB,CAAC;IACJ,CAAC;CACF;AAhCD,8BAgCC", "sourcesContent": ["/**\n * ```ts\n * import type { ArangoError, HttpError } from \"arangojs/error\";\n * ```\n *\n * The \"error\" module provides types and interfaces for TypeScript related\n * to arangojs error handling.\n *\n * @packageDocumentation\n */\n\nconst messages: { [key: number]: string } = {\n  0: \"Network Error\",\n  304: \"Not Modified\",\n  400: \"Bad Request\",\n  401: \"Unauthorized\",\n  402: \"Payment Required\",\n  403: \"Forbidden\",\n  404: \"Not Found\",\n  405: \"Method Not Allowed\",\n  406: \"Not Acceptable\",\n  407: \"Proxy Authentication Required\",\n  408: \"Request Timeout\",\n  409: \"Conflict\",\n  410: \"Gone\",\n  411: \"Length Required\",\n  412: \"Precondition Failed\",\n  413: \"Payload Too Large\",\n  414: \"Request-URI Too Long\",\n  415: \"Unsupported Media Type\",\n  416: \"Requested Range Not Satisfiable\",\n  417: \"Expectation Failed\",\n  418: \"I'm a teapot\",\n  421: \"Misdirected Request\",\n  422: \"Unprocessable Entity\",\n  423: \"Locked\",\n  424: \"Failed Dependency\",\n  426: \"Upgrade Required\",\n  428: \"Precondition Required\",\n  429: \"Too Many Requests\",\n  431: \"Request Header Fields Too Large\",\n  444: \"Connection Closed Without Response\",\n  451: \"Unavailable For Legal Reasons\",\n  499: \"Client Closed Request\",\n  500: \"Internal Server Error\",\n  501: \"Not Implemented\",\n  502: \"Bad Gateway\",\n  503: \"Service Unavailable\",\n  504: \"Gateway Timeout\",\n  505: \"HTTP Version Not Supported\",\n  506: \"Variant Also Negotiates\",\n  507: \"Insufficient Storage\",\n  508: \"Loop Detected\",\n  510: \"Not Extended\",\n  511: \"Network Authentication Required\",\n  599: \"Network Connect Timeout Error\",\n};\n\nconst nativeErrorKeys = [\n  \"fileName\",\n  \"lineNumber\",\n  \"columnNumber\",\n  \"stack\",\n  \"description\",\n  \"number\",\n] as (keyof Error)[];\n\n/**\n * Indicates whether the given value represents an {@link ArangoError}.\n *\n * @param error - A value that might be an `ArangoError`.\n */\nexport function isArangoError(error: any): error is ArangoError {\n  return Boolean(error && error.isArangoError);\n}\n\n/**\n * Indicates whether the given value represents an ArangoDB error response.\n *\n * @internal\n */\nexport function isArangoErrorResponse(body: any): boolean {\n  return (\n    body &&\n    body.hasOwnProperty(\"error\") &&\n    body.hasOwnProperty(\"code\") &&\n    body.hasOwnProperty(\"errorMessage\") &&\n    body.hasOwnProperty(\"errorNum\")\n  );\n}\n\n/**\n * Indicates whether the given value represents a Node.js `SystemError`.\n */\nexport function isSystemError(err: any): err is SystemError {\n  return (\n    Object.getPrototypeOf(err) === Error.prototype &&\n    err.hasOwnProperty(\"code\") &&\n    err.hasOwnProperty(\"errno\") &&\n    err.hasOwnProperty(\"syscall\")\n  );\n}\n\n/**\n * Interface representing a Node.js `SystemError`.\n */\nexport interface SystemError extends Error {\n  code: string;\n  errno: number | string;\n  syscall: string;\n}\n\n/**\n * Represents an error returned by ArangoDB.\n */\nexport class ArangoError extends Error {\n  name = \"ArangoError\";\n  /**\n   * ArangoDB error code.\n   *\n   * See [ArangoDB error documentation](https://www.arangodb.com/docs/stable/appendix-error-codes.html).\n   */\n  errorNum: number;\n  /**\n   * HTTP status code included in the server error response object.\n   */\n  code: number;\n  /**\n   * Server response object.\n   */\n  response: any;\n\n  /**\n   * @internal\n   */\n  constructor(response: any) {\n    super();\n    this.response = response;\n    this.message = response.body.errorMessage;\n    this.errorNum = response.body.errorNum;\n    this.code = response.body.code;\n    const err = new Error(this.message);\n    err.name = this.name;\n    for (const key of nativeErrorKeys) {\n      if (err[key]) this[key] = err[key]!;\n    }\n  }\n\n  /**\n   * @internal\n   *\n   * Indicates that this object represents an ArangoDB error.\n   */\n  get isArangoError(): true {\n    return true;\n  }\n\n  toJSON() {\n    return {\n      error: true,\n      errorMessage: this.message,\n      errorNum: this.errorNum,\n      code: this.code,\n    };\n  }\n}\n\n/**\n * Represents a plain HTTP error response.\n */\nexport class HttpError extends Error {\n  name = \"HttpError\";\n  /**\n   * Server response object.\n   */\n  response: any;\n  /**\n   * HTTP status code of the server response.\n   */\n  code: number;\n\n  /**\n   * @internal\n   */\n  constructor(response: any) {\n    super();\n    this.response = response;\n    this.code = response.statusCode || 500;\n    this.message = messages[this.code] || messages[500];\n    const err = new Error(this.message);\n    err.name = this.name;\n    for (const key of nativeErrorKeys) {\n      if (err[key]) this[key] = err[key]!;\n    }\n  }\n\n  toJSON() {\n    return {\n      error: true,\n      code: this.code,\n    };\n  }\n}\n"]}