"use strict";
/**
 * ```ts
 * import type { FoxxManifest } from "arangojs/foxx-manifest";
 * ```
 *
 * The "foxx-manifest" module provides the Foxx manifest type for TypeScript.
 *
 * Generated from [JSON Schema](http://json.schemastore.org/foxx-manifest)
 * using `json-schema-to-typescript`.
 *
 * @packageDocumentation
 */
Object.defineProperty(exports, "__esModule", { value: true });
//# sourceMappingURL=foxx-manifest.js.map