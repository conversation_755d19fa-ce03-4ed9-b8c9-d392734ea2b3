{"version": 3, "file": "foxx-manifest.js", "sourceRoot": "", "sources": ["../src/foxx-manifest.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;GAWG", "sourcesContent": ["/**\n * ```ts\n * import type { FoxxManifest } from \"arangojs/foxx-manifest\";\n * ```\n *\n * The \"foxx-manifest\" module provides the Foxx manifest type for TypeScript.\n *\n * Generated from [JSON Schema](http://json.schemastore.org/foxx-manifest)\n * using `json-schema-to-typescript`.\n *\n * @packageDocumentation\n */\n\n/**\n * Schema for ArangoDB Foxx service manifests.\n */\nexport type FoxxManifest = {\n  /**\n   * An object defining the configuration options this service requires.\n   */\n  configuration?: Record<string, Configuration>;\n  /**\n   * If specified, the / (root) route of the service will automatically redirect to the given relative path, e.g. \"index.html\".\n   */\n  defaultDocument?: string;\n  /**\n   * The dependencies this service uses, i.e. which APIs its dependencies need to be compatible with.\n   */\n  dependencies?: Record<string, string | Dependency>;\n  /**\n   * The dependencies this provides, i.e. which APIs it claims to be compatible with.\n   */\n  provides?: Record<string, string>;\n  /**\n   * An object indicating the semantic version ranges of ArangoDB (or compatible environments) the service will be compatible with.\n   */\n  engines?: Record<string, string> & {\n    arangodb?: string;\n  };\n  /**\n   * An object defining file assets served by this service.\n   */\n  files?: Record<string, string | File>;\n  /**\n   * The relative path to the Foxx JavaScript files in the service, e.g. \"lib\". Defaults to the folder containing this manifest.\n   */\n  lib?: string;\n  /**\n   * The relative path to the main entry point of this service (relative to lib), e.g. \"index.js\".\n   */\n  main?: string;\n  /**\n   * An object defining named scripts provided by this service, which can either be used directly or as queued jobs by other services.\n   */\n  scripts?: Record<string, string>;\n  /**\n   * A path/pattern or list of paths/patterns of JavaScript tests provided for this service.\n   */\n  tests?: string | string[];\n  /**\n   * The full name of the author of the service (i.e. you). This will be shown in the web interface.\n   */\n  author?: string;\n  /**\n   * A list of names of people that have contributed to the development of the service in some way. This will be shown in the web interface.\n   */\n  contributors?: string[];\n  /**\n   * A human-readable description of the service. This will be shown in the web interface.\n   */\n  description?: string;\n  /**\n   * A list of keywords that help categorize this service. This is used by the Foxx Store installers to organize services.\n   */\n  keywords?: string[];\n  /**\n   * A string identifying the license under which the service is published, ideally in the form of an SPDX license identifier. This will be shown in the web interface.\n   */\n  license?: string;\n  /**\n   * The name of the Foxx service. This will be shown in the web interface.\n   */\n  name?: string;\n  /**\n   * The filename of a thumbnail that will be used alongside the service in the web interface. This should be a JPEG or PNG image that looks good at sizes 50x50 and 160x160.\n   */\n  thumbnail?: string;\n  /**\n   * The version number of the Foxx service. The version number must follow the semantic versioning format. This will be shown in the web interface.\n   */\n  version?: string;\n};\n\n/**\n * A configuration option.\n */\nexport type Configuration = {\n  /**\n   * A human-readable description of the option.\n   */\n  description?: string;\n  /**\n   * The type of value expected for this option.\n   */\n  type:\n    | \"integer\"\n    | \"boolean\"\n    | \"number\"\n    | \"string\"\n    | \"json\"\n    | \"password\"\n    | \"int\"\n    | \"bool\";\n  /**\n   * The default value for this option in plain JSON. Can be omitted to provide no default value.\n   */\n  default?: any;\n  /**\n   * Whether the service can not function without this option. Defaults to true unless a default value is provided.\n   */\n  required?: boolean;\n};\n\n/**\n * A service dependency.\n */\nexport type Dependency = {\n  /**\n   * Name of the API the service expects.\n   */\n  name?: string;\n  /**\n   * The semantic version ranges of the API the service expects.\n   */\n  version?: string;\n  /**\n   * A description of how the API is used or why it is needed.\n   */\n  description?: string;\n  /**\n   * Whether the service can not function without this dependency.\n   */\n  required?: boolean;\n  /**\n   * Whether the dependency can be specified more than once.\n   */\n  multiple?: boolean;\n};\n\n/**\n * A service file asset.\n */\nexport type File = {\n  /**\n   * Relative path of the file or folder within the service.\n   */\n  path: string;\n  /**\n   * If set to true the file will be served with gzip-encoding if supported by the client. This can be useful when serving text files like client-side JavaScript, CSS or HTML.\n   */\n  gzip?: boolean;\n  /**\n   * The MIME content type of the file. Defaults to an intelligent guess based on the filename's extension.\n   */\n  type?: string;\n};\n"]}