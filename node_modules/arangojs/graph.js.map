{"version": 3, "file": "graph.js", "sourceRoot": "", "sources": ["../src/graph.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;GAaG;AACH,6CAMsB;AAGtB,2CASqB;AACrB,mCAAwC;AACxC,uCAAkE;AAElE;;;;GAIG;AACH,SAAgB,aAAa,CAAC,KAAU;IACtC,OAAO,OAAO,CAAC,KAAK,IAAI,KAAK,CAAC,aAAa,CAAC,CAAC;AAC/C,CAAC;AAFD,sCAEC;AAED;;GAEG;AACH,SAAS,oBAAoB,CAAC,IAAS,EAAE,IAAmC;IAC1E,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,CAAC;IAChE,MAAM,MAAM,GAAG,EAAE,GAAG,IAAI,EAAE,GAAG,GAAG,EAAE,CAAC;IACnC,IAAI,OAAO,MAAM,KAAK,WAAW;QAAE,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;IACvD,IAAI,OAAO,MAAM,KAAK,WAAW;QAAE,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;IACvD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACH,SAAS,oBAAoB,CAAC,OAA8B;IAC1D,MAAM,cAAc,GAAG,EAAoB,CAAC;IAC5C,cAAc,CAAC,UAAU,GAAG,IAAA,+BAAkB,EAAC,OAAO,CAAC,UAAU,CAAC,CAAC;IACnE,cAAc,CAAC,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC;QAC/C,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,+BAAkB,CAAC;QACtC,CAAC,CAAC,CAAC,IAAA,+BAAkB,EAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;IACvC,cAAc,CAAC,EAAE,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;QAC3C,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,+BAAkB,CAAC;QACpC,CAAC,CAAC,CAAC,IAAA,+BAAkB,EAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;IACrC,OAAO,cAAc,CAAC;AACxB,CAAC;AA8TD;;;;GAIG;AACH,MAAa,qBAAqB;IAQhC;;OAEG;IACH,YAAY,EAAY,EAAE,IAAY,EAAE,KAAY;QAClD,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACvC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;QACnC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACtB,CAAC;IAED;;;;OAIG;IACH,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED;;;;;;;;;;;;;;;;;;;OAmBG;IACH,KAAK,CAAC,YAAY,CAAC,QAA0B;QAC3C,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAC3B;gBACE,MAAM,EAAE,MAAM;gBACd,IAAI,EAAE,iBAAiB,kBAAkB,CACvC,IAAI,CAAC,KAAK,CAAC,IAAI,CAChB,WAAW,SAAS,CAAC,IAAA,2BAAe,EAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;aAC/D,EACD,GAAG,EAAE,CAAC,IAAI,CACX,CAAC;QACJ,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAClB,IAAI,GAAG,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;gBACrB,OAAO,KAAK,CAAC;YACf,CAAC;YACD,MAAM,GAAG,CAAC;QACZ,CAAC;IACH,CAAC;IA+ED,KAAK,CAAC,MAAM,CACV,QAA0B,EAC1B,UAAgD,EAAE;QAElD,IAAI,OAAO,OAAO,KAAK,SAAS,EAAE,CAAC;YACjC,OAAO,GAAG,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC;QAClC,CAAC;QACD,MAAM,EACJ,cAAc,GAAG,SAAS,EAC1B,QAAQ,GAAG,KAAK,EAChB,GAAG,EACH,GAAG,EAAE,EACN,GAAG,OAAO,CAAC;QACZ,MAAM,OAAO,GAAY,EAAE,CAAC;QAC5B,IAAI,GAAG;YAAE,OAAO,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC;QACnC,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAC7B;YACE,IAAI,EAAE,iBAAiB,kBAAkB,CACvC,IAAI,CAAC,KAAK,CAAC,IAAI,CAChB,WAAW,SAAS,CAAC,IAAA,2BAAe,EAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YAC9D,OAAO;YACP,EAAE;YACF,cAAc;SACf,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CACzB,CAAC;QACF,IAAI,CAAC,QAAQ;YAAE,OAAO,MAAM,CAAC;QAC7B,IAAI,CAAC;YACH,OAAO,MAAM,MAAM,CAAC;QACtB,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAClB,IAAI,IAAA,qBAAa,EAAC,GAAG,CAAC,IAAI,GAAG,CAAC,QAAQ,KAAK,0BAAkB,EAAE,CAAC;gBAC9D,OAAO,IAAI,CAAC;YACd,CAAC;YACD,MAAM,GAAG,CAAC;QACZ,CAAC;IACH,CAAC;IAuBD,IAAI,CAAC,IAAqB,EAAE,OAAsC;QAChE,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,iBAAiB,kBAAkB,CACvC,IAAI,CAAC,KAAK,CAAC,IAAI,CAChB,WAAW,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YAC5C,IAAI,EAAE,IAAI;YACV,EAAE,EAAE,OAAO;SACZ,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAClD,CAAC;IACJ,CAAC;IA+BD,OAAO,CACL,QAA0B,EAC1B,QAAyB,EACzB,UAAyC,EAAE;QAE3C,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;YAChC,OAAO,GAAG,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC;QAC7B,CAAC;QACD,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;QAC/B,MAAM,OAAO,GAAY,EAAE,CAAC;QAC5B,IAAI,GAAG;YAAE,OAAO,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC;QACnC,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,iBAAiB,kBAAkB,CACvC,IAAI,CAAC,KAAK,CAAC,IAAI,CAChB,WAAW,SAAS,CAAC,IAAA,2BAAe,EAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YAC9D,IAAI,EAAE,QAAQ;YACd,EAAE;YACF,OAAO;SACR,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAClD,CAAC;IACJ,CAAC;IA+BD,MAAM,CACJ,QAA0B,EAC1B,QAAgC,EAChC,UAAyC,EAAE;QAE3C,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;YAChC,OAAO,GAAG,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC;QAC7B,CAAC;QACD,MAAM,OAAO,GAAY,EAAE,CAAC;QAC5B,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;QAC/B,IAAI,GAAG;YAAE,OAAO,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC;QACnC,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,OAAO;YACf,IAAI,EAAE,iBAAiB,kBAAkB,CACvC,IAAI,CAAC,KAAK,CAAC,IAAI,CAChB,WAAW,SAAS,CAAC,IAAA,2BAAe,EAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YAC9D,IAAI,EAAE,QAAQ;YACd,EAAE;YACF,OAAO;SACR,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAClD,CAAC;IACJ,CAAC;IAiCD,MAAM,CACJ,QAA0B,EAC1B,UAAwC,EAAE;QAE1C,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;YAChC,OAAO,GAAG,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC;QAC7B,CAAC;QACD,MAAM,OAAO,GAAY,EAAE,CAAC;QAC5B,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;QAC/B,IAAI,GAAG;YAAE,OAAO,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC;QACnC,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,QAAQ;YAChB,IAAI,EAAE,iBAAiB,kBAAkB,CACvC,IAAI,CAAC,KAAK,CAAC,IAAI,CAChB,WAAW,SAAS,CAAC,IAAA,2BAAe,EAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YAC9D,EAAE;YACF,OAAO;SACR,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CACnD,CAAC;IACJ,CAAC;CACF;AA7YD,sDA6YC;AAED;;;;GAIG;AACH,MAAa,mBAAmB;IAQ9B;;OAEG;IACH,YAAY,EAAY,EAAE,IAAY,EAAE,KAAY;QAClD,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACvC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;QACnC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACtB,CAAC;IAED;;;;OAIG;IACH,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED;;;;;;;;;;;;;;;;;;;OAmBG;IACH,KAAK,CAAC,UAAU,CAAC,QAA0B;QACzC,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAC3B;gBACE,MAAM,EAAE,MAAM;gBACd,IAAI,EAAE,iBAAiB,kBAAkB,CACvC,IAAI,CAAC,KAAK,CAAC,IAAI,CAChB,SAAS,SAAS,CAAC,IAAA,2BAAe,EAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;aAC7D,EACD,GAAG,EAAE,CAAC,IAAI,CACX,CAAC;QACJ,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAClB,IAAI,GAAG,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;gBACrB,OAAO,KAAK,CAAC;YACf,CAAC;YACD,MAAM,GAAG,CAAC;QACZ,CAAC;IACH,CAAC;IA4ED,KAAK,CAAC,IAAI,CACR,QAA0B,EAC1B,UAAgD,EAAE;QAElD,IAAI,OAAO,OAAO,KAAK,SAAS,EAAE,CAAC;YACjC,OAAO,GAAG,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC;QAClC,CAAC;QACD,MAAM,EACJ,cAAc,GAAG,SAAS,EAC1B,QAAQ,GAAG,KAAK,EAChB,GAAG,EACH,GAAG,EAAE,EACN,GAAG,OAAO,CAAC;QACZ,MAAM,OAAO,GAAY,EAAE,CAAC;QAC5B,IAAI,GAAG;YAAE,OAAO,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC;QACnC,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAC7B;YACE,IAAI,EAAE,iBAAiB,kBAAkB,CACvC,IAAI,CAAC,KAAK,CAAC,IAAI,CAChB,SAAS,SAAS,CAAC,IAAA,2BAAe,EAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YAC5D,EAAE;YACF,cAAc;SACf,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CACvB,CAAC;QACF,IAAI,CAAC,QAAQ;YAAE,OAAO,MAAM,CAAC;QAC7B,IAAI,CAAC;YACH,OAAO,MAAM,MAAM,CAAC;QACtB,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAClB,IAAI,IAAA,qBAAa,EAAC,GAAG,CAAC,IAAI,GAAG,CAAC,QAAQ,KAAK,0BAAkB,EAAE,CAAC;gBAC9D,OAAO,IAAI,CAAC;YACd,CAAC;YACD,MAAM,GAAG,CAAC;QACZ,CAAC;IACH,CAAC;IAsBD,IAAI,CAAC,IAAiB,EAAE,OAAsC;QAC5D,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,iBAAiB,kBAAkB,CACvC,IAAI,CAAC,KAAK,CAAC,IAAI,CAChB,SAAS,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YAC1C,IAAI,EAAE,IAAI;YACV,EAAE,EAAE,OAAO;SACZ,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAChD,CAAC;IACJ,CAAC;IAuCD,OAAO,CACL,QAA0B,EAC1B,QAAqB,EACrB,UAAyC,EAAE;QAE3C,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;YAChC,OAAO,GAAG,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC;QAC7B,CAAC;QACD,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;QAC/B,MAAM,OAAO,GAAY,EAAE,CAAC;QAC5B,IAAI,GAAG;YAAE,OAAO,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC;QACnC,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,iBAAiB,kBAAkB,CACvC,IAAI,CAAC,KAAK,CAAC,IAAI,CAChB,SAAS,SAAS,CAAC,IAAA,2BAAe,EAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YAC5D,IAAI,EAAE,QAAQ;YACd,EAAE;YACF,OAAO;SACR,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAChD,CAAC;IACJ,CAAC;IAuCD,MAAM,CACJ,QAA0B,EAC1B,QAA4B,EAC5B,UAAyC,EAAE;QAE3C,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;YAChC,OAAO,GAAG,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC;QAC7B,CAAC;QACD,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;QAC/B,MAAM,OAAO,GAAY,EAAE,CAAC;QAC5B,IAAI,GAAG;YAAE,OAAO,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC;QACnC,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,OAAO;YACf,IAAI,EAAE,iBAAiB,kBAAkB,CACvC,IAAI,CAAC,KAAK,CAAC,IAAI,CAChB,SAAS,SAAS,CAAC,IAAA,2BAAe,EAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YAC5D,IAAI,EAAE,QAAQ;YACd,EAAE;YACF,OAAO;SACR,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAChD,CAAC;IACJ,CAAC;IAyBD,MAAM,CACJ,QAA0B,EAC1B,UAAwC,EAAE;QAE1C,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;YAChC,OAAO,GAAG,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC;QAC7B,CAAC;QACD,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;QAC/B,MAAM,OAAO,GAAY,EAAE,CAAC;QAC5B,IAAI,GAAG;YAAE,OAAO,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC;QACnC,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,QAAQ;YAChB,IAAI,EAAE,iBAAiB,kBAAkB,CACvC,IAAI,CAAC,KAAK,CAAC,IAAI,CAChB,SAAS,SAAS,CAAC,IAAA,2BAAe,EAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YAC5D,EAAE;YACF,OAAO;SACR,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CACnD,CAAC;IACJ,CAAC;CACF;AAhZD,kDAgZC;AAED;;GAEG;AACH,MAAa,KAAK;IAKhB;;OAEG;IACH,YAAY,EAAY,EAAE,IAAY;QACpC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACnC,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACH,IAAI,aAAa;QACf,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED;;;;;;;;;;OAUG;IACH,KAAK,CAAC,MAAM;QACV,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,GAAG,EAAE,CAAC;YACjB,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAClB,IAAI,IAAA,qBAAa,EAAC,GAAG,CAAC,IAAI,GAAG,CAAC,QAAQ,KAAK,uBAAe,EAAE,CAAC;gBAC3D,OAAO,KAAK,CAAC;YACf,CAAC;YACD,MAAM,GAAG,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;;;;;;;;;OAUG;IACH,GAAG;QACD,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB,EAAE,IAAI,EAAE,iBAAiB,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,EAC3D,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CACxB,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,MAAM,CACJ,eAAwC,EACxC,UAA8B,EAAE;QAEhC,MAAM,EAAE,iBAAiB,EAAE,UAAU,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,IAAI,EAAE,GACpE,OAAO,CAAC;QACV,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,eAAe;YACrB,IAAI,EAAE;gBACJ,iBAAiB,EACf,iBAAiB;oBACjB,CAAC,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC;wBAC/B,CAAC,CAAC,iBAAiB,CAAC,GAAG,CAAC,+BAAkB,CAAC;wBAC3C,CAAC,CAAC,CAAC,IAAA,+BAAkB,EAAC,iBAAiB,CAAC,CAAC,CAAC;gBAC9C,eAAe,EAAE,eAAe,CAAC,GAAG,CAAC,oBAAoB,CAAC;gBAC1D,OAAO;gBACP,IAAI,EAAE,IAAI,CAAC,KAAK;gBAChB,OAAO,EAAE,EAAE,GAAG,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,CAAC,+BAAkB,CAAC,EAAE;aACtE;YACD,EAAE,EAAE,EAAE,WAAW,EAAE;SACpB,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CACxB,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,IAAI,CAAC,kBAA2B,KAAK;QACnC,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,QAAQ;YAChB,IAAI,EAAE,iBAAiB,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YACvD,EAAE,EAAE,EAAE,eAAe,EAAE;SACxB,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAC1B,CAAC;IACJ,CAAC;IAED;;;;;;OAMG;IACH,gBAAgB,CACd,UAAqC;QAErC,OAAO,IAAI,qBAAqB,CAC9B,IAAI,CAAC,GAAG,EACR,IAAA,+BAAkB,EAAC,UAAU,CAAC,EAC9B,IAAI,CACL,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,qBAAqB;QACnB,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB,EAAE,IAAI,EAAE,iBAAiB,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,EAClE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAC9B,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACH,KAAK,CAAC,iBAAiB;QACrB,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACjD,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,qBAAqB,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;IAC9E,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACH,mBAAmB,CACjB,UAAqC,EACrC,UAAsC,EAAE;QAExC,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;QACxC,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,iBAAiB,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS;YAC9D,IAAI,EAAE;gBACJ,UAAU,EAAE,IAAA,+BAAkB,EAAC,UAAU,CAAC;gBAC1C,OAAO,EAAE,EAAE,GAAG,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,CAAC,+BAAkB,CAAC,EAAE;aACtE;SACF,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CACxB,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,sBAAsB,CACpB,UAAqC,EACrC,iBAA0B,KAAK;QAE/B,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,QAAQ;YAChB,IAAI,EAAE,iBAAiB,kBAAkB,CACvC,IAAI,CAAC,KAAK,CACX,WAAW,kBAAkB,CAAC,IAAA,+BAAkB,EAAC,UAAU,CAAC,CAAC,EAAE;YAChE,EAAE,EAAE;gBACF,cAAc;aACf;SACF,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CACxB,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACH,cAAc,CACZ,UAAqC;QAErC,OAAO,IAAI,mBAAmB,CAC5B,IAAI,CAAC,GAAG,EACR,IAAA,+BAAkB,EAAC,UAAU,CAAC,EAC9B,IAAI,CACL,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,mBAAmB;QACjB,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB,EAAE,IAAI,EAAE,iBAAiB,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,EAChE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAC9B,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;OAuBG;IACH,KAAK,CAAC,eAAe;QACnB,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC/C,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,mBAAmB,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;IAC5E,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACH,iBAAiB,CACf,cAAqC,EACrC,UAAoC,EAAE;QAEtC,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;QACxC,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,iBAAiB,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO;YAC5D,IAAI,EAAE;gBACJ,GAAG,oBAAoB,CAAC,cAAc,CAAC;gBACvC,OAAO,EAAE,EAAE,GAAG,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,CAAC,+BAAkB,CAAC,EAAE;aACtE;SACF,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CACxB,CAAC;IACJ,CAAC;IA8DD,qBAAqB,CACnB,iCAGyB,EACzB,uBAEgC,EAChC,UAAwC,EAAE;QAE1C,IAAI,UAAU,GAAG,iCAEG,CAAC;QACrB,IAAI,cAAc,GAAG,uBAAgD,CAAC;QACtE,IACE,uBAAuB;YACvB,CAAC,uBAAuB,CAAC,cAAc,CAAC,YAAY,CAAC,EACrD,CAAC;YACD,OAAO,GAAG,uBAAuD,CAAC;YAClE,uBAAuB,GAAG,SAAS,CAAC;QACtC,CAAC;QACD,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC7B,cAAc;gBACZ,iCAA0D,CAAC;YAC7D,UAAU,GAAG,cAAc,CAAC,UAAU,CAAC;QACzC,CAAC;QACD,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;QACxC,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,iBAAiB,kBAAkB,CACvC,IAAI,CAAC,KAAK,CACX,SAAS,kBAAkB,CAAC,IAAA,+BAAkB,EAAC,UAAU,CAAC,CAAC,EAAE;YAC9D,IAAI,EAAE;gBACJ,GAAG,oBAAoB,CAAC,cAAc,CAAC;gBACvC,OAAO,EAAE,EAAE,GAAG,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,CAAC,+BAAkB,CAAC,EAAE;aACtE;SACF,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CACxB,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,oBAAoB,CAClB,UAAqC,EACrC,iBAA0B,KAAK;QAE/B,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,QAAQ;YAChB,IAAI,EAAE,iBAAiB,kBAAkB,CACvC,IAAI,CAAC,KAAK,CACX,SAAS,kBAAkB,CAAC,IAAA,+BAAkB,EAAC,UAAU,CAAC,CAAC,EAAE;YAC9D,EAAE,EAAE;gBACF,cAAc;aACf;SACF,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CACxB,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA+BG;IACH,SAAS,CAAC,WAAmB,EAAE,OAA0B;QACvD,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,iBAAiB;YACvB,IAAI,EAAE;gBACJ,GAAG,OAAO;gBACV,WAAW;gBACX,SAAS,EAAE,IAAI,CAAC,KAAK;aACtB;SACF,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CACzB,CAAC;IACJ,CAAC;CACF;AA7lBD,sBA6lBC", "sourcesContent": ["/**\n * ```ts\n * import type {\n *   Graph,\n *   GraphVertexCollection,\n *   GraphEdgeCollection,\n * } from \"arangojs/graph\";\n * ```\n *\n * The \"graph\" module provides graph related types and interfaces\n * for TypeScript.\n *\n * @packageDocumentation\n */\nimport {\n  ArangoCollection,\n  collectionToString,\n  DocumentCollection,\n  EdgeCollection,\n  TraversalOptions,\n} from \"./collection\";\nimport { Headers } from \"./connection\";\nimport { Database } from \"./database\";\nimport {\n  Document,\n  DocumentData,\n  DocumentMetadata,\n  DocumentSelector,\n  Edge,\n  EdgeData,\n  Patch,\n  _documentHandle,\n} from \"./documents\";\nimport { isArangoError } from \"./error\";\nimport { DOCUMENT_NOT_FOUND, GRAPH_NOT_FOUND } from \"./lib/codes\";\n\n/**\n * Indicates whether the given value represents a {@link graph.Graph}.\n *\n * @param graph - A value that might be a Graph.\n */\nexport function isArangoGraph(graph: any): graph is Graph {\n  return Boolean(graph && graph.isArangoGraph);\n}\n\n/**\n * @internal\n */\nfunction mungeGharialResponse(body: any, prop: \"vertex\" | \"edge\" | \"removed\") {\n  const { new: newDoc, old: oldDoc, [prop]: doc, ...meta } = body;\n  const result = { ...meta, ...doc };\n  if (typeof newDoc !== \"undefined\") result.new = newDoc;\n  if (typeof oldDoc !== \"undefined\") result.old = oldDoc;\n  return result;\n}\n\n/**\n * @internal\n */\nfunction coerceEdgeDefinition(options: EdgeDefinitionOptions): EdgeDefinition {\n  const edgeDefinition = {} as EdgeDefinition;\n  edgeDefinition.collection = collectionToString(options.collection);\n  edgeDefinition.from = Array.isArray(options.from)\n    ? options.from.map(collectionToString)\n    : [collectionToString(options.from)];\n  edgeDefinition.to = Array.isArray(options.to)\n    ? options.to.map(collectionToString)\n    : [collectionToString(options.to)];\n  return edgeDefinition;\n}\n\n/**\n * Options for retrieving a document from a graph collection.\n */\nexport type GraphCollectionReadOptions = {\n  /**\n   * If set to a document revision, the document will only be returned if its\n   * `_rev` property matches this value.\n   *\n   * See also {@link documents.DocumentMetadata}.\n   */\n  rev?: string;\n  /**\n   * If set to `true`, `null` is returned instead of an exception being thrown\n   * if the document does not exist.\n   *\n   * Default: `false`\n   */\n  graceful?: boolean;\n  /**\n   * If set to `true`, the request will explicitly permit ArangoDB to return a\n   * potentially dirty or stale result and arangojs will load balance the\n   * request without distinguishing between leaders and followers.\n   *\n   * Default: `false`\n   */\n  allowDirtyRead?: boolean;\n};\n\n/**\n * Options for inserting a document into a graph collection.\n */\nexport type GraphCollectionInsertOptions = {\n  /**\n   * If set to `true`, data will be synchronized to disk before returning.\n   *\n   * Default: `false`\n   */\n  waitForSync?: boolean;\n  /**\n   * If set to `true`, the complete new document will be returned as the `new`\n   * property on the result object.\n   *\n   * Default: `false`\n   */\n  returnNew?: boolean;\n};\n\n/**\n * Options for replacing a document in a graph collection.\n */\nexport type GraphCollectionReplaceOptions = {\n  /**\n   * If set to a document revision, the document will only be modified if its\n   * `_rev` property matches this value.\n   *\n   * See also {@link documents.DocumentMetadata}.\n   */\n  rev?: string;\n  /**\n   * If set to `true`, data will be synchronized to disk before returning.\n   *\n   * Default: `false`\n   */\n  waitForSync?: boolean;\n  /**\n   * If set to `false`, properties with a value of `null` will be removed from\n   * the new document.\n   *\n   * Default: `true`\n   */\n  keepNull?: boolean;\n  /**\n   * If set to `true`, the complete old document will be returned as the `old`\n   * property on the result object.\n   *\n   * Default: `false`\n   */\n  returnOld?: boolean;\n  /**\n   * If set to `true`, the complete new document will be returned as the `new`\n   * property on the result object.\n   *\n   * Default: `false`\n   */\n  returnNew?: boolean;\n};\n\n/**\n * Options for removing a document from a graph collection.\n */\nexport type GraphCollectionRemoveOptions = {\n  /**\n   * If set to a document revision, the document will only be removed if its\n   * `_rev` property matches this value.\n   *\n   * See also {@link documents.DocumentMetadata}.\n   */\n  rev?: string;\n  /**\n   * If set to `true`, data will be synchronized to disk before returning.\n   *\n   * Default: `false`\n   */\n  waitForSync?: boolean;\n  /**\n   * If set to `true`, the complete old document will be returned as the `old`\n   * property on the result object.\n   *\n   * Default: `false`\n   */\n  returnOld?: boolean;\n};\n\n/**\n * Definition of a relation in a {@link graph.Graph}.\n */\nexport type EdgeDefinition = {\n  /**\n   * Name of the collection containing the edges.\n   */\n  collection: string;\n  /**\n   * Array of names of collections containing the start vertices.\n   */\n  from: string[];\n  /**\n   * Array of names of collections containing the end vertices.\n   */\n  to: string[];\n};\n\n/**\n * An edge definition used to define a collection of edges in a {@link graph.Graph}.\n */\nexport type EdgeDefinitionOptions = {\n  /**\n   * Collection containing the edges.\n   */\n  collection: string | ArangoCollection;\n  /**\n   * Collection or collections containing the start vertices.\n   */\n  from: (string | ArangoCollection)[] | string | ArangoCollection;\n  /**\n   * Collection or collections containing the end vertices.\n   */\n  to: (string | ArangoCollection)[] | string | ArangoCollection;\n};\n\n/**\n * General information about a graph.\n */\nexport type GraphInfo = {\n  /**\n   * Key of the document internally representing this graph.\n   *\n   * See {@link documents.DocumentMetadata}.\n   *\n   * @internal\n   */\n  _key: string;\n  /**\n   * Unique identifier of the document internally representing this graph.\n   *\n   * See {@link documents.DocumentMetadata}.\n   *\n   * @internal\n   */\n  _id: string;\n  /**\n   * Revision of the document internally representing this graph.\n   *\n   * See {@link documents.DocumentMetadata}.\n   *\n   * @internal\n   */\n  _rev: string;\n  /**\n   * Name of the graph.\n   */\n  name: string;\n  /**\n   * Definitions for the relations of the graph.\n   */\n  edgeDefinitions: EdgeDefinition[];\n  /**\n   * Additional vertex collections. Documents within these collections do not\n   * have edges within this graph.\n   */\n  orphanCollections: string[];\n\n  /**\n   * (Cluster only.) Number of shards that is used for every collection\n   * within this graph.\n   */\n  numberOfShards?: number;\n  /**\n   * (Cluster only.) Replication factor used when initially creating\n   * collections for this graph.\n   */\n  replicationFactor?: number;\n  /**\n   * (Cluster only.) Write concern for new collections in the graph.\n   */\n  writeConcern?: number;\n  /**\n   * (Enterprise Edition cluster only.) If set to `true`, the graph is a\n   * SatelliteGraph.\n   */\n  isSatellite?: boolean;\n  /**\n   * (Enterprise Edition cluster only.) If set to `true`, the graph has been\n   * created as a SmartGraph.\n   */\n  isSmart?: boolean;\n  /**\n   * (Enterprise Edition cluster only.) Attribute containing the shard key\n   * value to use for smart sharding.\n   */\n  smartGraphAttribute?: string;\n  /**\n   * (Enterprise Edition cluster only.) If set to `true`, the graph has been\n   * created as a Disjoint SmartGraph.\n   */\n  isDisjoint?: boolean;\n};\n\n/**\n * Option for creating a graph.\n */\nexport type CreateGraphOptions = {\n  /**\n   * If set to `true`, the request will wait until all modifications have been\n   * synchronized to disk before returning successfully.\n   *\n   * Default: `false`\n   */\n  waitForSync?: boolean;\n  /**\n   * Additional vertex collections. Documents within these collections do not\n   * have edges within this graph.\n   */\n  orphanCollections?: (string | ArangoCollection)[] | string | ArangoCollection;\n\n  /**\n   * (Cluster only.) Number of shards that is used for every collection\n   * within this graph.\n   *\n   * Has no effect when `replicationFactor` is set to `\"satellite\"`.\n   */\n  numberOfShards?: number;\n  /**\n   * (Cluster only.) Replication factor used when initially creating\n   * collections for this graph.\n   *\n   * Default: `1`\n   */\n  replicationFactor?: number | \"satellite\";\n  /**\n   * (Cluster only.) Write concern for new collections in the graph.\n   *\n   * Has no effect when `replicationFactor` is set to `\"satellite\"`.\n   */\n  writeConcern?: number;\n\n  // Extra options\n  /**\n   * (Enterprise Edition cluster only.) If set to `true`, the graph will be\n   * created as a SmartGraph.\n   *\n   * Default: `false`\n   */\n  isSmart?: boolean;\n  /**\n   * (Enterprise Edition cluster only.) Attribute containing the shard key\n   * value to use for smart sharding.\n   */\n  smartGraphAttribute?: string;\n  /**\n   * (Enterprise Edition cluster only.) If set to `true`, the graph will be\n   * created as a Disjoint SmartGraph.\n   *\n   * Default: `false`\n   */\n  isDisjoint?: boolean;\n  /**\n   * (Enterprise Edition cluster only.) Collections to be included in a Hybrid\n   * SmartGraph.\n   */\n  satellites?: (string | ArangoCollection)[];\n};\n\nexport type AddVertexCollectionOptions = {\n  /**\n   * (Enterprise Edition cluster only.) Collections to be included in a Hybrid\n   * SmartGraph.\n   */\n  satellites?: (string | ArangoCollection)[];\n};\n\nexport type AddEdgeDefinitionOptions = {\n  /**\n   * (Enterprise Edition cluster only.) Collections to be included in a Hybrid\n   * SmartGraph.\n   */\n  satellites?: (string | ArangoCollection)[];\n};\n\nexport type ReplaceEdgeDefinitionOptions = {\n  /**\n   * (Enterprise Edition cluster only.) Collections to be included in a Hybrid\n   * SmartGraph.\n   */\n  satellites?: string[];\n};\n\n/**\n * Represents a {@link collection.DocumentCollection} of vertices in a {@link graph.Graph}.\n *\n * @param T - Type to use for document data. Defaults to `any`.\n */\nexport class GraphVertexCollection<T extends Record<string, any> = any>\n  implements ArangoCollection\n{\n  protected _db: Database;\n  protected _name: string;\n  protected _graph: Graph;\n  protected _collection: DocumentCollection<T>;\n\n  /**\n   * @internal\n   */\n  constructor(db: Database, name: string, graph: Graph) {\n    this._db = db;\n    this._collection = db.collection(name);\n    this._name = this._collection.name;\n    this._graph = graph;\n  }\n\n  /**\n   * @internal\n   *\n   * Indicates that this object represents an ArangoDB collection.\n   */\n  get isArangoCollection(): true {\n    return true;\n  }\n\n  /**\n   * Name of the collection.\n   */\n  get name() {\n    return this._name;\n  }\n\n  /**\n   * A {@link collection.DocumentCollection} instance for this vertex collection.\n   */\n  get collection() {\n    return this._collection;\n  }\n\n  /**\n   * The {@link graph.Graph} instance this vertex collection is bound to.\n   */\n  get graph() {\n    return this._graph;\n  }\n\n  /**\n   * Checks whether a vertex matching the given key or id exists in this\n   * collection.\n   *\n   * Throws an exception when passed a vertex or `_id` from a different\n   * collection.\n   *\n   * @param selector - Document `_key`, `_id` or object with either of those\n   * properties (e.g. a vertex from this collection).\n   *\n   * @example\n   * ```js\n   * const graph = db.graph(\"some-graph\");\n   * const collection = graph.vertexCollection(\"vertices\");\n   * const exists = await collection.vertexExists(\"abc123\");\n   * if (!exists) {\n   *   console.log(\"Vertex does not exist\");\n   * }\n   * ```\n   */\n  async vertexExists(selector: DocumentSelector): Promise<boolean> {\n    try {\n      return await this._db.request(\n        {\n          method: \"HEAD\",\n          path: `/_api/gharial/${encodeURIComponent(\n            this.graph.name\n          )}/vertex/${encodeURI(_documentHandle(selector, this._name))}`,\n        },\n        () => true\n      );\n    } catch (err: any) {\n      if (err.code === 404) {\n        return false;\n      }\n      throw err;\n    }\n  }\n\n  /**\n   * Retrieves the vertex matching the given key or id.\n   *\n   * Throws an exception when passed a vertex or `_id` from a different\n   * collection.\n   *\n   * @param selector - Document `_key`, `_id` or object with either of those\n   * properties (e.g. a vertex from this collection).\n   * @param options - Options for retrieving the vertex.\n   *\n   * @example\n   * ```js\n   * const graph = db.graph(\"some-graph\");\n   * const collection = graph.vertexCollection(\"vertices\");\n   * try {\n   *   const vertex = await collection.vertex(\"abc123\");\n   *   console.log(vertex);\n   * } catch (e: any) {\n   *   console.error(\"Could not find vertex\");\n   * }\n   * ```\n   *\n   * @example\n   * ```js\n   * const graph = db.graph(\"some-graph\");\n   * const collection = graph.vertexCollection(\"vertices\");\n   * const vertex = await collection.vertex(\"abc123\", { graceful: true });\n   * if (vertex) {\n   *   console.log(vertex);\n   * } else {\n   *   console.error(\"Could not find vertex\");\n   * }\n   * ```\n   */\n  async vertex(\n    selector: DocumentSelector,\n    options?: GraphCollectionReadOptions\n  ): Promise<Document<T>>;\n  /**\n   * Retrieves the vertex matching the given key or id.\n   *\n   * Throws an exception when passed a vertex or `_id` from a different\n   * collection.\n   *\n   * @param selector - Document `_key`, `_id` or object with either of those\n   * properties (e.g. a vertex from this collection).\n   * @param graceful - If set to `true`, `null` is returned instead of an\n   * exception being thrown if the vertex does not exist.\n   *\n   * @example\n   * ```js\n   * const graph = db.graph(\"some-graph\");\n   * const collection = graph.vertexCollection(\"vertices\");\n   * try {\n   *   const vertex = await collection.vertex(\"abc123\", false);\n   *   console.log(vertex);\n   * } catch (e: any) {\n   *   console.error(\"Could not find vertex\");\n   * }\n   * ```\n   *\n   * @example\n   * ```js\n   * const graph = db.graph(\"some-graph\");\n   * const collection = graph.vertexCollection(\"vertices\");\n   * const vertex = await collection.vertex(\"abc123\", true);\n   * if (vertex) {\n   *   console.log(vertex);\n   * } else {\n   *   console.error(\"Could not find vertex\");\n   * }\n   * ```\n   */\n  async vertex(\n    selector: DocumentSelector,\n    graceful: boolean\n  ): Promise<Document<T>>;\n  async vertex(\n    selector: DocumentSelector,\n    options: boolean | GraphCollectionReadOptions = {}\n  ): Promise<Document<T> | null> {\n    if (typeof options === \"boolean\") {\n      options = { graceful: options };\n    }\n    const {\n      allowDirtyRead = undefined,\n      graceful = false,\n      rev,\n      ...qs\n    } = options;\n    const headers: Headers = {};\n    if (rev) headers[\"if-match\"] = rev;\n    const result = this._db.request(\n      {\n        path: `/_api/gharial/${encodeURIComponent(\n          this.graph.name\n        )}/vertex/${encodeURI(_documentHandle(selector, this._name))}`,\n        headers,\n        qs,\n        allowDirtyRead,\n      },\n      (res) => res.body.vertex\n    );\n    if (!graceful) return result;\n    try {\n      return await result;\n    } catch (err: any) {\n      if (isArangoError(err) && err.errorNum === DOCUMENT_NOT_FOUND) {\n        return null;\n      }\n      throw err;\n    }\n  }\n\n  /**\n   * Inserts a new vertex with the given `data` into the collection.\n   *\n   * @param data - The contents of the new vertex.\n   * @param options - Options for inserting the vertex.\n   *\n   * @example\n   * ```js\n   * const graph = db.graph(\"some-graph\");\n   * const collection = graph.vertexCollection(\"friends\");\n   * const result = await collection.save(\n   *   { _key: \"a\", color: \"blue\", count: 1 },\n   *   { returnNew: true }\n   * );\n   * console.log(result.new.color, result.new.count); // \"blue\" 1\n   * ```\n   */\n  save(\n    data: DocumentData<T>,\n    options?: GraphCollectionInsertOptions\n  ): Promise<DocumentMetadata & { new?: Document<T> }>;\n  save(data: DocumentData<T>, options?: GraphCollectionInsertOptions) {\n    return this._db.request(\n      {\n        method: \"POST\",\n        path: `/_api/gharial/${encodeURIComponent(\n          this.graph.name\n        )}/vertex/${encodeURIComponent(this._name)}`,\n        body: data,\n        qs: options,\n      },\n      (res) => mungeGharialResponse(res.body, \"vertex\")\n    );\n  }\n\n  /**\n   * Replaces an existing vertex in the collection.\n   *\n   * Throws an exception when passed a vertex or `_id` from a different\n   * collection.\n   *\n   * @param selector - Document `_key`, `_id` or object with either of those\n   * properties (e.g. a vertex from this collection).\n   * @param newData - The contents of the new vertex.\n   * @param options - Options for replacing the vertex.\n   *\n   * @example\n   * ```js\n   * const graph = db.graph(\"some-graph\");\n   * const collection = graph.collection(\"vertices\");\n   * await collection.save({ _key: \"a\", color: \"blue\", count: 1 });\n   * const result = await collection.replace(\n   *   \"a\",\n   *   { color: \"red\" },\n   *   { returnNew: true }\n   * );\n   * console.log(result.new.color, result.new.count); // \"red\" undefined\n   * ```\n   */\n  replace(\n    selector: DocumentSelector,\n    newValue: DocumentData<T>,\n    options?: GraphCollectionReplaceOptions\n  ): Promise<DocumentMetadata & { new?: Document<T>; old?: Document<T> }>;\n  replace(\n    selector: DocumentSelector,\n    newValue: DocumentData<T>,\n    options: GraphCollectionReplaceOptions = {}\n  ) {\n    if (typeof options === \"string\") {\n      options = { rev: options };\n    }\n    const { rev, ...qs } = options;\n    const headers: Headers = {};\n    if (rev) headers[\"if-match\"] = rev;\n    return this._db.request(\n      {\n        method: \"PUT\",\n        path: `/_api/gharial/${encodeURIComponent(\n          this.graph.name\n        )}/vertex/${encodeURI(_documentHandle(selector, this._name))}`,\n        body: newValue,\n        qs,\n        headers,\n      },\n      (res) => mungeGharialResponse(res.body, \"vertex\")\n    );\n  }\n\n  /**\n   * Updates an existing vertex in the collection.\n   *\n   * Throws an exception when passed a vertex or `_id` from a different\n   * collection.\n   *\n   * @param selector - Document `_key`, `_id` or object with either of those\n   * properties (e.g. a vertex from this collection).\n   * @param newData - The data for updating the vertex.\n   * @param options - Options for updating the vertex.\n   *\n   * @example\n   * ```js\n   * const graph = db.graph(\"some-graph\");\n   * const collection = graph.collection(\"vertices\");\n   * await collection.save({ _key: \"a\", color: \"blue\", count: 1 });\n   * const result = await collection.update(\n   *   \"a\",\n   *   { count: 2 },\n   *   { returnNew: true }\n   * );\n   * console.log(result.new.color, result.new.count); // \"blue\" 2\n   * ```\n   */\n  update(\n    selector: DocumentSelector,\n    newValue: Patch<DocumentData<T>>,\n    options?: GraphCollectionReplaceOptions\n  ): Promise<DocumentMetadata & { new?: Document<T>; old?: Document<T> }>;\n  update(\n    selector: DocumentSelector,\n    newValue: Patch<DocumentData<T>>,\n    options: GraphCollectionReplaceOptions = {}\n  ) {\n    if (typeof options === \"string\") {\n      options = { rev: options };\n    }\n    const headers: Headers = {};\n    const { rev, ...qs } = options;\n    if (rev) headers[\"if-match\"] = rev;\n    return this._db.request(\n      {\n        method: \"PATCH\",\n        path: `/_api/gharial/${encodeURIComponent(\n          this.graph.name\n        )}/vertex/${encodeURI(_documentHandle(selector, this._name))}`,\n        body: newValue,\n        qs,\n        headers,\n      },\n      (res) => mungeGharialResponse(res.body, \"vertex\")\n    );\n  }\n\n  /**\n   * Removes an existing vertex from the collection.\n   *\n   * Throws an exception when passed a vertex or `_id` from a different\n   * collection.\n   *\n   * @param selector - Document `_key`, `_id` or object with either of those\n   * properties (e.g. a vertex from this collection).\n   * @param options - Options for removing the vertex.\n   *\n   * @example\n   * ```js\n   * const graph = db.graph(\"some-graph\");\n   * const collection = graph.vertexCollection(\"vertices\");\n   * await collection.remove(\"abc123\");\n   * // document with key \"abc123\" deleted\n   * ```\n   *\n   * @example\n   * ```js\n   * const graph = db.graph(\"some-graph\");\n   * const collection = graph.vertexCollection(\"vertices\");\n   * const doc = await collection.vertex(\"abc123\");\n   * await collection.remove(doc);\n   * // document with key \"abc123\" deleted\n   * ```\n   */\n  remove(\n    selector: DocumentSelector,\n    options?: GraphCollectionRemoveOptions\n  ): Promise<DocumentMetadata & { old?: Document<T> }>;\n  remove(\n    selector: DocumentSelector,\n    options: GraphCollectionRemoveOptions = {}\n  ) {\n    if (typeof options === \"string\") {\n      options = { rev: options };\n    }\n    const headers: Headers = {};\n    const { rev, ...qs } = options;\n    if (rev) headers[\"if-match\"] = rev;\n    return this._db.request(\n      {\n        method: \"DELETE\",\n        path: `/_api/gharial/${encodeURIComponent(\n          this.graph.name\n        )}/vertex/${encodeURI(_documentHandle(selector, this._name))}`,\n        qs,\n        headers,\n      },\n      (res) => mungeGharialResponse(res.body, \"removed\")\n    );\n  }\n}\n\n/**\n * Represents a {@link collection.EdgeCollection} of edges in a {@link graph.Graph}.\n *\n * @param T - Type to use for document data. Defaults to `any`.\n */\nexport class GraphEdgeCollection<T extends Record<string, any> = any>\n  implements ArangoCollection\n{\n  protected _db: Database;\n  protected _name: string;\n  protected _graph: Graph;\n  protected _collection: EdgeCollection<T>;\n\n  /**\n   * @internal\n   */\n  constructor(db: Database, name: string, graph: Graph) {\n    this._db = db;\n    this._collection = db.collection(name);\n    this._name = this._collection.name;\n    this._graph = graph;\n  }\n\n  /**\n   * @internal\n   *\n   * Indicates that this object represents an ArangoDB collection.\n   */\n  get isArangoCollection(): true {\n    return true;\n  }\n\n  /**\n   * Name of the collection.\n   */\n  get name() {\n    return this._name;\n  }\n\n  /**\n   * A {@link collection.EdgeCollection} instance for this edge collection.\n   */\n  get collection() {\n    return this._collection;\n  }\n\n  /**\n   * The {@link graph.Graph} instance this edge collection is bound to.\n   */\n  get graph() {\n    return this._graph;\n  }\n\n  /**\n   * Checks whether a edge matching the given key or id exists in this\n   * collection.\n   *\n   * Throws an exception when passed a edge or `_id` from a different\n   * collection.\n   *\n   * @param selector - Document `_key`, `_id` or object with either of those\n   * properties (e.g. a edge from this collection).\n   *\n   * @example\n   * ```js\n   * const graph = db.graph(\"some-graph\");\n   * const collection = graph.edgeCollection(\"friends\")\n   * const exists = await collection.edgeExists(\"abc123\");\n   * if (!exists) {\n   *   console.log(\"Edge does not exist\");\n   * }\n   * ```\n   */\n  async edgeExists(selector: DocumentSelector): Promise<boolean> {\n    try {\n      return await this._db.request(\n        {\n          method: \"HEAD\",\n          path: `/_api/gharial/${encodeURIComponent(\n            this.graph.name\n          )}/edge/${encodeURI(_documentHandle(selector, this._name))}`,\n        },\n        () => true\n      );\n    } catch (err: any) {\n      if (err.code === 404) {\n        return false;\n      }\n      throw err;\n    }\n  }\n\n  /**\n   * Retrieves the edge matching the given key or id.\n   *\n   * Throws an exception when passed a edge or `_id` from a different\n   * collection, or if the edge does not exist.\n   *\n   * @param selector - Document `_key`, `_id` or object with either of those\n   * properties (e.g. a edge from this collection).\n   * @param options - Options for retrieving the edge.\n   *\n   * @example\n   * ```js\n   * const graph = db.graph(\"some-graph\");\n   * const collection = graph.edgeCollection(\"friends\")\n   * try {\n   *   const edge = await collection.edge(\"abc123\");\n   *   console.log(edge);\n   * } catch (e: any) {\n   *   console.error(\"Could not find edge\");\n   * }\n   * ```\n   *\n   * @example\n   * ```js\n   * const graph = db.graph(\"some-graph\");\n   * const collection = graph.edgeCollection(\"friends\")\n   * const edge = await collection.edge(\"abc123\", { graceful: true });\n   * if (edge) {\n   *   console.log(edge);\n   * } else {\n   *   console.error(\"Edge does not exist\");\n   * }\n   * ```\n   */\n  async edge(\n    selector: DocumentSelector,\n    options?: GraphCollectionReadOptions\n  ): Promise<Edge<T>>;\n  /**\n   * Retrieves the edge matching the given key or id.\n   *\n   * Throws an exception when passed a edge or `_id` from a different\n   * collection, or if the edge does not exist.\n   *\n   * @param selector - Document `_key`, `_id` or object with either of those\n   * properties (e.g. a edge from this collection).\n   * @param graceful - If set to `true`, `null` is returned instead of an\n   * exception being thrown if the edge does not exist.\n   *\n   * @example\n   * ```js\n   * const graph = db.graph(\"some-graph\");\n   * const collection = graph.edgeCollection(\"friends\")\n   * try {\n   *   const edge = await collection.edge(\"abc123\", false);\n   *   console.log(edge);\n   * } catch (e: any) {\n   *   console.error(\"Could not find edge\");\n   * }\n   * ```\n   *\n   * @example\n   * ```js\n   * const graph = db.graph(\"some-graph\");\n   * const collection = graph.edgeCollection(\"friends\")\n   * const edge = await collection.edge(\"abc123\", true);\n   * if (edge) {\n   *   console.log(edge);\n   * } else {\n   *   console.error(\"Edge does not exist\");\n   * }\n   * ```\n   */\n  async edge(selector: DocumentSelector, graceful: boolean): Promise<Edge<T>>;\n  async edge(\n    selector: DocumentSelector,\n    options: boolean | GraphCollectionReadOptions = {}\n  ): Promise<Edge<T> | null> {\n    if (typeof options === \"boolean\") {\n      options = { graceful: options };\n    }\n    const {\n      allowDirtyRead = undefined,\n      graceful = false,\n      rev,\n      ...qs\n    } = options;\n    const headers: Headers = {};\n    if (rev) headers[\"if-match\"] = rev;\n    const result = this._db.request(\n      {\n        path: `/_api/gharial/${encodeURIComponent(\n          this.graph.name\n        )}/edge/${encodeURI(_documentHandle(selector, this._name))}`,\n        qs,\n        allowDirtyRead,\n      },\n      (res) => res.body.edge\n    );\n    if (!graceful) return result;\n    try {\n      return await result;\n    } catch (err: any) {\n      if (isArangoError(err) && err.errorNum === DOCUMENT_NOT_FOUND) {\n        return null;\n      }\n      throw err;\n    }\n  }\n\n  /**\n   * Inserts a new edge with the given `data` into the collection.\n   *\n   * @param data - The contents of the new edge.\n   * @param options - Options for inserting the edge.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"friends\");\n   * const result = await collection.save(\n   *   { _from: \"users/rana\", _to: \"users/mudasir\", active: false },\n   *   { returnNew: true }\n   * );\n   * ```\n   */\n  save(\n    data: EdgeData<T>,\n    options?: GraphCollectionInsertOptions\n  ): Promise<DocumentMetadata & { new?: Edge<T> }>;\n  save(data: EdgeData<T>, options?: GraphCollectionInsertOptions) {\n    return this._db.request(\n      {\n        method: \"POST\",\n        path: `/_api/gharial/${encodeURIComponent(\n          this.graph.name\n        )}/edge/${encodeURIComponent(this._name)}`,\n        body: data,\n        qs: options,\n      },\n      (res) => mungeGharialResponse(res.body, \"edge\")\n    );\n  }\n\n  /**\n   * Replaces an existing edge in the collection.\n   *\n   * Throws an exception when passed a edge or `_id` from a different\n   * collection.\n   *\n   * @param selector - Document `_key`, `_id` or object with either of those\n   * properties (e.g. a edge from this collection).\n   * @param newData - The contents of the new edge.\n   * @param options - Options for replacing the edge.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"friends\");\n   * await collection.save(\n   *   {\n   *     _key: \"musadir\",\n   *     _from: \"users/rana\",\n   *     _to: \"users/mudasir\",\n   *     active: true,\n   *     best: true\n   *   }\n   * );\n   * const result = await collection.replace(\n   *   \"musadir\",\n   *   { active: false },\n   *   { returnNew: true }\n   * );\n   * console.log(result.new.active, result.new.best); // false undefined\n   * ```\n   */\n  replace(\n    selector: DocumentSelector,\n    newValue: EdgeData<T>,\n    options?: GraphCollectionReplaceOptions\n  ): Promise<DocumentMetadata & { new?: Edge<T>; old?: Edge<T> }>;\n  replace(\n    selector: DocumentSelector,\n    newValue: EdgeData<T>,\n    options: GraphCollectionReplaceOptions = {}\n  ) {\n    if (typeof options === \"string\") {\n      options = { rev: options };\n    }\n    const { rev, ...qs } = options;\n    const headers: Headers = {};\n    if (rev) headers[\"if-match\"] = rev;\n    return this._db.request(\n      {\n        method: \"PUT\",\n        path: `/_api/gharial/${encodeURIComponent(\n          this.graph.name\n        )}/edge/${encodeURI(_documentHandle(selector, this._name))}`,\n        body: newValue,\n        qs,\n        headers,\n      },\n      (res) => mungeGharialResponse(res.body, \"edge\")\n    );\n  }\n\n  /**\n   * Updates an existing edge in the collection.\n   *\n   * Throws an exception when passed a edge or `_id` from a different\n   * collection.\n   *\n   * @param selector - Document `_key`, `_id` or object with either of those\n   * properties (e.g. a edge from this collection).\n   * @param newData - The data for updating the edge.\n   * @param options - Options for updating the edge.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"friends\");\n   * await collection.save(\n   *   {\n   *     _key: \"musadir\",\n   *     _from: \"users/rana\",\n   *     _to: \"users/mudasir\",\n   *     active: true,\n   *     best: true\n   *   }\n   * );\n   * const result = await collection.update(\n   *   \"musadir\",\n   *   { active: false },\n   *   { returnNew: true }\n   * );\n   * console.log(result.new.active, result.new.best); // false true\n   * ```\n   */\n  update(\n    selector: DocumentSelector,\n    newValue: Patch<EdgeData<T>>,\n    options?: GraphCollectionReplaceOptions\n  ): Promise<DocumentMetadata & { new?: Edge<T>; old?: Edge<T> }>;\n  update(\n    selector: DocumentSelector,\n    newValue: Patch<EdgeData<T>>,\n    options: GraphCollectionReplaceOptions = {}\n  ) {\n    if (typeof options === \"string\") {\n      options = { rev: options };\n    }\n    const { rev, ...qs } = options;\n    const headers: Headers = {};\n    if (rev) headers[\"if-match\"] = rev;\n    return this._db.request(\n      {\n        method: \"PATCH\",\n        path: `/_api/gharial/${encodeURIComponent(\n          this.graph.name\n        )}/edge/${encodeURI(_documentHandle(selector, this._name))}`,\n        body: newValue,\n        qs,\n        headers,\n      },\n      (res) => mungeGharialResponse(res.body, \"edge\")\n    );\n  }\n\n  /**\n   * Removes an existing edge from the collection.\n   *\n   * Throws an exception when passed a edge or `_id` from a different\n   * collection.\n   *\n   * @param selector - Document `_key`, `_id` or object with either of those\n   * properties (e.g. a edge from this collection).\n   * @param options - Options for removing the edge.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const collection = db.collection(\"friends\");\n   * const doc = await collection.edge(\"musadir\");\n   * await collection.remove(doc);\n   * // edge with key \"musadir\" deleted\n   * ```\n   */\n  remove(\n    selector: DocumentSelector,\n    options?: GraphCollectionRemoveOptions\n  ): Promise<DocumentMetadata & { old?: Edge<T> }>;\n  remove(\n    selector: DocumentSelector,\n    options: GraphCollectionRemoveOptions = {}\n  ) {\n    if (typeof options === \"string\") {\n      options = { rev: options };\n    }\n    const { rev, ...qs } = options;\n    const headers: Headers = {};\n    if (rev) headers[\"if-match\"] = rev;\n    return this._db.request(\n      {\n        method: \"DELETE\",\n        path: `/_api/gharial/${encodeURIComponent(\n          this.graph.name\n        )}/edge/${encodeURI(_documentHandle(selector, this._name))}`,\n        qs,\n        headers,\n      },\n      (res) => mungeGharialResponse(res.body, \"removed\")\n    );\n  }\n}\n\n/**\n * Represents a graph in a {@link database.Database}.\n */\nexport class Graph {\n  protected _name: string;\n\n  protected _db: Database;\n\n  /**\n   * @internal\n   */\n  constructor(db: Database, name: string) {\n    this._name = name.normalize(\"NFC\");\n    this._db = db;\n  }\n\n  /**\n   * @internal\n   *\n   * Indicates that this object represents an ArangoDB Graph.\n   */\n  get isArangoGraph(): true {\n    return true;\n  }\n\n  /**\n   * Name of the graph.\n   */\n  get name() {\n    return this._name;\n  }\n\n  /**\n   * Checks whether the graph exists.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const graph = db.graph(\"some-graph\");\n   * const result = await graph.exists();\n   * // result indicates whether the graph exists\n   * ```\n   */\n  async exists(): Promise<boolean> {\n    try {\n      await this.get();\n      return true;\n    } catch (err: any) {\n      if (isArangoError(err) && err.errorNum === GRAPH_NOT_FOUND) {\n        return false;\n      }\n      throw err;\n    }\n  }\n\n  /**\n   * Retrieves general information about the graph.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const graph = db.graph(\"some-graph\");\n   * const data = await graph.get();\n   * // data contains general information about the graph\n   * ```\n   */\n  get(): Promise<GraphInfo> {\n    return this._db.request(\n      { path: `/_api/gharial/${encodeURIComponent(this._name)}` },\n      (res) => res.body.graph\n    );\n  }\n\n  /**\n   * Creates a graph with the given `edgeDefinitions` and `options` for this\n   * graph's name.\n   *\n   * @param edgeDefinitions - Definitions for the relations of the graph.\n   * @param options - Options for creating the graph.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const graph = db.graph(\"some-graph\");\n   * const info = await graph.create([\n   *   {\n   *     collection: \"edges\",\n   *     from: [\"start-vertices\"],\n   *     to: [\"end-vertices\"],\n   *   },\n   * ]);\n   * // graph now exists\n   * ```\n   */\n  create(\n    edgeDefinitions: EdgeDefinitionOptions[],\n    options: CreateGraphOptions = {}\n  ): Promise<GraphInfo> {\n    const { orphanCollections, satellites, waitForSync, isSmart, ...opts } =\n      options;\n    return this._db.request(\n      {\n        method: \"POST\",\n        path: \"/_api/gharial\",\n        body: {\n          orphanCollections:\n            orphanCollections &&\n            (Array.isArray(orphanCollections)\n              ? orphanCollections.map(collectionToString)\n              : [collectionToString(orphanCollections)]),\n          edgeDefinitions: edgeDefinitions.map(coerceEdgeDefinition),\n          isSmart,\n          name: this._name,\n          options: { ...opts, satellites: satellites?.map(collectionToString) },\n        },\n        qs: { waitForSync },\n      },\n      (res) => res.body.graph\n    );\n  }\n\n  /**\n   * Deletes the graph from the database.\n   *\n   * @param dropCollections - If set to `true`, the collections associated with\n   * the graph will also be deleted.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const graph = db.graph(\"some-graph\");\n   * await graph.drop();\n   * // the graph \"some-graph\" no longer exists\n   * ```\n   */\n  drop(dropCollections: boolean = false): Promise<boolean> {\n    return this._db.request(\n      {\n        method: \"DELETE\",\n        path: `/_api/gharial/${encodeURIComponent(this._name)}`,\n        qs: { dropCollections },\n      },\n      (res) => res.body.removed\n    );\n  }\n\n  /**\n   * Returns a {@link graph.GraphVertexCollection} instance for the given collection\n   * name representing the collection in this graph.\n   *\n   * @param T - Type to use for document data. Defaults to `any`.\n   * @param collection - Name of the vertex collection.\n   */\n  vertexCollection<T extends Record<string, any> = any>(\n    collection: string | ArangoCollection\n  ): GraphVertexCollection<T> {\n    return new GraphVertexCollection<T>(\n      this._db,\n      collectionToString(collection),\n      this\n    );\n  }\n\n  /**\n   * Fetches all vertex collections of this graph from the database and returns\n   * an array of their names.\n   *\n   * See also {@link graph.Graph#vertexCollections}.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const graph = db.graph(\"some-graph\");\n   * const info = await graph.create([\n   *   {\n   *     collection: \"edges\",\n   *     from: [\"start-vertices\"],\n   *     to: [\"end-vertices\"],\n   *   },\n   * ]);\n   * const vertexCollectionNames = await graph.listVertexCollections();\n   * // [\"start-vertices\", \"end-vertices\"]\n   * ```\n   */\n  listVertexCollections(): Promise<string[]> {\n    return this._db.request(\n      { path: `/_api/gharial/${encodeURIComponent(this._name)}/vertex` },\n      (res) => res.body.collections\n    );\n  }\n\n  /**\n   * Fetches all vertex collections of this graph from the database and returns\n   * an array of {@link graph.GraphVertexCollection} instances.\n   *\n   * See also {@link graph.Graph#listVertexCollections}.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const graph = db.graph(\"some-graph\");\n   * const info = await graph.create([\n   *   {\n   *     collection: \"edges\",\n   *     from: [\"start-vertices\"],\n   *     to: [\"end-vertices\"],\n   *   },\n   * ]);\n   * const vertexCollections = await graph.vertexCollections();\n   * for (const vertexCollection of vertexCollections) {\n   *   console.log(vertexCollection.name);\n   *   // \"start-vertices\"\n   *   // \"end-vertices\"\n   * }\n   * ```\n   */\n  async vertexCollections(): Promise<GraphVertexCollection[]> {\n    const names = await this.listVertexCollections();\n    return names.map((name) => new GraphVertexCollection(this._db, name, this));\n  }\n\n  /**\n   * Adds the given collection to this graph as a vertex collection.\n   *\n   * @param collection - Collection to add to the graph.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const graph = db.graph(\"some-graph\");\n   * await graph.addVertexCollection(\"more-vertices\");\n   * // The collection \"more-vertices\" has been added to the graph\n   * const extra = db.collection(\"extra-vertices\");\n   * await graph.addVertexCollection(extra);\n   * // The collection \"extra-vertices\" has been added to the graph\n   * ```\n   */\n  addVertexCollection(\n    collection: string | ArangoCollection,\n    options: AddVertexCollectionOptions = {}\n  ): Promise<GraphInfo> {\n    const { satellites, ...opts } = options;\n    return this._db.request(\n      {\n        method: \"POST\",\n        path: `/_api/gharial/${encodeURIComponent(this._name)}/vertex`,\n        body: {\n          collection: collectionToString(collection),\n          options: { ...opts, satellites: satellites?.map(collectionToString) },\n        },\n      },\n      (res) => res.body.graph\n    );\n  }\n\n  /**\n   * Removes the given collection from this graph as a vertex collection.\n   *\n   * @param collection - Collection to remove from the graph.\n   * @param dropCollection - If set to `true`, the collection will also be\n   * deleted from the database.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const graph = db.graph(\"some-graph\");\n   * const info = await graph.create([\n   *   {\n   *     collection: \"edges\",\n   *     from: [\"start-vertices\"],\n   *     to: [\"end-vertices\"],\n   *   },\n   * ]);\n   * await graph.removeVertexCollection(\"start-vertices\");\n   * // The collection \"start-vertices\" is no longer part of the graph.\n   * ```\n   */\n  removeVertexCollection(\n    collection: string | ArangoCollection,\n    dropCollection: boolean = false\n  ): Promise<GraphInfo> {\n    return this._db.request(\n      {\n        method: \"DELETE\",\n        path: `/_api/gharial/${encodeURIComponent(\n          this._name\n        )}/vertex/${encodeURIComponent(collectionToString(collection))}`,\n        qs: {\n          dropCollection,\n        },\n      },\n      (res) => res.body.graph\n    );\n  }\n\n  /**\n   * Returns a {@link graph.GraphEdgeCollection} instance for the given collection\n   * name representing the collection in this graph.\n   *\n   * @param T - Type to use for document data. Defaults to `any`.\n   * @param collection - Name of the edge collection.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const graph = db.graph(\"some-graph\");\n   * const info = await graph.create([\n   *   {\n   *     collection: \"edges\",\n   *     from: [\"start-vertices\"],\n   *     to: [\"end-vertices\"],\n   *   },\n   * ]);\n   * const graphEdgeCollection = graph.edgeCollection(\"edges\");\n   * // Access the underlying EdgeCollection API:\n   * const edgeCollection = graphEdgeCollection.collection;\n   * ```\n   */\n  edgeCollection<T extends Record<string, any> = any>(\n    collection: string | ArangoCollection\n  ): GraphEdgeCollection<T> {\n    return new GraphEdgeCollection<T>(\n      this._db,\n      collectionToString(collection),\n      this\n    );\n  }\n\n  /**\n   * Fetches all edge collections of this graph from the database and returns\n   * an array of their names.\n   *\n   * See also {@link graph.Graph#edgeCollections}.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const graph = db.graph(\"some-graph\");\n   * const info = await graph.create([\n   *   {\n   *     collection: \"edges\",\n   *     from: [\"start-vertices\"],\n   *     to: [\"end-vertices\"],\n   *   },\n   * ]);\n   * const edgeCollectionNames = await graph.listEdgeCollections();\n   * // [\"edges\"]\n   * ```\n   */\n  listEdgeCollections(): Promise<string[]> {\n    return this._db.request(\n      { path: `/_api/gharial/${encodeURIComponent(this._name)}/edge` },\n      (res) => res.body.collections\n    );\n  }\n\n  /**\n   * Fetches all edge collections of this graph from the database and returns\n   * an array of {@link graph.GraphEdgeCollection} instances.\n   *\n   * See also {@link graph.Graph#listEdgeCollections}.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const graph = db.graph(\"some-graph\");\n   * const info = await graph.create([\n   *   {\n   *     collection: \"edges\",\n   *     from: [\"start-vertices\"],\n   *     to: [\"end-vertices\"],\n   *   },\n   * ]);\n   * const graphEdgeCollections = await graph.edgeCollections();\n   * for (const collection of graphEdgeCollection) {\n   *   console.log(collection.name);\n   *   // \"edges\"\n   * }\n   * ```\n   */\n  async edgeCollections(): Promise<GraphEdgeCollection[]> {\n    const names = await this.listEdgeCollections();\n    return names.map((name) => new GraphEdgeCollection(this._db, name, this));\n  }\n\n  /**\n   * Adds an edge definition to this graph.\n   *\n   * @param edgeDefinition - Definition of a relation in this graph.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const graph = db.graph(\"some-graph\");\n   * await graph.addEdgeDefinition({\n   *   collection: \"edges\",\n   *   from: [\"start-vertices\"],\n   *   to: [\"end-vertices\"],\n   * });\n   * // The edge definition has been added to the graph\n   * ```\n   */\n  addEdgeDefinition(\n    edgeDefinition: EdgeDefinitionOptions,\n    options: AddEdgeDefinitionOptions = {}\n  ): Promise<GraphInfo> {\n    const { satellites, ...opts } = options;\n    return this._db.request(\n      {\n        method: \"POST\",\n        path: `/_api/gharial/${encodeURIComponent(this._name)}/edge`,\n        body: {\n          ...coerceEdgeDefinition(edgeDefinition),\n          options: { ...opts, satellites: satellites?.map(collectionToString) },\n        },\n      },\n      (res) => res.body.graph\n    );\n  }\n\n  /**\n   * Replaces an edge definition in this graph. The existing edge definition\n   * for the given edge collection will be overwritten.\n   *\n   * @param edgeDefinition - Definition of a relation in this graph.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const graph = db.graph(\"some-graph\");\n   * const info = await graph.create([\n   *   {\n   *     collection: \"edges\",\n   *     from: [\"start-vertices\"],\n   *     to: [\"end-vertices\"],\n   *   },\n   * ]);\n   * await graph.replaceEdgeDefinition({\n   *   collection: \"edges\",\n   *   from: [\"start-vertices\"],\n   *   to: [\"other-vertices\"],\n   * });\n   * // The edge definition for \"edges\" has been replaced\n   * ```\n   */\n  replaceEdgeDefinition(\n    edgeDefinition: EdgeDefinitionOptions,\n    options?: ReplaceEdgeDefinitionOptions\n  ): Promise<GraphInfo>;\n  /**\n   * Replaces an edge definition in this graph. The existing edge definition\n   * for the given edge collection will be overwritten.\n   *\n   * @param collection - Edge collection for which to replace the definition.\n   * @param edgeDefinition - Definition of a relation in this graph.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const graph = db.graph(\"some-graph\");\n   * const info = await graph.create([\n   *   {\n   *     collection: \"edges\",\n   *     from: [\"start-vertices\"],\n   *     to: [\"end-vertices\"],\n   *   },\n   * ]);\n   * await graph.replaceEdgeDefinition(\"edges\", {\n   *   collection: \"edges\",\n   *   from: [\"start-vertices\"],\n   *   to: [\"other-vertices\"],\n   * });\n   * // The edge definition for \"edges\" has been replaced\n   * ```\n   */\n  replaceEdgeDefinition(\n    collection: string | ArangoCollection,\n    edgeDefinition: EdgeDefinitionOptions,\n    options?: ReplaceEdgeDefinitionOptions\n  ): Promise<GraphInfo>;\n  replaceEdgeDefinition(\n    collectionOrEdgeDefinitionOptions:\n      | string\n      | ArangoCollection\n      | EdgeDefinitionOptions,\n    edgeDefinitionOrOptions?:\n      | EdgeDefinitionOptions\n      | ReplaceEdgeDefinitionOptions,\n    options: ReplaceEdgeDefinitionOptions = {}\n  ) {\n    let collection = collectionOrEdgeDefinitionOptions as\n      | string\n      | ArangoCollection;\n    let edgeDefinition = edgeDefinitionOrOptions as EdgeDefinitionOptions;\n    if (\n      edgeDefinitionOrOptions &&\n      !edgeDefinitionOrOptions.hasOwnProperty(\"collection\")\n    ) {\n      options = edgeDefinitionOrOptions as ReplaceEdgeDefinitionOptions;\n      edgeDefinitionOrOptions = undefined;\n    }\n    if (!edgeDefinitionOrOptions) {\n      edgeDefinition =\n        collectionOrEdgeDefinitionOptions as EdgeDefinitionOptions;\n      collection = edgeDefinition.collection;\n    }\n    const { satellites, ...opts } = options;\n    return this._db.request(\n      {\n        method: \"PUT\",\n        path: `/_api/gharial/${encodeURIComponent(\n          this._name\n        )}/edge/${encodeURIComponent(collectionToString(collection))}`,\n        body: {\n          ...coerceEdgeDefinition(edgeDefinition),\n          options: { ...opts, satellites: satellites?.map(collectionToString) },\n        },\n      },\n      (res) => res.body.graph\n    );\n  }\n\n  /**\n   * Removes the edge definition for the given edge collection from this graph.\n   *\n   * @param collection - Edge collection for which to remove the definition.\n   * @param dropCollection - If set to `true`, the collection will also be\n   * deleted from the database.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const graph = db.graph(\"some-graph\");\n   * const info = await graph.create([\n   *   {\n   *     collection: \"edges\",\n   *     from: [\"start-vertices\"],\n   *     to: [\"end-vertices\"],\n   *   },\n   * ]);\n   * await graph.removeEdgeDefinition(\"edges\");\n   * // The edge definition for \"edges\" has been replaced\n   * ```\n   */\n  removeEdgeDefinition(\n    collection: string | ArangoCollection,\n    dropCollection: boolean = false\n  ): Promise<GraphInfo> {\n    return this._db.request(\n      {\n        method: \"DELETE\",\n        path: `/_api/gharial/${encodeURIComponent(\n          this._name\n        )}/edge/${encodeURIComponent(collectionToString(collection))}`,\n        qs: {\n          dropCollection,\n        },\n      },\n      (res) => res.body.graph\n    );\n  }\n\n  /**\n   * Performs a traversal starting from the given `startVertex` and following\n   * edges contained in this graph.\n   *\n   * See also {@link collection.EdgeCollection#traversal}.\n   *\n   * @param startVertex - Document `_id` of a vertex in this graph.\n   * @param options - Options for performing the traversal.\n   *\n   * @deprecated Simple Queries have been deprecated in ArangoDB 3.4 and are\n   * no longer supported in ArangoDB 3.12. They can be replaced with AQL queries.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const graph = db.graph(\"my-graph\");\n   * const collection = graph.edgeCollection(\"edges\").collection;\n   * await collection.import([\n   *   [\"_key\", \"_from\", \"_to\"],\n   *   [\"x\", \"vertices/a\", \"vertices/b\"],\n   *   [\"y\", \"vertices/b\", \"vertices/c\"],\n   *   [\"z\", \"vertices/c\", \"vertices/d\"],\n   * ]);\n   * const startVertex = \"vertices/a\";\n   * const cursor = await db.query(aql`\n   *   FOR vertex IN OUTBOUND ${startVertex} GRAPH ${graph}\n   *   RETURN vertex._key\n   * `);\n   * const result = await cursor.all();\n   * console.log(result); // [\"a\", \"b\", \"c\", \"d\"]\n   * ```\n   */\n  traversal(startVertex: string, options?: TraversalOptions): Promise<any> {\n    return this._db.request(\n      {\n        method: \"POST\",\n        path: `/_api/traversal`,\n        body: {\n          ...options,\n          startVertex,\n          graphName: this._name,\n        },\n      },\n      (res) => res.body.result\n    );\n  }\n}\n"]}