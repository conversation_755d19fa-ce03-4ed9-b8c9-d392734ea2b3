{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;AAeA,yCAAsC;AAEtC,MAAM,CAAC,OAAO,GAAG,OAAO,GAAG,QAAQ,CAAC;AAiCpC,SAAgB,QAAQ,CAAC,MAAmC,EAAE,IAAa;IACzE,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;QACxD,MAAM,GAAG,GAAG,MAAM,CAAC;QACnB,OAAO,IAAI,mBAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;IACjC,CAAC;IACD,OAAO,IAAI,mBAAQ,CAAC,MAAM,CAAC,CAAC;AAC9B,CAAC;AAND,4BAMC;AACD,kBAAe,QAAQ,CAAC;AACxB,6BAA4B;AAAnB,0FAAA,GAAG,OAAA;AACZ,uCAAsC;AAA7B,oGAAA,QAAQ,OAAA", "sourcesContent": ["/**\n * ```js\n * import arangojs, { aql, Database } from \"arangojs\";\n * ```\n *\n * The \"index\" module is the default entry point when importing the arangojs\n * module or using the web build in the browser.\n *\n * If you are just getting started, you probably want to use the\n * {@link arangojs} function, which is also the default export of this module,\n * or the {@link database.Database} class for which it is a wrapper.\n *\n * @packageDocumentation\n */\nimport { Config } from \"./connection\";\nimport { Database } from \"./database\";\n\nmodule.exports = exports = arangojs;\n/**\n * Creates a new `Database` instance with its own connection pool.\n *\n * This is a wrapper function for the {@link database.Database:constructor}.\n *\n * @param config - An object with configuration options.\n *\n * @example\n * ```js\n * const db = arangojs({\n *   url: \"http://127.0.0.1:8529\",\n *   databaseName: \"myDatabase\",\n *   auth: { username: \"admin\", password: \"hunter2\" },\n * });\n * ```\n */\nexport function arangojs(config?: Config): Database;\n/**\n * Creates a new `Database` instance with its own connection pool.\n *\n * This is a wrapper function for the {@link database.Database:constructor}.\n *\n * @param url - Base URL of the ArangoDB server or list of server URLs.\n * Equivalent to the `url` option in {@link connection.Config}.\n *\n * @example\n * ```js\n * const db = arangojs(\"http://127.0.0.1:8529\", \"myDatabase\");\n * db.useBasicAuth(\"admin\", \"hunter2\");\n * ```\n */\nexport function arangojs(url: string | string[], name?: string): Database;\nexport function arangojs(config?: string | string[] | Config, name?: string) {\n  if (typeof config === \"string\" || Array.isArray(config)) {\n    const url = config;\n    return new Database(url, name);\n  }\n  return new Database(config);\n}\nexport default arangojs;\nexport { aql } from \"./aql\";\nexport { Database } from \"./database\";\n"]}