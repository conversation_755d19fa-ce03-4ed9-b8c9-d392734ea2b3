"use strict";
/**
 * ```ts
 * import type {
 *   FulltextIndex,
 *   GeoIndex,
 *   MdiIndex,
 *   PersistentIndex,
 *   PrimaryIndex,
 *   TtlIndex,
 * } from "arangojs/indexes";
 * ```
 *
 * The "indexes" module provides index-related types for TypeScript.
 *
 * @packageDocumentation
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports._indexHandle = void 0;
/**
 * @internal
 */
function _indexHandle(selector, collectionName) {
    if (typeof selector !== "string") {
        if (selector.id) {
            return _indexHandle(selector.id, collectionName);
        }
        throw new Error("Index handle must be a string or an object with an id attribute");
    }
    if (selector.includes("/")) {
        const [head, ...tail] = selector.split("/");
        const normalizedHead = head.normalize("NFC");
        if (normalizedHead !== collectionName) {
            throw new Error(`Index ID "${selector}" does not match collection name "${collectionName}"`);
        }
        selector = tail.join("/").normalize("NFC");
        return [normalizedHead, selector].join("/");
    }
    return `${collectionName}/${String(selector).normalize("NFC")}`;
}
exports._indexHandle = _indexHandle;
//# sourceMappingURL=indexes.js.map