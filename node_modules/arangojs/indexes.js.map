{"version": 3, "file": "indexes.js", "sourceRoot": "", "sources": ["../src/indexes.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAssBH;;GAEG;AACH,SAAgB,YAAY,CAC1B,QAAuB,EACvB,cAAsB;IAEtB,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;QACjC,IAAI,QAAQ,CAAC,EAAE,EAAE,CAAC;YAChB,OAAO,YAAY,CAAC,QAAQ,CAAC,EAAE,EAAE,cAAc,CAAC,CAAC;QACnD,CAAC;QACD,MAAM,IAAI,KAAK,CACb,iEAAiE,CAClE,CAAC;IACJ,CAAC;IACD,IAAI,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;QAC3B,MAAM,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC5C,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAC7C,IAAI,cAAc,KAAK,cAAc,EAAE,CAAC;YACtC,MAAM,IAAI,KAAK,CACb,aAAa,QAAQ,qCAAqC,cAAc,GAAG,CAC5E,CAAC;QACJ,CAAC;QACD,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAC3C,OAAO,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC9C,CAAC;IACD,OAAO,GAAG,cAAc,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;AAClE,CAAC;AAxBD,oCAwBC", "sourcesContent": ["/**\n * ```ts\n * import type {\n *   FulltextIndex,\n *   GeoIndex,\n *   MdiIndex,\n *   PersistentIndex,\n *   PrimaryIndex,\n *   TtlIndex,\n * } from \"arangojs/indexes\";\n * ```\n *\n * The \"indexes\" module provides index-related types for TypeScript.\n *\n * @packageDocumentation\n */\n\nimport { AnalyzerFeature } from \"./analyzer\";\nimport { Compression, Direction, TierConsolidationPolicy } from \"./view\";\n\n/**\n * Options for creating a persistent index.\n */\nexport type EnsurePersistentIndexOptions = {\n  /**\n   * Type of this index.\n   */\n  type: \"persistent\";\n  /**\n   * An array of attribute paths.\n   */\n  fields: string[];\n  /**\n   * A unique name for this index.\n   */\n  name?: string;\n  /**\n   * If set to `true`, a unique index will be created.\n   *\n   * Default: `false`\n   */\n  unique?: boolean;\n  /**\n   * If set to `true`, the index will omit documents that do not contain at\n   * least one of the attribute paths in `fields` and these documents will be\n   * ignored for uniqueness checks.\n   *\n   * Default: `false`\n   */\n  sparse?: boolean;\n  /**\n   * If set to `false`, inserting duplicate index values from the same\n   * document will lead to a unique constraint error if this is a unique index.\n   *\n   * Default: `true`\n   */\n  deduplicate?: boolean;\n  /**\n   * If set to `false`, index selectivity estimates will be disabled for this\n   * index.\n   *\n   * Default: `true`\n   */\n  estimates?: boolean;\n  /**\n   * If set to `true`, the index will be created in the background to reduce\n   * the write-lock duration for the collection during index creation.\n   *\n   * Default: `false`\n   */\n  inBackground?: boolean;\n  /**\n   * If set to `true`, an in-memory hash cache will be put in front of the\n   * persistent index.\n   *\n   * Default: `false`\n   */\n  cacheEnabled?: boolean;\n  /**\n   * An array of attribute paths that will be stored in the index but can not\n   * be used for index lookups or sorting but can avoid full document lookups.\n   */\n  storedValues?: string[];\n};\n\n/**\n * Options for creating a geo index.\n */\nexport type EnsureGeoIndexOptions =\n  | {\n      type: \"geo\";\n      /**\n       * If set to `true`, `fields` must be an array containing a single attribute\n       * path and the attribute value must be an array with two values, the first\n       * of which will be interpreted as the longitude and the second of which will\n       * be interpreted as the latitude of the document.\n       *\n       * Default: `false`\n       */\n      geoJson?: false;\n      /**\n       * If set to `true`, the index will use pre-3.10 rules for parsing\n       * GeoJSON polygons. This option is always implicitly `true` when using\n       * ArangoDB 3.9 or lower.\n       */\n      legacyPolygons?: boolean;\n      /**\n       * Attribute paths for the document's latitude and longitude values.\n       */\n      fields: [string, string];\n      /**\n       * A unique name for this index.\n       */\n      name?: string;\n      /**\n       * If set to `true`, the index will be created in the background to reduce\n       * the write-lock duration for the collection during index creation.\n       *\n       * Default: `false`\n       */\n      inBackground?: boolean;\n    }\n  | {\n      type: \"geo\";\n      /**\n       * If set to `true`, `fields` must be an array containing a single attribute\n       * path and the attribute value must be an array with two values, the first\n       * of which will be interpreted as the longitude and the second of which will\n       * be interpreted as the latitude of the document.\n       *\n       * Default: `false`\n       */\n      geoJson?: boolean;\n      /**\n       * If set to `true`, the index will use pre-3.10 rules for parsing\n       * GeoJSON polygons. This option is always implicitly `true` when using\n       * ArangoDB 3.9 or lower.\n       */\n      legacyPolygons?: boolean;\n      /**\n       * An array containing the attribute path for an array containing two values,\n       * the first of which will be interpreted as the latitude, the second as the\n       * longitude. If `geoJson` is set to `true`, the order is reversed to match\n       * the GeoJSON format.\n       */\n      fields: [string];\n      /**\n       * A unique name for this index.\n       */\n      name?: string;\n      /**\n       * If set to `true`, the index will be created in the background to reduce\n       * the write-lock duration for the collection during index creation.\n       *\n       * Default: `false`\n       */\n      inBackground?: boolean;\n    };\n\n/**\n * Options for creating a fulltext index.\n *\n * @deprecated Fulltext indexes have been deprecated in ArangoDB 3.10 and\n * should be replaced with ArangoSearch.\n */\nexport type EnsureFulltextIndexOptions = {\n  /**\n   * Type of this index.\n   */\n  type: \"fulltext\";\n  /**\n   * An array containing exactly one attribute path.\n   */\n  fields: [string];\n  /**\n   * A unique name for this index.\n   */\n  name?: string;\n  /**\n   * Minimum character length of words to index.\n   */\n  minLength?: number;\n  /**\n   * If set to `true`, the index will be created in the background to reduce\n   * the write-lock duration for the collection during index creation.\n   *\n   * Default: `false`\n   */\n  inBackground?: boolean;\n};\n\n/**\n * Options for creating a TTL index.\n */\nexport type EnsureTtlIndexOptions = {\n  /**\n   * Type of this index.\n   */\n  type: \"ttl\";\n  /**\n   * An array containing exactly one attribute path.\n   */\n  fields: [string];\n  /**\n   * A unique name for this index.\n   */\n  name?: string;\n  /**\n   * Duration in seconds after the attribute value at which the document will\n   * be considered as expired.\n   */\n  expireAfter: number;\n  /**\n   * If set to `true`, the index will be created in the background to reduce\n   * the write-lock duration for the collection during index creation.\n   *\n   * Default: `false`\n   */\n  inBackground?: boolean;\n};\n\n/**\n * Options for creating a MDI index.\n */\nexport type EnsureMdiIndexOptions = {\n  /**\n   * Type of this index.\n   */\n  type: \"mdi\";\n  /**\n   * An array containing attribute paths for the dimensions.\n   */\n  fields: string[];\n  /**\n   * Data type of the dimension attributes.\n   */\n  fieldValueTypes: \"double\";\n  /**\n   * A unique name for this index.\n   */\n  name?: string;\n  /**\n   * If set to `true`, a unique index will be created.\n   *\n   * Default: `false`\n   */\n  unique?: boolean;\n  /**\n   * If set to `true`, the index will be created in the background to reduce\n   * the write-lock duration for the collection during index creation.\n   *\n   * Default: `false`\n   */\n  inBackground?: boolean;\n};\n\n/**\n * (Enterprise Edition only.) Options for a nested field in an inverted index.\n */\nexport type InvertedIndexNestedFieldOptions = {\n  /**\n   * An attribute path.\n   */\n  name: string;\n  /**\n   * Name of the Analyzer to apply to the values of this field.\n   *\n   * Defaults to the `analyzer` specified on the parent options or on the index\n   * itself.\n   */\n  analyzer?: string;\n  /**\n   * List of Analyzer features to enable for this field's Analyzer.\n   *\n   * Defaults to the features of the Analyzer.\n   */\n  features?: AnalyzerFeature[];\n  /**\n   * If set to `true` array values will be indexed using the same behavior as\n   * ArangoSearch Views. This option only applies when using the index in a\n   * SearchAlias View.\n   *\n   * Defaults to the value of `searchField` specified on the index itself.\n   */\n  searchField?: boolean;\n  /**\n   * Sub-objects to index to allow querying for co-occurring values.\n   */\n  nested?: (string | InvertedIndexNestedFieldOptions)[];\n};\n\n/**\n * Options for an attribute path in an inverted index.\n */\nexport type InvertedIndexFieldOptions = {\n  /**\n   * An attribute path.\n   */\n  name: string;\n  /**\n   * Name of the Analyzer to apply to the values of this field.\n   *\n   * Defaults to the `analyzer` specified on the index itself.\n   */\n  analyzer?: string;\n  /**\n   * List of Analyzer features to enable for this field's Analyzer.\n   *\n   * Defaults to the features of the Analyzer.\n   */\n  features?: AnalyzerFeature[];\n  /**\n   * If set to `true`, all document attributes are indexed, excluding any\n   * sub-attributes configured in the `fields` array. The `analyzer` and\n   * `features` properties apply to the sub-attributes. This option only\n   * applies when using the index in a SearchAlias View.\n   *\n   * Defaults to the value of `includeAllFields` specified on the index itself.\n   */\n  includeAllFields?: boolean;\n  /**\n   * If set to `true` array values will be indexed using the same behavior as\n   * ArangoSearch Views. This option only applies when using the index in a\n   * SearchAlias View.\n   *\n   * Defaults to the value of `searchField` specified on the index itself.\n   */\n  searchField?: boolean;\n  /**\n   * If set to `true`, the position of values in array values are tracked and\n   * need to be specified in queries. Otherwise all values in an array are\n   * treated as equivalent. This option only applies when using the index in a\n   * SearchAlias View.\n   *\n   * Defaults to the value of `trackListPositions` specified on the index\n   * itself.\n   */\n  trackListPositions?: boolean;\n  /**\n   * (Enterprise Edition only.) Sub-objects to index to allow querying for\n   * co-occurring values.\n   */\n  nested?: (string | InvertedIndexNestedFieldOptions)[];\n  /**\n   * (Enterprise Edition only.) If set to `true`, then field normalization\n   * values will always be cached in memory.\n   *\n   * Defaults to the value of `cache` specified on the index itself.\n   */\n  cache?: boolean;\n};\n\n/**\n * Options for defining a stored value on an inverted index.\n */\nexport type InvertedIndexStoredValueOptions = {\n  /**\n   * The attribute paths to store.\n   */\n  fields: string[];\n  /**\n   * How the attribute values should be compressed.\n   *\n   * Default: `\"lz4\"`\n   */\n  compression?: Compression;\n  /**\n   * (Enterprise Edition only.) If set to `true`, then stored values will\n   * always be cached in memory.\n   *\n   * Default: `false`\n   */\n  cache?: boolean;\n};\n\n/**\n * Options for defining a primary sort field on an inverted index.\n */\nexport type InvertedIndexPrimarySortFieldOptions = {\n  /**\n   * The attribute path to sort by.\n   */\n  field: string;\n  /**\n   * The sorting direction.\n   */\n  direction: Direction;\n};\n\n/**\n * Options for creating an inverted index.\n */\nexport type EnsureInvertedIndexOptions = {\n  /**\n   * Type of this index.\n   */\n  type: \"inverted\";\n  /**\n   * An array of attribute paths or objects specifying options for the fields.\n   */\n  fields: (string | InvertedIndexFieldOptions)[];\n  /**\n   * A unique name for this index.\n   */\n  name?: string;\n  /**\n   * If set to `true` array values will by default be indexed using the same\n   * behavior as ArangoSearch Views. This option only applies when using the\n   * index in a SearchAlias View.\n   *\n   * Default: `false`\n   */\n  searchField?: boolean;\n  /**\n   * An array of attribute paths that will be stored in the index but can not\n   * be used for index lookups or sorting but can avoid full document lookups.\n   */\n  storedValues?: InvertedIndexStoredValueOptions[];\n  /**\n   * Primary sort order to optimize AQL queries using a matching sort order.\n   */\n  primarySort?: {\n    /**\n     * An array of fields to sort the index by.\n     */\n    fields: InvertedIndexPrimarySortFieldOptions[];\n    /**\n     * How the primary sort data should be compressed.\n     *\n     * Default: `\"lz4\"`\n     */\n    compression?: Compression;\n    /**\n     * (Enterprise Edition only.) If set to `true`, then primary sort columns\n     * will always be cached in memory.\n     *\n     * Default: `false`\n     */\n    cache?: boolean;\n  };\n  /**\n   * (Enterprise Edition only.) If set to `true`, then the primary key column\n   * will always be cached in memory.\n   *\n   * Default: `false`\n   */\n  primaryKeyCache?: boolean;\n  /**\n   * Name of the default Analyzer to apply to the values of indexed fields.\n   *\n   * Default: `\"identity\"`\n   */\n  analyzer?: string;\n  /**\n   * List of Analyzer features to enable for the default Analyzer.\n   *\n   * Defaults to the Analyzer's features.\n   */\n  features?: AnalyzerFeature[];\n  /**\n   * If set to `true`, all document attributes are indexed, excluding any\n   * sub-attributes configured in the `fields` array. The `analyzer` and\n   * `features` properties apply to the sub-attributes. This option only\n   * applies when using the index in a SearchAlias View.\n   *\n   * Default: `false`\n   */\n  includeAllFields?: boolean;\n  /**\n   * If set to `true`, the position of values in array values are tracked and\n   * need to be specified in queries. Otherwise all values in an array are\n   * treated as equivalent. This option only applies when using the index in a\n   * SearchAlias View.\n   *\n   * Default: `false`\n   */\n  trackListPositions?: boolean;\n  /**\n   * The number of threads to use for indexing the fields.\n   *\n   * Default: `2`\n   */\n  parallelism?: number;\n  /**\n   * Wait at least this many commits between removing unused files in the\n   * ArangoSearch data directory.\n   *\n   * Default: `2`\n   */\n  cleanupIntervalStep?: number;\n  /**\n   * Wait at least this many milliseconds between committing View data store\n   * changes and making documents visible to queries.\n   *\n   * Default: `1000`\n   */\n  commitIntervalMsec?: number;\n  /**\n   * Wait at least this many milliseconds between applying\n   * `consolidationPolicy` to consolidate View data store and possibly release\n   * space on the filesystem.\n   *\n   * Default: `1000`\n   */\n  consolidationIntervalMsec?: number;\n  /**\n   * The consolidation policy to apply for selecting which segments should be\n   * merged.\n   *\n   * Default: `{ type: \"tier\" }`\n   */\n  consolidationPolicy?: TierConsolidationPolicy;\n  /**\n   * Maximum number of writers (segments) cached in the pool.\n   *\n   * Default: `64`\n   */\n  writeBufferIdle?: number;\n  /**\n   * Maximum number of concurrent active writers (segments) that perform a\n   * transaction.\n   *\n   * Default: `0` (disabled)\n   */\n  writeBufferActive?: number;\n  /**\n   * Maximum memory byte size per writer (segment) before a writer (segment)\n   * flush is triggered.\n   *\n   * Default: `33554432` (32 MiB)\n   */\n  writeBufferSizeMax?: number;\n  /**\n   * If set to `true`, the index will be created in the background to reduce\n   * the write-lock duration for the collection during index creation.\n   *\n   * Default: `false`\n   */\n  inBackground?: boolean;\n  /**\n   * (Enterprise Edition only.) If set to `true`, then field normalization\n   * values will always be cached in memory.\n   *\n   * Default: `false`\n   */\n  cache?: boolean;\n  /**\n   * An array of strings defining sort expressions to optimize.\n   */\n  optimizeTopK?: string[];\n};\n\n/**\n * Shared attributes of all index types.\n */\nexport type GenericIndex = {\n  /**\n   * A unique name for this index.\n   */\n  name: string;\n  /**\n   * A unique identifier for this index.\n   */\n  id: string;\n  /**\n   * Whether documents not containing at least one of the attribute paths\n   * are omitted by this index.\n   */\n  sparse: boolean;\n  /**\n   * Whether this index enforces uniqueness for values of its attribute paths.\n   */\n  unique: boolean;\n};\n\n/**\n * An object representing a persistent index.\n */\nexport type PersistentIndex = GenericIndex & {\n  type: \"persistent\";\n  fields: string[];\n  cacheEnabled: boolean;\n  deduplicate: boolean;\n  estimates: boolean;\n  storedValues?: string[];\n};\n\n/**\n * An object representing a primary index.\n */\nexport type PrimaryIndex = GenericIndex & {\n  type: \"primary\";\n  fields: string[];\n  selectivityEstimate: number;\n};\n\n/**\n * An object representing a fulltext index.\n *\n * @deprecated Fulltext indexes have been deprecated in ArangoDB 3.10 and\n * should be replaced with ArangoSearch.\n */\nexport type FulltextIndex = GenericIndex & {\n  type: \"fulltext\";\n  fields: [string];\n  minLength: number;\n};\n\n/**\n * An object representing a geo index.\n */\nexport type GeoIndex = GenericIndex & {\n  type: \"geo\";\n  fields: [string] | [string, string];\n  geoJson: boolean;\n  legacyPolygons: boolean;\n  bestIndexedLevel: number;\n  worstIndexedLevel: number;\n  maxNumCoverCells: number;\n};\n\n/**\n * An object representing a TTL index.\n */\nexport type TtlIndex = GenericIndex & {\n  type: \"ttl\";\n  fields: [string];\n  expireAfter: number;\n  selectivityEstimate: number;\n};\n\n/**\n * An object representing a MDI index.\n */\nexport type MdiIndex = GenericIndex & {\n  type: \"mdi\";\n  fields: string[];\n  fieldValueTypes: \"double\";\n};\n\n/**\n * (Enterprise Edition only.) An object representing a nested field in an\n * inverted index.\n */\nexport type InvertedIndexNestedField = {\n  name: string;\n  analyzer?: string;\n  features?: AnalyzerFeature[];\n  searchField?: boolean;\n  nested?: InvertedIndexNestedField[];\n};\n\n/**\n * An object representing an inverted index.\n */\nexport type InvertedIndex = GenericIndex & {\n  type: \"inverted\";\n  fields: {\n    name: string;\n    analyzer?: string;\n    features?: AnalyzerFeature[];\n    includeAllFields?: boolean;\n    searchField?: boolean;\n    trackListPositions?: boolean;\n    nested?: InvertedIndexNestedField[];\n    cache?: boolean;\n  }[];\n  searchField: boolean;\n  cache?: boolean;\n  storedValues: {\n    fields: string[];\n    compression: Compression;\n    cache?: boolean;\n  }[];\n  primarySort: {\n    fields: {\n      field: string;\n      direction: Direction;\n    }[];\n    compression: Compression;\n    cache?: boolean;\n  };\n  primaryKeyCache?: boolean;\n  analyzer: string;\n  features: AnalyzerFeature[];\n  includeAllFields: boolean;\n  trackListPositions: boolean;\n  parallelism: number;\n  cleanupIntervalStep: number;\n  commitIntervalMsec: number;\n  consolidationIntervalMsec: number;\n  consolidationPolicy: Required<TierConsolidationPolicy>;\n  writeBufferIdle: number;\n  writeBufferActive: number;\n  writeBufferSizeMax: number;\n  optimizeTopK: string[];\n};\n\n/**\n * An object representing an index.\n */\nexport type Index =\n  | GeoIndex\n  | FulltextIndex\n  | PersistentIndex\n  | PrimaryIndex\n  | TtlIndex\n  | MdiIndex\n  | InvertedIndex;\n\nexport type ObjectWithId = {\n  [key: string]: any;\n  id: string;\n};\n\nexport type ObjectWithName = {\n  [key: string]: any;\n  name: string;\n};\n\n/**\n * Index name, id or object with a `name` or `id` property.\n */\nexport type IndexSelector = ObjectWithId | ObjectWithName | string;\n\n/**\n * @internal\n */\nexport function _indexHandle(\n  selector: IndexSelector,\n  collectionName: string\n): string {\n  if (typeof selector !== \"string\") {\n    if (selector.id) {\n      return _indexHandle(selector.id, collectionName);\n    }\n    throw new Error(\n      \"Index handle must be a string or an object with an id attribute\"\n    );\n  }\n  if (selector.includes(\"/\")) {\n    const [head, ...tail] = selector.split(\"/\");\n    const normalizedHead = head.normalize(\"NFC\");\n    if (normalizedHead !== collectionName) {\n      throw new Error(\n        `Index ID \"${selector}\" does not match collection name \"${collectionName}\"`\n      );\n    }\n    selector = tail.join(\"/\").normalize(\"NFC\");\n    return [normalizedHead, selector].join(\"/\");\n  }\n  return `${collectionName}/${String(selector).normalize(\"NFC\")}`;\n}\n"]}