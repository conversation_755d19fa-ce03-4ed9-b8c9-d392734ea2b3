import { Database } from "./database";
import { ArangojsResponse } from "./lib/request.node";
/**
 * Represents an async job in a {@link database.Database}.
 */
export declare class Job<T = any> {
    protected _id: string;
    protected _db: Database;
    protected _transformResponse?: (res: ArangojsResponse) => Promise<T>;
    protected _transformError?: (error: any) => Promise<T>;
    protected _loaded: boolean;
    protected _result: T | undefined;
    /**
     * @internal
     */
    constructor(db: Database, id: string, transformResponse?: (res: ArangojsResponse) => Promise<T>, transformError?: (error: any) => Promise<T>);
    /**
     * Whether the job's results have been loaded. If set to `true`, the job's
     * result can be accessed from {@link Job.result}.
     */
    get isLoaded(): boolean;
    /**
     * The job's result if it has been loaded or `undefined` otherwise.
     */
    get result(): T | undefined;
    /**
     * Loads the job's result from the database if it is not already loaded.
     *
     * @example
     * ```js
     * // poll for the job to complete
     * while (!job.isLoaded) {
     *   await timeout(1000);
     *   const result = await job.load();
     *   console.log(result);
     * }
     * // job result is now loaded and can also be accessed from job.result
     * console.log(job.result);
     * ```
     */
    load(): Promise<T | undefined>;
    /**
     * Cancels the job if it is still running. Note that it may take some time to
     * actually cancel the job.
     */
    cancel(): Promise<void>;
    /**
     * Deletes the result if it has not already been retrieved or deleted.
     */
    deleteResult(): Promise<void>;
    /**
     * Fetches the job's completion state.
     *
     * Returns `true` if the job has completed, `false` otherwise.
     *
     * @example
     * ```js
     * // poll for the job to complete
     * while (!(await job.getCompleted())) {
     *   await timeout(1000);
     * }
     * // job result is now available and can be loaded
     * await job.load();
     * console.log(job.result);
     * ```
     */
    getCompleted(): Promise<boolean>;
}
//# sourceMappingURL=job.d.ts.map