{"version": 3, "file": "job.js", "sourceRoot": "", "sources": ["../src/job.ts"], "names": [], "mappings": ";;;AAGA;;GAEG;AACH,MAAa,GAAG;IAQd;;OAEG;IACH,YACE,EAAY,EACZ,EAAU,EACV,iBAAyD,EACzD,cAA2C;QAVnC,YAAO,GAAY,KAAK,CAAC;QAYjC,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;QAC5C,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;IACxC,CAAC;IAED;;;OAGG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACH,KAAK,CAAC,IAAI;QACR,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,IAAI,GAAqB,CAAC;YAC1B,IAAI,CAAC;gBACH,GAAG,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAC1B;oBACE,MAAM,EAAE,KAAK;oBACb,IAAI,EAAE,aAAa,IAAI,CAAC,GAAG,EAAE;iBAC9B,EACD,KAAK,CACN,CAAC;YACJ,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACX,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;oBACzB,OAAO,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;gBACjC,CAAC;gBACD,MAAM,CAAC,CAAC;YACV,CAAC;YACD,IAAI,GAAG,CAAC,UAAU,KAAK,GAAG,EAAE,CAAC;gBAC3B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;gBACpB,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBAC5B,IAAI,CAAC,OAAO,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;gBACpD,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC;gBAC1B,CAAC;YACH,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED;;;OAGG;IACH,MAAM;QACJ,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,aAAa,IAAI,CAAC,GAAG,SAAS;SACrC,EACD,GAAG,EAAE,CAAC,SAAS,CAChB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,YAAY;QACV,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,QAAQ;YAChB,IAAI,EAAE,aAAa,IAAI,CAAC,GAAG,EAAE;SAC9B,EACD,GAAG,EAAE,CAAC,SAAS,CAChB,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACH,YAAY;QACV,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,IAAI,EAAE,aAAa,IAAI,CAAC,GAAG,EAAE;SAC9B,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,UAAU,KAAK,GAAG,CAChC,CAAC;IACJ,CAAC;CACF;AArID,kBAqIC", "sourcesContent": ["import { Database } from \"./database\";\nimport { ArangojsResponse } from \"./lib/request.node\";\n\n/**\n * Represents an async job in a {@link database.Database}.\n */\nexport class Job<T = any> {\n  protected _id: string;\n  protected _db: Database;\n  protected _transformResponse?: (res: ArangojsResponse) => Promise<T>;\n  protected _transformError?: (error: any) => Promise<T>;\n  protected _loaded: boolean = false;\n  protected _result: T | undefined;\n\n  /**\n   * @internal\n   */\n  constructor(\n    db: Database,\n    id: string,\n    transformResponse?: (res: ArangojsResponse) => Promise<T>,\n    transformError?: (error: any) => Promise<T>\n  ) {\n    this._db = db;\n    this._id = id;\n    this._transformResponse = transformResponse;\n    this._transformError = transformError;\n  }\n\n  /**\n   * Whether the job's results have been loaded. If set to `true`, the job's\n   * result can be accessed from {@link Job.result}.\n   */\n  get isLoaded(): boolean {\n    return this._loaded;\n  }\n\n  /**\n   * The job's result if it has been loaded or `undefined` otherwise.\n   */\n  get result(): T | undefined {\n    return this._result;\n  }\n\n  /**\n   * Loads the job's result from the database if it is not already loaded.\n   *\n   * @example\n   * ```js\n   * // poll for the job to complete\n   * while (!job.isLoaded) {\n   *   await timeout(1000);\n   *   const result = await job.load();\n   *   console.log(result);\n   * }\n   * // job result is now loaded and can also be accessed from job.result\n   * console.log(job.result);\n   * ```\n   */\n  async load(): Promise<T | undefined> {\n    if (!this.isLoaded) {\n      let res: ArangojsResponse;\n      try {\n        res = await this._db.request(\n          {\n            method: \"PUT\",\n            path: `/_api/job/${this._id}`,\n          },\n          false\n        );\n      } catch (e) {\n        if (this._transformError) {\n          return this._transformError(e);\n        }\n        throw e;\n      }\n      if (res.statusCode !== 204) {\n        this._loaded = true;\n        if (this._transformResponse) {\n          this._result = await this._transformResponse(res);\n        } else {\n          this._result = res.body;\n        }\n      }\n    }\n    return this._result;\n  }\n\n  /**\n   * Cancels the job if it is still running. Note that it may take some time to\n   * actually cancel the job.\n   */\n  cancel(): Promise<void> {\n    return this._db.request(\n      {\n        method: \"PUT\",\n        path: `/_api/job/${this._id}/cancel`,\n      },\n      () => undefined\n    );\n  }\n\n  /**\n   * Deletes the result if it has not already been retrieved or deleted.\n   */\n  deleteResult(): Promise<void> {\n    return this._db.request(\n      {\n        method: \"DELETE\",\n        path: `/_api/job/${this._id}`,\n      },\n      () => undefined\n    );\n  }\n\n  /**\n   * Fetches the job's completion state.\n   *\n   * Returns `true` if the job has completed, `false` otherwise.\n   *\n   * @example\n   * ```js\n   * // poll for the job to complete\n   * while (!(await job.getCompleted())) {\n   *   await timeout(1000);\n   * }\n   * // job result is now available and can be loaded\n   * await job.load();\n   * console.log(job.result);\n   * ```\n   */\n  getCompleted(): Promise<boolean> {\n    return this._db.request(\n      {\n        path: `/_api/job/${this._id}`,\n      },\n      (res) => res.statusCode !== 204\n    );\n  }\n}\n"]}