{"version": 3, "file": "route.js", "sourceRoot": "", "sources": ["../src/route.ts"], "names": [], "mappings": ";;;AAaA;;GAEG;AACH,MAAa,KAAK;IAKhB;;OAEG;IACH,YAAY,EAAY,EAAE,OAAe,EAAE,EAAE,UAAmB,EAAE;QAChE,IAAI,CAAC,IAAI;YAAE,IAAI,GAAG,EAAE,CAAC;aAChB,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG;YAAE,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;QACnD,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;IAC1B,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,KAAK,CAAC,IAAY,EAAE,OAAiB;QACnC,IAAI,CAAC,IAAI;YAAE,IAAI,GAAG,EAAE,CAAC;aAChB,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG;YAAE,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;QACnD,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,EAAE;YAC5C,GAAG,IAAI,CAAC,QAAQ;YAChB,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;OAmBG;IACH,OAAO,CAAC,OAAwB;QAC9B,MAAM,IAAI,GAAG,EAAE,GAAG,OAAO,EAAE,CAAC;QAC5B,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,GAAG;YAAE,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;aAC/C,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG;YAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;;YACtE,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;QACjC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC;QAC3B,IAAI,CAAC,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QACrD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;QAC9D,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACvC,CAAC;IAsCD,MAAM,CAAC,GAAG,IAAW;QACnB,MAAM,IAAI,GAAG,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;QACpE,MAAM,CAAC,EAAE,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC;QAC3B,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;IAC/D,CAAC;IAsCD,GAAG,CAAC,GAAG,IAAW;QAChB,MAAM,IAAI,GAAG,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;QACpE,MAAM,CAAC,EAAE,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC;QAC3B,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;IAC5D,CAAC;IAsCD,IAAI,CAAC,GAAG,IAAW;QACjB,MAAM,IAAI,GAAG,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;QACpE,MAAM,CAAC,EAAE,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC;QAC3B,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;IAC7D,CAAC;IA+CD,KAAK,CAAC,GAAG,IAAW;QAClB,MAAM,IAAI,GAAG,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;QACpE,MAAM,CAAC,IAAI,EAAE,EAAE,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC;QACjC,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;IACpE,CAAC;IAqDD,IAAI,CAAC,GAAG,IAAW;QACjB,MAAM,IAAI,GAAG,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;QACpE,MAAM,CAAC,IAAI,EAAE,EAAE,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC;QACjC,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;IACnE,CAAC;IA+CD,GAAG,CAAC,GAAG,IAAW;QAChB,MAAM,IAAI,GAAG,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;QACpE,MAAM,CAAC,IAAI,EAAE,EAAE,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC;QACjC,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;IAClE,CAAC;CACF;AAlWD,sBAkWC", "sourcesContent": ["/**\n * ```ts\n * import type { Route } from \"arangojs/route\";\n * ```\n *\n * The \"route\" module provides route related types and interfaces for TypeScript.\n *\n * @packageDocumentation\n */\nimport { Headers, Params, RequestOptions } from \"./connection\";\nimport { Database } from \"./database\";\nimport { ArangojsResponse } from \"./lib/request\";\n\n/**\n * Represents an arbitrary route relative to an ArangoDB database.\n */\nexport class Route {\n  protected _db: Database;\n  protected _path: string;\n  protected _headers: Headers;\n\n  /**\n   * @internal\n   */\n  constructor(db: Database, path: string = \"\", headers: Headers = {}) {\n    if (!path) path = \"\";\n    else if (path.charAt(0) !== \"/\") path = `/${path}`;\n    this._db = db;\n    this._path = path;\n    this._headers = headers;\n  }\n\n  /**\n   * Creates a new route relative to this route that inherits any of its default\n   * HTTP headers.\n   *\n   * @param path - Path relative to this route.\n   * @param headers - Additional headers that will be sent with each request.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const foxx = db.route(\"/my-foxx-service\");\n   * const users = foxx.route(\"/users\");\n   * ```\n   */\n  route(path: string, headers?: Headers) {\n    if (!path) path = \"\";\n    else if (path.charAt(0) !== \"/\") path = `/${path}`;\n    return new Route(this._db, this._path + path, {\n      ...this._headers,\n      ...headers,\n    });\n  }\n\n  /**\n   * Performs an arbitrary HTTP request relative to this route and returns the\n   * server response.\n   *\n   * @param options - Options for performing the request.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const foxx = db.route(\"/my-foxx-service\");\n   * const res = await foxx.request({\n   *   method: \"POST\",\n   *   path: \"/users\",\n   *   body: {\n   *     username: \"admin\",\n   *     password: \"hunter2\"\n   *   }\n   * });\n   * ```\n   */\n  request(options?: RequestOptions) {\n    const opts = { ...options };\n    if (!opts.path || opts.path === \"/\") opts.path = \"\";\n    else if (!this._path || opts.path.charAt(0) === \"/\") opts.path = opts.path;\n    else opts.path = `/${opts.path}`;\n    opts.basePath = this._path;\n    opts.headers = { ...this._headers, ...opts.headers };\n    opts.method = opts.method ? opts.method.toUpperCase() : \"GET\";\n    return this._db.request(opts, false);\n  }\n\n  /**\n   * Performs a DELETE request against the given path relative to this route\n   * and returns the server response.\n   *\n   * @param path - Path relative to this route.\n   * @param qs - Query string parameters for this request.\n   * @param headers - Additional headers to send with this request.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const foxx = db.route(\"/my-foxx-service\");\n   * const res = await foxx.delete(\"/users/admin\");\n   * ```\n   */\n  delete(\n    path: string,\n    qs?: string | Params,\n    headers?: Headers\n  ): Promise<ArangojsResponse>;\n  /**\n   * Performs a DELETE request against the given path relative to this route\n   * and returns the server response.\n   *\n   * @param qs - Query string parameters for this request.\n   * @param headers - Additional headers to send with this request.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const foxx = db.route(\"/my-foxx-service\");\n   * const user = foxx.roue(\"/users/admin\");\n   * const res = await user.delete();\n   * ```\n   */\n  delete(qs?: Params, headers?: Headers): Promise<ArangojsResponse>;\n  delete(...args: any[]): Promise<ArangojsResponse> {\n    const path = typeof args[0] === \"string\" ? args.shift() : undefined;\n    const [qs, headers] = args;\n    return this.request({ method: \"DELETE\", path, qs, headers });\n  }\n\n  /**\n   * Performs a GET request against the given path relative to this route\n   * and returns the server response.\n   *\n   * @param path - Path relative to this route.\n   * @param qs - Query string parameters for this request.\n   * @param headers - Additional headers to send with this request.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const foxx = db.route(\"/my-foxx-service\");\n   * const res = await foxx.get(\"/users\", { offset: 10, limit: 5 });\n   * ```\n   */\n  get(\n    path: string,\n    qs?: string | Params,\n    headers?: Headers\n  ): Promise<ArangojsResponse>;\n  /**\n   * Performs a GET request against the given path relative to this route\n   * and returns the server response.\n   *\n   * @param qs - Query string parameters for this request.\n   * @param headers - Additional headers to send with this request.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const foxx = db.route(\"/my-foxx-service\");\n   * const users = foxx.route(\"/users\");\n   * const res = await users.get({ offset: 10, limit: 5 });\n   * ```\n   */\n  get(qs?: Params, headers?: Headers): Promise<ArangojsResponse>;\n  get(...args: any[]): Promise<ArangojsResponse> {\n    const path = typeof args[0] === \"string\" ? args.shift() : undefined;\n    const [qs, headers] = args;\n    return this.request({ method: \"GET\", path, qs, headers });\n  }\n\n  /**\n   * Performs a HEAD request against the given path relative to this route\n   * and returns the server response.\n   *\n   * @param path - Path relative to this route.\n   * @param qs - Query string parameters for this request.\n   * @param headers - Additional headers to send with this request.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const foxx = db.route(\"/my-foxx-service\");\n   * const res = await foxx.head(\"/users\", { offset: 10, limit: 5 });\n   * ```\n   */\n  head(\n    path: string,\n    qs?: string | Params,\n    headers?: Headers\n  ): Promise<ArangojsResponse>;\n  /**\n   * Performs a HEAD request against the given path relative to this route\n   * and returns the server response.\n   *\n   * @param qs - Query string parameters for this request.\n   * @param headers - Additional headers to send with this request.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const foxx = db.route(\"/my-foxx-service\");\n   * const users = foxx.route(\"/users\");\n   * const res = await users.head({ offset: 10, limit: 5 });\n   * ```\n   */\n  head(qs?: Params, headers?: Headers): Promise<ArangojsResponse>;\n  head(...args: any[]): Promise<ArangojsResponse> {\n    const path = typeof args[0] === \"string\" ? args.shift() : undefined;\n    const [qs, headers] = args;\n    return this.request({ method: \"HEAD\", path, qs, headers });\n  }\n\n  /**\n   * Performs a PATCH request against the given path relative to this route\n   * and returns the server response.\n   *\n   * @param path - Path relative to this route.\n   * @param body - Body of the request object.\n   * @param qs - Query string parameters for this request.\n   * @param headers - Additional headers to send with this request.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const foxx = db.route(\"/my-foxx-service\");\n   * const res = await foxx.patch(\"/users/admin\", { password: \"admin\" });\n   * ```\n   */\n  patch(\n    path: string,\n    body?: any,\n    qs?: string | Params,\n    headers?: Headers\n  ): Promise<ArangojsResponse>;\n  /**\n   * Performs a PATCH request against the given path relative to this route\n   * and returns the server response.\n   *\n   * **Note**: `body` must not be a `string`.\n   *\n   * @param body - Body of the request object. Must not be a string.\n   * @param qs - Query string parameters for this request.\n   * @param headers - Additional headers to send with this request.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const foxx = db.route(\"/my-foxx-service\");\n   * const user = foxx.route(\"/users/admin\")\n   * const res = await user.patch({ password: \"admin\" });\n   * ```\n   */\n  patch(\n    body?: any,\n    qs?: string | Params,\n    headers?: Headers\n  ): Promise<ArangojsResponse>;\n  patch(...args: any[]): Promise<ArangojsResponse> {\n    const path = typeof args[0] === \"string\" ? args.shift() : undefined;\n    const [body, qs, headers] = args;\n    return this.request({ method: \"PATCH\", path, body, qs, headers });\n  }\n\n  /**\n   * Performs a POST request against the given path relative to this route\n   * and returns the server response.\n   *\n   * @param path - Path relative to this route.\n   * @param body - Body of the request object.\n   * @param qs - Query string parameters for this request.\n   * @param headers - Additional headers to send with this request.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const foxx = db.route(\"/my-foxx-service\");\n   * const res = await foxx.post(\"/users\", {\n   *   username: \"admin\",\n   *   password: \"hunter2\"\n   * });\n   * ```\n   */\n  post(\n    path: string,\n    body?: any,\n    qs?: string | Params,\n    headers?: Headers\n  ): Promise<ArangojsResponse>;\n  /**\n   * Performs a POST request against the given path relative to this route\n   * and returns the server response.\n   *\n   * **Note**: `body` must not be a `string`.\n   *\n   * @param body - Body of the request object. Must not be a string.\n   * @param qs - Query string parameters for this request.\n   * @param headers - Additional headers to send with this request.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const foxx = db.route(\"/my-foxx-service\");\n   * const users = foxx.route(\"/users\");\n   * const res = await users.post({\n   *   username: \"admin\",\n   *   password: \"hunter2\"\n   * });\n   * ```\n   */\n  post(\n    body?: any,\n    qs?: string | Params,\n    headers?: Headers\n  ): Promise<ArangojsResponse>;\n  post(...args: any[]): Promise<ArangojsResponse> {\n    const path = typeof args[0] === \"string\" ? args.shift() : undefined;\n    const [body, qs, headers] = args;\n    return this.request({ method: \"POST\", path, body, qs, headers });\n  }\n\n  /**\n   * Performs a PUT request against the given path relative to this route\n   * and returns the server response.\n   *\n   * @param path - Path relative to this route.\n   * @param body - Body of the request object.\n   * @param qs - Query string parameters for this request.\n   * @param headers - Additional headers to send with this request.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const foxx = db.route(\"/my-foxx-service\");\n   * const res = await foxx.put(\"/users/admin/password\", { password: \"admin\" });\n   * ```\n   */\n  put(\n    path: string,\n    body?: any,\n    qs?: string | Params,\n    headers?: Headers\n  ): Promise<ArangojsResponse>;\n  /**\n   * Performs a PUT request against the given path relative to this route\n   * and returns the server response.\n   *\n   * **Note**: `body` must not be a `string`.\n   *\n   * @param body - Body of the request object. Must not be a string.\n   * @param qs - Query string parameters for this request.\n   * @param headers - Additional headers to send with this request.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const foxx = db.route(\"/my-foxx-service\");\n   * const password = foxx.route(\"/users/admin/password\");\n   * const res = await password.put({ password: \"admin\" });\n   * ```\n   */\n  put(\n    body?: any,\n    qs?: string | Params,\n    headers?: Headers\n  ): Promise<ArangojsResponse>;\n  put(...args: any[]): Promise<ArangojsResponse> {\n    const path = typeof args[0] === \"string\" ? args.shift() : undefined;\n    const [body, qs, headers] = args;\n    return this.request({ method: \"PUT\", path, body, qs, headers });\n  }\n}\n"]}