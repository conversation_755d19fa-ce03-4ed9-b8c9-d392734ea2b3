{"version": 3, "file": "view.d.ts", "sourceRoot": "", "sources": ["../src/view.ts"], "names": [], "mappings": "AAAA;;;;;;;;GAQG;AACH,OAAO,EAAE,iBAAiB,EAAE,MAAM,cAAc,CAAC;AACjD,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAItC;;;;GAIG;AACH,wBAAgB,YAAY,CAAC,IAAI,EAAE,GAAG,GAAG,IAAI,IAAI,IAAI,CAEpD;AAED;;GAEG;AACH,MAAM,MAAM,SAAS,GAAG,MAAM,GAAG,KAAK,CAAC;AAEvC;;;;;;GAMG;AACH,MAAM,MAAM,6BAA6B,GAAG;IAC1C;;OAEG;IACH,IAAI,EAAE,aAAa,CAAC;IACpB;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,CAAC;CACpB,CAAC;AAEF;;;GAGG;AACH,MAAM,MAAM,uBAAuB,GAAG;IACpC;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IACb;;;;OAIG;IACH,kBAAkB,CAAC,EAAE,MAAM,CAAC;IAC5B;;;;OAIG;IACH,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAC1B;;;;;OAKG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB;;;;;OAKG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB;;;;;OAKG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;CACnB,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,WAAW,GAAG,KAAK,GAAG,MAAM,CAAC;AAEzC;;GAEG;AACH,MAAM,MAAM,iBAAiB,GACzB,6BAA6B,GAC7B,4BAA4B,CAAC;AAEjC;;GAEG;AACH,MAAM,MAAM,qBAAqB,GAC7B,iCAAiC,GACjC,gCAAgC,CAAC;AAErC;;GAEG;AACH,MAAM,MAAM,0BAA0B,GAClC,iCAAiC,GACjC,qCAAqC,CAAC;AAE1C;;GAEG;AACH,MAAM,MAAM,2BAA2B,GAAG;IACxC;;;;;OAKG;IACH,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC;IACrB;;;OAGG;IACH,MAAM,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,2BAA2B,CAAC,CAAC;IACrD;;;;;OAKG;IACH,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC3B;;;;OAIG;IACH,MAAM,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,2BAA2B,CAAC,CAAC;IACrD;;;OAGG;IACH,kBAAkB,CAAC,EAAE,OAAO,CAAC;IAC7B;;;;OAIG;IACH,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAC5B;;;;;OAKG;IACH,YAAY,CAAC,EAAE,OAAO,CAAC;IACvB;;;;;OAKG;IACH,KAAK,CAAC,EAAE,OAAO,CAAC;CACjB,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,iCAAiC,GAAG;IAC9C;;;;OAIG;IACH,mBAAmB,CAAC,EAAE,MAAM,CAAC;IAC7B;;;;OAIG;IACH,yBAAyB,CAAC,EAAE,MAAM,CAAC;IACnC;;;;;OAKG;IACH,kBAAkB,CAAC,EAAE,MAAM,CAAC;IAC5B;;;;;OAKG;IACH,mBAAmB,CAAC,EAAE,uBAAuB,CAAC;IAC9C;;;OAGG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,2BAA2B,EAAE,QAAQ,CAAC,CAAC,CAAC;CACrE,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,sCAAsC,GAChD,iCAAiC,CAAC;AAEpC;;GAEG;AACH,MAAM,WAAW,kCAAkC;IACjD;;;OAGG;IACH,MAAM,EAAE,MAAM,EAAE,CAAC;IACjB;;;;OAIG;IACH,WAAW,CAAC,EAAE,WAAW,CAAC;IAC1B;;;;;OAKG;IACH,KAAK,CAAC,EAAE,OAAO,CAAC;CACjB;AAED;;GAEG;AACH,MAAM,MAAM,6BAA6B,GACvC,iCAAiC,GAAG;IAClC;;OAEG;IACH,IAAI,EAAE,cAAc,CAAC;IACrB;;;;OAIG;IACH,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB;;;;OAIG;IACH,iBAAiB,CAAC,EAAE,MAAM,CAAC;IAC3B;;;;OAIG;IACH,kBAAkB,CAAC,EAAE,MAAM,CAAC;IAC5B;;;;;;;;;OASG;IACH,WAAW,CAAC,EAAE,CACV;QACE;;;WAGG;QACH,KAAK,EAAE,MAAM,CAAC;QACd;;;WAGG;QACH,SAAS,EAAE,SAAS,CAAC;KACtB,GACD;QACE;;;WAGG;QACH,KAAK,EAAE,MAAM,CAAC;QACd;;;WAGG;QACH,GAAG,EAAE,OAAO,CAAC;KACd,CACJ,EAAE,CAAC;IACJ;;;;OAIG;IACH,sBAAsB,CAAC,EAAE,WAAW,CAAC;IACrC;;;;;OAKG;IACH,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC3B;;;;;OAKG;IACH,eAAe,CAAC,EAAE,OAAO,CAAC;IAC1B;;;OAGG;IACH,YAAY,CAAC,EAAE,kCAAkC,EAAE,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE,EAAE,CAAC;IAC5E;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,EAAE,CAAC;CACzB,CAAC;AAEJ;;GAEG;AACH,MAAM,MAAM,2BAA2B,GAAG;IACxC;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IACnB;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;CACf,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,gCAAgC,GAAG;IAC7C;;OAEG;IACH,OAAO,EAAE,2BAA2B,EAAE,CAAC;CACxC,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,gCAAgC,GAAG,2BAA2B,GAAG;IAC3E;;;;OAIG;IACH,SAAS,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC;CAC3B,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,qCAAqC,GAAG;IAClD;;OAEG;IACH,OAAO,EAAE,gCAAgC,EAAE,CAAC;CAC7C,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,4BAA4B,GAAG,gCAAgC,GAAG;IAC5E;;OAEG;IACH,IAAI,EAAE,cAAc,CAAC;CACtB,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,sBAAsB,GAAG;IACnC;;OAEG;IACH,gBAAgB,EAAE,MAAM,CAAC;IACzB;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IACX;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;CACd,CAAC;AAEF,MAAM,MAAM,eAAe,GACvB,2BAA2B,GAC3B,0BAA0B,CAAC;AAE/B,MAAM,MAAM,2BAA2B,GAAG,sBAAsB,GAAG;IACjE,IAAI,EAAE,cAAc,CAAC;CACtB,CAAC;AAEF,MAAM,MAAM,0BAA0B,GAAG,sBAAsB,GAAG;IAChE,IAAI,EAAE,cAAc,CAAC;CACtB,CAAC;AAEF,MAAM,MAAM,cAAc,GACtB,0BAA0B,GAC1B,yBAAyB,CAAC;AAE9B;;GAEG;AACH,MAAM,MAAM,oBAAoB,GAAG;IACjC,SAAS,EAAE,MAAM,EAAE,CAAC;IACpB,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,oBAAoB,CAAC,CAAC;IAC7C,gBAAgB,EAAE,OAAO,CAAC;IAC1B,MAAM,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,oBAAoB,CAAC,CAAC;IAC9C,kBAAkB,EAAE,OAAO,CAAC;IAC5B,WAAW,EAAE,MAAM,GAAG,IAAI,CAAC;IAC3B,KAAK,EAAE,OAAO,CAAC;CAChB,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,0BAA0B,GAAG,2BAA2B,GAAG;IACrE,mBAAmB,EAAE,MAAM,CAAC;IAC5B,yBAAyB,EAAE,MAAM,CAAC;IAClC,kBAAkB,EAAE,MAAM,CAAC;IAC3B,eAAe,EAAE,MAAM,CAAC;IACxB,iBAAiB,EAAE,MAAM,CAAC;IAC1B,kBAAkB,EAAE,MAAM,CAAC;IAC3B,mBAAmB,EAAE,uBAAuB,GAAG,6BAA6B,CAAC;IAC7E,WAAW,EAAE;QACX,KAAK,EAAE,MAAM,CAAC;QACd,SAAS,EAAE,SAAS,CAAC;KACtB,EAAE,CAAC;IACJ,sBAAsB,EAAE,WAAW,CAAC;IACpC,gBAAgB,EAAE,OAAO,CAAC;IAC1B,eAAe,EAAE,OAAO,CAAC;IACzB,YAAY,EAAE;QACZ,MAAM,EAAE,MAAM,EAAE,CAAC;QACjB,WAAW,EAAE,WAAW,CAAC;QACzB,KAAK,EAAE,OAAO,CAAC;KAChB,EAAE,CAAC;IACJ,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,oBAAoB,EAAE,QAAQ,CAAC,CAAC,CAAC;IAC5D,YAAY,EAAE,MAAM,EAAE,CAAC;CACxB,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,yBAAyB,GAAG,0BAA0B,GAAG;IACnE,OAAO,EAAE;QAAE,UAAU,EAAE,MAAM,CAAC;QAAC,KAAK,EAAE,MAAM,CAAA;KAAE,EAAE,CAAC;CAClD,CAAC;AAEF;;GAEG;AACH,qBAAa,IAAI;IACf,SAAS,CAAC,KAAK,EAAE,MAAM,CAAC;IACxB,SAAS,CAAC,GAAG,EAAE,QAAQ,CAAC;IAExB;;OAEG;gBACS,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM;IAKtC;;;;OAIG;IACH,IAAI,YAAY,IAAI,IAAI,CAEvB;IAED;;OAEG;IACH,IAAI,IAAI,WAEP;IAED;;;;;;;;;;OAUG;IACH,GAAG,IAAI,OAAO,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;IAMlD;;;;;;;;;;OAUG;IACG,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC;IAYhC;;;;;;;;;;;;OAYG;IACH,MAAM,CAAC,OAAO,SAAS,iBAAiB,EACtC,OAAO,EAAE,iBAAiB,GACzB,OAAO,CACR,OAAO,OAAO,SAAS,6BAA6B,GAChD,2BAA2B,GAC3B,OAAO,SAAS,4BAA4B,GAC5C,0BAA0B,GAC1B,eAAe,CACpB;IAWD;;;;;;;;;;;;;;;;;;;;;OAqBG;IACG,MAAM,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;IAM1E;;;;;;;;;;OAUG;IACH,UAAU,IAAI,OAAO,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;IAMxD;;;;;;;;;;;;;;OAcG;IACH,gBAAgB,CAAC,UAAU,SAAS,0BAA0B,GAAG,SAAS,EACxE,UAAU,CAAC,EAAE,UAAU,GACtB,OAAO,CACR,UAAU,SAAS,sCAAsC,GACrD,0BAA0B,GAC1B,UAAU,SAAS,qCAAqC,GACxD,yBAAyB,GACzB,cAAc,CACnB;IAQD;;;;;;;;;;;;;;OAcG;IACH,iBAAiB,CAAC,UAAU,SAAS,qBAAqB,GAAG,SAAS,EACpE,UAAU,CAAC,EAAE,UAAU,GACtB,OAAO,CACR,UAAU,SAAS,iCAAiC,GAChD,0BAA0B,GAC1B,UAAU,SAAS,gCAAgC,GACnD,yBAAyB,GACzB,cAAc,CACnB;IAQD;;;;;;;;;;;OAWG;IACH,IAAI,IAAI,OAAO,CAAC,OAAO,CAAC;CASzB"}