{"version": 3, "file": "view.js", "sourceRoot": "", "sources": ["../src/view.ts"], "names": [], "mappings": ";;;AAWA,mCAAwC;AACxC,uCAA6C;AAE7C;;;;GAIG;AACH,SAAgB,YAAY,CAAC,IAAS;IACpC,OAAO,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC;AAC5C,CAAC;AAFD,oCAEC;AAicD;;GAEG;AACH,MAAa,IAAI;IAIf;;OAEG;IACH,YAAY,EAAY,EAAE,IAAY;QACpC,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IACrC,CAAC;IAED;;;;OAIG;IACH,IAAI,YAAY;QACd,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED;;;;;;;;;;OAUG;IACH,GAAG;QACD,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;YACtB,IAAI,EAAE,cAAc,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;SACrD,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;OAUG;IACH,KAAK,CAAC,MAAM;QACV,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,GAAG,EAAE,CAAC;YACjB,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAClB,IAAI,IAAA,qBAAa,EAAC,GAAG,CAAC,IAAI,GAAG,CAAC,QAAQ,KAAK,sBAAc,EAAE,CAAC;gBAC1D,OAAO,KAAK,CAAC;YACf,CAAC;YACD,MAAM,GAAG,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,MAAM,CACJ,OAA0B;QAQ1B,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;YACtB,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,YAAY;YAClB,IAAI,EAAE;gBACJ,GAAG,OAAO;gBACV,IAAI,EAAE,IAAI,CAAC,KAAK;aACjB;SACF,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,KAAK,CAAC,MAAM,CAAC,OAAe;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QACxD,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACtC,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;;;;;;OAUG;IACH,UAAU;QACR,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;YACtB,IAAI,EAAE,cAAc,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa;SAChE,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACH,gBAAgB,CACd,UAAuB;QAQvB,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;YACtB,MAAM,EAAE,OAAO;YACf,IAAI,EAAE,cAAc,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa;YAC/D,IAAI,EAAE,UAAU,IAAI,EAAE;SACvB,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACH,iBAAiB,CACf,UAAuB;QAQvB,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;YACtB,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,cAAc,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa;YAC/D,IAAI,EAAE,UAAU,IAAI,EAAE;SACvB,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;OAWG;IACH,IAAI;QACF,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CACrB;YACE,MAAM,EAAE,QAAQ;YAChB,IAAI,EAAE,cAAc,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;SACrD,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CACzB,CAAC;IACJ,CAAC;CACF;AApOD,oBAoOC", "sourcesContent": ["/**\n * ```ts\n * import type { ArangoSearchView } from \"arangojs/view\";\n * ```\n *\n * The \"view\" module provides View related types and interfaces for TypeScript.\n *\n * @packageDocumentation\n */\nimport { ArangoApiResponse } from \"./connection\";\nimport { Database } from \"./database\";\nimport { isArangoError } from \"./error\";\nimport { VIEW_NOT_FOUND } from \"./lib/codes\";\n\n/**\n * Indicates whether the given value represents a {@link View}.\n *\n * @param view - A value that might be a View.\n */\nexport function isArangoView(view: any): view is View {\n  return Boolean(view && view.isArangoView);\n}\n\n/**\n * Sorting direction. Descending or ascending.\n */\nexport type Direction = \"desc\" | \"asc\";\n\n/**\n * Policy to consolidate based on segment byte size and live document count as\n * dictated by the customization attributes.\n *\n * @deprecated The `bytes_accum` consolidation policy was deprecated in\n * ArangoDB 3.7 and should be replaced with the `tier` consolidation policy.\n */\nexport type BytesAccumConsolidationPolicy = {\n  /**\n   * Type of consolidation policy.\n   */\n  type: \"bytes_accum\";\n  /**\n   * Must be in the range of `0.0` to `1.0`.\n   */\n  threshold?: number;\n};\n\n/**\n * Policy to consolidate if the sum of all candidate segment byte size is less\n * than the total segment byte size multiplied by a given threshold.\n */\nexport type TierConsolidationPolicy = {\n  /**\n   * Type of consolidation policy.\n   */\n  type: \"tier\";\n  /**\n   * Size below which all segments are treated as equivalent.\n   *\n   * Default: `2097152` (2 MiB)\n   */\n  segmentsBytesFloor?: number;\n  /**\n   * Maximum allowed size of all consolidation segments.\n   *\n   * Default: `5368709120` (5 GiB)\n   */\n  segmentsBytesMax?: number;\n  /**\n   * Maximum number of segments that are evaluated as candidates for\n   * consolidation.\n   *\n   * Default: `10`\n   */\n  segmentsMax?: number;\n  /**\n   * Minimum number of segments that are evaluated as candidates for\n   * consolidation.\n   *\n   * Default: `1`\n   */\n  segmentsMin?: number;\n  /**\n   * Consolidation candidates with a score less than this value will be\n   * filtered out.\n   *\n   * Default: `0`\n   */\n  minScore?: number;\n};\n\n/**\n * Compression for storing data.\n */\nexport type Compression = \"lz4\" | \"none\";\n\n/**\n * Options for creating a View.\n */\nexport type CreateViewOptions =\n  | CreateArangoSearchViewOptions\n  | CreateSearchAliasViewOptions;\n\n/**\n * Options for replacing a View's properties.\n */\nexport type ViewPropertiesOptions =\n  | ArangoSearchViewPropertiesOptions\n  | SearchAliasViewPropertiesOptions;\n\n/**\n * Options for partially modifying a View's properties.\n */\nexport type ViewPatchPropertiesOptions =\n  | ArangoSearchViewPropertiesOptions\n  | SearchAliasViewPatchPropertiesOptions;\n\n/**\n * A link definition for an ArangoSearch View.\n */\nexport type ArangoSearchViewLinkOptions = {\n  /**\n   * A list of names of Analyzers to apply to values of processed document\n   * attributes.\n   *\n   * Default: `[\"identity\"]`\n   */\n  analyzers?: string[];\n  /**\n   * An object mapping names of attributes to process for each document to\n   * {@link ArangoSearchViewLinkOptions} definitions.\n   */\n  fields?: Record<string, ArangoSearchViewLinkOptions>;\n  /**\n   * If set to `true`, all document attributes will be processed, otherwise\n   * only the attributes in `fields` will be processed.\n   *\n   * Default: `false`\n   */\n  includeAllFields?: boolean;\n  /**\n   * (Enterprise Edition only.) An object mapping attribute names to\n   * {@link ArangoSearchViewLinkOptions} definitions to index sub-objects\n   * stored in an array.\n   */\n  nested?: Record<string, ArangoSearchViewLinkOptions>;\n  /**\n   * If set to `true`, the position of values in array values will be tracked,\n   * otherwise all values in an array will be treated as equal alternatives.\n   */\n  trackListPositions?: boolean;\n  /**\n   * Controls how the view should keep track of the attribute values.\n   *\n   * Default: `\"none\"`\n   */\n  storeValues?: \"none\" | \"id\";\n  /**\n   * If set to `true`, then no exclusive lock is used on the source collection\n   * during View index creation, so that it remains basically available.\n   *\n   * Default: `false`\n   */\n  inBackground?: boolean;\n  /**\n   * (Enterprise Edition only.) If set to `true`, then field normalization\n   * values will always be cached in memory.\n   *\n   * Default: `false`\n   */\n  cache?: boolean;\n};\n\n/**\n * Options for modifying the properties of an ArangoSearch View.\n */\nexport type ArangoSearchViewPropertiesOptions = {\n  /**\n   * How many commits to wait between removing unused files.\n   *\n   * Default: `2`\n   */\n  cleanupIntervalStep?: number;\n  /**\n   * How long to wait between applying the `consolidationPolicy`.\n   *\n   * Default: `10000`\n   */\n  consolidationIntervalMsec?: number;\n  /**\n   * How long to wait between commiting View data store changes and making\n   * documents visible to queries.\n   *\n   * Default: `1000`\n   */\n  commitIntervalMsec?: number;\n  /**\n   * Consolidation policy to apply for selecting which segments should be\n   * merged.\n   *\n   * Default: `{ type: \"tier\" }`\n   */\n  consolidationPolicy?: TierConsolidationPolicy;\n  /**\n   * An object mapping names of linked collections to\n   * {@link ArangoSearchViewLinkOptions} definitions.\n   */\n  links?: Record<string, Omit<ArangoSearchViewLinkOptions, \"nested\">>;\n};\n\n/**\n * Options for partially modifying the properties of an ArangoSearch View.\n */\nexport type ArangoSearchViewPatchPropertiesOptions =\n  ArangoSearchViewPropertiesOptions;\n\n/**\n * Options for creating a stored value in an ArangoSearch View.\n */\nexport interface ArangoSearchViewStoredValueOptions {\n  /**\n   * Attribute paths for which values should be stored in the view index\n   * in addition to those used for sorting via `primarySort`.\n   */\n  fields: string[];\n  /**\n   * How the attribute values should be compressed.\n   *\n   * Default: `\"lz4\"`\n   */\n  compression?: Compression;\n  /**\n   * (Enterprise Edition only.) If set to `true`, then stored values will\n   * always be cached in memory.\n   *\n   * Default: `false`\n   */\n  cache?: boolean;\n}\n\n/**\n * Options for creating an ArangoSearch View.\n */\nexport type CreateArangoSearchViewOptions =\n  ArangoSearchViewPropertiesOptions & {\n    /**\n     * Type of the View.\n     */\n    type: \"arangosearch\";\n    /**\n     * Maximum number of writers cached in the pool.\n     *\n     * Default: `64`\n     */\n    writebufferIdle?: number;\n    /**\n     * Maximum number of concurrent active writers that perform a transaction.\n     *\n     * Default: `0`\n     */\n    writebufferActive?: number;\n    /**\n     * Maximum memory byte size per writer before a writer flush is triggered.\n     *\n     * Default: `33554432` (32 MiB)\n     */\n    writebufferSizeMax?: number;\n    /**\n     * Attribute path (`field`) for the value of each document that will be\n     * used for sorting.\n     *\n     * If `direction` is set to `\"asc\"` or `asc` is set to `true`,\n     * the primary sorting order will be ascending.\n     *\n     * If `direction` is set to `\"desc\"` or `asc` is set to `false`,\n     * the primary sorting order will be descending.\n     */\n    primarySort?: (\n      | {\n          /**\n           * Attribute path for the value of each document to use for\n           * sorting.\n           */\n          field: string;\n          /**\n           * If set to `\"asc\"`, the primary sorting order will be ascending.\n           * If set to `\"desc\"`, the primary sorting order will be descending.\n           */\n          direction: Direction;\n        }\n      | {\n          /**\n           * Attribute path for the value of each document to use for\n           * sorting.\n           */\n          field: string;\n          /**\n           * If set to `true`, the primary sorting order will be ascending.\n           * If set to `false`, the primary sorting order will be descending.\n           */\n          asc: boolean;\n        }\n    )[];\n    /**\n     * Compression to use for the primary sort data.\n     *\n     * Default: `\"lz4\"`\n     */\n    primarySortCompression?: Compression;\n    /**\n     * (Enterprise Edition only.) If set to `true`, then primary sort columns\n     * will always be cached in memory.\n     *\n     * Default: `false`\n     */\n    primarySortCache?: boolean;\n    /**\n     * (Enterprise Edition only.) If set to `true`, then primary key columns\n     * will always be cached in memory.\n     *\n     * Default: `false`\n     */\n    primaryKeyCache?: boolean;\n    /**\n     * Attribute paths for which values should be stored in the view index\n     * in addition to those used for sorting via `primarySort`.\n     */\n    storedValues?: ArangoSearchViewStoredValueOptions[] | string[] | string[][];\n    /**\n     * An array of strings defining sort expressions to optimize.\n     */\n    optimizeTopK?: string[];\n  };\n\n/**\n * Options defining an index used in a SearchAlias View.\n */\nexport type SearchAliasViewIndexOptions = {\n  /**\n   *  Name of a collection.\n   */\n  collection: string;\n  /**\n   * Name of an inverted index in the collection.\n   */\n  index: string;\n};\n\n/**\n * Options for modifying the properties of a SearchAlias View.\n */\nexport type SearchAliasViewPropertiesOptions = {\n  /**\n   * An array of inverted indexes to add to the View.\n   */\n  indexes: SearchAliasViewIndexOptions[];\n};\n\n/**\n * Options defining an index to be modified in a SearchAlias View.\n */\nexport type SearchAliasViewPatchIndexOptions = SearchAliasViewIndexOptions & {\n  /**\n   * Whether to add or remove the index.\n   *\n   * Default: `\"add\"`\n   */\n  operation?: \"add\" | \"del\";\n};\n\n/**\n * Options for partially modifying the properties of a SearchAlias View.\n */\nexport type SearchAliasViewPatchPropertiesOptions = {\n  /**\n   * An array of inverted indexes to add to the View.\n   */\n  indexes: SearchAliasViewPatchIndexOptions[];\n};\n\n/**\n * Options for creating a SearchAlias View.\n */\nexport type CreateSearchAliasViewOptions = SearchAliasViewPropertiesOptions & {\n  /**\n   * Type of the View.\n   */\n  type: \"search-alias\";\n};\n\n/**\n * Generic description of a View.\n */\nexport type GenericViewDescription = {\n  /**\n   * A globally unique identifier for this View.\n   */\n  globallyUniqueId: string;\n  /**\n   * An identifier for this View.\n   */\n  id: string;\n  /**\n   * Name of the View.\n   */\n  name: string;\n};\n\nexport type ViewDescription =\n  | ArangoSearchViewDescription\n  | SearchAliasViewDescription;\n\nexport type ArangoSearchViewDescription = GenericViewDescription & {\n  type: \"arangosearch\";\n};\n\nexport type SearchAliasViewDescription = GenericViewDescription & {\n  type: \"search-alias\";\n};\n\nexport type ViewProperties =\n  | ArangoSearchViewProperties\n  | SearchAliasViewProperties;\n\n/**\n * A link definition for an ArangoSearch View.\n */\nexport type ArangoSearchViewLink = {\n  analyzers: string[];\n  fields: Record<string, ArangoSearchViewLink>;\n  includeAllFields: boolean;\n  nested?: Record<string, ArangoSearchViewLink>;\n  trackListPositions: boolean;\n  storeValues: \"none\" | \"id\";\n  cache: boolean;\n};\n\n/**\n * Properties of an ArangoSearch View.\n */\nexport type ArangoSearchViewProperties = ArangoSearchViewDescription & {\n  cleanupIntervalStep: number;\n  consolidationIntervalMsec: number;\n  commitIntervalMsec: number;\n  writebufferIdle: number;\n  writebufferActive: number;\n  writebufferSizeMax: number;\n  consolidationPolicy: TierConsolidationPolicy | BytesAccumConsolidationPolicy;\n  primarySort: {\n    field: string;\n    direction: Direction;\n  }[];\n  primarySortCompression: Compression;\n  primarySortCache: boolean;\n  primaryKeyCache: boolean;\n  storedValues: {\n    fields: string[];\n    compression: Compression;\n    cache: boolean;\n  }[];\n  links: Record<string, Omit<ArangoSearchViewLink, \"nested\">>;\n  optimizeTopK: string[];\n};\n\n/**\n * Properties of a SearchAlias View.\n */\nexport type SearchAliasViewProperties = SearchAliasViewDescription & {\n  indexes: { collection: string; index: string }[];\n};\n\n/**\n * Represents a View in a {@link database.Database}.\n */\nexport class View {\n  protected _name: string;\n  protected _db: Database;\n\n  /**\n   * @internal\n   */\n  constructor(db: Database, name: string) {\n    this._db = db;\n    this._name = name.normalize(\"NFC\");\n  }\n\n  /**\n   * @internal\n   *\n   * Indicates that this object represents an ArangoDB View.\n   */\n  get isArangoView(): true {\n    return true;\n  }\n\n  /**\n   * Name of the View.\n   */\n  get name() {\n    return this._name;\n  }\n\n  /**\n   * Retrieves general information about the View.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const view = db.view(\"some-view\");\n   * const data = await view.get();\n   * // data contains general information about the View\n   * ```\n   */\n  get(): Promise<ArangoApiResponse<ViewDescription>> {\n    return this._db.request({\n      path: `/_api/view/${encodeURIComponent(this._name)}`,\n    });\n  }\n\n  /**\n   * Checks whether the View exists.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const view = db.view(\"some-view\");\n   * const exists = await view.exists();\n   * console.log(exists); // indicates whether the View exists\n   * ```\n   */\n  async exists(): Promise<boolean> {\n    try {\n      await this.get();\n      return true;\n    } catch (err: any) {\n      if (isArangoError(err) && err.errorNum === VIEW_NOT_FOUND) {\n        return false;\n      }\n      throw err;\n    }\n  }\n\n  /**\n   * Creates a View with the given `options` and the instance's name.\n   *\n   * See also {@link database.Database#createView}.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const view = db.view(\"potatoes\");\n   * await view.create();\n   * // the ArangoSearch View \"potatoes\" now exists\n   * ```\n   */\n  create<Options extends CreateViewOptions>(\n    options: CreateViewOptions\n  ): Promise<\n    typeof options extends CreateArangoSearchViewOptions\n      ? ArangoSearchViewDescription\n      : Options extends CreateSearchAliasViewOptions\n      ? SearchAliasViewDescription\n      : ViewDescription\n  > {\n    return this._db.request({\n      method: \"POST\",\n      path: \"/_api/view\",\n      body: {\n        ...options,\n        name: this._name,\n      },\n    });\n  }\n\n  /**\n   * Renames the View and updates the instance's `name` to `newName`.\n   *\n   * Additionally removes the instance from the {@link database.Database}'s internal\n   * cache.\n   *\n   * **Note**: Renaming Views may not be supported when ArangoDB is\n   * running in a cluster configuration.\n   *\n   * @param newName - The new name of the View.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const view1 = db.view(\"some-view\");\n   * await view1.rename(\"other-view\");\n   * const view2 = db.view(\"some-view\");\n   * const view3 = db.view(\"other-view\");\n   * // Note all three View instances are different objects but\n   * // view1 and view3 represent the same ArangoDB view!\n   * ```\n   */\n  async rename(newName: string): Promise<ArangoApiResponse<ViewDescription>> {\n    const result = this._db.renameView(this._name, newName);\n    this._name = newName.normalize(\"NFC\");\n    return result;\n  }\n\n  /**\n   * Retrieves the View's properties.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const view = db.view(\"some-view\");\n   * const data = await view.properties();\n   * // data contains the View's properties\n   * ```\n   */\n  properties(): Promise<ArangoApiResponse<ViewProperties>> {\n    return this._db.request({\n      path: `/_api/view/${encodeURIComponent(this._name)}/properties`,\n    });\n  }\n\n  /**\n   * Updates the properties of the View.\n   *\n   * @param properties - Properties of the View to update.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const view = db.view(\"some-view\");\n   * const result = await view.updateProperties({\n   *   consolidationIntervalMsec: 234\n   * });\n   * console.log(result.consolidationIntervalMsec); // 234\n   * ```\n   */\n  updateProperties<Properties extends ViewPatchPropertiesOptions | undefined>(\n    properties?: Properties\n  ): Promise<\n    Properties extends ArangoSearchViewPatchPropertiesOptions\n      ? ArangoSearchViewProperties\n      : Properties extends SearchAliasViewPatchPropertiesOptions\n      ? SearchAliasViewProperties\n      : ViewProperties\n  > {\n    return this._db.request({\n      method: \"PATCH\",\n      path: `/_api/view/${encodeURIComponent(this._name)}/properties`,\n      body: properties ?? {},\n    });\n  }\n\n  /**\n   * Replaces the properties of the View.\n   *\n   * @param properties - New properties of the View.\n   *\n   * @example\n   * ```js\n   * const db = new Database();\n   * const view = db.view(\"some-view\");\n   * const result = await view.replaceProperties({\n   *   consolidationIntervalMsec: 234\n   * });\n   * console.log(result.consolidationIntervalMsec); // 234\n   * ```\n   */\n  replaceProperties<Properties extends ViewPropertiesOptions | undefined>(\n    properties?: Properties\n  ): Promise<\n    Properties extends ArangoSearchViewPropertiesOptions\n      ? ArangoSearchViewProperties\n      : Properties extends SearchAliasViewPropertiesOptions\n      ? SearchAliasViewProperties\n      : ViewProperties\n  > {\n    return this._db.request({\n      method: \"PUT\",\n      path: `/_api/view/${encodeURIComponent(this._name)}/properties`,\n      body: properties ?? {},\n    });\n  }\n\n  /**\n   * Deletes the View from the database.\n   *\n   * @example\n   *\n   * ```js\n   * const db = new Database();\n   * const view = db.view(\"some-view\");\n   * await view.drop();\n   * // the View \"some-view\" no longer exists\n   * ```\n   */\n  drop(): Promise<boolean> {\n    return this._db.request(\n      {\n        method: \"DELETE\",\n        path: `/_api/view/${encodeURIComponent(this._name)}`,\n      },\n      (res) => res.body.result\n    );\n  }\n}\n"]}