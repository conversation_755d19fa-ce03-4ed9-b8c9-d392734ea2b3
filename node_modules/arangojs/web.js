!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.arangojs=e():t.arangojs=e()}(self,(function(){return function(){var t={919:function(t,e,r){var n;n="undefined"!=typeof window?window:void 0!==r.g?r.g:"undefined"!=typeof self?self:{},t.exports=n},847:function(t){t.exports=function(t){if(!t)return!1;var r=e.call(t);return"[object Function]"===r||"function"==typeof t&&"[object RegExp]"!==r||"undefined"!=typeof window&&(t===window.setTimeout||t===window.alert||t===window.confirm||t===window.prompt)};var e=Object.prototype.toString},216:function(t){var e=function(t){return t.replace(/^\s+|\s+$/g,"")};t.exports=function(t){if(!t)return{};for(var r,n={},o=e(t).split("\n"),i=0;i<o.length;i++){var s=o[i],a=s.indexOf(":"),h=e(s.slice(0,a)).toLowerCase(),u=e(s.slice(a+1));void 0===n[h]?n[h]=u:(r=n[h],"[object Array]"===Object.prototype.toString.call(r)?n[h].push(u):n[h]=[n[h],u])}return n}},239:function(t){var e=function(t){"use strict";var e,r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},s=i.iterator||"@@iterator",a=i.asyncIterator||"@@asyncIterator",h=i.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function c(t,e,r,n){var i=e&&e.prototype instanceof _?e:_,s=Object.create(i.prototype),a=new I(n||[]);return o(s,"_invoke",{value:U(t,r,a)}),s}function l(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var d="suspendedStart",p="suspendedYield",m="executing",f="completed",y={};function _(){}function g(){}function b(){}var v={};u(v,s,(function(){return this}));var w=Object.getPrototypeOf,q=w&&w(w(A([])));q&&q!==r&&n.call(q,s)&&(v=q);var x=b.prototype=_.prototype=Object.create(v);function T(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function C(t,e){function r(o,i,s,a){var h=l(t[o],t,i);if("throw"!==h.type){var u=h.arg,c=u.value;return c&&"object"==typeof c&&n.call(c,"__await")?e.resolve(c.__await).then((function(t){r("next",t,s,a)}),(function(t){r("throw",t,s,a)})):e.resolve(c).then((function(t){u.value=t,s(u)}),(function(t){return r("throw",t,s,a)}))}a(h.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return i=i?i.then(o,o):o()}})}function U(t,r,n){var o=d;return function(i,s){if(o===m)throw new Error("Generator is already running");if(o===f){if("throw"===i)throw s;return{value:e,done:!0}}for(n.method=i,n.arg=s;;){var a=n.delegate;if(a){var h=R(a,n);if(h){if(h===y)continue;return h}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===d)throw o=f,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=m;var u=l(t,r,n);if("normal"===u.type){if(o=n.done?f:p,u.arg===y)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(o=f,n.method="throw",n.arg=u.arg)}}}function R(t,r){var n=r.method,o=t.iterator[n];if(o===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,R(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var i=l(o,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,y;var s=i.arg;return s?s.done?(r[t.resultName]=s.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,y):s:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function S(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function I(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function A(t){if(null!=t){var r=t[s];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,i=function r(){for(;++o<t.length;)if(n.call(t,o))return r.value=t[o],r.done=!1,r;return r.value=e,r.done=!0,r};return i.next=i}}throw new TypeError(typeof t+" is not iterable")}return g.prototype=b,o(x,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:g,configurable:!0}),g.displayName=u(b,h,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,u(t,h,"GeneratorFunction")),t.prototype=Object.create(x),t},t.awrap=function(t){return{__await:t}},T(C.prototype),u(C.prototype,a,(function(){return this})),t.AsyncIterator=C,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var s=new C(c(e,r,n,o),i);return t.isGeneratorFunction(r)?s:s.next().then((function(t){return t.done?t.value:s.next()}))},T(x),u(x,h,"Generator"),u(x,s,(function(){return this})),u(x,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=A,I.prototype={constructor:I,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(S),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function o(n,o){return a.type="throw",a.arg=t,r.next=n,o&&(r.method="next",r.arg=e),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var s=this.tryEntries[i],a=s.completion;if("root"===s.tryLoc)return o("end");if(s.tryLoc<=this.prev){var h=n.call(s,"catchLoc"),u=n.call(s,"finallyLoc");if(h&&u){if(this.prev<s.catchLoc)return o(s.catchLoc,!0);if(this.prev<s.finallyLoc)return o(s.finallyLoc)}else if(h){if(this.prev<s.catchLoc)return o(s.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<s.finallyLoc)return o(s.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var s=i?i.completion:{};return s.type=t,s.arg=e,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(s)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),S(r),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;S(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:A(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),y}},t}(t.exports);try{regeneratorRuntime=e}catch(t){"object"==typeof globalThis?globalThis.regeneratorRuntime=e:Function("r","regeneratorRuntime = r")(e)}},665:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});const n=r(246);class o{constructor(t){if(this.length=0,this.unlinkCleanup=t=>{this.first===t&&(this.first=this.first.behind),this.last===t&&(this.last=this.last.before),this.length--},t){t instanceof o&&(t=t.values());for(const e of t)this.push(e)}}clear(t=!1){if(t)for(;this.first;)this.first.unlink(!0);this.first=this.last=void 0,this.length=0}every(t,e){e&&(t=t.bind(e));for(const e of this.keys())if(!t(e.value,e,this))return!1;return!0}filter(t,e){e&&(t=t.bind(e));const r=new o;for(const[e,n]of this)t(n,e,this)&&r.push(n);return r}find(t,e){e&&(t=t.bind(e));for(const[e,r]of this)if(t(r,e,this))return r}findItem(t,e){e&&(t=t.bind(e));for(const[e,r]of this)if(t(r,e,this))return e}forEach(t,e){e&&(t=t.bind(e));for(const[e,r]of this)t(r,e,this)}includes(t,e=0){let r=this.getItemByIndex(e);for(;r;){if(r.value===t)return!0;r=r.behind}return!1}itemOf(t,e=0){let r=this.getItemByIndex(e);for(;r;){if(r.value===t)return r;r=r.behind}}lastItemOf(t,e=-1){let r=this.getItemByIndex(e);for(;r;){if(r.value===t)return r;r=r.before}}map(t,e){e&&(t=t.bind(e));const r=new o;for(const[e,n]of this)r.push(t(n,e,this));return r}reduce(t,e){let r=this.first;if(!r){if(!e)throw new TypeError("Empty accumulator on empty LinkedList is not allowed.");return e}if(void 0===e){if(e=r.value,!r.behind)return e;r=r.behind}do{e=t(e,r.value,r,this),r=r.behind}while(r);return e}reduceRight(t,e){let r=this.last;if(!r){if(!e)throw new TypeError("Empty accumulator on empty LinkedList is not allowed.");return e}if(void 0===e){if(e=r.value,!r.before)return e;r=r.before}do{e=t(e,r.value,r,this),r=r.before}while(r);return e}some(t,e){e&&(t=t.bind(e));for(const[e,r]of this)if(t(r,e,this))return!0;return!1}join(t){return[...this.values()].join(t)}concat(...t){const e=new o(this);for(const r of t)r instanceof o?e.push(...r.values()):e.push(r);return e}pop(){if(!this.last)return;const t=this.last;return t.unlink(),t.value}push(...t){for(const e of t){const t=new n.LinkedListItem(e,this.unlinkCleanup);this.first&&this.last?(this.last.insertBehind(t),this.last=t):this.first=this.last=t,this.length++}return this.length}unshift(...t){for(const e of t){const t=new n.LinkedListItem(e,this.unlinkCleanup);this.last&&this.first?(t.insertBehind(this.first),this.first=t):this.first=this.last=t,this.length++}return this.length}remove(t){for(const e of this.keys())if(e.value===t)return e.unlink(),!0;return!1}removeAllOccurrences(t){let e=!1;for(const r of this.keys())r.value===t&&(r.unlink(),e=!0);return e}shift(){if(!this.first)return;const t=this.first;return t.unlink(),t.value}*[Symbol.iterator](){let t=this.first;if(t)do{yield[t,t.value],t=t.behind}while(t)}entries(){return this[Symbol.iterator]()}*keys(){let t=this.first;if(t)do{yield t,t=t.behind}while(t)}*values(){let t=this.first;if(t)do{yield t.value,t=t.behind}while(t)}getItemByIndex(t){if(void 0===t)throw new Error("index must be a number!");if(!this.first)return;let e;if(t>0)for(e=this.first;e&&t--;)e=e.behind;else{if(!(t<0))return this.first;for(e=this.last;e&&++t;)e=e.before}return e}}e.LinkedList=o},246:function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.LinkedListItem=class{constructor(t,e){this.value=t,this.unlinkCleanup=e}insertBehind(t){if(t.insertBefore(this),this.behind){let e=t;for(;e.behind;)e=e.behind;this.behind.insertBefore(e),e.insertBehind(this.behind)}this.behind=t}unlink(t=!1){this.before&&(this.before.behind=this.behind),this.behind&&(this.behind.before=this.before),this.unlinkCleanup&&this.unlinkCleanup(this),this.unlinkCleanup=void 0,t&&(this.before=this.behind=void 0)}insertBefore(t){this.before=t,this.unlinkCleanup||(this.unlinkCleanup=t.unlinkCleanup)}}},462:function(t,e,r){"use strict";function n(t){for(var r in t)e.hasOwnProperty(r)||(e[r]=t[r])}Object.defineProperty(e,"__esModule",{value:!0}),n(r(665)),n(r(246))},558:function(t,e,r){"use strict";var n=r(919),o=r(847),i=r(216),s=r(597);function a(t,e,r){var n=t;return o(e)?(r=e,"string"==typeof t&&(n={uri:t})):n=s(e,{uri:t}),n.callback=r,n}function h(t,e,r){return u(e=a(t,e,r))}function u(t){if(void 0===t.callback)throw new Error("callback argument missing");var e=!1,r=function(r,n,o){e||(e=!0,t.callback(r,n,o))};function n(){var t=void 0;if(t=c.response?c.response:c.responseText||function(t){try{if("document"===t.responseType)return t.responseXML;var e=t.responseXML&&"parsererror"===t.responseXML.documentElement.nodeName;if(""===t.responseType&&!e)return t.responseXML}catch(t){}return null}(c),_)try{t=JSON.parse(t)}catch(t){}return t}function o(t){return clearTimeout(l),t instanceof Error||(t=new Error(""+(t||"Unknown XMLHttpRequest Error"))),t.statusCode=0,r(t,g)}function s(){if(!u){var e;clearTimeout(l),e=t.useXDR&&void 0===c.status?200:1223===c.status?204:c.status;var o=g,s=null;return 0!==e?(o={body:n(),statusCode:e,method:p,headers:{},url:d,rawRequest:c},c.getAllResponseHeaders&&(o.headers=i(c.getAllResponseHeaders()))):s=new Error("Internal XMLHttpRequest Error"),r(s,o,o.body)}}var a,u,c=t.xhr||null;c||(c=t.cors||t.useXDR?new h.XDomainRequest:new h.XMLHttpRequest);var l,d=c.url=t.uri||t.url,p=c.method=t.method||"GET",m=t.body||t.data,f=c.headers=t.headers||{},y=!!t.sync,_=!1,g={body:void 0,headers:{},statusCode:0,method:p,url:d,rawRequest:c};if("json"in t&&!1!==t.json&&(_=!0,f.accept||f.Accept||(f.Accept="application/json"),"GET"!==p&&"HEAD"!==p&&(f["content-type"]||f["Content-Type"]||(f["Content-Type"]="application/json"),m=JSON.stringify(!0===t.json?m:t.json))),c.onreadystatechange=function(){4===c.readyState&&setTimeout(s,0)},c.onload=s,c.onerror=o,c.onprogress=function(){},c.onabort=function(){u=!0},c.ontimeout=o,c.open(p,d,!y,t.username,t.password),y||(c.withCredentials=!!t.withCredentials),!y&&t.timeout>0&&(l=setTimeout((function(){if(!u){u=!0,c.abort("timeout");var t=new Error("XMLHttpRequest timeout");t.code="ETIMEDOUT",o(t)}}),t.timeout)),c.setRequestHeader)for(a in f)f.hasOwnProperty(a)&&c.setRequestHeader(a,f[a]);else if(t.headers&&!function(t){for(var e in t)if(t.hasOwnProperty(e))return!1;return!0}(t.headers))throw new Error("Headers cannot be set on an XDomainRequest object");return"responseType"in t&&(c.responseType=t.responseType),"beforeSend"in t&&"function"==typeof t.beforeSend&&t.beforeSend(c),c.send(m||null),c}t.exports=h,t.exports.default=h,h.XMLHttpRequest=n.XMLHttpRequest||function(){},h.XDomainRequest="withCredentials"in new h.XMLHttpRequest?h.XMLHttpRequest:n.XDomainRequest,function(t,e){for(var r=0;r<t.length;r++)e(t[r])}(["get","put","post","patch","head","delete"],(function(t){h["delete"===t?"del":t]=function(e,r,n){return(r=a(e,r,n)).method=t.toUpperCase(),u(r)}}))},597:function(t){t.exports=function(){for(var t={},r=0;r<arguments.length;r++){var n=arguments[r];for(var o in n)e.call(n,o)&&(t[o]=n[o])}return t};var e=Object.prototype.hasOwnProperty},357:function(t,e,r){"use strict";r.d(e,{D:function(){return s},P:function(){return i}});var n=r(489),o=r(625);function i(t){return Boolean(t&&t.isArangoAnalyzer)}class s{constructor(t,e){this._db=t,this._name=e.normalize("NFC")}get isArangoAnalyzer(){return!0}get name(){return this._name}async exists(){try{return await this.get(),!0}catch(t){if((0,n.tv)(t)&&t.errorNum===o.KN)return!1;throw t}}get(){return this._db.request({path:`/_api/analyzer/${encodeURIComponent(this._name)}`})}create(t){return this._db.request({method:"POST",path:"/_api/analyzer",body:{name:this._name,...t}})}drop(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return this._db.request({method:"DELETE",path:`/_api/analyzer/${encodeURIComponent(this._name)}`,qs:{force:t}})}}},153:function(t,e,r){"use strict";r.r(e),r.d(e,{aql:function(){return c},isAqlLiteral:function(){return u},isAqlQuery:function(){return a},isGeneratedAqlQuery:function(){return h},join:function(){return d},literal:function(){return l}});var n=r(357),o=r(268),i=r(751),s=r(782);function a(t){return Boolean(t&&"string"==typeof t.query&&t.bindVars)}function h(t){return a(t)&&"function"==typeof t._source}function u(t){return Boolean(t&&"function"==typeof t.toAQL)}function c(t){for(var e=arguments.length,r=new Array(e>1?e-1:0),a=1;a<e;a++)r[a-1]=arguments[a];const c=[...t],l={},d=[];let p=c[0];for(let t=0;t<r.length;t++){const e=r[t];let a=e;if(h(e)){const n=e._source();n.args.length?(p+=n.strings[0],r.splice(t,1,...n.args),c.splice(t,2,c[t]+n.strings[0],...n.strings.slice(1,n.args.length),n.strings[n.args.length]+c[t+1])):(p+=e.query+c[t+1],r.splice(t,1),c.splice(t,2,c[t]+e.query+c[t+1])),t-=1;continue}if(void 0===e){p+=c[t+1];continue}if(u(e)){p+=`${e.toAQL()}${c[t+1]}`;continue}const m=d.indexOf(e),f=-1!==m;let y=`value${f?m:d.length}`;((0,o.isArangoCollection)(e)||(0,i.ZG)(e)||(0,s.isArangoView)(e)||(0,n.P)(e))&&(y=`@${y}`,a=e.name),f||(d.push(e),l[y]=a),p+=`@${y}${c[t+1]}`}return{query:p,bindVars:l,_source:()=>({strings:c,args:r})}}function l(t){return u(t)?t:{toAQL(){return void 0===t?"":String(t)}}}function d(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:" ";return t.length?1===t.length?c`${t[0]}`:c(["",...Array(t.length-1).fill(e),""],...t):c``}},268:function(t,e,r){"use strict";r.r(e),r.d(e,{Collection:function(){return p},CollectionStatus:function(){return d},CollectionType:function(){return l},collectionToString:function(){return c},isArangoCollection:function(){return u}});var n=r(153),o=r(989),i=r(289),s=r(489);function a(t,e){if("string"!=typeof t){if(t.id)return a(t.id,e);throw new Error("Index handle must be a string or an object with an id attribute")}if(t.includes("/")){const[r,...n]=t.split("/"),o=r.normalize("NFC");if(o!==e)throw new Error(`Index ID "${t}" does not match collection name "${e}"`);return[o,t=n.join("/").normalize("NFC")].join("/")}return`${e}/${String(t).normalize("NFC")}`}var h=r(625);function u(t){return Boolean(t&&t.isArangoCollection)}function c(t){return u(t)?String(t.name):String(t).normalize("NFC")}let l=function(t){return t[t.DOCUMENT_COLLECTION=2]="DOCUMENT_COLLECTION",t[t.EDGE_COLLECTION=3]="EDGE_COLLECTION",t}({}),d=function(t){return t[t.NEWBORN=1]="NEWBORN",t[t.UNLOADED=2]="UNLOADED",t[t.LOADED=3]="LOADED",t[t.UNLOADING=4]="UNLOADING",t[t.DELETED=5]="DELETED",t[t.LOADING=6]="LOADING",t}({});class p{constructor(t,e){this._name=e.normalize("NFC"),this._db=t}get isArangoCollection(){return!0}get name(){return this._name}get(){return this._db.request({path:`/_api/collection/${encodeURIComponent(this._name)}`})}async exists(){try{return await this.get(),!0}catch(t){if((0,s.tv)(t)&&t.errorNum===h.sJ)return!1;throw t}}create(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{waitForSyncReplication:e,enforceReplicationFactor:r,...o}=t;o.computedValues&&(o.computedValues=o.computedValues.map((t=>(0,n.isAqlLiteral)(t.expression)?{...t,expression:t.expression.toAQL()}:(0,n.isAqlQuery)(t.expression)?{...t,expression:t.expression.query}:t)));const i={};return"boolean"==typeof e&&(i.waitForSyncReplication=e?1:0),"boolean"==typeof r&&(i.enforceReplicationFactor=r?1:0),this._db.request({method:"POST",path:"/_api/collection",qs:i,body:{...o,name:this._name}})}properties(t){return t?this._db.request({method:"PUT",path:`/_api/collection/${encodeURIComponent(this._name)}/properties`,body:t}):this._db.request({path:`/_api/collection/${encodeURIComponent(this._name)}/properties`})}count(){return this._db.request({path:`/_api/collection/${encodeURIComponent(this._name)}/count`})}async recalculateCount(){return this._db.request({method:"PUT",path:`/_api/collection/${encodeURIComponent(this._name)}/recalculateCount`},(t=>t.body.result))}figures(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return this._db.request({path:`/_api/collection/${encodeURIComponent(this._name)}/figures`,qs:{details:t}})}revision(){return this._db.request({path:`/_api/collection/${encodeURIComponent(this._name)}/revision`})}checksum(t){return this._db.request({path:`/_api/collection/${encodeURIComponent(this._name)}/checksum`,qs:t})}async loadIndexes(){return this._db.request({method:"PUT",path:`/_api/collection/${encodeURIComponent(this._name)}/loadIndexesIntoMemory`},(t=>t.body.result))}async rename(t){const e=await this._db.renameCollection(this._name,t);return this._name=t.normalize("NFC"),e}truncate(){return this._db.request({method:"PUT",path:`/_api/collection/${this._name}/truncate`})}drop(t){return this._db.request({method:"DELETE",path:`/_api/collection/${encodeURIComponent(this._name)}`,qs:t})}getResponsibleShard(t){return this._db.request({method:"PUT",path:`/_api/collection/${encodeURIComponent(this._name)}/responsibleShard`,body:t},(t=>t.body.shardId))}documentId(t){return(0,i.b)(t,this._name)}async documentExists(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{ifMatch:r,ifNoneMatch:n}=e,o={};r&&(o["if-match"]=r),n&&(o["if-none-match"]=n);try{return await this._db.request({method:"HEAD",path:`/_api/document/${encodeURI((0,i.b)(t,this._name))}`,headers:o},(t=>{if(n&&304===t.statusCode)throw new s.j$(t);return!0}))}catch(t){if(404===t.code)return!1;throw t}}documents(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{allowDirtyRead:r}=e;return this._db.request({method:"PUT",path:`/_api/document/${encodeURIComponent(this._name)}`,qs:{onlyget:!0},allowDirtyRead:r,body:t})}async document(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};"boolean"==typeof e&&(e={graceful:e});const{allowDirtyRead:r,graceful:n=!1,ifMatch:o,ifNoneMatch:a}=e,u={};o&&(u["if-match"]=o),a&&(u["if-none-match"]=a);const c=this._db.request({path:`/_api/document/${encodeURI((0,i.b)(t,this._name))}`,headers:u,allowDirtyRead:r},(t=>{if(a&&304===t.statusCode)throw new s.j$(t);return t.body}));if(!n)return c;try{return await c}catch(t){if((0,s.tv)(t)&&t.errorNum===h.lI)return null;throw t}}save(t,e){return this._db.request({method:"POST",path:`/_api/document/${encodeURIComponent(this._name)}`,body:t,qs:e},(t=>e?.silent?void 0:t.body))}saveAll(t,e){return this._db.request({method:"POST",path:`/_api/document/${encodeURIComponent(this._name)}`,body:t,qs:e},(t=>e?.silent?void 0:t.body))}replace(t,e){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const{ifMatch:n,...o}=r,s={};return n&&(s["if-match"]=n),this._db.request({method:"PUT",path:`/_api/document/${encodeURI((0,i.b)(t,this._name))}`,headers:s,body:e,qs:o},(t=>r?.silent?void 0:t.body))}replaceAll(t,e){return this._db.request({method:"PUT",path:`/_api/document/${encodeURIComponent(this._name)}`,body:t,qs:e},(t=>e?.silent?void 0:t.body))}update(t,e){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const{ifMatch:n,...o}=r,s={};return n&&(s["if-match"]=n),this._db.request({method:"PATCH",path:`/_api/document/${encodeURI((0,i.b)(t,this._name))}`,headers:s,body:e,qs:o},(t=>r?.silent?void 0:t.body))}updateAll(t,e){return this._db.request({method:"PATCH",path:`/_api/document/${encodeURIComponent(this._name)}`,body:t,qs:e},(t=>e?.silent?void 0:t.body))}remove(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{ifMatch:r,...n}=e,o={};return r&&(o["if-match"]=r),this._db.request({method:"DELETE",path:`/_api/document/${encodeURI((0,i.b)(t,this._name))}`,headers:o,qs:n},(t=>e?.silent?void 0:t.body))}removeAll(t,e){return this._db.request({method:"DELETE",path:`/_api/document/${encodeURIComponent(this._name)}`,body:t,qs:e},(t=>e?.silent?void 0:t.body))}import(t){const e={...arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},collection:this._name};return Array.isArray(t)&&(e.type=Array.isArray(t[0])?void 0:"documents",t=t.map((t=>JSON.stringify(t))).join("\r\n")+"\r\n"),this._db.request({method:"POST",path:"/_api/import",body:t,isBinary:!0,qs:e})}_edges(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2?arguments[2]:void 0;const{allowDirtyRead:n}=e;return this._db.request({path:`/_api/edges/${encodeURIComponent(this._name)}`,allowDirtyRead:n,qs:{direction:r,vertex:(0,i.b)(t,this._name,!1)}})}edges(t,e){return this._edges(t,e)}inEdges(t,e){return this._edges(t,e,"in")}outEdges(t,e){return this._edges(t,e,"out")}traversal(t,e){return this._db.request({method:"POST",path:"/_api/traversal",body:{...e,startVertex:t,edgeCollection:this._name}},(t=>t.body.result))}list(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"id";return this._db.request({method:"PUT",path:"/_api/simple/all-keys",body:{type:t,collection:this._name}},(t=>new o.F(this._db,t.body,t.arangojsHostUrl).items))}all(t){return this._db.request({method:"PUT",path:"/_api/simple/all",body:{...t,collection:this._name}},(t=>new o.F(this._db,t.body,t.arangojsHostUrl).items))}any(){return this._db.request({method:"PUT",path:"/_api/simple/any",body:{collection:this._name}},(t=>t.body.document))}byExample(t,e){return this._db.request({method:"PUT",path:"/_api/simple/by-example",body:{...e,example:t,collection:this._name}},(t=>new o.F(this._db,t.body,t.arangojsHostUrl).items))}firstExample(t){return this._db.request({method:"PUT",path:"/_api/simple/first-example",body:{example:t,collection:this._name}},(t=>t.body.document))}removeByExample(t,e){return this._db.request({method:"PUT",path:"/_api/simple/remove-by-example",body:{...e,example:t,collection:this._name}})}replaceByExample(t,e,r){return this._db.request({method:"PUT",path:"/_api/simple/replace-by-example",body:{...r,example:t,newValue:e,collection:this._name}})}updateByExample(t,e,r){return this._db.request({method:"PUT",path:"/_api/simple/update-by-example",body:{...r,example:t,newValue:e,collection:this._name}})}lookupByKeys(t){return this._db.request({method:"PUT",path:"/_api/simple/lookup-by-keys",body:{keys:t,collection:this._name}},(t=>t.body.documents))}removeByKeys(t,e){return this._db.request({method:"PUT",path:"/_api/simple/remove-by-keys",body:{options:e,keys:t,collection:this._name}})}indexes(){return this._db.request({path:"/_api/index",qs:{collection:this._name}},(t=>t.body.indexes))}index(t){return this._db.request({path:`/_api/index/${encodeURI(a(t,this._name))}`})}ensureIndex(t){const e={...t};return e.name&&(e.name=e.name.normalize("NFC")),this._db.request({method:"POST",path:"/_api/index",body:t,qs:{collection:this._name}})}dropIndex(t){return this._db.request({method:"DELETE",path:`/_api/index/${encodeURI(a(t,this._name))}`})}fulltext(t,e){let{index:r,...n}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this._db.request({method:"PUT",path:"/_api/simple/fulltext",body:{...n,index:r?a(r,this._name):void 0,attribute:t,query:e,collection:this._name}},(t=>new o.F(this._db,t.body,t.arangojsHostUrl).items))}compact(){return this._db.request({method:"PUT",path:`/_api/collection/${this._name}/compact`},(t=>t.body))}}},989:function(t,e,r){"use strict";r.d(e,{F:function(){return o}});var n=r(462);class o{constructor(t,e,r,o){const s=new n.LinkedList(e.result.length?[new n.LinkedList(e.result)]:[]);this._db=t,this._batches=s,this._id=e.id,this._hasMore=Boolean(e.id&&e.hasMore),this._nextBatchId=e.nextBatchId,this._hostUrl=r,this._count=e.count,this._extra=e.extra,this._allowDirtyRead=o,this._itemsCursor=new i(this,{get isEmpty(){return!s.length},more:()=>this._more(),shift:()=>{let t=s.first?.value;for(;t&&!t.length;)s.shift(),t=s.first?.value;if(!t)return;const e=t.shift();return t.length||s.shift(),e}})}async _more(){if(!this._id||!this.hasMore)return;const t=await this._db.request({method:"POST",path:this._nextBatchId?`/_api/cursor/${encodeURIComponent(this._id)}/${this._nextBatchId}`:`/_api/cursor/${encodeURIComponent(this._id)}`,hostUrl:this._hostUrl,allowDirtyRead:this._allowDirtyRead});this._batches.push(new n.LinkedList(t.result)),this._hasMore=t.hasMore,this._nextBatchId=t.nextBatchId}get items(){return this._itemsCursor}get extra(){return this._extra}get count(){return this._count}get hasMore(){return this._hasMore}get hasNext(){return this.hasMore||Boolean(this._batches.length)}async*[Symbol.asyncIterator](){for(;this.hasNext;)yield this.next()}async loadAll(){for(;this._hasMore;)await this._more()}async all(){return this.map((t=>t))}async next(){for(;!this._batches.length&&this.hasNext;)await this._more();if(!this._batches.length)return;const t=this._batches.shift();if(!t)return;const e=[...t.values()];return t.clear(!0),e}async forEach(t){let e=0;for(;this.hasNext;){const r=t(await this.next(),e,this);if(e++,!1===r)return r;this.hasNext&&await this._more()}return!0}async map(t){let e=0;const r=[];for(;this.hasNext;){const n=await this.next();r.push(t(n,e,this)),e++}return r}async flatMap(t){let e=0;const r=[];for(;this.hasNext;){const n=t(await this.next(),e,this);Array.isArray(n)?r.push(...n):r.push(n),e++}return r}async reduce(t,e){let r=0;if(!this.hasNext)return e;void 0===e&&(e=await this.next(),r+=1);let n=e;for(;this.hasNext;)n=t(n,await this.next(),r,this),r++;return n}async kill(){if(this._batches.length){for(const t of this._batches.values())t.clear();this._batches.clear()}if(this.hasNext)return this._db.request({method:"DELETE",path:`/_api/cursor/${encodeURIComponent(this._id)}`},(()=>{this._hasMore=!1}))}}class i{constructor(t,e){this._batches=t,this._view=e}get batches(){return this._batches}get extra(){return this.batches.extra}get count(){return this.batches.count}get hasNext(){return this.batches.hasNext}async*[Symbol.asyncIterator](){for(;this.hasNext;)yield this.next()}async all(){return this.batches.flatMap((t=>t))}async next(){for(;this._view.isEmpty&&this.batches.hasMore;)await this._view.more();if(!this._view.isEmpty)return this._view.shift()}async forEach(t){let e=0;for(;this.hasNext;){const r=t(await this.next(),e,this);if(e++,!1===r)return r}return!0}async map(t){let e=0;const r=[];for(;this.hasNext;){const n=await this.next();r.push(t(n,e,this)),e++}return r}async flatMap(t){let e=0;const r=[];for(;this.hasNext;){const n=t(await this.next(),e,this);Array.isArray(n)?r.push(...n):r.push(n),e++}return r}async reduce(t,e){let r=0;if(!this.hasNext)return e;void 0===e&&(e=await this.next(),r+=1);let n=e;for(;this.hasNext;)n=t(n,await this.next(),r,this),r++;return n}async kill(){return this.batches.kill()}}},296:function(t,e,r){"use strict";r.r(e),r.d(e,{Database:function(){return R},LogLevel:function(){return U},isArangoDatabase:function(){return T}});var n=r(357),o=r(153),i=r(268),s=r(462),a=r(489);function h(t){return btoa(t)}var u=r(625);function c(t){const e=t.match(/^(tcp|ssl|tls)((?::|\+).+)/);e&&(t=("tcp"===e[1]?"http":"https")+e[2]);const r=t.match(/^(?:(http|https)\+)?unix:\/\/(\/.+)/);return r&&(t=`${r[1]||"http"}://unix:${r[2]}`),t}var l=r(558);function d(){return{error:!0,message:this.message}}function p(t,e){const r=new URL(t),n=h(`${r.username||"root"}:${r.password}`);r.username="",r.password="";const o=function(t,e){const r={};for(const n of Object.keys(t))e.includes(n)||(r[n]=t[n]);return r}(e,["maxSockets"]);return function(t,e){let{method:i,url:s,headers:a,body:h,timeout:u,expectBinary:c}=t;const p=new URL(s.pathname,r);s.search?r.search?p.search=`${r.search}&${s.search.slice(1)}`:p.search=s.search.slice(1):p.search=r.search,a.authorization||(a.authorization=`Basic ${n}`);let m=(t,r)=>{m=()=>{},e(t,r)};const f=l({useXDR:!0,withCredentials:!0,...o,responseType:c?"blob":"text",url:String(p),body:h,method:i,headers:a,timeout:u},((t,e)=>{if(t){const e=t;e.request=f,e.toJSON=d,o.after&&o.after(e),m(e)}else{const t=e;t.request=f,t.body||(t.body=""),o.after&&o.after(null,t),m(null,t)}}));o.before&&o.before(f)}}const m=/\/(json|javascript)(\W|$)/,f="x-arango-endpoint";class y{_activeTasks=0;_arangoVersion=30900;_queue=new s.LinkedList;_databases=new Map;_hosts=[];_hostUrls=[];_transactionId=null;_queueTimes=new s.LinkedList;constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const e=t.url?Array.isArray(t.url)?t.url:[t.url]:["http://127.0.0.1:8529"],r=3*("ROUND_ROBIN"===t.loadBalancingStrategy?e.length:1);void 0!==t.arangoVersion&&(this._arangoVersion=t.arangoVersion),this._agent=t.agent,this._agentOptions={maxSockets:r,...t.agentOptions},this._maxTasks=this._agentOptions.maxSockets,this._headers={...t.headers},this._loadBalancingStrategy=t.loadBalancingStrategy??"NONE",this._precaptureStackTraces=Boolean(t.precaptureStackTraces),this._responseQueueTimeSamples=t.responseQueueTimeSamples??10,this._retryOnConflict=t.retryOnConflict??0,this._responseQueueTimeSamples<0&&(this._responseQueueTimeSamples=1/0),!1===t.maxRetries?this._maxRetries=!1:this._maxRetries=Number(t.maxRetries??0),this.addToHostList(e),t.auth&&(t.auth.hasOwnProperty("token")?this.setBearerAuth(t.auth):this.setBasicAuth(t.auth)),"ONE_RANDOM"===this._loadBalancingStrategy?(this._activeHostUrl=this._hostUrls[Math.floor(Math.random()*this._hostUrls.length)],this._activeDirtyHostUrl=this._hostUrls[Math.floor(Math.random()*this._hostUrls.length)]):(this._activeHostUrl=this._hostUrls[0],this._activeDirtyHostUrl=this._hostUrls[0])}get isArangoConnection(){return!0}get queueTime(){return{getLatest:()=>this._queueTimes.last?.value[1],getValues:()=>Array.from(this._queueTimes.values()),getAvg:()=>{let t=0;for(const[,[,e]]of this._queueTimes)t+=e/this._queueTimes.length;return t}}}_runQueue(){if(!this._queue.length||this._activeTasks>=this._maxTasks)return;const t=this._queue.shift();let e=this._activeHostUrl;void 0!==t.hostUrl?e=t.hostUrl:t.allowDirtyRead?(e=this._activeDirtyHostUrl,this._activeDirtyHostUrl=this._hostUrls[(this._hostUrls.indexOf(this._activeDirtyHostUrl)+1)%this._hostUrls.length],t.options.headers["x-arango-allow-dirty-read"]="true"):"ROUND_ROBIN"===this._loadBalancingStrategy&&(this._activeHostUrl=this._hostUrls[(this._hostUrls.indexOf(this._activeHostUrl)+1)%this._hostUrls.length]),this._activeTasks+=1;const r=(n,o)=>{if(this._activeTasks-=1,!n&&o)if(503===o.statusCode&&o.headers[f]){const r=o.headers[f],[n]=this.addToHostList(r);t.hostUrl=n,this._activeHostUrl===e&&(this._activeHostUrl=n),this._queue.push(t)}else{o.arangojsHostUrl=e;const i=o.headers["content-type"],s=o.headers["x-arango-queue-time-seconds"];if(s)for(this._queueTimes.push([Date.now(),Number(s)]);this._responseQueueTimeSamples<this._queueTimes.length;)this._queueTimes.shift();let h;if(o.body.length&&i&&i.match(m))try{h=o.body,h=JSON.parse(h)}catch(e){if(!t.options.expectBinary)return"string"!=typeof h&&(h=o.body.toString("utf-8")),e.res=o,t.stack&&(e.stack+=t.stack()),void r(e)}else h=o.body&&!t.options.expectBinary?o.body.toString("utf-8"):o.body;(0,a.Ml)(h)?(o.body=h,n=new a.b4(o)):o.statusCode&&o.statusCode>=400?(o.body=h,n=new a.j$(o)):(t.options.expectBinary||(o.body=h),t.resolve(t.transform?t.transform(o):o))}n&&(!t.allowDirtyRead&&this._hosts.length>1&&this._activeHostUrl===e&&"ROUND_ROBIN"!==this._loadBalancingStrategy&&(this._activeHostUrl=this._hostUrls[(this._hostUrls.indexOf(this._activeHostUrl)+1)%this._hostUrls.length]),(0,a.tv)(n)&&n.errorNum===u.RN&&t.retryOnConflict>0?(t.retryOnConflict-=1,this._queue.push(t)):((0,a.Or)(n)&&"connect"===n.syscall&&"ECONNREFUSED"===n.code||(0,a.tv)(n)&&n.errorNum===u.Qc)&&void 0===t.hostUrl&&!1!==this._maxRetries&&t.retries<(this._maxRetries||this._hosts.length-1)?(t.retries+=1,this._queue.push(t)):(t.stack&&(n.stack+=t.stack()),t.reject(n))),this._runQueue()};try{this._hosts[this._hostUrls.indexOf(e)](t.options,r)}catch(t){r(t)}}_buildUrl(t){let{basePath:e,path:r,qs:n}=t;const o=`${e||""}${r||""}`;let i;return n&&(i="string"==typeof n?`?${n}`:`?${function(t){let e="";for(let[r,n]of Object.entries(t))if(void 0!==n)if(r=encodeURIComponent(r),Array.isArray(n))for(let t of n)t=null==t?"":encodeURIComponent(String(t)),e+=`&${r}=${t}`;else n=null===n?"":encodeURIComponent(String(n)),e+=`&${r}=${n}`;return e.slice(1)}(n)}`),i?{pathname:o,search:i}:{pathname:o}}setBearerAuth(t){this.setHeader("authorization",`Bearer ${t.token}`)}setBasicAuth(t){this.setHeader("authorization",`Basic ${h(`${t.username}:${t.password}`)}`)}setResponseQueueTimeSamples(t){for(t<0&&(t=1/0),this._responseQueueTimeSamples=t;this._responseQueueTimeSamples<this._queueTimes.length;)this._queueTimes.shift()}database(t,e){if(null!==e)return e?(this._databases.set(t,e),e):this._databases.get(t);this._databases.delete(t)}setHostList(t){const e=t.map((t=>c(t)));this._hosts.splice(0,this._hosts.length,...e.map((t=>{const e=this._hostUrls.indexOf(t);return-1!==e?this._hosts[e]:p(t,this._agentOptions,this._agent)}))),this._hostUrls.splice(0,this._hostUrls.length,...e)}addToHostList(t){const e=(Array.isArray(t)?t:[t]).map((t=>c(t))),r=e.filter((t=>-1===this._hostUrls.indexOf(t)));return this._hostUrls.push(...r),this._hosts.push(...r.map((t=>p(t,this._agentOptions,this._agent)))),e}setTransactionId(t){this._transactionId=t}clearTransactionId(){this._transactionId=null}setHeader(t,e){null===e?delete this._headers[t]:this._headers[t]=e}close(){for(const t of this._hosts)t.close&&t.close()}async waitForPropagation(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1/0;const r=this._hosts.length,n=[],o=Date.now();let i=0;for(;;){if(n.length===r)return;for(;n.includes(this._hostUrls[i]);)i=(i+1)%r;const s=this._hostUrls[i];try{await this.request({...t,hostUrl:s})}catch(t){if(o+e<Date.now())throw t;await new Promise((t=>setTimeout(t,1e3)));continue}n.includes(s)||n.push(s)}}request(t,e){let{hostUrl:r,method:n="GET",body:o,expectBinary:i=!1,isBinary:s=!1,allowDirtyRead:a=!1,retryOnConflict:h=this._retryOnConflict,timeout:u=0,headers:c,...l}=t;return new Promise(((t,d)=>{let p="text/plain";s?p="application/octet-stream":o&&("object"==typeof o?(o=JSON.stringify(o),p="application/json"):o=String(o));const m={...this._headers,"content-type":p,"x-arango-version":String(this._arangoVersion),"x-arango-driver":"arangojs/8.8.1 (cloud)"};this._transactionId&&(m["x-arango-trx-id"]=this._transactionId);const f={retries:0,hostUrl:r,allowDirtyRead:a,retryOnConflict:h,options:{url:this._buildUrl(l),headers:{...m,...c},timeout:u,method:n,expectBinary:i,body:o},reject:d,resolve:t,transform:e};if(this._precaptureStackTraces)if("function"==typeof Error.captureStackTrace){const t={};Error.captureStackTrace(t),f.stack=()=>`\n${t.stack.split("\n").slice(3).join("\n")}`}else{const t=function(){let t=new Error;if(!t.stack)try{throw t}catch(e){t=e}return t}();Object.prototype.hasOwnProperty.call(t,"stack")&&(f.stack=()=>`\n${t.stack.split("\n").slice(4).join("\n")}`)}this._queue.push(f),this._runQueue()}))}}var _=r(989),g=r(751);class b{_loaded=!1;constructor(t,e,r,n){this._db=t,this._id=e,this._transformResponse=r,this._transformError=n}get isLoaded(){return this._loaded}get result(){return this._result}async load(){if(!this.isLoaded){let t;try{t=await this._db.request({method:"PUT",path:`/_api/job/${this._id}`},!1)}catch(t){if(this._transformError)return this._transformError(t);throw t}204!==t.statusCode&&(this._loaded=!0,this._transformResponse?this._result=await this._transformResponse(t):this._result=t.body)}return this._result}cancel(){return this._db.request({method:"PUT",path:`/_api/job/${this._id}/cancel`},(()=>{}))}deleteResult(){return this._db.request({method:"DELETE",path:`/_api/job/${this._id}`},(()=>{}))}getCompleted(){return this._db.request({path:`/_api/job/${this._id}`},(t=>204!==t.statusCode))}}function v(t,e){let r;try{r=new FormData;for(const e of Object.keys(t)){let n=t[e];void 0!==n&&(n instanceof Blob||"object"!=typeof n&&"function"!=typeof n||(n=JSON.stringify(n)),r.append(e,n))}}catch(t){return void e(t)}e(null,{body:r})}class w{constructor(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};e?"/"!==e.charAt(0)&&(e=`/${e}`):e="",this._db=t,this._path=e,this._headers=r}route(t,e){return t?"/"!==t.charAt(0)&&(t=`/${t}`):t="",new w(this._db,this._path+t,{...this._headers,...e})}request(t){const e={...t};return e.path&&"/"!==e.path?this._path&&"/"!==e.path.charAt(0)?e.path=`/${e.path}`:e.path=e.path:e.path="",e.basePath=this._path,e.headers={...this._headers,...e.headers},e.method=e.method?e.method.toUpperCase():"GET",this._db.request(e,!1)}delete(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];const n="string"==typeof e[0]?e.shift():void 0,[o,i]=e;return this.request({method:"DELETE",path:n,qs:o,headers:i})}get(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];const n="string"==typeof e[0]?e.shift():void 0,[o,i]=e;return this.request({method:"GET",path:n,qs:o,headers:i})}head(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];const n="string"==typeof e[0]?e.shift():void 0,[o,i]=e;return this.request({method:"HEAD",path:n,qs:o,headers:i})}patch(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];const n="string"==typeof e[0]?e.shift():void 0,[o,i,s]=e;return this.request({method:"PATCH",path:n,body:o,qs:i,headers:s})}post(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];const n="string"==typeof e[0]?e.shift():void 0,[o,i,s]=e;return this.request({method:"POST",path:n,body:o,qs:i,headers:s})}put(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];const n="string"==typeof e[0]?e.shift():void 0,[o,i,s]=e;return this.request({method:"PUT",path:n,body:o,qs:i,headers:s})}}class q{constructor(t,e){this._db=t,this._id=e}get isArangoTransaction(){return!0}get id(){return this._id}async exists(){try{return await this.get(),!0}catch(t){if((0,a.tv)(t)&&t.errorNum===u.U)return!1;throw t}}get(){return this._db.request({path:`/_api/transaction/${encodeURIComponent(this.id)}`},(t=>t.body.result))}commit(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{allowDirtyRead:e}=t;return this._db.request({method:"PUT",path:`/_api/transaction/${encodeURIComponent(this.id)}`,allowDirtyRead:e},(t=>t.body.result))}abort(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{allowDirtyRead:e}=t;return this._db.request({method:"DELETE",path:`/_api/transaction/${encodeURIComponent(this.id)}`,allowDirtyRead:e},(t=>t.body.result))}step(t){const e=this._db._connection;e.setTransactionId(this.id);try{const e=t();if(!e)throw new Error("Transaction callback was not an async function or did not return a promise!");return Promise.resolve(e)}finally{e.clearTransactionId()}}}var x=r(782);function T(t){return Boolean(t&&t.isArangoDatabase)}function C(t){if("string"==typeof t)return{write:[t]};if(Array.isArray(t))return{write:t.map(i.collectionToString)};if((0,i.isArangoCollection)(t))return{write:(0,i.collectionToString)(t)};const e={};return t&&(void 0!==t.allowImplicit&&(e.allowImplicit=t.allowImplicit),t.read&&(e.read=Array.isArray(t.read)?t.read.map(i.collectionToString):(0,i.collectionToString)(t.read)),t.write&&(e.write=Array.isArray(t.write)?t.write.map(i.collectionToString):(0,i.collectionToString)(t.write)),t.exclusive&&(e.exclusive=Array.isArray(t.exclusive)?t.exclusive.map(i.collectionToString):(0,i.collectionToString)(t.exclusive))),e}let U=function(t){return t[t.FATAL=0]="FATAL",t[t.ERROR=1]="ERROR",t[t.WARNING=2]="WARNING",t[t.INFO=3]="INFO",t[t.DEBUG=4]="DEBUG",t}({});class R{_analyzers=new Map;_collections=new Map;_graphs=new Map;_views=new Map;constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1?arguments[1]:void 0;if(T(t)){const r=t._connection,n=(e||t.name).normalize("NFC");this._connection=r,this._name=n;const o=r.database(n);if(o)return o}else{const r=t,{databaseName:n,...o}="string"==typeof r||Array.isArray(r)?{databaseName:e,url:r}:r;this._connection=new y(o),this._name=n?.normalize("NFC")||"_system"}}get isArangoDatabase(){return!0}get name(){return this._name}version(t){return this.request({method:"GET",path:"/_api/version",qs:{details:t}})}time(){return this.request({path:"/_admin/time"},(t=>1e3*t.body.time))}route(t,e){return new w(this,t,e)}async createJob(t){const e=new Promise((t=>{this._trapRequest=e=>t(e)})),r=t(),n=await e;if(n.error)return r;const{jobId:o,onResolve:i,onReject:s}=n;return new b(this,o,(t=>(i(t),r)),(t=>(s(t),r)))}async request(t){let{absolutePath:e=!1,basePath:r,...n}=t,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t=>t.body;if(e||(r=`/_db/${encodeURIComponent(this._name)}${r||""}`),this._trapRequest){const t=this._trapRequest;return this._trapRequest=void 0,new Promise((async(e,i)=>{const s={...n};let a;s.headers={...s.headers,"x-arango-async":"store"};try{a=await this._connection.request({basePath:r,...s})}catch(e){return t({error:!0}),void i(e)}const h=a.headers["x-arango-async-id"];t({jobId:h,onResolve:t=>{const r=o?o(t):t;return e(r),r},onReject:t=>{throw i(t),t}})}))}return this._connection.request({basePath:r,...n},o||void 0)}async acquireHostList(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];const e=await this.request({path:"/_api/cluster/endpoints"},(t=>t.body.endpoints.map((t=>t.endpoint))));e.length>0&&(t?this._connection.setHostList(e):this._connection.addToHostList(e))}close(){this._connection.close()}shutdown(){return this.request({method:"DELETE",path:"/_admin/shutdown"},(()=>{}))}async waitForPropagation(t,e){let{basePath:r,...n}=t;await this._connection.waitForPropagation({...n,basePath:`/_db/${encodeURIComponent(this._name)}${r||""}`},e)}get queueTime(){return this._connection.queueTime}setResponseQueueTimeSamples(t){this._connection.setResponseQueueTimeSamples(t)}useBasicAuth(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"root",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return this._connection.setBasicAuth({username:t,password:e}),this}useBearerAuth(t){return this._connection.setBearerAuth({token:t}),this}login(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"root",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return this.request({method:"POST",path:"/_open/auth",body:{username:t,password:e}},(t=>(this.useBearerAuth(t.body.jwt),t.body.jwt)))}renewAuthToken(){return this.request({method:"POST",path:"/_open/auth/renew"},(t=>t.body.jwt?(this.useBearerAuth(t.body.jwt),t.body.jwt):null))}getClusterImbalance(){return this.request({path:"/_admin/cluster/rebalance"},(t=>t.body.result))}computeClusterRebalance(t){return this.request({method:"POST",path:"/_admin/cluster/rebalance",body:{version:1,...t}},(t=>t.body.result))}executeClusterRebalance(t){return this.request({method:"POST",path:"/_admin/cluster/rebalance/execute",body:{version:1,moves:t}})}rebalanceCluster(t){return this.request({method:"PUT",path:"/_admin/cluster/rebalance",body:{version:1,...t}})}database(t){return new R(this,t)}get(){return this.request({path:"/_api/database/current"},(t=>t.body.result))}async exists(){try{return await this.get(),!0}catch(t){if((0,a.tv)(t)&&t.errorNum===u.rZ)return!1;throw t}}createDatabase(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{users:r,...n}=Array.isArray(e)?{users:e}:e;return this.request({method:"POST",path:"/_api/database",body:{name:t.normalize("NFC"),users:r,options:n}},(()=>this.database(t)))}listDatabases(){return this.request({path:"/_api/database"},(t=>t.body.result))}listUserDatabases(){return this.request({path:"/_api/database/user"},(t=>t.body.result))}databases(){return this.request({path:"/_api/database"},(t=>t.body.result.map((t=>this.database(t)))))}userDatabases(){return this.request({path:"/_api/database/user"},(t=>t.body.result.map((t=>this.database(t)))))}dropDatabase(t){return t=t.normalize("NFC"),this.request({method:"DELETE",path:`/_api/database/${encodeURIComponent(t)}`},(t=>t.body.result))}collection(t){return t=t.normalize("NFC"),this._collections.has(t)||this._collections.set(t,new i.Collection(this,t)),this._collections.get(t)}async createCollection(t,e){const r=this.collection(t);return await r.create(e),r}async createEdgeCollection(t,e){return this.createCollection(t,{...e,type:i.CollectionType.EDGE_COLLECTION})}async renameCollection(t,e){t=t.normalize("NFC");const r=await this.request({method:"PUT",path:`/_api/collection/${encodeURIComponent(t)}/rename`,body:{name:e.normalize("NFC")}});return this._collections.delete(t),r}listCollections(){let t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return this.request({path:"/_api/collection",qs:{excludeSystem:t}},(t=>t.body.result))}async collections(){let t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return(await this.listCollections(t)).map((t=>this.collection(t.name)))}graph(t){return t=t.normalize("NFC"),this._graphs.has(t)||this._graphs.set(t,new g.TS(this,t)),this._graphs.get(t)}async createGraph(t,e,r){const n=this.graph(t.normalize("NFC"));return await n.create(e,r),n}listGraphs(){return this.request({path:"/_api/gharial"},(t=>t.body.graphs))}async graphs(){return(await this.listGraphs()).map((t=>this.graph(t._key)))}view(t){return t=t.normalize("NFC"),this._views.has(t)||this._views.set(t,new x.View(this,t)),this._views.get(t)}async createView(t,e){const r=this.view(t.normalize("NFC"));return await r.create(e),r}async renameView(t,e){t=t.normalize("NFC");const r=await this.request({method:"PUT",path:`/_api/view/${encodeURIComponent(t)}/rename`,body:{name:e.normalize("NFC")}});return this._views.delete(t),r}listViews(){return this.request({path:"/_api/view"},(t=>t.body.result))}async views(){return(await this.listViews()).map((t=>this.view(t.name)))}analyzer(t){return t=t.normalize("NFC"),this._analyzers.has(t)||this._analyzers.set(t,new n.D(this,t)),this._analyzers.get(t)}async createAnalyzer(t,e){const r=this.analyzer(t);return await r.create(e),r}listAnalyzers(){return this.request({path:"/_api/analyzer"},(t=>t.body.result))}async analyzers(){return(await this.listAnalyzers()).map((t=>this.analyzer(t.name)))}listUsers(){return this.request({path:"/_api/user"},(t=>t.body.result))}getUser(t){return this.request({path:`/_api/user/${encodeURIComponent(t)}`})}createUser(t,e){return"string"==typeof e&&(e={passwd:e}),this.request({method:"POST",path:"/_api/user",body:{user:t,...e}},(t=>t.body))}updateUser(t,e){return"string"==typeof e&&(e={passwd:e}),this.request({method:"PATCH",path:`/_api/user/${encodeURIComponent(t)}`,body:e},(t=>t.body))}replaceUser(t,e){return"string"==typeof e&&(e={passwd:e}),this.request({method:"PUT",path:`/_api/user/${encodeURIComponent(t)}`,body:e},(t=>t.body))}removeUser(t){return this.request({method:"DELETE",path:`/_api/user/${encodeURIComponent(t)}`},(t=>t.body))}getUserAccessLevel(t,e){let{database:r,collection:n}=e;const o=T(r)?r.name:r?.normalize("NFC")??((0,i.isArangoCollection)(n)?n._db.name:this._name),s=n?`/${encodeURIComponent((0,i.isArangoCollection)(n)?n.name:n.normalize("NFC"))}`:"";return this.request({path:`/_api/user/${encodeURIComponent(t)}/database/${encodeURIComponent(o)}${s}`},(t=>t.body.result))}setUserAccessLevel(t,e){let{database:r,collection:n,grant:o}=e;const s=T(r)?r.name:r?.normalize("NFC")??((0,i.isArangoCollection)(n)?n._db.name:this._name),a=n?`/${encodeURIComponent((0,i.isArangoCollection)(n)?n.name:n.normalize("NFC"))}`:"";return this.request({method:"PUT",path:`/_api/user/${encodeURIComponent(t)}/database/${encodeURIComponent(s)}${a}`,body:{grant:o}},(t=>t.body))}clearUserAccessLevel(t,e){let{database:r,collection:n}=e;const o=T(r)?r.name:r?.normalize("NFC")??((0,i.isArangoCollection)(n)?n._db.name:this._name),s=n?`/${encodeURIComponent((0,i.isArangoCollection)(n)?n.name:n.normalize("NFC"))}`:"";return this.request({method:"DELETE",path:`/_api/user/${encodeURIComponent(t)}/database/${encodeURIComponent(o)}${s}`},(t=>t.body))}getUserDatabases(t,e){return this.request({path:`/_api/user/${encodeURIComponent(t)}/database`,qs:{full:e}},(t=>t.body.result))}executeTransaction(t,e){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const{allowDirtyRead:n,...o}=r;return this.request({method:"POST",path:"/_api/transaction",allowDirtyRead:n,body:{collections:C(t),action:e,...o}},(t=>t.body.result))}transaction(t){return new q(this,t)}beginTransaction(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{allowDirtyRead:r,...n}=e;return this.request({method:"POST",path:"/_api/transaction/begin",allowDirtyRead:r,body:{collections:C(t),...n}},(t=>new q(this,t.body.result.id)))}async withTransaction(t,e){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const n=await this.beginTransaction(t,r);try{const t=await e((t=>n.step(t)));return await n.commit(),t}catch(t){try{await n.abort()}catch{}throw t}}listTransactions(){return this._connection.request({path:"/_api/transaction"},(t=>t.body.transactions))}async transactions(){return(await this.listTransactions()).map((t=>this.transaction(t.id)))}query(t,e){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};(0,o.isAqlQuery)(t)?(r=e??{},e=t.bindVars,t=t.query):(0,o.isAqlLiteral)(t)&&(t=t.toAQL());const{allowDirtyRead:n,retryOnConflict:i,count:s,batchSize:a,cache:h,memoryLimit:u,ttl:c,timeout:l,...d}=r;return this.request({method:"POST",path:"/_api/cursor",body:{query:t,bindVars:e,count:s,batchSize:a,cache:h,memoryLimit:u,ttl:c,options:d},allowDirtyRead:n,retryOnConflict:i,timeout:l},(t=>new _.F(this,t.body,t.arangojsHostUrl,n).items))}explain(t,e,r){return(0,o.isAqlQuery)(t)?(r=e,e=t.bindVars,t=t.query):(0,o.isAqlLiteral)(t)&&(t=t.toAQL()),this.request({method:"POST",path:"/_api/explain",body:{query:t,bindVars:e,options:r}})}parse(t){return(0,o.isAqlQuery)(t)?t=t.query:(0,o.isAqlLiteral)(t)&&(t=t.toAQL()),this.request({method:"POST",path:"/_api/query",body:{query:t}})}queryRules(){return this.request({path:"/_api/query/rules"})}queryTracking(t){return this.request(t?{method:"PUT",path:"/_api/query/properties",body:t}:{method:"GET",path:"/_api/query/properties"})}listRunningQueries(){return this.request({method:"GET",path:"/_api/query/current"})}listSlowQueries(){return this.request({method:"GET",path:"/_api/query/slow"})}clearSlowQueries(){return this.request({method:"DELETE",path:"/_api/query/slow"},(()=>{}))}killQuery(t){return this.request({method:"DELETE",path:`/_api/query/${encodeURIComponent(t)}`},(()=>{}))}listFunctions(){return this.request({path:"/_api/aqlfunction"},(t=>t.body.result))}createFunction(t,e){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return this.request({method:"POST",path:"/_api/aqlfunction",body:{name:t,code:e,isDeterministic:r}})}dropFunction(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return this.request({method:"DELETE",path:`/_api/aqlfunction/${encodeURIComponent(t)}`,qs:{group:e}})}listServices(){let t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return this.request({path:"/_api/foxx",qs:{excludeSystem:t}})}async installService(t,e){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const{configuration:n,dependencies:o,...i}=r,s=await v({configuration:n,dependencies:o,source:e});return await this.request({...s,method:"POST",path:"/_api/foxx",isBinary:!0,qs:{...i,mount:t}})}async replaceService(t,e){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const{configuration:n,dependencies:o,...i}=r,s=await v({configuration:n,dependencies:o,source:e});return await this.request({...s,method:"PUT",path:"/_api/foxx/service",isBinary:!0,qs:{...i,mount:t}})}async upgradeService(t,e){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const{configuration:n,dependencies:o,...i}=r,s=await v({configuration:n,dependencies:o,source:e});return await this.request({...s,method:"PATCH",path:"/_api/foxx/service",isBinary:!0,qs:{...i,mount:t}})}uninstallService(t,e){return this.request({method:"DELETE",path:"/_api/foxx/service",qs:{...e,mount:t}},(()=>{}))}getService(t){return this.request({path:"/_api/foxx/service",qs:{mount:t}})}getServiceConfiguration(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return this.request({path:"/_api/foxx/configuration",qs:{mount:t,minimal:e}})}replaceServiceConfiguration(t,e){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return this.request({method:"PUT",path:"/_api/foxx/configuration",body:e,qs:{mount:t,minimal:r}})}updateServiceConfiguration(t,e){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return this.request({method:"PATCH",path:"/_api/foxx/configuration",body:e,qs:{mount:t,minimal:r}})}getServiceDependencies(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return this.request({path:"/_api/foxx/dependencies",qs:{mount:t,minimal:e}})}replaceServiceDependencies(t,e){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return this.request({method:"PUT",path:"/_api/foxx/dependencies",body:e,qs:{mount:t,minimal:r}})}updateServiceDependencies(t,e){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return this.request({method:"PATCH",path:"/_api/foxx/dependencies",body:e,qs:{mount:t,minimal:r}})}setServiceDevelopmentMode(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return this.request({method:e?"POST":"DELETE",path:"/_api/foxx/development",qs:{mount:t}})}listServiceScripts(t){return this.request({path:"/_api/foxx/scripts",qs:{mount:t}})}runServiceScript(t,e,r){return this.request({method:"POST",path:`/_api/foxx/scripts/${encodeURIComponent(e)}`,body:r,qs:{mount:t}})}runServiceTests(t,e){return this.request({method:"POST",path:"/_api/foxx/tests",qs:{...e,mount:t}})}getServiceReadme(t){return this.request({path:"/_api/foxx/readme",qs:{mount:t}})}getServiceDocumentation(t){return this.request({path:"/_api/foxx/swagger",qs:{mount:t}})}downloadService(t){return this.request({method:"POST",path:"/_api/foxx/download",qs:{mount:t},expectBinary:!0})}commitLocalServiceState(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return this.request({method:"POST",path:"/_api/foxx/commit",qs:{replace:t}},(()=>{}))}createHotBackup(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.request({method:"POST",path:"/_admin/backup/create",body:t},(t=>t.body.result))}listHotBackups(t){return this.request({method:"POST",path:"/_admin/backup/list",body:t?{id:t}:void 0},(t=>t.body.result))}restoreHotBackup(t){return this.request({method:"POST",path:"/_admin/backup/restore",body:{id:t}},(t=>t.body.result.previous))}deleteHotBackup(t){return this.request({method:"POST",path:"/_admin/backup/delete",body:{id:t}},(()=>{}))}getLogEntries(t){return this.request({path:"/_admin/log/entries",qs:t},(t=>t.body))}getLogMessages(t){return this.request({path:"/_admin/log",qs:t},(t=>t.body.messages))}getLogLevel(){return this.request({path:"/_admin/log/level"})}setLogLevel(t){return this.request({method:"PUT",path:"/_admin/log/level",body:t})}job(t){return new b(this,t)}listPendingJobs(){return this.request({path:"/_api/job/pending"},(t=>t.body))}listCompletedJobs(){return this.request({path:"/_api/job/done"},(t=>t.body))}deleteExpiredJobResults(t){return this.request({method:"DELETE",path:"/_api/job/expired",qs:{stamp:t/1e3}},(()=>{}))}deleteAllJobResults(){return this.request({method:"DELETE",path:"/_api/job/all"},(()=>{}))}}},289:function(t,e,r){"use strict";function n(t,e){let r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if("string"!=typeof t){if(t._id)return n(t._id,e);if(t._key)return n(t._key,e);throw new Error("Document handle must be a string or an object with a _key or _id attribute")}if(t.includes("/")){const[n,...o]=t.split("/"),i=n.normalize("NFC");if(r&&i!==e)throw new Error(`Document ID "${t}" does not match collection name "${e}"`);return[i,...o].join("/")}return`${e}/${t}`}r.d(e,{b:function(){return n}})},489:function(t,e,r){"use strict";r.d(e,{Ml:function(){return s},Or:function(){return a},b4:function(){return h},j$:function(){return u},tv:function(){return i}});const n={0:"Network Error",304:"Not Modified",400:"Bad Request",401:"Unauthorized",402:"Payment Required",403:"Forbidden",404:"Not Found",405:"Method Not Allowed",406:"Not Acceptable",407:"Proxy Authentication Required",408:"Request Timeout",409:"Conflict",410:"Gone",411:"Length Required",412:"Precondition Failed",413:"Payload Too Large",414:"Request-URI Too Long",415:"Unsupported Media Type",416:"Requested Range Not Satisfiable",417:"Expectation Failed",418:"I'm a teapot",421:"Misdirected Request",422:"Unprocessable Entity",423:"Locked",424:"Failed Dependency",426:"Upgrade Required",428:"Precondition Required",429:"Too Many Requests",431:"Request Header Fields Too Large",444:"Connection Closed Without Response",451:"Unavailable For Legal Reasons",499:"Client Closed Request",500:"Internal Server Error",501:"Not Implemented",502:"Bad Gateway",503:"Service Unavailable",504:"Gateway Timeout",505:"HTTP Version Not Supported",506:"Variant Also Negotiates",507:"Insufficient Storage",508:"Loop Detected",510:"Not Extended",511:"Network Authentication Required",599:"Network Connect Timeout Error"},o=["fileName","lineNumber","columnNumber","stack","description","number"];function i(t){return Boolean(t&&t.isArangoError)}function s(t){return t&&t.hasOwnProperty("error")&&t.hasOwnProperty("code")&&t.hasOwnProperty("errorMessage")&&t.hasOwnProperty("errorNum")}function a(t){return Object.getPrototypeOf(t)===Error.prototype&&t.hasOwnProperty("code")&&t.hasOwnProperty("errno")&&t.hasOwnProperty("syscall")}class h extends Error{name="ArangoError";constructor(t){super(),this.response=t,this.message=t.body.errorMessage,this.errorNum=t.body.errorNum,this.code=t.body.code;const e=new Error(this.message);e.name=this.name;for(const t of o)e[t]&&(this[t]=e[t])}get isArangoError(){return!0}toJSON(){return{error:!0,errorMessage:this.message,errorNum:this.errorNum,code:this.code}}}class u extends Error{name="HttpError";constructor(t){super(),this.response=t,this.code=t.statusCode||500,this.message=n[this.code]||n[500];const e=new Error(this.message);e.name=this.name;for(const t of o)e[t]&&(this[t]=e[t])}toJSON(){return{error:!0,code:this.code}}}},751:function(t,e,r){"use strict";r.d(e,{TS:function(){return d},ZG:function(){return a}});var n=r(268),o=r(289),i=r(489),s=r(625);function a(t){return Boolean(t&&t.isArangoGraph)}function h(t,e){const{new:r,old:n,[e]:o,...i}=t,s={...i,...o};return void 0!==r&&(s.new=r),void 0!==n&&(s.old=n),s}function u(t){const e={};return e.collection=(0,n.collectionToString)(t.collection),e.from=Array.isArray(t.from)?t.from.map(n.collectionToString):[(0,n.collectionToString)(t.from)],e.to=Array.isArray(t.to)?t.to.map(n.collectionToString):[(0,n.collectionToString)(t.to)],e}class c{constructor(t,e,r){this._db=t,this._collection=t.collection(e),this._name=this._collection.name,this._graph=r}get isArangoCollection(){return!0}get name(){return this._name}get collection(){return this._collection}get graph(){return this._graph}async vertexExists(t){try{return await this._db.request({method:"HEAD",path:`/_api/gharial/${encodeURIComponent(this.graph.name)}/vertex/${encodeURI((0,o.b)(t,this._name))}`},(()=>!0))}catch(t){if(404===t.code)return!1;throw t}}async vertex(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};"boolean"==typeof e&&(e={graceful:e});const{allowDirtyRead:r,graceful:n=!1,rev:a,...h}=e,u={};a&&(u["if-match"]=a);const c=this._db.request({path:`/_api/gharial/${encodeURIComponent(this.graph.name)}/vertex/${encodeURI((0,o.b)(t,this._name))}`,headers:u,qs:h,allowDirtyRead:r},(t=>t.body.vertex));if(!n)return c;try{return await c}catch(t){if((0,i.tv)(t)&&t.errorNum===s.lI)return null;throw t}}save(t,e){return this._db.request({method:"POST",path:`/_api/gharial/${encodeURIComponent(this.graph.name)}/vertex/${encodeURIComponent(this._name)}`,body:t,qs:e},(t=>h(t.body,"vertex")))}replace(t,e){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};"string"==typeof r&&(r={rev:r});const{rev:n,...i}=r,s={};return n&&(s["if-match"]=n),this._db.request({method:"PUT",path:`/_api/gharial/${encodeURIComponent(this.graph.name)}/vertex/${encodeURI((0,o.b)(t,this._name))}`,body:e,qs:i,headers:s},(t=>h(t.body,"vertex")))}update(t,e){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};"string"==typeof r&&(r={rev:r});const n={},{rev:i,...s}=r;return i&&(n["if-match"]=i),this._db.request({method:"PATCH",path:`/_api/gharial/${encodeURIComponent(this.graph.name)}/vertex/${encodeURI((0,o.b)(t,this._name))}`,body:e,qs:s,headers:n},(t=>h(t.body,"vertex")))}remove(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};"string"==typeof e&&(e={rev:e});const r={},{rev:n,...i}=e;return n&&(r["if-match"]=n),this._db.request({method:"DELETE",path:`/_api/gharial/${encodeURIComponent(this.graph.name)}/vertex/${encodeURI((0,o.b)(t,this._name))}`,qs:i,headers:r},(t=>h(t.body,"removed")))}}class l{constructor(t,e,r){this._db=t,this._collection=t.collection(e),this._name=this._collection.name,this._graph=r}get isArangoCollection(){return!0}get name(){return this._name}get collection(){return this._collection}get graph(){return this._graph}async edgeExists(t){try{return await this._db.request({method:"HEAD",path:`/_api/gharial/${encodeURIComponent(this.graph.name)}/edge/${encodeURI((0,o.b)(t,this._name))}`},(()=>!0))}catch(t){if(404===t.code)return!1;throw t}}async edge(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};"boolean"==typeof e&&(e={graceful:e});const{allowDirtyRead:r,graceful:n=!1,rev:a,...h}=e,u=this._db.request({path:`/_api/gharial/${encodeURIComponent(this.graph.name)}/edge/${encodeURI((0,o.b)(t,this._name))}`,qs:h,allowDirtyRead:r},(t=>t.body.edge));if(!n)return u;try{return await u}catch(t){if((0,i.tv)(t)&&t.errorNum===s.lI)return null;throw t}}save(t,e){return this._db.request({method:"POST",path:`/_api/gharial/${encodeURIComponent(this.graph.name)}/edge/${encodeURIComponent(this._name)}`,body:t,qs:e},(t=>h(t.body,"edge")))}replace(t,e){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};"string"==typeof r&&(r={rev:r});const{rev:n,...i}=r,s={};return n&&(s["if-match"]=n),this._db.request({method:"PUT",path:`/_api/gharial/${encodeURIComponent(this.graph.name)}/edge/${encodeURI((0,o.b)(t,this._name))}`,body:e,qs:i,headers:s},(t=>h(t.body,"edge")))}update(t,e){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};"string"==typeof r&&(r={rev:r});const{rev:n,...i}=r,s={};return n&&(s["if-match"]=n),this._db.request({method:"PATCH",path:`/_api/gharial/${encodeURIComponent(this.graph.name)}/edge/${encodeURI((0,o.b)(t,this._name))}`,body:e,qs:i,headers:s},(t=>h(t.body,"edge")))}remove(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};"string"==typeof e&&(e={rev:e});const{rev:r,...n}=e,i={};return r&&(i["if-match"]=r),this._db.request({method:"DELETE",path:`/_api/gharial/${encodeURIComponent(this.graph.name)}/edge/${encodeURI((0,o.b)(t,this._name))}`,qs:n,headers:i},(t=>h(t.body,"removed")))}}class d{constructor(t,e){this._name=e.normalize("NFC"),this._db=t}get isArangoGraph(){return!0}get name(){return this._name}async exists(){try{return await this.get(),!0}catch(t){if((0,i.tv)(t)&&t.errorNum===s.SL)return!1;throw t}}get(){return this._db.request({path:`/_api/gharial/${encodeURIComponent(this._name)}`},(t=>t.body.graph))}create(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{orphanCollections:r,satellites:o,waitForSync:i,isSmart:s,...a}=e;return this._db.request({method:"POST",path:"/_api/gharial",body:{orphanCollections:r&&(Array.isArray(r)?r.map(n.collectionToString):[(0,n.collectionToString)(r)]),edgeDefinitions:t.map(u),isSmart:s,name:this._name,options:{...a,satellites:o?.map(n.collectionToString)}},qs:{waitForSync:i}},(t=>t.body.graph))}drop(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return this._db.request({method:"DELETE",path:`/_api/gharial/${encodeURIComponent(this._name)}`,qs:{dropCollections:t}},(t=>t.body.removed))}vertexCollection(t){return new c(this._db,(0,n.collectionToString)(t),this)}listVertexCollections(){return this._db.request({path:`/_api/gharial/${encodeURIComponent(this._name)}/vertex`},(t=>t.body.collections))}async vertexCollections(){return(await this.listVertexCollections()).map((t=>new c(this._db,t,this)))}addVertexCollection(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{satellites:r,...o}=e;return this._db.request({method:"POST",path:`/_api/gharial/${encodeURIComponent(this._name)}/vertex`,body:{collection:(0,n.collectionToString)(t),options:{...o,satellites:r?.map(n.collectionToString)}}},(t=>t.body.graph))}removeVertexCollection(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return this._db.request({method:"DELETE",path:`/_api/gharial/${encodeURIComponent(this._name)}/vertex/${encodeURIComponent((0,n.collectionToString)(t))}`,qs:{dropCollection:e}},(t=>t.body.graph))}edgeCollection(t){return new l(this._db,(0,n.collectionToString)(t),this)}listEdgeCollections(){return this._db.request({path:`/_api/gharial/${encodeURIComponent(this._name)}/edge`},(t=>t.body.collections))}async edgeCollections(){return(await this.listEdgeCollections()).map((t=>new l(this._db,t,this)))}addEdgeDefinition(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{satellites:r,...o}=e;return this._db.request({method:"POST",path:`/_api/gharial/${encodeURIComponent(this._name)}/edge`,body:{...u(t),options:{...o,satellites:r?.map(n.collectionToString)}}},(t=>t.body.graph))}replaceEdgeDefinition(t,e){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=t,i=e;e&&!e.hasOwnProperty("collection")&&(r=e,e=void 0),e||(i=t,o=i.collection);const{satellites:s,...a}=r;return this._db.request({method:"PUT",path:`/_api/gharial/${encodeURIComponent(this._name)}/edge/${encodeURIComponent((0,n.collectionToString)(o))}`,body:{...u(i),options:{...a,satellites:s?.map(n.collectionToString)}}},(t=>t.body.graph))}removeEdgeDefinition(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return this._db.request({method:"DELETE",path:`/_api/gharial/${encodeURIComponent(this._name)}/edge/${encodeURIComponent((0,n.collectionToString)(t))}`,qs:{dropCollection:e}},(t=>t.body.graph))}traversal(t,e){return this._db.request({method:"POST",path:"/_api/traversal",body:{...e,startVertex:t,graphName:this._name}},(t=>t.body.result))}}},625:function(t,e,r){"use strict";r.d(e,{KN:function(){return s},Qc:function(){return o},RN:function(){return i},SL:function(){return l},U:function(){return n},V2:function(){return u},lI:function(){return a},rZ:function(){return c},sJ:function(){return h}});const n=10,o=503,i=1200,s=1202,a=1202,h=1203,u=1203,c=1228,l=1924},782:function(t,e,r){"use strict";r.r(e),r.d(e,{View:function(){return s},isArangoView:function(){return i}});var n=r(489),o=r(625);function i(t){return Boolean(t&&t.isArangoView)}class s{constructor(t,e){this._db=t,this._name=e.normalize("NFC")}get isArangoView(){return!0}get name(){return this._name}get(){return this._db.request({path:`/_api/view/${encodeURIComponent(this._name)}`})}async exists(){try{return await this.get(),!0}catch(t){if((0,n.tv)(t)&&t.errorNum===o.V2)return!1;throw t}}create(t){return this._db.request({method:"POST",path:"/_api/view",body:{...t,name:this._name}})}async rename(t){const e=this._db.renameView(this._name,t);return this._name=t.normalize("NFC"),e}properties(){return this._db.request({path:`/_api/view/${encodeURIComponent(this._name)}/properties`})}updateProperties(t){return this._db.request({method:"PATCH",path:`/_api/view/${encodeURIComponent(this._name)}/properties`,body:t??{}})}replaceProperties(t){return this._db.request({method:"PUT",path:`/_api/view/${encodeURIComponent(this._name)}/properties`,body:t??{}})}drop(){return this._db.request({method:"DELETE",path:`/_api/view/${encodeURIComponent(this._name)}`},(t=>t.body.result))}}},723:function(t,e,r){"use strict";const{aql:n}=r(153),{CollectionStatus:o,CollectionType:i}=r(268),{ViewType:s}=r(782),{Database:a}=r(296);function h(t){return"string"==typeof t||Array.isArray(t),new a(t)}t.exports=h,Object.assign(h,{aql:n,arangojs:h,CollectionStatus:o,CollectionType:i,Database:a,ViewType:s})}},e={};function r(n){var o=e[n];if(void 0!==o)return o.exports;var i=e[n]={exports:{}};return t[n](i,i.exports,r),i.exports}return r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,{a:e}),e},r.d=function(t,e){for(var n in e)r.o(e,n)&&!r.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r(239),r(723)}()}));
//# sourceMappingURL=web.js.map