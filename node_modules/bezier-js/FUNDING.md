**This package needs your support to stay maintained.** If you work for an organization
whose website is better off using react-onclickoutside than rolling its own code
solution, please consider talking to your manager to help fund this project.

Open Source is free to use, but certainly not free to develop. If you have the
means to reward those whose work you rely on, please consider doing so.

If you wish to help keep this library maintained through a financial contribution,
please visit the [Paypal donation page](https://www.paypal.com/donate/?cmd=_s-xclick&hosted_button_id=QPRDLNGDANJSW),
and send me an email if you want me to know your contribution was specifically
for this library.

Thank you,

- <PERSON>max
