{"name": "bezier-js", "version": "6.1.4", "author": "Pomax", "description": "A javascript library for working with Bezier curves", "type": "module", "exports": {"import": "./src/bezier.js", "require": "./dist/bezier.cjs"}, "main": "./dist/bezier.cjs", "browser": "./src/bezier.js", "license": "MIT", "bugs": {"url": "https://github.com/Pomax/bezierjs/issues"}, "homepage": "https://github.com/Pomax/bezierjs", "repository": {"type": "git", "url": "https://github.com/Pomax/bezierjs"}, "funding": {"type": "individual", "url": "https://github.com/Pomax/bezierjs/blob/master/FUNDING.md"}, "keywords": ["bezier", "curves"], "scripts": {"test": "npm run prettier jest", "start": "run-s test build", "build": "run-s build:*", "build:browser": "esbuild ./src/bezier.js --bundle --minify --outfile=dist/bezier.js", "build:node": "esbuild ./src/bezier.js --bundle --platform=node --target=node16 --outfile=dist/bezier.cjs", "prettier": "run-s prettier:*", "prettier:src": "prettier ./src/**/*.js --write", "prettier:text": "prettier ./test/**/*.js --write", "jest": "cross-env NODE_OPTIONS=--experimental-vm-modules jest ./test/"}, "devDependencies": {"cross-env": "^7.0.3", "esbuild": "^0.14.10", "jest": "^27.4.7", "npm-run-all": "^4.1.5", "prettier": "^2.0.5"}}