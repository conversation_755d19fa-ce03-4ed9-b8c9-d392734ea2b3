// Version 1.3.2 canvas-color-tracker - https://github.com/vasturiano/canvas-color-tracker
!function(t,r){"object"==typeof exports&&"undefined"!=typeof module?module.exports=r():"function"==typeof define&&define.amd?define(r):(t="undefined"!=typeof globalThis?globalThis:t||self).ColorTracker=r()}(this,(function(){"use strict";function t(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function r(t,r,e){if("function"==typeof t?t===r:t.has(r))return arguments.length<3?r:e;throw new TypeError("Private element is not present on this object")}function e(t,e){return t.get(r(t,e))}function n(t,r,e){(function(t,r){if(r.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")})(t,r),r.set(t,e)}function a(t,e,n){return t.set(r(t,e),n),n}function i(t,r,e){return r&&function(t,r){for(var e=0;e<r.length;e++){var n=r[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,s(n.key),n)}}(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function o(r){return function(r){if(Array.isArray(r))return t(r)}(r)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(r)||function(r,e){if(r){if("string"==typeof r)return t(r,e);var n={}.toString.call(r).slice(8,-1);return"Object"===n&&r.constructor&&(n=r.constructor.name),"Map"===n||"Set"===n?Array.from(r):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?t(r,e):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(t){var r=function(t,r){if("object"!=typeof t||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,r);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==typeof r?r:r+""}function f(t){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},f(t)}var h=/^\s+/,u=/\s+$/;function l(t,r){if(r=r||{},(t=t||"")instanceof l)return t;if(!(this instanceof l))return new l(t,r);var e=function(t){var r={r:0,g:0,b:0},e=1,n=null,a=null,i=null,o=!1,s=!1;"string"==typeof t&&(t=function(t){t=t.replace(h,"").replace(u,"").toLowerCase();var r,e=!1;if(H[t])t=H[t],e=!0;else if("transparent"==t)return{r:0,g:0,b:0,a:0,format:"name"};if(r=$.rgb.exec(t))return{r:r[1],g:r[2],b:r[3]};if(r=$.rgba.exec(t))return{r:r[1],g:r[2],b:r[3],a:r[4]};if(r=$.hsl.exec(t))return{h:r[1],s:r[2],l:r[3]};if(r=$.hsla.exec(t))return{h:r[1],s:r[2],l:r[3],a:r[4]};if(r=$.hsv.exec(t))return{h:r[1],s:r[2],v:r[3]};if(r=$.hsva.exec(t))return{h:r[1],s:r[2],v:r[3],a:r[4]};if(r=$.hex8.exec(t))return{r:E(r[1]),g:E(r[2]),b:E(r[3]),a:N(r[4]),format:e?"name":"hex8"};if(r=$.hex6.exec(t))return{r:E(r[1]),g:E(r[2]),b:E(r[3]),format:e?"name":"hex"};if(r=$.hex4.exec(t))return{r:E(r[1]+""+r[1]),g:E(r[2]+""+r[2]),b:E(r[3]+""+r[3]),a:N(r[4]+""+r[4]),format:e?"name":"hex8"};if(r=$.hex3.exec(t))return{r:E(r[1]+""+r[1]),g:E(r[2]+""+r[2]),b:E(r[3]+""+r[3]),format:e?"name":"hex"};return!1}(t));"object"==f(t)&&(U(t.r)&&U(t.g)&&U(t.b)?(l=t.r,c=t.g,d=t.b,r={r:255*j(l,255),g:255*j(c,255),b:255*j(d,255)},o=!0,s="%"===String(t.r).substr(-1)?"prgb":"rgb"):U(t.h)&&U(t.s)&&U(t.v)?(n=P(t.s),a=P(t.v),r=function(t,r,e){t=6*j(t,360),r=j(r,100),e=j(e,100);var n=Math.floor(t),a=t-n,i=e*(1-r),o=e*(1-a*r),s=e*(1-(1-a)*r),f=n%6,h=[e,o,i,i,s,e][f],u=[s,e,e,o,i,i][f],l=[i,i,s,e,e,o][f];return{r:255*h,g:255*u,b:255*l}}(t.h,n,a),o=!0,s="hsv"):U(t.h)&&U(t.s)&&U(t.l)&&(n=P(t.s),i=P(t.l),r=function(t,r,e){var n,a,i;function o(t,r,e){return e<0&&(e+=1),e>1&&(e-=1),e<1/6?t+6*(r-t)*e:e<.5?r:e<2/3?t+(r-t)*(2/3-e)*6:t}if(t=j(t,360),r=j(r,100),e=j(e,100),0===r)n=a=i=e;else{var s=e<.5?e*(1+r):e+r-e*r,f=2*e-s;n=o(f,s,t+1/3),a=o(f,s,t),i=o(f,s,t-1/3)}return{r:255*n,g:255*a,b:255*i}}(t.h,n,i),o=!0,s="hsl"),t.hasOwnProperty("a")&&(e=t.a));var l,c,d;return e=C(e),{ok:o,format:t.format||s,r:Math.min(255,Math.max(r.r,0)),g:Math.min(255,Math.max(r.g,0)),b:Math.min(255,Math.max(r.b,0)),a:e}}(t);this._originalInput=t,this._r=e.r,this._g=e.g,this._b=e.b,this._a=e.a,this._roundA=Math.round(100*this._a)/100,this._format=r.format||e.format,this._gradientType=r.gradientType,this._r<1&&(this._r=Math.round(this._r)),this._g<1&&(this._g=Math.round(this._g)),this._b<1&&(this._b=Math.round(this._b)),this._ok=e.ok}function c(t,r,e){t=j(t,255),r=j(r,255),e=j(e,255);var n,a,i=Math.max(t,r,e),o=Math.min(t,r,e),s=(i+o)/2;if(i==o)n=a=0;else{var f=i-o;switch(a=s>.5?f/(2-i-o):f/(i+o),i){case t:n=(r-e)/f+(r<e?6:0);break;case r:n=(e-t)/f+2;break;case e:n=(t-r)/f+4}n/=6}return{h:n,s:a,l:s}}function d(t,r,e){t=j(t,255),r=j(r,255),e=j(e,255);var n,a,i=Math.max(t,r,e),o=Math.min(t,r,e),s=i,f=i-o;if(a=0===i?0:f/i,i==o)n=0;else{switch(i){case t:n=(r-e)/f+(r<e?6:0);break;case r:n=(e-t)/f+2;break;case e:n=(t-r)/f+4}n/=6}return{h:n,s:a,v:s}}function g(t,r,e,n){var a=[I(Math.round(t).toString(16)),I(Math.round(r).toString(16)),I(Math.round(e).toString(16))];return n&&a[0].charAt(0)==a[0].charAt(1)&&a[1].charAt(0)==a[1].charAt(1)&&a[2].charAt(0)==a[2].charAt(1)?a[0].charAt(0)+a[1].charAt(0)+a[2].charAt(0):a.join("")}function b(t,r,e,n){return[I(q(n)),I(Math.round(t).toString(16)),I(Math.round(r).toString(16)),I(Math.round(e).toString(16))].join("")}function p(t,r){r=0===r?0:r||10;var e=l(t).toHsl();return e.s-=r/100,e.s=T(e.s),l(e)}function m(t,r){r=0===r?0:r||10;var e=l(t).toHsl();return e.s+=r/100,e.s=T(e.s),l(e)}function v(t){return l(t).desaturate(100)}function _(t,r){r=0===r?0:r||10;var e=l(t).toHsl();return e.l+=r/100,e.l=T(e.l),l(e)}function y(t,r){r=0===r?0:r||10;var e=l(t).toRgb();return e.r=Math.max(0,Math.min(255,e.r-Math.round(-r/100*255))),e.g=Math.max(0,Math.min(255,e.g-Math.round(-r/100*255))),e.b=Math.max(0,Math.min(255,e.b-Math.round(-r/100*255))),l(e)}function M(t,r){r=0===r?0:r||10;var e=l(t).toHsl();return e.l-=r/100,e.l=T(e.l),l(e)}function A(t,r){var e=l(t).toHsl(),n=(e.h+r)%360;return e.h=n<0?360+n:n,l(e)}function x(t){var r=l(t).toHsl();return r.h=(r.h+180)%360,l(r)}function w(t,r){if(isNaN(r)||r<=0)throw new Error("Argument to polyad must be a positive number");for(var e=l(t).toHsl(),n=[l(t)],a=360/r,i=1;i<r;i++)n.push(l({h:(e.h+i*a)%360,s:e.s,l:e.l}));return n}function k(t){var r=l(t).toHsl(),e=r.h;return[l(t),l({h:(e+72)%360,s:r.s,l:r.l}),l({h:(e+216)%360,s:r.s,l:r.l})]}function S(t,r,e){r=r||6,e=e||30;var n=l(t).toHsl(),a=360/e,i=[l(t)];for(n.h=(n.h-(a*r>>1)+720)%360;--r;)n.h=(n.h+a)%360,i.push(l(n));return i}function R(t,r){r=r||6;for(var e=l(t).toHsv(),n=e.h,a=e.s,i=e.v,o=[],s=1/r;r--;)o.push(l({h:n,s:a,v:i})),i=(i+s)%1;return o}l.prototype={isDark:function(){return this.getBrightness()<128},isLight:function(){return!this.isDark()},isValid:function(){return this._ok},getOriginalInput:function(){return this._originalInput},getFormat:function(){return this._format},getAlpha:function(){return this._a},getBrightness:function(){var t=this.toRgb();return(299*t.r+587*t.g+114*t.b)/1e3},getLuminance:function(){var t,r,e,n=this.toRgb();return t=n.r/255,r=n.g/255,e=n.b/255,.2126*(t<=.03928?t/12.92:Math.pow((t+.055)/1.055,2.4))+.7152*(r<=.03928?r/12.92:Math.pow((r+.055)/1.055,2.4))+.0722*(e<=.03928?e/12.92:Math.pow((e+.055)/1.055,2.4))},setAlpha:function(t){return this._a=C(t),this._roundA=Math.round(100*this._a)/100,this},toHsv:function(){var t=d(this._r,this._g,this._b);return{h:360*t.h,s:t.s,v:t.v,a:this._a}},toHsvString:function(){var t=d(this._r,this._g,this._b),r=Math.round(360*t.h),e=Math.round(100*t.s),n=Math.round(100*t.v);return 1==this._a?"hsv("+r+", "+e+"%, "+n+"%)":"hsva("+r+", "+e+"%, "+n+"%, "+this._roundA+")"},toHsl:function(){var t=c(this._r,this._g,this._b);return{h:360*t.h,s:t.s,l:t.l,a:this._a}},toHslString:function(){var t=c(this._r,this._g,this._b),r=Math.round(360*t.h),e=Math.round(100*t.s),n=Math.round(100*t.l);return 1==this._a?"hsl("+r+", "+e+"%, "+n+"%)":"hsla("+r+", "+e+"%, "+n+"%, "+this._roundA+")"},toHex:function(t){return g(this._r,this._g,this._b,t)},toHexString:function(t){return"#"+this.toHex(t)},toHex8:function(t){return function(t,r,e,n,a){var i=[I(Math.round(t).toString(16)),I(Math.round(r).toString(16)),I(Math.round(e).toString(16)),I(q(n))];if(a&&i[0].charAt(0)==i[0].charAt(1)&&i[1].charAt(0)==i[1].charAt(1)&&i[2].charAt(0)==i[2].charAt(1)&&i[3].charAt(0)==i[3].charAt(1))return i[0].charAt(0)+i[1].charAt(0)+i[2].charAt(0)+i[3].charAt(0);return i.join("")}(this._r,this._g,this._b,this._a,t)},toHex8String:function(t){return"#"+this.toHex8(t)},toRgb:function(){return{r:Math.round(this._r),g:Math.round(this._g),b:Math.round(this._b),a:this._a}},toRgbString:function(){return 1==this._a?"rgb("+Math.round(this._r)+", "+Math.round(this._g)+", "+Math.round(this._b)+")":"rgba("+Math.round(this._r)+", "+Math.round(this._g)+", "+Math.round(this._b)+", "+this._roundA+")"},toPercentageRgb:function(){return{r:Math.round(100*j(this._r,255))+"%",g:Math.round(100*j(this._g,255))+"%",b:Math.round(100*j(this._b,255))+"%",a:this._a}},toPercentageRgbString:function(){return 1==this._a?"rgb("+Math.round(100*j(this._r,255))+"%, "+Math.round(100*j(this._g,255))+"%, "+Math.round(100*j(this._b,255))+"%)":"rgba("+Math.round(100*j(this._r,255))+"%, "+Math.round(100*j(this._g,255))+"%, "+Math.round(100*j(this._b,255))+"%, "+this._roundA+")"},toName:function(){return 0===this._a?"transparent":!(this._a<1)&&(F[g(this._r,this._g,this._b,!0)]||!1)},toFilter:function(t){var r="#"+b(this._r,this._g,this._b,this._a),e=r,n=this._gradientType?"GradientType = 1, ":"";if(t){var a=l(t);e="#"+b(a._r,a._g,a._b,a._a)}return"progid:DXImageTransform.Microsoft.gradient("+n+"startColorstr="+r+",endColorstr="+e+")"},toString:function(t){var r=!!t;t=t||this._format;var e=!1,n=this._a<1&&this._a>=0;return r||!n||"hex"!==t&&"hex6"!==t&&"hex3"!==t&&"hex4"!==t&&"hex8"!==t&&"name"!==t?("rgb"===t&&(e=this.toRgbString()),"prgb"===t&&(e=this.toPercentageRgbString()),"hex"!==t&&"hex6"!==t||(e=this.toHexString()),"hex3"===t&&(e=this.toHexString(!0)),"hex4"===t&&(e=this.toHex8String(!0)),"hex8"===t&&(e=this.toHex8String()),"name"===t&&(e=this.toName()),"hsl"===t&&(e=this.toHslString()),"hsv"===t&&(e=this.toHsvString()),e||this.toHexString()):"name"===t&&0===this._a?this.toName():this.toRgbString()},clone:function(){return l(this.toString())},_applyModification:function(t,r){var e=t.apply(null,[this].concat([].slice.call(r)));return this._r=e._r,this._g=e._g,this._b=e._b,this.setAlpha(e._a),this},lighten:function(){return this._applyModification(_,arguments)},brighten:function(){return this._applyModification(y,arguments)},darken:function(){return this._applyModification(M,arguments)},desaturate:function(){return this._applyModification(p,arguments)},saturate:function(){return this._applyModification(m,arguments)},greyscale:function(){return this._applyModification(v,arguments)},spin:function(){return this._applyModification(A,arguments)},_applyCombination:function(t,r){return t.apply(null,[this].concat([].slice.call(r)))},analogous:function(){return this._applyCombination(S,arguments)},complement:function(){return this._applyCombination(x,arguments)},monochromatic:function(){return this._applyCombination(R,arguments)},splitcomplement:function(){return this._applyCombination(k,arguments)},triad:function(){return this._applyCombination(w,[3])},tetrad:function(){return this._applyCombination(w,[4])}},l.fromRatio=function(t,r){if("object"==f(t)){var e={};for(var n in t)t.hasOwnProperty(n)&&(e[n]="a"===n?t[n]:P(t[n]));t=e}return l(t,r)},l.equals=function(t,r){return!(!t||!r)&&l(t).toRgbString()==l(r).toRgbString()},l.random=function(){return l.fromRatio({r:Math.random(),g:Math.random(),b:Math.random()})},l.mix=function(t,r,e){e=0===e?0:e||50;var n=l(t).toRgb(),a=l(r).toRgb(),i=e/100;return l({r:(a.r-n.r)*i+n.r,g:(a.g-n.g)*i+n.g,b:(a.b-n.b)*i+n.b,a:(a.a-n.a)*i+n.a})},
// <http://www.w3.org/TR/2008/REC-WCAG20-20081211/#contrast-ratiodef (WCAG Version 2)
// Analyze the 2 colors and returns the color contrast defined by (WCAG Version 2)
l.readability=function(t,r){var e=l(t),n=l(r);return(Math.max(e.getLuminance(),n.getLuminance())+.05)/(Math.min(e.getLuminance(),n.getLuminance())+.05)},l.isReadable=function(t,r,e){var n,a,i=l.readability(t,r);switch(a=!1,(n=function(t){var r,e;r=((t=t||{level:"AA",size:"small"}).level||"AA").toUpperCase(),e=(t.size||"small").toLowerCase(),"AA"!==r&&"AAA"!==r&&(r="AA");"small"!==e&&"large"!==e&&(e="small");return{level:r,size:e}}(e)).level+n.size){case"AAsmall":case"AAAlarge":a=i>=4.5;break;case"AAlarge":a=i>=3;break;case"AAAsmall":a=i>=7}return a},l.mostReadable=function(t,r,e){var n,a,i,o,s=null,f=0;a=(e=e||{}).includeFallbackColors,i=e.level,o=e.size;for(var h=0;h<r.length;h++)(n=l.readability(t,r[h]))>f&&(f=n,s=l(r[h]));return l.isReadable(t,s,{level:i,size:o})||!a?s:(e.includeFallbackColors=!1,l.mostReadable(t,["#fff","#000"],e))};var H=l.names={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"0ff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000",blanchedalmond:"ffebcd",blue:"00f",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",burntsienna:"ea7e5d",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"0ff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkgrey:"a9a9a9",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"f0f",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",grey:"808080",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgreen:"90ee90",lightgrey:"d3d3d3",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"789",lightslategrey:"789",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"0f0",limegreen:"32cd32",linen:"faf0e6",magenta:"f0f",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370db",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"db7093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"f00",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"fff",whitesmoke:"f5f5f5",yellow:"ff0",yellowgreen:"9acd32"},F=l.hexNames=function(t){var r={};for(var e in t)t.hasOwnProperty(e)&&(r[t[e]]=e);return r}(H);function C(t){return t=parseFloat(t),(isNaN(t)||t<0||t>1)&&(t=1),t}function j(t,r){(function(t){return"string"==typeof t&&-1!=t.indexOf(".")&&1===parseFloat(t)})(t)&&(t="100%");var e=function(t){return"string"==typeof t&&-1!=t.indexOf("%")}(t);return t=Math.min(r,Math.max(0,parseFloat(t))),e&&(t=parseInt(t*r,10)/100),Math.abs(t-r)<1e-6?1:t%r/parseFloat(r)}function T(t){return Math.min(1,Math.max(0,t))}function E(t){return parseInt(t,16)}function I(t){return 1==t.length?"0"+t:""+t}function P(t){return t<=1&&(t=100*t+"%"),t}function q(t){return Math.round(255*parseFloat(t)).toString(16)}function N(t){return E(t)/255}var O,z,L,$=(z="[\\s|\\(]+("+(O="(?:[-\\+]?\\d*\\.\\d+%?)|(?:[-\\+]?\\d+%?)")+")[,|\\s]+("+O+")[,|\\s]+("+O+")\\s*\\)?",L="[\\s|\\(]+("+O+")[,|\\s]+("+O+")[,|\\s]+("+O+")[,|\\s]+("+O+")\\s*\\)?",{CSS_UNIT:new RegExp(O),rgb:new RegExp("rgb"+z),rgba:new RegExp("rgba"+L),hsl:new RegExp("hsl"+z),hsla:new RegExp("hsla"+L),hsv:new RegExp("hsv"+z),hsva:new RegExp("hsva"+L),hex3:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,hex4:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex8:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/});function U(t){return!!$.CSS_UNIT.exec(t)}var D=function(t,r,e){return(t<<16)+(r<<8)+e},B=function(t,r){return 123*t%Math.pow(2,r)},W=new WeakMap,G=new WeakMap,V=function(){return i((function t(){var r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:6;!function(t,r){if(!(t instanceof r))throw new TypeError("Cannot call a class as a function")}(this,t),n(this,W,void 0),n(this,G,void 0),a(G,this,r),this.reset()}),[{key:"reset",value:function(){a(W,this,["__reserved for background__"])}},{key:"register",value:function(t){if(e(W,this).length>=Math.pow(2,24-e(G,this)))return null;var r,n=e(W,this).length,a=B(n,e(G,this)),i=(r=n+(a<<24-e(G,this)),"#".concat(Math.min(r,Math.pow(2,24)).toString(16).padStart(6,"0")));return e(W,this).push(t),i}},{key:"lookup",value:function(t){if(!t)return null;var r,n,a,i,s="string"==typeof t?(r=l(t).toRgb(),n=r.r,a=r.g,i=r.b,D(n,a,i)):D.apply(void 0,o(t));if(!s)return null;var f=s&Math.pow(2,24-e(G,this))-1,h=s>>24-e(G,this)&Math.pow(2,e(G,this))-1;return B(f,e(G,this))!==h||f>=e(W,this).length?null:e(W,this)[f]}}])}();return V}));
