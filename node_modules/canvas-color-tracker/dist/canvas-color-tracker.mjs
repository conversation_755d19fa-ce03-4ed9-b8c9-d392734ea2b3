import tinyColor from 'tinycolor2';

function _arrayLikeToArray(r, a) {
  (null == a || a > r.length) && (a = r.length);
  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];
  return n;
}
function _arrayWithoutHoles(r) {
  if (Array.isArray(r)) return _arrayLikeToArray(r);
}
function _assertClassBrand(e, t, n) {
  if ("function" == typeof e ? e === t : e.has(t)) return arguments.length < 3 ? t : n;
  throw new TypeError("Private element is not present on this object");
}
function _checkPrivateRedeclaration(e, t) {
  if (t.has(e)) throw new TypeError("Cannot initialize the same private elements twice on an object");
}
function _classCallCheck(a, n) {
  if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function");
}
function _classPrivateFieldGet2(s, a) {
  return s.get(_assertClassBrand(s, a));
}
function _classPrivateFieldInitSpec(e, t, a) {
  _checkPrivateRedeclaration(e, t), t.set(e, a);
}
function _classPrivateFieldSet2(s, a, r) {
  return s.set(_assertClassBrand(s, a), r), r;
}
function _defineProperties(e, r) {
  for (var t = 0; t < r.length; t++) {
    var o = r[t];
    o.enumerable = o.enumerable || false, o.configurable = true, "value" in o && (o.writable = true), Object.defineProperty(e, _toPropertyKey(o.key), o);
  }
}
function _createClass(e, r, t) {
  return r && _defineProperties(e.prototype, r), Object.defineProperty(e, "prototype", {
    writable: false
  }), e;
}
function _iterableToArray(r) {
  if ("undefined" != typeof Symbol && null != r[Symbol.iterator] || null != r["@@iterator"]) return Array.from(r);
}
function _nonIterableSpread() {
  throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _toConsumableArray(r) {
  return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread();
}
function _toPrimitive(t, r) {
  if ("object" != typeof t || !t) return t;
  var e = t[Symbol.toPrimitive];
  if (void 0 !== e) {
    var i = e.call(t, r);
    if ("object" != typeof i) return i;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return (String )(t);
}
function _toPropertyKey(t) {
  var i = _toPrimitive(t, "string");
  return "symbol" == typeof i ? i : i + "";
}
function _unsupportedIterableToArray(r, a) {
  if (r) {
    if ("string" == typeof r) return _arrayLikeToArray(r, a);
    var t = {}.toString.call(r).slice(8, -1);
    return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0;
  }
}

var ENTROPY = 123; // Raise numbers to prevent collisions in lower indexes

var int2HexColor = function int2HexColor(num) {
  return "#".concat(Math.min(num, Math.pow(2, 24)).toString(16).padStart(6, '0'));
};
var rgb2Int = function rgb2Int(r, g, b) {
  return (r << 16) + (g << 8) + b;
};
var colorStr2Int = function colorStr2Int(str) {
  var _tinyColor$toRgb = tinyColor(str).toRgb(),
    r = _tinyColor$toRgb.r,
    g = _tinyColor$toRgb.g,
    b = _tinyColor$toRgb.b;
  return rgb2Int(r, g, b);
};
var checksum = function checksum(n, csBits) {
  return n * ENTROPY % Math.pow(2, csBits);
};
var _registry = /*#__PURE__*/new WeakMap();
var _csBits = /*#__PURE__*/new WeakMap();
var _default = /*#__PURE__*/function () {
  function _default() {
    var csBits = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 6;
    _classCallCheck(this, _default);
    // Internal state
    _classPrivateFieldInitSpec(this, _registry, void 0);
    // indexed objects for rgb lookup;
    _classPrivateFieldInitSpec(this, _csBits, void 0);
    _classPrivateFieldSet2(_csBits, this, csBits);
    this.reset();
  }
  return _createClass(_default, [{
    key: "reset",
    value: function reset() {
      _classPrivateFieldSet2(_registry, this, ['__reserved for background__']);
    }
  }, {
    key: "register",
    value: function register(obj) {
      if (_classPrivateFieldGet2(_registry, this).length >= Math.pow(2, 24 - _classPrivateFieldGet2(_csBits, this))) {
        // color has 24 bits (-checksum)
        return null; // Registry is full
      }
      var idx = _classPrivateFieldGet2(_registry, this).length;
      var cs = checksum(idx, _classPrivateFieldGet2(_csBits, this));
      var color = int2HexColor(idx + (cs << 24 - _classPrivateFieldGet2(_csBits, this)));
      _classPrivateFieldGet2(_registry, this).push(obj);
      return color;
    }
  }, {
    key: "lookup",
    value: function lookup(color) {
      if (!color) return null; // invalid color

      var n = typeof color === 'string' ? colorStr2Int(color) : rgb2Int.apply(void 0, _toConsumableArray(color));
      if (!n) return null; // 0 index is reserved for background

      var idx = n & Math.pow(2, 24 - _classPrivateFieldGet2(_csBits, this)) - 1; // registry index
      var cs = n >> 24 - _classPrivateFieldGet2(_csBits, this) & Math.pow(2, _classPrivateFieldGet2(_csBits, this)) - 1; // extract bits reserved for checksum

      if (checksum(idx, _classPrivateFieldGet2(_csBits, this)) !== cs || idx >= _classPrivateFieldGet2(_registry, this).length) return null; // failed checksum or registry out of bounds

      return _classPrivateFieldGet2(_registry, this)[idx];
    } // How many bits to reserve for checksum. Will eat away into the usable size of the registry.
  }]);
}();

export { _default as default };
