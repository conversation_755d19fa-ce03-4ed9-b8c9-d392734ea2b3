{"name": "canvas-color-tracker", "version": "1.3.2", "description": "A utility to track objects on a canvas by unique px color", "type": "module", "jsdelivr": "dist/canvas-color-tracker.min.js", "unpkg": "dist/canvas-color-tracker.min.js", "main": "dist/canvas-color-tracker.mjs", "module": "dist/canvas-color-tracker.mjs", "types": "dist/canvas-color-tracker.d.ts", "exports": {"types": "./dist/canvas-color-tracker.d.ts", "umd": "./dist/canvas-color-tracker.min.js", "default": "./dist/canvas-color-tracker.mjs"}, "sideEffects": false, "repository": {"type": "git", "url": "git+https://github.com/vasturiano/canvas-color-tracker.git"}, "keywords": ["canvas", "color", "tracker", "interaction", "hover", "hidden"], "author": {"name": "Vasco <PERSON>turiano", "url": "https://github.com/vasturiano"}, "license": "MIT", "bugs": {"url": "https://github.com/vasturiano/canvas-color-tracker/issues"}, "homepage": "https://github.com/vasturiano/canvas-color-tracker", "files": ["dist/**/*", "example/**/*"], "scripts": {"build": "rimraf dist && rollup -c", "dev": "rollup -w -c", "prepare": "npm run build"}, "dependencies": {"tinycolor2": "^1.6.0"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/preset-env": "^7.26.9", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-terser": "^0.4.4", "rimraf": "^6.0.1", "rollup": "^4.36.0", "rollup-plugin-dts": "^6.2.1", "typescript": "^5.8.2"}, "engines": {"node": ">=12"}}