<head>
  <style> body { margin: 0; } </style>

  <script src="//cdn.jsdelivr.net/npm/force-graph"></script>
  <!--<script src="../../dist/force-graph.js"></script>-->
</head>

<body>
  <div id="graph"></div>

  <script type="module">
    import { forceCollide, forceX, forceY } from 'https://esm.sh/d3-force';

    // Random tree
    const N = 300;
    const nodes = [...Array(N).keys()].map(i => ({
      id: i,
      pos: Math.random()
    }));

    const Graph = new ForceGraph(document.getElementById('graph'));

    Graph
      // Deactivate existing forces
      .d3Force('center', null)
      .d3Force('charge', null)

      // Add collision and x/y forces
      .d3Force('collide', forceCollide(Graph.nodeRelSize()))
      .d3Force('x', forceX(d => (d.pos - 0.5) * window.innerWidth))
      .d3Force('y', forceY(0).strength(0.2))

      // Add nodes
      .graphData({ nodes, links: [] });

    setTimeout(() => Graph.zoom(0.99), 1);
  </script>
</body>