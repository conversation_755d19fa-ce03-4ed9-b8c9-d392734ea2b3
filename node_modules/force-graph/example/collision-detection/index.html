<head>
  <style> body { margin: 0; } </style>

  <script src="//cdn.jsdelivr.net/npm/force-graph"></script>
  <!--<script src="../../dist/force-graph.js"></script>-->
</head>

<body>
  <div id="graph"></div>

  <script type="module">
    import { forceCollide } from 'https://esm.sh/d3-force';

    const N = 80;
    const nodes = [...Array(N).keys()].map(() => ({
      // Initial velocity in random direction
      vx: (Math.random() * 2) - 1,
      vy: (Math.random() * 2) - 1
    }));

    const Graph = new ForceGraph(document.getElementById('graph'));

    Graph.cooldownTime(Infinity)
      .d3AlphaDecay(0)
      .d3VelocityDecay(0)

      // Deactivate existing forces
      .d3Force('center', null)
      .d3Force('charge', null)

      // Add collision and bounding box forces
      .d3Force('collide', forceCollide(Graph.nodeRelSize()))
      .d3Force('box', () => {
        const SQUARE_HALF_SIDE = Graph.nodeRelSize() * N * 0.5;

        nodes.forEach(node => {
          const x = node.x || 0, y = node.y || 0;

          // bounce on box walls
          if (Math.abs(x) > SQUARE_HALF_SIDE) { node.vx *= -1; }
          if (Math.abs(y) > SQUARE_HALF_SIDE) { node.vy *= -1; }
        });
      })

      // Add nodes
      .graphData({ nodes, links: [] });
  </script>
</body>