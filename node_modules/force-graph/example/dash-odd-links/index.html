<head>
  <style> body { margin: 0; } </style>

  <script src="//cdn.jsdelivr.net/npm/force-graph"></script>
<!--  <script src="../../dist/force-graph.js"></script>-->
</head>

<body>
  <div id="graph"></div>

  <script>
    // Random tree
    const N = 100;
    const gData = {
      nodes: [...Array(N).keys()].map(i => ({ id: i })),
      links: [...Array(N).keys()]
        .filter(id => id)
        .map(id => ({
          source: id,
          target: Math.round(Math.random() * (id-1)),
          dashed: (id % 2 === 0)
        }))
    };

    const elem = document.getElementById('graph');

    const dashLen = 6;
    const gapLen = 8;

    const Graph = new ForceGraph(elem)
      .graphData(gData)
      .nodeRelSize(8)
      .linkWidth(3)
      .linkLineDash(link => link.dashed && [dashLen, gapLen]);

    // Dash animation
    const st = +new Date();
    const dashAnimateTime = 300; // time to animate a single dash
    (function animate() {
      const t = ((+new Date() - st) % dashAnimateTime) / dashAnimateTime;
      const lineDash = t < 0.5 ? [0, gapLen * t * 2, dashLen, gapLen * (1 - t * 2)] : [dashLen * (t - 0.5) * 2, gapLen, dashLen * (1 - (t - 0.5) * 2), 0];
      Graph.linkLineDash(link => link.dashed && lineDash);

      requestAnimationFrame(animate);
    })(); // IIFE
  </script>
</body>