<head>
  <style>
    body { margin: 0; }

    #emit-particles-btn {
      position: absolute;
      top: 10px;
      right: 10px;
      font-size: 13px;
    }
  </style>

  <script src="//cdn.jsdelivr.net/npm/force-graph"></script>
  <!--<script src="../../dist/force-graph.js"></script>-->
</head>

<body>
  <div id="graph"></div>
  <button id="emit-particles-btn">Emit 10 Random Particles</button>

  <script>
    // Random tree
    const N = 50;
    const links = [...Array(N).keys()]
    .filter(id => id)
      .map(id => ({
        source: id,
        target: Math.round(Math.random() * (id-1))
      }));
    const gData = {
      nodes: [...Array(N).keys()].map(i => ({ id: i })),
      links
    };

    const Graph = new ForceGraph(document.getElementById('graph'))
      .linkDirectionalParticleColor(() => 'red')
      .linkHoverPrecision(10)
      .graphData(gData);

    Graph.onLinkClick(Graph.emitParticle); // emit particles on link click

    document.getElementById('emit-particles-btn').addEventListener('click', () => {
      [...Array(10).keys()].forEach(() => {
        const link = links[Math.floor(Math.random() * links.length)];
        Graph.emitParticle(link);
      });
    });
  </script>
</body>