{"name": "force-graph", "version": "1.49.6", "description": "2D force-directed graph rendered on HTML5 canvas", "type": "module", "unpkg": "dist/force-graph.min.js", "jsdelivr": "dist/force-graph.min.js", "main": "dist/force-graph.mjs", "module": "dist/force-graph.mjs", "types": "src/index.d.ts", "exports": {"types": "./dist/force-graph.d.ts", "umd": "./dist/force-graph.min.js", "default": "./dist/force-graph.mjs"}, "sideEffects": ["./src/*.css"], "repository": {"type": "git", "url": "git+https://github.com/vasturiano/force-graph.git"}, "homepage": "https://github.com/vasturiano/force-graph", "keywords": ["2d", "force", "simulation", "graph", "canvas", "d3"], "author": {"name": "Vasco <PERSON>turiano", "url": "https://github.com/vasturiano"}, "license": "MIT", "bugs": {"url": "https://github.com/vasturiano/force-graph/issues"}, "scripts": {"build": "rimraf dist && rollup -c", "dev": "rollup -w -c rollup.config.dev.js", "prepare": "npm run build"}, "files": ["dist/**/*", "example/**/*", "src/**/*"], "dependencies": {"@tweenjs/tween.js": "18 - 25", "accessor-fn": "1", "bezier-js": "3 - 6", "canvas-color-tracker": "^1.3", "d3-array": "1 - 3", "d3-drag": "2 - 3", "d3-force-3d": "2 - 3", "d3-scale": "1 - 4", "d3-scale-chromatic": "1 - 3", "d3-selection": "2 - 3", "d3-zoom": "2 - 3", "float-tooltip": "^1.7", "index-array-by": "1", "kapsule": "^1.16", "lodash-es": "4"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/preset-env": "^7.27.2", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-terser": "^0.4.4", "postcss": "^8.5.3", "rimraf": "^6.0.1", "rollup": "^4.40.2", "rollup-plugin-dts": "^6.2.1", "rollup-plugin-postcss": "^4.0.2", "typescript": "^5.8.3"}, "engines": {"node": ">=12"}}