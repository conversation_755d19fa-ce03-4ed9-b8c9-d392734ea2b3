{"name": "multi-part", "author": "<PERSON><PERSON> <<EMAIL>>", "version": "4.0.0", "engines": {"node": ">=10"}, "description": "Simple multipart/form-data implementation with automatic data type detection. Supports: Strings, Numbers, Arrays, Streams, Buffers and Vinyl.", "keywords": ["multi-part", "form", "data", "buffer", "stream", "vinyl", "form-data", "multipart"], "main": "./main.js", "files": ["main.js"], "scripts": {"test": "mocha test", "lint": "eslint main.js", "check": "npm run lint && npm run test", "cover": "nyc ./node_modules/mocha/bin/_mocha && nyc report --reporter=html"}, "repository": {"type": "git", "url": "git+https://github.com/strikeentco/multi-part.git"}, "bugs": {"url": "https://github.com/strikeentco/multi-part/issues"}, "dependencies": {"mime-kind": "^4.0.0", "multi-part-lite": "^1.0.0"}, "devDependencies": {"eslint": "^8.24.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.26.0", "express": "^4.18.1", "got": "^9.6.0", "mocha": "^6.2.3", "multer": "^1.4.4", "nyc": "^14.1.1", "should": "^13.2.3", "vinyl": "^2.2.1"}, "license": "MIT"}