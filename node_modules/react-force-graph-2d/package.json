{"name": "react-force-graph-2d", "version": "1.27.1", "description": "React component for 2D force directed graphs", "license": "MIT", "type": "module", "unpkg": "dist/react-force-graph-2d.min.js", "jsdelivr": "dist/react-force-graph-2d.min.js", "main": "dist/react-force-graph-2d.mjs", "module": "dist/react-force-graph-2d.mjs", "types": "dist/react-force-graph-2d.d.ts", "exports": {"types": "./dist/react-force-graph-2d.d.ts", "umd": "./dist/react-force-graph-2d.min.js", "default": "./dist/react-force-graph-2d.mjs"}, "sideEffects": false, "repository": {"type": "git", "url": "git+https://github.com/vasturiano/react-force-graph.git"}, "homepage": "https://github.com/vasturiano/react-force-graph", "keywords": ["react", "force", "graph", "2d", "canvas"], "author": {"name": "Vasco <PERSON>turiano", "url": "https://github.com/vasturiano"}, "bugs": {"url": "https://github.com/vasturiano/react-force-graph/issues"}, "scripts": {"build": "rimraf dist && rollup -c", "dev": "rollup -w -c rollup.config.dev.js", "prepare": "npm run build"}, "files": ["dist/**/*"], "dependencies": {"force-graph": "^1.49", "prop-types": "15", "react-kapsule": "^2.5"}, "peerDependencies": {"react": "*"}, "devDependencies": {"rimraf": "^6.0.1", "rollup": "^4.34.6"}, "engines": {"node": ">=12"}}