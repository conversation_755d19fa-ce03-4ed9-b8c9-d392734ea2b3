// Version 1.42.13 three-forcegraph - https://github.com/vasturiano/three-forcegraph
!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?module.exports=n(require("three")):"function"==typeof define&&define.amd?define(["three"],n):(t="undefined"!=typeof globalThis?globalThis:t||self).ThreeForceGraph=n(t.THREE)}(this,(function(t){"use strict";function n(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=Array(n);e<n;e++)r[e]=t[e];return r}function e(t,n,e){if("function"==typeof t?t===n:t.has(n))return arguments.length<3?n:e;throw new TypeError("Private element is not present on this object")}function r(t,n,e){return n=h(n),function(t,n){if(n&&("object"==typeof n||"function"==typeof n))return n;if(void 0!==n)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,p()?Reflect.construct(n,e||[],h(t).constructor):n.apply(t,e))}function i(t,n){if(!(t instanceof n))throw new TypeError("Cannot call a class as a function")}function o(t,n){return t.get(e(t,n))}function a(t,n,e){(function(t,n){if(n.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")})(t,n),n.set(t,e)}function u(t,n,r){return t.set(e(t,n),r),r}function s(t,n,e){if(p())return Reflect.construct.apply(null,arguments);var r=[null];return r.push.apply(r,n),new(t.bind.apply(t,r))}function f(t,n,e){return n&&function(t,n){for(var e=0;e<n.length;e++){var r=n[e];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,_(r.key),r)}}(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function l(t,n,e){return(n=_(n))in t?Object.defineProperty(t,n,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[n]=e,t}function c(){return c="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,n,e){var r=function(t,n){for(;!{}.hasOwnProperty.call(t,n)&&null!==(t=h(t)););return t}(t,n);if(r){var i=Object.getOwnPropertyDescriptor(r,n);return i.get?i.get.call(arguments.length<3?t:e):i.value}},c.apply(null,arguments)}function h(t){return h=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},h(t)}function d(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(n&&n.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),n&&v(t,n)}function p(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(p=function(){return!!t})()}function g(t,n){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable}))),e.push.apply(e,r)}return e}function v(t,n){return v=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,n){return t.__proto__=n,t},v(t,n)}function y(t,n){return function(t){if(Array.isArray(t))return t}(t)||function(t,n){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var r,i,o,a,u=[],s=!0,f=!1;try{if(o=(e=e.call(t)).next,0===n);else for(;!(s=(r=o.call(e)).done)&&(u.push(r.value),u.length!==n);s=!0);}catch(t){f=!0,i=t}finally{try{if(!s&&null!=e.return&&(a=e.return(),Object(a)!==a))return}finally{if(f)throw i}}return u}}(t,n)||x(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function b(t,n,e,r){var i=c(h(t.prototype),n,e);return"function"==typeof i?function(t){return i.apply(e,t)}:i}function m(t){return function(t){if(Array.isArray(t))return n(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||x(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _(t){var n=function(t,n){if("object"!=typeof t||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,n);if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==typeof n?n:n+""}function w(t){return w="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},w(t)}function x(t,e){if(t){if("string"==typeof t)return n(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(t,e):void 0}}function k(t,n,e){var r,i=1;function o(){var o,a,u=r.length,s=0,f=0,l=0;for(o=0;o<u;++o)s+=(a=r[o]).x||0,f+=a.y||0,l+=a.z||0;for(s=(s/u-t)*i,f=(f/u-n)*i,l=(l/u-e)*i,o=0;o<u;++o)a=r[o],s&&(a.x-=s),f&&(a.y-=f),l&&(a.z-=l)}return null==t&&(t=0),null==n&&(n=0),null==e&&(e=0),o.initialize=function(t){r=t},o.x=function(n){return arguments.length?(t=+n,o):t},o.y=function(t){return arguments.length?(n=+t,o):n},o.z=function(t){return arguments.length?(e=+t,o):e},o.strength=function(t){return arguments.length?(i=+t,o):i},o}function M(t,n,e){if(isNaN(n))return t;var r,i,o,a,u,s,f=t._root,l={data:e},c=t._x0,h=t._x1;if(!f)return t._root=l,t;for(;f.length;)if((a=n>=(i=(c+h)/2))?c=i:h=i,r=f,!(f=f[u=+a]))return r[u]=l,t;if(n===(o=+t._x.call(null,f.data)))return l.next=f,r?r[u]=l:t._root=l,t;do{r=r?r[u]=new Array(2):t._root=new Array(2),(a=n>=(i=(c+h)/2))?c=i:h=i}while((u=+a)==(s=+(o>=i)));return r[s]=f,r[u]=l,t}function A(t,n,e){this.node=t,this.x0=n,this.x1=e}function O(t){return t[0]}function j(t,n){var e=new N(null==n?O:n,NaN,NaN);return null==t?e:e.addAll(t)}function N(t,n,e){this._x=t,this._x0=n,this._x1=e,this._root=void 0}function S(t){for(var n={data:t.data},e=n;t=t.next;)e=e.next={data:t.data};return n}var C=j.prototype=N.prototype;function P(t,n,e,r){if(isNaN(n)||isNaN(e))return t;var i,o,a,u,s,f,l,c,h,d=t._root,p={data:r},g=t._x0,v=t._y0,y=t._x1,b=t._y1;if(!d)return t._root=p,t;for(;d.length;)if((f=n>=(o=(g+y)/2))?g=o:y=o,(l=e>=(a=(v+b)/2))?v=a:b=a,i=d,!(d=d[c=l<<1|f]))return i[c]=p,t;if(u=+t._x.call(null,d.data),s=+t._y.call(null,d.data),n===u&&e===s)return p.next=d,i?i[c]=p:t._root=p,t;do{i=i?i[c]=new Array(4):t._root=new Array(4),(f=n>=(o=(g+y)/2))?g=o:y=o,(l=e>=(a=(v+b)/2))?v=a:b=a}while((c=l<<1|f)==(h=(s>=a)<<1|u>=o));return i[h]=d,i[c]=p,t}function z(t,n,e,r,i){this.node=t,this.x0=n,this.y0=e,this.x1=r,this.y1=i}function E(t){return t[0]}function B(t){return t[1]}function T(t,n,e){var r=new D(null==n?E:n,null==e?B:e,NaN,NaN,NaN,NaN);return null==t?r:r.addAll(t)}function D(t,n,e,r,i,o){this._x=t,this._y=n,this._x0=e,this._y0=r,this._x1=i,this._y1=o,this._root=void 0}function $(t){for(var n={data:t.data},e=n;t=t.next;)e=e.next={data:t.data};return n}C.copy=function(){var t,n,e=new N(this._x,this._x0,this._x1),r=this._root;if(!r)return e;if(!r.length)return e._root=S(r),e;for(t=[{source:r,target:e._root=new Array(2)}];r=t.pop();)for(var i=0;i<2;++i)(n=r.source[i])&&(n.length?t.push({source:n,target:r.target[i]=new Array(2)}):r.target[i]=S(n));return e},C.add=function(t){const n=+this._x.call(null,t);return M(this.cover(n),n,t)},C.addAll=function(t){Array.isArray(t)||(t=Array.from(t));const n=t.length,e=new Float64Array(n);let r=1/0,i=-1/0;for(let o,a=0;a<n;++a)isNaN(o=+this._x.call(null,t[a]))||(e[a]=o,o<r&&(r=o),o>i&&(i=o));if(r>i)return this;this.cover(r).cover(i);for(let r=0;r<n;++r)M(this,e[r],t[r]);return this},C.cover=function(t){if(isNaN(t=+t))return this;var n=this._x0,e=this._x1;if(isNaN(n))e=(n=Math.floor(t))+1;else{for(var r,i,o=e-n||1,a=this._root;n>t||t>=e;)switch(i=+(t<n),(r=new Array(2))[i]=a,a=r,o*=2,i){case 0:e=n+o;break;case 1:n=e-o}this._root&&this._root.length&&(this._root=a)}return this._x0=n,this._x1=e,this},C.data=function(){var t=[];return this.visit((function(n){if(!n.length)do{t.push(n.data)}while(n=n.next)})),t},C.extent=function(t){return arguments.length?this.cover(+t[0][0]).cover(+t[1][0]):isNaN(this._x0)?void 0:[[this._x0],[this._x1]]},C.find=function(t,n){var e,r,i,o,a,u=this._x0,s=this._x1,f=[],l=this._root;for(l&&f.push(new A(l,u,s)),null==n?n=1/0:(u=t-n,s=t+n);o=f.pop();)if(!(!(l=o.node)||(r=o.x0)>s||(i=o.x1)<u))if(l.length){var c=(r+i)/2;f.push(new A(l[1],c,i),new A(l[0],r,c)),(a=+(t>=c))&&(o=f[f.length-1],f[f.length-1]=f[f.length-1-a],f[f.length-1-a]=o)}else{var h=Math.abs(t-+this._x.call(null,l.data));h<n&&(n=h,u=t-h,s=t+h,e=l.data)}return e},C.remove=function(t){if(isNaN(o=+this._x.call(null,t)))return this;var n,e,r,i,o,a,u,s,f,l=this._root,c=this._x0,h=this._x1;if(!l)return this;if(l.length)for(;;){if((u=o>=(a=(c+h)/2))?c=a:h=a,n=l,!(l=l[s=+u]))return this;if(!l.length)break;n[s+1&1]&&(e=n,f=s)}for(;l.data!==t;)if(r=l,!(l=l.next))return this;return(i=l.next)&&delete l.next,r?(i?r.next=i:delete r.next,this):n?(i?n[s]=i:delete n[s],(l=n[0]||n[1])&&l===(n[1]||n[0])&&!l.length&&(e?e[f]=l:this._root=l),this):(this._root=i,this)},C.removeAll=function(t){for(var n=0,e=t.length;n<e;++n)this.remove(t[n]);return this},C.root=function(){return this._root},C.size=function(){var t=0;return this.visit((function(n){if(!n.length)do{++t}while(n=n.next)})),t},C.visit=function(t){var n,e,r,i,o=[],a=this._root;for(a&&o.push(new A(a,this._x0,this._x1));n=o.pop();)if(!t(a=n.node,r=n.x0,i=n.x1)&&a.length){var u=(r+i)/2;(e=a[1])&&o.push(new A(e,u,i)),(e=a[0])&&o.push(new A(e,r,u))}return this},C.visitAfter=function(t){var n,e=[],r=[];for(this._root&&e.push(new A(this._root,this._x0,this._x1));n=e.pop();){var i=n.node;if(i.length){var o,a=n.x0,u=n.x1,s=(a+u)/2;(o=i[0])&&e.push(new A(o,a,s)),(o=i[1])&&e.push(new A(o,s,u))}r.push(n)}for(;n=r.pop();)t(n.node,n.x0,n.x1);return this},C.x=function(t){return arguments.length?(this._x=t,this):this._x};var I=T.prototype=D.prototype;function L(t,n,e,r,i){if(isNaN(n)||isNaN(e)||isNaN(r))return t;var o,a,u,s,f,l,c,h,d,p,g,v,y=t._root,b={data:i},m=t._x0,_=t._y0,w=t._z0,x=t._x1,k=t._y1,M=t._z1;if(!y)return t._root=b,t;for(;y.length;)if((h=n>=(a=(m+x)/2))?m=a:x=a,(d=e>=(u=(_+k)/2))?_=u:k=u,(p=r>=(s=(w+M)/2))?w=s:M=s,o=y,!(y=y[g=p<<2|d<<1|h]))return o[g]=b,t;if(f=+t._x.call(null,y.data),l=+t._y.call(null,y.data),c=+t._z.call(null,y.data),n===f&&e===l&&r===c)return b.next=y,o?o[g]=b:t._root=b,t;do{o=o?o[g]=new Array(8):t._root=new Array(8),(h=n>=(a=(m+x)/2))?m=a:x=a,(d=e>=(u=(_+k)/2))?_=u:k=u,(p=r>=(s=(w+M)/2))?w=s:M=s}while((g=p<<2|d<<1|h)==(v=(c>=s)<<2|(l>=u)<<1|f>=a));return o[v]=y,o[g]=b,t}function F(t,n,e,r,i,o,a){this.node=t,this.x0=n,this.y0=e,this.z0=r,this.x1=i,this.y1=o,this.z1=a}I.copy=function(){var t,n,e=new D(this._x,this._y,this._x0,this._y0,this._x1,this._y1),r=this._root;if(!r)return e;if(!r.length)return e._root=$(r),e;for(t=[{source:r,target:e._root=new Array(4)}];r=t.pop();)for(var i=0;i<4;++i)(n=r.source[i])&&(n.length?t.push({source:n,target:r.target[i]=new Array(4)}):r.target[i]=$(n));return e},I.add=function(t){const n=+this._x.call(null,t),e=+this._y.call(null,t);return P(this.cover(n,e),n,e,t)},I.addAll=function(t){var n,e,r,i,o=t.length,a=new Array(o),u=new Array(o),s=1/0,f=1/0,l=-1/0,c=-1/0;for(e=0;e<o;++e)isNaN(r=+this._x.call(null,n=t[e]))||isNaN(i=+this._y.call(null,n))||(a[e]=r,u[e]=i,r<s&&(s=r),r>l&&(l=r),i<f&&(f=i),i>c&&(c=i));if(s>l||f>c)return this;for(this.cover(s,f).cover(l,c),e=0;e<o;++e)P(this,a[e],u[e],t[e]);return this},I.cover=function(t,n){if(isNaN(t=+t)||isNaN(n=+n))return this;var e=this._x0,r=this._y0,i=this._x1,o=this._y1;if(isNaN(e))i=(e=Math.floor(t))+1,o=(r=Math.floor(n))+1;else{for(var a,u,s=i-e||1,f=this._root;e>t||t>=i||r>n||n>=o;)switch(u=(n<r)<<1|t<e,(a=new Array(4))[u]=f,f=a,s*=2,u){case 0:i=e+s,o=r+s;break;case 1:e=i-s,o=r+s;break;case 2:i=e+s,r=o-s;break;case 3:e=i-s,r=o-s}this._root&&this._root.length&&(this._root=f)}return this._x0=e,this._y0=r,this._x1=i,this._y1=o,this},I.data=function(){var t=[];return this.visit((function(n){if(!n.length)do{t.push(n.data)}while(n=n.next)})),t},I.extent=function(t){return arguments.length?this.cover(+t[0][0],+t[0][1]).cover(+t[1][0],+t[1][1]):isNaN(this._x0)?void 0:[[this._x0,this._y0],[this._x1,this._y1]]},I.find=function(t,n,e){var r,i,o,a,u,s,f,l=this._x0,c=this._y0,h=this._x1,d=this._y1,p=[],g=this._root;for(g&&p.push(new z(g,l,c,h,d)),null==e?e=1/0:(l=t-e,c=n-e,h=t+e,d=n+e,e*=e);s=p.pop();)if(!(!(g=s.node)||(i=s.x0)>h||(o=s.y0)>d||(a=s.x1)<l||(u=s.y1)<c))if(g.length){var v=(i+a)/2,y=(o+u)/2;p.push(new z(g[3],v,y,a,u),new z(g[2],i,y,v,u),new z(g[1],v,o,a,y),new z(g[0],i,o,v,y)),(f=(n>=y)<<1|t>=v)&&(s=p[p.length-1],p[p.length-1]=p[p.length-1-f],p[p.length-1-f]=s)}else{var b=t-+this._x.call(null,g.data),m=n-+this._y.call(null,g.data),_=b*b+m*m;if(_<e){var w=Math.sqrt(e=_);l=t-w,c=n-w,h=t+w,d=n+w,r=g.data}}return r},I.remove=function(t){if(isNaN(o=+this._x.call(null,t))||isNaN(a=+this._y.call(null,t)))return this;var n,e,r,i,o,a,u,s,f,l,c,h,d=this._root,p=this._x0,g=this._y0,v=this._x1,y=this._y1;if(!d)return this;if(d.length)for(;;){if((f=o>=(u=(p+v)/2))?p=u:v=u,(l=a>=(s=(g+y)/2))?g=s:y=s,n=d,!(d=d[c=l<<1|f]))return this;if(!d.length)break;(n[c+1&3]||n[c+2&3]||n[c+3&3])&&(e=n,h=c)}for(;d.data!==t;)if(r=d,!(d=d.next))return this;return(i=d.next)&&delete d.next,r?(i?r.next=i:delete r.next,this):n?(i?n[c]=i:delete n[c],(d=n[0]||n[1]||n[2]||n[3])&&d===(n[3]||n[2]||n[1]||n[0])&&!d.length&&(e?e[h]=d:this._root=d),this):(this._root=i,this)},I.removeAll=function(t){for(var n=0,e=t.length;n<e;++n)this.remove(t[n]);return this},I.root=function(){return this._root},I.size=function(){var t=0;return this.visit((function(n){if(!n.length)do{++t}while(n=n.next)})),t},I.visit=function(t){var n,e,r,i,o,a,u=[],s=this._root;for(s&&u.push(new z(s,this._x0,this._y0,this._x1,this._y1));n=u.pop();)if(!t(s=n.node,r=n.x0,i=n.y0,o=n.x1,a=n.y1)&&s.length){var f=(r+o)/2,l=(i+a)/2;(e=s[3])&&u.push(new z(e,f,l,o,a)),(e=s[2])&&u.push(new z(e,r,l,f,a)),(e=s[1])&&u.push(new z(e,f,i,o,l)),(e=s[0])&&u.push(new z(e,r,i,f,l))}return this},I.visitAfter=function(t){var n,e=[],r=[];for(this._root&&e.push(new z(this._root,this._x0,this._y0,this._x1,this._y1));n=e.pop();){var i=n.node;if(i.length){var o,a=n.x0,u=n.y0,s=n.x1,f=n.y1,l=(a+s)/2,c=(u+f)/2;(o=i[0])&&e.push(new z(o,a,u,l,c)),(o=i[1])&&e.push(new z(o,l,u,s,c)),(o=i[2])&&e.push(new z(o,a,c,l,f)),(o=i[3])&&e.push(new z(o,l,c,s,f))}r.push(n)}for(;n=r.pop();)t(n.node,n.x0,n.y0,n.x1,n.y1);return this},I.x=function(t){return arguments.length?(this._x=t,this):this._x},I.y=function(t){return arguments.length?(this._y=t,this):this._y};const R=(t,n,e,r,i,o)=>Math.sqrt((t-r)**2+(n-i)**2+(e-o)**2);function q(t){return t[0]}function U(t){return t[1]}function V(t){return t[2]}function G(t,n,e,r){var i=new H(null==n?q:n,null==e?U:e,null==r?V:r,NaN,NaN,NaN,NaN,NaN,NaN);return null==t?i:i.addAll(t)}function H(t,n,e,r,i,o,a,u,s){this._x=t,this._y=n,this._z=e,this._x0=r,this._y0=i,this._z0=o,this._x1=a,this._y1=u,this._z1=s,this._root=void 0}function W(t){for(var n={data:t.data},e=n;t=t.next;)e=e.next={data:t.data};return n}var Q=G.prototype=H.prototype;function X(t){return function(){return t}}function J(t){return 1e-6*(t()-.5)}function Y(t){return t.index}function K(t,n){var e=t.get(n);if(!e)throw new Error("node not found: "+n);return e}function Z(t){var n,e,r,i,o,a,u,s=Y,f=function(t){return 1/Math.min(o[t.source.index],o[t.target.index])},l=X(30),c=1;function h(r){for(var o=0,s=t.length;o<c;++o)for(var f,l,h,d,p,g=0,v=0,y=0,b=0;g<s;++g)l=(f=t[g]).source,v=(h=f.target).x+h.vx-l.x-l.vx||J(u),i>1&&(y=h.y+h.vy-l.y-l.vy||J(u)),i>2&&(b=h.z+h.vz-l.z-l.vz||J(u)),v*=d=((d=Math.sqrt(v*v+y*y+b*b))-e[g])/d*r*n[g],y*=d,b*=d,h.vx-=v*(p=a[g]),i>1&&(h.vy-=y*p),i>2&&(h.vz-=b*p),l.vx+=v*(p=1-p),i>1&&(l.vy+=y*p),i>2&&(l.vz+=b*p)}function d(){if(r){var i,u,f=r.length,l=t.length,c=new Map(r.map(((t,n)=>[s(t,n,r),t])));for(i=0,o=new Array(f);i<l;++i)(u=t[i]).index=i,"object"!=typeof u.source&&(u.source=K(c,u.source)),"object"!=typeof u.target&&(u.target=K(c,u.target)),o[u.source.index]=(o[u.source.index]||0)+1,o[u.target.index]=(o[u.target.index]||0)+1;for(i=0,a=new Array(l);i<l;++i)u=t[i],a[i]=o[u.source.index]/(o[u.source.index]+o[u.target.index]);n=new Array(l),p(),e=new Array(l),g()}}function p(){if(r)for(var e=0,i=t.length;e<i;++e)n[e]=+f(t[e],e,t)}function g(){if(r)for(var n=0,i=t.length;n<i;++n)e[n]=+l(t[n],n,t)}return null==t&&(t=[]),h.initialize=function(t,...n){r=t,u=n.find((t=>"function"==typeof t))||Math.random,i=n.find((t=>[1,2,3].includes(t)))||2,d()},h.links=function(n){return arguments.length?(t=n,d(),h):t},h.id=function(t){return arguments.length?(s=t,h):s},h.iterations=function(t){return arguments.length?(c=+t,h):c},h.strength=function(t){return arguments.length?(f="function"==typeof t?t:X(+t),p(),h):f},h.distance=function(t){return arguments.length?(l="function"==typeof t?t:X(+t),g(),h):l},h}Q.copy=function(){var t,n,e=new H(this._x,this._y,this._z,this._x0,this._y0,this._z0,this._x1,this._y1,this._z1),r=this._root;if(!r)return e;if(!r.length)return e._root=W(r),e;for(t=[{source:r,target:e._root=new Array(8)}];r=t.pop();)for(var i=0;i<8;++i)(n=r.source[i])&&(n.length?t.push({source:n,target:r.target[i]=new Array(8)}):r.target[i]=W(n));return e},Q.add=function(t){const n=+this._x.call(null,t),e=+this._y.call(null,t),r=+this._z.call(null,t);return L(this.cover(n,e,r),n,e,r,t)},Q.addAll=function(t){Array.isArray(t)||(t=Array.from(t));const n=t.length,e=new Float64Array(n),r=new Float64Array(n),i=new Float64Array(n);let o=1/0,a=1/0,u=1/0,s=-1/0,f=-1/0,l=-1/0;for(let c,h,d,p,g=0;g<n;++g)isNaN(h=+this._x.call(null,c=t[g]))||isNaN(d=+this._y.call(null,c))||isNaN(p=+this._z.call(null,c))||(e[g]=h,r[g]=d,i[g]=p,h<o&&(o=h),h>s&&(s=h),d<a&&(a=d),d>f&&(f=d),p<u&&(u=p),p>l&&(l=p));if(o>s||a>f||u>l)return this;this.cover(o,a,u).cover(s,f,l);for(let o=0;o<n;++o)L(this,e[o],r[o],i[o],t[o]);return this},Q.cover=function(t,n,e){if(isNaN(t=+t)||isNaN(n=+n)||isNaN(e=+e))return this;var r=this._x0,i=this._y0,o=this._z0,a=this._x1,u=this._y1,s=this._z1;if(isNaN(r))a=(r=Math.floor(t))+1,u=(i=Math.floor(n))+1,s=(o=Math.floor(e))+1;else{for(var f,l,c=a-r||1,h=this._root;r>t||t>=a||i>n||n>=u||o>e||e>=s;)switch(l=(e<o)<<2|(n<i)<<1|t<r,(f=new Array(8))[l]=h,h=f,c*=2,l){case 0:a=r+c,u=i+c,s=o+c;break;case 1:r=a-c,u=i+c,s=o+c;break;case 2:a=r+c,i=u-c,s=o+c;break;case 3:r=a-c,i=u-c,s=o+c;break;case 4:a=r+c,u=i+c,o=s-c;break;case 5:r=a-c,u=i+c,o=s-c;break;case 6:a=r+c,i=u-c,o=s-c;break;case 7:r=a-c,i=u-c,o=s-c}this._root&&this._root.length&&(this._root=h)}return this._x0=r,this._y0=i,this._z0=o,this._x1=a,this._y1=u,this._z1=s,this},Q.data=function(){var t=[];return this.visit((function(n){if(!n.length)do{t.push(n.data)}while(n=n.next)})),t},Q.extent=function(t){return arguments.length?this.cover(+t[0][0],+t[0][1],+t[0][2]).cover(+t[1][0],+t[1][1],+t[1][2]):isNaN(this._x0)?void 0:[[this._x0,this._y0,this._z0],[this._x1,this._y1,this._z1]]},Q.find=function(t,n,e,r){var i,o,a,u,s,f,l,c,h,d=this._x0,p=this._y0,g=this._z0,v=this._x1,y=this._y1,b=this._z1,m=[],_=this._root;for(_&&m.push(new F(_,d,p,g,v,y,b)),null==r?r=1/0:(d=t-r,p=n-r,g=e-r,v=t+r,y=n+r,b=e+r,r*=r);c=m.pop();)if(!(!(_=c.node)||(o=c.x0)>v||(a=c.y0)>y||(u=c.z0)>b||(s=c.x1)<d||(f=c.y1)<p||(l=c.z1)<g))if(_.length){var w=(o+s)/2,x=(a+f)/2,k=(u+l)/2;m.push(new F(_[7],w,x,k,s,f,l),new F(_[6],o,x,k,w,f,l),new F(_[5],w,a,k,s,x,l),new F(_[4],o,a,k,w,x,l),new F(_[3],w,x,u,s,f,k),new F(_[2],o,x,u,w,f,k),new F(_[1],w,a,u,s,x,k),new F(_[0],o,a,u,w,x,k)),(h=(e>=k)<<2|(n>=x)<<1|t>=w)&&(c=m[m.length-1],m[m.length-1]=m[m.length-1-h],m[m.length-1-h]=c)}else{var M=t-+this._x.call(null,_.data),A=n-+this._y.call(null,_.data),O=e-+this._z.call(null,_.data),j=M*M+A*A+O*O;if(j<r){var N=Math.sqrt(r=j);d=t-N,p=n-N,g=e-N,v=t+N,y=n+N,b=e+N,i=_.data}}return i},Q.findAllWithinRadius=function(t,n,e,r){const i=[],o=t-r,a=n-r,u=e-r,s=t+r,f=n+r,l=e+r;return this.visit(((c,h,d,p,g,v,y)=>{if(!c.length)do{const o=c.data;R(t,n,e,this._x(o),this._y(o),this._z(o))<=r&&i.push(o)}while(c=c.next);return h>s||d>f||p>l||g<o||v<a||y<u})),i},Q.remove=function(t){if(isNaN(o=+this._x.call(null,t))||isNaN(a=+this._y.call(null,t))||isNaN(u=+this._z.call(null,t)))return this;var n,e,r,i,o,a,u,s,f,l,c,h,d,p,g,v=this._root,y=this._x0,b=this._y0,m=this._z0,_=this._x1,w=this._y1,x=this._z1;if(!v)return this;if(v.length)for(;;){if((c=o>=(s=(y+_)/2))?y=s:_=s,(h=a>=(f=(b+w)/2))?b=f:w=f,(d=u>=(l=(m+x)/2))?m=l:x=l,n=v,!(v=v[p=d<<2|h<<1|c]))return this;if(!v.length)break;(n[p+1&7]||n[p+2&7]||n[p+3&7]||n[p+4&7]||n[p+5&7]||n[p+6&7]||n[p+7&7])&&(e=n,g=p)}for(;v.data!==t;)if(r=v,!(v=v.next))return this;return(i=v.next)&&delete v.next,r?(i?r.next=i:delete r.next,this):n?(i?n[p]=i:delete n[p],(v=n[0]||n[1]||n[2]||n[3]||n[4]||n[5]||n[6]||n[7])&&v===(n[7]||n[6]||n[5]||n[4]||n[3]||n[2]||n[1]||n[0])&&!v.length&&(e?e[g]=v:this._root=v),this):(this._root=i,this)},Q.removeAll=function(t){for(var n=0,e=t.length;n<e;++n)this.remove(t[n]);return this},Q.root=function(){return this._root},Q.size=function(){var t=0;return this.visit((function(n){if(!n.length)do{++t}while(n=n.next)})),t},Q.visit=function(t){var n,e,r,i,o,a,u,s,f=[],l=this._root;for(l&&f.push(new F(l,this._x0,this._y0,this._z0,this._x1,this._y1,this._z1));n=f.pop();)if(!t(l=n.node,r=n.x0,i=n.y0,o=n.z0,a=n.x1,u=n.y1,s=n.z1)&&l.length){var c=(r+a)/2,h=(i+u)/2,d=(o+s)/2;(e=l[7])&&f.push(new F(e,c,h,d,a,u,s)),(e=l[6])&&f.push(new F(e,r,h,d,c,u,s)),(e=l[5])&&f.push(new F(e,c,i,d,a,h,s)),(e=l[4])&&f.push(new F(e,r,i,d,c,h,s)),(e=l[3])&&f.push(new F(e,c,h,o,a,u,d)),(e=l[2])&&f.push(new F(e,r,h,o,c,u,d)),(e=l[1])&&f.push(new F(e,c,i,o,a,h,d)),(e=l[0])&&f.push(new F(e,r,i,o,c,h,d))}return this},Q.visitAfter=function(t){var n,e=[],r=[];for(this._root&&e.push(new F(this._root,this._x0,this._y0,this._z0,this._x1,this._y1,this._z1));n=e.pop();){var i=n.node;if(i.length){var o,a=n.x0,u=n.y0,s=n.z0,f=n.x1,l=n.y1,c=n.z1,h=(a+f)/2,d=(u+l)/2,p=(s+c)/2;(o=i[0])&&e.push(new F(o,a,u,s,h,d,p)),(o=i[1])&&e.push(new F(o,h,u,s,f,d,p)),(o=i[2])&&e.push(new F(o,a,d,s,h,l,p)),(o=i[3])&&e.push(new F(o,h,d,s,f,l,p)),(o=i[4])&&e.push(new F(o,a,u,p,h,d,c)),(o=i[5])&&e.push(new F(o,h,u,p,f,d,c)),(o=i[6])&&e.push(new F(o,a,d,p,h,l,c)),(o=i[7])&&e.push(new F(o,h,d,p,f,l,c))}r.push(n)}for(;n=r.pop();)t(n.node,n.x0,n.y0,n.z0,n.x1,n.y1,n.z1);return this},Q.x=function(t){return arguments.length?(this._x=t,this):this._x},Q.y=function(t){return arguments.length?(this._y=t,this):this._y},Q.z=function(t){return arguments.length?(this._z=t,this):this._z};var tt={value:()=>{}};function nt(){for(var t,n=0,e=arguments.length,r={};n<e;++n){if(!(t=arguments[n]+"")||t in r||/[\s.]/.test(t))throw new Error("illegal type: "+t);r[t]=[]}return new et(r)}function et(t){this._=t}function rt(t,n){for(var e,r=0,i=t.length;r<i;++r)if((e=t[r]).name===n)return e.value}function it(t,n,e){for(var r=0,i=t.length;r<i;++r)if(t[r].name===n){t[r]=tt,t=t.slice(0,r).concat(t.slice(r+1));break}return null!=e&&t.push({name:n,value:e}),t}et.prototype=nt.prototype={constructor:et,on:function(t,n){var e,r,i=this._,o=(r=i,(t+"").trim().split(/^|\s+/).map((function(t){var n="",e=t.indexOf(".");if(e>=0&&(n=t.slice(e+1),t=t.slice(0,e)),t&&!r.hasOwnProperty(t))throw new Error("unknown type: "+t);return{type:t,name:n}}))),a=-1,u=o.length;if(!(arguments.length<2)){if(null!=n&&"function"!=typeof n)throw new Error("invalid callback: "+n);for(;++a<u;)if(e=(t=o[a]).type)i[e]=it(i[e],t.name,n);else if(null==n)for(e in i)i[e]=it(i[e],t.name,null);return this}for(;++a<u;)if((e=(t=o[a]).type)&&(e=rt(i[e],t.name)))return e},copy:function(){var t={},n=this._;for(var e in n)t[e]=n[e].slice();return new et(t)},call:function(t,n){if((e=arguments.length-2)>0)for(var e,r,i=new Array(e),o=0;o<e;++o)i[o]=arguments[o+2];if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(o=0,e=(r=this._[t]).length;o<e;++o)r[o].value.apply(n,i)},apply:function(t,n,e){if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(var r=this._[t],i=0,o=r.length;i<o;++i)r[i].value.apply(n,e)}};var ot,at,ut=0,st=0,ft=0,lt=0,ct=0,ht=0,dt="object"==typeof performance&&performance.now?performance:Date,pt="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function gt(){return ct||(pt(vt),ct=dt.now()+ht)}function vt(){ct=0}function yt(){this._call=this._time=this._next=null}function bt(t,n,e){var r=new yt;return r.restart(t,n,e),r}function mt(){ct=(lt=dt.now())+ht,ut=st=0;try{!function(){gt(),++ut;for(var t,n=ot;n;)(t=ct-n._time)>=0&&n._call.call(void 0,t),n=n._next;--ut}()}finally{ut=0,function(){var t,n,e=ot,r=1/0;for(;e;)e._call?(r>e._time&&(r=e._time),t=e,e=e._next):(n=e._next,e._next=null,e=t?t._next=n:ot=n);at=t,wt(r)}(),ct=0}}function _t(){var t=dt.now(),n=t-lt;n>1e3&&(ht-=n,lt=t)}function wt(t){ut||(st&&(st=clearTimeout(st)),t-ct>24?(t<1/0&&(st=setTimeout(mt,t-dt.now()-ht)),ft&&(ft=clearInterval(ft))):(ft||(lt=dt.now(),ft=setInterval(_t,1e3)),ut=1,pt(mt)))}yt.prototype=bt.prototype={constructor:yt,restart:function(t,n,e){if("function"!=typeof t)throw new TypeError("callback is not a function");e=(null==e?gt():+e)+(null==n?0:+n),this._next||at===this||(at?at._next=this:ot=this,at=this),this._call=t,this._time=e,wt()},stop:function(){this._call&&(this._call=null,this._time=1/0,wt())}};const xt=4294967296;function kt(t){return t.x}function Mt(t){return t.y}function At(t){return t.z}var Ot,jt,Nt,St,Ct=Math.PI*(3-Math.sqrt(5)),Pt=20*Math.PI/(9+Math.sqrt(221));function zt(t,n){n=n||2;var e,r=Math.min(3,Math.max(1,Math.round(n))),i=1,o=.001,a=1-Math.pow(o,1/300),u=0,s=.6,f=new Map,l=bt(d),c=nt("tick","end"),h=function(){let t=1;return()=>(t=(1664525*t+1013904223)%xt)/xt}();function d(){p(),c.call("tick",e),i<o&&(l.stop(),c.call("end",e))}function p(n){var o,l,c=t.length;void 0===n&&(n=1);for(var h=0;h<n;++h)for(i+=(u-i)*a,f.forEach((function(t){t(i)})),o=0;o<c;++o)null==(l=t[o]).fx?l.x+=l.vx*=s:(l.x=l.fx,l.vx=0),r>1&&(null==l.fy?l.y+=l.vy*=s:(l.y=l.fy,l.vy=0)),r>2&&(null==l.fz?l.z+=l.vz*=s:(l.z=l.fz,l.vz=0));return e}function g(){for(var n,e=0,i=t.length;e<i;++e){if((n=t[e]).index=e,null!=n.fx&&(n.x=n.fx),null!=n.fy&&(n.y=n.fy),null!=n.fz&&(n.z=n.fz),isNaN(n.x)||r>1&&isNaN(n.y)||r>2&&isNaN(n.z)){var o=10*(r>2?Math.cbrt(.5+e):r>1?Math.sqrt(.5+e):e),a=e*Ct,u=e*Pt;1===r?n.x=o:2===r?(n.x=o*Math.cos(a),n.y=o*Math.sin(a)):(n.x=o*Math.sin(a)*Math.cos(u),n.y=o*Math.cos(a),n.z=o*Math.sin(a)*Math.sin(u))}(isNaN(n.vx)||r>1&&isNaN(n.vy)||r>2&&isNaN(n.vz))&&(n.vx=0,r>1&&(n.vy=0),r>2&&(n.vz=0))}}function v(n){return n.initialize&&n.initialize(t,h,r),n}return null==t&&(t=[]),g(),e={tick:p,restart:function(){return l.restart(d),e},stop:function(){return l.stop(),e},numDimensions:function(t){return arguments.length?(r=Math.min(3,Math.max(1,Math.round(t))),f.forEach(v),e):r},nodes:function(n){return arguments.length?(t=n,g(),f.forEach(v),e):t},alpha:function(t){return arguments.length?(i=+t,e):i},alphaMin:function(t){return arguments.length?(o=+t,e):o},alphaDecay:function(t){return arguments.length?(a=+t,e):+a},alphaTarget:function(t){return arguments.length?(u=+t,e):u},velocityDecay:function(t){return arguments.length?(s=1-t,e):1-s},randomSource:function(t){return arguments.length?(h=t,f.forEach(v),e):h},force:function(t,n){return arguments.length>1?(null==n?f.delete(t):f.set(t,v(n)),e):f.get(t)},find:function(){var n,e,i,o,a,u,s=Array.prototype.slice.call(arguments),f=s.shift()||0,l=(r>1?s.shift():null)||0,c=(r>2?s.shift():null)||0,h=s.shift()||1/0,d=0,p=t.length;for(h*=h,d=0;d<p;++d)(o=(n=f-(a=t[d]).x)*n+(e=l-(a.y||0))*e+(i=c-(a.z||0))*i)<h&&(u=a,h=o);return u},on:function(t,n){return arguments.length>1?(c.on(t,n),e):c.on(t)}}}function Et(){var t,n,e,r,i,o,a=X(-30),u=1,s=1/0,f=.81;function l(r){var o,a=t.length,u=(1===n?j(t,kt):2===n?T(t,kt,Mt):3===n?G(t,kt,Mt,At):null).visitAfter(h);for(i=r,o=0;o<a;++o)e=t[o],u.visit(d)}function c(){if(t){var n,e,r=t.length;for(o=new Array(r),n=0;n<r;++n)e=t[n],o[e.index]=+a(e,n,t)}}function h(t){var e,r,i,a,u,s,f=0,l=0,c=t.length;if(c){for(i=a=u=s=0;s<c;++s)(e=t[s])&&(r=Math.abs(e.value))&&(f+=e.value,l+=r,i+=r*(e.x||0),a+=r*(e.y||0),u+=r*(e.z||0));f*=Math.sqrt(4/c),t.x=i/l,n>1&&(t.y=a/l),n>2&&(t.z=u/l)}else{(e=t).x=e.data.x,n>1&&(e.y=e.data.y),n>2&&(e.z=e.data.z);do{f+=o[e.data.index]}while(e=e.next)}t.value=f}function d(t,a,l,c,h){if(!t.value)return!0;var d=[l,c,h][n-1],p=t.x-e.x,g=n>1?t.y-e.y:0,v=n>2?t.z-e.z:0,y=d-a,b=p*p+g*g+v*v;if(y*y/f<b)return b<s&&(0===p&&(b+=(p=J(r))*p),n>1&&0===g&&(b+=(g=J(r))*g),n>2&&0===v&&(b+=(v=J(r))*v),b<u&&(b=Math.sqrt(u*b)),e.vx+=p*t.value*i/b,n>1&&(e.vy+=g*t.value*i/b),n>2&&(e.vz+=v*t.value*i/b)),!0;if(!(t.length||b>=s)){(t.data!==e||t.next)&&(0===p&&(b+=(p=J(r))*p),n>1&&0===g&&(b+=(g=J(r))*g),n>2&&0===v&&(b+=(v=J(r))*v),b<u&&(b=Math.sqrt(u*b)));do{t.data!==e&&(y=o[t.data.index]*i/b,e.vx+=p*y,n>1&&(e.vy+=g*y),n>2&&(e.vz+=v*y))}while(t=t.next)}}return l.initialize=function(e,...i){t=e,r=i.find((t=>"function"==typeof t))||Math.random,n=i.find((t=>[1,2,3].includes(t)))||2,c()},l.strength=function(t){return arguments.length?(a="function"==typeof t?t:X(+t),c(),l):a},l.distanceMin=function(t){return arguments.length?(u=t*t,l):Math.sqrt(u)},l.distanceMax=function(t){return arguments.length?(s=t*t,l):Math.sqrt(s)},l.theta=function(t){return arguments.length?(f=t*t,l):Math.sqrt(f)},l}function Bt(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}function Tt(){if(jt)return Ot;return jt=1,Ot=function(t){!function(t){if(!t)throw new Error("Eventify cannot use falsy object as events subject");for(var n=["on","fire","off"],e=0;e<n.length;++e)if(t.hasOwnProperty(n[e]))throw new Error("Subject cannot be eventified, since it already has property '"+n[e]+"'")}(t);var n=function(t){var n=Object.create(null);return{on:function(e,r,i){if("function"!=typeof r)throw new Error("callback is expected to be a function");var o=n[e];return o||(o=n[e]=[]),o.push({callback:r,ctx:i}),t},off:function(e,r){if(void 0===e)return n=Object.create(null),t;if(n[e])if("function"!=typeof r)delete n[e];else for(var i=n[e],o=0;o<i.length;++o)i[o].callback===r&&i.splice(o,1);return t},fire:function(e){var r,i=n[e];if(!i)return t;arguments.length>1&&(r=Array.prototype.splice.call(arguments,1));for(var o=0;o<i.length;++o){var a=i[o];a.callback.apply(a.ctx,r)}return t}}}(t);return t.on=n.on,t.off=n.off,t.fire=n.fire,t},Ot}var Dt,$t,It,Lt,Ft,Rt=function(){if(St)return Nt;St=1,Nt=function(o){"uniqueLinkId"in(o=o||{})&&(console.warn("ngraph.graph: Starting from version 0.14 `uniqueLinkId` is deprecated.\nUse `multigraph` option instead\n","\n","Note: there is also change in default behavior: From now on each graph\nis considered to be not a multigraph by default (each edge is unique)."),o.multigraph=o.uniqueLinkId);void 0===o.multigraph&&(o.multigraph=!1);if("function"!=typeof Map)throw new Error("ngraph.graph requires `Map` to be defined. Please polyfill it before using ngraph");var a=new Map,u=new Map,s={},f=0,l=o.multigraph?function(t,n,e){var o=i(t,n),a=s.hasOwnProperty(o);if(a||A(t,n)){a||(s[o]=0);var u="@"+ ++s[o];o=i(t+u,n+u)}return new r(t,n,e,o)}:function(t,n,e){var o=i(t,n),a=u.get(o);if(a)return a.data=e,a;return new r(t,n,e,o)},c=[],h=O,d=O,p=O,g=O,v={version:20,addNode:m,addLink:function(t,n,r){p();var i=_(t)||m(t),o=_(n)||m(n),a=l(t,n,r),s=u.has(a.id);u.set(a.id,a),e(i,a),t!==n&&e(o,a);return h(a,s?"update":"add"),g(),a},removeLink:function(t,n){void 0!==n&&(t=A(t,n));return M(t)},removeNode:w,getNode:_,getNodeCount:x,getLinkCount:k,getEdgeCount:k,getLinksCount:k,getNodesCount:x,getLinks:function(t){var n=_(t);return n?n.links:null},forEachNode:S,forEachLinkedNode:function(t,n,e){var r=_(t);if(r&&r.links&&"function"==typeof n)return e?function(t,n,e){var r=t.values(),i=r.next();for(;!i.done;){var o=i.value;if(o.fromId===n&&e(a.get(o.toId),o))return!0;i=r.next()}}(r.links,t,n):function(t,n,e){var r=t.values(),i=r.next();for(;!i.done;){var o=i.value,u=o.fromId===n?o.toId:o.fromId;if(e(a.get(u),o))return!0;i=r.next()}}(r.links,t,n)},forEachLink:function(t){if("function"==typeof t)for(var n=u.values(),e=n.next();!e.done;){if(t(e.value))return!0;e=n.next()}},beginUpdate:p,endUpdate:g,clear:function(){p(),S((function(t){w(t.id)})),g()},hasLink:A,hasNode:_,getLink:A};return t(v),function(){var t=v.on;function n(){return v.beginUpdate=p=j,v.endUpdate=g=N,h=y,d=b,v.on=t,t.apply(v,arguments)}v.on=n}(),v;function y(t,n){c.push({link:t,changeType:n})}function b(t,n){c.push({node:t,changeType:n})}function m(t,e){if(void 0===t)throw new Error("Invalid node identifier");p();var r=_(t);return r?(r.data=e,d(r,"update")):(r=new n(t,e),d(r,"add")),a.set(t,r),g(),r}function _(t){return a.get(t)}function w(t){var n=_(t);if(!n)return!1;p();var e=n.links;return e&&(e.forEach(M),n.links=null),a.delete(t),d(n,"remove"),g(),!0}function x(){return a.size}function k(){return u.size}function M(t){if(!t)return!1;if(!u.get(t.id))return!1;p(),u.delete(t.id);var n=_(t.fromId),e=_(t.toId);return n&&n.links.delete(t),e&&e.links.delete(t),h(t,"remove"),g(),!0}function A(t,n){if(void 0!==t&&void 0!==n)return u.get(i(t,n))}function O(){}function j(){f+=1}function N(){0===(f-=1)&&c.length>0&&(v.fire("changed",c),c.length=0)}function S(t){if("function"!=typeof t)throw new Error("Function is expected to iterate over graph nodes. You passed "+t);for(var n=a.values(),e=n.next();!e.done;){if(t(e.value))return!0;e=n.next()}}};var t=Tt();function n(t,n){this.id=t,this.links=null,this.data=n}function e(t,n){t.links?t.links.add(n):t.links=new Set([n])}function r(t,n,e,r){this.fromId=t,this.toId=n,this.data=e,this.id=r}function i(t,n){return t.toString()+"👉 "+n.toString()}return Nt}(),qt=Bt(Rt),Ut={exports:{}},Vt={exports:{}};function Gt(){return $t||($t=1,Dt=function(t){return 0===t?"x":1===t?"y":2===t?"z":"c"+(t+1)}),Dt}function Ht(){if(Lt)return It;Lt=1;const t=Gt();return It=function(n){return function(e,r){let i=r&&r.indent||0,o=r&&void 0!==r.join?r.join:"\n",a=Array(i+1).join(" "),u=[];for(let r=0;r<n;++r){let n=t(r),i=0===r?"":a;u.push(i+e.replace(/{var}/g,n))}return u.join(o)}}}var Wt,Qt={exports:{}};var Xt,Jt={exports:{}};var Yt,Kt={exports:{}};var Zt,tn={exports:{}};var nn,en,rn,on,an,un={exports:{}};var sn,fn,ln,cn,hn={exports:{}};function dn(){if(ln)return fn;ln=1,fn=function(s){var f=rn?en:(rn=1,en=function(t,n,e,r){this.from=t,this.to=n,this.length=e,this.coefficient=r}),l=(an||(an=1,on=function t(n,e){var r;if(n||(n={}),e)for(r in e)if(e.hasOwnProperty(r)){var i=n.hasOwnProperty(r),o=typeof e[r];i&&typeof n[r]===o?"object"===o&&(n[r]=t(n[r],e[r])):n[r]=e[r]}return n}),on),c=Tt();if(s){if(void 0!==s.springCoeff)throw new Error("springCoeff was renamed to springCoefficient");if(void 0!==s.dragCoeff)throw new Error("dragCoeff was renamed to dragCoefficient")}s=l(s,{springLength:10,springCoefficient:.8,gravity:-12,theta:.8,dragCoefficient:.9,timeStep:.5,adaptiveTimeStepWeight:0,dimensions:2,debug:!1});var h=a[s.dimensions];if(!h){var d=s.dimensions;h={Body:t(d,s.debug),createQuadTree:n(d),createBounds:e(d),createDragForce:r(d),createSpringForce:i(d),integrate:o(d)},a[d]=h}var p=h.Body,g=h.createQuadTree,v=h.createBounds,y=h.createDragForce,b=h.createSpringForce,m=h.integrate,_=function(){if(sn)return hn.exports;function t(t){return new n("number"==typeof t?t:+new Date)}function n(t){this.seed=t}function e(t){return Math.sqrt(2*Math.PI/t)*Math.pow(1/Math.E*(t+1/(12*t-1/(10*t))),t)}function r(){var t=this.seed;return t=4294967295&(3042594569^(t=4251993797+(t=4294967295&(3550635116+(t=374761393+(t=4294967295&(3345072700^(t=t+2127912214+(t<<12)&4294967295)^t>>>19))+(t<<5)&4294967295)^t<<9))+(t<<3)&4294967295)^t>>>16),this.seed=t,(268435455&t)/268435456}return sn=1,hn.exports=t,hn.exports.random=t,hn.exports.randomIterator=function(n,e){var r=e||t();if("function"!=typeof r.next)throw new Error("customRandom does not match expected API: next() function is missing");return{forEach:function(t){var e,i,o;for(e=n.length-1;e>0;--e)i=r.next(e+1),o=n[i],n[i]=n[e],n[e]=o,t(o);n.length&&t(n[0])},shuffle:function(){var t,e,i;for(t=n.length-1;t>0;--t)e=r.next(t+1),i=n[e],n[e]=n[t],n[t]=i;return n}}},n.prototype.next=function(t){return Math.floor(this.nextDouble()*t)},n.prototype.nextDouble=r,n.prototype.uniform=r,n.prototype.gaussian=function(){var t,n,e;do{t=(n=2*this.nextDouble()-1)*n+(e=2*this.nextDouble()-1)*e}while(t>=1||0===t);return n*Math.sqrt(-2*Math.log(t)/t)},n.prototype.levy=function(){var t=1.5,n=Math.pow(e(2.5)*Math.sin(Math.PI*t/2)/(e(1.25)*t*Math.pow(2,.25)),1/t);return this.gaussian()*n/Math.pow(Math.abs(this.gaussian()),1/t)},hn.exports}().random(42),w=[],x=[],k=g(s,_),M=v(w,s,_),A=b(s,_),O=y(s),j=[],N=new Map,S=0;z("nbody",(function(){if(0===w.length)return;k.insertBodies(w);var t=w.length;for(;t--;){var n=w[t];n.isPinned||(n.reset(),k.updateBodyForce(n),O.update(n))}})),z("spring",(function(){var t=x.length;for(;t--;)A.update(x[t])}));var C={bodies:w,quadTree:k,springs:x,settings:s,addForce:z,removeForce:function(t){var n=j.indexOf(N.get(t));if(n<0)return;j.splice(n,1),N.delete(t)},getForces:function(){return N},step:function(){for(var t=0;t<j.length;++t)j[t](S);var n=m(w,s.timeStep,s.adaptiveTimeStepWeight);return S+=1,n},addBody:function(t){if(!t)throw new Error("Body is required");return w.push(t),t},addBodyAt:function(t){if(!t)throw new Error("Body position is required");var n=(t=>new p(t))(t);return w.push(n),n},removeBody:function(t){if(t){var n=w.indexOf(t);if(!(n<0))return w.splice(n,1),0===w.length&&M.reset(),!0}},addSpring:function(t,n,e,r){if(!t||!n)throw new Error("Cannot add null spring to force simulator");"number"!=typeof e&&(e=-1);var i=new f(t,n,e,r>=0?r:-1);return x.push(i),i},getTotalMovement:function(){return 0},removeSpring:function(t){if(t){var n=x.indexOf(t);return n>-1?(x.splice(n,1),!0):void 0}},getBestNewBodyPosition:function(t){return M.getBestNewPosition(t)},getBBox:P,getBoundingBox:P,invalidateBBox:function(){console.warn("invalidateBBox() is deprecated, bounds always recomputed on `getBBox()` call")},gravity:function(t){return void 0!==t?(s.gravity=t,k.options({gravity:t}),this):s.gravity},theta:function(t){return void 0!==t?(s.theta=t,k.options({theta:t}),this):s.theta},random:_};return function(t,n){for(var e in t)u(t,n,e)}(s,C),c(C),C;function P(){return M.update(),M.box}function z(t,n){if(N.has(t))throw new Error("Force "+t+" is already added");N.set(t,n),j.push(n)}};var t=function(){if(Ft)return Vt.exports;Ft=1;const t=Ht();function n(t,n){return`\n${r(t,n)}\n${e(t)}\nreturn {Body: Body, Vector: Vector};\n`}function e(n){let e=t(n),r=e("{var}",{join:", "});return`\nfunction Body(${r}) {\n  this.isPinned = false;\n  this.pos = new Vector(${r});\n  this.force = new Vector();\n  this.velocity = new Vector();\n  this.mass = 1;\n\n  this.springCount = 0;\n  this.springLength = 0;\n}\n\nBody.prototype.reset = function() {\n  this.force.reset();\n  this.springCount = 0;\n  this.springLength = 0;\n}\n\nBody.prototype.setPosition = function (${r}) {\n  ${e("this.pos.{var} = {var} || 0;",{indent:2})}\n};`}function r(n,e){let r=t(n),i="";return e&&(i=`${r("\n\t   var v{var};\n\tObject.defineProperty(this, '{var}', {\n\t  set: function(v) { \n\t    if (!Number.isFinite(v)) throw new Error('Cannot set non-numbers to {var}');\n\t    v{var} = v; \n\t  },\n\t  get: function() { return v{var}; }\n\t});")}`),`function Vector(${r("{var}",{join:", "})}) {\n  ${i}\n    if (typeof arguments[0] === 'object') {\n      // could be another vector\n      let v = arguments[0];\n      ${r('if (!Number.isFinite(v.{var})) throw new Error("Expected value is not a finite number at Vector constructor ({var})");',{indent:4})}\n      ${r("this.{var} = v.{var};",{indent:4})}\n    } else {\n      ${r('this.{var} = typeof {var} === "number" ? {var} : 0;',{indent:4})}\n    }\n  }\n  \n  Vector.prototype.reset = function () {\n    ${r("this.{var} = ",{join:""})}0;\n  };`}return Vt.exports=function(t,e){let r=n(t,e),{Body:i}=new Function(r)();return i},Vt.exports.generateCreateBodyFunctionBody=n,Vt.exports.getVectorCode=r,Vt.exports.getBodyCode=e,Vt.exports}(),n=function(){if(Wt)return Qt.exports;Wt=1;const t=Ht(),n=Gt();function e(e){let s=t(e),f=Math.pow(2,e);return`\n${u()}\n${a(e)}\n${r(e)}\n${o(e)}\n${i(e)}\n\nfunction createQuadTree(options, random) {\n  options = options || {};\n  options.gravity = typeof options.gravity === 'number' ? options.gravity : -1;\n  options.theta = typeof options.theta === 'number' ? options.theta : 0.8;\n\n  var gravity = options.gravity;\n  var updateQueue = [];\n  var insertStack = new InsertStack();\n  var theta = options.theta;\n\n  var nodesCache = [];\n  var currentInCache = 0;\n  var root = newNode();\n\n  return {\n    insertBodies: insertBodies,\n\n    /**\n     * Gets root node if it is present\n     */\n    getRoot: function() {\n      return root;\n    },\n\n    updateBodyForce: update,\n\n    options: function(newOptions) {\n      if (newOptions) {\n        if (typeof newOptions.gravity === 'number') {\n          gravity = newOptions.gravity;\n        }\n        if (typeof newOptions.theta === 'number') {\n          theta = newOptions.theta;\n        }\n\n        return this;\n      }\n\n      return {\n        gravity: gravity,\n        theta: theta\n      };\n    }\n  };\n\n  function newNode() {\n    // To avoid pressure on GC we reuse nodes.\n    var node = nodesCache[currentInCache];\n    if (node) {\n${function(t){let n=[];for(let e=0;e<f;++e)n.push(`${t}quad${e} = null;`);return n.join("\n")}("      node.")}\n      node.body = null;\n      node.mass = ${s("node.mass_{var} = ",{join:""})}0;\n      ${s("node.min_{var} = node.max_{var} = ",{join:""})}0;\n    } else {\n      node = new QuadNode();\n      nodesCache[currentInCache] = node;\n    }\n\n    ++currentInCache;\n    return node;\n  }\n\n  function update(sourceBody) {\n    var queue = updateQueue;\n    var v;\n    ${s("var d{var};",{indent:4})}\n    var r; \n    ${s("var f{var} = 0;",{indent:4})}\n    var queueLength = 1;\n    var shiftIdx = 0;\n    var pushIdx = 1;\n\n    queue[0] = root;\n\n    while (queueLength) {\n      var node = queue[shiftIdx];\n      var body = node.body;\n\n      queueLength -= 1;\n      shiftIdx += 1;\n      var differentBody = (body !== sourceBody);\n      if (body && differentBody) {\n        // If the current node is a leaf node (and it is not source body),\n        // calculate the force exerted by the current node on body, and add this\n        // amount to body's net force.\n        ${s("d{var} = body.pos.{var} - sourceBody.pos.{var};",{indent:8})}\n        r = Math.sqrt(${s("d{var} * d{var}",{join:" + "})});\n\n        if (r === 0) {\n          // Poor man's protection against zero distance.\n          ${s("d{var} = (random.nextDouble() - 0.5) / 50;",{indent:10})}\n          r = Math.sqrt(${s("d{var} * d{var}",{join:" + "})});\n        }\n\n        // This is standard gravitation force calculation but we divide\n        // by r^3 to save two operations when normalizing force vector.\n        v = gravity * body.mass * sourceBody.mass / (r * r * r);\n        ${s("f{var} += v * d{var};",{indent:8})}\n      } else if (differentBody) {\n        // Otherwise, calculate the ratio s / r,  where s is the width of the region\n        // represented by the internal node, and r is the distance between the body\n        // and the node's center-of-mass\n        ${s("d{var} = node.mass_{var} / node.mass - sourceBody.pos.{var};",{indent:8})}\n        r = Math.sqrt(${s("d{var} * d{var}",{join:" + "})});\n\n        if (r === 0) {\n          // Sorry about code duplication. I don't want to create many functions\n          // right away. Just want to see performance first.\n          ${s("d{var} = (random.nextDouble() - 0.5) / 50;",{indent:10})}\n          r = Math.sqrt(${s("d{var} * d{var}",{join:" + "})});\n        }\n        // If s / r < θ, treat this internal node as a single body, and calculate the\n        // force it exerts on sourceBody, and add this amount to sourceBody's net force.\n        if ((node.max_${n(0)} - node.min_${n(0)}) / r < theta) {\n          // in the if statement above we consider node's width only\n          // because the region was made into square during tree creation.\n          // Thus there is no difference between using width or height.\n          v = gravity * node.mass * sourceBody.mass / (r * r * r);\n          ${s("f{var} += v * d{var};",{indent:10})}\n        } else {\n          // Otherwise, run the procedure recursively on each of the current node's children.\n\n          // I intentionally unfolded this loop, to save several CPU cycles.\n${function(){let t=Array(11).join(" "),n=[];for(let e=0;e<f;++e)n.push(t+`if (node.quad${e}) {`),n.push(t+`  queue[pushIdx] = node.quad${e};`),n.push(t+"  queueLength += 1;"),n.push(t+"  pushIdx += 1;"),n.push(t+"}");return n.join("\n")}()}\n        }\n      }\n    }\n\n    ${s("sourceBody.force.{var} += f{var};",{indent:4})}\n  }\n\n  function insertBodies(bodies) {\n    ${s("var {var}min = Number.MAX_VALUE;",{indent:4})}\n    ${s("var {var}max = Number.MIN_VALUE;",{indent:4})}\n    var i = bodies.length;\n\n    // To reduce quad tree depth we are looking for exact bounding box of all particles.\n    while (i--) {\n      var pos = bodies[i].pos;\n      ${s("if (pos.{var} < {var}min) {var}min = pos.{var};",{indent:6})}\n      ${s("if (pos.{var} > {var}max) {var}max = pos.{var};",{indent:6})}\n    }\n\n    // Makes the bounds square.\n    var maxSideLength = -Infinity;\n    ${s("if ({var}max - {var}min > maxSideLength) maxSideLength = {var}max - {var}min ;",{indent:4})}\n\n    currentInCache = 0;\n    root = newNode();\n    ${s("root.min_{var} = {var}min;",{indent:4})}\n    ${s("root.max_{var} = {var}min + maxSideLength;",{indent:4})}\n\n    i = bodies.length - 1;\n    if (i >= 0) {\n      root.body = bodies[i];\n    }\n    while (i--) {\n      insert(bodies[i], root);\n    }\n  }\n\n  function insert(newBody) {\n    insertStack.reset();\n    insertStack.push(root, newBody);\n\n    while (!insertStack.isEmpty()) {\n      var stackItem = insertStack.pop();\n      var node = stackItem.node;\n      var body = stackItem.body;\n\n      if (!node.body) {\n        // This is internal node. Update the total mass of the node and center-of-mass.\n        ${s("var {var} = body.pos.{var};",{indent:8})}\n        node.mass += body.mass;\n        ${s("node.mass_{var} += body.mass * {var};",{indent:8})}\n\n        // Recursively insert the body in the appropriate quadrant.\n        // But first find the appropriate quadrant.\n        var quadIdx = 0; // Assume we are in the 0's quad.\n        ${s("var min_{var} = node.min_{var};",{indent:8})}\n        ${s("var max_{var} = (min_{var} + node.max_{var}) / 2;",{indent:8})}\n\n${function(){let t=[],r=Array(8+1).join(" ");for(let i=0;i<e;++i)t.push(r+`if (${n(i)} > max_${n(i)}) {`),t.push(r+`  quadIdx = quadIdx + ${Math.pow(2,i)};`),t.push(r+`  min_${n(i)} = max_${n(i)};`),t.push(r+`  max_${n(i)} = node.max_${n(i)};`),t.push(r+"}");return t.join("\n")}()}\n\n        var child = getChild(node, quadIdx);\n\n        if (!child) {\n          // The node is internal but this quadrant is not taken. Add\n          // subnode to it.\n          child = newNode();\n          ${s("child.min_{var} = min_{var};",{indent:10})}\n          ${s("child.max_{var} = max_{var};",{indent:10})}\n          child.body = body;\n\n          setChild(node, quadIdx, child);\n        } else {\n          // continue searching in this quadrant.\n          insertStack.push(child, body);\n        }\n      } else {\n        // We are trying to add to the leaf node.\n        // We have to convert current leaf into internal node\n        // and continue adding two nodes.\n        var oldBody = node.body;\n        node.body = null; // internal nodes do not cary bodies\n\n        if (isSamePosition(oldBody.pos, body.pos)) {\n          // Prevent infinite subdivision by bumping one node\n          // anywhere in this quadrant\n          var retriesCount = 3;\n          do {\n            var offset = random.nextDouble();\n            ${s("var d{var} = (node.max_{var} - node.min_{var}) * offset;",{indent:12})}\n\n            ${s("oldBody.pos.{var} = node.min_{var} + d{var};",{indent:12})}\n            retriesCount -= 1;\n            // Make sure we don't bump it out of the box. If we do, next iteration should fix it\n          } while (retriesCount > 0 && isSamePosition(oldBody.pos, body.pos));\n\n          if (retriesCount === 0 && isSamePosition(oldBody.pos, body.pos)) {\n            // This is very bad, we ran out of precision.\n            // if we do not return from the method we'll get into\n            // infinite loop here. So we sacrifice correctness of layout, and keep the app running\n            // Next layout iteration should get larger bounding box in the first step and fix this\n            return;\n          }\n        }\n        // Next iteration should subdivide node further.\n        insertStack.push(node, oldBody);\n        insertStack.push(node, body);\n      }\n    }\n  }\n}\nreturn createQuadTree;\n\n`}function r(n){let e=t(n);return`\n  function isSamePosition(point1, point2) {\n    ${e("var d{var} = Math.abs(point1.{var} - point2.{var});",{indent:2})}\n  \n    return ${e("d{var} < 1e-8",{join:" && "})};\n  }  \n`}function i(t){var n=Math.pow(2,t);return`\nfunction setChild(node, idx, child) {\n  ${function(){let t=[];for(let e=0;e<n;++e){let n=0===e?"  ":"  else ";t.push(`${n}if (idx === ${e}) node.quad${e} = child;`)}return t.join("\n")}()}\n}`}function o(t){return`function getChild(node, idx) {\n${function(){let n=[],e=Math.pow(2,t);for(let t=0;t<e;++t)n.push(`  if (idx === ${t}) return node.quad${t};`);return n.join("\n")}()}\n  return null;\n}`}function a(n){let e=t(n),r=Math.pow(2,n);return`\nfunction QuadNode() {\n  // body stored inside this node. In quad tree only leaf nodes (by construction)\n  // contain bodies:\n  this.body = null;\n\n  // Child nodes are stored in quads. Each quad is presented by number:\n  // 0 | 1\n  // -----\n  // 2 | 3\n${function(t){let n=[];for(let e=0;e<r;++e)n.push(`${t}quad${e} = null;`);return n.join("\n")}("  this.")}\n\n  // Total mass of current node\n  this.mass = 0;\n\n  // Center of mass coordinates\n  ${e("this.mass_{var} = 0;",{indent:2})}\n\n  // bounding box coordinates\n  ${e("this.min_{var} = 0;",{indent:2})}\n  ${e("this.max_{var} = 0;",{indent:2})}\n}\n`}function u(){return"\n/**\n * Our implementation of QuadTree is non-recursive to avoid GC hit\n * This data structure represent stack of elements\n * which we are trying to insert into quad tree.\n */\nfunction InsertStack () {\n    this.stack = [];\n    this.popIdx = 0;\n}\n\nInsertStack.prototype = {\n    isEmpty: function() {\n        return this.popIdx === 0;\n    },\n    push: function (node, body) {\n        var item = this.stack[this.popIdx];\n        if (!item) {\n            // we are trying to avoid memory pressure: create new element\n            // only when absolutely necessary\n            this.stack[this.popIdx] = new InsertStackElement(node, body);\n        } else {\n            item.node = node;\n            item.body = body;\n        }\n        ++this.popIdx;\n    },\n    pop: function () {\n        if (this.popIdx > 0) {\n            return this.stack[--this.popIdx];\n        }\n    },\n    reset: function () {\n        this.popIdx = 0;\n    }\n};\n\nfunction InsertStackElement(node, body) {\n    this.node = node; // QuadTree node\n    this.body = body; // physical body which needs to be inserted to node\n}\n"}return Qt.exports=function(t){let n=e(t);return new Function(n)()},Qt.exports.generateQuadTreeFunctionBody=e,Qt.exports.getInsertStackCode=u,Qt.exports.getQuadNodeCode=a,Qt.exports.isSamePosition=r,Qt.exports.getChildBodyCode=o,Qt.exports.setChildBodyCode=i,Qt.exports}(),e=function(){if(Xt)return Jt.exports;Xt=1,Jt.exports=function(t){let e=n(t);return new Function("bodies","settings","random",e)},Jt.exports.generateFunctionBody=n;const t=Ht();function n(n){let e=t(n);return`\n  var boundingBox = {\n    ${e("min_{var}: 0, max_{var}: 0,",{indent:4})}\n  };\n\n  return {\n    box: boundingBox,\n\n    update: updateBoundingBox,\n\n    reset: resetBoundingBox,\n\n    getBestNewPosition: function (neighbors) {\n      var ${e("base_{var} = 0",{join:", "})};\n\n      if (neighbors.length) {\n        for (var i = 0; i < neighbors.length; ++i) {\n          let neighborPos = neighbors[i].pos;\n          ${e("base_{var} += neighborPos.{var};",{indent:10})}\n        }\n\n        ${e("base_{var} /= neighbors.length;",{indent:8})}\n      } else {\n        ${e("base_{var} = (boundingBox.min_{var} + boundingBox.max_{var}) / 2;",{indent:8})}\n      }\n\n      var springLength = settings.springLength;\n      return {\n        ${e("{var}: base_{var} + (random.nextDouble() - 0.5) * springLength,",{indent:8})}\n      };\n    }\n  };\n\n  function updateBoundingBox() {\n    var i = bodies.length;\n    if (i === 0) return; // No bodies - no borders.\n\n    ${e("var max_{var} = -Infinity;",{indent:4})}\n    ${e("var min_{var} = Infinity;",{indent:4})}\n\n    while(i--) {\n      // this is O(n), it could be done faster with quadtree, if we check the root node bounds\n      var bodyPos = bodies[i].pos;\n      ${e("if (bodyPos.{var} < min_{var}) min_{var} = bodyPos.{var};",{indent:6})}\n      ${e("if (bodyPos.{var} > max_{var}) max_{var} = bodyPos.{var};",{indent:6})}\n    }\n\n    ${e("boundingBox.min_{var} = min_{var};",{indent:4})}\n    ${e("boundingBox.max_{var} = max_{var};",{indent:4})}\n  }\n\n  function resetBoundingBox() {\n    ${e("boundingBox.min_{var} = boundingBox.max_{var} = 0;",{indent:4})}\n  }\n`}return Jt.exports}(),r=function(){if(Yt)return Kt.exports;Yt=1;const t=Ht();function n(n){return`\n  if (!Number.isFinite(options.dragCoefficient)) throw new Error('dragCoefficient is not a finite number');\n\n  return {\n    update: function(body) {\n      ${t(n)("body.force.{var} -= options.dragCoefficient * body.velocity.{var};",{indent:6})}\n    }\n  };\n`}return Kt.exports=function(t){let e=n(t);return new Function("options",e)},Kt.exports.generateCreateDragForceFunctionBody=n,Kt.exports}(),i=function(){if(Zt)return tn.exports;Zt=1;const t=Ht();function n(n){let e=t(n);return`\n  if (!Number.isFinite(options.springCoefficient)) throw new Error('Spring coefficient is not a number');\n  if (!Number.isFinite(options.springLength)) throw new Error('Spring length is not a number');\n\n  return {\n    /**\n     * Updates forces acting on a spring\n     */\n    update: function (spring) {\n      var body1 = spring.from;\n      var body2 = spring.to;\n      var length = spring.length < 0 ? options.springLength : spring.length;\n      ${e("var d{var} = body2.pos.{var} - body1.pos.{var};",{indent:6})}\n      var r = Math.sqrt(${e("d{var} * d{var}",{join:" + "})});\n\n      if (r === 0) {\n        ${e("d{var} = (random.nextDouble() - 0.5) / 50;",{indent:8})}\n        r = Math.sqrt(${e("d{var} * d{var}",{join:" + "})});\n      }\n\n      var d = r - length;\n      var coefficient = ((spring.coefficient > 0) ? spring.coefficient : options.springCoefficient) * d / r;\n\n      ${e("body1.force.{var} += coefficient * d{var}",{indent:6})};\n      body1.springCount += 1;\n      body1.springLength += r;\n\n      ${e("body2.force.{var} -= coefficient * d{var}",{indent:6})};\n      body2.springCount += 1;\n      body2.springLength += r;\n    }\n  };\n`}return tn.exports=function(t){let e=n(t);return new Function("options","random",e)},tn.exports.generateCreateSpringForceFunctionBody=n,tn.exports}(),o=function(){if(nn)return un.exports;nn=1;const t=Ht();function n(n){let e=t(n);return`\n  var length = bodies.length;\n  if (length === 0) return 0;\n\n  ${e("var d{var} = 0, t{var} = 0;",{indent:2})}\n\n  for (var i = 0; i < length; ++i) {\n    var body = bodies[i];\n    if (body.isPinned) continue;\n\n    if (adaptiveTimeStepWeight && body.springCount) {\n      timeStep = (adaptiveTimeStepWeight * body.springLength/body.springCount);\n    }\n\n    var coeff = timeStep / body.mass;\n\n    ${e("body.velocity.{var} += coeff * body.force.{var};",{indent:4})}\n    ${e("var v{var} = body.velocity.{var};",{indent:4})}\n    var v = Math.sqrt(${e("v{var} * v{var}",{join:" + "})});\n\n    if (v > 1) {\n      // We normalize it so that we move within timeStep range. \n      // for the case when v <= 1 - we let velocity to fade out.\n      ${e("body.velocity.{var} = v{var} / v;",{indent:6})}\n    }\n\n    ${e("d{var} = timeStep * body.velocity.{var};",{indent:4})}\n\n    ${e("body.pos.{var} += d{var};",{indent:4})}\n\n    ${e("t{var} += Math.abs(d{var});",{indent:4})}\n  }\n\n  return (${e("t{var} * t{var}",{join:" + "})})/length;\n`}return un.exports=function(t){let e=n(t);return new Function("bodies","timeStep","adaptiveTimeStepWeight",e)},un.exports.generateIntegratorFunctionBody=n,un.exports}(),a={};function u(t,n,e){if(t.hasOwnProperty(e)&&"function"!=typeof n[e]){var r=Number.isFinite(t[e]);n[e]=r?function(r){if(void 0!==r){if(!Number.isFinite(r))throw new Error("Value of "+e+" should be a valid number.");return t[e]=r,n}return t[e]}:function(r){return void 0!==r?(t[e]=r,n):t[e]}}}return fn}var pn=function(){if(cn)return Ut.exports;cn=1,Ut.exports=function(e,r){if(!e)throw new Error("Graph structure cannot be undefined");var i=(r&&r.createSimulator||dn())(r);if(Array.isArray(r))throw new Error("Physics settings is expected to be an object");var o=e.version>19?function(t){var n=e.getLinks(t);return n?1+n.size/3:1}:function(t){var n=e.getLinks(t);return n?1+n.length/3:1};r&&"function"==typeof r.nodeMass&&(o=r.nodeMass);var a=new Map,u={},s=0,f=i.settings.springTransform||n;s=0,e.forEachNode((function(t){g(t.id),s+=1})),e.forEachLink(y),e.on("changed",p);var l=!1,c={step:function(){if(0===s)return h(!0),!0;var t=i.step();c.lastMove=t,c.fire("step");var n=t/s<=.01;return h(n),n},getNodePosition:function(t){return _(t).pos},setNodePosition:function(t){var n=_(t);n.setPosition.apply(n,Array.prototype.slice.call(arguments,1))},getLinkPosition:function(t){var n=u[t];if(n)return{from:n.from.pos,to:n.to.pos}},getGraphRect:function(){return i.getBBox()},forEachBody:d,pinNode:function(t,n){_(t.id).isPinned=!!n},isNodePinned:function(t){return _(t.id).isPinned},dispose:function(){e.off("changed",p),c.fire("disposed")},getBody:function(t){return a.get(t)},getSpring:function(t,n){var r;if(void 0===n)r="object"!=typeof t?t:t.id;else{var i=e.hasLink(t,n);if(!i)return;r=i.id}return u[r]},getForceVectorLength:function(){var t=0,n=0;return d((function(e){t+=Math.abs(e.force.x),n+=Math.abs(e.force.y)})),Math.sqrt(t*t+n*n)},simulator:i,graph:e,lastMove:0};return t(c),c;function h(t){var n;l!==t&&(l=t,n=t,c.fire("stable",n))}function d(t){a.forEach(t)}function p(t){for(var n=0;n<t.length;++n){var r=t[n];"add"===r.changeType?(r.node&&g(r.node.id),r.link&&y(r.link)):"remove"===r.changeType&&(r.node&&v(r.node),r.link&&b(r.link))}s=e.getNodesCount()}function g(t){var n=a.get(t);if(!n){var r=e.getNode(t);if(!r)throw new Error("initBody() was called with unknown node id");var o=r.position;if(!o){var u=function(t){var n=[];if(!t.links)return n;for(var e=Math.min(t.links.length,2),r=0;r<e;++r){var i=t.links[r],o=i.fromId!==t.id?a.get(i.fromId):a.get(i.toId);o&&o.pos&&n.push(o)}return n}(r);o=i.getBestNewBodyPosition(u)}(n=i.addBodyAt(o)).id=t,a.set(t,n),m(t),function(t){return t&&(t.isPinned||t.data&&t.data.isPinned)}(r)&&(n.isPinned=!0)}}function v(t){var n=t.id,e=a.get(n);e&&(a.delete(n),i.removeBody(e))}function y(t){m(t.fromId),m(t.toId);var n=a.get(t.fromId),e=a.get(t.toId),r=i.addSpring(n,e,t.length);f(t,r),u[t.id]=r}function b(t){var n=u[t.id];if(n){var r=e.getNode(t.fromId),o=e.getNode(t.toId);r&&m(r.id),o&&m(o.id),delete u[t.id],i.removeSpring(n)}}function m(t){var n=a.get(t);if(n.mass=o(t),Number.isNaN(n.mass))throw new Error("Node mass should be a number")}function _(t){var n=a.get(t);return n||(g(t),n=a.get(t)),n}},Ut.exports.simulator=dn();var t=Tt();function n(){}return Ut.exports}(),gn=Bt(pn);function vn(t){var n=typeof t;return null!=t&&("object"==n||"function"==n)}var yn="object"==typeof global&&global&&global.Object===Object&&global,bn="object"==typeof self&&self&&self.Object===Object&&self,mn=yn||bn||Function("return this")(),_n=function(){return mn.Date.now()},wn=/\s/;var xn=/^\s+/;function kn(t){return t?t.slice(0,function(t){for(var n=t.length;n--&&wn.test(t.charAt(n)););return n}(t)+1).replace(xn,""):t}var Mn=mn.Symbol,An=Object.prototype,On=An.hasOwnProperty,jn=An.toString,Nn=Mn?Mn.toStringTag:void 0;var Sn=Object.prototype.toString;var Cn=Mn?Mn.toStringTag:void 0;function Pn(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":Cn&&Cn in Object(t)?function(t){var n=On.call(t,Nn),e=t[Nn];try{t[Nn]=void 0;var r=!0}catch(t){}var i=jn.call(t);return r&&(n?t[Nn]=e:delete t[Nn]),i}(t):function(t){return Sn.call(t)}(t)}var zn=/^[-+]0x[0-9a-f]+$/i,En=/^0b[01]+$/i,Bn=/^0o[0-7]+$/i,Tn=parseInt;function Dn(t){if("number"==typeof t)return t;if(function(t){return"symbol"==typeof t||function(t){return null!=t&&"object"==typeof t}(t)&&"[object Symbol]"==Pn(t)}(t))return NaN;if(vn(t)){var n="function"==typeof t.valueOf?t.valueOf():t;t=vn(n)?n+"":n}if("string"!=typeof t)return 0===t?t:+t;t=kn(t);var e=En.test(t);return e||Bn.test(t)?Tn(t.slice(2),e?2:8):zn.test(t)?NaN:+t}var $n=Math.max,In=Math.min;function Ln(t,n,e){var r,i,o,a,u,s,f=0,l=!1,c=!1,h=!0;if("function"!=typeof t)throw new TypeError("Expected a function");function d(n){var e=r,o=i;return r=i=void 0,f=n,a=t.apply(o,e)}function p(t){var e=t-s;return void 0===s||e>=n||e<0||c&&t-f>=o}function g(){var t=_n();if(p(t))return v(t);u=setTimeout(g,function(t){var e=n-(t-s);return c?In(e,o-(t-f)):e}(t))}function v(t){return u=void 0,h&&r?d(t):(r=i=void 0,a)}function y(){var t=_n(),e=p(t);if(r=arguments,i=this,s=t,e){if(void 0===u)return function(t){return f=t,u=setTimeout(g,n),l?d(t):a}(s);if(c)return clearTimeout(u),u=setTimeout(g,n),d(s)}return void 0===u&&(u=setTimeout(g,n)),a}return n=Dn(n)||0,vn(e)&&(l=!!e.leading,o=(c="maxWait"in e)?$n(Dn(e.maxWait)||0,n):o,h="trailing"in e?!!e.trailing:h),y.cancel=function(){void 0!==u&&clearTimeout(u),f=0,r=s=i=u=void 0},y.flush=function(){return void 0===u?a:v(_n())},y}function Fn(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=Array(n);e<n;e++)r[e]=t[e];return r}function Rn(t,n,e){return Object.defineProperty(t,"prototype",{writable:!1}),t}function qn(t,n){return function(t){if(Array.isArray(t))return t}(t)||function(t,n){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var r,i,o,a,u=[],s=!0,f=!1;try{if(o=(e=e.call(t)).next,0===n);else for(;!(s=(r=o.call(e)).done)&&(u.push(r.value),u.length!==n);s=!0);}catch(t){f=!0,i=t}finally{try{if(!s&&null!=e.return&&(a=e.return(),Object(a)!==a))return}finally{if(f)throw i}}return u}}(t,n)||function(t,n){if(t){if("string"==typeof t)return Fn(t,n);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Fn(t,n):void 0}}(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Un=Rn((function t(n,e){var r=e.default,i=void 0===r?null:r,o=e.triggerUpdate,a=void 0===o||o,u=e.onChange,s=void 0===u?function(t,n){}:u;!function(t,n){if(!(t instanceof n))throw new TypeError("Cannot call a class as a function")}(this,t),this.name=n,this.defaultVal=i,this.triggerUpdate=a,this.onChange=s}));var Vn=function(t){return"function"==typeof t?t:"string"==typeof t?function(n){return n[t]}:function(n){return t}};class Gn extends Map{constructor(t,n=Wn){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:n}}),null!=t)for(const[n,e]of t)this.set(n,e)}get(t){return super.get(Hn(this,t))}has(t){return super.has(Hn(this,t))}set(t,n){return super.set(function({_intern:t,_key:n},e){const r=n(e);return t.has(r)?t.get(r):(t.set(r,e),e)}(this,t),n)}delete(t){return super.delete(function({_intern:t,_key:n},e){const r=n(e);t.has(r)&&(e=t.get(r),t.delete(r));return e}(this,t))}}function Hn({_intern:t,_key:n},e){const r=n(e);return t.has(r)?t.get(r):e}function Wn(t){return null!==t&&"object"==typeof t?t.valueOf():t}function Qn(t,n){let e;if(void 0===n)for(const n of t)null!=n&&(e<n||void 0===e&&n>=n)&&(e=n);else{let r=-1;for(let i of t)null!=(i=n(i,++r,t))&&(e<i||void 0===e&&i>=i)&&(e=i)}return e}function Xn(t,n){let e;if(void 0===n)for(const n of t)null!=n&&(e>n||void 0===e&&n>=n)&&(e=n);else{let r=-1;for(let i of t)null!=(i=n(i,++r,t))&&(e>i||void 0===e&&i>=i)&&(e=i)}return e}function Jn(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=Array(n);e<n;e++)r[e]=t[e];return r}function Yn(t,n,e){if("function"==typeof t?t===n:t.has(n))return arguments.length<3?n:e;throw new TypeError("Private element is not present on this object")}function Kn(t,n){return t.get(Yn(t,n))}function Zn(t,n,e){(function(t,n){if(n.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")})(t,n),n.set(t,e)}function te(t,n,e){return t.set(Yn(t,n),e),e}function ne(t,n,e){return n&&function(t,n){for(var e=0;e<n.length;e++){var r=n[e];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,ie(r.key),r)}}(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function ee(t,n){return function(t){if(Array.isArray(t))return t}(t)||function(t,n){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var r,i,o,a,u=[],s=!0,f=!1;try{if(o=(e=e.call(t)).next,0===n);else for(;!(s=(r=o.call(e)).done)&&(u.push(r.value),u.length!==n);s=!0);}catch(t){f=!0,i=t}finally{try{if(!s&&null!=e.return&&(a=e.return(),Object(a)!==a))return}finally{if(f)throw i}}return u}}(t,n)||oe(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function re(t){return function(t){if(Array.isArray(t))return Jn(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||oe(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ie(t){var n=function(t,n){if("object"!=typeof t||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,n);if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==typeof n?n:n+""}function oe(t,n){if(t){if("string"==typeof t)return Jn(t,n);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Jn(t,n):void 0}}var ae=new WeakMap,ue=new WeakMap,se=new WeakMap,fe=new WeakMap,le=new WeakMap,ce=new WeakMap,he=function(){return ne((function t(){!function(t,n){if(!(t instanceof n))throw new TypeError("Cannot call a class as a function")}(this,t),Zn(this,ae,new Map),Zn(this,ue,new Map),Zn(this,se,(function(t){return t})),Zn(this,fe,(function(){return{}})),Zn(this,le,(function(){})),Zn(this,ce,(function(){}))}),[{key:"getObj",value:function(t){return Kn(ae,this).get(Kn(se,this).call(this,t))}},{key:"getData",value:function(t){return Kn(ue,this).get(t)}},{key:"entries",value:function(){return re(Kn(ue,this).entries()).map((function(t){var n=ee(t,2),e=n[0];return[n[1],e]}))}},{key:"id",value:function(t){return te(se,this,Vn(t)),this}},{key:"onCreateObj",value:function(t){return te(fe,this,t),this}},{key:"onUpdateObj",value:function(t){return te(le,this,t),this}},{key:"onRemoveObj",value:function(t){return te(ce,this,t),this}},{key:"digest",value:function(t){var n=this;t.filter((function(t){return!Kn(ae,n).has(Kn(se,n).call(n,t))})).forEach((function(t){var e=Kn(fe,n).call(n,t);Kn(ae,n).set(Kn(se,n).call(n,t),e),Kn(ue,n).set(e,t)}));var e=new Map(t.map((function(t){return[Kn(se,n).call(n,t),t]})));return Kn(ae,this).forEach((function(t,r){e.has(r)?Kn(le,n).call(n,t,e.get(r)):(Kn(ce,n).call(n,t,r),Kn(ae,n).delete(r),Kn(ue,n).delete(t))})),this}},{key:"clear",value:function(){return this.digest([]),this}}])}(),de=function(t){t instanceof Array?t.forEach(de):(t.map&&t.map.dispose(),t.dispose())},pe=function(t){t.geometry&&t.geometry.dispose(),t.material&&de(t.material),t.texture&&t.texture.dispose(),t.children&&t.children.forEach(pe)},ge=function(t){for(;t.children.length;){var n=t.children[0];t.remove(n),pe(n)}},ve=new WeakMap,ye=new WeakMap,be=function(t){function n(t){var e,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=o.dataBindAttr,f=void 0===s?"__data":s,c=o.objBindAttr,h=void 0===c?"__threeObj":c;return i(this,n),l(e=r(this,n),"scene",void 0),a(e,ve,void 0),a(e,ye,void 0),e.scene=t,u(ve,e,f),u(ye,e,h),e.onRemoveObj((function(){})),e}return d(n,t),f(n,[{key:"onCreateObj",value:function(t){var e=this;return b(n,"onCreateObj",this)([function(n){var r=t(n);return n[o(ye,e)]=r,r[o(ve,e)]=n,e.scene.add(r),r}]),this}},{key:"onRemoveObj",value:function(t){var e=this;return b(n,"onRemoveObj",this)([function(r,i){var a=b(n,"getData",e)([r]);t(r,i),e.scene.remove(r),ge(r),delete a[o(ye,e)]}]),this}}])}(he);function me(t,n){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(n).domain(t)}return this}const _e=Symbol("implicit");var we=function(t){for(var n=t.length/6|0,e=new Array(n),r=0;r<n;)e[r]="#"+t.slice(6*r,6*++r);return e}("a6cee31f78b4b2df8a33a02cfb9a99e31a1cfdbf6fff7f00cab2d66a3d9affff99b15928");function xe(t){return xe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},xe(t)}var ke=/^\s+/,Me=/\s+$/;function Ae(t,n){if(n=n||{},(t=t||"")instanceof Ae)return t;if(!(this instanceof Ae))return new Ae(t,n);var e=function(t){var n={r:0,g:0,b:0},e=1,r=null,i=null,o=null,a=!1,u=!1;"string"==typeof t&&(t=function(t){t=t.replace(ke,"").replace(Me,"").toLowerCase();var n,e=!1;if(qe[t])t=qe[t],e=!0;else if("transparent"==t)return{r:0,g:0,b:0,a:0,format:"name"};if(n=nr.rgb.exec(t))return{r:n[1],g:n[2],b:n[3]};if(n=nr.rgba.exec(t))return{r:n[1],g:n[2],b:n[3],a:n[4]};if(n=nr.hsl.exec(t))return{h:n[1],s:n[2],l:n[3]};if(n=nr.hsla.exec(t))return{h:n[1],s:n[2],l:n[3],a:n[4]};if(n=nr.hsv.exec(t))return{h:n[1],s:n[2],v:n[3]};if(n=nr.hsva.exec(t))return{h:n[1],s:n[2],v:n[3],a:n[4]};if(n=nr.hex8.exec(t))return{r:We(n[1]),g:We(n[2]),b:We(n[3]),a:Ye(n[4]),format:e?"name":"hex8"};if(n=nr.hex6.exec(t))return{r:We(n[1]),g:We(n[2]),b:We(n[3]),format:e?"name":"hex"};if(n=nr.hex4.exec(t))return{r:We(n[1]+""+n[1]),g:We(n[2]+""+n[2]),b:We(n[3]+""+n[3]),a:Ye(n[4]+""+n[4]),format:e?"name":"hex8"};if(n=nr.hex3.exec(t))return{r:We(n[1]+""+n[1]),g:We(n[2]+""+n[2]),b:We(n[3]+""+n[3]),format:e?"name":"hex"};return!1}(t));"object"==xe(t)&&(er(t.r)&&er(t.g)&&er(t.b)?(s=t.r,f=t.g,l=t.b,n={r:255*Ge(s,255),g:255*Ge(f,255),b:255*Ge(l,255)},a=!0,u="%"===String(t.r).substr(-1)?"prgb":"rgb"):er(t.h)&&er(t.s)&&er(t.v)?(r=Xe(t.s),i=Xe(t.v),n=function(t,n,e){t=6*Ge(t,360),n=Ge(n,100),e=Ge(e,100);var r=Math.floor(t),i=t-r,o=e*(1-n),a=e*(1-i*n),u=e*(1-(1-i)*n),s=r%6,f=[e,a,o,o,u,e][s],l=[u,e,e,a,o,o][s],c=[o,o,u,e,e,a][s];return{r:255*f,g:255*l,b:255*c}}(t.h,r,i),a=!0,u="hsv"):er(t.h)&&er(t.s)&&er(t.l)&&(r=Xe(t.s),o=Xe(t.l),n=function(t,n,e){var r,i,o;function a(t,n,e){return e<0&&(e+=1),e>1&&(e-=1),e<1/6?t+6*(n-t)*e:e<.5?n:e<2/3?t+(n-t)*(2/3-e)*6:t}if(t=Ge(t,360),n=Ge(n,100),e=Ge(e,100),0===n)r=i=o=e;else{var u=e<.5?e*(1+n):e+n-e*n,s=2*e-u;r=a(s,u,t+1/3),i=a(s,u,t),o=a(s,u,t-1/3)}return{r:255*r,g:255*i,b:255*o}}(t.h,r,o),a=!0,u="hsl"),t.hasOwnProperty("a")&&(e=t.a));var s,f,l;return e=Ve(e),{ok:a,format:t.format||u,r:Math.min(255,Math.max(n.r,0)),g:Math.min(255,Math.max(n.g,0)),b:Math.min(255,Math.max(n.b,0)),a:e}}(t);this._originalInput=t,this._r=e.r,this._g=e.g,this._b=e.b,this._a=e.a,this._roundA=Math.round(100*this._a)/100,this._format=n.format||e.format,this._gradientType=n.gradientType,this._r<1&&(this._r=Math.round(this._r)),this._g<1&&(this._g=Math.round(this._g)),this._b<1&&(this._b=Math.round(this._b)),this._ok=e.ok}function Oe(t,n,e){t=Ge(t,255),n=Ge(n,255),e=Ge(e,255);var r,i,o=Math.max(t,n,e),a=Math.min(t,n,e),u=(o+a)/2;if(o==a)r=i=0;else{var s=o-a;switch(i=u>.5?s/(2-o-a):s/(o+a),o){case t:r=(n-e)/s+(n<e?6:0);break;case n:r=(e-t)/s+2;break;case e:r=(t-n)/s+4}r/=6}return{h:r,s:i,l:u}}function je(t,n,e){t=Ge(t,255),n=Ge(n,255),e=Ge(e,255);var r,i,o=Math.max(t,n,e),a=Math.min(t,n,e),u=o,s=o-a;if(i=0===o?0:s/o,o==a)r=0;else{switch(o){case t:r=(n-e)/s+(n<e?6:0);break;case n:r=(e-t)/s+2;break;case e:r=(t-n)/s+4}r/=6}return{h:r,s:i,v:u}}function Ne(t,n,e,r){var i=[Qe(Math.round(t).toString(16)),Qe(Math.round(n).toString(16)),Qe(Math.round(e).toString(16))];return r&&i[0].charAt(0)==i[0].charAt(1)&&i[1].charAt(0)==i[1].charAt(1)&&i[2].charAt(0)==i[2].charAt(1)?i[0].charAt(0)+i[1].charAt(0)+i[2].charAt(0):i.join("")}function Se(t,n,e,r){return[Qe(Je(r)),Qe(Math.round(t).toString(16)),Qe(Math.round(n).toString(16)),Qe(Math.round(e).toString(16))].join("")}function Ce(t,n){n=0===n?0:n||10;var e=Ae(t).toHsl();return e.s-=n/100,e.s=He(e.s),Ae(e)}function Pe(t,n){n=0===n?0:n||10;var e=Ae(t).toHsl();return e.s+=n/100,e.s=He(e.s),Ae(e)}function ze(t){return Ae(t).desaturate(100)}function Ee(t,n){n=0===n?0:n||10;var e=Ae(t).toHsl();return e.l+=n/100,e.l=He(e.l),Ae(e)}function Be(t,n){n=0===n?0:n||10;var e=Ae(t).toRgb();return e.r=Math.max(0,Math.min(255,e.r-Math.round(-n/100*255))),e.g=Math.max(0,Math.min(255,e.g-Math.round(-n/100*255))),e.b=Math.max(0,Math.min(255,e.b-Math.round(-n/100*255))),Ae(e)}function Te(t,n){n=0===n?0:n||10;var e=Ae(t).toHsl();return e.l-=n/100,e.l=He(e.l),Ae(e)}function De(t,n){var e=Ae(t).toHsl(),r=(e.h+n)%360;return e.h=r<0?360+r:r,Ae(e)}function $e(t){var n=Ae(t).toHsl();return n.h=(n.h+180)%360,Ae(n)}function Ie(t,n){if(isNaN(n)||n<=0)throw new Error("Argument to polyad must be a positive number");for(var e=Ae(t).toHsl(),r=[Ae(t)],i=360/n,o=1;o<n;o++)r.push(Ae({h:(e.h+o*i)%360,s:e.s,l:e.l}));return r}function Le(t){var n=Ae(t).toHsl(),e=n.h;return[Ae(t),Ae({h:(e+72)%360,s:n.s,l:n.l}),Ae({h:(e+216)%360,s:n.s,l:n.l})]}function Fe(t,n,e){n=n||6,e=e||30;var r=Ae(t).toHsl(),i=360/e,o=[Ae(t)];for(r.h=(r.h-(i*n>>1)+720)%360;--n;)r.h=(r.h+i)%360,o.push(Ae(r));return o}function Re(t,n){n=n||6;for(var e=Ae(t).toHsv(),r=e.h,i=e.s,o=e.v,a=[],u=1/n;n--;)a.push(Ae({h:r,s:i,v:o})),o=(o+u)%1;return a}Ae.prototype={isDark:function(){return this.getBrightness()<128},isLight:function(){return!this.isDark()},isValid:function(){return this._ok},getOriginalInput:function(){return this._originalInput},getFormat:function(){return this._format},getAlpha:function(){return this._a},getBrightness:function(){var t=this.toRgb();return(299*t.r+587*t.g+114*t.b)/1e3},getLuminance:function(){var t,n,e,r=this.toRgb();return t=r.r/255,n=r.g/255,e=r.b/255,.2126*(t<=.03928?t/12.92:Math.pow((t+.055)/1.055,2.4))+.7152*(n<=.03928?n/12.92:Math.pow((n+.055)/1.055,2.4))+.0722*(e<=.03928?e/12.92:Math.pow((e+.055)/1.055,2.4))},setAlpha:function(t){return this._a=Ve(t),this._roundA=Math.round(100*this._a)/100,this},toHsv:function(){var t=je(this._r,this._g,this._b);return{h:360*t.h,s:t.s,v:t.v,a:this._a}},toHsvString:function(){var t=je(this._r,this._g,this._b),n=Math.round(360*t.h),e=Math.round(100*t.s),r=Math.round(100*t.v);return 1==this._a?"hsv("+n+", "+e+"%, "+r+"%)":"hsva("+n+", "+e+"%, "+r+"%, "+this._roundA+")"},toHsl:function(){var t=Oe(this._r,this._g,this._b);return{h:360*t.h,s:t.s,l:t.l,a:this._a}},toHslString:function(){var t=Oe(this._r,this._g,this._b),n=Math.round(360*t.h),e=Math.round(100*t.s),r=Math.round(100*t.l);return 1==this._a?"hsl("+n+", "+e+"%, "+r+"%)":"hsla("+n+", "+e+"%, "+r+"%, "+this._roundA+")"},toHex:function(t){return Ne(this._r,this._g,this._b,t)},toHexString:function(t){return"#"+this.toHex(t)},toHex8:function(t){return function(t,n,e,r,i){var o=[Qe(Math.round(t).toString(16)),Qe(Math.round(n).toString(16)),Qe(Math.round(e).toString(16)),Qe(Je(r))];if(i&&o[0].charAt(0)==o[0].charAt(1)&&o[1].charAt(0)==o[1].charAt(1)&&o[2].charAt(0)==o[2].charAt(1)&&o[3].charAt(0)==o[3].charAt(1))return o[0].charAt(0)+o[1].charAt(0)+o[2].charAt(0)+o[3].charAt(0);return o.join("")}(this._r,this._g,this._b,this._a,t)},toHex8String:function(t){return"#"+this.toHex8(t)},toRgb:function(){return{r:Math.round(this._r),g:Math.round(this._g),b:Math.round(this._b),a:this._a}},toRgbString:function(){return 1==this._a?"rgb("+Math.round(this._r)+", "+Math.round(this._g)+", "+Math.round(this._b)+")":"rgba("+Math.round(this._r)+", "+Math.round(this._g)+", "+Math.round(this._b)+", "+this._roundA+")"},toPercentageRgb:function(){return{r:Math.round(100*Ge(this._r,255))+"%",g:Math.round(100*Ge(this._g,255))+"%",b:Math.round(100*Ge(this._b,255))+"%",a:this._a}},toPercentageRgbString:function(){return 1==this._a?"rgb("+Math.round(100*Ge(this._r,255))+"%, "+Math.round(100*Ge(this._g,255))+"%, "+Math.round(100*Ge(this._b,255))+"%)":"rgba("+Math.round(100*Ge(this._r,255))+"%, "+Math.round(100*Ge(this._g,255))+"%, "+Math.round(100*Ge(this._b,255))+"%, "+this._roundA+")"},toName:function(){return 0===this._a?"transparent":!(this._a<1)&&(Ue[Ne(this._r,this._g,this._b,!0)]||!1)},toFilter:function(t){var n="#"+Se(this._r,this._g,this._b,this._a),e=n,r=this._gradientType?"GradientType = 1, ":"";if(t){var i=Ae(t);e="#"+Se(i._r,i._g,i._b,i._a)}return"progid:DXImageTransform.Microsoft.gradient("+r+"startColorstr="+n+",endColorstr="+e+")"},toString:function(t){var n=!!t;t=t||this._format;var e=!1,r=this._a<1&&this._a>=0;return n||!r||"hex"!==t&&"hex6"!==t&&"hex3"!==t&&"hex4"!==t&&"hex8"!==t&&"name"!==t?("rgb"===t&&(e=this.toRgbString()),"prgb"===t&&(e=this.toPercentageRgbString()),"hex"!==t&&"hex6"!==t||(e=this.toHexString()),"hex3"===t&&(e=this.toHexString(!0)),"hex4"===t&&(e=this.toHex8String(!0)),"hex8"===t&&(e=this.toHex8String()),"name"===t&&(e=this.toName()),"hsl"===t&&(e=this.toHslString()),"hsv"===t&&(e=this.toHsvString()),e||this.toHexString()):"name"===t&&0===this._a?this.toName():this.toRgbString()},clone:function(){return Ae(this.toString())},_applyModification:function(t,n){var e=t.apply(null,[this].concat([].slice.call(n)));return this._r=e._r,this._g=e._g,this._b=e._b,this.setAlpha(e._a),this},lighten:function(){return this._applyModification(Ee,arguments)},brighten:function(){return this._applyModification(Be,arguments)},darken:function(){return this._applyModification(Te,arguments)},desaturate:function(){return this._applyModification(Ce,arguments)},saturate:function(){return this._applyModification(Pe,arguments)},greyscale:function(){return this._applyModification(ze,arguments)},spin:function(){return this._applyModification(De,arguments)},_applyCombination:function(t,n){return t.apply(null,[this].concat([].slice.call(n)))},analogous:function(){return this._applyCombination(Fe,arguments)},complement:function(){return this._applyCombination($e,arguments)},monochromatic:function(){return this._applyCombination(Re,arguments)},splitcomplement:function(){return this._applyCombination(Le,arguments)},triad:function(){return this._applyCombination(Ie,[3])},tetrad:function(){return this._applyCombination(Ie,[4])}},Ae.fromRatio=function(t,n){if("object"==xe(t)){var e={};for(var r in t)t.hasOwnProperty(r)&&(e[r]="a"===r?t[r]:Xe(t[r]));t=e}return Ae(t,n)},Ae.equals=function(t,n){return!(!t||!n)&&Ae(t).toRgbString()==Ae(n).toRgbString()},Ae.random=function(){return Ae.fromRatio({r:Math.random(),g:Math.random(),b:Math.random()})},Ae.mix=function(t,n,e){e=0===e?0:e||50;var r=Ae(t).toRgb(),i=Ae(n).toRgb(),o=e/100;return Ae({r:(i.r-r.r)*o+r.r,g:(i.g-r.g)*o+r.g,b:(i.b-r.b)*o+r.b,a:(i.a-r.a)*o+r.a})},
// <http://www.w3.org/TR/2008/REC-WCAG20-20081211/#contrast-ratiodef (WCAG Version 2)
// Analyze the 2 colors and returns the color contrast defined by (WCAG Version 2)
Ae.readability=function(t,n){var e=Ae(t),r=Ae(n);return(Math.max(e.getLuminance(),r.getLuminance())+.05)/(Math.min(e.getLuminance(),r.getLuminance())+.05)},Ae.isReadable=function(t,n,e){var r,i,o=Ae.readability(t,n);switch(i=!1,(r=function(t){var n,e;n=((t=t||{level:"AA",size:"small"}).level||"AA").toUpperCase(),e=(t.size||"small").toLowerCase(),"AA"!==n&&"AAA"!==n&&(n="AA");"small"!==e&&"large"!==e&&(e="small");return{level:n,size:e}}(e)).level+r.size){case"AAsmall":case"AAAlarge":i=o>=4.5;break;case"AAlarge":i=o>=3;break;case"AAAsmall":i=o>=7}return i},Ae.mostReadable=function(t,n,e){var r,i,o,a,u=null,s=0;i=(e=e||{}).includeFallbackColors,o=e.level,a=e.size;for(var f=0;f<n.length;f++)(r=Ae.readability(t,n[f]))>s&&(s=r,u=Ae(n[f]));return Ae.isReadable(t,u,{level:o,size:a})||!i?u:(e.includeFallbackColors=!1,Ae.mostReadable(t,["#fff","#000"],e))};var qe=Ae.names={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"0ff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000",blanchedalmond:"ffebcd",blue:"00f",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",burntsienna:"ea7e5d",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"0ff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkgrey:"a9a9a9",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"f0f",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",grey:"808080",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgreen:"90ee90",lightgrey:"d3d3d3",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"789",lightslategrey:"789",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"0f0",limegreen:"32cd32",linen:"faf0e6",magenta:"f0f",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370db",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"db7093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"f00",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"fff",whitesmoke:"f5f5f5",yellow:"ff0",yellowgreen:"9acd32"},Ue=Ae.hexNames=function(t){var n={};for(var e in t)t.hasOwnProperty(e)&&(n[t[e]]=e);return n}(qe);function Ve(t){return t=parseFloat(t),(isNaN(t)||t<0||t>1)&&(t=1),t}function Ge(t,n){(function(t){return"string"==typeof t&&-1!=t.indexOf(".")&&1===parseFloat(t)})(t)&&(t="100%");var e=function(t){return"string"==typeof t&&-1!=t.indexOf("%")}(t);return t=Math.min(n,Math.max(0,parseFloat(t))),e&&(t=parseInt(t*n,10)/100),Math.abs(t-n)<1e-6?1:t%n/parseFloat(n)}function He(t){return Math.min(1,Math.max(0,t))}function We(t){return parseInt(t,16)}function Qe(t){return 1==t.length?"0"+t:""+t}function Xe(t){return t<=1&&(t=100*t+"%"),t}function Je(t){return Math.round(255*parseFloat(t)).toString(16)}function Ye(t){return We(t)/255}var Ke,Ze,tr,nr=(Ze="[\\s|\\(]+("+(Ke="(?:[-\\+]?\\d*\\.\\d+%?)|(?:[-\\+]?\\d+%?)")+")[,|\\s]+("+Ke+")[,|\\s]+("+Ke+")\\s*\\)?",tr="[\\s|\\(]+("+Ke+")[,|\\s]+("+Ke+")[,|\\s]+("+Ke+")[,|\\s]+("+Ke+")\\s*\\)?",{CSS_UNIT:new RegExp(Ke),rgb:new RegExp("rgb"+Ze),rgba:new RegExp("rgba"+tr),hsl:new RegExp("hsl"+Ze),hsla:new RegExp("hsla"+tr),hsv:new RegExp("hsv"+Ze),hsva:new RegExp("hsva"+tr),hex3:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,hex4:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex8:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/});function er(t){return!!nr.CSS_UNIT.exec(t)}var rr=function(t){return isNaN(t)?parseInt(Ae(t).toHex(),16):t},ir=function(t){return isNaN(t)?Ae(t).getAlpha():1},or=function t(){var n=new Gn,e=[],r=[],i=_e;function o(t){let o=n.get(t);if(void 0===o){if(i!==_e)return i;n.set(t,o=e.push(t)-1)}return r[o%r.length]}return o.domain=function(t){if(!arguments.length)return e.slice();e=[],n=new Gn;for(const r of t)n.has(r)||n.set(r,e.push(r)-1);return o},o.range=function(t){return arguments.length?(r=Array.from(t),o):r.slice()},o.unknown=function(t){return arguments.length?(i=t,o):i},o.copy=function(){return t(e,r).unknown(i)},me.apply(o,arguments),o}(we);function ar(t,n,e){n&&"string"==typeof e&&t.filter((function(t){return!t[e]})).forEach((function(t){t[e]=or(n(t))}))}var ur,sr,fr,lr,cr,hr,dr,pr,gr,vr,yr,br,mr,_r,wr=window.THREE?window.THREE:{Group:t.Group,Mesh:t.Mesh,MeshLambertMaterial:t.MeshLambertMaterial,Color:t.Color,BufferGeometry:t.BufferGeometry,BufferAttribute:t.BufferAttribute,Matrix4:t.Matrix4,Vector3:t.Vector3,SphereGeometry:t.SphereGeometry,CylinderGeometry:t.CylinderGeometry,TubeGeometry:t.TubeGeometry,ConeGeometry:t.ConeGeometry,Line:t.Line,LineBasicMaterial:t.LineBasicMaterial,QuadraticBezierCurve3:t.QuadraticBezierCurve3,CubicBezierCurve3:t.CubicBezierCurve3,Box3:t.Box3},xr={graph:qt,forcelayout:gn},kr=(new wr.BufferGeometry).setAttribute?"setAttribute":"addAttribute",Mr=(new wr.BufferGeometry).applyMatrix4?"applyMatrix4":"applyMatrix",Ar=(ur={props:{jsonUrl:{onChange:function(t,n){var e=this;t&&!n.fetchingJson&&(n.fetchingJson=!0,n.onLoading(),fetch(t).then((function(t){return t.json()})).then((function(t){n.fetchingJson=!1,n.onFinishLoading(t),e.graphData(t)})))},triggerUpdate:!1},graphData:{default:{nodes:[],links:[]},onChange:function(t,n){n.engineRunning=!1}},numDimensions:{default:3,onChange:function(t,n){var e=n.d3ForceLayout.force("charge");function r(t,n){t.forEach((function(t){delete t[n],delete t["v".concat(n)]}))}e&&e.strength(t>2?-60:-30),t<3&&r(n.graphData.nodes,"z"),t<2&&r(n.graphData.nodes,"y")}},dagMode:{onChange:function(t,n){!t&&"d3"===n.forceEngine&&(n.graphData.nodes||[]).forEach((function(t){return t.fx=t.fy=t.fz=void 0}))}},dagLevelDistance:{},dagNodeFilter:{default:function(t){return!0}},onDagError:{triggerUpdate:!1},nodeRelSize:{default:4},nodeId:{default:"id"},nodeVal:{default:"val"},nodeResolution:{default:8},nodeColor:{default:"color"},nodeAutoColorBy:{},nodeOpacity:{default:.75},nodeVisibility:{default:!0},nodeThreeObject:{},nodeThreeObjectExtend:{default:!1},nodePositionUpdate:{triggerUpdate:!1},linkSource:{default:"source"},linkTarget:{default:"target"},linkVisibility:{default:!0},linkColor:{default:"color"},linkAutoColorBy:{},linkOpacity:{default:.2},linkWidth:{},linkResolution:{default:6},linkCurvature:{default:0,triggerUpdate:!1},linkCurveRotation:{default:0,triggerUpdate:!1},linkMaterial:{},linkThreeObject:{},linkThreeObjectExtend:{default:!1},linkPositionUpdate:{triggerUpdate:!1},linkDirectionalArrowLength:{default:0},linkDirectionalArrowColor:{},linkDirectionalArrowRelPos:{default:.5,triggerUpdate:!1},linkDirectionalArrowResolution:{default:8},linkDirectionalParticles:{default:0},linkDirectionalParticleSpeed:{default:.01,triggerUpdate:!1},linkDirectionalParticleWidth:{default:.5},linkDirectionalParticleColor:{},linkDirectionalParticleResolution:{default:4},forceEngine:{default:"d3"},d3AlphaMin:{default:0},d3AlphaDecay:{default:.0228,triggerUpdate:!1,onChange:function(t,n){n.d3ForceLayout.alphaDecay(t)}},d3AlphaTarget:{default:0,triggerUpdate:!1,onChange:function(t,n){n.d3ForceLayout.alphaTarget(t)}},d3VelocityDecay:{default:.4,triggerUpdate:!1,onChange:function(t,n){n.d3ForceLayout.velocityDecay(t)}},ngraphPhysics:{default:{timeStep:20,gravity:-1.2,theta:.8,springLength:30,springCoefficient:8e-4,dragCoefficient:.02}},warmupTicks:{default:0,triggerUpdate:!1},cooldownTicks:{default:1/0,triggerUpdate:!1},cooldownTime:{default:15e3,triggerUpdate:!1},onLoading:{default:function(){},triggerUpdate:!1},onFinishLoading:{default:function(){},triggerUpdate:!1},onUpdate:{default:function(){},triggerUpdate:!1},onFinishUpdate:{default:function(){},triggerUpdate:!1},onEngineTick:{default:function(){},triggerUpdate:!1},onEngineStop:{default:function(){},triggerUpdate:!1}},methods:{refresh:function(t){return t._flushObjects=!0,t._rerender(),this},d3Force:function(t,n,e){return void 0===e?t.d3ForceLayout.force(n):(t.d3ForceLayout.force(n,e),this)},d3ReheatSimulation:function(t){return t.d3ForceLayout.alpha(1),this.resetCountdown(),this},resetCountdown:function(t){return t.cntTicks=0,t.startTickTime=new Date,t.engineRunning=!0,this},tickFrame:function(t){var n,e,r,i,o="ngraph"!==t.forceEngine;return t.engineRunning&&function(){++t.cntTicks>t.cooldownTicks||new Date-t.startTickTime>t.cooldownTime||o&&t.d3AlphaMin>0&&t.d3ForceLayout.alpha()<t.d3AlphaMin?(t.engineRunning=!1,t.onEngineStop()):(t.layout[o?"tick":"step"](),t.onEngineTick());var n=Vn(t.nodeThreeObjectExtend);t.nodeDataMapper.entries().forEach((function(e){var r=y(e,2),i=r[0],a=r[1];if(a){var u=o?i:t.layout.getNodePosition(i[t.nodeId]),s=n(i);t.nodePositionUpdate&&t.nodePositionUpdate(s?a.children[0]:a,{x:u.x,y:u.y,z:u.z},i)&&!s||(a.position.x=u.x,a.position.y=u.y||0,a.position.z=u.z||0)}}));var e=Vn(t.linkWidth),r=Vn(t.linkCurvature),i=Vn(t.linkCurveRotation),a=Vn(t.linkThreeObjectExtend);function u(n){var e=o?n:t.layout.getLinkPosition(t.layout.graph.getLink(n.source,n.target).id),a=e[o?"source":"from"],u=e[o?"target":"to"];if(a&&u&&a.hasOwnProperty("x")&&u.hasOwnProperty("x")){var s=r(n);if(s){var f,l=new wr.Vector3(a.x,a.y||0,a.z||0),c=new wr.Vector3(u.x,u.y||0,u.z||0),h=l.distanceTo(c),d=i(n);if(h>0){var p=u.x-a.x,g=u.y-a.y||0,v=(new wr.Vector3).subVectors(c,l),y=v.clone().multiplyScalar(s).cross(0!==p||0!==g?new wr.Vector3(0,0,1):new wr.Vector3(0,1,0)).applyAxisAngle(v.normalize(),d).add((new wr.Vector3).addVectors(l,c).divideScalar(2));f=new wr.QuadraticBezierCurve3(l,y,c)}else{var b=70*s,m=-d,_=m+Math.PI/2;f=new wr.CubicBezierCurve3(l,new wr.Vector3(b*Math.cos(_),b*Math.sin(_),0).add(l),new wr.Vector3(b*Math.cos(m),b*Math.sin(m),0).add(l),c)}n.__curve=f}else n.__curve=null}}t.linkDataMapper.entries().forEach((function(n){var r=y(n,2),i=r[0],s=r[1];if(s){var f=o?i:t.layout.getLinkPosition(t.layout.graph.getLink(i.source,i.target).id),l=f[o?"source":"from"],c=f[o?"target":"to"];if(l&&c&&l.hasOwnProperty("x")&&c.hasOwnProperty("x")){u(i);var h=a(i);if(!t.linkPositionUpdate||!t.linkPositionUpdate(h?s.children[1]:s,{start:{x:l.x,y:l.y,z:l.z},end:{x:c.x,y:c.y,z:c.z}},i)||h){var d=30,p=i.__curve,g=s.children.length?s.children[0]:s;if("Line"===g.type){if(p){var v=p.getPoints(d);g.geometry.getAttribute("position").array.length!==3*v.length&&g.geometry[kr]("position",new wr.BufferAttribute(new Float32Array(3*v.length),3)),g.geometry.setFromPoints(v)}else{var b=g.geometry.getAttribute("position");b&&b.array&&6===b.array.length||g.geometry[kr]("position",b=new wr.BufferAttribute(new Float32Array(6),3)),b.array[0]=l.x,b.array[1]=l.y||0,b.array[2]=l.z||0,b.array[3]=c.x,b.array[4]=c.y||0,b.array[5]=c.z||0,b.needsUpdate=!0}g.geometry.computeBoundingSphere()}else if("Mesh"===g.type)if(p){g.geometry.type.match(/^Tube(Buffer)?Geometry$/)||(g.position.set(0,0,0),g.rotation.set(0,0,0),g.scale.set(1,1,1));var m=Math.ceil(10*e(i))/10/2,_=new wr.TubeGeometry(p,d,m,t.linkResolution,!1);g.geometry.dispose(),g.geometry=_}else{if(!g.geometry.type.match(/^Cylinder(Buffer)?Geometry$/)){var w=Math.ceil(10*e(i))/10/2,x=new wr.CylinderGeometry(w,w,1,t.linkResolution,1,!1);x[Mr]((new wr.Matrix4).makeTranslation(0,.5,0)),x[Mr]((new wr.Matrix4).makeRotationX(Math.PI/2)),g.geometry.dispose(),g.geometry=x}var k=new wr.Vector3(l.x,l.y||0,l.z||0),M=new wr.Vector3(c.x,c.y||0,c.z||0),A=k.distanceTo(M);g.position.x=k.x,g.position.y=k.y,g.position.z=k.z,g.scale.z=A,g.parent.localToWorld(M),g.lookAt(M)}}}}}))}(),n=Vn(t.linkDirectionalArrowRelPos),e=Vn(t.linkDirectionalArrowLength),r=Vn(t.nodeVal),t.arrowDataMapper.entries().forEach((function(i){var a=y(i,2),u=a[0],f=a[1];if(f){var l=o?u:t.layout.getLinkPosition(t.layout.graph.getLink(u.source,u.target).id),c=l[o?"source":"from"],h=l[o?"target":"to"];if(c&&h&&c.hasOwnProperty("x")&&h.hasOwnProperty("x")){var d=Math.cbrt(Math.max(0,r(c)||1))*t.nodeRelSize,p=Math.cbrt(Math.max(0,r(h)||1))*t.nodeRelSize,g=e(u),v=n(u),b=u.__curve?function(t){return u.__curve.getPoint(t)}:function(t){var n=function(t,n,e,r){return n[t]+(e[t]-n[t])*r||0};return{x:n("x",c,h,t),y:n("y",c,h,t),z:n("z",c,h,t)}},_=u.__curve?u.__curve.getLength():Math.sqrt(["x","y","z"].map((function(t){return Math.pow((h[t]||0)-(c[t]||0),2)})).reduce((function(t,n){return t+n}),0)),w=d+g+(_-d-p-g)*v,x=b(w/_),k=b((w-g)/_);["x","y","z"].forEach((function(t){return f.position[t]=k[t]}));var M=s(wr.Vector3,m(["x","y","z"].map((function(t){return x[t]}))));f.parent.localToWorld(M),f.lookAt(M)}}})),i=Vn(t.linkDirectionalParticleSpeed),t.graphData.links.forEach((function(n){var e=t.particlesDataMapper.getObj(n),r=e&&e.children,a=n.__singleHopPhotonsObj&&n.__singleHopPhotonsObj.children;if(a&&a.length||r&&r.length){var u=o?n:t.layout.getLinkPosition(t.layout.graph.getLink(n.source,n.target).id),s=u[o?"source":"from"],f=u[o?"target":"to"];if(s&&f&&s.hasOwnProperty("x")&&f.hasOwnProperty("x")){var l=i(n),c=n.__curve?function(t){return n.__curve.getPoint(t)}:function(t){var n=function(t,n,e,r){return n[t]+(e[t]-n[t])*r||0};return{x:n("x",s,f,t),y:n("y",s,f,t),z:n("z",s,f,t)}};[].concat(m(r||[]),m(a||[])).forEach((function(t,n){var e="singleHopPhotons"===t.parent.__linkThreeObjType;if(t.hasOwnProperty("__progressRatio")||(t.__progressRatio=e?0:n/r.length),t.__progressRatio+=l,t.__progressRatio>=1){if(e)return t.parent.remove(t),void ge(t);t.__progressRatio=t.__progressRatio%1}var i=t.__progressRatio,o=c(i);["x","y","z"].forEach((function(n){return t.position[n]=o[n]}))}))}}})),this},emitParticle:function(t,n){if(n&&t.graphData.links.includes(n)){if(!n.__singleHopPhotonsObj){var e=new wr.Group;e.__linkThreeObjType="singleHopPhotons",n.__singleHopPhotonsObj=e,t.graphScene.add(e)}var r=Vn(t.linkDirectionalParticleWidth),i=Math.ceil(10*r(n))/10/2,o=t.linkDirectionalParticleResolution,a=new wr.SphereGeometry(i,o,o),u=Vn(t.linkColor),s=Vn(t.linkDirectionalParticleColor)(n)||u(n)||"#f0f0f0",f=new wr.Color(rr(s)),l=3*t.linkOpacity,c=new wr.MeshLambertMaterial({color:f,transparent:!0,opacity:l});n.__singleHopPhotonsObj.add(new wr.Mesh(a,c))}return this},getGraphBbox:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(){return!0};if(!t.initialised)return null;var e=function t(e){var r=[];if(e.geometry){e.geometry.computeBoundingBox();var i=new wr.Box3;i.copy(e.geometry.boundingBox).applyMatrix4(e.matrixWorld),r.push(i)}return r.concat.apply(r,m((e.children||[]).filter((function(t){return!t.hasOwnProperty("__graphObjType")||"node"===t.__graphObjType&&n(t.__data)})).map(t)))}(t.graphScene);return e.length?Object.assign.apply(Object,m(["x","y","z"].map((function(t){return l({},t,[Xn(e,(function(n){return n.min[t]})),Qn(e,(function(n){return n.max[t]}))])})))):null}},stateInit:function(){return{d3ForceLayout:zt().force("link",Z()).force("charge",Et()).force("center",k()).force("dagRadial",null).stop(),engineRunning:!1}},init:function(t,n){n.graphScene=t,n.nodeDataMapper=new be(t,{objBindAttr:"__threeObj"}),n.linkDataMapper=new be(t,{objBindAttr:"__lineObj"}),n.arrowDataMapper=new be(t,{objBindAttr:"__arrowObj"}),n.particlesDataMapper=new be(t,{objBindAttr:"__photonsObj"})},update:function(t,n){var e=function(t){return t.some((function(t){return n.hasOwnProperty(t)}))};if(t.engineRunning=!1,"function"==typeof t.onUpdate&&t.onUpdate(),null!==t.nodeAutoColorBy&&e(["nodeAutoColorBy","graphData","nodeColor"])&&ar(t.graphData.nodes,Vn(t.nodeAutoColorBy),t.nodeColor),null!==t.linkAutoColorBy&&e(["linkAutoColorBy","graphData","linkColor"])&&ar(t.graphData.links,Vn(t.linkAutoColorBy),t.linkColor),t._flushObjects||e(["graphData","nodeThreeObject","nodeThreeObjectExtend","nodeVal","nodeColor","nodeVisibility","nodeRelSize","nodeResolution","nodeOpacity"])){var r=Vn(t.nodeThreeObject),i=Vn(t.nodeThreeObjectExtend),o=Vn(t.nodeVal),a=Vn(t.nodeColor),u=Vn(t.nodeVisibility),s={},f={};(t._flushObjects||e(["nodeThreeObject","nodeThreeObjectExtend"]))&&t.nodeDataMapper.clear(),t.nodeDataMapper.onCreateObj((function(n){var e,o=r(n),a=i(n);return o&&t.nodeThreeObject===o&&(o=o.clone()),o&&!a?e=o:((e=new wr.Mesh).__graphDefaultObj=!0,o&&a&&e.add(o)),e.__graphObjType="node",e})).onUpdateObj((function(n,e){if(n.__graphDefaultObj){var r=o(e)||1,i=Math.cbrt(r)*t.nodeRelSize,u=t.nodeResolution;n.geometry.type.match(/^Sphere(Buffer)?Geometry$/)&&n.geometry.parameters.radius===i&&n.geometry.parameters.widthSegments===u||(s.hasOwnProperty(r)||(s[r]=new wr.SphereGeometry(i,u,u)),n.geometry.dispose(),n.geometry=s[r]);var l=a(e),c=new wr.Color(rr(l||"#ffffaa")),h=t.nodeOpacity*ir(l);"MeshLambertMaterial"===n.material.type&&n.material.color.equals(c)&&n.material.opacity===h||(f.hasOwnProperty(l)||(f[l]=new wr.MeshLambertMaterial({color:c,transparent:!0,opacity:h})),n.material.dispose(),n.material=f[l])}})).digest(t.graphData.nodes.filter(u))}if(t._flushObjects||e(["graphData","linkThreeObject","linkThreeObjectExtend","linkMaterial","linkColor","linkWidth","linkVisibility","linkResolution","linkOpacity","linkDirectionalArrowLength","linkDirectionalArrowColor","linkDirectionalArrowResolution","linkDirectionalParticles","linkDirectionalParticleWidth","linkDirectionalParticleColor","linkDirectionalParticleResolution"])){var c=Vn(t.linkThreeObject),h=Vn(t.linkThreeObjectExtend),d=Vn(t.linkMaterial),p=Vn(t.linkVisibility),v=Vn(t.linkColor),b=Vn(t.linkWidth),_={},x={},k={},M=t.graphData.links.filter(p);if((t._flushObjects||e(["linkThreeObject","linkThreeObjectExtend","linkWidth"]))&&t.linkDataMapper.clear(),t.linkDataMapper.onRemoveObj((function(t){var n=t.__data&&t.__data.__singleHopPhotonsObj;n&&(n.parent.remove(n),ge(n),delete t.__data.__singleHopPhotonsObj)})).onCreateObj((function(n){var e,r,i=c(n),o=h(n);if(i&&t.linkThreeObject===i&&(i=i.clone()),!i||o)if(b(n))e=new wr.Mesh;else{var a=new wr.BufferGeometry;a[kr]("position",new wr.BufferAttribute(new Float32Array(6),3)),e=new wr.Line(a)}return i?o?((r=new wr.Group).__graphDefaultObj=!0,r.add(e),r.add(i)):r=i:(r=e).__graphDefaultObj=!0,r.renderOrder=10,r.__graphObjType="link",r})).onUpdateObj((function(n,e){if(n.__graphDefaultObj){var r=n.children.length?n.children[0]:n,i=Math.ceil(10*b(e))/10,o=!!i;if(o){var a=i/2,u=t.linkResolution;if(!r.geometry.type.match(/^Cylinder(Buffer)?Geometry$/)||r.geometry.parameters.radiusTop!==a||r.geometry.parameters.radialSegments!==u){if(!_.hasOwnProperty(i)){var s=new wr.CylinderGeometry(a,a,1,u,1,!1);s[Mr]((new wr.Matrix4).makeTranslation(0,.5,0)),s[Mr]((new wr.Matrix4).makeRotationX(Math.PI/2)),_[i]=s}r.geometry.dispose(),r.geometry=_[i]}}var f=d(e);if(f)r.material=f;else{var l=v(e),c=new wr.Color(rr(l||"#f0f0f0")),h=t.linkOpacity*ir(l),p=o?"MeshLambertMaterial":"LineBasicMaterial";if(r.material.type!==p||!r.material.color.equals(c)||r.material.opacity!==h){var g=o?x:k;g.hasOwnProperty(l)||(g[l]=new wr[p]({color:c,transparent:h<1,opacity:h,depthWrite:h>=1})),r.material.dispose(),r.material=g[l]}}}})).digest(M),t.linkDirectionalArrowLength||n.hasOwnProperty("linkDirectionalArrowLength")){var A=Vn(t.linkDirectionalArrowLength),O=Vn(t.linkDirectionalArrowColor);t.arrowDataMapper.onCreateObj((function(){var t=new wr.Mesh(void 0,new wr.MeshLambertMaterial({transparent:!0}));return t.__linkThreeObjType="arrow",t})).onUpdateObj((function(n,e){var r=A(e),i=t.linkDirectionalArrowResolution;if(!n.geometry.type.match(/^Cone(Buffer)?Geometry$/)||n.geometry.parameters.height!==r||n.geometry.parameters.radialSegments!==i){var o=new wr.ConeGeometry(.25*r,r,i);o.translate(0,r/2,0),o.rotateX(Math.PI/2),n.geometry.dispose(),n.geometry=o}var a=O(e)||v(e)||"#f0f0f0";n.material.color=new wr.Color(rr(a)),n.material.opacity=3*t.linkOpacity*ir(a)})).digest(M.filter(A))}if(t.linkDirectionalParticles||n.hasOwnProperty("linkDirectionalParticles")){var j=Vn(t.linkDirectionalParticles),N=Vn(t.linkDirectionalParticleWidth),S=Vn(t.linkDirectionalParticleColor),C={},P={};t.particlesDataMapper.onCreateObj((function(){var t=new wr.Group;return t.__linkThreeObjType="photons",t.__photonDataMapper=new be(t),t})).onUpdateObj((function(n,e){var r,i=Math.round(Math.abs(j(e))),o=!!n.children.length&&n.children[0],a=Math.ceil(10*N(e))/10/2,u=t.linkDirectionalParticleResolution;o&&o.geometry.parameters.radius===a&&o.geometry.parameters.widthSegments===u?r=o.geometry:(P.hasOwnProperty(a)||(P[a]=new wr.SphereGeometry(a,u,u)),r=P[a],o&&o.geometry.dispose());var s,f=S(e)||v(e)||"#f0f0f0",l=new wr.Color(rr(f)),c=3*t.linkOpacity;o&&o.material.color.equals(l)&&o.material.opacity===c?s=o.material:(C.hasOwnProperty(f)||(C[f]=new wr.MeshLambertMaterial({color:l,transparent:!0,opacity:c})),s=C[f],o&&o.material.dispose()),n.__photonDataMapper.id((function(t){return t.idx})).onCreateObj((function(){return new wr.Mesh(r,s)})).onUpdateObj((function(t){t.geometry=r,t.material=s})).digest(m(new Array(i)).map((function(t,n){return{idx:n}})))})).digest(M.filter(j))}}if(t._flushObjects=!1,e(["graphData","nodeId","linkSource","linkTarget","numDimensions","forceEngine","dagMode","dagNodeFilter","dagLevelDistance"])){t.engineRunning=!1,t.graphData.links.forEach((function(n){n.source=n[t.linkSource],n.target=n[t.linkTarget]}));var z,E="ngraph"!==t.forceEngine;if(E){(z=t.d3ForceLayout).stop().alpha(1).numDimensions(t.numDimensions).nodes(t.graphData.nodes);var B=t.d3ForceLayout.force("link");B&&B.id((function(n){return n[t.nodeId]})).links(t.graphData.links);var T=t.dagMode&&function(t,n){var e=t.nodes,r=t.links,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=i.nodeFilter,a=void 0===o?function(){return!0}:o,u=i.onLoopError,s=void 0===u?function(t){throw"Invalid DAG structure! Found cycle in node path: ".concat(t.join(" -> "),".")}:u,f={};e.forEach((function(t){return f[n(t)]={data:t,out:[],depth:-1,skip:!a(t)}})),r.forEach((function(t){var e=t.source,r=t.target,i=s(e),o=s(r);if(!f.hasOwnProperty(i))throw"Missing source node with id: ".concat(i);if(!f.hasOwnProperty(o))throw"Missing target node with id: ".concat(o);var a=f[i],u=f[o];function s(t){return"object"===w(t)?n(t):t}a.out.push(u)}));var c=[];return function t(e){for(var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=function(){var o=e[a];if(-1!==r.indexOf(o)){var u=[].concat(m(r.slice(r.indexOf(o))),[o]).map((function(t){return n(t.data)}));return c.some((function(t){return t.length===u.length&&t.every((function(t,n){return t===u[n]}))}))||(c.push(u),s(u)),1}i>o.depth&&(o.depth=i,t(o.out,[].concat(m(r),[o]),i+(o.skip?0:1)))},a=0,u=e.length;a<u;a++)o()}(Object.values(f)),Object.assign.apply(Object,[{}].concat(m(Object.entries(f).filter((function(t){return!y(t,2)[1].skip})).map((function(t){var n=y(t,2);return l({},n[0],n[1].depth)})))))}(t.graphData,(function(n){return n[t.nodeId]}),{nodeFilter:t.dagNodeFilter,onLoopError:t.onDagError||void 0}),D=Math.max.apply(Math,m(Object.values(T||[]))),$=t.dagLevelDistance||t.graphData.nodes.length/(D||1)*2*(-1!==["radialin","radialout"].indexOf(t.dagMode)?.7:1);if(["lr","rl","td","bu","zin","zout"].includes(n.dagMode)){var I=["lr","rl"].includes(n.dagMode)?"fx":["td","bu"].includes(n.dagMode)?"fy":"fz";t.graphData.nodes.filter(t.dagNodeFilter).forEach((function(t){return delete t[I]}))}if(["lr","rl","td","bu","zin","zout"].includes(t.dagMode)){var L=["rl","td","zout"].includes(t.dagMode),F=["lr","rl"].includes(t.dagMode)?"fx":["td","bu"].includes(t.dagMode)?"fy":"fz";t.graphData.nodes.filter(t.dagNodeFilter).forEach((function(n){return n[F]=function(n){return(T[n[t.nodeId]]-D/2)*$*(L?-1:1)}(n)}))}t.d3ForceLayout.force("dagRadial",-1!==["radialin","radialout"].indexOf(t.dagMode)?function(t,n,e,r){var i,o,a,u,s=X(.1);function f(t){for(var s=0,f=i.length;s<f;++s){var l=i[s],c=l.x-n||1e-6,h=(l.y||0)-e||1e-6,d=(l.z||0)-r||1e-6,p=Math.sqrt(c*c+h*h+d*d),g=(u[s]-p)*a[s]*t/p;l.vx+=c*g,o>1&&(l.vy+=h*g),o>2&&(l.vz+=d*g)}}function l(){if(i){var n,e=i.length;for(a=new Array(e),u=new Array(e),n=0;n<e;++n)u[n]=+t(i[n],n,i),a[n]=isNaN(u[n])?0:+s(i[n],n,i)}}return"function"!=typeof t&&(t=X(+t)),null==n&&(n=0),null==e&&(e=0),null==r&&(r=0),f.initialize=function(t,...n){i=t,o=n.find((t=>[1,2,3].includes(t)))||2,l()},f.strength=function(t){return arguments.length?(s="function"==typeof t?t:X(+t),l(),f):s},f.radius=function(n){return arguments.length?(t="function"==typeof n?n:X(+n),l(),f):t},f.x=function(t){return arguments.length?(n=+t,f):n},f.y=function(t){return arguments.length?(e=+t,f):e},f.z=function(t){return arguments.length?(r=+t,f):r},f}((function(n){var e=T[n[t.nodeId]]||-1;return("radialin"===t.dagMode?D-e:e)*$})).strength((function(n){return t.dagNodeFilter(n)?1:0})):null)}else{var R=xr.graph();t.graphData.nodes.forEach((function(n){R.addNode(n[t.nodeId])})),t.graphData.links.forEach((function(t){R.addLink(t.source,t.target)})),z=xr.forcelayout(R,function(t){for(var n=1;n<arguments.length;n++){var e=null!=arguments[n]?arguments[n]:{};n%2?g(Object(e),!0).forEach((function(n){l(t,n,e[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):g(Object(e)).forEach((function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(e,n))}))}return t}({dimensions:t.numDimensions},t.ngraphPhysics)),z.graph=R}for(var q=0;q<t.warmupTicks&&!(E&&t.d3AlphaMin>0&&t.d3ForceLayout.alpha()<t.d3AlphaMin);q++)z[E?"tick":"step"]();t.layout=z,this.resetCountdown()}t.engineRunning=!0,t.onFinishUpdate()}},sr=ur.stateInit,fr=void 0===sr?function(){return{}}:sr,lr=ur.props,cr=void 0===lr?{}:lr,hr=ur.methods,dr=void 0===hr?{}:hr,pr=ur.aliases,gr=void 0===pr?{}:pr,vr=ur.init,yr=void 0===vr?function(){}:vr,br=ur.update,mr=void 0===br?function(){}:br,_r=Object.keys(cr).map((function(t){return new Un(t,cr[t])})),function t(){for(var n=arguments.length,e=new Array(n),r=0;r<n;r++)e[r]=arguments[r];var i=!!(this instanceof t?this.constructor:void 0),o=i?e.shift():void 0,a=e[0],u=void 0===a?{}:a,s=Object.assign({},fr instanceof Function?fr(u):fr,{initialised:!1}),f={};function l(t){return c(t,u),h(),l}var c=function(t,n){yr.call(l,t,s,n),s.initialised=!0},h=Ln((function(){s.initialised&&(mr.call(l,s,f),f={})}),1);return _r.forEach((function(t){l[t.name]=function(t){var n=t.name,e=t.triggerUpdate,r=void 0!==e&&e,i=t.onChange,o=void 0===i?function(t,n){}:i,a=t.defaultVal,u=void 0===a?null:a;return function(t){var e=s[n];if(!arguments.length)return e;var i=void 0===t?u:t;return s[n]=i,o.call(l,i,s,e),!f.hasOwnProperty(n)&&(f[n]=e),r&&h(),l}}(t)})),Object.keys(dr).forEach((function(t){l[t]=function(){for(var n,e=arguments.length,r=new Array(e),i=0;i<e;i++)r[i]=arguments[i];return(n=dr[t]).call.apply(n,[l,s].concat(r))}})),Object.entries(gr).forEach((function(t){var n=qn(t,2),e=n[0],r=n[1];return l[e]=l[r]})),l.resetProps=function(){return _r.forEach((function(t){l[t.name](t.defaultVal)})),l},l.resetProps(),s._rerender=h,i&&o&&l(o),l});var Or=function(t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],e=function(e){function o(){var e;i(this,o);for(var a=arguments.length,u=new Array(a),f=0;f<a;f++)u[f]=arguments[f];return(e=r(this,o,[].concat(u))).__kapsuleInstance=s(t,[].concat(m(n?[e]:[]),u)),e}return d(o,e),f(o)}(arguments.length>1&&void 0!==arguments[1]?arguments[1]:Object);return Object.keys(t()).forEach((function(t){return e.prototype[t]=function(){var n,e=(n=this.__kapsuleInstance)[t].apply(n,arguments);return e===this.__kapsuleInstance?this:e}})),e}(Ar,(window.THREE?window.THREE:{Group:t.Group}).Group,!0);return Or}));
