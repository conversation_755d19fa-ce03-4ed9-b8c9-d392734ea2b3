{"name": "three-forcegraph", "version": "1.42.13", "description": "Force-directed graph as a ThreeJS 3d object", "type": "module", "unpkg": "dist/three-forcegraph.min.js", "jsdelivr": "dist/three-forcegraph.min.js", "main": "dist/three-forcegraph.mjs", "module": "dist/three-forcegraph.mjs", "types": "dist/three-forcegraph.d.ts", "exports": {"types": "./dist/three-forcegraph.d.ts", "umd": "./dist/three-forcegraph.min.js", "default": "./dist/three-forcegraph.mjs"}, "sideEffects": false, "repository": {"type": "git", "url": "git+https://github.com/vasturiano/three-forcegraph.git"}, "homepage": "https://github.com/vasturiano/three-forcegraph", "keywords": ["3d", "force", "simulation", "graph", "three", "d3", "ngraph", "webgl"], "author": {"name": "Vasco <PERSON>turiano", "url": "https://github.com/vasturiano"}, "license": "MIT", "bugs": {"url": "https://github.com/vasturiano/three-forcegraph/issues"}, "scripts": {"build": "rimraf dist && rollup -c", "dev": "rollup -w -c rollup.config.dev.js", "prepare": "npm run build"}, "files": ["dist/**/*"], "dependencies": {"accessor-fn": "1", "d3-array": "1 - 3", "d3-force-3d": "2 - 3", "d3-scale": "1 - 4", "d3-scale-chromatic": "1 - 3", "data-bind-mapper": "1", "kapsule": "^1.16", "ngraph.forcelayout": "3", "ngraph.graph": "20", "tinycolor2": "1"}, "peerDependencies": {"three": ">=0.118.3"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/preset-env": "^7.26.9", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-terser": "^0.4.4", "@types/three": ">=0.118.3", "rimraf": "^6.0.1", "rollup": "^4.36.0", "rollup-plugin-dts": "^6.2.0", "typescript": "^5.8.2"}, "engines": {"node": ">=12"}}