import { LinkedListItem } from "./LinkedListItem";
/**
 * Implements a linked list structure
 * @typeparam T Type of values within this LinkedList
 */
export declare class LinkedList<T> {
    /**
     * First item in list
     */
    first: LinkedListItem<T> | undefined;
    /**
     * Last item in list
     */
    last: LinkedListItem<T> | undefined;
    /**
     * Current length of this LinkedList.
     * Note that this does not work anymore if you for some reason add your own LinkedListItems to LinkedList by hand
     */
    length: number;
    /**
     * @param values Values to be added upfront into list
     */
    constructor(values?: Iterable<T> | LinkedList<T>);
    /**
     * Clears this LinkedList.
     * The default complexity is O(1), because it only removes links to the first and last item and resets the length.
     * Note that if any LinkedListItem is still referenced outside the LinkedList, their before and behind fields might
     * still reference the chain, not freeing space.
     * You can set the unchain parameter to true, so every item in the linked list will be unchained,
     * meaning all references to before and behind items will be removed.
     * This increases complexity to O(n), but removes accidental outside references to the full chain.
     * @param unchain If `true`, remove link info from every item. Changes complexity to O(n)!
     */
    clear(unchain?: boolean): void;
    /**
     * As Array#every() given callback is called for every element until one call returns falsy or all elements had been processed
     * @returns `false` if there was a falsy response from the callback, `true` if all elements have been processed "falselesly"
     * @see Array#every
     */
    every<C>(callback: (value: T, item: LinkedListItem<T>, list: this) => boolean, thisArg?: C): boolean;
    /**
     * Filters values into a new LinkedList
     * @param callback decides wether given element should be part of new LinkedList
     * @see Array#filter
     */
    filter<C>(callback: (value: T, item: LinkedListItem<T>, list: this) => boolean, thisArg?: C): LinkedList<T>;
    /**
     * Returns value for which given callback returns truthy
     * @param callback runs for every value in LinkedList. If it returns truthy, current value is returned.
     * @see Array#find
     */
    find<C>(callback: (value: T, item: LinkedListItem<T>, list: this) => boolean, thisArg?: C): T | undefined;
    /**
     * Returns the LinkedListItem for which given callback returns truthy
     * @param callback runs for every LinkedListItem in LinkedList. If it returns truthy, current LinkedListItem is returned.
     * @see Array#findIndex
     */
    findItem<C>(callback: (value: T, item: LinkedListItem<T>, list: this) => boolean, thisArg?: C): LinkedListItem<T> | undefined;
    /**
     * Iterates this LinkedList's items and values
     * @param callback Gets every value in LinkedList once with corresponding LinkedListItem and LinkedList
     * @param thisArg If given, callback will be bound here
     * @see Array#forEach
     */
    forEach<C>(callback: (value: T, item: LinkedListItem<T>, list: this) => void, thisArg?: C): void;
    /**
     * Checks if value can be found within LinkedList, starting from fromIndex, if given.
     * @param value value to be found in this
     * @param fromIndex Starting index. Supports negative values for which `this.size - 1 + fromIndex` will be used as starting point.
     * @returns true if value could be found in LinkedList (respecting fromIndex), false otherwhise
     * @see Array#includes
     */
    includes(value: T, fromIndex?: number): boolean;
    /**
     * Searches forward for given value and returns the first corresponding LinkedListItem found
     * @param searchedValue Value to be found
     * @param fromIndex Index to start from
     * @see Array#indexOf
     */
    itemOf(searchedValue: T, fromIndex?: number): LinkedListItem<T> | undefined;
    /**
     * Searches backwards for given value and returns the first corresponding LinkedListItem found
     * @param searchedValue Value to be found
     * @param fromIndex Index to start from
     * @see Array#indexOf
     */
    lastItemOf(searchedValue: T, fromIndex?: number): LinkedListItem<T> | undefined;
    /**
     * Creates a new LinkedList with each of its itesm representing the output of the callback with each item in current LinkedList.
     * @param callback Gets value, LinkedListeItem and LinkedList. The response will be used as value in the new LinkedList
     * @param thisArg If given, callback is bound to thisArg
     * @see Array#map
     */
    map<V, C>(callback: (value: T, item: LinkedListItem<T>, list: this) => V, thisArg?: C): LinkedList<V>;
    /**
     * From Array#reduce on MDN: The reduce() method executes a reducer function (that you provide) on each element of the LinkedList,
     * resulting in a single output value.
     * @param callback Gets accumulator, value, LinkedListeItem and LinkedList. The response will be used as the next accumulator
     * @param initialValue
     * @see Array#reduce
     */
    reduce<V>(callback: (accumulator: T, currentValue: T, currentItem: LinkedListItem<T>, list: this) => V): V;
    reduce<V>(callback: (accumulator: V, currentValue: T, currentItem: LinkedListItem<T>, list: this) => V, initialValue: V): V;
    /**
     * From Array#reduce on MDN: The reduceRight() method applies a function against an accumulator and each value of the LinkedList (from last-to-first)
     * to reduce it to a single value.
     * @param callback Gets accumulator, value, LinkedListeItem and LinkedList. The response will be used as the next accumulator
     * @param initialValue Initial accumulator being passed to first call
     */
    reduceRight<V>(callback: (accumulator: T, currentValue: T, currentItem: LinkedListItem<T>, list: this) => V): V;
    reduceRight<V>(callback: (accumulator: V, currentValue: T, currentItem: LinkedListItem<T>, list: this) => V, initialValue: V): V;
    /**
     * Runs callback for every entry and returns true immediately if call of callback returns truthy.
     * @param callback called for every element. If response is truthy, iteration
     * @param thisArg If set, callback is bound to this
     * @returns `true` once a callback call returns truthy, `false` if none returned truthy.
     */
    some<C>(callback: (currentValue: T, item: LinkedListItem<T>, list: this) => boolean, thisArg?: C): boolean;
    /**
     * Joins values within this by given separator. Uses Array#join directly.
     * @param separator separator to be used
     * @see Array#join
     */
    join(separator?: string): string;
    /**
     * Concats given values and returns a new LinkedList with all given values.
     * If LinkedList's are given, they will be spread.
     * @param others Other values or lists to be concat'ed together
     * @see Array#concat
     */
    concat<V>(...others: Array<V | LinkedList<V>>): LinkedList<V | T>;
    /**
     * Removes the last LinkedListItem and returns its inner value
     */
    pop(): T | undefined;
    /**
     * Adds given values on the end of this LinkedList
     * @param values Values to be added
     */
    push(...values: T[]): number;
    /**
     * Adds given values to the beginning of this LinkedList
     * @param values Values to be added
     */
    unshift(...values: T[]): number;
    /**
     * Removes first occurrence of value found.
     * @param value value to remove from LinkedList
     */
    remove(value: T): boolean;
    /**
     * Removes every occurrance of value within this.
     * @param value value to remove from LinkedList
     */
    removeAllOccurrences(value: T): boolean;
    /**
     * Returns and removes first element from LinkedList
     */
    shift(): T | undefined;
    /**
     * Returns LinkedListItem and value for every entry of this LinkedList
     */
    [Symbol.iterator](): IterableIterator<[LinkedListItem<T>, T]>;
    /**
     * Returns LinkedListItem and value for every entry of this LinkedList
     * @see LinkedList#Symbol.iterator
     */
    entries(): IterableIterator<[LinkedListItem<T>, T]>;
    /**
     * Iterates the LinkedListItem's of this LinkedList
     */
    keys(): IterableIterator<LinkedListItem<T>>;
    /**
     * Returns a value for every entry of this LinkedList
     */
    values(): IterableIterator<T>;
    /**
     * Returns the item by given index.
     * Supports negative values and will return the item at `LinkedList.size - 1 + index` in that case.
     * @param index Index of item to get from list
     */
    private getItemByIndex;
    /**
     * Given to own LinkedListItem's for following jobs regarding an unlink:
     * - If item is first item, set the next item as first item
     * - If item is last item, set the previous item as last item
     * - Decrease length
     * @param item Item that has been unlinked
     */
    private unlinkCleanup;
}
