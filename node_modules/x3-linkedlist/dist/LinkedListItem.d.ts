/**
 * Represents an Item within LinkedList.
 * An item holds a value and the links to other LinkedListItem's
 * LinkedListItem's can only be attached behind.
 * Theirfor, to add one before, before has to add one behind.
 */
export declare class LinkedListItem<T> {
    value: T;
    /**
     *
     */
    protected unlinkCleanup?: ((item: LinkedListItem<T>) => void) | undefined;
    /**
     * Item behind this item
     * A -> ThisItem -> C
     *                  ^
     */
    behind: LinkedListItem<T> | undefined;
    /**
     * Item before this item
     * A -> ThisItem -> C
     * ^
     */
    before: LinkedListItem<T> | undefined;
    /**
     * @param value Value to be held
     * @param unlinkCleanup Function to run on unlink() call. Usually used by LinkedList to fix first and last pointers and reduce length.
     */
    constructor(value: T, 
    /**
     *
     */
    unlinkCleanup?: ((item: LinkedListItem<T>) => void) | undefined);
    /**
     * This will link given LinkListItem behind this item.
     * If there's already a LinkedListItem linked behind, it will be relinked accordingly
     * @param item LinkListItem to be inserted behind this one
     */
    insertBehind(item: LinkedListItem<T>): void;
    /**
     * Unlinks this LinkedListItem and calls unlinkCleanup
     * @param unchain If true, additionally removes the reference to the item before and behind
     * @see LinkedListItem#unlinkCleanup
     */
    unlink(unchain?: boolean): void;
    /**
     * Item given will be inserted before this item.
     * unlinkCleanup will be copied if neccessary.
     * This function is protected, because LinkedListItem's can only be attached behind.
     *
     * @param before
     * @see insertBehind
     */
    protected insertBefore(before: LinkedListItem<T>): void;
}
