{"name": "candid-connections-katra", "version": "1.0.0", "description": "A graph-based talent matching platform that visualizes connections between job seekers, companies, positions, and skills.", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "export": "next export"}, "dependencies": {"@react-three/drei": "^10.0.8", "@react-three/fiber": "^9.1.2", "3d-force-graph": "^1.73.2", "arangojs": "^8.8.1", "d3": "^7.9.0", "next": "^15.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-force-graph-2d": "^1.27.1", "react-force-graph-3d": "^1.26.1", "three": "^0.158.0"}, "devDependencies": {"autoprefixer": "^10.4.16", "eslint": "^8.54.0", "eslint-config-next": "^15.0.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.5"}, "keywords": ["job-matching", "graph-database", "visualization", "talent-platform", "arang<PERSON><PERSON>", "nextjs", "d3js", "threejs"], "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/bradygeorgen/candid-connections.git"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}