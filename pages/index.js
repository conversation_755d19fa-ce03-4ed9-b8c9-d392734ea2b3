import Head from 'next/head'
import Layout from '../components/Layout'
import DashboardCards from '../components/DashboardCards'

export default function Home() {
  return (
    <Layout>
      <Head>
        <title>Candid Connections Katra | Professional Talent Matching Platform</title>
        <meta name="description" content="Advanced graph-based talent matching platform connecting job seekers with opportunities through intelligent relationship mapping." />
        <meta name="keywords" content="job matching, talent platform, career connections, hiring, recruitment" />
      </Head>

      {/* Cosmic Hero Section */}
      <div className="relative bg-gradient-to-br from-secondary-900 via-secondary-800 to-secondary-950 rounded-3xl p-12 mb-12 overflow-hidden starfield">
        {/* Cosmic Background Elements */}
        <div className="absolute inset-0 opacity-30">
          <div className="absolute top-10 left-10 w-4 h-4 bg-primary-500 rounded-full cosmic-glow"></div>
          <div className="absolute top-20 right-20 w-2 h-2 bg-primary-400 rounded-full cosmic-glow" style={{animationDelay: '1s'}}></div>
          <div className="absolute bottom-20 left-20 w-3 h-3 bg-primary-300 rounded-full cosmic-glow" style={{animationDelay: '2s'}}></div>
          <div className="absolute bottom-10 right-10 w-5 h-5 bg-primary-600 rounded-full cosmic-glow" style={{animationDelay: '0.5s'}}></div>
          <div className="absolute top-1/2 left-1/4 w-1 h-1 bg-white rounded-full cosmic-glow" style={{animationDelay: '1.5s'}}></div>
          <div className="absolute top-1/3 right-1/3 w-1 h-1 bg-white rounded-full cosmic-glow" style={{animationDelay: '2.5s'}}></div>
        </div>

        <div className="relative z-10 text-center">
          <h1 className="text-4xl lg:text-6xl font-bold text-white mb-6">
            Welcome to<br />
            <span className="cosmic-gradient">
              Candid Connections
            </span>
          </h1>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto leading-relaxed mb-8">
            the smart way to reach hiring managers in the<br />
            hidden job market and <span className="text-primary-400 font-semibold">skip the job board grind.</span>
          </p>

          <button className="bg-white text-secondary-900 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-100 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105">
            Take Control of your Job Search Today
          </button>
        </div>

        {/* Floating Connection Nodes with Animation */}
        <div className="absolute top-16 left-16 w-16 h-16 bg-primary-500 network-node flex items-center justify-center opacity-80">
          <div className="w-8 h-8 bg-white rounded-full"></div>
        </div>
        <div className="absolute top-20 right-16 w-12 h-12 bg-primary-400 network-node flex items-center justify-center opacity-70">
          <div className="w-6 h-6 bg-white rounded-full"></div>
        </div>
        <div className="absolute bottom-16 left-20 w-14 h-14 bg-primary-600 network-node flex items-center justify-center opacity-75">
          <div className="w-7 h-7 bg-white rounded-full"></div>
        </div>
        <div className="absolute bottom-20 right-20 w-10 h-10 bg-primary-300 network-node flex items-center justify-center opacity-60">
          <div className="w-5 h-5 bg-white rounded-full"></div>
        </div>

        {/* Connection Lines */}
        <svg className="absolute inset-0 w-full h-full pointer-events-none" style={{zIndex: 5}}>
          <line x1="20%" y1="25%" x2="80%" y2="30%" stroke="#00d4ff" strokeWidth="1" opacity="0.4" className="connection-line"/>
          <line x1="15%" y1="75%" x2="85%" y2="80%" stroke="#00d4ff" strokeWidth="1" opacity="0.3" className="connection-line" style={{animationDelay: '2s'}}/>
          <line x1="25%" y1="50%" x2="75%" y2="50%" stroke="#00d4ff" strokeWidth="1" opacity="0.2" className="connection-line" style={{animationDelay: '1s'}}/>
        </svg>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12 max-w-4xl mx-auto">
        <div className="text-center">
          <div className="text-3xl font-bold text-primary-500">156</div>
          <div className="text-sm text-candid-gray-500 uppercase tracking-wide">Job Seekers</div>
        </div>
        <div className="text-center">
          <div className="text-3xl font-bold text-secondary-600">23</div>
          <div className="text-sm text-candid-gray-500 uppercase tracking-wide">Companies</div>
        </div>
        <div className="text-center">
          <div className="text-3xl font-bold text-accent-600">89</div>
          <div className="text-sm text-candid-gray-500 uppercase tracking-wide">Open Positions</div>
        </div>
        <div className="text-center">
          <div className="text-3xl font-bold text-primary-600">342</div>
          <div className="text-sm text-candid-gray-500 uppercase tracking-wide">Connections</div>
        </div>
      </div>

      {/* Dashboard Cards */}
      <div className="mb-12">
        <h2 className="text-2xl font-semibold text-candid-navy-900 mb-8 text-center">
          Explore the Platform
        </h2>
        <DashboardCards />
      </div>

      {/* Professional Networking Section */}
      <div className="bg-gradient-to-r from-orange-50 to-orange-100 rounded-3xl p-12 mb-12">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-secondary-800 mb-4">
            GIVE YOUR CLIENTS AND FRIENDS THE EDGE
          </h2>

          {/* Business Figures Illustration */}
          <div className="flex justify-center items-center space-x-8 mb-8">
            {/* Business Person 1 */}
            <div className="flex flex-col items-center">
              <div className="w-16 h-16 bg-primary-500 rounded-full mb-2 flex items-center justify-center">
                <div className="w-8 h-8 bg-white rounded-full"></div>
              </div>
              <div className="w-12 h-20 bg-primary-600 rounded-t-lg"></div>
              <div className="w-16 h-12 bg-secondary-800 rounded-b-lg"></div>
            </div>

            {/* Connection Line */}
            <div className="flex-1 h-1 bg-gradient-to-r from-primary-500 to-accent-500 rounded-full"></div>

            {/* Business Person 2 */}
            <div className="flex flex-col items-center">
              <div className="w-16 h-16 bg-accent-500 rounded-full mb-2 flex items-center justify-center">
                <div className="w-8 h-8 bg-white rounded-full"></div>
              </div>
              <div className="w-12 h-20 bg-accent-600 rounded-t-lg"></div>
              <div className="w-16 h-12 bg-secondary-800 rounded-b-lg"></div>
            </div>

            {/* Connection Line */}
            <div className="flex-1 h-1 bg-gradient-to-r from-accent-500 to-primary-500 rounded-full"></div>

            {/* Business Person 3 */}
            <div className="flex flex-col items-center">
              <div className="w-16 h-16 bg-secondary-600 rounded-full mb-2 flex items-center justify-center">
                <div className="w-8 h-8 bg-white rounded-full"></div>
              </div>
              <div className="w-12 h-20 bg-secondary-700 rounded-t-lg"></div>
              <div className="w-16 h-12 bg-secondary-800 rounded-b-lg"></div>
            </div>
          </div>
        </div>

        <div className="max-w-4xl mx-auto text-center">
          <p className="text-lg text-secondary-700 leading-relaxed mb-8">
            No approval is necessary to become a referral partner—anyone can join! This is especially beneficial if you work with or know people in need of jobs but
            aren't able to assist them yourself. Let us help you make an impact and earn in the process. Simply register below as a Referral Partner. You will gain access
            to a referral portal where you can submit contact information for job seeking candidates. Once someone signs up through your referral, you'll earn a $50
            referral fee. It's that simple—help others and get rewarded.
          </p>

          <button className="bg-secondary-800 text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-secondary-900 transition-all duration-300 shadow-lg hover:shadow-xl underline">
            GET SIGNED UP NOW
          </button>
        </div>
      </div>

      {/* Features Section */}
      <div className="card">
        <div className="card-body">
          <h2 className="text-2xl font-semibold text-candid-navy-900 mb-6 text-center">
            Platform Features
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-soft">
                <span className="text-white text-2xl">🎯</span>
              </div>
              <h3 className="text-lg font-semibold text-candid-navy-900 mb-2">Smart Matching</h3>
              <p className="text-candid-gray-600 text-sm">
                AI-powered algorithms analyze skills, experience, and preferences to create optimal job matches.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-soft">
                <span className="text-white text-2xl">🌐</span>
              </div>
              <h3 className="text-lg font-semibold text-candid-navy-900 mb-2">Network Visualization</h3>
              <p className="text-candid-gray-600 text-sm">
                Interactive 2D and 3D visualizations reveal hidden connections and opportunities in the job market.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-accent-500 to-accent-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-soft">
                <span className="text-white text-2xl">📊</span>
              </div>
              <h3 className="text-lg font-semibold text-candid-navy-900 mb-2">Market Analytics</h3>
              <p className="text-candid-gray-600 text-sm">
                Real-time insights into skill demand, salary trends, and market opportunities.
              </p>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  )
}