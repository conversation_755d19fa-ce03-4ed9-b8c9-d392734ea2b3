import { useState, useEffect } from 'react'
import Head from 'next/head'
import Layout from '../components/Layout'
import VisualizationModal from '../components/VisualizationModal'
import { generateSampleGraphData } from '../lib/graphData'
import { getMatchColor } from '../lib/utils'

export default function Matches() {
  const [matches, setMatches] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)