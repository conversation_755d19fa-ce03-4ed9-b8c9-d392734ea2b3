@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom global styles */
html,
body {
  padding: 0;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen,
    Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Consistent focus styles */
*:focus {
  outline: 2px solid #6366f1;
  outline-offset: 2px;
}

/* Custom utility classes */
@layer components {
  .btn-primary {
    @apply bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-md transition-colors;
  }
  
  .btn-secondary {
    @apply bg-white hover:bg-gray-100 text-gray-800 font-medium py-2 px-4 border border-gray-300 rounded-md transition-colors;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-sm p-6 transition-shadow hover:shadow-md;
  }
}