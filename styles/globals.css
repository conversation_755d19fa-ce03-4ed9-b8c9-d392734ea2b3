@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Candid Connections Global Styles */
@layer base {
  html {
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    scroll-behavior: smooth;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    @apply bg-candid-gray-50 text-candid-navy-800 leading-relaxed;
    margin: 0;
    padding: 0;
  }

  /* Consistent focus styles with brand colors */
  *:focus {
    @apply outline-2 outline-primary-500 outline-offset-2;
    outline-style: solid;
  }

  /* Headings with brand typography */
  h1, h2, h3, h4, h5, h6 {
    @apply text-candid-navy-900 font-semibold leading-tight;
  }

  h1 { @apply text-4xl lg:text-5xl; }
  h2 { @apply text-3xl lg:text-4xl; }
  h3 { @apply text-2xl lg:text-3xl; }
  h4 { @apply text-xl lg:text-2xl; }
  h5 { @apply text-lg lg:text-xl; }
  h6 { @apply text-base lg:text-lg; }

  /* Links with brand styling */
  a {
    @apply text-primary-600 hover:text-primary-700 transition-colors duration-200;
  }

  /* Form elements */
  input, textarea, select {
    @apply border-candid-gray-300 focus:border-primary-500 focus:ring-primary-500;
  }
}

/* Candid Connections Component Library */
@layer components {
  /* Primary Button - Main brand blue */
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 active:bg-primary-800
           text-white font-medium py-3 px-6 rounded-lg
           transition-all duration-200 ease-in-out
           shadow-soft hover:shadow-medium
           focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  /* Secondary Button - Professional navy */
  .btn-secondary {
    @apply bg-white hover:bg-candid-gray-50 active:bg-candid-gray-100
           text-secondary-700 font-medium py-3 px-6
           border border-candid-gray-300 hover:border-candid-gray-400 rounded-lg
           transition-all duration-200 ease-in-out
           shadow-soft hover:shadow-medium
           focus:outline-none focus:ring-2 focus:ring-secondary-500 focus:ring-offset-2;
  }

  /* Accent Button - Orange highlight */
  .btn-accent {
    @apply bg-accent-500 hover:bg-accent-600 active:bg-accent-700
           text-white font-medium py-3 px-6 rounded-lg
           transition-all duration-200 ease-in-out
           shadow-soft hover:shadow-medium
           focus:outline-none focus:ring-2 focus:ring-accent-500 focus:ring-offset-2;
  }

  /* Outline Button */
  .btn-outline {
    @apply bg-transparent hover:bg-primary-50 active:bg-primary-100
           text-primary-600 hover:text-primary-700 font-medium py-3 px-6
           border-2 border-primary-600 hover:border-primary-700 rounded-lg
           transition-all duration-200 ease-in-out
           focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  /* Card Components */
  .card {
    @apply bg-white rounded-xl shadow-soft hover:shadow-medium
           transition-all duration-300 ease-in-out
           border border-candid-gray-200;
  }

  .card-interactive {
    @apply card hover:scale-[1.02] hover:-translate-y-1 cursor-pointer;
  }

  .card-header {
    @apply p-6 border-b border-candid-gray-200;
  }

  .card-body {
    @apply p-6;
  }

  .card-footer {
    @apply p-6 border-t border-candid-gray-200 bg-candid-gray-50 rounded-b-xl;
  }

  /* Navigation Styles */
  .nav-link {
    @apply text-candid-navy-600 hover:text-primary-600
           font-medium transition-colors duration-200
           px-4 py-2 rounded-lg hover:bg-primary-50;
  }

  .nav-link-active {
    @apply nav-link text-primary-700 bg-primary-100;
  }

  /* Form Styles */
  .form-input {
    @apply w-full px-4 py-3 border border-candid-gray-300 rounded-lg
           focus:border-primary-500 focus:ring-1 focus:ring-primary-500
           transition-colors duration-200
           placeholder-candid-gray-400;
  }

  .form-label {
    @apply block text-sm font-medium text-candid-navy-700 mb-2;
  }

  .form-error {
    @apply text-red-600 text-sm mt-1;
  }

  /* Badge Components */
  .badge {
    @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium;
  }

  .badge-primary {
    @apply badge bg-primary-100 text-primary-800;
  }

  .badge-secondary {
    @apply badge bg-secondary-100 text-secondary-800;
  }

  .badge-accent {
    @apply badge bg-accent-100 text-accent-800;
  }

  .badge-success {
    @apply badge bg-green-100 text-green-800;
  }

  .badge-warning {
    @apply badge bg-yellow-100 text-yellow-800;
  }

  .badge-error {
    @apply badge bg-red-100 text-red-800;
  }

  /* Status Indicators */
  .status-active {
    @apply badge-success;
  }

  .status-pending {
    @apply badge-warning;
  }

  .status-inactive {
    @apply badge-secondary;
  }

  .status-error {
    @apply badge-error;
  }

  /* Loading States */
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-candid-gray-300 border-t-primary-600;
  }

  /* Professional Layout Utilities */
  .container-app {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .section-padding {
    @apply py-12 lg:py-16;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-accent-500 bg-clip-text text-transparent;
  }

  /* Dashboard Grid */
  .dashboard-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
  }

  /* Responsive Tables */
  .table-responsive {
    @apply overflow-x-auto shadow-soft rounded-lg;
  }

  .table {
    @apply min-w-full divide-y divide-candid-gray-200;
  }

  .table-header {
    @apply bg-candid-gray-50;
  }

  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm;
  }

  .table-header-cell {
    @apply table-cell font-medium text-candid-navy-900 uppercase tracking-wider;
  }

  /* Cosmic/Network Theme Animations */
  .cosmic-glow {
    animation: cosmic-pulse 3s ease-in-out infinite;
  }

  .network-node {
    @apply rounded-full transition-all duration-300;
    animation: float 6s ease-in-out infinite;
  }

  .network-node:nth-child(2) {
    animation-delay: -2s;
  }

  .network-node:nth-child(3) {
    animation-delay: -4s;
  }

  .connection-line {
    animation: connection-flow 4s linear infinite;
  }

  /* Floating animation for network elements */
  @keyframes float {
    0%, 100% {
      transform: translateY(0px) scale(1);
    }
    50% {
      transform: translateY(-10px) scale(1.05);
    }
  }

  /* Cosmic pulse effect */
  @keyframes cosmic-pulse {
    0%, 100% {
      opacity: 0.6;
      transform: scale(1);
    }
    50% {
      opacity: 1;
      transform: scale(1.1);
    }
  }

  /* Connection flow animation */
  @keyframes connection-flow {
    0% {
      stroke-dasharray: 0 10;
    }
    50% {
      stroke-dasharray: 5 5;
    }
    100% {
      stroke-dasharray: 10 0;
    }
  }

  /* Network connection background - subtle and non-distracting */
  .network-background {
    background:
      radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(0, 212, 255, 0.08) 0%, transparent 50%),
      radial-gradient(circle at 40% 60%, rgba(255, 255, 255, 0.05) 0%, transparent 30%);
    background-size: 800px 800px, 600px 600px, 400px 400px;
    animation: network-drift 30s ease-in-out infinite;
  }

  @keyframes network-drift {
    0%, 100% {
      background-position: 0% 0%, 100% 100%, 50% 50%;
    }
    33% {
      background-position: 30% 20%, 70% 80%, 60% 40%;
    }
    66% {
      background-position: 70% 30%, 30% 70%, 40% 60%;
    }
  }

  /* Enhanced gradient text for cosmic theme */
  .cosmic-gradient {
    @apply bg-gradient-to-r from-primary-400 via-primary-500 to-primary-600 bg-clip-text text-transparent;
    background-size: 200% 200%;
    animation: gradient-shift 3s ease infinite;
  }

  @keyframes gradient-shift {
    0%, 100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }
}